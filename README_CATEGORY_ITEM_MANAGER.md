# Category Item Manager

This document describes the new Category Item Manager interface that matches the design you requested.

## Features

### 🔴 Filter Buttons (Top Header)
- **🔴 All** - Show all items (active by default)
- **🕒 Recent** - Show recently modified items
- **🌐 Home website** - Show items available on home website

### 📋 Categories Sidebar
- **Color-coded categories** with status indicators:
  - 🟢 **Started** (Green indicator)
  - 🟢 **Main Course** (Green indicator)
  - 🟡 **Breads** (Yellow indicator)
  - 🔴 **Beverages** (Red indicator)
  - 💤 **Inactive** (Gray indicator) - Shows all disabled items for easy reactivation

### 📊 Items Management Table
- **Status Column**: Radio button selection + colored status indicator
- **Name Column**: Item name display
- **Mark as Column**: On/Off toggle switches
- **Action Buttons**:
  - ➕ **Add Item** - Add new items to current category
  - ✅ **OK** - Confirm all changes and save

### ⚙️ Item Status Management
- **Green dot** = Available/On
- **Red dot** = Unavailable/Off
- **Toggle switches** for quick enable/disable
- **Radio button selection** for bulk operations

## Sample Data

### Started Category:
- ✅ Veg Manchurian (Available)
- ❌ French Fries (Unavailable)
- ✅ Tomato Soup (Available)
- ✅ Chicken Wings (Available)

### Main Course Category:
- ✅ Chicken Biryani (Available)
- ✅ Paneer Butter Masala (Available)
- ❌ Dal Tadka (Unavailable)
- ✅ Veg Fried Rice (Available)
- ✅ Mutton Curry (Available)

### Breads Category:
- ✅ Butter Naan (Available)
- ✅ Garlic Bread (Available)
- ❌ Roti (Unavailable)
- ✅ Cheese Naan (Available)

### Beverages Category:
- ✅ Mango Lassi (Available)
- ✅ Cold Coffee (Available)
- ❌ Fresh Lime Water (Unavailable)
- ✅ Masala Chai (Available)
- ✅ Chocolate Shake (Available)

### Inactive Category:
- ❌ French Fries (from Started)
- ❌ Dal Tadka (from Main Course)
- ❌ Roti (from Breads)
- ❌ Fresh Lime Water (from Beverages)
- ❌ Fish Curry (from Main Course)
- ❌ Kulfi (from Beverages)
- ❌ Samosa (from Started)

## How to Access

### Method 1: From Menu Management
1. Open the main restaurant application
2. Go to **Menu Management** (🍽️ Menu Management)
3. Click the **📋 Category Manager** button in the header
4. The Category Item Manager window will open as a modal dialog

### Method 2: Standalone Launch
You can also run the Category Item Manager interface independently:

```bash
# Navigate to your project directory
cd e:\restaurant-desktop

# Compile and run the standalone launcher
javac -cp "path/to/javafx/lib/*" src/main/java/com/restaurant/util/CategoryItemManagerLauncher.java
java -cp "path/to/javafx/lib/*:src/main/java" com.restaurant.util.CategoryItemManagerLauncher
```

## Visual Design

The interface matches your requested design with:

- **Filter buttons** - Red active state, gray inactive state
- **Categories sidebar** - Color-coded indicators with clean selection
- **Items table** - Radio buttons, status dots, and toggle switches
- **Responsive layout** - Sidebar + content area layout
- **Professional styling** - Clean, modern appearance

## Functionality

### Category Selection
- Click any category in the sidebar to view its items
- Selected category is highlighted with blue background
- Category title updates in the content area

### Item Management
- **Toggle switches**: Click "On/Off" to enable/disable items
- **Status indicators**: Green = available, Red = unavailable
- **Radio selection**: Click rows to select items for bulk operations
- **Add new items**: Click "➕ Add Item" button

### Filter Options
- **All**: Show all items in selected category
- **Recent**: Show recently modified items
- **Home website**: Show items available on website

## Integration

This interface integrates with your existing:
- Menu management system
- Database structure (MenuItem, MenuCategory)
- Item availability tracking
- Restaurant operations workflow

## Files Created/Modified

### New Files:
- `src/main/resources/fxml/CategoryItemManager.fxml`
- `src/main/java/com/restaurant/controller/CategoryItemManagerController.java`
- `src/main/java/com/restaurant/util/CategoryItemManagerLauncher.java`

### Modified Files:
- `src/main/resources/css/application.css` (added category manager styles)
- `src/main/java/com/restaurant/controller/MenuManagementController.java` (added openCategoryItemManager method)
- `src/main/resources/fxml/MenuManagement.fxml` (added Category Manager button)

## Custom Components

### ToggleSwitch Component
- Custom JavaFX component for On/Off switching
- Green "On" state, Red "Off" state
- Smooth visual transitions
- Callback support for state changes

## Usage Example

1. **Select Category**: Click "Started" in the sidebar
2. **View Items**: See Veg Manchurian, French Fries, etc.
3. **Toggle Status**: Click "Off" to disable French Fries (it moves to Inactive)
4. **View Inactive**: Click "💤 Inactive" to see all disabled items
5. **Reactivate Item**: In Inactive category, toggle French Fries back "On"
6. **Add New Item**: Click "➕ Add Item" and enter "Spring Rolls"
7. **Confirm Changes**: Click "✅ OK" to save all changes
8. **Filter**: Click "Recent" to see recently modified items

## New Features Added

### ✅ OK Button
- **Confirm Changes**: Click the green "✅ OK" button to confirm and save all changes
- **Summary Dialog**: Shows count of active/inactive items after confirmation
- **Database Integration**: Ready for saving changes to your database

### 💤 Inactive Category
- **Centralized View**: All disabled items from all categories appear here
- **Easy Reactivation**: Simply toggle items back "On" to reactivate them
- **Memory Aid**: Users can easily remember and reactivate previously disabled dishes
- **Visual Indicator**: Gray color and sleep emoji (💤) to indicate inactive status
- **Smart Filtering**: Only shows items that are currently disabled

### Enhanced Functionality
- **Real-time Updates**: When you disable an item, it immediately appears in Inactive category
- **Category Counts**: Each category shows item count in parentheses
- **Validation**: Cannot add new items to Inactive category (shows warning)
- **Automatic Refresh**: Views update automatically when items are toggled

The interface provides a complete category-based item management solution that matches your design requirements with professional styling and full functionality!

## Technical Details

- **JavaFX FXML** for UI layout
- **CSS styling** for visual appearance
- **Observer pattern** for data updates
- **Modal dialog** integration
- **Responsive design** for different screen sizes
- **Custom components** for specialized UI elements
