@echo off
echo Starting Restaurant Management System for NetBeans...

REM Set Java path (adjust this to your Java installation)
set JAVA_HOME=C:\Program Files\Java\jdk-23
set PATH=%JAVA_HOME%\bin;%PATH%

echo.
echo Choose execution method:
echo 1. AntRun Plugin (Recommended - Most Reliable)
echo 2. Profile-based execution
echo 3. Secure JavaFX with signing (New!)
echo 4. Signed execution profile
echo 5. Standard exec:java
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" (
    echo Building and running with AntRun plugin...
    call mvn clean compile dependency:copy-dependencies antrun:run
) else if "%choice%"=="2" (
    echo Building and running with profile...
    call mvn clean compile dependency:copy-dependencies -Prun-app
) else if "%choice%"=="3" (
    echo Building and running with secure JavaFX profile...
    call mvn clean compile dependency:copy-dependencies -Psecure-javafx
) else if "%choice%"=="4" (
    echo Building with signed execution...
    call mvn clean compile dependency:copy-dependencies package -Psigned-execution
    echo Running signed application...
    call mvn antrun:run
) else (
    echo Building and running with exec:java...
    call mvn clean compile dependency:copy-dependencies exec:java
)

echo.
echo Application finished. Press any key to close...
pause
