# Platform Configuration Interface

This document describes the new Platform Configuration interface that matches the design you requested.

## Features

### 🔧 Platform Selection
- **Radio button selection** for different delivery platforms:
  - 🍊 **Swiggy** (Orange theme)
  - **Z** **Zomato** (Red theme with styled icon)
  - 🎲 **Dice** (Gray theme)
  - ✓ **Deliveroo** (Teal theme with styled icon)
  - 🌐 **Global** (Blue theme)
  - **T** **Talabat** (Orange theme with styled icon)

### 📋 Outlet List
- **Checkbox selection** for different restaurant brands:
  - ✅ Bakery Brand
  - ✅ Chinese Brand  
  - ✅ Thai Food Brand
  - ☐ Indian Brand
  - ☐ Italian Brand
  - ☐ Mexican Brand

### ⚙️ Configuration Options
- **API Endpoint** - Platform API URL
- **API Key** - Secure authentication key
- **Sync Interval** - How often to sync (1-60 minutes)
- **Timeout** - Request timeout (10-300 seconds)
- **Enable Notifications** - Platform notification alerts
- **Auto Accept Orders** - Automatic order acceptance (not recommended)
- **Enable Logging** - Detailed operation logging

### 🔧 Action Buttons
- **🔗 Test Connection** - Verify platform connectivity
- **🔄 Reset to Default** - Reset all settings
- **📤 Export Config** - Export configuration to JSON
- **📥 Import Config** - Import configuration from JSON
- **💾 Save Configuration** - Save all settings

## How to Access

### Method 1: From Settings Panel
1. Open the main restaurant application
2. Go to **Settings** (🛠️ System Settings)
3. Click the **🔧 Platform Config** button in the header
4. The Platform Configuration window will open as a modal dialog

### Method 2: Standalone Launch
You can also run the Platform Configuration interface independently:

```bash
# Navigate to your project directory
cd e:\restaurant-desktop

# Compile and run the standalone launcher
javac -cp "path/to/javafx/lib/*" src/main/java/com/restaurant/util/PlatformConfigurationLauncher.java
java -cp "path/to/javafx/lib/*:src/main/java" com.restaurant.util.PlatformConfigurationLauncher
```

## Visual Design

The interface matches your requested design with:

- **Platform selection grid** - 3x2 grid of radio button options with platform-specific styling
- **Outlet checkboxes** - Clean checkbox list with proper spacing
- **Configuration sections** - Organized in collapsible sections
- **Action buttons** - Color-coded buttons (primary, secondary, danger)
- **Responsive layout** - Adapts to different window sizes

## Platform-Specific Styling

Each platform has its own visual theme:

- **Swiggy**: Orange border and background (#fc8019)
- **Zomato**: Red border with styled "Z" icon (#e23744)
- **Dice**: Gray theme for neutral appearance
- **Deliveroo**: Teal theme with checkmark icon (#00ccbc)
- **Global**: Blue theme for international feel (#17a2b8)
- **Talabat**: Orange theme with "T" icon (#ff5722)

## Configuration Storage

The configuration can be:
- Saved to database
- Exported to JSON files
- Imported from JSON files
- Reset to default values

## Integration

This interface integrates with your existing:
- Settings system
- Notification system
- Database configuration
- Platform API connections

## Files Created/Modified

### New Files:
- `src/main/resources/fxml/PlatformConfiguration.fxml`
- `src/main/java/com/restaurant/controller/PlatformConfigurationController.java`
- `src/main/java/com/restaurant/util/PlatformConfigurationLauncher.java`

### Modified Files:
- `src/main/resources/css/application.css` (added platform configuration styles)
- `src/main/java/com/restaurant/controller/SettingsController.java` (added openPlatformConfiguration method)
- `src/main/resources/fxml/Settings.fxml` (added Platform Config button)

## Usage Example

1. **Select Platform**: Click on Swiggy radio button
2. **Choose Outlets**: Check "Bakery Brand", "Chinese Brand", "Thai Food Brand"
3. **Configure API**: Enter API endpoint and key
4. **Set Options**: Enable notifications, set sync interval to 5 minutes
5. **Test**: Click "Test Connection" to verify setup
6. **Save**: Click "Save Configuration" to store settings

The interface provides a complete platform configuration solution that matches your design requirements!
