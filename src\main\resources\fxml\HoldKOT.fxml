<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.HoldKOTController" stylesheets="@../css/application.css">
   <top>
      <VBox spacing="10.0" styleClass="header-section">
         <padding>
            <Insets bottom="10.0" left="20.0" right="20.0" top="20.0" />
         </padding>
         
         <!-- Header -->
         <HBox alignment="CENTER">
            <Label styleClass="module-title" text="⏸️ Hold KOT - Kitchen Order Ticket">
               <font>
                  <Font name="System Bold" size="20.0" />
               </font>
            </Label>
         </HBox>
         
         <Label styleClass="subtitle" text="Temporarily hold orders before sending to kitchen">
            <font>
               <Font size="14.0" />
            </font>
         </Label>
      </VBox>
   </top>
   
   <center>
      <HBox spacing="20.0" styleClass="main-content">
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
         </padding>
         
         <!-- Left Panel - Current Order -->
         <VBox spacing="15.0" styleClass="left-panel" HBox.hgrow="ALWAYS">
            <Label styleClass="section-header" text="📋 Current Order">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font>
            </Label>
            
            <!-- Order Details -->
            <HBox spacing="15.0" alignment="CENTER_LEFT">
               <Label text="Table Number:" styleClass="field-label" />
               <TextField fx:id="tableNumberField" promptText="Table #" styleClass="table-field" prefWidth="80.0" editable="false" />
               <Region HBox.hgrow="ALWAYS" />
               <Label text="Total:" styleClass="field-label" />
               <Label fx:id="currentOrderTotal" text="₹0.00" styleClass="total-amount">
                  <font>
                     <Font name="System Bold" size="16.0" />
                  </font>
               </Label>
            </HBox>
            
            <!-- Current Order Items Table -->
            <TableView fx:id="currentOrderTable" styleClass="order-table" prefHeight="200.0">
               <columns>
                  <TableColumn fx:id="itemNameColumn" prefWidth="150.0" text="Item Name" />
                  <TableColumn fx:id="quantityColumn" prefWidth="60.0" text="Qty" />
                  <TableColumn fx:id="priceColumn" prefWidth="80.0" text="Price" />
                  <TableColumn fx:id="totalColumn" prefWidth="80.0" text="Total" />
               </columns>
               <columnResizePolicy>
                  <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
               </columnResizePolicy>
            </TableView>
            
            <!-- Hold Reason -->
            <VBox spacing="5.0">
               <Label text="Hold Reason:" styleClass="field-label" />
               <TextArea fx:id="holdReasonArea" promptText="Enter reason for holding this order (optional)..." 
                        styleClass="reason-area" prefRowCount="3" wrapText="true" />
            </VBox>
            
            <!-- Hold Button -->
            <Button fx:id="holdOrderBtn" onAction="#handleHoldOrder" styleClass="hold-button" 
                   text="⏸️ Hold Order" prefWidth="150.0" disable="true">
               <font>
                  <Font name="System Bold" size="14.0" />
               </font>
            </Button>
         </VBox>
         
         <!-- Right Panel - Held Orders -->
         <VBox spacing="15.0" styleClass="right-panel" HBox.hgrow="ALWAYS">
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <Label styleClass="section-header" text="⏸️ Held Orders">
                  <font>
                     <Font name="System Bold" size="16.0" />
                  </font>
               </Label>
               <Region HBox.hgrow="ALWAYS" />
               <Label text="Count:" styleClass="field-label" />
               <Label fx:id="heldOrdersCount" text="0" styleClass="count-badge">
                  <font>
                     <Font name="System Bold" size="14.0" />
                  </font>
               </Label>
            </HBox>
            
            <!-- Held Orders Table -->
            <TableView fx:id="heldOrdersTable" styleClass="held-orders-table" prefHeight="300.0">
               <columns>
                  <TableColumn fx:id="heldOrderIdColumn" prefWidth="100.0" text="Order ID" />
                  <TableColumn fx:id="heldTableColumn" prefWidth="60.0" text="Table" />
                  <TableColumn fx:id="heldTimeColumn" prefWidth="80.0" text="Time" />
                  <TableColumn fx:id="heldReasonColumn" prefWidth="120.0" text="Reason" />
                  <TableColumn fx:id="heldTotalColumn" prefWidth="80.0" text="Total" />
               </columns>
               <columnResizePolicy>
                  <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
               </columnResizePolicy>
            </TableView>
            
            <!-- Action Buttons -->
            <HBox spacing="10.0" alignment="CENTER">
               <Button fx:id="releaseOrderBtn" onAction="#handleReleaseOrder" styleClass="release-button" 
                      text="▶️ Release Order" disable="true">
                  <font>
                     <Font name="System Bold" size="12.0" />
                  </font>
               </Button>
               <Button fx:id="deleteHeldOrderBtn" onAction="#handleDeleteHeldOrder" styleClass="delete-button" 
                      text="🗑️ Delete" disable="true">
                  <font>
                     <Font name="System Bold" size="12.0" />
                  </font>
               </Button>
            </HBox>
         </VBox>
      </HBox>
   </center>
   
   <bottom>
      <HBox alignment="CENTER_RIGHT" spacing="15.0" styleClass="footer-section">
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
         
         <Label text="💡 Tip: Use Ctrl+H to quickly access Hold KOT functionality" styleClass="tip-label" />
         <Region HBox.hgrow="ALWAYS" />
         <Button fx:id="closeBtn" onAction="#handleClose" text="Close" styleClass="close-button">
            <font>
               <Font name="System Bold" size="12.0" />
            </font>
         </Button>
      </HBox>
   </bottom>
</BorderPane>
