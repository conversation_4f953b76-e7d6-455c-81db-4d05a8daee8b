<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<StackPane xmlns="http://javafx.com/javafx/17.0.2-ea" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.AttendanceManagementController" stylesheets="@../css/application.css">
   <children>
      <!-- Main Content -->
      <VBox>
         <children>
            <!-- Header Section -->
            <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="module-header">
               <children>
                  <Button mnemonicParsing="false" onAction="#goBack" styleClass="back-button" text="← Back" />
                  <Label styleClass="module-title" text="📅 Attendance Management">
                     <font>
                        <Font name="System Bold" size="24.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button mnemonicParsing="false" onAction="#markAttendance" styleClass="primary-button" text="✓ Mark Attendance" />
                  <Button mnemonicParsing="false" onAction="#generateReport" styleClass="secondary-button" text="📊 Generate Report" />
               </children>
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>
            </HBox>
      
      <!-- Date and Filter Section -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="filter-section">
         <children>
            <Label text="Date:" />
            <DatePicker fx:id="attendanceDatePicker" styleClass="date-picker" />
            <Label text="Department:" />
            <ComboBox fx:id="departmentFilterCombo" promptText="All Departments" styleClass="filter-combo" />
            <Label text="Status:" />
            <ComboBox fx:id="statusFilterCombo" promptText="All Status" styleClass="filter-combo" />
            <Button mnemonicParsing="false" onAction="#refreshAttendance" styleClass="refresh-button" text="🔄 Refresh" />
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="5.0" />
         </padding>
      </HBox>
      
      <!-- Attendance Summary Cards -->
      <HBox spacing="20.0" styleClass="summary-section">
         <children>
            <VBox styleClass="summary-card,present-card">
               <children>
                  <Label fx:id="presentCountLabel" styleClass="summary-number" text="12">
                     <font>
                        <Font name="System Bold" size="32.0" />
                     </font>
                  </Label>
                  <Label styleClass="summary-label" text="Present Today" />
               </children>
            </VBox>
            <VBox styleClass="summary-card,absent-card">
               <children>
                  <Label fx:id="absentCountLabel" styleClass="summary-number" text="3">
                     <font>
                        <Font name="System Bold" size="32.0" />
                     </font>
                  </Label>
                  <Label styleClass="summary-label" text="Absent Today" />
               </children>
            </VBox>
            <VBox styleClass="summary-card,late-card">
               <children>
                  <Label fx:id="lateCountLabel" styleClass="summary-number" text="2">
                     <font>
                        <Font name="System Bold" size="32.0" />
                     </font>
                  </Label>
                  <Label styleClass="summary-label" text="Late Arrivals" />
               </children>
            </VBox>
            <VBox styleClass="summary-card,total-card">
               <children>
                  <Label fx:id="totalStaffLabel" styleClass="summary-number" text="15">
                     <font>
                        <Font name="System Bold" size="32.0" />
                     </font>
                  </Label>
                  <Label styleClass="summary-label" text="Total Staff" />
               </children>
            </VBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
         </padding>
      </HBox>
      
      <!-- Attendance Table -->
      <VBox VBox.vgrow="ALWAYS" styleClass="table-section">
         <children>
            <TableView fx:id="attendanceTable" styleClass="attendance-table">
               <columns>
                  <TableColumn fx:id="staffIdColumn" prefWidth="80.0" text="ID" />
                  <TableColumn fx:id="staffNameColumn" prefWidth="200.0" text="Staff Name" />
                  <TableColumn fx:id="departmentColumn" prefWidth="150.0" text="Department" />
                  <TableColumn fx:id="checkInColumn" prefWidth="120.0" text="Check In" />
                  <TableColumn fx:id="checkOutColumn" prefWidth="120.0" text="Check Out" />
                  <TableColumn fx:id="hoursWorkedColumn" prefWidth="120.0" text="Hours Worked" />
                  <TableColumn fx:id="statusColumn" prefWidth="100.0" text="Status" />
                  <TableColumn fx:id="actionsColumn" prefWidth="150.0" text="Actions" />
               </columns>
            </TableView>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
            </padding>
         </VBox>
         </children>
      </VBox>

      <!-- Mark Attendance Dialog (Hidden by default) -->
      <VBox fx:id="attendanceDialog" managed="false" visible="false" styleClass="dialog-overlay" StackPane.alignment="CENTER">
         <children>
            <VBox styleClass="dialog-content">
               <children>
                  <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="dialog-header">
                     <children>
                        <Label fx:id="dialogTitle" styleClass="dialog-title" text="Mark Attendance">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        <Region HBox.hgrow="ALWAYS" />
                        <Button mnemonicParsing="false" onAction="#closeAttendanceDialog" styleClass="close-button" text="✕" />
                     </children>
                  </HBox>
                  
                  <VBox spacing="15.0" styleClass="dialog-body">
                     <children>
                        <HBox spacing="10.0">
                           <children>
                              <Label text="Staff:" />
                              <ComboBox fx:id="staffCombo" prefWidth="200.0" promptText="Select Staff Member" />
                           </children>
                        </HBox>
                        
                        <HBox spacing="10.0">
                           <children>
                              <Label text="Date:" />
                              <DatePicker fx:id="markDatePicker" prefWidth="200.0" />
                           </children>
                        </HBox>
                        
                        <HBox spacing="10.0">
                           <children>
                              <Label text="Check In Time:" />
                              <TextField fx:id="checkInTimeField" prefWidth="200.0" promptText="HH:MM (e.g., 09:00)" />
                           </children>
                        </HBox>
                        
                        <HBox spacing="10.0">
                           <children>
                              <Label text="Check Out Time:" />
                              <TextField fx:id="checkOutTimeField" prefWidth="200.0" promptText="HH:MM (e.g., 17:00)" />
                           </children>
                        </HBox>
                        
                        <HBox spacing="10.0">
                           <children>
                              <Label text="Status:" />
                              <ComboBox fx:id="attendanceStatusCombo" prefWidth="200.0" />
                           </children>
                        </HBox>
                        
                        <HBox spacing="10.0">
                           <children>
                              <Label text="Notes:" />
                              <TextArea fx:id="notesArea" prefHeight="60.0" prefWidth="200.0" promptText="Optional notes..." />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <HBox alignment="CENTER_RIGHT" spacing="10.0" styleClass="dialog-footer">
                     <children>
                        <Button mnemonicParsing="false" onAction="#closeAttendanceDialog" styleClass="cancel-button" text="Cancel" />
                        <Button mnemonicParsing="false" onAction="#saveAttendance" styleClass="save-button" text="Save Attendance" />
                     </children>
                  </HBox>
               </children>
            </VBox>
         </children>
      </VBox>
   </children>
</StackPane>
