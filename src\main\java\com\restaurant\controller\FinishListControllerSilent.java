package com.restaurant.controller;

import com.restaurant.model.OnlineOrder;
import com.restaurant.model.OnlineOrderDAO;
import com.restaurant.model.OnlineOrderItem;
import com.restaurant.util.NotificationManager;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;

import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Silent version of FinishListController - All sounds moved to NotificationManager
 * This controller handles order status updates without any audio notifications
 */
public class FinishListControllerSilent implements Initializable {
    
    // FXML Components
    @FXML private VBox ordersContainer;
    @FXML private Label totalOrdersLabel;
    @FXML private Label preparingCountLabel;
    @FXML private Label readyCountLabel;
    @FXML private Label pricingCountLabel;
    @FXML private Label completedCountLabel;
    @FXML private ComboBox<String> platformFilterComboBox;
    @FXML private ComboBox<String> statusFilterComboBox;
    @FXML private Button refreshButton;
    @FXML private Button markAllReadyButton;
    @FXML private Button clearCompletedButton;
    @FXML private Button addTestOrderButton;
    
    // Data
    private List<OnlineOrder> allOrders = new ArrayList<>();
    private List<OnlineOrder> filteredOrders = new ArrayList<>();
    private ScheduledExecutorService refreshScheduler;
    private NotificationManager notificationManager;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("FinishListControllerSilent: Initializing...");

        // Initialize notification manager (for visual notifications only)
        notificationManager = NotificationManager.getInstance();
        
        // Disable audio notifications in this controller
        notificationManager.setAudioEnabled(false);

        // Initialize database
        OnlineOrderDAO.initializeDatabase();

        // Setup UI components
        setupFilters();
        setupAutoRefresh();

        // Load initial data
        refreshOrders();

        // Show welcome notification (visual only)
        notificationManager.notifySuccess("Finish List Ready",
            "Silent order tracking system is now active (sounds managed by Notifications)");

        System.out.println("FinishListControllerSilent: Initialization complete - Audio disabled");
    }
    
    private void setupFilters() {
        // Platform filter
        platformFilterComboBox.setItems(FXCollections.observableArrayList(
            "All Platforms", "Swiggy", "Zomato", "Online"
        ));
        platformFilterComboBox.setValue("All Platforms");
        platformFilterComboBox.setOnAction(e -> applyFilters());
        
        // Status filter
        statusFilterComboBox.setItems(FXCollections.observableArrayList(
            "All Status", "NEW", "PREPARING", "READY", "PRICING", "COMPLETED"
        ));
        statusFilterComboBox.setValue("All Status");
        statusFilterComboBox.setOnAction(e -> applyFilters());
    }
    
    private void setupAutoRefresh() {
        refreshScheduler = Executors.newSingleThreadScheduledExecutor();
        refreshScheduler.scheduleAtFixedRate(() -> {
            Platform.runLater(this::refreshOrders);
        }, 30, 30, TimeUnit.SECONDS);
    }
    
    private void applyFilters() {
        String platformFilter = platformFilterComboBox.getValue();
        String statusFilter = statusFilterComboBox.getValue();
        
        filteredOrders = allOrders.stream()
            .filter(order -> {
                // Platform filter
                if (!"All Platforms".equals(platformFilter)) {
                    if (!order.getPlatform().name().equals(platformFilter)) {
                        return false;
                    }
                }
                
                // Status filter
                if (!"All Status".equals(statusFilter)) {
                    if (!order.getStatus().name().equals(statusFilter)) {
                        return false;
                    }
                }
                
                return true;
            })
            .collect(Collectors.toList());
        
        displayOrders();
        updateStatistics();
    }
    
    private void displayOrders() {
        ordersContainer.getChildren().clear();
        
        if (filteredOrders.isEmpty()) {
            Label noOrdersLabel = new Label("No orders found matching the current filters");
            noOrdersLabel.setStyle("-fx-font-size: 16px; -fx-text-fill: #7f8c8d; -fx-padding: 20px;");
            ordersContainer.getChildren().add(noOrdersLabel);
            return;
        }
        
        // Group orders by status for better organization
        Map<OnlineOrder.OrderStatus, List<OnlineOrder>> ordersByStatus = filteredOrders.stream()
            .collect(Collectors.groupingBy(OnlineOrder::getStatus));
        
        // Display orders in status order
        OnlineOrder.OrderStatus[] statusOrder = {
            OnlineOrder.OrderStatus.NEW,
            OnlineOrder.OrderStatus.PREPARING,
            OnlineOrder.OrderStatus.READY,
            OnlineOrder.OrderStatus.PRICING,
            OnlineOrder.OrderStatus.COMPLETED
        };
        
        for (OnlineOrder.OrderStatus status : statusOrder) {
            List<OnlineOrder> statusOrders = ordersByStatus.get(status);
            if (statusOrders != null && !statusOrders.isEmpty()) {
                // Add status header
                Label statusHeader = new Label(status.getDisplayName() + " (" + statusOrders.size() + ")");
                statusHeader.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-padding: 10px 0 5px 0;");
                statusHeader.setTextFill(getStatusColor(status));
                ordersContainer.getChildren().add(statusHeader);
                
                // Add orders for this status
                for (OnlineOrder order : statusOrders) {
                    ordersContainer.getChildren().add(createOrderCard(order));
                }
            }
        }
    }
    
    private Color getStatusColor(OnlineOrder.OrderStatus status) {
        switch (status) {
            case NEW: return Color.RED;
            case PREPARING: return Color.ORANGE;
            case READY: return Color.BLUE;
            case PRICING: return Color.PURPLE;
            case COMPLETED: return Color.GREEN;
            default: return Color.BLACK;
        }
    }
    
    private VBox createOrderCard(OnlineOrder order) {
        VBox card = new VBox(10);
        card.setPadding(new Insets(15));
        card.setStyle(getCardStyle(order.getStatus()));
        
        // Order header
        HBox header = new HBox(10);
        header.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
        
        Label orderIdLabel = new Label("Order #" + order.getOrderId());
        orderIdLabel.setFont(Font.font("System", FontWeight.BOLD, 16));
        
        Label platformLabel = new Label(order.getPlatform().name());
        platformLabel.setStyle(getPlatformStyle(order.getPlatform()));
        
        Label timeLabel = new Label(order.getOrderTime().format(DateTimeFormatter.ofPattern("HH:mm")));
        timeLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #7f8c8d;");
        
        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);
        
        header.getChildren().addAll(orderIdLabel, platformLabel, spacer, timeLabel);
        
        // Customer info
        Label customerLabel = new Label("Customer: " + order.getCustomerName());
        customerLabel.setStyle("-fx-font-size: 14px;");
        
        // Order items
        VBox itemsBox = new VBox(5);
        for (OnlineOrderItem item : order.getItems()) {
            Label itemLabel = new Label(item.getQuantity() + "x " + item.getItemName());
            itemLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #34495e;");
            itemsBox.getChildren().add(itemLabel);
        }
        
        // Total amount
        Label totalLabel = new Label("Total: ₹" + String.format("%.2f", order.getTotalAmount()));
        totalLabel.setFont(Font.font("System", FontWeight.BOLD, 14));
        
        // Status buttons (SILENT - no audio)
        HBox buttonsBox = createStatusButtons(order);
        
        card.getChildren().addAll(header, customerLabel, itemsBox, totalLabel, buttonsBox);
        return card;
    }
    
    private String getCardStyle(OnlineOrder.OrderStatus status) {
        String baseStyle = "-fx-background-color: white; -fx-border-radius: 8px; -fx-background-radius: 8px; " +
                          "-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2); -fx-border-width: 2px; ";
        
        switch (status) {
            case NEW:
                return baseStyle + "-fx-border-color: #e74c3c;";
            case PREPARING:
                return baseStyle + "-fx-border-color: #f39c12;";
            case READY:
                return baseStyle + "-fx-border-color: #3498db;";
            case PRICING:
                return baseStyle + "-fx-border-color: #9b59b6;";
            case COMPLETED:
                return baseStyle + "-fx-border-color: #27ae60;";
            default:
                return baseStyle + "-fx-border-color: #bdc3c7;";
        }
    }
    
    private String getPlatformStyle(OnlineOrder.Platform platform) {
        switch (platform) {
            case SWIGGY:
                return "-fx-background-color: #ff6b35; -fx-text-fill: white; -fx-padding: 2px 8px; " +
                       "-fx-background-radius: 12px; -fx-font-size: 10px; -fx-font-weight: bold;";
            case ZOMATO:
                return "-fx-background-color: #e23744; -fx-text-fill: white; -fx-padding: 2px 8px; " +
                       "-fx-background-radius: 12px; -fx-font-size: 10px; -fx-font-weight: bold;";
            case WOK_KA_TADKA:
                return "-fx-background-color: #2e7d32; -fx-text-fill: white; -fx-padding: 2px 8px; " +
                       "-fx-background-radius: 12px; -fx-font-size: 10px; -fx-font-weight: bold;";
            default:
                return "-fx-background-color: #34495e; -fx-text-fill: white; -fx-padding: 2px 8px; " +
                       "-fx-background-radius: 12px; -fx-font-size: 10px; -fx-font-weight: bold;";
        }
    }
    
    private HBox createStatusButtons(OnlineOrder order) {
        HBox buttonsBox = new HBox(10);
        buttonsBox.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
        
        OnlineOrder.OrderStatus currentStatus = order.getStatus();
        
        // Create buttons based on current status (SILENT VERSION)
        // NOTE: NEW orders are handled in notifications panel - no Accept button here
        switch (currentStatus) {
            case NEW:
                // No Accept button - order acceptance handled in notifications panel
                Label newOrderLabel = new Label("📱 Accept/Reject in Notifications Panel");
                newOrderLabel.setStyle("-fx-text-fill: #e74c3c; -fx-font-weight: bold; -fx-font-style: italic;");
                buttonsBox.getChildren().add(newOrderLabel);
                break;
            case PREPARING:
                buttonsBox.getChildren().add(createStatusButton("🍽️ Mark Ready", 
                    OnlineOrder.OrderStatus.READY, order, "#3498db"));
                break;
            case READY:
                buttonsBox.getChildren().add(createStatusButton("💰 Start Pricing", 
                    OnlineOrder.OrderStatus.PRICING, order, "#9b59b6"));
                break;
            case PRICING:
                buttonsBox.getChildren().add(createStatusButton("📦 Complete Order", 
                    OnlineOrder.OrderStatus.COMPLETED, order, "#e67e22"));
                break;
            case COMPLETED:
                Label completedLabel = new Label("✅ Order Completed");
                completedLabel.setStyle("-fx-text-fill: #27ae60; -fx-font-weight: bold;");
                buttonsBox.getChildren().add(completedLabel);
                break;
        }
        
        return buttonsBox;
    }
    
    private Button createStatusButton(String text, OnlineOrder.OrderStatus targetStatus, 
                                    OnlineOrder order, String color) {
        Button button = new Button(text);
        button.setStyle("-fx-background-color: " + color + "; -fx-text-fill: white; " +
                       "-fx-font-weight: bold; -fx-padding: 8px 16px; -fx-background-radius: 5px;");
        
        // Set button action with confirmation for important status changes (SILENT)
        button.setOnAction(e -> {
            // Show confirmation for completing orders
            if (targetStatus == OnlineOrder.OrderStatus.COMPLETED) {
                Alert confirmation = new Alert(Alert.AlertType.CONFIRMATION);
                confirmation.setTitle("Confirm Order Completion");
                confirmation.setHeaderText("Complete Order " + order.getOrderId());
                confirmation.setContentText("Are you sure you want to mark this order as completed?\n" +
                                          "This means it has been handed over to the delivery partner.");

                confirmation.showAndWait().ifPresent(response -> {
                    if (response == ButtonType.OK) {
                        updateOrderStatusSilent(order, targetStatus);
                    }
                });
            } else {
                // Direct update for other status changes (SILENT)
                updateOrderStatusSilent(order, targetStatus);
            }
        });
        
        return button;
    }
    
    /**
     * SILENT version of updateOrderStatus - NO AUDIO NOTIFICATIONS
     * All sounds are handled by the separate NotificationManager
     */
    private void updateOrderStatusSilent(OnlineOrder order, OnlineOrder.OrderStatus newStatus) {
        try {
            OnlineOrder.OrderStatus oldStatus = order.getStatus();
            boolean success = OnlineOrderDAO.updateOrderStatus(order.getId(), newStatus);
            if (success) {
                order.setStatus(newStatus);
                System.out.println("SILENT: Updated order " + order.getOrderId() + " status to " + newStatus);

                // Send VISUAL-ONLY notifications (no audio)
                switch (newStatus) {
                    case NEW:
                        // Visual notification only - audio handled by NotificationManager
                        notificationManager.showNotification(NotificationManager.NotificationType.STATUS_CHANGE,
                            "Order Status", "Order " + order.getOrderId() + " marked as NEW");
                        break;
                    case PREPARING:
                        // Visual notification only - audio handled by NotificationManager
                        notificationManager.showNotification(NotificationManager.NotificationType.STATUS_CHANGE,
                            "Order Accepted", "Order " + order.getOrderId() + " is now being prepared");
                        break;
                    case READY:
                        // Visual notification only - audio handled by NotificationManager
                        notificationManager.showNotification(NotificationManager.NotificationType.ORDER_READY,
                            "Order Ready", "Order " + order.getOrderId() + " is ready for pickup");
                        break;
                    case PRICING:
                        // Visual notification only - audio handled by NotificationManager
                        notificationManager.showNotification(NotificationManager.NotificationType.STATUS_CHANGE,
                            "Pricing Started", "Order " + order.getOrderId() + " moved to pricing & packaging");
                        break;
                    case COMPLETED:
                        // Visual notification only - audio handled by NotificationManager
                        notificationManager.showNotification(NotificationManager.NotificationType.ORDER_COMPLETED,
                            "Order Completed", "Order " + order.getOrderId() + " has been completed");
                        break;
                }

                // Refresh display
                Platform.runLater(() -> {
                    updateStatistics();
                    displayOrders();
                });

            } else {
                // Visual error notification only
                notificationManager.notifyError("Update Failed",
                    "Failed to update order " + order.getOrderId() + " status");
            }
        } catch (Exception e) {
            System.err.println("Error updating order status: " + e.getMessage());
            notificationManager.notifyError("System Error",
                "Error updating order status: " + e.getMessage());
        }
    }
    
    private void updateStatistics() {
        int total = allOrders.size();
        int preparing = (int) allOrders.stream().filter(o -> o.getStatus() == OnlineOrder.OrderStatus.PREPARING).count();
        int ready = (int) allOrders.stream().filter(o -> o.getStatus() == OnlineOrder.OrderStatus.READY).count();
        int pricing = (int) allOrders.stream().filter(o -> o.getStatus() == OnlineOrder.OrderStatus.PRICING).count();
        int completed = (int) allOrders.stream().filter(o -> o.getStatus() == OnlineOrder.OrderStatus.COMPLETED).count();
        
        Platform.runLater(() -> {
            totalOrdersLabel.setText(String.valueOf(total));
            preparingCountLabel.setText(String.valueOf(preparing));
            readyCountLabel.setText(String.valueOf(ready));
            pricingCountLabel.setText(String.valueOf(pricing));
            completedCountLabel.setText(String.valueOf(completed));
        });
    }
    
    @FXML
    private void addTestOrder() {
        // Create a test order for demonstration (SILENT)
        OnlineOrder testOrder = createTestOrder();

        try {
            int orderId = OnlineOrderDAO.createOnlineOrder(testOrder);
            if (orderId > 0) {
                System.out.println("SILENT: Created test order with ID: " + orderId);

                // Visual notification only - audio handled by NotificationManager
                notificationManager.notifySuccess("Test Order Created",
                    "Test order " + testOrder.getOrderId() + " created successfully");

                refreshOrders();
            } else {
                notificationManager.notifyError("Creation Failed", "Failed to create test order");
            }
        } catch (Exception e) {
            System.err.println("Error creating test order: " + e.getMessage());
            notificationManager.notifyError("System Error",
                "Error creating test order: " + e.getMessage());
        }
    }
    
    @FXML
    private void markAllReady() {
        long preparingCount = allOrders.stream()
            .filter(order -> order.getStatus() == OnlineOrder.OrderStatus.PREPARING)
            .count();
        
        if (preparingCount == 0) {
            notificationManager.notifyWarning("No Orders",
                "No orders in PREPARING status to mark as ready");
            return;
        }
        
        Alert confirmation = new Alert(Alert.AlertType.CONFIRMATION);
        confirmation.setTitle("Confirm Action");
        confirmation.setHeaderText("Mark All Ready");
        confirmation.setContentText("Are you sure you want to mark all " + preparingCount + " preparing orders as ready?");
        
        confirmation.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                allOrders.stream()
                    .filter(order -> order.getStatus() == OnlineOrder.OrderStatus.PREPARING)
                    .forEach(order -> updateOrderStatusSilent(order, OnlineOrder.OrderStatus.READY));

                // Visual notification only
                notificationManager.notifySuccess("Bulk Update Complete",
                    preparingCount + " orders marked as ready");
            }
        });
    }
    
    @FXML
    private void clearCompleted() {
        long completedCount = allOrders.stream()
            .filter(order -> order.getStatus() == OnlineOrder.OrderStatus.COMPLETED)
            .count();
        
        if (completedCount == 0) {
            notificationManager.notifyWarning("No Orders", "No completed orders to clear");
            return;
        }
        
        Alert confirmation = new Alert(Alert.AlertType.CONFIRMATION);
        confirmation.setTitle("Confirm Action");
        confirmation.setHeaderText("Clear Completed Orders");
        confirmation.setContentText("Are you sure you want to clear all " + completedCount + " completed orders?");
        
        confirmation.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                // Visual notification only
                notificationManager.notifySuccess("Completed Orders Cleared",
                    completedCount + " completed orders cleared");
                refreshOrders();
            }
        });
    }
    
    @FXML
    private void refreshOrders() {
        try {
            allOrders = OnlineOrderDAO.getAllOnlineOrders();
            applyFilters();
            updateStatistics();
            displayOrders();

            // NO persistent notifications - all sounds handled by NotificationManager
            System.out.println("SILENT: Orders refreshed - " + allOrders.size() + " total orders");
            
        } catch (Exception e) {
            System.err.println("Error refreshing orders: " + e.getMessage());
            notificationManager.notifyError("Refresh Error",
                "Failed to refresh orders: " + e.getMessage());
        }
    }
    
    private OnlineOrder createTestOrder() {
        OnlineOrder order = new OnlineOrder();
        order.setOrderId("TEST" + System.currentTimeMillis());

        // Random platform selection including Wok Ka Tadka
        double random = Math.random();
        if (random < 0.33) {
            order.setPlatform(OnlineOrder.Platform.SWIGGY);
        } else if (random < 0.66) {
            order.setPlatform(OnlineOrder.Platform.ZOMATO);
        } else {
            order.setPlatform(OnlineOrder.Platform.WOK_KA_TADKA);
        }

        order.setCustomerName("Test Customer " + (int)(Math.random() * 100));
        order.setStatus(OnlineOrder.OrderStatus.NEW);
        order.setOrderTime(LocalDateTime.now());
        order.setTotalAmount(150.0 + (Math.random() * 300));
        
        // Add test items
        List<OnlineOrderItem> items = new ArrayList<>();
        items.add(new OnlineOrderItem("Butter Chicken", 2, 180.0, "Main Course"));
        items.add(new OnlineOrderItem("Naan", 3, 45.0, "Bread"));
        order.setItems(items);
        
        return order;
    }
    
    public void cleanup() {
        if (refreshScheduler != null && !refreshScheduler.isShutdown()) {
            refreshScheduler.shutdown();
        }
        
        // Re-enable audio for other parts of the application
        notificationManager.setAudioEnabled(true);
        
        System.out.println("FinishListControllerSilent: Cleanup complete - Audio re-enabled");
    }
}
