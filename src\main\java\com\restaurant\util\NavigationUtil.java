package com.restaurant.util;

import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.stage.Stage;
import javafx.scene.Node;
import javafx.event.ActionEvent;

import java.io.IOException;
import java.util.Map;
import java.util.HashMap;

/**
 * Utility class for handling navigation between screens
 */
public class NavigationUtil {
    
    private static Stage primaryStage;
    private static Scene mainScene;
    private static Map<String, Object> navigationParams = new HashMap<>();
    
    /**
     * Set the primary stage for the application
     */
    public static void setPrimaryStage(Stage stage) {
        primaryStage = stage;
    }
    
    /**
     * Set the main scene for the application
     */
    public static void setMainScene(Scene scene) {
        mainScene = scene;
    }
    
    /**
     * Navigate to a new FXML view
     * 
     * @param fxmlPath Path to the FXML file
     * @throws IOException If the FXML file cannot be loaded
     */
    public static void navigateTo(String fxmlPath) throws IOException {
        FXMLLoader loader = new FXMLLoader(NavigationUtil.class.getResource(fxmlPath));
        Parent root = loader.load();
        
        if (mainScene != null) {
            mainScene.setRoot(root);
        } else if (primaryStage != null) {
            Scene scene = new Scene(root);
            primaryStage.setScene(scene);
            mainScene = scene;
        } else {
            throw new IllegalStateException("Neither mainScene nor primaryStage is set");
        }
    }
    
    /**
     * Navigate to a new FXML view with parameters
     * 
     * @param fxmlPath Path to the FXML file
     * @param params Parameters to pass to the controller
     * @throws IOException If the FXML file cannot be loaded
     */
    public static void navigateToWithParams(String fxmlPath, Map<String, Object> params) throws IOException {
        navigationParams.clear();
        if (params != null) {
            navigationParams.putAll(params);
        }
        
        navigateTo(fxmlPath);
    }
    
    /**
     * Get navigation parameters
     * 
     * @return Map of navigation parameters
     */
    public static Map<String, Object> getNavigationParams() {
        return navigationParams;
    }
    
    /**
     * Navigate back to the previous view (from an event)
     * 
     * @param event The action event
     * @throws IOException If the FXML file cannot be loaded
     */
    public static void navigateBack(ActionEvent event) throws IOException {
        Stage stage = (Stage) ((Node) event.getSource()).getScene().getWindow();
        // This would require maintaining a navigation stack
        // For now, just go to the dashboard
        FXMLLoader loader = new FXMLLoader(NavigationUtil.class.getResource("/fxml/Dashboard.fxml"));
        Parent root = loader.load();
        Scene scene = new Scene(root);
        stage.setScene(scene);
    }
}
