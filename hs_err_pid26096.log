#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x0000000070c17ed0, pid=26096, tid=29884
#
# JRE version: Java(TM) SE Runtime Environment (23.0.2+7) (build 23.0.2+7-58)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (23.0.2+7-58, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# Problematic frame:
# C  0x0000000070c17ed0
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://bugreport.java.com/bugreport/crash.jsp
# The crash happened outside the Java Virtual Machine in native code.
# See problematic frame for where to report the bug.
#

---------------  S U M M A R Y ------------

Command Line: --module-path=C:\Users\<USER>\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics com.restaurant.RestaurantApp

Host: 12th Gen Intel(R) Core(TM) i5-12450H, 12 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
Time: Thu Jul 10 16:10:21 2025 India Standard Time elapsed time: 15.080543 seconds (0d 0h 0m 15s)

---------------  T H R E A D  ---------------

Current thread (0x0000015d0eebe5d0):  JavaThread "Thread-10" daemon [_thread_in_native, id=29884, stack(0x0000003920300000,0x0000003920400000) (1024K)]

Stack: [0x0000003920300000,0x0000003920400000],  sp=0x00000039203fecd0,  free space=1019k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  0x0000000070c17ed0  (no source info available)
C  0x0000015cb285dec0  (no source info available)

The last pc belongs to native method entry point (kind = native_synchronized) (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  org.sqlite.core.NativeDB.reset(J)I+0
j  org.sqlite.jdbc3.JDBC3PreparedStatement.executeQuery()Ljava/sql/ResultSet;+39
j  com.restaurant.model.ActivityDAO.getRecentActivities(I)Ljava/util/List;+39
j  com.restaurant.controller.DashboardController$2.call()Ljava/util/List;+8
j  com.restaurant.controller.DashboardController$2.call()Ljava/lang/Object;+1
j  javafx.concurrent.Task$TaskCallable.call()Ljava/lang/Object;+25 javafx.graphics
j  java.util.concurrent.FutureTask.run()V+39 java.base@23.0.2
j  java.lang.Thread.runWith(Ljava/lang/Object;Ljava/lang/Runnable;)V+5 java.base@23.0.2
j  java.lang.Thread.run()V+19 java.base@23.0.2
v  ~StubRoutines::call_stub 0x0000015cb2850fcd

siginfo: EXCEPTION_ACCESS_VIOLATION (0xc0000005), reading address 0x0000015d0e1e3ed8


Registers:
RAX=0x0000000000000000, RBX=0x0000015d0e1e3ed8, RCX=0x0000015d0e1e3ed8, RDX=0x00000039203fede0
RSP=0x00000039203fecd0, RBP=0x00000039203fedb0, RSI=0x0000015d04006860, RDI=0x00000000000f73be
R8 =0x0000015d0e1e3ed8, R9 =0x0000000000000000, R10=0x0000015cb285de91, R11=0x0000000000000121
R12=0x0000000000000000, R13=0x0000015d04fe04a8, R14=0x00000039203fede0, R15=0x0000015d0eebe5d0
RIP=0x0000000070c17ed0, EFLAGS=0x0000000000010206


Register to memory mapping:

RAX=0x0 is null
RBX=0x0000015d0e1e3ed8 is an unknown value
RCX=0x0000015d0e1e3ed8 is an unknown value
RDX=0x00000039203fede0 is pointing into the stack for thread: 0x0000015d0eebe5d0
RSP=0x00000039203fecd0 is pointing into the stack for thread: 0x0000015d0eebe5d0
RBP=0x00000039203fedb0 is pointing into the stack for thread: 0x0000015d0eebe5d0
RSI=0x0000015d04006860 is pointing into metadata
RDI=0x00000000000f73be is an unknown value
R8 =0x0000015d0e1e3ed8 is an unknown value
R9 =0x0 is null
R10=0x0000015cb285de91 is at code_begin+1329 in an Interpreter codelet
native method entry point (kind = native_synchronized)  [0x0000015cb285d960, 0x0000015cb285e5f0]  3216 bytes
R11=0x0000000000000121 is an unknown value
R12=0x0 is null
R13={method} {0x0000015d04fe04b0} 'reset' '(J)I' in 'org/sqlite/core/NativeDB'
R14=0x00000039203fede0 is pointing into the stack for thread: 0x0000015d0eebe5d0
R15=0x0000015d0eebe5d0 is a thread

Top of Stack: (sp=0x00000039203fecd0)
0x00000039203fecd0:   0000015d00000002 00000039203fedb0
0x00000039203fece0:   0000000000000000 0000015d04fe04a8
0x00000039203fecf0:   00000007136fa348 00007ffb91673f44
0x00000039203fed00:   0000015d04006860 0000015d04fe04a8
0x00000039203fed10:   0000015d04006860 0000015cb285dec0
0x00000039203fed20:   00000000000f73be 0000015cb285dc28
0x00000039203fed30:   0000000000000628 0000000000000000
0x00000039203fed40:   00000039203fed70 00000039203fedc0
0x00000039203fed50:   0000015cb285dbd0 0000000000000000
0x00000039203fed60:   00000007136fa348 fffffffffffffff5
0x00000039203fed70:   0000015d04fe04a8 0000000000000006
0x00000039203fed80:   0000015d05035140 0000000000000000
0x00000039203fed90:   00000007055e2b80 0000015d04fe04a8
0x00000039203feda0:   0000000000000000 00000039203fedd0
0x00000039203fedb0:   00000039203fee30 0000015cb2858848
0x00000039203fedc0:   0000000000000000 0000015cb285a5d5
0x00000039203fedd0:   0000015d0e1e3ed8 0000000000000000
0x00000039203fede0:   00000007136fa348 fffffffffffffff7
0x00000039203fedf0:   0000015d05066b0f 0000000000000004
0x00000039203fee00:   0000015d0506b300 0000000000000000
0x00000039203fee10:   0000000705525a38 0000015d05066ba8
0x00000039203fee20:   fffffffffffffff4 00000039203fee50
0x00000039203fee30:   00000039203feea0 0000015cb2858da0
0x00000039203fee40:   0000000000000000 0000000000000000
0x00000039203fee50:   0000000714736890 fffffffffffffff7
0x00000039203fee60:   0000015d052153af 0000000000000009
0x00000039203fee70:   0000015d05219730 0000000000000000
0x00000039203fee80:   0000000713701348 0000015d05215518
0x00000039203fee90:   fffffffffffffff6 00000039203feee8
0x00000039203feea0:   00000039203fef38 0000015cb285889a
0x00000039203feeb0:   0000000000000000 0000000000000000
0x00000039203feec0:   0000000714736890 00000007136fa328 

Instructions: (pc=0x0000000070c17ed0)
0x0000000070c17dd0:   89 44 24 20 48 8b 13 e8 2c af fb ff 48 8b 43 10
0x0000000070c17de0:   48 89 43 28 eb 17 48 8b 4c 24 40 49 89 c0 48 89
0x0000000070c17df0:   da 48 81 c1 cf 01 00 00 e8 89 b0 fb ff 45 31 ed
0x0000000070c17e00:   48 8b 43 18 45 89 2c 24 48 89 45 28 48 81 c4 28
0x0000000070c17e10:   01 00 00 5b 5e 5f 5d 41 5c 41 5d 41 5e 41 5f c3
0x0000000070c17e20:   48 83 ec 38 48 8d 05 6d 8c ff ff 48 89 44 24 20
0x0000000070c17e30:   e8 17 f3 ff ff 48 83 c4 38 c3 56 53 48 83 ec 28
0x0000000070c17e40:   48 8b 31 48 89 cb 48 8b 49 10 48 85 c9 74 05 e8
0x0000000070c17e50:   78 cc ff ff 48 8b 93 58 01 00 00 48 89 f1 e8 63
0x0000000070c17e60:   53 fc ff 48 8b 93 60 01 00 00 48 85 d2 74 15 48
0x0000000070c17e70:   8b 42 28 48 89 f1 48 89 83 60 01 00 00 e8 1e 44
0x0000000070c17e80:   fc ff eb df 48 8b 93 68 01 00 00 48 89 f1 e8 b6
0x0000000070c17e90:   54 fc ff 48 8b 53 08 48 89 f1 e8 a8 51 fb ff 48
0x0000000070c17ea0:   8b 93 a8 01 00 00 48 89 f1 e8 5f 53 fb ff 48 89
0x0000000070c17eb0:   d9 48 83 c4 28 5b 5e e9 27 42 fc ff 56 53 48 83
0x0000000070c17ec0:   ec 38 31 c0 48 85 c9 48 89 cb 0f 84 84 00 00 00
0x0000000070c17ed0:   48 8b 31 48 8b 4e 18 e8 3d c1 fa ff 48 83 bb b8
0x0000000070c17ee0:   00 00 00 00 7e 0b 48 89 da 48 89 f1 e8 15 4f fb
0x0000000070c17ef0:   ff 48 89 d9 e8 0f cb ff ff 48 89 f1 89 c2 c7 43
0x0000000070c17f00:   24 a3 0d f2 2d c7 43 34 ff ff ff ff c7 43 38 00
0x0000000070c17f10:   00 00 00 c6 83 c2 00 00 00 02 c7 43 3c 00 00 00
0x0000000070c17f20:   00 c7 43 30 01 00 00 00 c6 83 c3 00 00 00 ff c7
0x0000000070c17f30:   43 40 00 00 00 00 48 c7 43 50 00 00 00 00 e8 6c
0x0000000070c17f40:   a4 fb ff 48 8b 4e 18 89 44 24 2c e8 e5 c0 fa ff
0x0000000070c17f50:   8b 44 24 2c 48 83 c4 38 5b 5e c3 55 57 56 53 48
0x0000000070c17f60:   83 ec 28 48 8b 31 48 89 cf 48 8b 89 a0 00 00 00
0x0000000070c17f70:   e8 fa 4f fb ff 48 8b 8f b0 00 00 00 e8 ee 4f fb
0x0000000070c17f80:   ff 48 8b 5f 38 48 85 db 74 36 83 7f 20 05 b8 02
0x0000000070c17f90:   00 00 00 75 09 31 c0 83 7f 24 00 0f 95 c0 48 8b
0x0000000070c17fa0:   56 28 48 89 d9 48 8d 2c c2 48 83 7d 28 00 75 0b
0x0000000070c17fb0:   e8 07 ff ff ff 48 89 5d 28 eb 05 e8 47 cb ff ff
0x0000000070c17fc0:   48 8b 5f 48 48 85 db 74 10 48 8b 0b e8 36 cb ff 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x0000015d00000002 is an unknown value
stack at sp + 1 slots: 0x00000039203fedb0 is pointing into the stack for thread: 0x0000015d0eebe5d0
stack at sp + 2 slots: 0x0 is null
stack at sp + 3 slots: {method} {0x0000015d04fe04b0} 'reset' '(J)I' in 'org/sqlite/core/NativeDB'
stack at sp + 4 slots: 0x00000007136fa348 is an oop: org.sqlite.core.NativeDB 
{0x00000007136fa348} - klass: 'org/sqlite/core/NativeDB'
 - ---- fields (total size 10 words):
 - private final 'url' 'Ljava/lang/String;' @12  "*************************"{0x00000007052c59b8} (0xe0a58b37)
 - 'begin' 'J' @16  0 (0x0000000000000000)
 - 'commit' 'J' @24  0 (0x0000000000000000)
 - private final 'fileName' 'Ljava/lang/String;' @32  "C:\Users\<USER>\Downloads\restaurant-desktop (6)\restaurant-desktop\restaurant.db"{0x00000007136fa398} (0xe26df473)
 - private final 'config' 'Lorg/sqlite/SQLiteConfig;' @36  a 'org/sqlite/SQLiteConfig'{0x00000007136fa418} (0xe26df483)
 - private final 'closed' 'Ljava/util/concurrent/atomic/AtomicBoolean;' @40  a 'java/util/concurrent/atomic/AtomicBoolean'{0x00000007136fa560} (0xe26df4ac)
 - private final 'stmts' 'Ljava/util/Map;' @44  a 'java/util/HashMap'{0x00000007136fa570} (0xe26df4ae)
 - private final 'updateListeners' 'Ljava/util/Set;' @48  a 'java/util/HashSet'{0x00000007136fa768} (0xe26df4ed)
 - private final 'commitListeners' 'Ljava/util/Set;' @52  a 'java/util/HashSet'{0x00000007136fa7a8} (0xe26df4f5)
 - 'pointer' 'J' @56  0 (0x0000000000000000)
 - private final 'udfdatalist' 'J' @64  0 (0x0000000000000000)
 - private final 'colldatalist' 'J' @72  0 (0x0000000000000000)
stack at sp + 5 slots: 0x00007ffb91673f44 jvm.dll
stack at sp + 6 slots: 0x0000015d04006860 is pointing into metadata
stack at sp + 7 slots: {method} {0x0000015d04fe04b0} 'reset' '(J)I' in 'org/sqlite/core/NativeDB'

Lock stack of current Java thread (top to bottom):

native method entry point (kind = native_synchronized)  [0x0000015cb285d960, 0x0000015cb285e5f0]  3216 bytes
[MachCode]
  0x0000015cb285d960: 488b 4b08 | 0fb7 492e | 584c 8d74 | ccf8 6800 | 0000 0068 | 0000 0000 | 5055 488b | ec41 5568 
  0x0000015cb285d980: 0000 0000 | 4c8b 6b08 | 4d8d 6d38 | 5348 8b53 | 0848 8b52 | 0848 8b52 | 1848 8b52 | 7048 8b12 
  0x0000015cb285d9a0: 5248 8b53 | 1048 85d2 | 0f84 0700 | 0000 4881 | c210 0100 | 0052 488b | 5308 488b | 5208 488b 
  0x0000015cb285d9c0: 5210 5249 | 8bc6 482b | c548 c1e8 | 0350 6800 | 0000 0068 | f7ff ffff | 41c6 8771 | 0400 0001 
  0x0000015cb285d9e0: 488b 4310 | 4885 c074 | 208b 88cc | 0000 0083 | c102 8988 | cc00 0000 | 2388 e000 | 0000 0f84 
  0x0000015cb285da00: 390b 0000 | e9cf 0000 | 0048 8b43 | 1848 85c0 | 0f85 b000 | 0000 e805 | 0000 00e9 | 9900 0000 
  0x0000015cb285da20: 488b d348 | 8d44 2408 | 4c89 6dc0 | 498b cfc5 | f877 4989 | afb0 0300 | 0049 8987 | a003 0000 
  0x0000015cb285da40: 4883 ec20 | 40f6 c40f | 0f84 1900 | 0000 4883 | ec08 48b8 | 8028 6791 | fb7f 0000 | ffd0 4883 
  0x0000015cb285da60: c408 e90c | 0000 0048 | b880 2867 | 91fb 7f00 | 00ff d048 | 83c4 2049 | c787 a003 | 0000 0000 
  0x0000015cb285da80: 0000 49c7 | 87b0 0300 | 0000 0000 | 0049 c787 | a803 0000 | 0000 0000 | c5f8 7749 | 837f 0800 
  0x0000015cb285daa0: 0f84 0500 | 0000 e915 | 34ff ff4c | 8b6d c04c | 8b75 c84e | 8d74 f500 | c348 8b43 | 1848 85c0 
  0x0000015cb285dac0: 0f84 1200 | 0000 8b48 | 0883 c102 | 8948 0823 | 481c 0f84 | 650a 0000 | 493b a7e8 | 0400 000f 
  0x0000015cb285dae0: 8748 0000 | 0089 8424 | 00f0 ffff | 8984 2400 | e0ff ff89 | 8424 00d0 | ffff 8984 | 2400 c0ff 
  0x0000015cb285db00: ff89 8424 | 00b0 ffff | 8984 2400 | a0ff ff89 | 8424 0090 | ffff 8984 | 2400 80ff | ff49 3ba7 
  0x0000015cb285db20: e004 0000 | 7607 4989 | a7e8 0400 | 0041 c687 | 7104 0000 | 008b 4328 | a808 498b | 060f 8413 
  0x0000015cb285db40: 0000 0048 | 8b43 0848 | 8b40 0848 | 8b40 1848 | 8b40 7048 | 8b00 4883 | ec10 4883 | 6db8 0248 
  0x0000015cb285db60: 8944 2408 | 488b d44c | 8b4a 0849 | 8b01 418b | 9f18 0600 | 0081 fb68 | 0600 000f | 8d4a 0000 
  0x0000015cb285db80: 004d 3b4c | 1ff8 0f84 | 2500 0000 | a802 0f85 | 3700 0000 | 488b d848 | 83e3 fe48 | 83c8 01f0 
  0x0000015cb285dba0: 490f b119 | 0f85 2100 | 0000 418b | 9f18 0600 | 004d 890c | 1f83 c308 | 4189 9f18 | 0600 0049 
  0x0000015cb285dbc0: ff87 5005 | 0000 e9a3 | 0000 00e8 | 0500 0000 | e999 0000 | 0049 8bd1 | 488d 4424 | 084c 896d 
  0x0000015cb285dbe0: c049 8bcf | c5f8 7749 | 89af b003 | 0000 4989 | 87a0 0300 | 0048 83ec | 2040 f6c4 | 0f0f 8419 
  0x0000015cb285dc00: 0000 0048 | 83ec 0848 | b8e0 3e67 | 91fb 7f00 | 00ff d048 | 83c4 08e9 | 0c00 0000 | 48b8 e03e 
  0x0000015cb285dc20: 6791 fb7f | 0000 ffd0 | 4883 c420 | 49c7 87a0 | 0300 0000 | 0000 0049 | c787 b003 | 0000 0000 
  0x0000015cb285dc40: 0000 49c7 | 87a8 0300 | 0000 0000 | 00c5 f877 | 4983 7f08 | 000f 8405 | 0000 00e9 | 6032 ffff 
  0x0000015cb285dc60: 4c8b 6dc0 | 4c8b 75c8 | 4e8d 74f5 | 00c3 49ba | 41bd ee91 | fb7f 0000 | 4180 3a00 | 0f84 3e00 
  0x0000015cb285dc80: 0000 488b | 55e8 498b | cf48 83ec | 2040 f6c4 | 0f0f 8419 | 0000 0048 | 83ec 0848 | b8f0 f09e 
  0x0000015cb285dca0: 91fb 7f00 | 00ff d048 | 83c4 08e9 | 0c00 0000 | 48b8 f0f0 | 9e91 fb7f | 0000 ffd0 | 4883 c420 
  0x0000015cb285dcc0: 488b 5de8 | 4c8b 5b08 | 450f b75b | 2e41 c1e3 | 0349 2be3 | 4883 ec20 | 4883 e4f0 | 4c8b 5b60 
  0x0000015cb285dce0: 4d85 db0f | 85ab 0000 | 00e8 0500 | 0000 e999 | 0000 0048 | 8bd3 488d | 4424 084c | 896d c049 
  0x0000015cb285dd00: 8bcf c5f8 | 7749 89af | b003 0000 | 4989 87a0 | 0300 0048 | 83ec 2040 | f6c4 0f0f | 8419 0000 
  0x0000015cb285dd20: 0048 83ec | 0848 b8e0 | 4f67 91fb | 7f00 00ff | d048 83c4 | 08e9 0c00 | 0000 48b8 | e04f 6791 
  0x0000015cb285dd40: fb7f 0000 | ffd0 4883 | c420 49c7 | 87a0 0300 | 0000 0000 | 0049 c787 | b003 0000 | 0000 0000 
  0x0000015cb285dd60: 49c7 87a8 | 0300 0000 | 0000 00c5 | f877 4983 | 7f08 000f | 8405 0000 | 00e9 4231 | ffff 4c8b 
  0x0000015cb285dd80: 6dc0 4c8b | 75c8 4e8d | 74f5 00c3 | 488b 5de8 | 4c8b 5b60 | 41ff d348 | 8b5d e848 | 8945 1844 
  0x0000015cb285dda0: 8b5b 2841 | f6c3 080f | 841b 0000 | 004c 8b5b | 084d 8b5b | 084d 8b5b | 184d 8b5b | 704d 8b1b 
  0x0000015cb285ddc0: 4c89 5d10 | 488d 5510 | 488b 4358 | 49ba 403f | 9f91 fb7f | 0000 493b | c20f 85ab | 0000 00e8 
  0x0000015cb285dde0: 0500 0000 | e999 0000 | 0048 8bd3 | 488d 4424 | 084c 896d | c049 8bcf | c5f8 7749 | 89af b003 
  0x0000015cb285de00: 0000 4989 | 87a0 0300 | 0048 83ec | 2040 f6c4 | 0f0f 8419 | 0000 0048 | 83ec 0848 | b8e0 4f67 
  0x0000015cb285de20: 91fb 7f00 | 00ff d048 | 83c4 08e9 | 0c00 0000 | 48b8 e04f | 6791 fb7f | 0000 ffd0 | 4883 c420 
  0x0000015cb285de40: 49c7 87a0 | 0300 0000 | 0000 0049 | c787 b003 | 0000 0000 | 0000 49c7 | 87a8 0300 | 0000 0000 
  0x0000015cb285de60: 00c5 f877 | 4983 7f08 | 000f 8405 | 0000 00e9 | 4c30 ffff | 4c8b 6dc0 | 4c8b 75c8 | 4e8d 74f5 
  0x0000015cb285de80: 00c3 488b | 5de8 488b | 4358 498d | 8fc0 0300 | 00c5 f877 | 4989 afb0 | 0300 0049 | ba91 de85 
  0x0000015cb285dea0: b25c 0100 | 004d 8997 | a803 0000 | 4989 a7a0 | 0300 0041 | c787 4404 | 0000 0400 | 0000 ffd0 
  0x0000015cb285dec0: c5f8 7748 | 83ec 10c5 | fb11 0424 | 4883 ec10 | 4889 0424 | 48c7 4424 | 0800 0000 | 0041 c787 
  0x0000015cb285dee0: 4404 0000 | 0500 0000 | f083 4424 | c000 493b | af48 0400 | 000f 870e | 0000 0041 | 83bf 4004 
  0x0000015cb285df00: 0000 000f | 8420 0000 | 0049 8bcf | 4c8b e448 | 83ec 2048 | 83e4 f048 | b880 ab68 | 91fb 7f00 
  0x0000015cb285df20: 00ff d049 | 8be4 4d33 | e441 c787 | 4404 0000 | 0800 0000 | 49c7 87a0 | 0300 0000 | 0000 0049 
  0x0000015cb285df40: c787 b003 | 0000 0000 | 0000 49c7 | 87a8 0300 | 0000 0000 | 00c5 f877 | 4d8b 9f30 | 0400 0041 
  0x0000015cb285df60: c783 0001 | 0000 0000 | 0000 49bb | daa5 85b2 | 5c01 0000 | 4c3b 5d18 | 0f85 2402 | 0000 488b 
  0x0000015cb285df80: 0424 4883 | c410 4885 | c00f 84fe | 0100 00a8 | 030f 8508 | 0000 0048 | 8b00 e9ee | 0100 00a8 
  0x0000015cb285dfa0: 010f 8509 | 0000 0048 | 8b40 fee9 | dd01 0000 | 488b 40ff | 4180 7f38 | 000f 84ce | 0100 0048 
  0x0000015cb285dfc0: 83f8 000f | 84c4 0100 | 004d 8b5f | 2849 83fb | 000f 8414 | 0000 0049 | 83eb 084d | 895f 284d 
  0x0000015cb285dfe0: 035f 3049 | 8903 e9a2 | 0100 0048 | 81ec d000 | 0000 4889 | 0424 4889 | 4c24 0848 | 8954 2410 
  0x0000015cb285e000: 4889 7424 | 1848 897c | 2420 4c89 | 4424 284c | 894c 2430 | 4c89 5424 | 384c 895c | 2440 c5fb 
  0x0000015cb285e020: 1144 2450 | c5fb 114c | 2458 c5fb | 1154 2460 | c5fb 115c | 2468 c5fb | 1164 2470 | c5fb 116c 
  0x0000015cb285e040: 2478 c5fb | 11b4 2480 | 0000 00c5 | fb11 bc24 | 8800 0000 | c57b 1184 | 2490 0000 | 00c5 7b11 
  0x0000015cb285e060: 8c24 9800 | 0000 c57b | 1194 24a0 | 0000 00c5 | 7b11 9c24 | a800 0000 | c57b 11a4 | 24b0 0000 
  0x0000015cb285e080: 00c5 7b11 | ac24 b800 | 0000 c57b | 11b4 24c0 | 0000 00c5 | 7b11 bc24 | c800 0000 | 498b d748 
  0x0000015cb285e0a0: 8bc8 4883 | ec20 40f6 | c40f 0f84 | 1900 0000 | 4883 ec08 | 48b8 0009 | 5c91 fb7f | 0000 ffd0 
  0x0000015cb285e0c0: 4883 c408 | e90c 0000 | 0048 b800 | 095c 91fb | 7f00 00ff | d048 83c4 | 20c5 7b10 | bc24 c800 
  0x0000015cb285e0e0: 0000 c57b | 10b4 24c0 | 0000 00c5 | 7b10 ac24 | b800 0000 | c57b 10a4 | 24b0 0000 | 00c5 7b10 
  0x0000015cb285e100: 9c24 a800 | 0000 c57b | 1094 24a0 | 0000 00c5 | 7b10 8c24 | 9800 0000 | c57b 1084 | 2490 0000 
  0x0000015cb285e120: 00c5 fb10 | bc24 8800 | 0000 c5fb | 10b4 2480 | 0000 00c5 | fb10 6c24 | 78c5 fb10 | 6424 70c5 
  0x0000015cb285e140: fb10 5c24 | 68c5 fb10 | 5424 60c5 | fb10 4c24 | 58c5 fb10 | 4424 504c | 8b5c 2440 | 4c8b 5424 
  0x0000015cb285e160: 384c 8b4c | 2430 4c8b | 4424 2848 | 8b7c 2420 | 488b 7424 | 1848 8b54 | 2410 488b | 4c24 0848 
  0x0000015cb285e180: 8b04 2448 | 81c4 d000 | 0000 c5f8 | 7748 8945 | 1048 83ec | 1048 8904 | 2448 c744 | 2408 0000 
  0x0000015cb285e1a0: 0000 4183 | bfc8 0400 | 0002 0f85 | bf00 0000 | 4881 ec80 | 0000 0048 | 8944 2478 | 4889 4c24 
  0x0000015cb285e1c0: 7048 8954 | 2468 4889 | 5c24 6048 | 896c 2450 | 4889 7424 | 4848 897c | 2440 4c89 | 4424 384c 
  0x0000015cb285e1e0: 894c 2430 | 4c89 5424 | 284c 895c | 2420 4c89 | 6424 184c | 896c 2410 | 4c89 7424 | 084c 893c 
  0x0000015cb285e200: 244c 8be4 | 4883 ec20 | 4883 e4f0 | 48b8 a025 | 9f91 fb7f | 0000 ffd0 | 498b e44c | 8b3c 244c 
  0x0000015cb285e220: 8b74 2408 | 4c8b 6c24 | 104c 8b64 | 2418 4c8b | 5c24 204c | 8b54 2428 | 4c8b 4c24 | 304c 8b44 
  0x0000015cb285e240: 2438 488b | 7c24 4048 | 8b74 2448 | 488b 6c24 | 5048 8b5c | 2460 488b | 5424 6848 | 8b4c 2470 
  0x0000015cb285e260: 488b 4424 | 7848 81c4 | 8000 0000 | 4d33 e448 | 8b5d e84c | 8b6b 084d | 8d6d 3849 | 837f 0800 
  0x0000015cb285e280: 0f84 bb00 | 0000 e805 | 0000 00e9 | 9600 0000 | 488d 4424 | 084c 896d | c049 8bcf | c5f8 7749 
  0x0000015cb285e2a0: 89af b003 | 0000 4989 | 87a0 0300 | 0048 83ec | 2040 f6c4 | 0f0f 8419 | 0000 0048 | 83ec 0848 
  0x0000015cb285e2c0: b8f0 6d67 | 91fb 7f00 | 00ff d048 | 83c4 08e9 | 0c00 0000 | 48b8 f06d | 6791 fb7f | 0000 ffd0 
  0x0000015cb285e2e0: 4883 c420 | 49c7 87a0 | 0300 0000 | 0000 0049 | c787 b003 | 0000 0000 | 0000 49c7 | 87a8 0300 
  0x0000015cb285e300: 0000 0000 | 00c5 f877 | 4983 7f08 | 000f 8405 | 0000 00e9 | a82b ffff | 4c8b 6dc0 | 4c8b 75c8 
  0x0000015cb285e320: 4e8d 74f5 | 00c3 48b9 | 40e5 c691 | fb7f 0000 | 4883 e4f0 | 48b8 504d | 8d91 fb7f | 0000 ffd0 
  0x0000015cb285e340: f444 8b5b | 2841 f6c3 | 200f 8479 | 0100 0048 | 8d55 a84c | 8b5a 084d | 85db 0f85 | bb00 0000 
  0x0000015cb285e360: e805 0000 | 00e9 9600 | 0000 488d | 4424 084c | 896d c049 | 8bcf c5f8 | 7749 89af | b003 0000 
  0x0000015cb285e380: 4989 87a0 | 0300 0048 | 83ec 2040 | f6c4 0f0f | 8419 0000 | 0048 83ec | 0848 b820 | 6d67 91fb 
  0x0000015cb285e3a0: 7f00 00ff | d048 83c4 | 08e9 0c00 | 0000 48b8 | 206d 6791 | fb7f 0000 | ffd0 4883 | c420 49c7 
  0x0000015cb285e3c0: 87a0 0300 | 0000 0000 | 0049 c787 | b003 0000 | 0000 0000 | 49c7 87a8 | 0300 0000 | 0000 00c5 
  0x0000015cb285e3e0: f877 4983 | 7f08 000f | 8405 0000 | 00e9 ce2a | ffff 4c8b | 6dc0 4c8b | 75c8 4e8d | 74f5 00c3 
  0x0000015cb285e400: 48b9 40e5 | c691 fb7f | 0000 4883 | e4f0 48b8 | 504d 8d91 | fb7f 0000 | ffd0 f44c | 896d c04c 
  0x0000015cb285e420: 8b4a 0848 | c742 0800 | 0000 0045 | 8b87 1806 | 0000 4f3b | 4c07 f80f | 8549 0000 | 0041 83af 
  0x0000015cb285e440: 1806 0000 | 084f 3b4c | 07f0 0f84 | 2a00 0000 | 498b 01a8 | 020f 8512 | 0000 004c | 8bc0 4983 
  0x0000015cb285e460: c801 f04d | 0fb1 010f | 840d 0000 | 0041 8387 | 1806 0000 | 08e9 0c00 | 0000 49ff | 8f50 0500 
  0x0000015cb285e480: 00e9 3e00 | 0000 4c89 | 4a08 488b | ca48 83ec | 2040 f6c4 | 0f0f 8419 | 0000 0048 | 83ec 0848 
  0x0000015cb285e4a0: b8e0 3f67 | 91fb 7f00 | 00ff d048 | 83c4 08e9 | 0c00 0000 | 48b8 e03f | 6791 fb7f | 0000 ffd0 
  0x0000015cb285e4c0: 4883 c420 | 4c8b 6dc0 | 49ba 41bd | ee91 fb7f | 0000 4180 | 3a00 0f84 | 3e00 0000 | 488b 55e8 
  0x0000015cb285e4e0: 498b cf48 | 83ec 2040 | f6c4 0f0f | 8419 0000 | 0048 83ec | 0848 b8f0 | f09e 91fb | 7f00 00ff 
  0x0000015cb285e500: d048 83c4 | 08e9 0c00 | 0000 48b8 | f0f0 9e91 | fb7f 0000 | ffd0 4883 | c420 488b | 0424 4883 
  0x0000015cb285e520: c410 c5fb | 1004 2448 | 83c4 104c | 8b5d 1841 | ffd3 4c8b | 5df8 c95f | 498b e3ff | e7ba 0000 
  0x0000015cb285e540: 0000 e805 | 0000 00e9 | 9600 0000 | 488d 4424 | 084c 896d | c049 8bcf | c5f8 7749 | 89af b003 
  0x0000015cb285e560: 0000 4989 | 87a0 0300 | 0048 83ec | 2040 f6c4 | 0f0f 8419 | 0000 0048 | 83ec 0848 | b890 3367 
  0x0000015cb285e580: 91fb 7f00 | 00ff d048 | 83c4 08e9 | 0c00 0000 | 48b8 9033 | 6791 fb7f | 0000 ffd0 | 4883 c420 
  0x0000015cb285e5a0: 49c7 87a0 | 0300 0000 | 0000 0049 | c787 b003 | 0000 0000 | 0000 49c7 | 87a8 0300 | 0000 0000 
  0x0000015cb285e5c0: 00c5 f877 | 4983 7f08 | 000f 8405 | 0000 00e9 | ec28 ffff | 4c8b 6dc0 | 4c8b 75c8 | 4e8d 74f5 
  0x0000015cb285e5e0: 00c3 488b | 5de8 e9ed | f4ff ff0f | 1f44 0000 
[/MachCode]

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000015d0eea4490, length=25, elements={
0x0000015ca2be5210, 0x0000015cc2366980, 0x0000015cc23675e0, 0x0000015cc236fe90,
0x0000015cc2372a40, 0x0000015cc237bc30, 0x0000015cc237de20, 0x0000015cc238ce60,
0x0000015cc238d520, 0x0000015cc24722c0, 0x0000015d080c2810, 0x0000015d08393260,
0x0000015d08556db0, 0x0000015d08559cc0, 0x0000015d092cae10, 0x0000015d092d1a80,
0x0000015d095f3010, 0x0000015d0eebf2f0, 0x0000015d130da900, 0x0000015cc24a3120,
0x0000015cc24a3e40, 0x0000015cc24a37b0, 0x0000015cc24a51f0, 0x0000015d0eebe5d0,
0x0000015d0e941bb0
}

Java Threads: ( => current thread )
  0x0000015ca2be5210 JavaThread "main"                              [_thread_blocked, id=28476, stack(0x000000391c600000,0x000000391c700000) (1024K)]
  0x0000015cc2366980 JavaThread "Reference Handler"          daemon [_thread_blocked, id=30700, stack(0x000000391ce00000,0x000000391cf00000) (1024K)]
  0x0000015cc23675e0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=22084, stack(0x000000391cf00000,0x000000391d000000) (1024K)]
  0x0000015cc236fe90 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=5400, stack(0x000000391d000000,0x000000391d100000) (1024K)]
  0x0000015cc2372a40 JavaThread "Attach Listener"            daemon [_thread_blocked, id=29408, stack(0x000000391d100000,0x000000391d200000) (1024K)]
  0x0000015cc237bc30 JavaThread "Service Thread"             daemon [_thread_blocked, id=11420, stack(0x000000391d200000,0x000000391d300000) (1024K)]
  0x0000015cc237de20 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=3328, stack(0x000000391d300000,0x000000391d400000) (1024K)]
  0x0000015cc238ce60 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=26648, stack(0x000000391d400000,0x000000391d500000) (1024K)]
  0x0000015cc238d520 JavaThread "C1 CompilerThread0"         daemon [_thread_in_native, id=13660, stack(0x000000391d500000,0x000000391d600000) (1024K)]
  0x0000015cc24722c0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=29096, stack(0x000000391d600000,0x000000391d700000) (1024K)]
  0x0000015d080c2810 JavaThread "Notification Thread"        daemon [_thread_blocked, id=8688, stack(0x000000391d900000,0x000000391da00000) (1024K)]
  0x0000015d08393260 JavaThread "QuantumRenderer-0"          daemon [_thread_blocked, id=29272, stack(0x000000391da00000,0x000000391db00000) (1024K)]
  0x0000015d08556db0 JavaThread "InvokeLaterDispatcher"      daemon [_thread_blocked, id=26044, stack(0x000000391d700000,0x000000391d800000) (1024K)]
  0x0000015d08559cc0 JavaThread "JavaFX Application Thread"         [_thread_in_vm, id=18636, stack(0x000000391d800000,0x000000391d900000) (1024K)]
  0x0000015d092cae10 JavaThread "JavaFX-Launcher"                   [_thread_blocked, id=14480, stack(0x000000391eb00000,0x000000391ec00000) (1024K)]
  0x0000015d092d1a80 JavaThread "Thread-2"                   daemon [_thread_in_native, id=27944, stack(0x000000391ea00000,0x000000391eb00000) (1024K)]
  0x0000015d095f3010 JavaThread "Prism Font Disposer"        daemon [_thread_blocked, id=23804, stack(0x000000391ec00000,0x000000391ed00000) (1024K)]
  0x0000015d0eebf2f0 JavaThread "Cleaner-0"                  daemon [_thread_blocked, id=25112, stack(0x000000391fb00000,0x000000391fc00000) (1024K)]
  0x0000015d130da900 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=11120, stack(0x000000391ed00000,0x000000391ee00000) (1024K)]
  0x0000015cc24a3120 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=23588, stack(0x000000391fd00000,0x000000391fe00000) (1024K)]
  0x0000015cc24a3e40 JavaThread "pool-2-thread-2"                   [_thread_blocked, id=26484, stack(0x000000391fe00000,0x000000391ff00000) (1024K)]
  0x0000015cc24a37b0 JavaThread "Timer-0"                    daemon [_thread_blocked, id=5564, stack(0x000000391ff00000,0x0000003920000000) (1024K)]
  0x0000015cc24a51f0 JavaThread "Timer-1"                           [_thread_blocked, id=26860, stack(0x000000391fa00000,0x000000391fb00000) (1024K)]
=>0x0000015d0eebe5d0 JavaThread "Thread-10"                  daemon [_thread_in_native, id=29884, stack(0x0000003920300000,0x0000003920400000) (1024K)]
  0x0000015d0e941bb0 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=24348, stack(0x000000391fc00000,0x000000391fd00000) (1024K)]
Total: 25

Other Threads:
  0x0000015cc234fb90 VMThread "VM Thread"                           [id=24964, stack(0x000000391cd00000,0x000000391ce00000) (1024K)]
  0x0000015cc2339750 WatcherThread "VM Periodic Task Thread"        [id=27224, stack(0x000000391cc00000,0x000000391cd00000) (1024K)]
  0x0000015ca2c4bdb0 WorkerThread "GC Thread#0"                     [id=26500, stack(0x000000391c700000,0x000000391c800000) (1024K)]
  0x0000015d0935d120 WorkerThread "GC Thread#1"                     [id=4784, stack(0x000000391ee00000,0x000000391ef00000) (1024K)]
  0x0000015d09335880 WorkerThread "GC Thread#2"                     [id=27140, stack(0x000000391ef00000,0x000000391f000000) (1024K)]
  0x0000015d093d2dd0 WorkerThread "GC Thread#3"                     [id=25948, stack(0x000000391f000000,0x000000391f100000) (1024K)]
  0x0000015d09335c30 WorkerThread "GC Thread#4"                     [id=30032, stack(0x000000391f100000,0x000000391f200000) (1024K)]
  0x0000015d09335fe0 WorkerThread "GC Thread#5"                     [id=18700, stack(0x000000391f200000,0x000000391f300000) (1024K)]
  0x0000015d094535f0 WorkerThread "GC Thread#6"                     [id=11340, stack(0x000000391f300000,0x000000391f400000) (1024K)]
  0x0000015d095d41d0 WorkerThread "GC Thread#7"                     [id=28308, stack(0x000000391f400000,0x000000391f500000) (1024K)]
  0x0000015d095d5090 WorkerThread "GC Thread#8"                     [id=12808, stack(0x000000391f500000,0x000000391f600000) (1024K)]
  0x0000015d095d5440 WorkerThread "GC Thread#9"                     [id=28148, stack(0x000000391f600000,0x000000391f700000) (1024K)]
  0x0000015ca2c60810 ConcurrentGCThread "G1 Main Marker"            [id=19040, stack(0x000000391c800000,0x000000391c900000) (1024K)]
  0x0000015ca2c631b0 WorkerThread "G1 Conc#0"                       [id=30460, stack(0x000000391c900000,0x000000391ca00000) (1024K)]
  0x0000015d0960ef80 WorkerThread "G1 Conc#1"                       [id=6768, stack(0x0000003920000000,0x0000003920100000) (1024K)]
  0x0000015d0960f330 WorkerThread "G1 Conc#2"                       [id=24308, stack(0x0000003920200000,0x0000003920300000) (1024K)]
  0x0000015cc220b6a0 ConcurrentGCThread "G1 Refine#0"               [id=24848, stack(0x000000391ca00000,0x000000391cb00000) (1024K)]
  0x0000015cc220dee0 ConcurrentGCThread "G1 Service"                [id=27704, stack(0x000000391cb00000,0x000000391cc00000) (1024K)]
Total: 18

Threads with active compile tasks:
C2 CompilerThread0  15629 5118       4       javafx.collections.ListChangeBuilder::commit (516 bytes)
C2 CompilerThread1  15629 5046  s!   4       com.sun.scenario.effect.impl.ImagePool::checkOut (447 bytes)
C2 CompilerThread2  15629 5087       4       com.sun.prism.impl.ps.BaseShaderContext::checkState (211 bytes)
Total: 3

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000704c00000, size: 4020 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000015cc3000000-0x0000015cc3d70000-0x0000015cc3d70000), size 14090240, SharedBaseAddress: 0x0000015cc3000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000015cc4000000-0x0000015d04000000, reserved size: 1073741824
Narrow klass base: 0x0000015cc3000000, Narrow klass shift: 0, Narrow klass range: 0x41000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096
 CPUs: 12 total, 12 available
 Memory: 16076M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 252M
 Heap Max Capacity: 4020M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total reserved 4116480K, committed 69632K, used 38508K [0x0000000704c00000, 0x0000000800000000)
  region size 2048K, 13 young (26624K), 2 survivors (4096K)
 Metaspace       used 22141K, committed 22592K, reserved 1114112K
  class space    used 3173K, committed 3392K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000704c00000, 0x0000000704e00000, 0x0000000704e00000|100%| O|  |TAMS 0x0000000704c00000| PB 0x0000000704c00000| Untracked |  0
|   1|0x0000000704e00000, 0x0000000705000000, 0x0000000705000000|100%| O|  |TAMS 0x0000000704e00000| PB 0x0000000704e00000| Untracked |  0
|   2|0x0000000705000000, 0x0000000705200000, 0x0000000705200000|100%|HS|  |TAMS 0x0000000705000000| PB 0x0000000705000000| Complete |  0
|   3|0x0000000705200000, 0x0000000705400000, 0x0000000705400000|100%| O|  |TAMS 0x0000000705200000| PB 0x0000000705200000| Untracked |  0
|   4|0x0000000705400000, 0x0000000705600000, 0x0000000705600000|100%| O|  |TAMS 0x0000000705400000| PB 0x0000000705400000| Untracked |  0
|   5|0x0000000705600000, 0x0000000705800000, 0x0000000705800000|100%| O|  |TAMS 0x0000000705600000| PB 0x0000000705600000| Untracked |  0
|   6|0x0000000705800000, 0x0000000705a00000, 0x0000000705a00000|100%| O|  |TAMS 0x0000000705800000| PB 0x0000000705800000| Untracked |  0
|   7|0x0000000705a00000, 0x0000000705a86dc0, 0x0000000705c00000| 26%| O|  |TAMS 0x0000000705a00000| PB 0x0000000705a00000| Untracked |  0
|   8|0x0000000705c00000, 0x0000000705c00000, 0x0000000705e00000|  0%| F|  |TAMS 0x0000000705c00000| PB 0x0000000705c00000| Untracked |  0
|   9|0x0000000705e00000, 0x0000000705e00000, 0x0000000706000000|  0%| F|  |TAMS 0x0000000705e00000| PB 0x0000000705e00000| Untracked |  0
|  10|0x0000000706000000, 0x0000000706000000, 0x0000000706200000|  0%| F|  |TAMS 0x0000000706000000| PB 0x0000000706000000| Untracked |  0
|  11|0x0000000706200000, 0x0000000706200000, 0x0000000706400000|  0%| F|  |TAMS 0x0000000706200000| PB 0x0000000706200000| Untracked |  0
|  12|0x0000000706400000, 0x0000000706400000, 0x0000000706600000|  0%| F|  |TAMS 0x0000000706400000| PB 0x0000000706400000| Untracked |  0
|  13|0x0000000706600000, 0x0000000706600000, 0x0000000706800000|  0%| F|  |TAMS 0x0000000706600000| PB 0x0000000706600000| Untracked |  0
|  14|0x0000000706800000, 0x0000000706800000, 0x0000000706a00000|  0%| F|  |TAMS 0x0000000706800000| PB 0x0000000706800000| Untracked |  0
|  15|0x0000000706a00000, 0x0000000706a00000, 0x0000000706c00000|  0%| F|  |TAMS 0x0000000706a00000| PB 0x0000000706a00000| Untracked |  0
|  16|0x0000000706c00000, 0x0000000706c00000, 0x0000000706e00000|  0%| F|  |TAMS 0x0000000706c00000| PB 0x0000000706c00000| Untracked |  0
|  17|0x0000000706e00000, 0x0000000706e00000, 0x0000000707000000|  0%| F|  |TAMS 0x0000000706e00000| PB 0x0000000706e00000| Untracked |  0
|  18|0x0000000707000000, 0x0000000707000000, 0x0000000707200000|  0%| F|  |TAMS 0x0000000707000000| PB 0x0000000707000000| Untracked |  0
|  19|0x0000000707200000, 0x0000000707200000, 0x0000000707400000|  0%| F|  |TAMS 0x0000000707200000| PB 0x0000000707200000| Untracked |  0
|  20|0x0000000707400000, 0x0000000707400000, 0x0000000707600000|  0%| F|  |TAMS 0x0000000707400000| PB 0x0000000707400000| Untracked |  0
|  21|0x0000000707600000, 0x00000007076e6ae0, 0x0000000707800000| 45%| E|  |TAMS 0x0000000707600000| PB 0x0000000707600000| Complete |  0
|  22|0x0000000707800000, 0x0000000707a00000, 0x0000000707a00000|100%| E|CS|TAMS 0x0000000707800000| PB 0x0000000707800000| Complete |  0
|  23|0x0000000707a00000, 0x0000000707c00000, 0x0000000707c00000|100%| E|CS|TAMS 0x0000000707a00000| PB 0x0000000707a00000| Complete |  0
|  24|0x0000000707c00000, 0x0000000707e00000, 0x0000000707e00000|100%| E|CS|TAMS 0x0000000707c00000| PB 0x0000000707c00000| Complete |  0
|  25|0x0000000707e00000, 0x0000000708000000, 0x0000000708000000|100%| E|CS|TAMS 0x0000000707e00000| PB 0x0000000707e00000| Complete |  0
|  26|0x0000000708000000, 0x0000000708200000, 0x0000000708200000|100%| E|CS|TAMS 0x0000000708000000| PB 0x0000000708000000| Complete |  0
|  27|0x0000000708200000, 0x0000000708400000, 0x0000000708400000|100%| E|CS|TAMS 0x0000000708200000| PB 0x0000000708200000| Complete |  0
|  28|0x0000000708400000, 0x0000000708600000, 0x0000000708600000|100%| E|CS|TAMS 0x0000000708400000| PB 0x0000000708400000| Complete |  0
|  29|0x0000000708600000, 0x0000000708800000, 0x0000000708800000|100%| E|CS|TAMS 0x0000000708600000| PB 0x0000000708600000| Complete |  0
|  30|0x0000000708800000, 0x0000000708a00000, 0x0000000708a00000|100%| E|CS|TAMS 0x0000000708800000| PB 0x0000000708800000| Complete |  0
| 116|0x0000000713400000, 0x00000007135142b8, 0x0000000713600000| 53%| S|CS|TAMS 0x0000000713400000| PB 0x0000000713400000| Complete |  0
| 117|0x0000000713600000, 0x0000000713800000, 0x0000000713800000|100%| S|CS|TAMS 0x0000000713600000| PB 0x0000000713600000| Complete |  0
| 125|0x0000000714600000, 0x0000000714800000, 0x0000000714800000|100%| E|CS|TAMS 0x0000000714600000| PB 0x0000000714600000| Complete |  0

Card table byte_map: [0x0000015cbb050000,0x0000015cbb830000] _byte_map_base: 0x0000015cb782a000

Marking Bits: (CMBitMap*) 0x0000015ca2c4c4c0
 Bits: [0x0000015cbb830000, 0x0000015cbf700000)

Polling page: 0x0000015ca0c00000

Metaspace:

Usage:
  Non-class:     18.53 MB used.
      Class:      3.10 MB used.
       Both:     21.63 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      18.75 MB ( 29%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       3.31 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      22.06 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  12.98 MB
       Class:  12.61 MB
        Both:  25.59 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 35.25 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 3.
num_arena_births: 402.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 353.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 3.
num_chunks_taken_from_freelist: 1154.
num_chunk_merges: 3.
num_chunk_splits: 781.
num_chunks_enlarged: 560.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120064Kb used=1809Kb max_used=1809Kb free=118255Kb
 bounds [0x0000015cb2de0000, 0x0000015cb3050000, 0x0000015cba320000]
CodeHeap 'profiled nmethods': size=120000Kb used=8104Kb max_used=8104Kb free=111896Kb
 bounds [0x0000015cab320000, 0x0000015cabb10000, 0x0000015cb2850000]
CodeHeap 'non-nmethods': size=5696Kb used=1832Kb max_used=1907Kb free=3863Kb
 bounds [0x0000015cb2850000, 0x0000015cb2ac0000, 0x0000015cb2de0000]
CodeCache: size=245760Kb, used=11745Kb, max_used=11820Kb, free=234014Kb
 total_blobs=5984, nmethods=5117, adapters=770, full_count=0
Compilation: enabled, stopped_count=0, restarted_count=0

Compilation events (20 events):
Event: 15.019 Thread 0x0000015cc238d520 4466       3       javafx.scene.Parent$2::onChanged (729 bytes)
Event: 15.019 Thread 0x0000015cc238ce60 4467       4       java.util.ArrayList::add (65 bytes)
Event: 15.026 Thread 0x0000015cc238d520 nmethod 4466 0x0000015cab9c2d88 code [0x0000015cab9c3540, 0x0000015cab9c9160]
Event: 15.026 Thread 0x0000015cc238d520 4469       3       javafx.css.StyleableBooleanProperty::set (13 bytes)
Event: 15.026 Thread 0x0000015cc238d520 nmethod 4469 0x0000015cab9c9288 code [0x0000015cab9c93c0, 0x0000015cab9c9578]
Event: 15.026 Thread 0x0000015cc238d520 4470       3       javafx.beans.WeakInvalidationListener::<init> (31 bytes)
Event: 15.027 Thread 0x0000015cc238d520 nmethod 4470 0x0000015cab9c9608 code [0x0000015cab9c9780, 0x0000015cab9c9d20]
Event: 15.027 Thread 0x0000015cc238d520 4471       3       javafx.beans.property.DoublePropertyBase::<init> (25 bytes)
Event: 15.027 Thread 0x0000015cc238d520 nmethod 4471 0x0000015cab9c9d88 code [0x0000015cab9c9ec0, 0x0000015cab9ca240]
Event: 15.031 Thread 0x0000015cc238d520 4473       3       java.util.Collections$UnmodifiableCollection::iterator (9 bytes)
Event: 15.032 Thread 0x0000015cc238d520 nmethod 4473 0x0000015cab9ca288 code [0x0000015cab9ca3c0, 0x0000015cab9ca788]
Event: 15.032 Thread 0x0000015cc238d520 4475       3       javafx.beans.property.ReadOnlyBooleanWrapper::getReadOnlyProperty (24 bytes)
Event: 15.032 Thread 0x0000015cc238d520 nmethod 4475 0x0000015cab9ca808 code [0x0000015cab9ca960, 0x0000015cab9cada8]
Event: 15.032 Thread 0x0000015cc238d520 4474       3       java.util.Collections$UnmodifiableCollection$1::<init> (26 bytes)
Event: 15.033 Thread 0x0000015cc238d520 nmethod 4474 0x0000015cab9cae08 code [0x0000015cab9caf40, 0x0000015cab9cb250]
Event: 15.034 Thread 0x0000015cc238ce60 nmethod 4467 0x0000015cb2f65188 code [0x0000015cb2f652e0, 0x0000015cb2f65d00]
Event: 15.034 Thread 0x0000015cc238ce60 4472       4       javafx.scene.Scene::getWindow (22 bytes)
Event: 15.035 Thread 0x0000015cc238ce60 nmethod 4472 0x0000015cb2f65d88 code [0x0000015cb2f65e80, 0x0000015cb2f65f98]
Event: 15.035 Thread 0x0000015cc238ce60 4468       4       javafx.beans.property.ReadOnlyObjectProperty::<init> (5 bytes)
Event: 15.036 Thread 0x0000015cc238ce60 nmethod 4468 0x0000015cb2f66088 code [0x0000015cb2f66180, 0x0000015cb2f661f8]

GC Heap History (8 events):
Event: 1.449 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total reserved 4116480K, committed 258048K, used 22528K [0x0000000704c00000, 0x0000000800000000)
  region size 2048K, 12 young (24576K), 0 survivors (0K)
 Metaspace       used 5756K, committed 5952K, reserved 1114112K
  class space    used 667K, committed 768K, reserved 1048576K
}
Event: 1.455 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total reserved 4116480K, committed 258048K, used 3397K [0x0000000704c00000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 5756K, committed 5952K, reserved 1114112K
  class space    used 667K, committed 768K, reserved 1048576K
}
Event: 2.093 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total reserved 4116480K, committed 258048K, used 25925K [0x0000000704c00000, 0x0000000800000000)
  region size 2048K, 13 young (26624K), 2 survivors (4096K)
 Metaspace       used 10765K, committed 11136K, reserved 1114112K
  class space    used 1484K, committed 1664K, reserved 1048576K
}
Event: 2.102 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total reserved 4116480K, committed 258048K, used 6647K [0x0000000704c00000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 10765K, committed 11136K, reserved 1114112K
  class space    used 1484K, committed 1664K, reserved 1048576K
}
Event: 14.759 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total reserved 4116480K, committed 258048K, used 63991K [0x0000000704c00000, 0x0000000800000000)
  region size 2048K, 30 young (61440K), 2 survivors (4096K)
 Metaspace       used 19485K, committed 19904K, reserved 1114112K
  class space    used 2777K, committed 2944K, reserved 1048576K
}
Event: 14.764 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total reserved 4116480K, committed 258048K, used 14699K [0x0000000704c00000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 19485K, committed 19904K, reserved 1114112K
  class space    used 2777K, committed 2944K, reserved 1048576K
}
Event: 15.009 GC heap before
{Heap before GC invocations=3 (full 0):
 garbage-first heap   total reserved 4116480K, committed 258048K, used 29035K [0x0000000704c00000, 0x0000000800000000)
  region size 2048K, 12 young (24576K), 4 survivors (8192K)
 Metaspace       used 21128K, committed 21504K, reserved 1114112K
  class space    used 3034K, committed 3200K, reserved 1048576K
}
Event: 15.015 GC heap after
{Heap after GC invocations=4 (full 0):
 garbage-first heap   total reserved 4116480K, committed 258048K, used 18028K [0x0000000704c00000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 21128K, committed 21504K, reserved 1114112K
  class space    used 3034K, committed 3200K, reserved 1048576K
}

Dll operation events (20 events):
Event: 0.692 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-locale-l1-1-0.dll
Event: 0.698 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-math-l1-1-0.dll
Event: 0.704 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-multibyte-l1-1-0.dll
Event: 0.711 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-private-l1-1-0.dll
Event: 0.718 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-process-l1-1-0.dll
Event: 0.724 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-runtime-l1-1-0.dll
Event: 0.729 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-stdio-l1-1-0.dll
Event: 0.737 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-string-l1-1-0.dll
Event: 0.745 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-time-l1-1-0.dll
Event: 0.752 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-utility-l1-1-0.dll
Event: 0.781 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\ucrtbase.dll
Event: 0.791 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\vcruntime140.dll
Event: 0.796 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\vcruntime140_1.dll
Event: 0.812 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\msvcp140.dll
Event: 0.886 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\prism_d3d.dll
Event: 1.037 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\glass.dll
Event: 1.323 Loaded shared library C:\Program Files\Java\jdk-23\bin\jimage.dll
Event: 1.596 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\javafx_font.dll
Event: 14.631 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\sqlite-3.36.0.3-7bea6ec2-fd1f-4233-8df4-8a2432005557-sqlitejdbc.dll
Event: 14.648 Loaded shared library C:\Program Files\Java\jdk-23\bin\verify.dll

Deoptimization events (20 events):
Event: 14.911 Thread 0x0000015d08559cc0 DEOPT PACKING pc=0x0000015cb2f56a9c sp=0x000000391d8fba50
Event: 14.911 Thread 0x0000015d08559cc0 DEOPT UNPACKING pc=0x0000015cb28a4402 sp=0x000000391d8fb9e0 mode 2
Event: 14.911 Thread 0x0000015d08559cc0 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000015cb2f56a9c relative=0x000000000000007c
Event: 14.911 Thread 0x0000015d08559cc0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000015cb2f56a9c method=com.sun.javafx.collections.VetoableListDecorator.size()I @ 4 c2
Event: 14.911 Thread 0x0000015d08559cc0 DEOPT PACKING pc=0x0000015cb2f56a9c sp=0x000000391d8fbab0
Event: 14.911 Thread 0x0000015d08559cc0 DEOPT UNPACKING pc=0x0000015cb28a4402 sp=0x000000391d8fba40 mode 2
Event: 14.946 Thread 0x0000015d08559cc0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000015cb2e8d3c8 relative=0x0000000000000b28
Event: 14.946 Thread 0x0000015d08559cc0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000015cb2e8d3c8 method=java.util.WeakHashMap.expungeStaleEntries()V @ 66 c2
Event: 14.946 Thread 0x0000015d08559cc0 DEOPT PACKING pc=0x0000015cb2e8d3c8 sp=0x000000391d8fb6d0
Event: 14.946 Thread 0x0000015d08559cc0 DEOPT UNPACKING pc=0x0000015cb28a4402 sp=0x000000391d8fb650 mode 2
Event: 14.959 Thread 0x0000015d08559cc0 DEOPT PACKING pc=0x0000015cab68cb5d sp=0x000000391d8fb1c0
Event: 14.959 Thread 0x0000015d08559cc0 DEOPT UNPACKING pc=0x0000015cb28a4b22 sp=0x000000391d8fa648 mode 0
Event: 15.004 Thread 0x0000015d08559cc0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000015cb2f0b970 relative=0x00000000000001d0
Event: 15.004 Thread 0x0000015d08559cc0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000015cb2f0b970 method=java.util.WeakHashMap.matchesKey(Ljava/util/WeakHashMap$Entry;Ljava/lang/Object;)Z @ 5 c2
Event: 15.004 Thread 0x0000015d08559cc0 DEOPT PACKING pc=0x0000015cb2f0b970 sp=0x000000391d8fad70
Event: 15.004 Thread 0x0000015d08559cc0 DEOPT UNPACKING pc=0x0000015cb28a4402 sp=0x000000391d8fac90 mode 2
Event: 15.027 Thread 0x0000015d08559cc0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000015cb2f05a98 relative=0x00000000000000d8
Event: 15.027 Thread 0x0000015d08559cc0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000015cb2f05a98 method=java.lang.StrictMath.floorOrCeil(DDDD)D @ 24 c2
Event: 15.027 Thread 0x0000015d08559cc0 DEOPT PACKING pc=0x0000015cb2f05a98 sp=0x000000391d8fb160
Event: 15.027 Thread 0x0000015d08559cc0 DEOPT UNPACKING pc=0x0000015cb28a4402 sp=0x000000391d8fb010 mode 2

Classes loaded (20 events):
Event: 14.901 Loading class java/util/concurrent/Delayed
Event: 14.901 Loading class java/util/concurrent/Delayed done
Event: 14.901 Loading class java/util/concurrent/ScheduledFuture done
Event: 14.901 Loading class java/util/concurrent/RunnableScheduledFuture done
Event: 14.904 Loading class jdk/internal/math/FormattedFPDecimal
Event: 14.904 Loading class jdk/internal/math/FormattedFPDecimal done
Event: 14.907 Loading class java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask
Event: 14.907 Loading class java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask done
Event: 14.913 Loading class java/util/TaskQueue
Event: 14.913 Loading class java/util/TaskQueue done
Event: 14.913 Loading class java/util/TimerThread
Event: 14.913 Loading class java/util/TimerThread done
Event: 14.914 Loading class java/util/Timer$ThreadReaper
Event: 14.914 Loading class java/util/Timer$ThreadReaper done
Event: 14.917 Loading class java/util/stream/ReduceOps$5
Event: 14.918 Loading class java/util/stream/ReduceOps$5 done
Event: 14.918 Loading class java/util/stream/ReduceOps$CountingSink$OfRef
Event: 14.918 Loading class java/util/stream/ReduceOps$CountingSink
Event: 14.918 Loading class java/util/stream/ReduceOps$CountingSink done
Event: 14.918 Loading class java/util/stream/ReduceOps$CountingSink$OfRef done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 7.985 Thread 0x0000015d08559cc0 Exception <a 'java/lang/ClassCastException'{0x00000007117088d0}: class java.lang.String cannot be cast to class javafx.css.Size (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.Size is in module javafx.graphics of loader 'app')> (0x00000007117088d0) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 7.986 Thread 0x0000015d08559cc0 Exception <a 'java/lang/ClassCastException'{0x00000007117275d8}: class java.lang.String cannot be cast to class javafx.css.Size (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.Size is in module javafx.graphics of loader 'app')> (0x00000007117275d8) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 12.016 Thread 0x0000015d08559cc0 Exception <a 'java/lang/ClassCastException'{0x00000007117dac20}: class java.lang.String cannot be cast to class javafx.scene.paint.Paint (java.lang.String is in module java.base of loader 'bootstrap'; javafx.scene.paint.Paint is in module javafx.graphics of loader 'app')> (0x00000007117dac20) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 12.018 Thread 0x0000015d08559cc0 Exception <a 'java/lang/ClassCastException'{0x0000000711459bd8}: class java.lang.String cannot be cast to class javafx.css.Size (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.Size is in module javafx.graphics of loader 'app')> (0x0000000711459bd8) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 12.020 Thread 0x0000015d08559cc0 Exception <a 'java/lang/ClassCastException'{0x0000000711477a30}: class java.lang.String cannot be cast to class javafx.css.Size (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.Size is in module javafx.graphics of loader 'app')> (0x0000000711477a30) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 12.700 Thread 0x0000015d08559cc0 Exception <a 'java/lang/ClassCastException'{0x0000000711281980}: class java.lang.String cannot be cast to class javafx.css.Size (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.Size is in module javafx.graphics of loader 'app')> (0x0000000711281980) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 12.701 Thread 0x0000015d08559cc0 Exception <a 'java/lang/ClassCastException'{0x000000071129fc68}: class java.lang.String cannot be cast to class javafx.css.Size (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.Size is in module javafx.graphics of loader 'app')> (0x000000071129fc68) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 13.187 Thread 0x0000015d08559cc0 Exception <a 'java/lang/ClassCastException'{0x0000000711362008}: class java.lang.String cannot be cast to class javafx.css.Size (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.Size is in module javafx.graphics of loader 'app')> (0x0000000711362008) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 13.189 Thread 0x0000015d08559cc0 Exception <a 'java/lang/ClassCastException'{0x00000007113801b8}: class java.lang.String cannot be cast to class javafx.css.Size (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.Size is in module javafx.graphics of loader 'app')> (0x00000007113801b8) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 13.782 Thread 0x0000015d08559cc0 Exception <a 'java/lang/ClassCastException'{0x00000007110d4710}: class java.lang.String cannot be cast to class javafx.css.Size (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.Size is in module javafx.graphics of loader 'app')> (0x00000007110d4710) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 13.783 Thread 0x0000015d08559cc0 Exception <a 'java/lang/ClassCastException'{0x000000071112fda0}: class java.lang.String cannot be cast to class javafx.css.Size (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.Size is in module javafx.graphics of loader 'app')> (0x000000071112fda0) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 14.529 Thread 0x0000015d08559cc0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000710fc5ff0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int, int, int)'> (0x0000000710fc5ff0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 14.532 Thread 0x0000015d08559cc0 Implicit null exception at 0x0000015cb2e1431b to 0x0000015cb2e145b0
Event: 14.544 Thread 0x0000015d08559cc0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000710c88c88}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, int, int, int, int)'> (0x0000000710c88c88) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 14.915 Thread 0x0000015d08559cc0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000713f05600}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, double, int)'> (0x0000000713f05600) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 14.926 Thread 0x0000015d08559cc0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000713f48448}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x0000000713f48448) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 14.961 Thread 0x0000015d08559cc0 Exception <a 'java/lang/ClassCastException'{0x0000000713a8db98}: class java.lang.String cannot be cast to class javafx.scene.paint.Paint (java.lang.String is in module java.base of loader 'bootstrap'; javafx.scene.paint.Paint is in module javafx.graphics of loader 'app')> (0x0000000713a8db98) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 15.005 Thread 0x0000015d08559cc0 Exception <a 'java/lang/ClassCastException'{0x000000071397c340}: class java.lang.String cannot be cast to class javafx.scene.paint.Paint (java.lang.String is in module java.base of loader 'bootstrap'; javafx.scene.paint.Paint is in module javafx.graphics of loader 'app')> (0x000000071397c340) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 15.006 Thread 0x0000015d08559cc0 Exception <a 'java/lang/ClassCastException'{0x0000000713989f10}: class java.lang.String cannot be cast to class javafx.css.Size (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.Size is in module javafx.graphics of loader 'app')> (0x0000000713989f10) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 15.007 Thread 0x0000015d08559cc0 Exception <a 'java/lang/ClassCastException'{0x000000071399f3e8}: class java.lang.String cannot be cast to class javafx.css.Size (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.Size is in module javafx.graphics of loader 'app')> (0x000000071399f3e8) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]

VM Operations (20 events):
Event: 2.813 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 2.813 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 2.950 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 2.950 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 14.480 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 14.480 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 14.504 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 14.504 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 14.531 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 14.531 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 14.759 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 14.764 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 14.918 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 14.918 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 15.009 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold)
Event: 15.015 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold) done
Event: 15.022 Executing VM operation: G1PauseRemark
Event: 15.025 Executing VM operation: G1PauseRemark done
Event: 15.028 Executing VM operation: G1PauseCleanup
Event: 15.028 Executing VM operation: G1PauseCleanup done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 3.968 Thread 0x0000015d0e511060 Thread exited: 0x0000015d0e511060
Event: 12.670 Thread 0x0000015cc238d520 Thread added: 0x0000015d0803bb20
Event: 12.997 Thread 0x0000015d0803bb20 Thread exited: 0x0000015d0803bb20
Event: 13.788 Thread 0x0000015cc238d520 Thread added: 0x0000015d0e59c4c0
Event: 13.979 Thread 0x0000015d0e59c4c0 Thread exited: 0x0000015d0e59c4c0
Event: 14.507 Thread 0x0000015cc238ce60 Thread added: 0x0000015d130da900
Event: 14.860 Thread 0x0000015d08559cc0 Thread added: 0x0000015d0eebe5d0
Event: 14.862 Thread 0x0000015d08559cc0 Thread added: 0x0000015cc24a4b60
Event: 14.908 Thread 0x0000015d08559cc0 Thread added: 0x0000015cc24a3120
Event: 14.908 Thread 0x0000015d08559cc0 Thread added: 0x0000015cc24a3e40
Event: 14.914 Thread 0x0000015d0eebe5d0 Thread exited: 0x0000015d0eebe5d0
Event: 14.914 Thread 0x0000015d08559cc0 Thread added: 0x0000015cc24a37b0
Event: 14.923 Thread 0x0000015d08559cc0 Thread added: 0x0000015cc24a51f0
Event: 14.930 Thread 0x0000015d08559cc0 Thread added: 0x0000015cc24a5880
Event: 14.930 Thread 0x0000015d08559cc0 Thread added: 0x0000015cc24a2400
Event: 14.931 Thread 0x0000015d08559cc0 Thread added: 0x0000015cc24a2a90
Event: 14.931 Thread 0x0000015d08559cc0 Thread added: 0x0000015d0eebe5d0
Event: 14.980 Thread 0x0000015cc24a5880 Thread exited: 0x0000015cc24a5880
Event: 14.984 Thread 0x0000015cc24a2a90 Thread exited: 0x0000015cc24a2a90
Event: 15.034 Thread 0x0000015cc24a2400 Thread exited: 0x0000015cc24a2400


Dynamic libraries:
0x00007ff744a90000 - 0x00007ff744aa0000 	C:\Program Files\Java\jdk-23\bin\java.exe
0x00007ffc67860000 - 0x00007ffc67ac8000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffc274f0000 - 0x00007ffc27510000 	C:\Program Files\Avast Software\Avast\aswhook.dll
0x00007ffc66820000 - 0x00007ffc668e9000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffc65150000 - 0x00007ffc6553d000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffc64f70000 - 0x00007ffc650bb000 	C:\Windows\System32\ucrtbase.dll
0x00007ffc4a3a0000 - 0x00007ffc4a3b7000 	C:\Program Files\Java\jdk-23\bin\jli.dll
0x00007ffc43be0000 - 0x00007ffc43bfb000 	C:\Program Files\Java\jdk-23\bin\VCRUNTIME140.dll
0x00007ffc66120000 - 0x00007ffc662ec000 	C:\Windows\System32\USER32.dll
0x00007ffc64d10000 - 0x00007ffc64d37000 	C:\Windows\System32\win32u.dll
0x00007ffc37c60000 - 0x00007ffc37efa000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3\COMCTL32.dll
0x00007ffc67010000 - 0x00007ffc6703b000 	C:\Windows\System32\GDI32.dll
0x00007ffc657a0000 - 0x00007ffc65849000 	C:\Windows\System32\msvcrt.dll
0x00007ffc64a50000 - 0x00007ffc64b87000 	C:\Windows\System32\gdi32full.dll
0x00007ffc64ec0000 - 0x00007ffc64f63000 	C:\Windows\System32\msvcp_win.dll
0x00007ffc66580000 - 0x00007ffc665af000 	C:\Windows\System32\IMM32.DLL
0x00007ffc52930000 - 0x00007ffc5293c000 	C:\Program Files\Java\jdk-23\bin\vcruntime140_1.dll
0x00007ffc21370000 - 0x00007ffc213fe000 	C:\Program Files\Java\jdk-23\bin\msvcp140.dll
0x00007ffb912a0000 - 0x00007ffb91ff0000 	C:\Program Files\Java\jdk-23\bin\server\jvm.dll
0x00007ffc66e30000 - 0x00007ffc66ee4000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffc662f0000 - 0x00007ffc66396000 	C:\Windows\System32\sechost.dll
0x00007ffc66ef0000 - 0x00007ffc67008000 	C:\Windows\System32\RPCRT4.dll
0x00007ffc65c10000 - 0x00007ffc65c84000 	C:\Windows\System32\WS2_32.dll
0x00007ffc64800000 - 0x00007ffc6485e000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffc52630000 - 0x00007ffc5263b000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffc59eb0000 - 0x00007ffc59ee5000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffc647e0000 - 0x00007ffc647f4000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffc63750000 - 0x00007ffc6376b000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffc526e0000 - 0x00007ffc526ea000 	C:\Program Files\Java\jdk-23\bin\jimage.dll
0x00007ffc62080000 - 0x00007ffc622c1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffc668f0000 - 0x00007ffc66c76000 	C:\Windows\System32\combase.dll
0x00007ffc666c0000 - 0x00007ffc667a0000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffc51180000 - 0x00007ffc511c3000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffc649b0000 - 0x00007ffc64a49000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffc428b0000 - 0x00007ffc428ce000 	C:\Program Files\Java\jdk-23\bin\java.dll
0x00007ffc65900000 - 0x00007ffc65a9e000 	C:\Windows\System32\ole32.dll
0x00007ffc67050000 - 0x00007ffc6779a000 	C:\Windows\System32\SHELL32.dll
0x00007ffc64d40000 - 0x00007ffc64eb4000 	C:\Windows\System32\wintypes.dll
0x00007ffc62440000 - 0x00007ffc62c9b000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffc665c0000 - 0x00007ffc666b5000 	C:\Windows\System32\SHCORE.dll
0x00007ffc65b50000 - 0x00007ffc65bba000 	C:\Windows\System32\shlwapi.dll
0x00007ffc648c0000 - 0x00007ffc648ef000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffc14730000 - 0x00007ffc14807000 	C:\Program Files\Java\jdk-23\bin\jsvml.dll
0x00007ffc43bd0000 - 0x00007ffc43be0000 	C:\Program Files\Java\jdk-23\bin\net.dll
0x00007ffc63ce0000 - 0x00007ffc63d4a000 	C:\Windows\system32\mswsock.dll
0x00007ffc40c30000 - 0x00007ffc40c46000 	C:\Program Files\Java\jdk-23\bin\nio.dll
0x00007ffc41790000 - 0x00007ffc417a7000 	C:\Program Files\Java\jdk-23\bin\zip.dll
0x0000015ca0cd0000 - 0x0000015ca0cd3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-console-l1-1-0.dll
0x0000015ca2ad0000 - 0x0000015ca2ad3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-console-l1-2-0.dll
0x0000015ca2ae0000 - 0x0000015ca2ae3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-datetime-l1-1-0.dll
0x0000015ca2af0000 - 0x0000015ca2af3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-debug-l1-1-0.dll
0x0000015ca2b00000 - 0x0000015ca2b03000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-errorhandling-l1-1-0.dll
0x0000015ca2b10000 - 0x0000015ca2b14000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-file-l1-1-0.dll
0x0000015ca2b20000 - 0x0000015ca2b23000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-file-l1-2-0.dll
0x0000015ca2b30000 - 0x0000015ca2b33000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-file-l2-1-0.dll
0x0000015ca2b40000 - 0x0000015ca2b43000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-handle-l1-1-0.dll
0x0000015ca2b50000 - 0x0000015ca2b53000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-heap-l1-1-0.dll
0x0000015ca2b60000 - 0x0000015ca2b63000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-interlocked-l1-1-0.dll
0x0000015ca2b70000 - 0x0000015ca2b73000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-libraryloader-l1-1-0.dll
0x0000015ca2b80000 - 0x0000015ca2b83000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-localization-l1-2-0.dll
0x0000015ca2b90000 - 0x0000015ca2b93000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-memory-l1-1-0.dll
0x0000015cc2900000 - 0x0000015cc2903000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-namedpipe-l1-1-0.dll
0x0000015cc2910000 - 0x0000015cc2913000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-processenvironment-l1-1-0.dll
0x0000015cc2920000 - 0x0000015cc2923000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-processthreads-l1-1-0.dll
0x0000015cc2930000 - 0x0000015cc2933000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-processthreads-l1-1-1.dll
0x0000015cc2940000 - 0x0000015cc2943000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-profile-l1-1-0.dll
0x0000015cc2950000 - 0x0000015cc2953000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-rtlsupport-l1-1-0.dll
0x0000015cc2960000 - 0x0000015cc2963000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-string-l1-1-0.dll
0x0000015cc2970000 - 0x0000015cc2973000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-synch-l1-1-0.dll
0x0000015cc2980000 - 0x0000015cc2983000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-synch-l1-2-0.dll
0x0000015cc2990000 - 0x0000015cc2993000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-sysinfo-l1-1-0.dll
0x0000015cc29a0000 - 0x0000015cc29a3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-timezone-l1-1-0.dll
0x0000015cc29b0000 - 0x0000015cc29b3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-util-l1-1-0.dll
0x0000015cc29c0000 - 0x0000015cc29c3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-conio-l1-1-0.dll
0x0000015cc29d0000 - 0x0000015cc29d4000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-convert-l1-1-0.dll
0x0000015cc29e0000 - 0x0000015cc29e3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-environment-l1-1-0.dll
0x0000015cc29f0000 - 0x0000015cc29f3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-filesystem-l1-1-0.dll
0x0000015cc2a00000 - 0x0000015cc2a03000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-heap-l1-1-0.dll
0x0000015cc2a10000 - 0x0000015cc2a13000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-locale-l1-1-0.dll
0x0000015cc2a20000 - 0x0000015cc2a25000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-math-l1-1-0.dll
0x0000015cc2a30000 - 0x0000015cc2a35000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-multibyte-l1-1-0.dll
0x0000015cc2a40000 - 0x0000015cc2a50000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-private-l1-1-0.dll
0x0000015cc2a50000 - 0x0000015cc2a53000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-process-l1-1-0.dll
0x0000015cc2a60000 - 0x0000015cc2a64000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-runtime-l1-1-0.dll
0x0000015cc2a70000 - 0x0000015cc2a74000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-stdio-l1-1-0.dll
0x0000015cc2a80000 - 0x0000015cc2a84000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-string-l1-1-0.dll
0x0000015cc2a90000 - 0x0000015cc2a93000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-time-l1-1-0.dll
0x0000015cc2aa0000 - 0x0000015cc2aa3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-utility-l1-1-0.dll
0x00007ffc23fd0000 - 0x00007ffc240cc000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\ucrtbase.dll
0x00007ffc4a5f0000 - 0x00007ffc4a60a000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\vcruntime140.dll
0x00007ffc529b0000 - 0x00007ffc529bc000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\vcruntime140_1.dll
0x00007ffc43c70000 - 0x00007ffc43cfd000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\msvcp140.dll
0x00007ffc44300000 - 0x00007ffc4432a000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\prism_d3d.dll
0x00007ffbad810000 - 0x00007ffbad9ca000 	C:\Windows\system32\d3d9.dll
0x00007ffc5f2c0000 - 0x00007ffc5f2f6000 	C:\Windows\SYSTEM32\dwmapi.dll
0x00007ffc5f300000 - 0x00007ffc5f34d000 	C:\Windows\SYSTEM32\dxcore.dll
0x00007ffc5eef0000 - 0x00007ffc5ef9f000 	C:\Windows\system32\uxtheme.dll
0x00007ffb9b870000 - 0x00007ffb9bb00000 	C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_f93b33e201f14a12\igdumdim64.dll
0x00007ffb9a310000 - 0x00007ffb9b861000 	C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_f93b33e201f14a12\igd9trinity64.dll
0x00007ffc5f140000 - 0x00007ffc5f278000 	C:\Windows\system32\dxgi.dll
0x00007ffc5f0d0000 - 0x00007ffc5f134000 	C:\Windows\SYSTEM32\directxdatabasehelper.dll
0x00007ffc64520000 - 0x00007ffc64577000 	C:\Windows\SYSTEM32\cfgmgr32.dll
0x00007ffc596a0000 - 0x00007ffc59ae1000 	C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_f93b33e201f14a12\igdgmm64.dll
0x00007ffc590c0000 - 0x00007ffc590e5000 	C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_f93b33e201f14a12\igc64.dll
0x00007ffb9a2c0000 - 0x00007ffb9a30f000 	C:\Windows\SYSTEM32\ControlLib.dll
0x00007ffc186e0000 - 0x00007ffc18784000 	C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_f93b33e201f14a12\IntelControlLib.dll
0x00007ffb98c80000 - 0x00007ffb9a1c5000 	C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_f93b33e201f14a12\igd9dxva64.dll
0x00007ffc65c90000 - 0x00007ffc66116000 	C:\Windows\System32\SETUPAPI.dll
0x00007ffc46990000 - 0x00007ffc486bd000 	C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_f93b33e201f14a12\media_bin_64.dll
0x00007ffc42d40000 - 0x00007ffc42d82000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\glass.dll
0x00007ffc65620000 - 0x00007ffc65718000 	C:\Windows\System32\COMDLG32.dll
0x00007ffc66cb0000 - 0x00007ffc66e10000 	C:\Windows\System32\MSCTF.dll
0x00007ffc54330000 - 0x00007ffc58fcd000 	C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_f93b33e201f14a12\igc-default64.dll
0x00007ffbdf080000 - 0x00007ffbdf1c7000 	C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_f93b33e201f14a12\igdml64.dll
0x00007ffc468e0000 - 0x00007ffc46908000 	C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_f93b33e201f14a12\igdinfo64.dll
0x00007ffc63fa0000 - 0x00007ffc63fbb000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffc636b0000 - 0x00007ffc636eb000 	C:\Windows\system32\rsaenh.dll
0x00007ffc63f90000 - 0x00007ffc63f9c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffc64890000 - 0x00007ffc648b6000 	C:\Windows\SYSTEM32\bcrypt.dll
0x00007ffc49d70000 - 0x00007ffc49d84000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\javafx_font.dll
0x00007ffc5d650000 - 0x00007ffc5d8bb000 	C:\Windows\SYSTEM32\dwrite.dll
0x00007ffc65850000 - 0x00007ffc658f8000 	C:\Windows\System32\clbcatq.dll
0x00007ffc5d410000 - 0x00007ffc5d64b000 	C:\Windows\SYSTEM32\WindowsCodecs.dll
0x00007ffc197d0000 - 0x00007ffc1982a000 	C:\Windows\system32\dataexchange.dll
0x00007ffc514f0000 - 0x00007ffc51728000 	C:\Windows\system32\twinapi.appcore.dll
0x00007ffc40d40000 - 0x00007ffc40d6c000 	C:\Program Files\Common Files\Microsoft Shared\Ink\rtscom.dll
0x00007ffc3f440000 - 0x00007ffc3f590000 	C:\Windows\SYSTEM32\textinputframework.dll
0x00007ffc5e810000 - 0x00007ffc5e935000 	C:\Windows\SYSTEM32\CoreMessaging.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-23\bin;C:\Windows\SYSTEM32;C:\Program Files\Avast Software\Avast;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3;C:\Program Files\Java\jdk-23\bin\server;C:\Users\<USER>\.openjfx\cache\17.0.2-ea;C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_f93b33e201f14a12;C:\Program Files\Common Files\Microsoft Shared\Ink;C:\Users\<USER>\AppData\Local\Temp

VM Arguments:
jvm_args: --module-path=C:\Users\<USER>\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics 
java_command: com.restaurant.RestaurantApp
java_class_path (initial): C:\Users\<USER>\Downloads\restaurant-desktop (6)\restaurant-desktop\target\classes;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\pdfbox\fontbox\2.0.27\fontbox-2.0.27.jar;C:\Users\<USER>\.m2\repository\org\apache\pdfbox\pdfbox\2.0.27\pdfbox-2.0.27.jar;C:\Users\<USER>\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar;C:\Users\<USER>\.m2\repository\org\xerial\sqlite-jdbc\3.36.0.3\sqlite-jdbc-3.36.0.3.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
   size_t InitialHeapSize                          = 264241152                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MarkStackSizeMax                         = 536870912                                 {product} {ergonomic}
   size_t MaxHeapSize                              = 4215275520                                {product} {ergonomic}
   size_t MaxNewSize                               = 2529165312                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832704                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122945536                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122880000                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4215275520                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-23
PATH=C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Java\jdk-23\bin;C:\Program Files\MongoDB\Server\8.0\bin;C:\Users\<USER>\Downloads\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;C:\cygwin64\bin;C:\Program Files\Java\jdk-23\bin;C:\Program Files\Apache Software Foundation\Tomcat 10.01\bin;C:\Program Files\Redis\;C:\Program Files (x86)\cloudflared\;C:\Program Files\php-8.4.7-Win32-vs17-x64;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\apache-maven-3.9.10\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\mongosh\;C:\Program Files\nodejs;C:\Program Files\apache-ant-1.10.15\bin;set TA_LIBRARY_PATH=C:\ta-lib\lib;set TA_INCLUDE_PATH=C:\ta-lib\include;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\LiveKit.LiveKitCLI_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\Users\<USER>\AppData\Roaming\npm;C:\flutter\bin;C:\apache-maven-3.9.10\bin;;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
USERNAME=Anshdeep
LANG=en_US.UTF-8
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 154 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
OS uptime: 1 days 0:05 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 154 stepping 3 microcode 0x435, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv, serialize, rdtscp, rdpid, fsrm, gfni, f16c, cet_ibt, cet_ss
Processor Information for processor 0
  Max Mhz: 2000, Current Mhz: 2000, Mhz Limit: 2000
Processor Information for processor 1
  Max Mhz: 2000, Current Mhz: 2000, Mhz Limit: 2000
Processor Information for processor 2
  Max Mhz: 2000, Current Mhz: 2000, Mhz Limit: 2000
Processor Information for processor 3
  Max Mhz: 2000, Current Mhz: 2000, Mhz Limit: 2000
Processor Information for processor 4
  Max Mhz: 2000, Current Mhz: 2000, Mhz Limit: 2000
Processor Information for processor 5
  Max Mhz: 2000, Current Mhz: 2000, Mhz Limit: 2000
Processor Information for processor 6
  Max Mhz: 2000, Current Mhz: 2000, Mhz Limit: 2000
Processor Information for processor 7
  Max Mhz: 2000, Current Mhz: 2000, Mhz Limit: 2000
Processor Information for processor 8
  Max Mhz: 2000, Current Mhz: 1500, Mhz Limit: 1500
Processor Information for processor 9
  Max Mhz: 2000, Current Mhz: 1500, Mhz Limit: 1500
Processor Information for processor 10
  Max Mhz: 2000, Current Mhz: 1500, Mhz Limit: 1500
Processor Information for processor 11
  Max Mhz: 2000, Current Mhz: 1500, Mhz Limit: 1500

Memory: 4k page, system-wide physical 16076M (2263M free)
TotalPageFile size 27340M (AvailPageFile size 3906M)
current process WorkingSet (physical memory assigned to process): 378M, peak: 378M
current process commit charge ("private bytes"): 447M, peak: 583M

vm_info: Java HotSpot(TM) 64-Bit Server VM (23.0.2+7-58) for windows-amd64 JRE (23.0.2+7-58), built on 2024-11-29T09:34:55Z with MS VC++ 17.6 (VS2022)

END.
