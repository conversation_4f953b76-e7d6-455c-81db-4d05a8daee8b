package com.restaurant.model;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Model class representing a processed voice command
 */
public class VoiceCommand {
    private String originalText;
    private boolean recognized;
    private String commandType;
    private String description;
    private Map<String, String> entities;
    private LocalDateTime timestamp;
    private double confidenceScore;
    private String executionStatus;

    // Constructors
    public VoiceCommand() {
        this.timestamp = LocalDateTime.now();
        this.executionStatus = "PENDING";
    }

    public VoiceCommand(String originalText, boolean recognized, String commandType, 
                       String description, Map<String, String> entities) {
        this();
        this.originalText = originalText;
        this.recognized = recognized;
        this.commandType = commandType;
        this.description = description;
        this.entities = entities;
        this.confidenceScore = recognized ? 0.85 : 0.0; // Default confidence
    }

    public VoiceCommand(String originalText, boolean recognized, String commandType, 
                       String description, Map<String, String> entities, double confidenceScore) {
        this(originalText, recognized, commandType, description, entities);
        this.confidenceScore = confidenceScore;
    }

    // Get<PERSON> and Setters
    public String getOriginalText() {
        return originalText;
    }

    public void setOriginalText(String originalText) {
        this.originalText = originalText;
    }

    public boolean isRecognized() {
        return recognized;
    }

    public void setRecognized(boolean recognized) {
        this.recognized = recognized;
    }

    public String getCommandType() {
        return commandType;
    }

    public void setCommandType(String commandType) {
        this.commandType = commandType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Map<String, String> getEntities() {
        return entities;
    }

    public void setEntities(Map<String, String> entities) {
        this.entities = entities;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public double getConfidenceScore() {
        return confidenceScore;
    }

    public void setConfidenceScore(double confidenceScore) {
        this.confidenceScore = confidenceScore;
    }

    public String getExecutionStatus() {
        return executionStatus;
    }

    public void setExecutionStatus(String executionStatus) {
        this.executionStatus = executionStatus;
    }

    // Utility methods
    public String getEntity(String key) {
        return entities != null ? entities.get(key) : null;
    }

    public boolean hasEntity(String key) {
        return entities != null && entities.containsKey(key);
    }

    public String getConfidenceLevel() {
        if (confidenceScore >= 0.9) return "Very High";
        if (confidenceScore >= 0.75) return "High";
        if (confidenceScore >= 0.6) return "Medium";
        if (confidenceScore >= 0.4) return "Low";
        return "Very Low";
    }

    public boolean isExecutable() {
        return recognized && confidenceScore >= 0.5;
    }

    public String getFormattedTimestamp() {
        return timestamp.toString(); // Can be formatted as needed
    }

    @Override
    public String toString() {
        return "VoiceCommand{" +
                "originalText='" + originalText + '\'' +
                ", recognized=" + recognized +
                ", commandType='" + commandType + '\'' +
                ", description='" + description + '\'' +
                ", confidenceScore=" + confidenceScore +
                ", executionStatus='" + executionStatus + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        VoiceCommand that = (VoiceCommand) o;

        if (!originalText.equals(that.originalText)) return false;
        if (!commandType.equals(that.commandType)) return false;
        return timestamp.equals(that.timestamp);
    }

    @Override
    public int hashCode() {
        int result = originalText.hashCode();
        result = 31 * result + commandType.hashCode();
        result = 31 * result + timestamp.hashCode();
        return result;
    }
}
