package com.restaurant.model;

import java.time.LocalDateTime;

public class User {
    private int id;
    private String username;
    private String role;
    private String passwordHash;
    private String fullName;
    private String email;
    private String phone;
    private String status;
    private LocalDateTime createdAt;
    private LocalDateTime lastLogin;

    // Default constructor
    public User() {
        this.createdAt = LocalDateTime.now();
        this.status = "Active";
    }

    // Constructor for authentication (existing)
    public User(int id, String username, String role) {
        this.id = id;
        this.username = username;
        this.role = role;
        this.createdAt = LocalDateTime.now();
        this.status = "Active";
    }

    // Full constructor
    public User(int id, String username, String fullName, String role, String email, String phone, String status) {
        this.id = id;
        this.username = username;
        this.fullName = fullName;
        this.role = role;
        this.email = email;
        this.phone = phone;
        this.status = status;
        this.createdAt = LocalDateTime.now();
    }

    // Getters and setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }

    public String getRole() { return role; }
    public void setRole(String role) { this.role = role; }

    public String getPasswordHash() { return passwordHash; }
    public void setPasswordHash(String passwordHash) { this.passwordHash = passwordHash; }

    public String getFullName() { return fullName; }
    public void setFullName(String fullName) { this.fullName = fullName; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getLastLogin() { return lastLogin; }
    public void setLastLogin(LocalDateTime lastLogin) { this.lastLogin = lastLogin; }

    public boolean isAdmin() { return "ADMIN".equals(role); }
}