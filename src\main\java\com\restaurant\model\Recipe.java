package com.restaurant.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Model class for Recipe management
 */
public class Recipe {
    private int id;
    private String name;
    private String description;
    private String category;
    private int servings;
    private int prepTimeMinutes;
    private int cookTimeMinutes;
    private String instructions;
    private boolean isActive;
    private boolean autoConvert;
    private LocalDateTime createdAt;
    private LocalDateTime lastUpdated;
    private String createdBy;
    private List<RecipeIngredient> ingredients;

    // Constructors
    public Recipe() {
        this.createdAt = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
        this.ingredients = new ArrayList<>();
        this.isActive = true;
        this.autoConvert = false;
    }

    public Recipe(String name, String description) {
        this();
        this.name = name;
        this.description = description;
    }

    public Recipe(int id, String name, String description) {
        this(name, description);
        this.id = id;
    }

    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }

    public int getServings() { return servings; }
    public void setServings(int servings) { this.servings = servings; }

    public int getPrepTimeMinutes() { return prepTimeMinutes; }
    public void setPrepTimeMinutes(int prepTimeMinutes) { this.prepTimeMinutes = prepTimeMinutes; }

    public int getCookTimeMinutes() { return cookTimeMinutes; }
    public void setCookTimeMinutes(int cookTimeMinutes) { this.cookTimeMinutes = cookTimeMinutes; }

    public String getInstructions() { return instructions; }
    public void setInstructions(String instructions) { this.instructions = instructions; }

    public boolean isActive() { return isActive; }
    public void setActive(boolean active) { isActive = active; }

    public boolean isAutoConvert() { return autoConvert; }
    public void setAutoConvert(boolean autoConvert) { this.autoConvert = autoConvert; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getLastUpdated() { return lastUpdated; }
    public void setLastUpdated(LocalDateTime lastUpdated) { this.lastUpdated = lastUpdated; }

    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }

    public List<RecipeIngredient> getIngredients() { return ingredients; }
    public void setIngredients(List<RecipeIngredient> ingredients) { this.ingredients = ingredients; }

    // Utility methods
    public void addIngredient(RecipeIngredient ingredient) {
        this.ingredients.add(ingredient);
    }

    public void removeIngredient(RecipeIngredient ingredient) {
        this.ingredients.remove(ingredient);
    }

    public int getTotalTimeMinutes() {
        return prepTimeMinutes + cookTimeMinutes;
    }

    public String getFormattedTime() {
        int total = getTotalTimeMinutes();
        if (total < 60) {
            return total + " mins";
        } else {
            int hours = total / 60;
            int mins = total % 60;
            return hours + "h " + mins + "m";
        }
    }

    @Override
    public String toString() {
        return "Recipe{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", category='" + category + '\'' +
                ", servings=" + servings +
                ", totalTime=" + getTotalTimeMinutes() + " mins" +
                '}';
    }
}
