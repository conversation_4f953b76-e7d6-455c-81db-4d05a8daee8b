package com.restaurant.model;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * Represents the current status of a table including active orders
 */
public class TableStatus {
    private int tableNumber;
    private String status; // BLANK, RUNNING, PRINTED, PAID, RUNNING_KOT
    private double totalAmount;
    private LocalDateTime orderTime;
    private int orderId;
    private boolean hasActiveOrder;
    private int seats = 4; // Default number of seats
    
    public TableStatus(int tableNumber) {
        this.tableNumber = tableNumber;
        this.status = "BLANK";
        this.totalAmount = 0.0;
        this.hasActiveOrder = false;
    }
    
    public TableStatus(int tableNumber, String status, double totalAmount, LocalDateTime orderTime, int orderId) {
        this.tableNumber = tableNumber;
        this.status = status;
        this.totalAmount = totalAmount;
        this.orderTime = orderTime;
        this.orderId = orderId;
        this.hasActiveOrder = true;
    }
    
    // Getters and setters
    public int getTableNumber() {
        return tableNumber;
    }
    
    public void setTableNumber(int tableNumber) {
        this.tableNumber = tableNumber;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public double getTotalAmount() {
        return totalAmount;
    }
    
    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }
    
    public LocalDateTime getOrderTime() {
        return orderTime;
    }
    
    public void setOrderTime(LocalDateTime orderTime) {
        this.orderTime = orderTime;
    }
    
    public int getOrderId() {
        return orderId;
    }
    
    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }
    
    public boolean hasActiveOrder() {
        return hasActiveOrder;
    }
    
    public void setHasActiveOrder(boolean hasActiveOrder) {
        this.hasActiveOrder = hasActiveOrder;
    }
    
    /**
     * Get the time elapsed since order was placed
     */
    public String getElapsedTime() {
        if (orderTime == null) {
            return "";
        }
        
        long minutes = ChronoUnit.MINUTES.between(orderTime, LocalDateTime.now());
        if (minutes < 60) {
            return minutes + " Min";
        } else {
            long hours = minutes / 60;
            long remainingMinutes = minutes % 60;
            return hours + "h " + remainingMinutes + "m";
        }
    }
    
    /**
     * Get formatted amount string
     */
    public String getFormattedAmount() {
        if (totalAmount <= 0) {
            return "";
        }
        return "₹" + String.format("%.2f", totalAmount);
    }
    
    /**
     * Get CSS style class based on status
     */
    public String getStyleClass() {
        switch (status) {
            case "PENDING":
            case "CONFIRMED":
            case "WORKING":
                return "running-table";
            case "READY":
                return "printed-table";
            case "COMPLETED":
            case "PAID":
                return "paid-table";
            case "KOT_PRINTED":
                return "running-kot-table";
            default:
                return "blank-table";
        }
    }
    
    /**
     * Check if table should show timing information
     */
    public boolean shouldShowTiming() {
        return hasActiveOrder && orderTime != null &&
               ("PENDING".equals(status) || "CONFIRMED".equals(status) ||
                "WORKING".equals(status) || "READY".equals(status));
    }

    /**
     * Get number of seats for this table
     */
    public int getSeats() {
        return seats;
    }

    /**
     * Set number of seats for this table
     */
    public void setSeats(int seats) {
        this.seats = seats;
    }

    /**
     * Get display-friendly status text
     */
    public String getDisplayStatus() {
        switch (status) {
            case "BLANK":
                return "Available";
            case "PENDING":
                return "Order Pending";
            case "WORKING":
                return "Preparing";
            case "READY":
                return "Ready to Serve";
            case "RUNNING":
                return "Occupied";
            case "PRINTED":
                return "Bill Printed";
            case "PAID":
                return "Paid";
            case "RUNNING_KOT":
                return "KOT Printed";
            default:
                return status;
        }
    }

    /**
     * Get Integer version of orderId (can be null)
     */
    public Integer getOrderIdAsInteger() {
        return hasActiveOrder ? orderId : null;
    }

    /**
     * Get formatted timing string for display
     */
    public String getFormattedTiming() {
        if (orderTime == null) {
            return "";
        }

        long minutes = ChronoUnit.MINUTES.between(orderTime, LocalDateTime.now());

        if (minutes < 60) {
            return minutes + " Min";
        } else {
            long hours = minutes / 60;
            long remainingMinutes = minutes % 60;
            return hours + "h " + remainingMinutes + "m";
        }
    }
}
