package com.restaurant.util; 
 
import java.sql.*; 
 
public class QuickAdminCreator { 
    public static void main(String[] args) { 
        System.out.println("🔧 Creating fresh database with admin user..."); 
 
        try { 
            Class.forName("org.sqlite.JDBC"); 
            System.out.println("✅ SQLite driver loaded"); 
        } catch (Exception e) { 
            System.err.println("❌ Driver error: " + e.getMessage()); 
            return; 
        } 
 
        String url = "*************************"; 
 
        try (Connection conn = DriverManager.getConnection(url)) { 
            System.out.println("✅ Database connected"); 
 
            // Create users table 
            String createTable = "CREATE TABLE users (" + 
                "id INTEGER PRIMARY KEY AUTOINCREMENT, " + 
                "username TEXT UNIQUE NOT NULL, " + 
                "password TEXT NOT NULL, " + 
                "role TEXT NOT NULL)"; 
 
            conn.createStatement().execute(createTable); 
            System.out.println("✅ Users table created"); 
 
            // Insert admin with BCrypt hash for 'admin123' 
            String insert = "INSERT INTO users (username, password, role) VALUES (?, ?, ?)"; 
            PreparedStatement ps = conn.prepareStatement(insert); 
            ps.setString(1, "admin"); 
            ps.setString(2, "$2a$10$N9qo8uLOickgx2ZMRZoMye.Uo0aBOBjXoXebXeQABkc4ILoLGxjvO"); 
            ps.setString(3, "ADMIN"); 
            ps.executeUpdate(); 
            ps.close(); 
            System.out.println("✅ Admin user created"); 
 
            // Verify 
            ResultSet rs = conn.createStatement().executeQuery("SELECT * FROM users"); 
            while (rs.next()) { 
                System.out.println("User: " + rs.getString("username") + " | Role: " + rs.getString("role")); 
            } 
            rs.close(); 
 
            System.out.println("🎉 SUCCESS! Admin user ready for login."); 
 
        } catch (Exception e) { 
            System.err.println("❌ Error: " + e.getMessage()); 
            e.printStackTrace(); 
        } 
    } 
} 
