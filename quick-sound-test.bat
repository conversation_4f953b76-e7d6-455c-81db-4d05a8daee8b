@echo off
echo 🔊 QUICK NOTIFICATION SOUND TEST 🔊
echo.

echo This will test all the notification sounds implemented in your restaurant system.
echo Make sure your system volume is turned on!
echo.

echo TESTING SOUNDS:
echo.

echo 1. 🔔 NEW ORDER SOUND (Double Beep)...
echo    Pattern: BEEP-pause-BEEP
echo    Use: When new Swiggy/<PERSON><PERSON>to order arrives
java -cp "target/classes" com.restaurant.util.SoundTester newOrder
echo.

timeout /t 2 /nobreak >nul

echo 2. 🍽️ ORDER READY SOUND (Triple Beep)...
echo    Pattern: BEEP-BEEP-BEEP (rapid)
echo    Use: When food is ready for delivery
java -cp "target/classes" com.restaurant.util.SoundTester orderReady
echo.

timeout /t 2 /nobreak >nul

echo 3. ✅ SUCCESS SOUND (Single Beep)...
echo    Pattern: BEEP
echo    Use: For successful operations
java -cp "target/classes" com.restaurant.util.SoundTester success
echo.

timeout /t 2 /nobreak >nul

echo 4. 🚨 URGENT ALERT SOUND (Rapid 5 Beeps)...
echo    Pattern: BEEP-BEEP-BEEP-BEEP-BEEP (very fast)
echo    Use: For urgent notifications and errors
java -cp "target/classes" com.restaurant.util.SoundTester urgent
echo.

timeout /t 2 /nobreak >nul

echo 5. ❌ ERROR SOUND (Long Pause Pattern)...
echo    Pattern: BEEP-long pause-BEEP
echo    Use: For system errors
java -cp "target/classes" com.restaurant.util.SoundTester error
echo.

timeout /t 2 /nobreak >nul

echo 6. 🔔 PERSISTENT RINGING (Urgent Pattern)...
echo    Pattern: BEEP-BEEP-BEEP (pause) BEEP-BEEP-BEEP
echo    Use: Continuous ringing for unaccepted orders
echo    This will demonstrate 3 ring cycles...
java -cp "target/classes" com.restaurant.util.SoundTester persistentRing
echo.

echo ✅ ALL NOTIFICATION SOUNDS TESTED!
echo.

echo SOUND SUMMARY:
echo 🔔 New Order: Double beep (when orders arrive)
echo 🍽️ Order Ready: Triple beep (when food is ready)
echo ✅ Success: Single beep (for successful actions)
echo 🚨 Urgent: Rapid 5 beeps (for urgent alerts)
echo ❌ Error: Long pause pattern (for errors)
echo 🔔 Persistent Ring: Urgent pattern (for unaccepted orders)
echo.

echo To test with the full application:
echo 1. Run: .\test-persistent-ringing.bat
echo 2. Login with admin/admin123
echo 3. Go to "🍽️ Finish List"
echo 4. You'll hear persistent ringing for NEW orders immediately
echo 5. Click status buttons to hear different sounds
echo.

pause
