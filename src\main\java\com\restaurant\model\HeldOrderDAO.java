package com.restaurant.model;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Data Access Object for HeldOrder operations
 * Handles database operations for held orders (KOT on hold)
 */
public class HeldOrderDAO {
    
    /**
     * Create held_orders table if it doesn't exist
     */
    public HeldOrderDAO() {
        createTableIfNotExists();
    }
    
    /**
     * Create the held_orders table
     */
    private void createTableIfNotExists() {
        String sql = "CREATE TABLE IF NOT EXISTS held_orders (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "order_id TEXT UNIQUE NOT NULL," +
                    "table_number INTEGER NOT NULL," +
                    "held_time TIMESTAMP NOT NULL," +
                    "reason TEXT," +
                    "total_amount REAL NOT NULL," +
                    "order_data TEXT," +
                    "held_by TEXT," +
                    "created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                    "status TEXT DEFAULT 'HELD'" +
                    ")";
        
        try (Connection conn = DatabaseManager.getConnection();
             Statement stmt = conn.createStatement()) {
            
            stmt.execute(sql);
            System.out.println("Held orders table created/verified successfully");
            
        } catch (SQLException e) {
            System.err.println("Error creating held_orders table: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Save a held order to database
     */
    public boolean saveHeldOrder(HeldOrder heldOrder) {
        String sql = "INSERT INTO held_orders (order_id, table_number, held_time, reason, " +
                    "total_amount, order_data, held_by, status) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, heldOrder.getOrderId());
            pstmt.setInt(2, heldOrder.getTableNumber());
            pstmt.setTimestamp(3, Timestamp.valueOf(heldOrder.getHeldTime()));
            pstmt.setString(4, heldOrder.getReason());
            pstmt.setDouble(5, heldOrder.getTotalAmount());
            pstmt.setString(6, heldOrder.getOrderData());
            pstmt.setString(7, heldOrder.getHeldBy());
            pstmt.setString(8, heldOrder.getStatus());
            
            int rowsAffected = pstmt.executeUpdate();
            System.out.println("Held order saved: " + heldOrder.getOrderId());
            return rowsAffected > 0;
            
        } catch (SQLException e) {
            System.err.println("Error saving held order: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Get all held orders
     */
    public List<HeldOrder> getAllHeldOrders() {
        List<HeldOrder> heldOrders = new ArrayList<>();
        String sql = "SELECT order_id, table_number, held_time, reason, total_amount, " +
                    "order_data, held_by, created_time, status " +
                    "FROM held_orders " +
                    "WHERE status = 'HELD' " +
                    "ORDER BY held_time DESC";
        
        try (Connection conn = DatabaseManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                HeldOrder heldOrder = new HeldOrder();
                heldOrder.setOrderId(rs.getString("order_id"));
                heldOrder.setTableNumber(rs.getInt("table_number"));
                heldOrder.setHeldTime(rs.getTimestamp("held_time").toLocalDateTime());
                heldOrder.setReason(rs.getString("reason"));
                heldOrder.setTotalAmount(rs.getDouble("total_amount"));
                heldOrder.setOrderData(rs.getString("order_data"));
                heldOrder.setHeldBy(rs.getString("held_by"));
                heldOrder.setStatus(rs.getString("status"));
                
                Timestamp createdTime = rs.getTimestamp("created_time");
                if (createdTime != null) {
                    heldOrder.setCreatedTime(createdTime.toLocalDateTime());
                }
                
                heldOrders.add(heldOrder);
            }
            
            System.out.println("Retrieved " + heldOrders.size() + " held orders");
            
        } catch (SQLException e) {
            System.err.println("Error retrieving held orders: " + e.getMessage());
            e.printStackTrace();
        }
        
        return heldOrders;
    }
    
    /**
     * Get held order by ID
     */
    public HeldOrder getHeldOrderById(String orderId) {
        String sql = "SELECT order_id, table_number, held_time, reason, total_amount, " +
                    "order_data, held_by, created_time, status " +
                    "FROM held_orders " +
                    "WHERE order_id = ?";
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, orderId);
            ResultSet rs = pstmt.executeQuery();
            
            if (rs.next()) {
                HeldOrder heldOrder = new HeldOrder();
                heldOrder.setOrderId(rs.getString("order_id"));
                heldOrder.setTableNumber(rs.getInt("table_number"));
                heldOrder.setHeldTime(rs.getTimestamp("held_time").toLocalDateTime());
                heldOrder.setReason(rs.getString("reason"));
                heldOrder.setTotalAmount(rs.getDouble("total_amount"));
                heldOrder.setOrderData(rs.getString("order_data"));
                heldOrder.setHeldBy(rs.getString("held_by"));
                heldOrder.setStatus(rs.getString("status"));
                
                Timestamp createdTime = rs.getTimestamp("created_time");
                if (createdTime != null) {
                    heldOrder.setCreatedTime(createdTime.toLocalDateTime());
                }
                
                return heldOrder;
            }
            
        } catch (SQLException e) {
            System.err.println("Error retrieving held order: " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * Get held orders by table number
     */
    public List<HeldOrder> getHeldOrdersByTable(int tableNumber) {
        List<HeldOrder> heldOrders = new ArrayList<>();
        String sql = "SELECT order_id, table_number, held_time, reason, total_amount, " +
                    "order_data, held_by, created_time, status " +
                    "FROM held_orders " +
                    "WHERE table_number = ? AND status = 'HELD' " +
                    "ORDER BY held_time DESC";
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, tableNumber);
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                HeldOrder heldOrder = new HeldOrder();
                heldOrder.setOrderId(rs.getString("order_id"));
                heldOrder.setTableNumber(rs.getInt("table_number"));
                heldOrder.setHeldTime(rs.getTimestamp("held_time").toLocalDateTime());
                heldOrder.setReason(rs.getString("reason"));
                heldOrder.setTotalAmount(rs.getDouble("total_amount"));
                heldOrder.setOrderData(rs.getString("order_data"));
                heldOrder.setHeldBy(rs.getString("held_by"));
                heldOrder.setStatus(rs.getString("status"));
                
                Timestamp createdTime = rs.getTimestamp("created_time");
                if (createdTime != null) {
                    heldOrder.setCreatedTime(createdTime.toLocalDateTime());
                }
                
                heldOrders.add(heldOrder);
            }
            
        } catch (SQLException e) {
            System.err.println("Error retrieving held orders by table: " + e.getMessage());
            e.printStackTrace();
        }
        
        return heldOrders;
    }
    
    /**
     * Update held order status (release or cancel)
     */
    public boolean updateHeldOrderStatus(String orderId, String status) {
        String sql = "UPDATE held_orders SET status = ? WHERE order_id = ?";
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, status);
            pstmt.setString(2, orderId);
            
            int rowsAffected = pstmt.executeUpdate();
            System.out.println("Held order status updated: " + orderId + " -> " + status);
            return rowsAffected > 0;
            
        } catch (SQLException e) {
            System.err.println("Error updating held order status: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Delete held order (permanent removal)
     */
    public boolean deleteHeldOrder(String orderId) {
        String sql = "DELETE FROM held_orders WHERE order_id = ?";
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, orderId);
            
            int rowsAffected = pstmt.executeUpdate();
            System.out.println("Held order deleted: " + orderId);
            return rowsAffected > 0;
            
        } catch (SQLException e) {
            System.err.println("Error deleting held order: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Get count of held orders
     */
    public int getHeldOrdersCount() {
        String sql = "SELECT COUNT(*) FROM held_orders WHERE status = 'HELD'";
        
        try (Connection conn = DatabaseManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            if (rs.next()) {
                return rs.getInt(1);
            }
            
        } catch (SQLException e) {
            System.err.println("Error getting held orders count: " + e.getMessage());
            e.printStackTrace();
        }
        
        return 0;
    }
    
    /**
     * Get overdue held orders (held for more than 30 minutes)
     */
    public List<HeldOrder> getOverdueHeldOrders() {
        List<HeldOrder> overdueOrders = new ArrayList<>();
        String sql = "SELECT order_id, table_number, held_time, reason, total_amount, " +
                    "order_data, held_by, created_time, status " +
                    "FROM held_orders " +
                    "WHERE status = 'HELD' " +
                    "AND datetime(held_time, '+30 minutes') < datetime('now') " +
                    "ORDER BY held_time ASC";
        
        try (Connection conn = DatabaseManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                HeldOrder heldOrder = new HeldOrder();
                heldOrder.setOrderId(rs.getString("order_id"));
                heldOrder.setTableNumber(rs.getInt("table_number"));
                heldOrder.setHeldTime(rs.getTimestamp("held_time").toLocalDateTime());
                heldOrder.setReason(rs.getString("reason"));
                heldOrder.setTotalAmount(rs.getDouble("total_amount"));
                heldOrder.setOrderData(rs.getString("order_data"));
                heldOrder.setHeldBy(rs.getString("held_by"));
                heldOrder.setStatus(rs.getString("status"));
                
                Timestamp createdTime = rs.getTimestamp("created_time");
                if (createdTime != null) {
                    heldOrder.setCreatedTime(createdTime.toLocalDateTime());
                }
                
                overdueOrders.add(heldOrder);
            }
            
        } catch (SQLException e) {
            System.err.println("Error retrieving overdue held orders: " + e.getMessage());
            e.printStackTrace();
        }
        
        return overdueOrders;
    }
}
