<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.NotificationPanelController" styleClass="notification-panel" fillWidth="true" VBox.vgrow="ALWAYS">
   <children>
      <!-- Notification Header - Improved Button Alignment -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="notification-header">
         <children>
            <Label text="🔔 Notifications" styleClass="notification-title">
               <font>
                  <Font name="System Bold" size="15.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="unreadCountLabel" text="5" styleClass="unread-count" />
            <HBox spacing="8.0" alignment="CENTER_RIGHT">
               <children>
                  <Button fx:id="markAllReadBtn" onAction="#markAllAsRead" styleClass="notification-action-btn" text="Mark All Read" prefWidth="110.0" prefHeight="32.0" />
                  <Button fx:id="clearAllBtn" onAction="#clearAllNotifications" styleClass="notification-action-btn-danger" text="Clear All" prefWidth="85.0" prefHeight="32.0" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="10.0" left="12.0" right="12.0" top="10.0" />
         </padding>
      </HBox>
      
      <!-- Quick Action Buttons removed -->
      
      <!-- Filter Tabs -->
      <HBox alignment="CENTER_LEFT" spacing="5.0" styleClass="filter-tabs">
         <children>
            <ToggleButton fx:id="allTab" onAction="#filterAll" selected="true" styleClass="filter-tab" text="All" />
            <ToggleButton fx:id="onlineOrdersTab" onAction="#filterOnlineOrders" styleClass="filter-tab" text="🛒 Online" />
            <ToggleButton fx:id="kotTab" onAction="#filterKOT" styleClass="filter-tab" text="📋 KOT" />
            <ToggleButton fx:id="billingTab" onAction="#filterBilling" styleClass="filter-tab" text="💰 Bills" />
            <ToggleButton fx:id="urgentTab" onAction="#filterUrgent" styleClass="filter-tab" text="🚨 Urgent" />
         </children>
         <padding>
            <Insets bottom="10.0" left="15.0" right="15.0" top="5.0" />
         </padding>
      </HBox>
      
      <!-- Notifications List -->
      <ScrollPane fx:id="notificationScrollPane" fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="ALWAYS" styleClass="notification-scroll-functional" prefHeight="400.0" minHeight="400.0" VBox.vgrow="ALWAYS">
         <content>
            <VBox fx:id="notificationContainer" spacing="5.0" styleClass="notification-container">
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
         </content>
      </ScrollPane>
      
      <!-- Integration panels removed -->
      
      <!-- Bottom Status Bar -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="status-bar">
         <children>
            <Label fx:id="lastUpdateLabel" text="Last updated: Just now" styleClass="status-text" />
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="connectionStatusLabel" text="🟢 Online" styleClass="status-text" />
            <Button fx:id="refreshBtn" onAction="#refreshNotifications" styleClass="refresh-btn" text="🔄" />
         </children>
         <padding>
            <Insets bottom="5.0" left="15.0" right="15.0" top="5.0" />
         </padding>
      </HBox>
   </children>
</VBox>
