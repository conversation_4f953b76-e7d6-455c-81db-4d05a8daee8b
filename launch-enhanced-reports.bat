@echo off
echo 🚀 LAUNCHING ENHANCED REPORTS SYSTEM 🚀
echo.

echo 📊 FEATURES INCLUDED:
echo ✅ Date Range Filtering - Select custom date ranges
echo ✅ Report Type Selection - Daily, Weekly, Monthly views
echo ✅ Order Type Filtering - All, Swiggy, Zomato, Online
echo ✅ Real-time Analytics - Summary cards with key metrics
echo ✅ Interactive Charts - Pie charts, line charts, bar charts
echo ✅ Detailed Data Table - Comprehensive report data
echo ✅ Export Functionality - Export filtered data to CSV
echo.

echo 🔧 COMPILING ENHANCED COMPONENTS...

echo Compiling EnhancedReportService...
javac -d target/classes -cp "target/classes;lib/*" src/main/java/com/restaurant/service/EnhancedReportService.java

if %ERRORLEVEL% neq 0 (
    echo ❌ Failed to compile EnhancedReportService
    pause
    exit /b 1
)

echo Compiling EnhancedReportsController...
javac -d target/classes -cp "target/classes;lib/*" src/main/java/com/restaurant/controller/EnhancedReportsController.java

if %ERRORLEVEL% neq 0 (
    echo ❌ Failed to compile EnhancedReportsController
    pause
    exit /b 1
)

echo ✅ Compilation successful!
echo.

echo 🚀 CREATING ENHANCED LAUNCHER...

echo package com.restaurant.launcher; > src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo. >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo import javafx.application.Application; >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo import javafx.fxml.FXMLLoader; >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo import javafx.scene.Scene; >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo import javafx.stage.Stage; >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo. >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo public class EnhancedReportsLauncher extends Application { >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo. >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo     @Override >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo     public void start(Stage primaryStage) { >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo         try { >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             System.out.println("🚀 Launching Enhanced Reports System..."); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo. >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             FXMLLoader loader = new FXMLLoader(); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             loader.setLocation(getClass().getResource("/com/restaurant/view/enhanced-reports.fxml")); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo. >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             Scene scene = new Scene(loader.load(), 1500, 1000); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo. >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             primaryStage.setTitle("📊 Enhanced Reports ^& Analytics - Restaurant Management"); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             primaryStage.setScene(scene); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             primaryStage.setMaximized(true); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             primaryStage.show(); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo. >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             System.out.println("✅ Enhanced Reports System launched successfully!"); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             System.out.println(); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             System.out.println("📊 ENHANCED FEATURES:"); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             System.out.println("📅 Date Range Filtering - Select any date range"); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             System.out.println("📊 Report Types - Daily, Weekly, Monthly views"); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             System.out.println("🛒 Order Type Filters - All, Swiggy, Zomato, Online"); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             System.out.println("📈 Real-time Analytics - Live summary cards"); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             System.out.println("📊 Interactive Charts - Multiple chart types"); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             System.out.println("📋 Detailed Table - Comprehensive data view"); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             System.out.println("📤 Export Function - CSV export with filters"); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo. >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             System.err.println("❌ Error launching Enhanced Reports: " + e.getMessage()); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo             e.printStackTrace(); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo         } >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo     } >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo. >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo     public static void main(String[] args) { >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo         System.out.println("📊 Starting Enhanced Reports System..."); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo         launch(args); >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo     } >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java
echo } >> src\main\java\com\restaurant\launcher\EnhancedReportsLauncher.java

echo ✅ Enhanced launcher created
echo.

echo 🔧 COMPILING LAUNCHER...
javac -d target/classes -cp "target/classes;lib/*" src/main/java/com/restaurant/launcher/EnhancedReportsLauncher.java

if %ERRORLEVEL% neq 0 (
    echo ❌ Launcher compilation failed
    pause
    exit /b 1
)

echo ✅ Launcher compilation successful
echo.

echo 🚀 LAUNCHING ENHANCED REPORTS INTERFACE...
echo.
echo This will open the enhanced reports system with:
echo.
echo 📅 DATE RANGE FILTERING:
echo    - Select custom start and end dates
echo    - Filter reports for any time period
echo.
echo 📊 REPORT TYPE SELECTION:
echo    - Daily reports - Day-by-day analysis
echo    - Weekly reports - Week-by-week trends
echo    - Monthly reports - Month-by-month insights
echo.
echo 🛒 ORDER TYPE FILTERING:
echo    - All order types (default)
echo    - Swiggy orders only
echo    - Zomato orders only
echo    - Online orders only
echo    - Multiple selections supported
echo.
echo 📈 REAL-TIME ANALYTICS:
echo    - Total orders and revenue
echo    - Average order value
echo    - Peak hour identification
echo    - Platform-wise breakdown
echo.
echo 📊 INTERACTIVE CHARTS:
echo    - Order distribution pie chart
echo    - Revenue trend line chart
echo    - Platform comparison bar chart
echo.
echo 📋 DETAILED DATA TABLE:
echo    - Comprehensive report data
echo    - Sortable columns
echo    - Formatted currency display
echo.
echo 📤 EXPORT FUNCTIONALITY:
echo    - Export filtered data to CSV
echo    - Custom file naming
echo    - Preserves all applied filters
echo.

java -Dprism.order=sw ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2-win.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics,javafx.media ^
     -cp "target/classes;lib/*" ^
     com.restaurant.launcher.EnhancedReportsLauncher

echo.
echo 🎉 ENHANCED REPORTS SYSTEM LAUNCHED!
echo.

pause
