/* Restaurant Management System - Fixed CSS Styling */
/* This CSS file fixes the JavaFX ClassCastException errors by using proper syntax */

/* Global Root Styling */
.root {
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-background-color: #f8f9fa;
}

/* Button Styling - FIXED SYNTAX */
.button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 10px 20px 10px 20px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
}

.button:hover {
    -fx-background-color: #0056b3;
}

.button:pressed {
    -fx-background-color: #004085;
}

/* Text Field Styling - FIXED SYNTAX */
.text-field {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 12px 16px 12px 16px;
    -fx-font-size: 14px;
}

.text-field:focused {
    -fx-border-color: #007bff;
}

/* ComboBox Styling - FIXED SYNTAX */
.combo-box {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 12px 16px 12px 16px;
    -fx-font-size: 14px;
}

.combo-box:focused {
    -fx-border-color: #007bff;
}

/* Table Styling - FIXED SYNTAX */
.table-view {
    -fx-background-color: white;
    -fx-background-radius: 8px;
    -fx-border-color: #e9ecef;
    -fx-border-radius: 8px;
    -fx-border-width: 1px;
}

.table-view .column-header {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
}

.table-view .table-row-cell {
    -fx-border-color: #e9ecef;
}

.table-view .table-row-cell:selected {
    -fx-background-color: rgba(0, 123, 255, 0.1);
}

/* Card Styling - FIXED SYNTAX */
.card {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-padding: 20px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
}

/* Primary Button Variant */
.button.primary {
    -fx-background-color: #28a745;
}

.button.primary:hover {
    -fx-background-color: #218838;
}

/* Secondary Button Variant */
.button.secondary {
    -fx-background-color: #6c757d;
}

.button.secondary:hover {
    -fx-background-color: #545b62;
}

/* Quick Action Buttons - FIXED SYNTAX */
.quick-action-btn {
    -fx-background-color: #f7fafc;
    -fx-text-fill: #4a5568;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 15px 20px 15px 20px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-border-color: #e2e8f0;
    -fx-border-width: 1px;
}

.quick-action-btn:hover {
    -fx-background-color: #edf2f7;
}

/* Modal Dialog - FIXED SYNTAX */
.modal-dialog {
    -fx-background-color: white;
    -fx-background-radius: 10px;
    -fx-border-radius: 10px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
}

.modal-header {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 10px 10px 0px 0px;
    -fx-padding: 20px 25px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 0px 0px 1px 0px;
}

.modal-footer {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 0px 0px 10px 10px;
    -fx-padding: 18px 25px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px 0px 0px 0px;
}

/* Form Fields - FIXED SYNTAX */
.form-field {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-width: 1px;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
    -fx-padding: 10px 12px;
    -fx-font-size: 14px;
}

.form-field:focused {
    -fx-border-color: #007bff;
}

/* Action Buttons - FIXED SYNTAX */
.save-btn {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-background-radius: 5px;
    -fx-border-radius: 5px;
    -fx-padding: 10px 20px;
    -fx-font-size: 14px;
    -fx-font-weight: 500;
    -fx-cursor: hand;
}

.save-btn:hover {
    -fx-background-color: #0056b3;
}

.cancel-btn {
    -fx-background-color: white;
    -fx-text-fill: #6c757d;
    -fx-background-radius: 5px;
    -fx-border-radius: 5px;
    -fx-padding: 10px 20px;
    -fx-font-size: 14px;
    -fx-font-weight: 500;
    -fx-cursor: hand;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
}

.cancel-btn:hover {
    -fx-background-color: #f8f9fa;
}

/* Navigation Buttons - FIXED SYNTAX */
.nav-button {
    -fx-background-color: transparent;
    -fx-text-fill: #6b7280;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 8px 12px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
    -fx-font-size: 12px;
}

.nav-button:hover {
    -fx-background-color: #f3f4f6;
}

.nav-button:pressed {
    -fx-background-color: #e5e7eb;
}

/* Status Indicators - FIXED SYNTAX */
.status-badge {
    -fx-padding: 4px 12px 4px 12px;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

.status-active {
    -fx-background-color: #d4edda;
    -fx-text-fill: #155724;
}

.status-pending {
    -fx-background-color: #fff3cd;
    -fx-text-fill: #856404;
}

.status-error {
    -fx-background-color: #f8d7da;
    -fx-text-fill: #721c24;
}

/* Scroll Panes - FIXED SYNTAX */
.scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.scroll-pane .viewport {
    -fx-background-color: transparent;
}

.scroll-pane .scroll-bar:vertical {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 4px;
    -fx-pref-width: 8px;
}

.scroll-pane .scroll-bar:vertical .thumb {
    -fx-background-color: #dee2e6;
    -fx-background-radius: 4px;
}

.scroll-pane .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #adb5bd;
}

/* Order Summary - FIXED SYNTAX */
.order-summary {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-padding: 15px;
}

/* Menu Item Cards - FIXED SYNTAX */
.menu-item-card {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 15px;
    -fx-spacing: 8px;
}

.menu-item-card:hover {
    -fx-border-color: #007bff;
}

/* This CSS file contains only essential styles with proper JavaFX syntax */
/* All radius values include 'px' units to prevent ClassCastException errors */
/* All padding values use proper 4-value syntax */
/* This should eliminate the CSS-related application crashes */
