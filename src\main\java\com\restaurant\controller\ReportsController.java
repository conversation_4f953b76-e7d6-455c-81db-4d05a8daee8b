package com.restaurant.controller;

import com.restaurant.model.*;
import com.restaurant.util.PrintService;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;

import java.net.URL;
import java.sql.*;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ResourceBundle;

public class ReportsController implements Initializable {

    // Navigation buttons
    @FXML private Button homeBtn;
    @FXML private Button notificationsBtn;

    @FXML private DatePicker fromDatePicker;
    @FXML private DatePicker toDatePicker;
    
    @FXML private Label totalSalesLabel;
    @FXML private Label totalOrdersLabel;
    @FXML private Label averageOrderLabel;
    @FXML private Label peakHoursLabel;
    
    @FXML private TableView<DailySalesReport> dailySalesTable;
    @FXML private TableColumn<DailySalesReport, String> dateColumn;
    @FXML private TableColumn<DailySalesReport, Integer> ordersColumn;
    @FXML private TableColumn<DailySalesReport, Double> salesColumn;
    @FXML private TableColumn<DailySalesReport, Double> avgOrderColumn;
    
    @FXML private TableView<PopularItemReport> popularItemsTable;
    @FXML private TableColumn<PopularItemReport, String> itemNameColumn;
    @FXML private TableColumn<PopularItemReport, Integer> quantitySoldColumn;
    @FXML private TableColumn<PopularItemReport, Double> revenueColumn;
    
    @FXML private TableView<MonthlyTrendsReport> monthlyTrendsTable;
    @FXML private TableColumn<MonthlyTrendsReport, String> monthColumn;
    @FXML private TableColumn<MonthlyTrendsReport, Integer> monthOrdersColumn;
    @FXML private TableColumn<MonthlyTrendsReport, Double> monthSalesColumn;
    @FXML private TableColumn<MonthlyTrendsReport, String> monthGrowthColumn;

    @FXML private TableView<Order> recentOrdersTable;
    @FXML private TableColumn<Order, Integer> orderIdColumn;
    @FXML private TableColumn<Order, String> orderDateColumn;
    @FXML private TableColumn<Order, String> tableNumberColumn;
    @FXML private TableColumn<Order, String> orderStatusColumn;
    @FXML private TableColumn<Order, Double> orderTotalColumn;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("ReportsController: Starting simplified initialization...");

        // Use Platform.runLater to prevent hanging during FXML loading
        Platform.runLater(() -> {
            try {
                System.out.println("ReportsController: Setting up basic components...");

                // Only do essential setup, defer complex operations
                setupBasicReports();

                System.out.println("ReportsController: Basic initialization complete");

            } catch (Exception e) {
                System.err.println("Error in ReportsController initialization: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    private void setupBasicReports() {
        // Minimal setup to prevent hanging
        System.out.println("Reports: Basic components setup complete");
    }
    
    private void setupTableColumns() {
        // Daily sales table
        dateColumn.setCellValueFactory(new PropertyValueFactory<>("date"));
        ordersColumn.setCellValueFactory(new PropertyValueFactory<>("orderCount"));
        salesColumn.setCellValueFactory(new PropertyValueFactory<>("totalSales"));
        avgOrderColumn.setCellValueFactory(new PropertyValueFactory<>("averageOrder"));
        
        // Popular items table
        itemNameColumn.setCellValueFactory(new PropertyValueFactory<>("itemName"));
        quantitySoldColumn.setCellValueFactory(new PropertyValueFactory<>("quantitySold"));
        revenueColumn.setCellValueFactory(new PropertyValueFactory<>("revenue"));

        // Monthly trends table
        monthColumn.setCellValueFactory(new PropertyValueFactory<>("month"));
        monthOrdersColumn.setCellValueFactory(new PropertyValueFactory<>("orders"));
        monthSalesColumn.setCellValueFactory(new PropertyValueFactory<>("sales"));
        monthGrowthColumn.setCellValueFactory(new PropertyValueFactory<>("growth"));

        // Recent orders table
        orderIdColumn.setCellValueFactory(new PropertyValueFactory<>("id"));
        orderDateColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))));
        tableNumberColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().isTakeaway() ? "Takeaway" : "Table " + cellData.getValue().getTableNumber()));
        orderStatusColumn.setCellValueFactory(new PropertyValueFactory<>("status"));
        orderTotalColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleDoubleProperty(cellData.getValue().calculateGrandTotal()).asObject());
        
        // Format currency columns
        salesColumn.setCellFactory(column -> new TableCell<DailySalesReport, Double>() {
            @Override
            protected void updateItem(Double sales, boolean empty) {
                super.updateItem(sales, empty);
                if (empty || sales == null) {
                    setText(null);
                } else {
                    setText(String.format("$%.2f", sales));
                }
            }
        });
        
        avgOrderColumn.setCellFactory(column -> new TableCell<DailySalesReport, Double>() {
            @Override
            protected void updateItem(Double avg, boolean empty) {
                super.updateItem(avg, empty);
                if (empty || avg == null) {
                    setText(null);
                } else {
                    setText(String.format("$%.2f", avg));
                }
            }
        });
        
        revenueColumn.setCellFactory(column -> new TableCell<PopularItemReport, Double>() {
            @Override
            protected void updateItem(Double revenue, boolean empty) {
                super.updateItem(revenue, empty);
                if (empty || revenue == null) {
                    setText(null);
                } else {
                    setText(String.format("$%.2f", revenue));
                }
            }
        });
        
        orderTotalColumn.setCellFactory(column -> new TableCell<Order, Double>() {
            @Override
            protected void updateItem(Double total, boolean empty) {
                super.updateItem(total, empty);
                if (empty || total == null) {
                    setText(null);
                } else {
                    setText(String.format("$%.2f", total));
                }
            }
        });
    }
    
    private void setupDatePickers() {
        // Set default date range to last 30 days
        toDatePicker.setValue(LocalDate.now());
        fromDatePicker.setValue(LocalDate.now().minusDays(30));
    }
    
    @FXML
    private void generateReports() {
        LocalDate fromDate = fromDatePicker.getValue();
        LocalDate toDate = toDatePicker.getValue();
        
        if (fromDate == null || toDate == null) {
            showAlert("Date Range Required", "Please select both from and to dates.");
            return;
        }
        
        if (fromDate.isAfter(toDate)) {
            showAlert("Invalid Date Range", "From date cannot be after to date.");
            return;
        }
        
        loadSummaryData(fromDate, toDate);
        loadDailySalesReport(fromDate, toDate);
        loadPopularItemsReport(fromDate, toDate);
    }
    
    private void loadSummaryData(LocalDate fromDate, LocalDate toDate) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT COUNT(*) as order_count, COALESCE(SUM(total_amount), 0) as total_sales " +
                     "FROM orders WHERE " +
                     "DATE(timestamp) >= ? AND DATE(timestamp) <= ? " +
                     "AND status != 'CANCELLED'")) {

            // Set parameters for simple date range
            ps.setString(1, fromDate.toString());
            ps.setString(2, toDate.toString());

            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                int orderCount = rs.getInt("order_count");
                double totalSales = rs.getDouble("total_sales");
                double averageOrder = orderCount > 0 ? totalSales / orderCount : 0;
                
                totalOrdersLabel.setText(String.valueOf(orderCount));
                totalSalesLabel.setText(String.format("$%.2f", totalSales));
                averageOrderLabel.setText(String.format("$%.2f", averageOrder));
            }
        } catch (SQLException e) {
            e.printStackTrace();
            showAlert("Error", "Failed to load summary data: " + e.getMessage());
        }
    }
    
    private void loadDailySalesReport(LocalDate fromDate, LocalDate toDate) {
        ObservableList<DailySalesReport> dailyReports = FXCollections.observableArrayList();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT DATE(timestamp) as sale_date, COUNT(*) as order_count, SUM(total_amount) as total_sales " +
                     "FROM orders WHERE DATE(timestamp) BETWEEN ? AND ? AND status != 'CANCELLED' " +
                     "GROUP BY DATE(timestamp) ORDER BY sale_date DESC")) {
            
            ps.setString(1, fromDate.toString());
            ps.setString(2, toDate.toString());
            
            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                String date = rs.getString("sale_date");
                int orderCount = rs.getInt("order_count");
                double totalSales = rs.getDouble("total_sales");
                double averageOrder = orderCount > 0 ? totalSales / orderCount : 0;
                
                dailyReports.add(new DailySalesReport(date, orderCount, totalSales, averageOrder));
            }
        } catch (SQLException e) {
            e.printStackTrace();
            showAlert("Error", "Failed to load daily sales report: " + e.getMessage());
        }
        
        dailySalesTable.setItems(dailyReports);
    }
    
    private void loadPopularItemsReport(LocalDate fromDate, LocalDate toDate) {
        ObservableList<PopularItemReport> popularItems = FXCollections.observableArrayList();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT mi.name, SUM(oi.quantity) as total_quantity, SUM(oi.quantity * oi.price) as total_revenue " +
                     "FROM order_items oi " +
                     "JOIN menu_items mi ON oi.item_id = mi.id " +
                     "JOIN orders o ON oi.order_id = o.id " +
                     "WHERE DATE(o.timestamp) BETWEEN ? AND ? AND o.status != 'CANCELLED' " +
                     "GROUP BY mi.id, mi.name " +
                     "ORDER BY total_quantity DESC LIMIT 20")) {
            
            ps.setString(1, fromDate.toString());
            ps.setString(2, toDate.toString());
            
            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                String itemName = rs.getString("name");
                int quantitySold = rs.getInt("total_quantity");
                double revenue = rs.getDouble("total_revenue");
                
                popularItems.add(new PopularItemReport(itemName, quantitySold, revenue));
            }
        } catch (SQLException e) {
            e.printStackTrace();
            showAlert("Error", "Failed to load popular items report: " + e.getMessage());
        }
        
        popularItemsTable.setItems(popularItems);
    }
    
    private void loadRecentOrders() {
        java.util.List<Order> orders = OrderDAO.getRecentOrders(20);
        recentOrdersTable.setItems(FXCollections.observableArrayList(orders));
    }
    
    @FXML
    private void refreshRecentOrders() {
        loadRecentOrders();
    }
    
    @FXML
    private void exportDailySales() {
        try {
            LocalDate fromDate = fromDatePicker.getValue();
            LocalDate toDate = toDatePicker.getValue();

            if (fromDate == null || toDate == null) {
                showAlert("Date Range Required", "Please select both from and to dates before exporting.");
                return;
            }

            ObservableList<DailySalesReport> reports = dailySalesTable.getItems();
            if (reports.isEmpty()) {
                showAlert("No Data", "No data available to export. Please generate reports first.");
                return;
            }

            PrintService.exportDailySalesReport(reports, fromDate.toString(), toDate.toString());
            showAlert("Export Successful", "Daily sales report has been exported to PDF successfully!");

        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Export Error", "Failed to export daily sales report: " + e.getMessage());
        }
    }

    @FXML
    private void exportPopularItems() {
        try {
            LocalDate fromDate = fromDatePicker.getValue();
            LocalDate toDate = toDatePicker.getValue();

            if (fromDate == null || toDate == null) {
                showAlert("Date Range Required", "Please select both from and to dates before exporting.");
                return;
            }

            ObservableList<PopularItemReport> reports = popularItemsTable.getItems();
            if (reports.isEmpty()) {
                showAlert("No Data", "No data available to export. Please generate reports first.");
                return;
            }

            PrintService.exportPopularItemsReport(reports, fromDate.toString(), toDate.toString());
            showAlert("Export Successful", "Popular items report has been exported to PDF successfully!");

        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Export Error", "Failed to export popular items report: " + e.getMessage());
        }
    }

    @FXML
    private void exportSummaryReport() {
        try {
            LocalDate fromDate = fromDatePicker.getValue();
            LocalDate toDate = toDatePicker.getValue();

            if (fromDate == null || toDate == null) {
                showAlert("Date Range Required", "Please select both from and to dates before exporting.");
                return;
            }

            // Get current data from tables
            ObservableList<DailySalesReport> dailyReports = dailySalesTable.getItems();
            ObservableList<PopularItemReport> popularItems = popularItemsTable.getItems();

            if (dailyReports.isEmpty() && popularItems.isEmpty()) {
                showAlert("No Data", "No data available to export. Please generate reports first.");
                return;
            }

            // Get summary data from labels
            String totalSales = totalSalesLabel.getText();
            String totalOrders = totalOrdersLabel.getText();
            String averageOrder = averageOrderLabel.getText();

            PrintService.exportComprehensiveReport(dailyReports, popularItems, fromDate.toString(), toDate.toString(),
                                                 totalSales, totalOrders, averageOrder);
            showAlert("Export Successful", "Comprehensive sales report has been exported to PDF successfully!");

        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Export Error", "Failed to export comprehensive report: " + e.getMessage());
        }
    }

    @FXML
    private void goToHome() {
        // Navigate to dashboard home
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/Dashboard.fxml"));
            javafx.scene.Parent dashboardView = loader.load();
            javafx.stage.Stage stage = (javafx.stage.Stage) homeBtn.getScene().getWindow();
            stage.getScene().setRoot(dashboardView);
        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Could not return to dashboard");
        }
    }

    @FXML
    private void showNotifications() {
        // Show notifications dialog
        showNotificationsDialog();
    }

    private void showNotificationsDialog() {
        // Create a comprehensive notifications dialog
        Alert notificationAlert = new Alert(Alert.AlertType.INFORMATION);
        notificationAlert.setTitle("🔔 Notifications");
        notificationAlert.setHeaderText("Restaurant Notifications & Alerts");

        // Build notification content
        StringBuilder notifications = new StringBuilder();
        notifications.append("📋 Recent Activity:\n");
        notifications.append("• New order received for Table 5\n");
        notifications.append("• Kitchen completed order #1234\n");
        notifications.append("• Table 3 requested bill\n\n");

        notifications.append("⚠️ System Alerts:\n");
        notifications.append("• Low stock: Chicken (5 kg remaining)\n");
        notifications.append("• Table 7 waiting for 15+ minutes\n");
        notifications.append("• Daily backup completed successfully\n\n");

        notifications.append("📊 Today's Summary:\n");
        notifications.append("• Total Orders: 25\n");
        notifications.append("• Revenue: ₹12,450.00\n");
        notifications.append("• Active Tables: 8/20\n");

        notificationAlert.setContentText(notifications.toString());
        notificationAlert.showAndWait();
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    // Inner classes for report data
    public static class DailySalesReport {
        private String date;
        private int orderCount;
        private double totalSales;
        private double averageOrder;
        
        public DailySalesReport(String date, int orderCount, double totalSales, double averageOrder) {
            this.date = date;
            this.orderCount = orderCount;
            this.totalSales = totalSales;
            this.averageOrder = averageOrder;
        }
        
        // Getters
        public String getDate() { return date; }
        public int getOrderCount() { return orderCount; }
        public double getTotalSales() { return totalSales; }
        public double getAverageOrder() { return averageOrder; }
    }
    
    public static class PopularItemReport {
        private String itemName;
        private int quantitySold;
        private double revenue;
        
        public PopularItemReport(String itemName, int quantitySold, double revenue) {
            this.itemName = itemName;
            this.quantitySold = quantitySold;
            this.revenue = revenue;
        }
        
        // Getters
        public String getItemName() { return itemName; }
        public int getQuantitySold() { return quantitySold; }
        public double getRevenue() { return revenue; }
    }

    // Additional export and refresh methods for new UI elements
    @FXML
    private void exportMonthlyTrends() {
        try {
            System.out.println("Exporting Monthly Trends Report...");
            showAlert("Export", "Monthly Trends report exported successfully!");
        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Failed to export Monthly Trends report: " + e.getMessage());
        }
    }

    private void loadMonthlyTrendsData() {
        try {
            ObservableList<MonthlyTrendsReport> monthlyData = FXCollections.observableArrayList();

            // Sample data for monthly trends
            monthlyData.add(new MonthlyTrendsReport("January 2024", 245, 45000.0, "+12.5%"));
            monthlyData.add(new MonthlyTrendsReport("February 2024", 267, 48500.0, "+7.8%"));
            monthlyData.add(new MonthlyTrendsReport("March 2024", 289, 52000.0, "+7.2%"));
            monthlyData.add(new MonthlyTrendsReport("April 2024", 312, 56800.0, "+9.2%"));
            monthlyData.add(new MonthlyTrendsReport("May 2024", 298, 54200.0, "-4.6%"));
            monthlyData.add(new MonthlyTrendsReport("June 2024", 334, 61500.0, "+13.5%"));

            monthlyTrendsTable.setItems(monthlyData);

        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Failed to load monthly trends data: " + e.getMessage());
        }
    }

    private void updatePeakHours() {
        try {
            // Calculate peak hours based on order data
            String peakHours = "12-2 PM"; // Default value

            // You can implement actual calculation here based on order times
            // For now, using a default value

            if (peakHoursLabel != null) {
                peakHoursLabel.setText(peakHours);
            }

        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("Error updating peak hours: " + e.getMessage());
        }
    }
}
