package com.restaurant.model;

import java.time.LocalDateTime;
import java.time.Month;

public class MonthlyReport {
    private int id;
    private int reportMonth;
    private int reportYear;
    private int totalOrders;
    private double totalRevenue;
    private int swiggyOrders;
    private int zomatoOrders;
    private double swiggyRevenue;
    private double zomatoRevenue;
    private double avgDailyOrders;
    private double avgDailyRevenue;
    private double growthPercentage;
    private String bestWeek;
    private LocalDateTime createdAt;
    
    // Constructors
    public MonthlyReport() {}
    
    public MonthlyReport(int reportMonth, int reportYear) {
        this.reportMonth = reportMonth;
        this.reportYear = reportYear;
        this.createdAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public int getReportMonth() { return reportMonth; }
    public void setReportMonth(int reportMonth) { this.reportMonth = reportMonth; }
    
    public int getReportYear() { return reportYear; }
    public void setReportYear(int reportYear) { this.reportYear = reportYear; }
    
    public int getTotalOrders() { return totalOrders; }
    public void setTotalOrders(int totalOrders) { this.totalOrders = totalOrders; }
    
    public double getTotalRevenue() { return totalRevenue; }
    public void setTotalRevenue(double totalRevenue) { this.totalRevenue = totalRevenue; }
    
    public int getSwiggyOrders() { return swiggyOrders; }
    public void setSwiggyOrders(int swiggyOrders) { this.swiggyOrders = swiggyOrders; }
    
    public int getZomatoOrders() { return zomatoOrders; }
    public void setZomatoOrders(int zomatoOrders) { this.zomatoOrders = zomatoOrders; }
    
    public double getSwiggyRevenue() { return swiggyRevenue; }
    public void setSwiggyRevenue(double swiggyRevenue) { this.swiggyRevenue = swiggyRevenue; }
    
    public double getZomatoRevenue() { return zomatoRevenue; }
    public void setZomatoRevenue(double zomatoRevenue) { this.zomatoRevenue = zomatoRevenue; }
    
    public double getAvgDailyOrders() { return avgDailyOrders; }
    public void setAvgDailyOrders(double avgDailyOrders) { this.avgDailyOrders = avgDailyOrders; }
    
    public double getAvgDailyRevenue() { return avgDailyRevenue; }
    public void setAvgDailyRevenue(double avgDailyRevenue) { this.avgDailyRevenue = avgDailyRevenue; }
    
    public double getGrowthPercentage() { return growthPercentage; }
    public void setGrowthPercentage(double growthPercentage) { this.growthPercentage = growthPercentage; }
    
    public String getBestWeek() { return bestWeek; }
    public void setBestWeek(String bestWeek) { this.bestWeek = bestWeek; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    // Utility methods
    public double getSwiggyPercentage() {
        return totalOrders > 0 ? (swiggyOrders * 100.0) / totalOrders : 0.0;
    }
    
    public double getZomatoPercentage() {
        return totalOrders > 0 ? (zomatoOrders * 100.0) / totalOrders : 0.0;
    }
    
    public double getAvgOrderValue() {
        return totalOrders > 0 ? totalRevenue / totalOrders : 0.0;
    }
    
    public String getMonthName() {
        return Month.of(reportMonth).name();
    }
    
    public String getMonthYear() {
        return getMonthName() + " " + reportYear;
    }
    
    public String getGrowthStatus() {
        if (growthPercentage > 0) {
            return "Growth";
        } else if (growthPercentage < 0) {
            return "Decline";
        } else {
            return "Stable";
        }
    }
    
    public String getDominantPlatform() {
        if (swiggyRevenue > zomatoRevenue) {
            return "Swiggy";
        } else if (zomatoRevenue > swiggyRevenue) {
            return "Zomato";
        } else {
            return "Equal";
        }
    }
    
    @Override
    public String toString() {
        return String.format("MonthlyReport{month=%s %d, orders=%d, revenue=%.2f, growth=%.1f%%}", 
                           getMonthName(), reportYear, totalOrders, totalRevenue, growthPercentage);
    }
}
