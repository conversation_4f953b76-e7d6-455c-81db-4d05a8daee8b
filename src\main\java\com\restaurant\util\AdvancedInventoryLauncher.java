package com.restaurant.util;

import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.Parent;
import javafx.stage.Stage;

/**
 * Launcher for Advanced Inventory Management interface
 * This provides Purchase Orders and Internal Transfers management
 */
public class AdvancedInventoryLauncher extends Application {

    @Override
    public void start(Stage primaryStage) {
        try {
            // Load the FXML file
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/AdvancedInventoryManagement.fxml"));
            Parent root = loader.load();
            
            // Create scene with smaller, more compact size
            Scene scene = new Scene(root, 900, 650);

            // Set up stage
            primaryStage.setTitle("Advanced Inventory Management - Restaurant System");
            primaryStage.setScene(scene);
            primaryStage.setMinWidth(800);
            primaryStage.setMinHeight(550);
            primaryStage.show();
            
            System.out.println("Advanced Inventory Management interface launched successfully");
            
        } catch (Exception e) {
            System.err.println("Error launching Advanced Inventory Management: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        launch(args);
    }
}
