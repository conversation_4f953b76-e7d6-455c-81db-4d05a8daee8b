package com.restaurant.controller;

import javafx.animation.*;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.shape.*;
import javafx.scene.text.Text;
import javafx.util.Duration;

import java.net.URL;
import java.util.*;
import java.util.regex.Pattern;

import com.restaurant.service.VoiceCommandProcessor;
import com.restaurant.model.VoiceCommand;

public class VoiceInputController implements Initializable {

    // Voice Activation Controls
    @FXML private VBox voiceInputContainer;
    @FXML private Button micButton;
    @FXML private Label micIcon;
    @FXML private Tooltip micTooltip;
    @FXML private Circle pulseRing;
    @FXML private HBox waveformContainer;
    @FXML private Rectangle wave1, wave2, wave3, wave4, wave5, wave6, wave7;
    
    // Status and Controls
    @FXML private Label statusIcon;
    @FXML private Text statusText;
    @FXML private HBox commandHints;
    @FXML private MenuButton voiceSettingsButton;
    @FXML private CheckMenuItem autoSubmitMenuItem;
    @FXML private CheckMenuItem showHintsMenuItem;
    
    // Transcription Area
    @FXML private VBox transcriptionArea;
    @FXML private TextArea transcriptionText;
    @FXML private Button clearTranscriptionButton;
    @FXML private HBox commandStatus;
    @FXML private Label commandStatusIcon;
    @FXML private Text commandStatusText;
    @FXML private ProgressIndicator commandProgress;
    
    // Action Buttons
    @FXML private Button submitCommandButton;
    @FXML private Button tryAgainButton;
    @FXML private Button cancelVoiceButton;
    
    // Suggestions and Results
    @FXML private VBox suggestionsPanel;
    @FXML private FlowPane suggestionsFlow;
    @FXML private HBox matchResultContainer;
    @FXML private Label matchResultIcon;
    @FXML private Text matchResultTitle;
    @FXML private Text matchResultDescription;
    @FXML private Button executeMatchButton;
    
    // Error Display
    @FXML private HBox errorContainer;
    @FXML private Label errorIcon;
    @FXML private Text errorTitle;
    @FXML private Text errorDescription;
    @FXML private Button retryErrorButton;

    // Voice Processing
    private VoiceCommandProcessor commandProcessor;
    private VoiceState currentState = VoiceState.IDLE;
    private Timeline pulseAnimation;
    private Timeline waveformAnimation;
    private VoiceCommand lastRecognizedCommand;
    private boolean isListening = false;

    public enum VoiceState {
        IDLE, LISTENING, PROCESSING, COMMAND_MATCHED, ERROR
    }

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        commandProcessor = new VoiceCommandProcessor();
        setupAnimations();
        setupEventHandlers();
        setupInitialState();
    }

    private void setupAnimations() {
        // Pulse ring animation
        pulseAnimation = new Timeline(
            new KeyFrame(Duration.ZERO, 
                new KeyValue(pulseRing.radiusProperty(), 25.0),
                new KeyValue(pulseRing.opacityProperty(), 1.0)),
            new KeyFrame(Duration.seconds(1.0), 
                new KeyValue(pulseRing.radiusProperty(), 35.0),
                new KeyValue(pulseRing.opacityProperty(), 0.0))
        );
        pulseAnimation.setCycleCount(Timeline.INDEFINITE);

        // Waveform animation
        Rectangle[] waves = {wave1, wave2, wave3, wave4, wave5, wave6, wave7};
        waveformAnimation = new Timeline();
        
        for (int i = 0; i < waves.length; i++) {
            final int index = i;
            KeyFrame frame = new KeyFrame(
                Duration.millis(100 * i),
                e -> animateWave(waves[index])
            );
            waveformAnimation.getKeyFrames().add(frame);
        }
        waveformAnimation.setCycleCount(Timeline.INDEFINITE);
    }

    private void animateWave(Rectangle wave) {
        double randomHeight = 8 + Math.random() * 20;
        Timeline waveTimeline = new Timeline(
            new KeyFrame(Duration.ZERO, new KeyValue(wave.heightProperty(), wave.getHeight())),
            new KeyFrame(Duration.millis(300), new KeyValue(wave.heightProperty(), randomHeight))
        );
        waveTimeline.setAutoReverse(true);
        waveTimeline.setCycleCount(2);
        waveTimeline.play();
    }

    private void setupEventHandlers() {
        // Auto-submit setting
        autoSubmitMenuItem.selectedProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal && lastRecognizedCommand != null) {
                executeMatchedCommand();
            }
        });

        // Show hints setting
        showHintsMenuItem.selectedProperty().addListener((obs, oldVal, newVal) -> {
            commandHints.setVisible(newVal);
        });

        // Transcription text changes
        transcriptionText.textProperty().addListener((obs, oldText, newText) -> {
            if (newText != null && !newText.trim().isEmpty()) {
                submitCommandButton.setDisable(false);
                processTranscriptionChange(newText);
            } else {
                submitCommandButton.setDisable(true);
            }
        });
    }

    private void setupInitialState() {
        setState(VoiceState.IDLE);
        commandHints.setVisible(showHintsMenuItem.isSelected());
    }

    @FXML
    private void handleMicButtonClick() {
        switch (currentState) {
            case IDLE:
                startListening();
                break;
            case LISTENING:
                stopListening();
                break;
            case PROCESSING:
                // Cannot interrupt processing
                break;
            default:
                setState(VoiceState.IDLE);
                break;
        }
    }

    private void startListening() {
        setState(VoiceState.LISTENING);
        isListening = true;
        
        // Start speech recognition simulation
        Task<String> speechTask = new Task<String>() {
            @Override
            protected String call() throws Exception {
                // Simulate speech recognition delay
                Thread.sleep(3000 + (long)(Math.random() * 2000));
                
                // Simulate various voice commands
                String[] sampleCommands = {
                    "forecast sales for next 30 days",
                    "search butter chicken in menu",
                    "open table 5 order details",
                    "show top selling items this week",
                    "generate report for July",
                    "reset all filters",
                    "navigate to billing section"
                };
                
                return sampleCommands[(int)(Math.random() * sampleCommands.length)];
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    if (isListening) {
                        String recognizedText = getValue();
                        transcriptionText.setText(recognizedText);
                        stopListening();
                        processCommand(recognizedText);
                    }
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    if (isListening) {
                        showError("Speech Recognition Failed", "Could not capture audio. Please check your microphone.");
                        setState(VoiceState.IDLE);
                    }
                });
            }
        };

        new Thread(speechTask).start();
    }

    private void stopListening() {
        isListening = false;
        setState(VoiceState.IDLE);
    }

    private void processCommand(String text) {
        setState(VoiceState.PROCESSING);
        
        Task<VoiceCommand> processTask = new Task<VoiceCommand>() {
            @Override
            protected VoiceCommand call() throws Exception {
                Thread.sleep(1000); // Simulate processing time
                return commandProcessor.processCommand(text);
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    VoiceCommand command = getValue();
                    if (command != null && command.isRecognized()) {
                        showCommandMatch(command);
                        lastRecognizedCommand = command;
                        
                        if (autoSubmitMenuItem.isSelected()) {
                            executeMatchedCommand();
                        }
                    } else {
                        showCommandSuggestions(text);
                    }
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    showError("Processing Failed", "Could not process the voice command.");
                });
            }
        };

        new Thread(processTask).start();
    }

    private void processTranscriptionChange(String text) {
        // Real-time command suggestions as user types/speaks
        if (text.length() > 3) {
            List<String> suggestions = commandProcessor.getSuggestions(text);
            updateSuggestions(suggestions);
        }
    }

    private void setState(VoiceState state) {
        currentState = state;
        
        // Hide all status containers first
        hideAllContainers();
        
        switch (state) {
            case IDLE:
                micIcon.setText("🎤");
                micButton.getStyleClass().removeAll("listening", "processing");
                micButton.getStyleClass().add("idle");
                statusIcon.setText("💬");
                statusText.setText("Ready to listen");
                micTooltip.setText("Click to speak");
                stopAnimations();
                break;
                
            case LISTENING:
                micIcon.setText("🔴");
                micButton.getStyleClass().removeAll("idle", "processing");
                micButton.getStyleClass().add("listening");
                statusIcon.setText("🎙️");
                statusText.setText("Listening...");
                micTooltip.setText("Click to stop");
                transcriptionArea.setVisible(true);
                startAnimations();
                break;
                
            case PROCESSING:
                micIcon.setText("⏳");
                micButton.getStyleClass().removeAll("idle", "listening");
                micButton.getStyleClass().add("processing");
                statusIcon.setText("🧠");
                statusText.setText("Processing command...");
                micTooltip.setText("Processing...");
                commandStatus.setVisible(true);
                commandProgress.setVisible(true);
                stopAnimations();
                break;
                
            case COMMAND_MATCHED:
                setState(VoiceState.IDLE);
                matchResultContainer.setVisible(true);
                break;
                
            case ERROR:
                setState(VoiceState.IDLE);
                errorContainer.setVisible(true);
                break;
        }
    }

    private void hideAllContainers() {
        transcriptionArea.setVisible(false);
        commandStatus.setVisible(false);
        commandProgress.setVisible(false);
        suggestionsPanel.setVisible(false);
        matchResultContainer.setVisible(false);
        errorContainer.setVisible(false);
    }

    private void startAnimations() {
        pulseRing.setVisible(true);
        waveformContainer.setVisible(true);
        pulseAnimation.play();
        waveformAnimation.play();
    }

    private void stopAnimations() {
        pulseAnimation.stop();
        waveformAnimation.stop();
        pulseRing.setVisible(false);
        waveformContainer.setVisible(false);
    }

    private void showCommandMatch(VoiceCommand command) {
        setState(VoiceState.COMMAND_MATCHED);
        matchResultIcon.setText("✅");
        matchResultTitle.setText("Command Recognized");
        matchResultDescription.setText(command.getDescription());
    }

    private void showCommandSuggestions(String originalText) {
        setState(VoiceState.IDLE);
        List<String> suggestions = commandProcessor.getSuggestions(originalText);
        updateSuggestions(suggestions);
        suggestionsPanel.setVisible(true);
    }

    private void updateSuggestions(List<String> suggestions) {
        suggestionsFlow.getChildren().clear();
        
        for (String suggestion : suggestions) {
            Button suggestionButton = new Button(suggestion);
            suggestionButton.getStyleClass().add("suggestion-button");
            suggestionButton.setOnAction(e -> {
                transcriptionText.setText(suggestion);
                processCommand(suggestion);
            });
            suggestionsFlow.getChildren().add(suggestionButton);
        }
    }

    private void showError(String title, String description) {
        setState(VoiceState.ERROR);
        errorTitle.setText(title);
        errorDescription.setText(description);
    }

    @FXML
    private void submitCommand() {
        String text = transcriptionText.getText().trim();
        if (!text.isEmpty()) {
            processCommand(text);
        }
    }

    @FXML
    private void tryAgain() {
        clearTranscription();
        startListening();
    }

    @FXML
    private void cancelVoice() {
        stopListening();
        clearTranscription();
        setState(VoiceState.IDLE);
    }

    @FXML
    private void clearTranscription() {
        transcriptionText.clear();
        hideAllContainers();
    }

    @FXML
    private void executeMatchedCommand() {
        if (lastRecognizedCommand != null) {
            commandProcessor.executeCommand(lastRecognizedCommand);
            setState(VoiceState.IDLE);
            clearTranscription();
        }
    }

    @FXML
    private void retryVoiceInput() {
        setState(VoiceState.IDLE);
        startListening();
    }

    @FXML
    private void insertHint() {
        Button source = (Button) micButton.getScene().getFocusOwner();
        if (source != null && source.getUserData() != null) {
            String hint = source.getUserData().toString();
            transcriptionText.setText(hint);
        }
    }

    @FXML
    private void testMicrophone() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Microphone Test");
        alert.setHeaderText("Testing Microphone");
        alert.setContentText("Microphone test functionality would be implemented here.\n\nThis would check:\n• Microphone permissions\n• Audio input levels\n• Speech recognition availability");
        alert.showAndWait();
    }

    @FXML
    private void showVoiceHelp() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Voice Commands Help");
        alert.setHeaderText("Available Voice Commands");
        alert.setContentText(
            "📊 FORECASTING:\n" +
            "• \"Forecast sales for next 30 days\"\n" +
            "• \"Show growth trends for July\"\n" +
            "• \"Generate forecast for main course\"\n\n" +
            "🔍 SEARCH & NAVIGATION:\n" +
            "• \"Search butter chicken\"\n" +
            "• \"Open table 5 order\"\n" +
            "• \"Navigate to billing section\"\n\n" +
            "📈 REPORTS & DATA:\n" +
            "• \"Show top selling items\"\n" +
            "• \"Reset all filters\"\n" +
            "• \"Export sales report\"\n\n" +
            "💡 TIPS:\n" +
            "• Speak clearly and at normal pace\n" +
            "• Use specific dates and numbers\n" +
            "• Commands are case-insensitive"
        );
        alert.showAndWait();
    }

    // Public methods for integration
    public void setCompactMode(boolean compact) {
        if (compact) {
            voiceInputContainer.getStyleClass().add("compact-mode");
            commandHints.setVisible(false);
        } else {
            voiceInputContainer.getStyleClass().remove("compact-mode");
            commandHints.setVisible(showHintsMenuItem.isSelected());
        }
    }

    public void setVoiceCommandCallback(VoiceCommandCallback callback) {
        if (commandProcessor != null) {
            commandProcessor.setCallback(callback);
        }
    }

    public interface VoiceCommandCallback {
        void onCommandExecuted(VoiceCommand command);
        void onCommandFailed(String error);
    }
}
