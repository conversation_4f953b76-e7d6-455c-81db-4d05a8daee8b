@echo off
echo 🎵 TESTING EXISTING MP3 FILES IN SOUNDS FOLDER 🎵
echo.

echo Checking what audio files you have...
echo.

echo 📁 CONTENTS OF SOUNDS FOLDER:
if exist sounds (
    dir sounds
    echo.
) else (
    echo ❌ sounds folder does not exist
    echo Creating sounds folder...
    mkdir sounds
)

echo 🔍 CHECKING FOR SPECIFIC FILES:
echo.

if exist "sounds\swiggy-notification.mp3" (
    echo ✅ swiggy-notification.mp3 found
    set SWIGGY_EXISTS=1
) else (
    echo ❌ swiggy-notification.mp3 not found
    set SWIGGY_EXISTS=0
)

if exist "sounds\zomato-notification.mp3" (
    echo ✅ zomato-notification.mp3 found
    set ZOMATO_EXISTS=1
) else (
    echo ❌ zomato-notification.mp3 not found
    set ZOMATO_EXISTS=0
)

if exist "sounds\mixkit-urgent-simple-tone-loop-2976.mp3" (
    echo ✅ mixkit-urgent-simple-tone-loop-2976.mp3 found
    set DEFAULT_EXISTS=1
) else (
    echo ❌ mixkit-urgent-simple-tone-loop-2976.mp3 not found
    set DEFAULT_EXISTS=0
)

echo.
echo 🎯 TESTING PLAN:
echo.

if %SWIGGY_EXISTS%==1 (
    echo 🟠 SWIGGY: Will test custom swiggy-notification.mp3
) else (
    if %DEFAULT_EXISTS%==1 (
        echo 🟠 SWIGGY: Will test with default MP3 file
    ) else (
        echo 🟠 SWIGGY: Will use system beep fallback
    )
)

if %ZOMATO_EXISTS%==1 (
    echo 🔴 ZOMATO: Will test custom zomato-notification.mp3
) else (
    if %DEFAULT_EXISTS%==1 (
        echo 🔴 ZOMATO: Will test with default MP3 file
    ) else (
        echo 🔴 ZOMATO: Will use system beep fallback
    )
)

echo.
echo 🔧 CREATING MP3 TESTER...

echo package com.restaurant.util; > src\main\java\com\restaurant\util\MP3Tester.java
echo. >> src\main\java\com\restaurant\util\MP3Tester.java
echo import javafx.application.Application; >> src\main\java\com\restaurant\util\MP3Tester.java
echo import javafx.scene.media.Media; >> src\main\java\com\restaurant\util\MP3Tester.java
echo import javafx.scene.media.MediaPlayer; >> src\main\java\com\restaurant\util\MP3Tester.java
echo import javafx.stage.Stage; >> src\main\java\com\restaurant\util\MP3Tester.java
echo import java.io.File; >> src\main\java\com\restaurant\util\MP3Tester.java
echo. >> src\main\java\com\restaurant\util\MP3Tester.java
echo public class MP3Tester extends Application { >> src\main\java\com\restaurant\util\MP3Tester.java
echo. >> src\main\java\com\restaurant\util\MP3Tester.java
echo     @Override >> src\main\java\com\restaurant\util\MP3Tester.java
echo     public void start(Stage primaryStage) { >> src\main\java\com\restaurant\util\MP3Tester.java
echo         System.out.println("🎵 Testing MP3 audio files..."); >> src\main\java\com\restaurant\util\MP3Tester.java
echo         testSwiggyAudio(); >> src\main\java\com\restaurant\util\MP3Tester.java
echo     } >> src\main\java\com\restaurant\util\MP3Tester.java
echo. >> src\main\java\com\restaurant\util\MP3Tester.java
echo     private void testSwiggyAudio() { >> src\main\java\com\restaurant\util\MP3Tester.java
echo         System.out.println("🟠 Testing Swiggy audio..."); >> src\main\java\com\restaurant\util\MP3Tester.java
echo         String audioFile = findSwiggyAudio(); >> src\main\java\com\restaurant\util\MP3Tester.java
echo         if (audioFile != null) { >> src\main\java\com\restaurant\util\MP3Tester.java
echo             playAudio(audioFile, "Swiggy", this::testZomatoAudio); >> src\main\java\com\restaurant\util\MP3Tester.java
echo         } else { >> src\main\java\com\restaurant\util\MP3Tester.java
echo             System.out.println("No Swiggy audio file found, testing Zomato..."); >> src\main\java\com\restaurant\util\MP3Tester.java
echo             testZomatoAudio(); >> src\main\java\com\restaurant\util\MP3Tester.java
echo         } >> src\main\java\com\restaurant\util\MP3Tester.java
echo     } >> src\main\java\com\restaurant\util\MP3Tester.java
echo. >> src\main\java\com\restaurant\util\MP3Tester.java
echo     private void testZomatoAudio() { >> src\main\java\com\restaurant\util\MP3Tester.java
echo         System.out.println("🔴 Testing Zomato audio..."); >> src\main\java\com\restaurant\util\MP3Tester.java
echo         String audioFile = findZomatoAudio(); >> src\main\java\com\restaurant\util\MP3Tester.java
echo         if (audioFile != null) { >> src\main\java\com\restaurant\util\MP3Tester.java
echo             playAudio(audioFile, "Zomato", this::finishTest); >> src\main\java\com\restaurant\util\MP3Tester.java
echo         } else { >> src\main\java\com\restaurant\util\MP3Tester.java
echo             System.out.println("No Zomato audio file found"); >> src\main\java\com\restaurant\util\MP3Tester.java
echo             finishTest(); >> src\main\java\com\restaurant\util\MP3Tester.java
echo         } >> src\main\java\com\restaurant\util\MP3Tester.java
echo     } >> src\main\java\com\restaurant\util\MP3Tester.java
echo. >> src\main\java\com\restaurant\util\MP3Tester.java
echo     private void finishTest() { >> src\main\java\com\restaurant\util\MP3Tester.java
echo         System.out.println("🎉 MP3 audio test complete!"); >> src\main\java\com\restaurant\util\MP3Tester.java
echo         System.exit(0); >> src\main\java\com\restaurant\util\MP3Tester.java
echo     } >> src\main\java\com\restaurant\util\MP3Tester.java
echo. >> src\main\java\com\restaurant\util\MP3Tester.java
echo     private String findSwiggyAudio() { >> src\main\java\com\restaurant\util\MP3Tester.java
echo         File swiggy = new File("sounds/swiggy-notification.mp3"); >> src\main\java\com\restaurant\util\MP3Tester.java
echo         if (swiggy.exists()) { >> src\main\java\com\restaurant\util\MP3Tester.java
echo             System.out.println("Found custom Swiggy audio: " + swiggy.getAbsolutePath()); >> src\main\java\com\restaurant\util\MP3Tester.java
echo             return "sounds/swiggy-notification.mp3"; >> src\main\java\com\restaurant\util\MP3Tester.java
echo         } >> src\main\java\com\restaurant\util\MP3Tester.java
echo         File defaultFile = new File("sounds/mixkit-urgent-simple-tone-loop-2976.mp3"); >> src\main\java\com\restaurant\util\MP3Tester.java
echo         if (defaultFile.exists()) { >> src\main\java\com\restaurant\util\MP3Tester.java
echo             System.out.println("Using default audio for Swiggy: " + defaultFile.getAbsolutePath()); >> src\main\java\com\restaurant\util\MP3Tester.java
echo             return "sounds/mixkit-urgent-simple-tone-loop-2976.mp3"; >> src\main\java\com\restaurant\util\MP3Tester.java
echo         } >> src\main\java\com\restaurant\util\MP3Tester.java
echo         return null; >> src\main\java\com\restaurant\util\MP3Tester.java
echo     } >> src\main\java\com\restaurant\util\MP3Tester.java
echo. >> src\main\java\com\restaurant\util\MP3Tester.java
echo     private String findZomatoAudio() { >> src\main\java\com\restaurant\util\MP3Tester.java
echo         File zomato = new File("sounds/zomato-notification.mp3"); >> src\main\java\com\restaurant\util\MP3Tester.java
echo         if (zomato.exists()) { >> src\main\java\com\restaurant\util\MP3Tester.java
echo             System.out.println("Found custom Zomato audio: " + zomato.getAbsolutePath()); >> src\main\java\com\restaurant\util\MP3Tester.java
echo             return "sounds/zomato-notification.mp3"; >> src\main\java\com\restaurant\util\MP3Tester.java
echo         } >> src\main\java\com\restaurant\util\MP3Tester.java
echo         File defaultFile = new File("sounds/mixkit-urgent-simple-tone-loop-2976.mp3"); >> src\main\java\com\restaurant\util\MP3Tester.java
echo         if (defaultFile.exists()) { >> src\main\java\com\restaurant\util\MP3Tester.java
echo             System.out.println("Using default audio for Zomato: " + defaultFile.getAbsolutePath()); >> src\main\java\com\restaurant\util\MP3Tester.java
echo             return "sounds/mixkit-urgent-simple-tone-loop-2976.mp3"; >> src\main\java\com\restaurant\util\MP3Tester.java
echo         } >> src\main\java\com\restaurant\util\MP3Tester.java
echo         return null; >> src\main\java\com\restaurant\util\MP3Tester.java
echo     } >> src\main\java\com\restaurant\util\MP3Tester.java
echo. >> src\main\java\com\restaurant\util\MP3Tester.java
echo     private void playAudio(String fileName, String platform, Runnable onComplete) { >> src\main\java\com\restaurant\util\MP3Tester.java
echo         try { >> src\main\java\com\restaurant\util\MP3Tester.java
echo             File audioFile = new File(fileName); >> src\main\java\com\restaurant\util\MP3Tester.java
echo             Media media = new Media(audioFile.toURI().toString()); >> src\main\java\com\restaurant\util\MP3Tester.java
echo             MediaPlayer player = new MediaPlayer(media); >> src\main\java\com\restaurant\util\MP3Tester.java
echo             player.setVolume(1.0); >> src\main\java\com\restaurant\util\MP3Tester.java
echo. >> src\main\java\com\restaurant\util\MP3Tester.java
echo             player.setOnReady(() -> { >> src\main\java\com\restaurant\util\MP3Tester.java
echo                 System.out.println("🎵 Playing " + platform + " audio: " + fileName); >> src\main\java\com\restaurant\util\MP3Tester.java
echo                 player.play(); >> src\main\java\com\restaurant\util\MP3Tester.java
echo             }); >> src\main\java\com\restaurant\util\MP3Tester.java
echo. >> src\main\java\com\restaurant\util\MP3Tester.java
echo             player.setOnEndOfMedia(() -> { >> src\main\java\com\restaurant\util\MP3Tester.java
echo                 System.out.println("✅ " + platform + " audio finished"); >> src\main\java\com\restaurant\util\MP3Tester.java
echo                 player.dispose(); >> src\main\java\com\restaurant\util\MP3Tester.java
echo                 if (onComplete != null) onComplete.run(); >> src\main\java\com\restaurant\util\MP3Tester.java
echo             }); >> src\main\java\com\restaurant\util\MP3Tester.java
echo. >> src\main\java\com\restaurant\util\MP3Tester.java
echo             player.setOnError(() -> { >> src\main\java\com\restaurant\util\MP3Tester.java
echo                 System.err.println("❌ Error playing " + platform + " audio: " + player.getError()); >> src\main\java\com\restaurant\util\MP3Tester.java
echo                 if (onComplete != null) onComplete.run(); >> src\main\java\com\restaurant\util\MP3Tester.java
echo             }); >> src\main\java\com\restaurant\util\MP3Tester.java
echo. >> src\main\java\com\restaurant\util\MP3Tester.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\util\MP3Tester.java
echo             System.err.println("❌ Exception playing " + platform + " audio: " + e.getMessage()); >> src\main\java\com\restaurant\util\MP3Tester.java
echo             if (onComplete != null) onComplete.run(); >> src\main\java\com\restaurant\util\MP3Tester.java
echo         } >> src\main\java\com\restaurant\util\MP3Tester.java
echo     } >> src\main\java\com\restaurant\util\MP3Tester.java
echo. >> src\main\java\com\restaurant\util\MP3Tester.java
echo     public static void main(String[] args) { >> src\main\java\com\restaurant\util\MP3Tester.java
echo         launch(args); >> src\main\java\com\restaurant\util\MP3Tester.java
echo     } >> src\main\java\com\restaurant\util\MP3Tester.java
echo } >> src\main\java\com\restaurant\util\MP3Tester.java

echo ✅ MP3Tester created
echo.

echo 🔧 COMPILING MP3 TESTER...
mvn compile -q

if %ERRORLEVEL% neq 0 (
    echo ❌ Compilation failed
    pause
    exit /b 1
)

echo ✅ Compilation successful
echo.

echo 🎵 TESTING MP3 AUDIO FILES...
echo.

java -Dprism.order=sw ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2-win.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics,javafx.media ^
     -cp "target/classes" ^
     com.restaurant.util.MP3Tester

echo.
echo 🎉 MP3 AUDIO TEST COMPLETE!
echo.

pause
