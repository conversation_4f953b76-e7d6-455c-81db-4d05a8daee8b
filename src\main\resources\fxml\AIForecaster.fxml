<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.chart.LineChart?>
<?import javafx.scene.chart.NumberAxis?>
<?import javafx.scene.chart.CategoryAxis?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.AIForecasterController">
   <top>
      <VBox styleClass="forecaster-header">
         <padding>
            <Insets bottom="20.0" left="30.0" right="30.0" top="20.0" />
         </padding>
         <children>
            <HBox alignment="CENTER_LEFT" spacing="15.0">
               <children>
                  <Label text="📊" styleClass="header-icon" />
                  <Text styleClass="page-title" text="AI Sales Forecaster" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="helpButton" styleClass="help-button" text="❓ Help">
                     <tooltip>
                        <Tooltip text="Get help with AI Forecaster" />
                     </tooltip>
                  </Button>
               </children>
            </HBox>
            <Text styleClass="page-subtitle" text="Predict future sales trends based on historical data and AI analytics" />
         </children>
      </VBox>
   </top>
   
   <center>
      <ScrollPane fitToWidth="true" styleClass="forecaster-content">
         <content>
            <VBox spacing="25.0">
               <padding>
                  <Insets bottom="30.0" left="30.0" right="30.0" top="10.0" />
               </padding>
               <children>
                  <!-- Input Section -->
                  <VBox styleClass="input-section">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label text="⚙️" styleClass="section-icon" />
                              <Text styleClass="section-title" text="Forecast Parameters" />
                           </children>
                        </HBox>
                        
                        <GridPane hgap="20.0" vgap="15.0" styleClass="parameter-grid">
                           <columnConstraints>
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="200.0" />
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="200.0" />
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="200.0" />
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" />
                           </columnConstraints>
                           <rowConstraints>
                              <RowConstraints />
                              <RowConstraints />
                           </rowConstraints>
                           <children>
                              <!-- Date Range -->
                              <VBox spacing="5.0" GridPane.columnIndex="0" GridPane.rowIndex="0">
                                 <children>
                                    <Label styleClass="input-label" text="Start Date" />
                                    <DatePicker fx:id="startDatePicker" styleClass="date-picker" />
                                 </children>
                              </VBox>
                              
                              <VBox spacing="5.0" GridPane.columnIndex="1" GridPane.rowIndex="0">
                                 <children>
                                    <Label styleClass="input-label" text="End Date" />
                                    <DatePicker fx:id="endDatePicker" styleClass="date-picker" />
                                 </children>
                              </VBox>
                              
                              <!-- Category Selection -->
                              <VBox spacing="5.0" GridPane.columnIndex="2" GridPane.rowIndex="0">
                                 <children>
                                    <Label styleClass="input-label" text="Category" />
                                    <ComboBox fx:id="categoryComboBox" prefWidth="200.0" promptText="Select category" styleClass="combo-box" />
                                 </children>
                              </VBox>
                              
                              <!-- Forecast Period -->
                              <VBox spacing="5.0" GridPane.columnIndex="3" GridPane.rowIndex="0">
                                 <children>
                                    <Label styleClass="input-label" text="Forecast Period" />
                                    <ComboBox fx:id="forecastPeriodComboBox" prefWidth="150.0" styleClass="combo-box" />
                                 </children>
                              </VBox>
                              
                              <!-- Sales Channel -->
                              <VBox spacing="5.0" GridPane.columnIndex="0" GridPane.rowIndex="1">
                                 <children>
                                    <Label styleClass="input-label" text="Sales Channel" />
                                    <ComboBox fx:id="salesChannelComboBox" prefWidth="200.0" styleClass="combo-box" />
                                 </children>
                              </VBox>
                              
                              <!-- Location Filter -->
                              <VBox spacing="5.0" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                 <children>
                                    <Label styleClass="input-label" text="Location" />
                                    <ComboBox fx:id="locationComboBox" prefWidth="200.0" styleClass="combo-box" />
                                 </children>
                              </VBox>
                              
                              <!-- Action Buttons -->
                              <HBox alignment="CENTER_LEFT" spacing="15.0" GridPane.columnIndex="2" GridPane.columnSpan="2" GridPane.rowIndex="1">
                                 <children>
                                    <Button fx:id="generateForecastButton" onAction="#handleGenerateForecast" styleClass="primary-button" text="🔮 Generate Forecast" />
                                    <Button fx:id="resetButton" onAction="#handleReset" styleClass="secondary-button" text="🔄 Reset" />
                                 </children>
                              </HBox>
                           </children>
                           <padding>
                              <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                           </padding>
                        </GridPane>
                     </children>
                  </VBox>
                  
                  <!-- Key Metrics Cards -->
                  <HBox fx:id="metricsContainer" spacing="20.0" styleClass="metrics-container" visible="false">
                     <children>
                        <VBox styleClass="metric-card">
                           <children>
                              <HBox alignment="CENTER_LEFT" spacing="10.0">
                                 <children>
                                    <Label text="💰" styleClass="metric-icon-positive" />
                                    <Text styleClass="metric-label" text="Predicted Sales" />
                                 </children>
                              </HBox>
                              <Text fx:id="predictedSalesText" styleClass="metric-value" text="₹0" />
                              <Text fx:id="salesGrowthText" styleClass="metric-change" text="+0%" />
                           </children>
                        </VBox>
                        
                        <VBox styleClass="metric-card">
                           <children>
                              <HBox alignment="CENTER_LEFT" spacing="10.0">
                                 <children>
                                    <Label text="📈" styleClass="metric-icon-positive" />
                                    <Text styleClass="metric-label" text="Growth Rate" />
                                 </children>
                              </HBox>
                              <Text fx:id="growthRateText" styleClass="metric-value" text="0%" />
                              <Text fx:id="growthTrendText" styleClass="metric-change" text="vs last period" />
                           </children>
                        </VBox>

                        <VBox styleClass="metric-card">
                           <children>
                              <HBox alignment="CENTER_LEFT" spacing="10.0">
                                 <children>
                                    <Label text="⭐" styleClass="metric-icon-neutral" />
                                    <Text styleClass="metric-label" text="Top Category" />
                                 </children>
                              </HBox>
                              <Text fx:id="topCategoryText" styleClass="metric-value" text="N/A" />
                              <Text fx:id="topCategoryPercentText" styleClass="metric-change" text="0% of sales" />
                           </children>
                        </VBox>

                        <VBox styleClass="metric-card">
                           <children>
                              <HBox alignment="CENTER_LEFT" spacing="10.0">
                                 <children>
                                    <Label text="🛡️" styleClass="metric-icon-neutral" />
                                    <Text styleClass="metric-label" text="Confidence" />
                                 </children>
                              </HBox>
                              <Text fx:id="confidenceText" styleClass="metric-value" text="0%" />
                              <Text fx:id="confidenceDescText" styleClass="metric-change" text="prediction accuracy" />
                           </children>
                        </VBox>
                     </children>
                  </HBox>
                  
                  <!-- Chart Section -->
                  <VBox fx:id="chartContainer" styleClass="chart-section" visible="false">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="15.0">
                           <children>
                              <Label text="📊" styleClass="section-icon" />
                              <Text styleClass="section-title" text="Sales Forecast Chart" />
                              <Region HBox.hgrow="ALWAYS" />

                              <!-- View Toggle -->
                              <ToggleButton fx:id="dailyViewButton" styleClass="view-toggle" text="Daily" />
                              <ToggleButton fx:id="weeklyViewButton" styleClass="view-toggle" text="Weekly" />
                              <ToggleButton fx:id="monthlyViewButton" styleClass="view-toggle" text="Monthly" />

                              <!-- Export Options -->
                              <MenuButton styleClass="export-button" text="📥 Export">
                                 <items>
                                    <MenuItem fx:id="exportCsvMenuItem" onAction="#handleExportCsv" text="Export as CSV" />
                                    <MenuItem fx:id="exportPdfMenuItem" onAction="#handleExportPdf" text="Export as PDF" />
                                    <MenuItem fx:id="exportImageMenuItem" onAction="#handleExportImage" text="Export as Image" />
                                 </items>
                              </MenuButton>
                           </children>
                        </HBox>
                        
                        <LineChart fx:id="forecastChart" styleClass="forecast-chart" prefHeight="400.0">
                           <xAxis>
                              <CategoryAxis fx:id="xAxis" label="Date" />
                           </xAxis>
                           <yAxis>
                              <NumberAxis fx:id="yAxis" label="Sales (₹)" />
                           </yAxis>
                        </LineChart>
                        
                        <!-- Chart Legend -->
                        <HBox alignment="CENTER" spacing="30.0" styleClass="chart-legend">
                           <children>
                              <HBox alignment="CENTER_LEFT" spacing="8.0">
                                 <children>
                                    <Region styleClass="legend-color-historical" />
                                    <Text styleClass="legend-text" text="Historical Data" />
                                 </children>
                              </HBox>
                              <HBox alignment="CENTER_LEFT" spacing="8.0">
                                 <children>
                                    <Region styleClass="legend-color-forecast" />
                                    <Text styleClass="legend-text" text="Forecast" />
                                 </children>
                              </HBox>
                              <HBox alignment="CENTER_LEFT" spacing="8.0">
                                 <children>
                                    <Region styleClass="legend-color-confidence" />
                                    <Text styleClass="legend-text" text="Confidence Range" />
                                 </children>
                              </HBox>
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Loading Indicator -->
                  <VBox fx:id="loadingContainer" alignment="CENTER" spacing="15.0" styleClass="loading-container" visible="false">
                     <children>
                        <ProgressIndicator fx:id="loadingIndicator" />
                        <Text styleClass="loading-text" text="Generating AI forecast..." />
                        <Text styleClass="loading-subtext" text="Analyzing historical data and market trends" />
                     </children>
                  </VBox>
                  
                  <!-- Empty State -->
                  <VBox fx:id="emptyStateContainer" alignment="CENTER" spacing="20.0" styleClass="empty-state" visible="true">
                     <children>
                        <Label text="📈" styleClass="empty-state-icon" style="-fx-font-size: 64px;" />
                        <Text styleClass="empty-state-title" text="Ready to Generate Forecast" />
                        <Text styleClass="empty-state-subtitle" text="Select your parameters above and click 'Generate Forecast' to see AI-powered predictions" />
                        <Button onAction="#handleGenerateForecast" styleClass="primary-button" text="🔮 Get Started" />
                     </children>
                  </VBox>
               </children>
            </VBox>
         </content>
      </ScrollPane>
   </center>
</BorderPane>
