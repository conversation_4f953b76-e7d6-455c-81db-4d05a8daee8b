package com.restaurant.model;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class InventoryDAO {
    
    /**
     * Get all inventory items
     */
    public static List<InventoryItem> getAllInventoryItems() {
        List<InventoryItem> items = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(
                     "SELECT i.*, s.name as supplier_name " +
                     "FROM inventory_items i " +
                     "LEFT JOIN suppliers s ON i.supplier_id = s.id " +
                     "ORDER BY i.name")) {
            
            while (rs.next()) {
                InventoryItem item = new InventoryItem(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getDouble("quantity"),
                    rs.getString("unit"),
                    rs.getString("status")
                );
                item.setCategory(rs.getString("category"));
                item.setMinThreshold(rs.getDouble("min_threshold"));
                item.setSupplier(rs.getString("supplier_name"));
                item.setLastUpdated(rs.getTimestamp("last_updated").toLocalDateTime());
                items.add(item);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return items;
    }
    
    /**
     * Get inventory item by ID
     */
    public static InventoryItem getInventoryItemById(int id) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT i.*, s.name as supplier_name " +
                     "FROM inventory_items i " +
                     "LEFT JOIN suppliers s ON i.supplier_id = s.id " +
                     "WHERE i.id = ?")) {
            
            ps.setInt(1, id);
            ResultSet rs = ps.executeQuery();
            
            if (rs.next()) {
                InventoryItem item = new InventoryItem(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getDouble("quantity"),
                    rs.getString("unit"),
                    rs.getString("status")
                );
                item.setCategory(rs.getString("category"));
                item.setMinThreshold(rs.getDouble("min_threshold"));
                item.setSupplier(rs.getString("supplier_name"));
                item.setLastUpdated(rs.getTimestamp("last_updated").toLocalDateTime());
                return item;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * Add new inventory item
     */
    public static boolean addInventoryItem(InventoryItem item) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "INSERT INTO inventory_items (name, quantity, unit, category, min_threshold, supplier_id, status, last_updated) " +
                     "VALUES (?, ?, ?, ?, ?, ?, ?, ?)", Statement.RETURN_GENERATED_KEYS)) {
            
            ps.setString(1, item.getName());
            ps.setDouble(2, item.getQuantity());
            ps.setString(3, item.getUnit());
            ps.setString(4, item.getCategory());
            ps.setDouble(5, item.getMinThreshold());
            ps.setInt(6, getSupplierIdByName(item.getSupplier()));
            ps.setString(7, item.getStatus());
            ps.setTimestamp(8, Timestamp.valueOf(LocalDateTime.now()));
            
            int result = ps.executeUpdate();
            
            if (result > 0) {
                ResultSet rs = ps.getGeneratedKeys();
                if (rs.next()) {
                    item.setId(rs.getInt(1));
                }
                return true;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Update inventory item
     */
    public static boolean updateInventoryItem(InventoryItem item) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "UPDATE inventory_items SET name = ?, quantity = ?, unit = ?, category = ?, " +
                     "min_threshold = ?, supplier_id = ?, status = ?, last_updated = ? WHERE id = ?")) {
            
            ps.setString(1, item.getName());
            ps.setDouble(2, item.getQuantity());
            ps.setString(3, item.getUnit());
            ps.setString(4, item.getCategory());
            ps.setDouble(5, item.getMinThreshold());
            ps.setInt(6, getSupplierIdByName(item.getSupplier()));
            ps.setString(7, item.getStatus());
            ps.setTimestamp(8, Timestamp.valueOf(LocalDateTime.now()));
            ps.setInt(9, item.getId());
            
            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Delete inventory item
     */
    public static boolean deleteInventoryItem(int id) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("DELETE FROM inventory_items WHERE id = ?")) {
            
            ps.setInt(1, id);
            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Update inventory quantity
     */
    public static boolean updateInventoryQuantity(int id, double newQuantity) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "UPDATE inventory_items SET quantity = ?, last_updated = ? WHERE id = ?")) {
            
            ps.setDouble(1, newQuantity);
            ps.setTimestamp(2, Timestamp.valueOf(LocalDateTime.now()));
            ps.setInt(3, id);
            
            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Get low stock items
     */
    public static List<InventoryItem> getLowStockItems() {
        List<InventoryItem> items = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(
                     "SELECT i.*, s.name as supplier_name " +
                     "FROM inventory_items i " +
                     "LEFT JOIN suppliers s ON i.supplier_id = s.id " +
                     "WHERE i.quantity <= i.min_threshold " +
                     "ORDER BY i.quantity ASC")) {
            
            while (rs.next()) {
                InventoryItem item = new InventoryItem(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getDouble("quantity"),
                    rs.getString("unit"),
                    rs.getString("status")
                );
                item.setCategory(rs.getString("category"));
                item.setMinThreshold(rs.getDouble("min_threshold"));
                item.setSupplier(rs.getString("supplier_name"));
                item.setLastUpdated(rs.getTimestamp("last_updated").toLocalDateTime());
                items.add(item);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return items;
    }
    
    /**
     * Search inventory items by name
     */
    public static List<InventoryItem> searchInventoryItems(String searchTerm) {
        List<InventoryItem> items = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT i.*, s.name as supplier_name " +
                     "FROM inventory_items i " +
                     "LEFT JOIN suppliers s ON i.supplier_id = s.id " +
                     "WHERE i.name LIKE ? OR i.category LIKE ? " +
                     "ORDER BY i.name")) {
            
            String searchPattern = "%" + searchTerm + "%";
            ps.setString(1, searchPattern);
            ps.setString(2, searchPattern);
            
            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                InventoryItem item = new InventoryItem(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getDouble("quantity"),
                    rs.getString("unit"),
                    rs.getString("status")
                );
                item.setCategory(rs.getString("category"));
                item.setMinThreshold(rs.getDouble("min_threshold"));
                item.setSupplier(rs.getString("supplier_name"));
                item.setLastUpdated(rs.getTimestamp("last_updated").toLocalDateTime());
                items.add(item);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return items;
    }
    
    /**
     * Get inventory items by category
     */
    public static List<InventoryItem> getInventoryItemsByCategory(String category) {
        List<InventoryItem> items = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT i.*, s.name as supplier_name " +
                     "FROM inventory_items i " +
                     "LEFT JOIN suppliers s ON i.supplier_id = s.id " +
                     "WHERE i.category = ? " +
                     "ORDER BY i.name")) {
            
            ps.setString(1, category);
            ResultSet rs = ps.executeQuery();
            
            while (rs.next()) {
                InventoryItem item = new InventoryItem(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getDouble("quantity"),
                    rs.getString("unit"),
                    rs.getString("status")
                );
                item.setCategory(rs.getString("category"));
                item.setMinThreshold(rs.getDouble("min_threshold"));
                item.setSupplier(rs.getString("supplier_name"));
                item.setLastUpdated(rs.getTimestamp("last_updated").toLocalDateTime());
                items.add(item);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return items;
    }
    
    /**
     * Helper method to get supplier ID by name
     */
    private static int getSupplierIdByName(String supplierName) {
        if (supplierName == null || supplierName.trim().isEmpty()) {
            return 1; // Default supplier
        }
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("SELECT id FROM suppliers WHERE name = ?")) {
            
            ps.setString(1, supplierName);
            ResultSet rs = ps.executeQuery();
            
            if (rs.next()) {
                return rs.getInt("id");
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return 1; // Default supplier if not found
    }
}
