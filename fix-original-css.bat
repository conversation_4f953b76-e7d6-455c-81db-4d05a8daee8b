@echo off
echo Restoring Your Original UI with Fixed CSS Syntax...
echo.

echo WHAT I'M DOING:
echo 1. Restoring your original CSS file (all your styling)
echo 2. Fixing ONLY the syntax errors (adding missing 'px' units)
echo 3. Keeping ALL your original visual design intact
echo.

echo Step 1: Restoring original CSS file...
copy "src\main\resources\css\application-original-backup.css" "src\main\resources\css\application.css"

if %ERRORLEVEL% neq 0 (
    echo Failed to restore original CSS!
    pause
    exit /b 1
)

echo ✅ Original CSS restored successfully!
echo.

echo Step 2: Fixing syntax errors in original CSS...
echo (This will add 'px' units to radius values to prevent ClassCastException)

powershell -Command ^
"$content = Get-Content 'src\main\resources\css\application.css' -Raw; ^
$content = $content -replace '-fx-background-radius: ([0-9]+);', '-fx-background-radius: ${1}px;'; ^
$content = $content -replace '-fx-border-radius: ([0-9]+);', '-fx-border-radius: ${1}px;'; ^
$content = $content -replace '-fx-background-radius: ([0-9]+) ([0-9]+) ([0-9]+) ([0-9]+);', '-fx-background-radius: ${1}px ${2}px ${3}px ${4}px;'; ^
$content = $content -replace '-fx-border-radius: ([0-9]+) ([0-9]+) ([0-9]+) ([0-9]+);', '-fx-border-radius: ${1}px ${2}px ${3}px ${4}px;'; ^
Set-Content 'src\main\resources\css\application.css' $content"

if %ERRORLEVEL% neq 0 (
    echo Failed to fix CSS syntax!
    pause
    exit /b 1
)

echo ✅ CSS syntax errors fixed!
echo.

echo Step 3: Compiling with your original UI (fixed)...
call mvn clean compile -q

if %ERRORLEVEL% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo ✅ Compilation successful!
echo.

echo RESULT:
echo ✅ Your original UI design is restored
echo ✅ All your custom styling is preserved  
echo ✅ Only syntax errors are fixed (added 'px' units)
echo ✅ No visual changes to your interface
echo ✅ ClassCastException errors eliminated
echo.

echo Your application now has:
echo - Your original beautiful UI design
echo - All your custom colors, fonts, and styling
echo - Fixed CSS syntax to prevent crashes
echo - Same visual appearance as before
echo.

echo Ready to test! The UI should look exactly like your original design.
echo.
pause
