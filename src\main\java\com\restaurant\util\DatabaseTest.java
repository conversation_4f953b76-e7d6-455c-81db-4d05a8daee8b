package com.restaurant.util;

import com.restaurant.model.DatabaseManager;
import com.restaurant.model.User;
import com.restaurant.model.UserDAO;
import org.mindrot.jbcrypt.BCrypt;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class DatabaseTest {
    
    public static void main(String[] args) {
        System.out.println("=== Database Test ===");
        
        try {
            // Test database connection
            Connection conn = DatabaseManager.getConnection();
            System.out.println("✓ Database connection successful");
            
            // Check if users table exists and has data
            PreparedStatement ps = conn.prepareStatement("SELECT COUNT(*) FROM users");
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                int userCount = rs.getInt(1);
                System.out.println("✓ Users table exists with " + userCount + " users");
            }
            
            // List all users
            ps = conn.prepareStatement("SELECT username, role FROM users");
            rs = ps.executeQuery();
            System.out.println("\n=== Existing Users ===");
            while (rs.next()) {
                System.out.println("Username: " + rs.getString("username") + ", Role: " + rs.getString("role"));
            }
            
            // Test authentication with admin/admin123
            System.out.println("\n=== Testing Authentication ===");
            User user = UserDAO.authenticate("admin", "admin123");
            if (user != null) {
                System.out.println("✓ Admin authentication successful: " + user.getUsername() + " (" + user.getRole() + ")");
            } else {
                System.out.println("✗ Admin authentication failed");
                
                // Check the stored password hash
                ps = conn.prepareStatement("SELECT password_hash FROM users WHERE username = ?");
                ps.setString(1, "admin");
                rs = ps.executeQuery();
                if (rs.next()) {
                    String storedHash = rs.getString("password_hash");
                    System.out.println("Stored hash: " + storedHash);
                    
                    // Test if the password matches
                    boolean matches = BCrypt.checkpw("admin123", storedHash);
                    System.out.println("Password matches: " + matches);
                }
            }
            
            // Test staff authentication
            user = UserDAO.authenticate("staff", "staff123");
            if (user != null) {
                System.out.println("✓ Staff authentication successful: " + user.getUsername() + " (" + user.getRole() + ")");
            } else {
                System.out.println("✗ Staff authentication failed");
            }
            
        } catch (SQLException e) {
            System.out.println("✗ Database error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
