<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.chart.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.EnhancedReportsController">
   <top>
      <!-- Navigation and Title Bar -->
      <VBox>
         <children>
            <!-- Title Bar -->
            <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-background-color: #2c3e50; -fx-padding: 15;">
               <children>
                  <Label text="📊 Enhanced Reports &amp; Analytics" textFill="WHITE">
                     <font>
                        <Font name="System Bold" size="20.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button mnemonicParsing="false" style="-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-weight: bold;" text="🏠 Home" />
                  <Button mnemonicParsing="false" style="-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-weight: bold;" text="🔔 Notifications" />
               </children>
            </HBox>
            
            <!-- Filter Controls -->
            <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: #ecf0f1; -fx-padding: 15;">
               <children>
                  <!-- Date Range -->
                  <VBox spacing="5.0">
                     <children>
                        <Label text="📅 Date Range:" style="-fx-font-weight: bold;" />
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <children>
                              <Label text="From:" />
                              <DatePicker fx:id="startDatePicker" prefWidth="130.0" />
                              <Label text="To:" />
                              <DatePicker fx:id="endDatePicker" prefWidth="130.0" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Report Type -->
                  <VBox spacing="5.0">
                     <children>
                        <Label text="📊 Report Type:" style="-fx-font-weight: bold;" />
                        <ComboBox fx:id="reportTypeComboBox" prefWidth="120.0" />
                     </children>
                  </VBox>
                  
                  <!-- Order Types -->
                  <VBox spacing="5.0">
                     <children>
                        <Label text="🛒 Order Types:" style="-fx-font-weight: bold;" />
                        <HBox spacing="10.0">
                           <children>
                              <CheckBox fx:id="allOrderTypesCheckBox" text="All" style="-fx-font-weight: bold;" />
                              <CheckBox fx:id="swiggyCheckBox" text="🟠 Swiggy" />
                              <CheckBox fx:id="zomatoCheckBox" text="🔴 Zomato" />
                              <CheckBox fx:id="onlineCheckBox" text="💻 Online" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Action Buttons -->
                  <VBox spacing="5.0">
                     <children>
                        <Label text="⚡ Actions:" style="-fx-font-weight: bold;" />
                        <HBox spacing="10.0">
                           <children>
                              <Button fx:id="generateReportBtn" style="-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-weight: bold;" text="📊 Generate Report" />
                              <Button fx:id="exportReportBtn" style="-fx-background-color: #f39c12; -fx-text-fill: white; -fx-font-weight: bold;" text="📤 Export" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
               </children>
            </HBox>
         </children>
      </VBox>
   </top>
   
   <center>
      <ScrollPane fitToWidth="true">
         <content>
            <VBox spacing="20.0">
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>
               <children>
                  <!-- Summary Cards Row -->
                  <HBox spacing="15.0">
                     <children>
                        <!-- Total Orders Card -->
                        <VBox alignment="CENTER" spacing="8.0" style="-fx-background-color: #3498db; -fx-background-radius: 10; -fx-padding: 15;" HBox.hgrow="ALWAYS">
                           <children>
                              <Label text="📦 Total Orders" textFill="WHITE">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label fx:id="totalOrdersLabel" text="0" textFill="WHITE">
                                 <font>
                                    <Font name="System Bold" size="24.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>
                        
                        <!-- Total Revenue Card -->
                        <VBox alignment="CENTER" spacing="8.0" style="-fx-background-color: #2ecc71; -fx-background-radius: 10; -fx-padding: 15;" HBox.hgrow="ALWAYS">
                           <children>
                              <Label text="💰 Total Revenue" textFill="WHITE">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label fx:id="totalRevenueLabel" text="₹0.00" textFill="WHITE">
                                 <font>
                                    <Font name="System Bold" size="20.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>
                        
                        <!-- Average Order Value Card -->
                        <VBox alignment="CENTER" spacing="8.0" style="-fx-background-color: #e74c3c; -fx-background-radius: 10; -fx-padding: 15;" HBox.hgrow="ALWAYS">
                           <children>
                              <Label text="📊 Avg Order Value" textFill="WHITE">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label fx:id="avgOrderValueLabel" text="₹0.00" textFill="WHITE">
                                 <font>
                                    <Font name="System Bold" size="20.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>
                        
                        <!-- Peak Hour Card -->
                        <VBox alignment="CENTER" spacing="8.0" style="-fx-background-color: #9b59b6; -fx-background-radius: 10; -fx-padding: 15;" HBox.hgrow="ALWAYS">
                           <children>
                              <Label text="⏰ Peak Hour" textFill="WHITE">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label fx:id="peakHourLabel" text="No data" textFill="WHITE">
                                 <font>
                                    <Font name="System Bold" size="16.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>
                     </children>
                  </HBox>
                  
                  <!-- Platform Breakdown Row -->
                  <HBox spacing="15.0">
                     <children>
                        <!-- Swiggy Stats -->
                        <VBox alignment="CENTER" spacing="8.0" style="-fx-background-color: #ff6b35; -fx-background-radius: 10; -fx-padding: 15;" HBox.hgrow="ALWAYS">
                           <children>
                              <Label text="🟠 Swiggy Orders" textFill="WHITE">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label fx:id="swiggyOrdersLabel" text="0" textFill="WHITE">
                                 <font>
                                    <Font name="System Bold" size="22.0" />
                                 </font>
                              </Label>
                              <Label fx:id="swiggyRevenueLabel" text="₹0.00" textFill="WHITE">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>
                        
                        <!-- Zomato Stats -->
                        <VBox alignment="CENTER" spacing="8.0" style="-fx-background-color: #e23744; -fx-background-radius: 10; -fx-padding: 15;" HBox.hgrow="ALWAYS">
                           <children>
                              <Label text="🔴 Zomato Orders" textFill="WHITE">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label fx:id="zomatoOrdersLabel" text="0" textFill="WHITE">
                                 <font>
                                    <Font name="System Bold" size="22.0" />
                                 </font>
                              </Label>
                              <Label fx:id="zomatoRevenueLabel" text="₹0.00" textFill="WHITE">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>
                     </children>
                  </HBox>
                  
                  <!-- Charts Row -->
                  <HBox spacing="20.0">
                     <children>
                        <!-- Order Distribution Pie Chart -->
                        <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                           <children>
                              <Label text="📊 Order Distribution by Platform" style="-fx-font-weight: bold; -fx-font-size: 16px;" />
                              <PieChart fx:id="orderTypePieChart" prefHeight="300.0" />
                           </children>
                        </VBox>
                        
                        <!-- Revenue Trend Line Chart -->
                        <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                           <children>
                              <Label text="📈 Revenue Trend" style="-fx-font-weight: bold; -fx-font-size: 16px;" />
                              <LineChart fx:id="revenueTrendChart" prefHeight="300.0">
                                 <xAxis>
                                    <CategoryAxis side="BOTTOM" />
                                 </xAxis>
                                 <yAxis>
                                    <NumberAxis side="LEFT" />
                                 </yAxis>
                              </LineChart>
                           </children>
                        </VBox>
                     </children>
                  </HBox>
                  
                  <!-- Platform Comparison Bar Chart -->
                  <VBox spacing="10.0">
                     <children>
                        <Label text="📊 Platform Comparison" style="-fx-font-weight: bold; -fx-font-size: 16px;" />
                        <BarChart fx:id="platformComparisonChart" prefHeight="250.0">
                           <xAxis>
                              <CategoryAxis side="BOTTOM" />
                           </xAxis>
                           <yAxis>
                              <NumberAxis side="LEFT" />
                           </yAxis>
                        </BarChart>
                     </children>
                  </VBox>
                  
                  <!-- Data Table -->
                  <VBox spacing="10.0">
                     <children>
                        <Label text="📋 Detailed Report Data" style="-fx-font-weight: bold; -fx-font-size: 16px;" />
                        <TableView fx:id="reportsTable" prefHeight="400.0">
                           <columns>
                              <TableColumn fx:id="dateColumn" prefWidth="120.0" text="Date/Period" />
                              <TableColumn fx:id="ordersColumn" prefWidth="100.0" text="Orders" />
                              <TableColumn fx:id="revenueColumn" prefWidth="120.0" text="Revenue" />
                              <TableColumn fx:id="swiggyColumn" prefWidth="100.0" text="Swiggy" />
                              <TableColumn fx:id="zomatoColumn" prefWidth="100.0" text="Zomato" />
                              <TableColumn fx:id="avgValueColumn" prefWidth="120.0" text="Avg Value" />
                           </columns>
                        </TableView>
                     </children>
                  </VBox>
               </children>
            </VBox>
         </content>
      </ScrollPane>
   </center>
</BorderPane>
