<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane fx:id="mainContainer" xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.DashboardController" stylesheets="@../css/application.css">
   <top>
      <VBox styleClass="header">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="top-bar">
               <children>
                  <Label styleClass="app-title" text="WOK KA TADKA - Admin Dashboard">
                     <font>
                        <Font name="System Bold" size="18.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />

                  <!-- Home and Notifications buttons -->
                  <Button fx:id="homeBtn" mnemonicParsing="false" onAction="#goToHome" styleClass="header-icon-button" text="Home">
                     <tooltip>
                        <Tooltip text="Go to Dashboard Home" />
                     </tooltip>
                  </Button>
                  <Button fx:id="notificationsBtn" mnemonicParsing="false" onAction="#showNotifications" styleClass="header-icon-button" text="Alerts">
                     <tooltip>
                        <Tooltip text="View Notifications" />
                     </tooltip>
                  </Button>

                  <Label fx:id="userLabel" styleClass="user-info" text="Logged in as: User">
                     <font>
                        <Font size="12.0" />
                     </font>
                  </Label>
                  <Button mnemonicParsing="false" onAction="#logout" styleClass="logout-button" text="Logout" />
               </children>
               <padding>
                  <Insets bottom="12.0" left="20.0" right="20.0" top="12.0" />
               </padding>
            </HBox>
            
            <HBox alignment="CENTER_LEFT" spacing="8.0" styleClass="navigation-bar">
               <children>
                  <Button mnemonicParsing="false" onAction="#loadDashboardHome" styleClass="nav-button, nav-button-active" text="🏠 Dashboard" />
                  <Button mnemonicParsing="false" onAction="#loadUserManagement" styleClass="nav-button" text="🧑‍💼 Users" />
                  <Button mnemonicParsing="false" onAction="#loadOrderManagement" styleClass="nav-button" text="📋 Orders" />
                  <Button fx:id="finishListBtn" mnemonicParsing="false" onAction="#loadFinishList" styleClass="nav-button" text="🍽️ Finish List" />
                  <Button fx:id="menuManagementBtn" mnemonicParsing="false" onAction="#loadMenuManagement" styleClass="nav-button" text="🍽️ Menu" />
                  <Button mnemonicParsing="false" onAction="#loadBillingKOT" styleClass="nav-button" text="🧾 Billing" />
                  <Button mnemonicParsing="false" onAction="#loadReports" styleClass="nav-button" text="📊 Reports" />
                  <Button mnemonicParsing="false" onAction="#loadSettings" styleClass="nav-button" text="🛠️ Settings" />
                  <Button fx:id="aiForecasterBtn" mnemonicParsing="false" onAction="#openAIForecaster" styleClass="nav-button ai-button" text="🤖 AI Forecaster">
                     <tooltip>
                        <Tooltip text="AI Sales &amp; Demand Forecasting" />
                     </tooltip>
                  </Button>
                  <Button fx:id="smartAssistantBtn" mnemonicParsing="false" onAction="#openSmartAIAssistant" styleClass="nav-button ai-button smart-assistant-nav" text="🤖 Smart Assistant">
                     <tooltip>
                        <Tooltip text="Smart AI Assistant - Chat with AI for help, reports, and insights" />
                     </tooltip>
                  </Button>

                  <!-- Separator for navigation buttons -->
                  <Region prefWidth="20.0" />

                  <!-- Home and Notifications buttons in navigation bar -->
                  <Button fx:id="navHomeBtn" mnemonicParsing="false" onAction="#goToHome" styleClass="nav-icon-button" text="🏠">
                     <tooltip>
                        <Tooltip text="Quick Home" />
                     </tooltip>
                  </Button>
                  <Button fx:id="navNotificationsBtn" mnemonicParsing="false" onAction="#showNotifications" styleClass="nav-icon-button" text="🔔">
                     <tooltip>
                        <Tooltip text="Notifications" />
                     </tooltip>
                  </Button>
               </children>
               <padding>
                  <Insets bottom="10.0" left="20.0" right="20.0" top="10.0" />
               </padding>
            </HBox>
         </children>
      </VBox>
   </top>
   
   <center>
      <StackPane fx:id="contentArea" styleClass="content-area">
         <children>
            <!-- Dashboard Home Content -->
            <ScrollPane fx:id="dashboardHome" styleClass="universal-scroll-pane" fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="ALWAYS">
               <content>
                  <VBox spacing="15.0" styleClass="dashboard-content">
                     <children>
                        <!-- Welcome Section -->
                        <VBox spacing="8.0" styleClass="welcome-section">
                           <children>
                              <Label styleClass="welcome-title" text="Welcome to WOK KA TADKA">
                                 <font>
                                    <Font name="System Bold" size="22.0" />
                                 </font>
                              </Label>
                              <Label styleClass="welcome-subtitle" text="Complete Restaurant Management Solution">
                                 <font>
                                    <Font size="14.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>

                        <!-- Quick Stats Cards -->
                        <HBox spacing="15.0" styleClass="stats-container">
                           <children>
                              <VBox styleClass="stat-card">
                                 <children>
                                    <Label styleClass="stat-icon" text="📊" />
                                    <Label fx:id="todayOrdersLabel" styleClass="stat-value" text="0">
                                       <font>
                                          <Font name="System Bold" size="20.0" />
                                       </font>
                                    </Label>
                                    <Label styleClass="stat-label" text="Today's Orders" />
                                 </children>
                              </VBox>

                              <VBox styleClass="stat-card">
                                 <children>
                                    <Label styleClass="stat-icon" text="💰" />
                                    <Label fx:id="todayRevenueLabel" styleClass="stat-value" text="₹0.00">
                                       <font>
                                          <Font name="System Bold" size="20.0" />
                                       </font>
                                    </Label>
                                    <Label styleClass="stat-label" text="Today's Revenue" />
                                 </children>
                              </VBox>

                              <VBox styleClass="stat-card">
                                 <children>
                                    <Label styleClass="stat-icon" text="🍽️" />
                                    <Label fx:id="activeOrdersLabel" styleClass="stat-value" text="0">
                                       <font>
                                          <Font name="System Bold" size="20.0" />
                                       </font>
                                    </Label>
                                    <Label styleClass="stat-label" text="Active Orders" />
                                 </children>
                              </VBox>

                              <VBox styleClass="stat-card">
                                 <children>
                                    <Label styleClass="stat-icon" text="👥" />
                                    <Label fx:id="staffCountLabel" styleClass="stat-value" text="0">
                                       <font>
                                          <Font name="System Bold" size="20.0" />
                                       </font>
                                    </Label>
                                    <Label styleClass="stat-label" text="Staff Members" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>

                        <!-- Quick Actions with Responsive Grid Layout -->
                        <VBox spacing="15.0" styleClass="quick-actions-section">
                           <children>
                              <Label styleClass="quick-actions-title" text="Quick Actions">
                                 <font>
                                    <Font name="System Bold" size="18.0" />
                                 </font>
                              </Label>

                              <!-- Responsive Icon Grid - No Scroll Needed -->
                              <GridPane styleClass="quick-actions-responsive-grid" hgap="12.0" vgap="12.0">
                                 <columnConstraints>
                                    <ColumnConstraints hgrow="ALWAYS" minWidth="120.0" prefWidth="160.0" />
                                    <ColumnConstraints hgrow="ALWAYS" minWidth="120.0" prefWidth="160.0" />
                                    <ColumnConstraints hgrow="ALWAYS" minWidth="120.0" prefWidth="160.0" />
                                    <ColumnConstraints hgrow="ALWAYS" minWidth="120.0" prefWidth="160.0" />
                                 </columnConstraints>
                                 <rowConstraints>
                                    <RowConstraints minHeight="105.0" prefHeight="115.0" vgrow="SOMETIMES" />
                                    <RowConstraints minHeight="105.0" prefHeight="115.0" vgrow="SOMETIMES" />
                                 </rowConstraints>
                                 <children>
                                    <!-- Row 1 -->
                                    <VBox styleClass="quick-action-icon-card" alignment="CENTER" spacing="12.0" GridPane.columnIndex="0" GridPane.rowIndex="0">
                                       <children>
                                          <Label styleClass="quick-action-icon" text="👥" />
                                          <Label styleClass="quick-action-label" text="Manage Staff" />
                                          <Button mnemonicParsing="false" onAction="#quickManageStaff" styleClass="quick-action-button-modern" text="Open">
                                             <tooltip>
                                                <Tooltip text="Add, edit, or manage restaurant staff" />
                                             </tooltip>
                                          </Button>
                                       </children>
                                    </VBox>

                                    <VBox styleClass="quick-action-icon-card" alignment="CENTER" spacing="12.0" GridPane.columnIndex="1" GridPane.rowIndex="0">
                                       <children>
                                          <Label styleClass="quick-action-icon" text="🍽️" />
                                          <Label styleClass="quick-action-label" text="Menu Management" />
                                          <Button mnemonicParsing="false" onAction="#quickMenuManagement" styleClass="quick-action-button-modern" text="Open">
                                             <tooltip>
                                                <Tooltip text="Update menu items, prices, and availability" />
                                             </tooltip>
                                          </Button>
                                       </children>
                                    </VBox>

                                    <VBox styleClass="quick-action-icon-card" alignment="CENTER" spacing="12.0" GridPane.columnIndex="2" GridPane.rowIndex="0">
                                       <children>
                                          <Label styleClass="quick-action-icon" text="📦" />
                                          <Label styleClass="quick-action-label" text="Inventory" />
                                          <Button mnemonicParsing="false" onAction="#quickInventoryCheck" styleClass="quick-action-button-modern" text="Open">
                                             <tooltip>
                                                <Tooltip text="Check stock levels and inventory status" />
                                             </tooltip>
                                          </Button>
                                       </children>
                                    </VBox>

                                    <VBox styleClass="quick-action-icon-card" alignment="CENTER" spacing="12.0" GridPane.columnIndex="3" GridPane.rowIndex="0">
                                       <children>
                                          <Label styleClass="quick-action-icon" text="🪑" />
                                          <Label styleClass="quick-action-label" text="See Tables" />
                                          <Button mnemonicParsing="false" onAction="#quickSeeTables" styleClass="quick-action-button-modern" text="Open">
                                             <tooltip>
                                                <Tooltip text="View and manage table occupancy status" />
                                             </tooltip>
                                          </Button>
                                       </children>
                                    </VBox>

                                    <!-- Row 2 -->
                                    <VBox styleClass="quick-action-icon-card" alignment="CENTER" spacing="12.0" GridPane.columnIndex="0" GridPane.rowIndex="1">
                                       <children>
                                          <Label styleClass="quick-action-icon" text="🧾" />
                                          <Label styleClass="quick-action-label" text="Billing &amp; KOT" />
                                          <Button mnemonicParsing="false" onAction="#loadBillingKOT" styleClass="quick-action-button-modern" text="Open">
                                             <tooltip>
                                                <Tooltip text="Generate bills and kitchen order tickets" />
                                             </tooltip>
                                          </Button>
                                       </children>
                                    </VBox>

                                    <VBox styleClass="quick-action-icon-card" alignment="CENTER" spacing="12.0" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                       <children>
                                          <Label styleClass="quick-action-icon" text="👥" />
                                          <Label styleClass="quick-action-label" text="Customer CRM" />
                                          <Button mnemonicParsing="false" onAction="#loadCustomerCRM" styleClass="quick-action-button-modern" text="Open">
                                             <tooltip>
                                                <Tooltip text="Customer Relationship Management System" />
                                             </tooltip>
                                          </Button>
                                       </children>
                                    </VBox>

                                    <VBox styleClass="quick-action-icon-card" alignment="CENTER" spacing="12.0" GridPane.columnIndex="2" GridPane.rowIndex="1">
                                       <children>
                                          <Label styleClass="quick-action-icon" text="🍳" />
                                          <Label styleClass="quick-action-label" text="Recipe Management" />
                                          <Button mnemonicParsing="false" onAction="#loadRecipeManagement" styleClass="quick-action-button-modern" text="Open">
                                             <tooltip>
                                                <Tooltip text="Multi-Stage Recipe Conversion System" />
                                             </tooltip>
                                          </Button>
                                       </children>
                                    </VBox>

                                    <VBox styleClass="quick-action-icon-card" alignment="CENTER" spacing="12.0" GridPane.columnIndex="3" GridPane.rowIndex="1">
                                       <children>
                                          <Label styleClass="quick-action-icon" text="📋" />
                                          <Label styleClass="quick-action-label" text="Purchase Orders" />
                                          <Button mnemonicParsing="false" onAction="#loadPurchaseOrderRequest" styleClass="quick-action-button-modern" text="Open">
                                             <tooltip>
                                                <Tooltip text="Request items from suppliers" />
                                             </tooltip>
                                          </Button>
                                       </children>
                                    </VBox>
                                 </children>
                              </GridPane>
                           </children>
                        </VBox>

                        <!-- Recent Activity -->
                        <VBox spacing="15.0" styleClass="recent-activity-section">
                           <children>
                              <Label styleClass="section-title" text="Recent Activity">
                                 <font>
                                    <Font name="System Bold" size="18.0" />
                                 </font>
                              </Label>

                              <TableView fx:id="recentActivityTable" styleClass="activity-table">
                                 <columns>
                                    <TableColumn fx:id="activityTimeColumn" prefWidth="120.0" text="Time" />
                                    <TableColumn fx:id="activityTypeColumn" prefWidth="100.0" text="Type" />
                                    <TableColumn fx:id="activityDescriptionColumn" prefWidth="300.0" text="Description" />
                                    <TableColumn fx:id="activityUserColumn" prefWidth="120.0" text="User" />
                                 </columns>
                              </TableView>
                           </children>
                        </VBox>
                     </children>
                     <padding>
                        <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
                     </padding>
                  </VBox>
               </content>
            </ScrollPane>
         </children>
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </padding>
      </StackPane>
   </center>

   <!-- Notification Panel on the Right -->
   <right>
      <VBox fx:id="notificationPanelContainer" prefWidth="400.0" styleClass="notification-panel-container" fillWidth="true">
         <children>
            <!-- Notification panel will be loaded here -->
         </children>
      </VBox>
   </right>

   <!-- Floating AI Assistant Button -->
   <bottom>
      <StackPane>
         <children>
            <Button fx:id="floatingAIButton" onAction="#openSmartAIAssistant" styleClass="floating-ai-button">
               <graphic>
                  <VBox alignment="CENTER" spacing="2.0">
                     <children>
                        <Label text="🤖" styleClass="floating-ai-icon" />
                        <Label text="AI Assistant" styleClass="floating-ai-text" />
                     </children>
                  </VBox>
               </graphic>
               <tooltip>
                  <Tooltip text="Open Smart AI Assistant - Ask questions, get insights, and control your restaurant" />
               </tooltip>
            </Button>
         </children>
         <StackPane.margin>
            <Insets bottom="20.0" right="20.0" />
         </StackPane.margin>
      </StackPane>
   </bottom>

</BorderPane>
