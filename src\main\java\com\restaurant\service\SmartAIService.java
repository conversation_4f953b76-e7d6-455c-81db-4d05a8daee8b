package com.restaurant.service;

import com.restaurant.model.AIResponse;
import java.util.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class SmartAIService {
    
    private Map<String, Pattern> intentPatterns;
    private Map<String, String> responseTemplates;
    private Random random;
    
    public SmartAIService() {
        this.random = new Random();
        initializeIntentPatterns();
        initializeResponseTemplates();
    }
    
    private void initializeIntentPatterns() {
        intentPatterns = new HashMap<>();
        
        // Sales & Forecasting patterns
        intentPatterns.put("FORECAST_SALES", Pattern.compile(
            "(?i).*(forecast|predict|projection|estimate).*(sales|revenue|income).*(next|coming|upcoming)\\s*(\\d+)\\s*(day|week|month|year).*", 
            Pattern.CASE_INSENSITIVE));
        intentPatterns.put("SALES_TREND", Pattern.compile(
            "(?i).*(trend|pattern|growth|decline).*(sales|revenue).*", 
            Pattern.CASE_INSENSITIVE));
        intentPatterns.put("GROWTH_RATE", Pattern.compile(
            "(?i).*(growth|increase|decrease)\\s*(rate|percentage).*", 
            Pattern.CASE_INSENSITIVE));
        
        // Billing & Orders patterns
        intentPatterns.put("CREATE_ORDER", Pattern.compile(
            "(?i).*(create|make|place|add)\\s*(order|bill).*(table)\\s*(\\d+).*", 
            Pattern.CASE_INSENSITIVE));
        intentPatterns.put("ADD_ITEMS", Pattern.compile(
            "(?i).*(add|include)\\s*(\\d+)?\\s*([\\w\\s]+)\\s*to\\s*table\\s*(\\d+).*", 
            Pattern.CASE_INSENSITIVE));
        intentPatterns.put("SHOW_BILL", Pattern.compile(
            "(?i).*(show|display|get)\\s*(bill|invoice|receipt).*(table)\\s*(\\d+).*", 
            Pattern.CASE_INSENSITIVE));
        
        // Inventory & Stock patterns
        intentPatterns.put("LOW_STOCK", Pattern.compile(
            "(?i).*(low|running low|shortage|out of)\\s*(stock|inventory).*", 
            Pattern.CASE_INSENSITIVE));
        intentPatterns.put("UPDATE_STOCK", Pattern.compile(
            "(?i).*(update|change|set)\\s*([\\w\\s]+)\\s*(quantity|stock)\\s*to\\s*(\\d+)\\s*(kg|liter|piece|unit).*", 
            Pattern.CASE_INSENSITIVE));
        intentPatterns.put("CHECK_STOCK", Pattern.compile(
            "(?i).*(check|show|display)\\s*(stock|inventory).*(status|level|amount).*", 
            Pattern.CASE_INSENSITIVE));
        
        // Analytics & Reports patterns
        intentPatterns.put("SALES_SUMMARY", Pattern.compile(
            "(?i).*(today|daily|yesterday).*sales\\s*(summary|report|total).*", 
            Pattern.CASE_INSENSITIVE));
        intentPatterns.put("COMPARE_PERIODS", Pattern.compile(
            "(?i).*(compare|comparison)\\s*(last|previous)\\s*(week|month)\\s*(vs|versus|with)\\s*(this|current)\\s*(week|month).*", 
            Pattern.CASE_INSENSITIVE));
        intentPatterns.put("DOWNLOAD_REPORT", Pattern.compile(
            "(?i).*(download|export|save)\\s*(report|data|summary).*", 
            Pattern.CASE_INSENSITIVE));
        
        // Menu & Search patterns
        intentPatterns.put("SEARCH_MENU", Pattern.compile(
            "(?i).*(search|find|show|display)\\s*(all)?\\s*([\\w\\s]+)\\s*(dish|item|food|menu).*", 
            Pattern.CASE_INSENSITIVE));
        intentPatterns.put("FILTER_BY_PRICE", Pattern.compile(
            "(?i).*(under|below|less than|cheaper than)\\s*[₹$]?(\\d+).*", 
            Pattern.CASE_INSENSITIVE));
        intentPatterns.put("POPULAR_ITEMS", Pattern.compile(
            "(?i).*(popular|best selling|top|most sold|bestseller)\\s*(item|dish|food).*", 
            Pattern.CASE_INSENSITIVE));
        
        // Navigation patterns
        intentPatterns.put("NAVIGATE", Pattern.compile(
            "(?i).*(go to|open|navigate to|show)\\s*(dashboard|forecaster|table|billing|report|menu|inventory).*", 
            Pattern.CASE_INSENSITIVE));
        intentPatterns.put("RESET_FILTERS", Pattern.compile(
            "(?i).*(reset|clear|remove)\\s*(all)?\\s*(filter|search|selection).*", 
            Pattern.CASE_INSENSITIVE));
    }
    
    private void initializeResponseTemplates() {
        responseTemplates = new HashMap<>();
        
        // Sales & Forecasting responses
        responseTemplates.put("FORECAST_SALES", "📊 Based on historical data and current trends, here's your sales forecast:");
        responseTemplates.put("SALES_TREND", "📈 Here's the sales trend analysis:");
        responseTemplates.put("GROWTH_RATE", "📊 Growth rate analysis:");
        
        // Billing & Orders responses
        responseTemplates.put("CREATE_ORDER", "✅ Order created successfully for table {table}!");
        responseTemplates.put("ADD_ITEMS", "✅ Added {quantity} {item} to table {table}!");
        responseTemplates.put("SHOW_BILL", "💰 Here's the bill for table {table}:");
        
        // Inventory & Stock responses
        responseTemplates.put("LOW_STOCK", "⚠️ Here are the items running low in stock:");
        responseTemplates.put("UPDATE_STOCK", "✅ Updated {item} stock to {quantity} {unit}!");
        responseTemplates.put("CHECK_STOCK", "📦 Current stock status:");
        
        // Analytics & Reports responses
        responseTemplates.put("SALES_SUMMARY", "💰 Today's sales summary:");
        responseTemplates.put("COMPARE_PERIODS", "📊 Period comparison analysis:");
        responseTemplates.put("DOWNLOAD_REPORT", "📥 Report download initiated!");
        
        // Menu & Search responses
        responseTemplates.put("SEARCH_MENU", "🔍 Search results for '{query}':");
        responseTemplates.put("FILTER_BY_PRICE", "💰 Items under ₹{price}:");
        responseTemplates.put("POPULAR_ITEMS", "🏆 Most popular items:");
        
        // Navigation responses
        responseTemplates.put("NAVIGATE", "🧭 Navigating to {destination}...");
        responseTemplates.put("RESET_FILTERS", "🔄 All filters have been reset!");
    }
    
    public AIResponse processMessage(String message) {
        String intent = detectIntent(message);
        Map<String, String> entities = extractEntities(message, intent);
        
        return generateResponse(intent, entities, message);
    }
    
    private String detectIntent(String message) {
        for (Map.Entry<String, Pattern> entry : intentPatterns.entrySet()) {
            if (entry.getValue().matcher(message).matches()) {
                return entry.getKey();
            }
        }
        return "UNKNOWN";
    }
    
    private Map<String, String> extractEntities(String message, String intent) {
        Map<String, String> entities = new HashMap<>();
        
        switch (intent) {
            case "FORECAST_SALES":
                extractForecastEntities(message, entities);
                break;
            case "ADD_ITEMS":
                extractOrderEntities(message, entities);
                break;
            case "CREATE_ORDER":
            case "SHOW_BILL":
                extractTableEntities(message, entities);
                break;
            case "UPDATE_STOCK":
                extractStockEntities(message, entities);
                break;
            case "SEARCH_MENU":
                extractSearchEntities(message, entities);
                break;
            case "FILTER_BY_PRICE":
                extractPriceEntities(message, entities);
                break;
            case "NAVIGATE":
                extractNavigationEntities(message, entities);
                break;
        }
        
        return entities;
    }
    
    private void extractForecastEntities(String message, Map<String, String> entities) {
        Pattern timePattern = Pattern.compile("(?i)(\\d+)\\s*(day|week|month|year)");
        Matcher matcher = timePattern.matcher(message);
        if (matcher.find()) {
            entities.put("period", matcher.group(1));
            entities.put("unit", matcher.group(2));
        }
        
        // Extract category if mentioned
        if (message.toLowerCase().contains("biryani")) entities.put("category", "biryani");
        else if (message.toLowerCase().contains("beverage")) entities.put("category", "beverages");
        else if (message.toLowerCase().contains("starter")) entities.put("category", "starters");
    }
    
    private void extractOrderEntities(String message, Map<String, String> entities) {
        Pattern orderPattern = Pattern.compile("(?i)(\\d+)?\\s*([\\w\\s]+)\\s*to\\s*table\\s*(\\d+)");
        Matcher matcher = orderPattern.matcher(message);
        if (matcher.find()) {
            entities.put("quantity", matcher.group(1) != null ? matcher.group(1) : "1");
            entities.put("item", matcher.group(2).trim());
            entities.put("table", matcher.group(3));
        }
    }
    
    private void extractTableEntities(String message, Map<String, String> entities) {
        Pattern tablePattern = Pattern.compile("(?i)table\\s*(\\d+)");
        Matcher matcher = tablePattern.matcher(message);
        if (matcher.find()) {
            entities.put("table", matcher.group(1));
        }
    }
    
    private void extractStockEntities(String message, Map<String, String> entities) {
        Pattern stockPattern = Pattern.compile("(?i)([\\w\\s]+)\\s*to\\s*(\\d+)\\s*(kg|liter|piece|unit)");
        Matcher matcher = stockPattern.matcher(message);
        if (matcher.find()) {
            entities.put("item", matcher.group(1).trim());
            entities.put("quantity", matcher.group(2));
            entities.put("unit", matcher.group(3));
        }
    }
    
    private void extractSearchEntities(String message, Map<String, String> entities) {
        Pattern searchPattern = Pattern.compile("(?i)(search|find|show)\\s*(all)?\\s*([\\w\\s]+)\\s*(dish|item|food)");
        Matcher matcher = searchPattern.matcher(message);
        if (matcher.find()) {
            entities.put("query", matcher.group(3).trim());
        }
        
        // Extract dietary preferences
        if (message.toLowerCase().contains("vegetarian")) entities.put("dietary", "vegetarian");
        else if (message.toLowerCase().contains("vegan")) entities.put("dietary", "vegan");
        else if (message.toLowerCase().contains("spicy")) entities.put("spice", "spicy");
    }
    
    private void extractPriceEntities(String message, Map<String, String> entities) {
        Pattern pricePattern = Pattern.compile("(?i)(under|below|less than)\\s*[₹$]?(\\d+)");
        Matcher matcher = pricePattern.matcher(message);
        if (matcher.find()) {
            entities.put("price", matcher.group(2));
        }
    }
    
    private void extractNavigationEntities(String message, Map<String, String> entities) {
        if (message.toLowerCase().contains("dashboard")) entities.put("destination", "dashboard");
        else if (message.toLowerCase().contains("forecaster")) entities.put("destination", "forecaster");
        else if (message.toLowerCase().contains("table")) entities.put("destination", "tables");
        else if (message.toLowerCase().contains("billing")) entities.put("destination", "billing");
        else if (message.toLowerCase().contains("report")) entities.put("destination", "reports");
        else if (message.toLowerCase().contains("menu")) entities.put("destination", "menu");
        else if (message.toLowerCase().contains("inventory")) entities.put("destination", "inventory");
    }
    
    private AIResponse generateResponse(String intent, Map<String, String> entities, String originalMessage) {
        AIResponse response = new AIResponse();
        
        String template = responseTemplates.getOrDefault(intent, "I understand you want help with: {message}");
        String message = formatResponseMessage(template, entities, originalMessage);
        
        response.setMessage(message);
        response.setIntent(intent);
        response.setEntities(entities);
        response.setConfidence(calculateConfidence(intent, entities));
        
        // Add visual data and actions based on intent
        addVisualDataAndActions(response, intent, entities);
        
        return response;
    }
    
    private String formatResponseMessage(String template, Map<String, String> entities, String originalMessage) {
        String formatted = template;
        
        // Replace placeholders with entity values
        for (Map.Entry<String, String> entity : entities.entrySet()) {
            formatted = formatted.replace("{" + entity.getKey() + "}", entity.getValue());
        }
        
        // Replace {message} with original message
        formatted = formatted.replace("{message}", originalMessage);
        
        return formatted;
    }
    
    private double calculateConfidence(String intent, Map<String, String> entities) {
        double baseConfidence = intent.equals("UNKNOWN") ? 0.3 : 0.8;
        double entityBonus = entities.size() * 0.05;
        return Math.min(1.0, baseConfidence + entityBonus);
    }
    
    private void addVisualDataAndActions(AIResponse response, String intent, Map<String, String> entities) {
        List<String> actionButtons = new ArrayList<>();
        List<String> suggestedActions = new ArrayList<>();
        
        switch (intent) {
            case "FORECAST_SALES":
                response.setChartData("Sales Forecast Chart - Next " + entities.getOrDefault("period", "7") + " " + entities.getOrDefault("unit", "days"));
                response.setTableData("Projected Revenue: ₹" + (50000 + random.nextInt(50000)));
                actionButtons.addAll(Arrays.asList("View Details", "Download Report", "Open Forecaster"));
                suggestedActions.addAll(Arrays.asList("📊 Show growth trends", "📈 Compare with last period", "🎯 Set sales targets"));
                response.setExecutedAction("Generated sales forecast");
                break;
                
            case "SALES_SUMMARY":
                response.setChartData("Daily Sales Chart");
                response.setTableData("Today's Total: ₹" + (15000 + random.nextInt(10000)) + " | Orders: " + (45 + random.nextInt(20)));
                actionButtons.addAll(Arrays.asList("View Details", "Download Report", "Compare Periods"));
                suggestedActions.addAll(Arrays.asList("📊 Show hourly breakdown", "🏆 Top selling items", "📈 Weekly comparison"));
                response.setExecutedAction("Retrieved sales summary");
                break;
                
            case "LOW_STOCK":
                response.setTableData("Low Stock Items: Paneer (2kg), Chicken (5kg), Tomatoes (3kg)");
                actionButtons.addAll(Arrays.asList("Update Stock", "Generate Purchase Order", "Set Alerts"));
                suggestedActions.addAll(Arrays.asList("📦 Update inventory", "🛒 Create purchase order", "⚠️ Set stock alerts"));
                response.setExecutedAction("Retrieved low stock items");
                break;
                
            case "ADD_ITEMS":
                String table = entities.getOrDefault("table", "X");
                String item = entities.getOrDefault("item", "item");
                String quantity = entities.getOrDefault("quantity", "1");
                response.setTableData("Order Updated - Table " + table + ": " + quantity + " " + item);
                actionButtons.addAll(Arrays.asList("View Bill", "Add More Items", "Place Order"));
                suggestedActions.addAll(Arrays.asList("💰 Show table bill", "🍽️ Add more items", "✅ Finalize order"));
                response.setExecutedAction("Added " + quantity + " " + item + " to table " + table);
                break;
                
            case "SEARCH_MENU":
                String query = entities.getOrDefault("query", "items");
                response.setTableData("Found 8 " + query + " items");
                actionButtons.addAll(Arrays.asList("View Menu", "Filter Results", "Add to Order"));
                suggestedActions.addAll(Arrays.asList("🔍 Refine search", "💰 Filter by price", "🌶️ Filter by spice level"));
                response.setExecutedAction("Searched menu for " + query);
                break;
                
            case "POPULAR_ITEMS":
                response.setTableData("Top 5: Butter Chicken, Biryani, Naan, Lassi, Gulab Jamun");
                actionButtons.addAll(Arrays.asList("View Details", "Add to Order", "See Analytics"));
                suggestedActions.addAll(Arrays.asList("📊 View sales analytics", "🎯 Promote items", "📈 Track trends"));
                response.setExecutedAction("Retrieved popular items");
                break;
                
            case "NAVIGATE":
                String destination = entities.getOrDefault("destination", "dashboard");
                actionButtons.addAll(Arrays.asList("Go to " + destination.substring(0, 1).toUpperCase() + destination.substring(1)));
                suggestedActions.addAll(Arrays.asList("🏠 Go to dashboard", "📊 Open reports", "🪑 Manage tables"));
                response.setExecutedAction("Navigation to " + destination);
                break;
                
            default:
                suggestedActions.addAll(Arrays.asList("📊 Check sales", "📦 View inventory", "🪑 Manage tables", "📈 Generate reports"));
                response.setExecutedAction("Processed general query");
        }
        
        response.setActionButtons(actionButtons);
        response.setSuggestedActions(suggestedActions);
        response.setActionDetails("Detailed information about the executed action would be shown here.");
    }
}
