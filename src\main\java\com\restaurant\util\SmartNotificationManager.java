package com.restaurant.util;

import javafx.application.Platform;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.stage.Popup;
import javafx.stage.Stage;
import javafx.stage.StageStyle;
import javafx.util.Duration;
import javafx.animation.*;
import javafx.scene.paint.Color;
import javafx.scene.effect.DropShadow;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Smart Notification Manager - Unified notification system with proper UX
 * Features:
 * - Non-blocking toast notifications
 * - Priority-based notification queue
 * - Smart positioning and stacking
 * - Auto-dismiss with user interaction
 * - Audio alerts with volume control
 * - Persistent notifications for critical actions
 */
public class SmartNotificationManager {
    
    private static SmartNotificationManager instance;
    private Stage primaryStage;
    private final Queue<NotificationData> notificationQueue = new LinkedList<>();
    private final Map<String, Popup> activeNotifications = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    
    // Notification settings
    private boolean notificationsEnabled = true;
    private boolean audioEnabled = true;
    private double notificationWidth = 350;
    private double notificationHeight = 80;
    private int maxVisibleNotifications = 5;
    private int defaultDuration = 4; // seconds
    
    // Notification types with priorities and styling
    public enum NotificationType {
        SUCCESS("✅", "#27ae60", 3, false),
        INFO("ℹ️", "#3498db", 2, false),
        WARNING("⚠️", "#f39c12", 4, false),
        ERROR("❌", "#e74c3c", 5, false),
        LOADING("⏳", "#9b59b6", 1, true),
        ORDER_NEW("🛒", "#2196f3", 5, false),
        ORDER_SWIGGY("🟠", "#ff6600", 5, false),
        ORDER_ZOMATO("🔴", "#e23744", 5, false),
        ORDER_READY("🍽️", "#ff9800", 4, false),
        URGENT("🚨", "#f44336", 6, false);
        
        private final String icon;
        private final String color;
        private final int priority;
        private final boolean persistent;
        
        NotificationType(String icon, String color, int priority, boolean persistent) {
            this.icon = icon;
            this.color = color;
            this.priority = priority;
            this.persistent = persistent;
        }
        
        public String getIcon() { return icon; }
        public String getColor() { return color; }
        public int getPriority() { return priority; }
        public boolean isPersistent() { return persistent; }
    }
    
    // Notification data class
    private static class NotificationData {
        final String id;
        final NotificationType type;
        final String title;
        final String message;
        final int duration;
        final Runnable onAction;
        final long timestamp;
        
        NotificationData(String id, NotificationType type, String title, String message, 
                        int duration, Runnable onAction) {
            this.id = id;
            this.type = type;
            this.title = title;
            this.message = message;
            this.duration = duration;
            this.onAction = onAction;
            this.timestamp = System.currentTimeMillis();
        }
    }
    
    private SmartNotificationManager() {}
    
    public static synchronized SmartNotificationManager getInstance() {
        if (instance == null) {
            instance = new SmartNotificationManager();
        }
        return instance;
    }
    
    public void initialize(Stage primaryStage) {
        this.primaryStage = primaryStage;
        System.out.println("SmartNotificationManager initialized");
    }
    
    // Main notification methods
    public void showSuccess(String title, String message) {
        showNotification(NotificationType.SUCCESS, title, message);
    }
    
    public void showInfo(String title, String message) {
        showNotification(NotificationType.INFO, title, message);
    }
    
    public void showWarning(String title, String message) {
        showNotification(NotificationType.WARNING, title, message);
    }
    
    public void showError(String title, String message) {
        showNotification(NotificationType.ERROR, title, message);
    }
    
    public void showLoading(String title, String message) {
        showNotification(NotificationType.LOADING, title, message, 0, null); // Persistent
    }
    
    public void hideLoading(String title) {
        hideNotification(title);
    }
    
    public void showOrderNotification(String platform, String orderId, String customerName, double amount) {
        NotificationType type = getOrderNotificationType(platform);
        String title = platform.toUpperCase() + " ORDER #" + orderId;
        String message = "Customer: " + customerName + "\nAmount: ₹" + String.format("%.2f", amount);
        
        showNotification(type, title, message, 10, () -> {
            // Action when notification is clicked - open order details
            System.out.println("Opening order details for: " + orderId);
        });
    }
    
    private NotificationType getOrderNotificationType(String platform) {
        switch (platform.toUpperCase()) {
            case "SWIGGY": return NotificationType.ORDER_SWIGGY;
            case "ZOMATO": return NotificationType.ORDER_ZOMATO;
            default: return NotificationType.ORDER_NEW;
        }
    }
    
    public void showNotification(NotificationType type, String title, String message) {
        showNotification(type, title, message, defaultDuration, null);
    }
    
    public void showNotification(NotificationType type, String title, String message, 
                               int duration, Runnable onAction) {
        if (!notificationsEnabled) return;
        
        String id = generateNotificationId(title);
        NotificationData notification = new NotificationData(id, type, title, message, duration, onAction);
        
        Platform.runLater(() -> {
            // Remove existing notification with same title if exists
            hideNotification(title);
            
            // Add to queue and process
            notificationQueue.offer(notification);
            processNotificationQueue();
        });
    }
    
    private void processNotificationQueue() {
        // Limit visible notifications
        while (activeNotifications.size() >= maxVisibleNotifications && !notificationQueue.isEmpty()) {
            // Remove oldest notification
            removeOldestNotification();
        }
        
        // Show new notifications
        while (!notificationQueue.isEmpty() && activeNotifications.size() < maxVisibleNotifications) {
            NotificationData notification = notificationQueue.poll();
            if (notification != null) {
                displayNotification(notification);
            }
        }
    }
    
    private void displayNotification(NotificationData notification) {
        if (primaryStage == null) return;
        
        try {
            Popup popup = createNotificationPopup(notification);
            activeNotifications.put(notification.id, popup);
            
            // Position notification
            positionNotification(popup);
            
            // Show with animation
            popup.show(primaryStage);
            animateNotificationIn(popup);
            
            // Auto-hide if not persistent
            if (notification.duration > 0) {
                scheduler.schedule(() -> {
                    Platform.runLater(() -> hideNotification(notification.id));
                }, notification.duration, TimeUnit.SECONDS);
            }
            
            // Play audio alert
            if (audioEnabled) {
                playNotificationSound(notification.type);
            }
            
            System.out.println("📢 " + notification.type.getIcon() + " " + notification.title + ": " + notification.message);
            
        } catch (Exception e) {
            System.err.println("Error displaying notification: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private Popup createNotificationPopup(NotificationData notification) {
        Popup popup = new Popup();
        popup.setAutoHide(false);
        popup.setHideOnEscape(false);
        
        // Create notification content
        VBox container = new VBox(8);
        container.getStyleClass().add("notification-container");
        container.setStyle(
            "-fx-background-color: white;" +
            "-fx-background-radius: 8;" +
            "-fx-border-color: " + notification.type.getColor() + ";" +
            "-fx-border-width: 0 0 0 4;" +
            "-fx-border-radius: 8;" +
            "-fx-padding: 12;" +
            "-fx-spacing: 8;" +
            "-fx-min-width: " + notificationWidth + ";" +
            "-fx-max-width: " + notificationWidth + ";" +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 10, 0, 0, 2);"
        );
        
        // Header with icon and close button
        HBox header = new HBox(8);
        header.setAlignment(Pos.CENTER_LEFT);
        
        Label iconLabel = new Label(notification.type.getIcon());
        iconLabel.setStyle("-fx-font-size: 16px;");
        
        Label titleLabel = new Label(notification.title);
        titleLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 13px; -fx-text-fill: #2c3e50;");
        titleLabel.setWrapText(true);
        
        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);
        
        Button closeBtn = new Button("✕");
        closeBtn.setStyle(
            "-fx-background-color: transparent;" +
            "-fx-border-color: transparent;" +
            "-fx-text-fill: #7f8c8d;" +
            "-fx-font-size: 12px;" +
            "-fx-padding: 2 6 2 6;" +
            "-fx-cursor: hand;"
        );
        closeBtn.setOnAction(e -> hideNotification(notification.id));
        
        header.getChildren().addAll(iconLabel, titleLabel, spacer, closeBtn);
        
        // Message
        Label messageLabel = new Label(notification.message);
        messageLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #34495e;");
        messageLabel.setWrapText(true);
        
        container.getChildren().addAll(header, messageLabel);
        
        // Add click action if provided
        if (notification.onAction != null) {
            container.setOnMouseClicked(e -> {
                notification.onAction.run();
                hideNotification(notification.id);
            });
            container.setStyle(container.getStyle() + "-fx-cursor: hand;");
        }
        
        popup.getContent().add(container);
        return popup;
    }
    
    private void positionNotification(Popup popup) {
        if (primaryStage == null) return;
        
        double stageX = primaryStage.getX();
        double stageY = primaryStage.getY();
        double stageWidth = primaryStage.getWidth();
        
        // Position at top-right of the stage
        double x = stageX + stageWidth - notificationWidth - 20;
        double y = stageY + 60 + (activeNotifications.size() * (notificationHeight + 10));
        
        popup.setX(x);
        popup.setY(y);
    }
    
    private void animateNotificationIn(Popup popup) {
        if (popup.getContent().isEmpty()) return;
        
        VBox container = (VBox) popup.getContent().get(0);
        container.setTranslateX(notificationWidth);
        container.setOpacity(0);
        
        TranslateTransition slideIn = new TranslateTransition(Duration.millis(300), container);
        slideIn.setFromX(notificationWidth);
        slideIn.setToX(0);
        
        FadeTransition fadeIn = new FadeTransition(Duration.millis(300), container);
        fadeIn.setFromValue(0);
        fadeIn.setToValue(1);
        
        ParallelTransition animation = new ParallelTransition(slideIn, fadeIn);
        animation.play();
    }
    
    public void hideNotification(String id) {
        Popup popup = activeNotifications.remove(id);
        if (popup != null) {
            animateNotificationOut(popup);
        }
    }
    
    private void animateNotificationOut(Popup popup) {
        if (popup.getContent().isEmpty()) return;
        
        VBox container = (VBox) popup.getContent().get(0);
        
        FadeTransition fadeOut = new FadeTransition(Duration.millis(200), container);
        fadeOut.setFromValue(1);
        fadeOut.setToValue(0);
        fadeOut.setOnFinished(e -> popup.hide());
        fadeOut.play();
        
        // Reposition remaining notifications
        Platform.runLater(this::repositionNotifications);
    }
    
    private void repositionNotifications() {
        int index = 0;
        for (Popup popup : activeNotifications.values()) {
            double newY = primaryStage.getY() + 60 + (index * (notificationHeight + 10));
            
            TranslateTransition reposition = new TranslateTransition(Duration.millis(200), popup.getContent().get(0));
            reposition.setToY(newY - popup.getY());
            reposition.play();
            
            index++;
        }
    }
    
    private void removeOldestNotification() {
        if (activeNotifications.isEmpty()) return;
        
        String oldestId = activeNotifications.keySet().iterator().next();
        hideNotification(oldestId);
    }
    
    private void playNotificationSound(NotificationType type) {
        try {
            // Simple beep for now - can be enhanced with actual sound files
            java.awt.Toolkit.getDefaultToolkit().beep();
        } catch (Exception e) {
            System.err.println("Error playing notification sound: " + e.getMessage());
        }
    }
    
    private String generateNotificationId(String title) {
        return title + "_" + System.currentTimeMillis();
    }
    
    // Settings
    public void setNotificationsEnabled(boolean enabled) {
        this.notificationsEnabled = enabled;
    }
    
    public void setAudioEnabled(boolean enabled) {
        this.audioEnabled = enabled;
    }
    
    public void clearAllNotifications() {
        Platform.runLater(() -> {
            new ArrayList<>(activeNotifications.keySet()).forEach(this::hideNotification);
            notificationQueue.clear();
        });
    }
    
    public void shutdown() {
        clearAllNotifications();
        scheduler.shutdown();
    }
}
