@echo off
echo Debugging OrderManagement Hanging Issue...
echo.

echo This script will help identify where OrderManagement gets stuck during loading.
echo Watch the console output carefully for the last message before it hangs.
echo.

echo Compiling project...
call mvn clean compile -q

if %ERRORLEVEL% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo Compilation successful!
echo.

echo Starting application with detailed OrderManagement logging...
echo.
echo WHAT TO LOOK FOR:
echo 1. "OrderManagementController.initialize() called"
echo 2. "Setting up table columns..."
echo 3. "Loading sample orders..."
echo 4. "Starting loadTodayActiveOrders..."
echo 5. "Calling OrderDAO.getTodayActiveOrders()..."
echo 6. "OrderDAO.getTodayActiveOrders() - Starting database query..."
echo 7. "OrderDAO.getTodayActiveOrders() - Got database connection"
echo 8. "OrderDAO.getTodayActiveOrders() - Query executed, processing results..."
echo 9. "loadOrderItems() - Loading items for order ID: X"
echo 10. "OrderDAO.getTodayActiveOrders() - Returning X orders"
echo 11. "Setting up filters..."
echo 12. "OrderManagementController initialized successfully"
echo.
echo If it hangs, note the LAST message you see before it stops responding.
echo.

java -Dprism.order=sw ^
     -Dprism.allowhidpi=false ^
     -Djavafx.animation.pulse=60 ^
     -Djava.awt.headless=false ^
     -Dsqlite.purejava=false ^
     -Xms512m ^
     -Xmx2g ^
     -XX:+UseG1GC ^
     -XX:MaxGCPauseMillis=200 ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics ^
     -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\pdfbox\2.0.27\pdfbox-2.0.27.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\fontbox\2.0.27\fontbox-2.0.27.jar;%USERPROFILE%\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar" ^
     com.restaurant.RestaurantApp

echo.
echo Application closed.
echo.
echo DEBUGGING ANALYSIS:
echo.
echo If OrderManagement hung, check which was the LAST message you saw:
echo.
echo 1. If it stopped at "OrderManagementController.initialize() called"
echo    - Problem is in the initialize method itself
echo.
echo 2. If it stopped at "Setting up table columns..."
echo    - Problem is in setupTableColumns() method
echo.
echo 3. If it stopped at "Loading sample orders..."
echo    - Problem is in loadSampleOrders() method
echo.
echo 4. If it stopped at "Starting loadTodayActiveOrders..."
echo    - Problem is in the database query
echo.
echo 5. If it stopped at "Calling OrderDAO.getTodayActiveOrders()..."
echo    - Problem is in the OrderDAO database call
echo.
echo 6. If it stopped at "Setting up filters..."
echo    - Problem is in setupFilters() method
echo.
echo 7. If you saw "OrderManagementController initialized successfully"
echo    - Initialization completed, problem might be elsewhere
echo.
echo The detailed logging should help pinpoint exactly where it hangs.
echo.
pause
