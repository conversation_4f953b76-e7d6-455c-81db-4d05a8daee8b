package com.restaurant.component;

import javafx.animation.KeyFrame;
import javafx.animation.Timeline;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.geometry.Orientation;
import javafx.scene.control.ScrollPane;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.Pane;
import javafx.scene.layout.Region;
import javafx.scene.paint.Color;
import javafx.scene.shape.Rectangle;
import javafx.util.Duration;

/**
 * Custom ScrollBar component with full control over appearance and behavior
 * Based on your exact specifications: 15px width, light grey colors, functional scrolling
 */
public class CustomScrollBar extends Region {
    
    // Properties
    private final DoubleProperty value = new SimpleDoubleProperty(0.0);
    private final DoubleProperty min = new SimpleDoubleProperty(0.0);
    private final DoubleProperty max = new SimpleDoubleProperty(100.0);
    private final DoubleProperty visibleAmount = new SimpleDoubleProperty(10.0);
    
    // Visual components
    private Rectangle track;
    private Rectangle thumb;
    private Rectangle upButton;
    private Rectangle downButton;
    
    // Behavior properties
    private double thumbDragStartY;
    private double thumbDragStartValue;
    private boolean isDragging = false;
    
    // Dimensions based on your specifications
    private static final double TRACK_WIDTH = 15.0;
    private static final double THUMB_WIDTH = 13.0;
    private static final double BUTTON_HEIGHT = 15.0;
    private static final double MIN_THUMB_HEIGHT = 20.0;
    
    // Colors based on your specifications
    private static final Color TRACK_COLOR = Color.web("#f2f2f2");
    private static final Color THUMB_COLOR = Color.web("#cccccc");
    private static final Color THUMB_HOVER_COLOR = Color.web("#999999");
    private static final Color THUMB_PRESSED_COLOR = Color.web("#777777");
    private static final Color BUTTON_COLOR = Color.web("#e0e0e0");
    
    public CustomScrollBar() {
        initializeComponents();
        setupEventHandlers();
        setupBindings();
        
        // Set preferred width
        setPrefWidth(TRACK_WIDTH);
        setMinWidth(TRACK_WIDTH);
        setMaxWidth(TRACK_WIDTH);
    }
    
    private void initializeComponents() {
        // Create track
        track = new Rectangle();
        track.setFill(TRACK_COLOR);
        track.setStroke(Color.web("#e0e0e0"));
        track.setStrokeWidth(1);
        track.setWidth(TRACK_WIDTH);
        
        // Create thumb
        thumb = new Rectangle();
        thumb.setFill(THUMB_COLOR);
        thumb.setArcWidth(3);
        thumb.setArcHeight(3);
        thumb.setWidth(THUMB_WIDTH);
        thumb.setX(1); // Center in track
        
        // Create up button
        upButton = new Rectangle();
        upButton.setFill(BUTTON_COLOR);
        upButton.setStroke(Color.web("#cccccc"));
        upButton.setStrokeWidth(1);
        upButton.setWidth(TRACK_WIDTH);
        upButton.setHeight(BUTTON_HEIGHT);
        
        // Create down button
        downButton = new Rectangle();
        downButton.setFill(BUTTON_COLOR);
        downButton.setStroke(Color.web("#cccccc"));
        downButton.setStrokeWidth(1);
        downButton.setWidth(TRACK_WIDTH);
        downButton.setHeight(BUTTON_HEIGHT);
        
        getChildren().addAll(track, thumb, upButton, downButton);
    }
    
    private void setupEventHandlers() {
        // Thumb drag handling
        thumb.setOnMousePressed(this::handleThumbPressed);
        thumb.setOnMouseDragged(this::handleThumbDragged);
        thumb.setOnMouseReleased(this::handleThumbReleased);
        
        // Thumb hover effects
        thumb.setOnMouseEntered(e -> thumb.setFill(THUMB_HOVER_COLOR));
        thumb.setOnMouseExited(e -> {
            if (!isDragging) {
                thumb.setFill(THUMB_COLOR);
            }
        });
        
        // Button click handling
        upButton.setOnMouseClicked(e -> scrollUp());
        downButton.setOnMouseClicked(e -> scrollDown());
        
        // Track click handling (page scroll)
        track.setOnMouseClicked(this::handleTrackClicked);
    }
    
    private void setupBindings() {
        // Update layout when size changes
        heightProperty().addListener((obs, oldVal, newVal) -> updateLayout());
        
        // Update thumb position when value changes
        value.addListener((obs, oldVal, newVal) -> updateThumbPosition());
        visibleAmount.addListener((obs, oldVal, newVal) -> updateThumbSize());
        max.addListener((obs, oldVal, newVal) -> updateThumbSize());
    }
    
    private void updateLayout() {
        double height = getHeight();
        
        // Position up button at top
        upButton.setY(0);
        
        // Position track in middle
        track.setY(BUTTON_HEIGHT);
        track.setHeight(height - 2 * BUTTON_HEIGHT);
        
        // Position down button at bottom
        downButton.setY(height - BUTTON_HEIGHT);
        
        // Update thumb size and position
        updateThumbSize();
        updateThumbPosition();
    }
    
    private void updateThumbSize() {
        double trackHeight = getHeight() - 2 * BUTTON_HEIGHT;
        double range = max.get() - min.get();
        
        if (range <= 0) {
            thumb.setHeight(trackHeight);
            return;
        }
        
        // Calculate thumb height based on visible amount ratio
        double ratio = visibleAmount.get() / (range + visibleAmount.get());
        double thumbHeight = Math.max(MIN_THUMB_HEIGHT, trackHeight * ratio);
        
        thumb.setHeight(thumbHeight);
        updateThumbPosition();
    }
    
    private void updateThumbPosition() {
        double trackHeight = getHeight() - 2 * BUTTON_HEIGHT;
        double thumbHeight = thumb.getHeight();
        double availableTrackHeight = trackHeight - thumbHeight;
        
        double range = max.get() - min.get();
        if (range <= 0) {
            thumb.setY(BUTTON_HEIGHT);
            return;
        }
        
        // Calculate thumb position based on current value
        double ratio = (value.get() - min.get()) / range;
        double thumbY = BUTTON_HEIGHT + (availableTrackHeight * ratio);
        
        thumb.setY(thumbY);
    }
    
    private void handleThumbPressed(MouseEvent event) {
        isDragging = true;
        thumbDragStartY = event.getSceneY();
        thumbDragStartValue = value.get();
        thumb.setFill(THUMB_PRESSED_COLOR);
        event.consume();
    }
    
    private void handleThumbDragged(MouseEvent event) {
        if (!isDragging) return;
        
        double deltaY = event.getSceneY() - thumbDragStartY;
        double trackHeight = getHeight() - 2 * BUTTON_HEIGHT;
        double thumbHeight = thumb.getHeight();
        double availableTrackHeight = trackHeight - thumbHeight;
        
        if (availableTrackHeight <= 0) return;
        
        double range = max.get() - min.get();
        double deltaValue = (deltaY / availableTrackHeight) * range;
        double newValue = thumbDragStartValue + deltaValue;
        
        // Clamp value to valid range
        newValue = Math.max(min.get(), Math.min(max.get(), newValue));
        value.set(newValue);
        
        event.consume();
    }
    
    private void handleThumbReleased(MouseEvent event) {
        isDragging = false;
        thumb.setFill(THUMB_HOVER_COLOR);
        event.consume();
    }
    
    private void handleTrackClicked(MouseEvent event) {
        double clickY = event.getY();
        double thumbCenterY = thumb.getY() + thumb.getHeight() / 2;
        
        if (clickY < thumbCenterY) {
            // Clicked above thumb - page up
            pageUp();
        } else {
            // Clicked below thumb - page down
            pageDown();
        }
        
        event.consume();
    }
    
    private void scrollUp() {
        double newValue = value.get() - 1.0;
        value.set(Math.max(min.get(), newValue));
    }
    
    private void scrollDown() {
        double newValue = value.get() + 1.0;
        value.set(Math.min(max.get(), newValue));
    }
    
    private void pageUp() {
        double newValue = value.get() - visibleAmount.get();
        value.set(Math.max(min.get(), newValue));
    }
    
    private void pageDown() {
        double newValue = value.get() + visibleAmount.get();
        value.set(Math.min(max.get(), newValue));
    }
    
    // Property getters
    public DoubleProperty valueProperty() { return value; }
    public double getValue() { return value.get(); }
    public void setValue(double value) { this.value.set(value); }
    
    public DoubleProperty minProperty() { return min; }
    public double getMin() { return min.get(); }
    public void setMin(double min) { this.min.set(min); }
    
    public DoubleProperty maxProperty() { return max; }
    public double getMax() { return max.get(); }
    public void setMax(double max) { this.max.set(max); }
    
    public DoubleProperty visibleAmountProperty() { return visibleAmount; }
    public double getVisibleAmount() { return visibleAmount.get(); }
    public void setVisibleAmount(double visibleAmount) { this.visibleAmount.set(visibleAmount); }
}
