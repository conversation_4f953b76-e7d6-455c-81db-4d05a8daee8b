package com.restaurant.controller;

import javafx.animation.*;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.shape.Circle;
import javafx.scene.text.Text;
import javafx.util.Duration;

import java.net.URL;
import java.util.*;

import com.restaurant.service.GlobalVoiceAssistant;
import com.restaurant.model.VoiceIntent;

public class GlobalVoiceController implements Initializable, GlobalVoiceAssistant.VoiceAssistantCallback {

    // Header Components
    @FXML private VBox globalVoiceContainer;
    @FXML private Circle aiPulseRing;
    @FXML private Label contextBadge;
    @FXML private Text assistantStatus;
    
    // Main Voice Controls
    @FXML private Button mainVoiceButton;
    @FXML private Label mainVoiceIcon;
    @FXML private Tooltip mainVoiceTooltip;
    @FXML private Circle mainPulseRing;
    @FXML private MenuButton voiceSettingsMenu;
    @FXML private CheckMenuItem wakeWordEnabled;
    @FXML private CheckMenuItem continuousListening;
    @FXML private CheckMenuItem voiceFeedback;
    
    // Voice Input Area
    @FXML private VBox voiceInputArea;
    @FXML private TextArea liveTranscription;
    @FXML private Label confidenceIndicator;
    
    // Intent Display
    @FXML private HBox intentDisplay;
    @FXML private Label intentIcon;
    @FXML private Text intentCategory;
    @FXML private Label intentConfidence;
    @FXML private Text intentDescription;
    @FXML private Button executeIntentButton;
    
    // Action Buttons
    @FXML private Button confirmVoiceButton;
    @FXML private Button editCommandButton;
    @FXML private Button tryAgainVoiceButton;
    @FXML private Button cancelVoiceButton;
    
    // Suggestions
    @FXML private VBox suggestionsSection;
    @FXML private FlowPane contextualSuggestions;
    @FXML private Button refreshSuggestions;
    
    // Results and Errors
    @FXML private VBox executionResult;
    @FXML private Label executionIcon;
    @FXML private Text executionTitle;
    @FXML private Text executionDescription;
    @FXML private Button viewResultButton;
    
    @FXML private VBox errorDisplay;
    @FXML private Label errorIcon;
    @FXML private Text errorTitle;
    @FXML private Text errorDescription;
    @FXML private Button retryErrorButton;
    
    // Quick Actions
    @FXML private HBox quickActionsBar;
    @FXML private Button quickForecast;
    @FXML private Button quickSales;
    @FXML private Button quickTables;
    @FXML private Button quickMenu;
    @FXML private Button quickStock;
    
    // Voice Activity
    @FXML private HBox voiceActivityIndicator;
    @FXML private Circle activityDot1;
    @FXML private Circle activityDot2;
    @FXML private Circle activityDot3;

    // Voice Assistant
    private GlobalVoiceAssistant voiceAssistant;
    private VoiceIntent currentIntent;
    private Timeline pulseAnimation;
    private Timeline activityAnimation;
    private boolean isListening = false;
    private String lastTranscription = "";

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        voiceAssistant = GlobalVoiceAssistant.getInstance();
        voiceAssistant.setCallback(this);
        
        setupAnimations();
        setupEventHandlers();
        loadContextualSuggestions();
        updateContextBadge();
        
        // Initialize with welcome state
        assistantStatus.setText("Ready to help with restaurant operations");
    }

    private void setupAnimations() {
        // Main pulse animation
        pulseAnimation = new Timeline(
            new KeyFrame(Duration.ZERO, 
                new KeyValue(mainPulseRing.radiusProperty(), 30.0),
                new KeyValue(mainPulseRing.opacityProperty(), 1.0)),
            new KeyFrame(Duration.seconds(1.5), 
                new KeyValue(mainPulseRing.radiusProperty(), 45.0),
                new KeyValue(mainPulseRing.opacityProperty(), 0.0))
        );
        pulseAnimation.setCycleCount(Timeline.INDEFINITE);

        // Activity dots animation
        activityAnimation = new Timeline();
        Circle[] dots = {activityDot1, activityDot2, activityDot3};
        
        for (int i = 0; i < dots.length; i++) {
            final int index = i;
            KeyFrame frame = new KeyFrame(
                Duration.millis(300 * i),
                e -> animateActivityDot(dots[index])
            );
            activityAnimation.getKeyFrames().add(frame);
        }
        activityAnimation.setCycleCount(Timeline.INDEFINITE);
    }

    private void animateActivityDot(Circle dot) {
        Timeline dotAnimation = new Timeline(
            new KeyFrame(Duration.ZERO, new KeyValue(dot.scaleXProperty(), 1.0), new KeyValue(dot.scaleYProperty(), 1.0)),
            new KeyFrame(Duration.millis(150), new KeyValue(dot.scaleXProperty(), 1.5), new KeyValue(dot.scaleYProperty(), 1.5)),
            new KeyFrame(Duration.millis(300), new KeyValue(dot.scaleXProperty(), 1.0), new KeyValue(dot.scaleYProperty(), 1.0))
        );
        dotAnimation.play();
    }

    private void setupEventHandlers() {
        // Settings menu handlers
        wakeWordEnabled.selectedProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal) {
                startWakeWordDetection();
            } else {
                stopWakeWordDetection();
            }
        });

        continuousListening.selectedProperty().addListener((obs, oldVal, newVal) -> {
            voiceAssistant.setListening(newVal);
        });

        // Live transcription handler
        liveTranscription.textProperty().addListener((obs, oldText, newText) -> {
            if (newText != null && !newText.equals(lastTranscription)) {
                lastTranscription = newText;
                processLiveTranscription(newText);
            }
        });
    }

    @FXML
    private void handleMainVoiceInput() {
        if (isListening) {
            stopListening();
        } else {
            startListening();
        }
    }

    private void startListening() {
        isListening = true;
        voiceInputArea.setVisible(true);
        mainVoiceIcon.setText("🔴");
        mainVoiceTooltip.setText("Click to stop listening");
        assistantStatus.setText("Listening for your command...");
        
        // Start animations
        mainPulseRing.setVisible(true);
        pulseAnimation.play();
        
        // Start speech recognition simulation
        simulateSpeechRecognition();
    }

    private void stopListening() {
        isListening = false;
        mainVoiceIcon.setText("🎤");
        mainVoiceTooltip.setText("Click to speak or say 'Hey Restaurant'");
        assistantStatus.setText("Processing your command...");
        
        // Stop animations
        pulseAnimation.stop();
        mainPulseRing.setVisible(false);
        
        // Process the final transcription
        if (!liveTranscription.getText().trim().isEmpty()) {
            processVoiceCommand(liveTranscription.getText());
        }
    }

    private void simulateSpeechRecognition() {
        Task<String> speechTask = new Task<String>() {
            @Override
            protected String call() throws Exception {
                // Simulate real-time speech recognition
                String[] sampleCommands = {
                    "forecast sales for next 30 days",
                    "add two butter naan to table 5",
                    "show today's sales report",
                    "check stock for paneer",
                    "open table 6 order details",
                    "search chicken tikka in menu",
                    "show low stock items",
                    "compare this week with last week",
                    "navigate to billing section",
                    "merge table 3 and 4"
                };
                
                String command = sampleCommands[(int)(Math.random() * sampleCommands.length)];
                
                // Simulate typing effect
                for (int i = 0; i <= command.length(); i++) {
                    if (!isListening) break;

                    final String partial = command.substring(0, i);
                    final int currentIndex = i;
                    Platform.runLater(() -> {
                        if (isListening) {
                            liveTranscription.setText(partial);
                            updateConfidenceIndicator(Math.min(95, 60 + currentIndex * 2));
                        }
                    });
                    Thread.sleep(100);
                }
                
                Thread.sleep(1000); // Pause at end
                return command;
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    if (isListening) {
                        stopListening();
                    }
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    showError("Speech Recognition Failed", "Could not capture audio. Please check your microphone.");
                    stopListening();
                });
            }
        };

        new Thread(speechTask).start();
    }

    private void processLiveTranscription(String text) {
        if (text.length() > 5) {
            // Real-time intent recognition
            VoiceIntent intent = voiceAssistant.processVoiceCommand(text);
            if (intent.getConfidence() > 0.6) {
                showIntentPreview(intent);
            }
        }
    }

    private void processVoiceCommand(String command) {
        assistantStatus.setText("Processing command...");
        
        Task<VoiceIntent> processTask = new Task<VoiceIntent>() {
            @Override
            protected VoiceIntent call() throws Exception {
                Thread.sleep(1000); // Simulate processing time
                return voiceAssistant.processVoiceCommand(command);
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    VoiceIntent intent = getValue();
                    currentIntent = intent;
                    
                    if (intent.isExecutable()) {
                        showIntentResult(intent);
                        confirmVoiceButton.setDisable(false);
                    } else {
                        showError("Command Not Understood", 
                            "Could not understand: '" + command + "'. Please try rephrasing.");
                    }
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    showError("Processing Failed", "Could not process the voice command.");
                });
            }
        };

        new Thread(processTask).start();
    }

    private void showIntentPreview(VoiceIntent intent) {
        intentDisplay.setVisible(true);
        intentIcon.setText(intent.getCategoryIcon());
        intentCategory.setText(intent.getCategoryDisplayName());
        intentConfidence.setText(String.format("%.0f%%", intent.getConfidence() * 100));
        intentDescription.setText(intent.getDescription());
    }

    private void showIntentResult(VoiceIntent intent) {
        showIntentPreview(intent);
        assistantStatus.setText("Command recognized! Ready to execute.");
    }

    private void updateConfidenceIndicator(double confidence) {
        confidenceIndicator.setText(String.format("%.0f%%", confidence));
        
        if (confidence >= 80) {
            confidenceIndicator.getStyleClass().removeAll("confidence-low", "confidence-medium");
            confidenceIndicator.getStyleClass().add("confidence-high");
        } else if (confidence >= 60) {
            confidenceIndicator.getStyleClass().removeAll("confidence-low", "confidence-high");
            confidenceIndicator.getStyleClass().add("confidence-medium");
        } else {
            confidenceIndicator.getStyleClass().removeAll("confidence-high", "confidence-medium");
            confidenceIndicator.getStyleClass().add("confidence-low");
        }
    }

    @FXML
    private void executeRecognizedIntent() {
        if (currentIntent != null) {
            executeIntent(currentIntent);
        }
    }

    @FXML
    private void confirmAndExecute() {
        if (currentIntent != null) {
            executeIntent(currentIntent);
        }
    }

    private void executeIntent(VoiceIntent intent) {
        assistantStatus.setText("Executing command...");
        
        Task<String> executeTask = new Task<String>() {
            @Override
            protected String call() throws Exception {
                Thread.sleep(1500); // Simulate execution time
                return voiceAssistant.executeVoiceIntent(intent);
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    String result = getValue();
                    showExecutionSuccess(intent, result);
                    resetVoiceInterface();
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    showError("Execution Failed", "Could not execute the command.");
                });
            }
        };

        new Thread(executeTask).start();
    }

    private void showExecutionSuccess(VoiceIntent intent, String result) {
        executionResult.setVisible(true);
        executionIcon.setText("✅");
        executionTitle.setText("Command Executed Successfully");
        executionDescription.setText(result);
        assistantStatus.setText("Command completed successfully!");
        
        // Auto-hide after 5 seconds
        Timeline hideTimeline = new Timeline(
            new KeyFrame(Duration.seconds(5), e -> {
                executionResult.setVisible(false);
                assistantStatus.setText("Ready for next command");
            })
        );
        hideTimeline.play();
    }

    private void showError(String title, String description) {
        errorDisplay.setVisible(true);
        errorTitle.setText(title);
        errorDescription.setText(description);
        assistantStatus.setText("Error occurred. Please try again.");
        
        // Auto-hide after 5 seconds
        Timeline hideTimeline = new Timeline(
            new KeyFrame(Duration.seconds(5), e -> {
                errorDisplay.setVisible(false);
                assistantStatus.setText("Ready for next command");
            })
        );
        hideTimeline.play();
    }

    private void resetVoiceInterface() {
        voiceInputArea.setVisible(false);
        intentDisplay.setVisible(false);
        liveTranscription.clear();
        confirmVoiceButton.setDisable(true);
        currentIntent = null;
        lastTranscription = "";
    }

    @FXML
    private void editCommand() {
        liveTranscription.setEditable(true);
        liveTranscription.requestFocus();
        assistantStatus.setText("Edit your command and press Confirm");
    }

    @FXML
    private void tryAgainVoice() {
        resetVoiceInterface();
        startListening();
    }

    @FXML
    private void cancelVoiceInput() {
        stopListening();
        resetVoiceInterface();
        assistantStatus.setText("Voice input cancelled");
    }

    private void loadContextualSuggestions() {
        List<String> suggestions = voiceAssistant.getContextualSuggestions();
        updateSuggestions(suggestions);
    }

    @FXML
    private void refreshContextualSuggestions() {
        loadContextualSuggestions();
    }

    private void updateSuggestions(List<String> suggestions) {
        contextualSuggestions.getChildren().clear();
        
        for (String suggestion : suggestions) {
            Button suggestionButton = new Button(suggestion);
            suggestionButton.getStyleClass().add("contextual-suggestion-button");
            suggestionButton.setOnAction(e -> {
                liveTranscription.setText(suggestion);
                processVoiceCommand(suggestion);
            });
            contextualSuggestions.getChildren().add(suggestionButton);
        }
    }

    private void updateContextBadge() {
        String context = voiceAssistant.getCurrentContext();
        contextBadge.setText(context);
        
        // Update badge style based on context
        contextBadge.getStyleClass().removeAll("context-dashboard", "context-forecasting", 
            "context-billing", "context-tables", "context-menu", "context-reports");
        contextBadge.getStyleClass().add("context-" + context.toLowerCase());
    }

    // Quick Action Handlers
    @FXML
    private void quickForecastAction() {
        executeQuickCommand("forecast sales for next 30 days");
    }

    @FXML
    private void quickSalesAction() {
        executeQuickCommand("show today's sales");
    }

    @FXML
    private void quickTablesAction() {
        executeQuickCommand("go to table management");
    }

    @FXML
    private void quickMenuAction() {
        executeQuickCommand("search menu items");
    }

    @FXML
    private void quickStockAction() {
        executeQuickCommand("show low stock items");
    }

    private void executeQuickCommand(String command) {
        liveTranscription.setText(command);
        processVoiceCommand(command);
    }

    // Voice Assistant Callback Implementation
    @Override
    public void onCommandExecuted(VoiceIntent intent, String result) {
        Platform.runLater(() -> {
            showExecutionSuccess(intent, result);
        });
    }

    @Override
    public void onCommandFailed(String error) {
        Platform.runLater(() -> {
            showError("Command Failed", error);
        });
    }

    @Override
    public void onListeningStateChanged(boolean listening) {
        Platform.runLater(() -> {
            if (listening && wakeWordEnabled.isSelected()) {
                voiceActivityIndicator.setVisible(true);
                activityAnimation.play();
            } else {
                voiceActivityIndicator.setVisible(false);
                activityAnimation.stop();
            }
        });
    }

    @Override
    public void onContextChanged(String newContext) {
        Platform.runLater(() -> {
            updateContextBadge();
            loadContextualSuggestions();
        });
    }

    // Additional Methods
    private void startWakeWordDetection() {
        voiceAssistant.setListening(true);
        assistantStatus.setText("Listening for 'Hey Restaurant'...");
    }

    private void stopWakeWordDetection() {
        voiceAssistant.setListening(false);
        assistantStatus.setText("Wake word detection disabled");
    }

    @FXML
    private void openVoiceTraining() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Voice Training");
        alert.setHeaderText("Voice Training Module");
        alert.setContentText("Voice training feature would help improve recognition accuracy for your voice.\n\nThis feature is coming soon!");
        alert.showAndWait();
    }

    @FXML
    private void showCommandHistory() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Command History");
        alert.setHeaderText("Recent Voice Commands");
        alert.setContentText("Recent commands:\n• Forecast sales for next 30 days\n• Show today's sales\n• Add butter naan to table 5\n• Check stock for paneer\n\nFull history feature coming soon!");
        alert.showAndWait();
    }

    @FXML
    private void showVoiceHelp() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Voice Commands Help");
        alert.setHeaderText("Available Voice Commands");
        alert.setContentText(getVoiceHelpText());
        alert.showAndWait();
    }

    private String getVoiceHelpText() {
        return "🎤 GLOBAL VOICE COMMANDS:\n\n" +
               "📊 FORECASTING:\n" +
               "• \"Forecast sales for next 30 days\"\n" +
               "• \"Predict biryani sales next week\"\n" +
               "• \"Show last month's forecast\"\n\n" +
               "💰 BILLING & ORDERS:\n" +
               "• \"Add two butter naan to table 5\"\n" +
               "• \"Show table 3 bill\"\n" +
               "• \"Place order for table 2\"\n" +
               "• \"Remove fried rice from table 4\"\n\n" +
               "🪑 TABLE MANAGEMENT:\n" +
               "• \"Open table 6\"\n" +
               "• \"Close table 2\"\n" +
               "• \"Merge table 3 and 4\"\n\n" +
               "🔍 MENU SEARCH:\n" +
               "• \"Search chicken tikka\"\n" +
               "• \"Show vegetarian items\"\n" +
               "• \"Search beverages under 100 rupees\"\n\n" +
               "📈 REPORTS & ANALYTICS:\n" +
               "• \"Show today's sales\"\n" +
               "• \"Open item-wise report\"\n" +
               "• \"Compare last week vs this week\"\n\n" +
               "📦 INVENTORY:\n" +
               "• \"Check stock for paneer\"\n" +
               "• \"Show low stock items\"\n" +
               "• \"Update stock for cheese to 20 kg\"\n\n" +
               "🧭 NAVIGATION:\n" +
               "• \"Go to dashboard\"\n" +
               "• \"Open reports\"\n" +
               "• \"Reset filters\"\n" +
               "• \"Log out\"\n\n" +
               "💡 TIPS:\n" +
               "• Speak clearly and at normal pace\n" +
               "• Use specific numbers and dates\n" +
               "• Say 'Hey Restaurant' for wake word activation";
    }

    @FXML
    private void viewExecutionResult() {
        // This would navigate to the relevant section to show the result
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("View Result");
        alert.setHeaderText("Command Result");
        alert.setContentText("This would navigate to the relevant section to show the detailed result of the executed command.");
        alert.showAndWait();
    }

    @FXML
    private void retryFailedCommand() {
        errorDisplay.setVisible(false);
        if (currentIntent != null) {
            executeIntent(currentIntent);
        } else {
            tryAgainVoice();
        }
    }
}
