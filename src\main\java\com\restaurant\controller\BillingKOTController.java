package com.restaurant.controller;

import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.input.KeyCode;
import javafx.animation.Timeline;
import javafx.animation.KeyFrame;
import javafx.util.Duration;
import java.util.Optional;
import java.util.stream.Collectors;
import javafx.scene.Node;
import javafx.scene.layout.VBox;
import javafx.scene.layout.HBox;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.Region;
import javafx.scene.layout.Priority;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import com.restaurant.model.TableStatus;
import com.restaurant.model.OrderDAO;
import com.restaurant.model.Order;
import com.restaurant.model.OrderItem;
import com.restaurant.model.MenuItem;
import com.restaurant.util.PrintService;
import com.restaurant.util.MenuShortcuts;
import com.restaurant.service.OrderManager;
import com.restaurant.util.SceneEventHandlerManager;
import com.restaurant.util.UniversalNavigationManager;
import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;
import java.time.format.DateTimeFormatter;

public class BillingKOTController {

    // Table View Controls
    @FXML private GridPane groundFloorGrid;
    @FXML private GridPane partyHallGrid;
    @FXML private ToggleButton deliveryToggle;
    @FXML private ToggleButton pickUpToggle;
    @FXML private Button tableReservationBtn;
    @FXML private Button contestationsBtn;
    @FXML private ComboBox<String> fromTableCombo;
    @FXML private ComboBox<String> toTableCombo;
    @FXML private Button moveKOTBtn;

    // Configuration Controls
    @FXML private CheckBox enableGSTCheckbox;
    @FXML private Spinner<Double> gstRateSpinner;
    @FXML private TextField gstNumberField;
    @FXML private CheckBox enableServiceChargeCheckbox;
    @FXML private Spinner<Double> serviceChargeRateSpinner;
    @FXML private ComboBox<String> invoicePrinterCombo;
    @FXML private ComboBox<String> kotPrinterCombo;
    
    // Preview Controls
    @FXML private ComboBox<String> sampleOrderCombo;
    @FXML private VBox invoicePreview;
    @FXML private Label gstNumberLabel;
    @FXML private TableView<PreviewItemRecord> previewItemsTable;
    @FXML private TableColumn<PreviewItemRecord, String> previewItemColumn;
    @FXML private TableColumn<PreviewItemRecord, Integer> previewQtyColumn;
    @FXML private TableColumn<PreviewItemRecord, String> previewPriceColumn;
    @FXML private TableColumn<PreviewItemRecord, String> previewTotalColumn;
    @FXML private Label previewSubtotal;
    @FXML private HBox previewGSTRow;
    @FXML private Label previewGSTLabel;
    @FXML private Label previewGSTAmount;
    @FXML private HBox previewServiceChargeRow;

    // Order Details and Billing Controls
    @FXML private VBox orderDetailsSection;
    @FXML private Label orderDetailsTitle;
    @FXML private Label orderIdLabel;
    @FXML private Label tableNumberLabel;
    @FXML private Label orderTimeLabel;
    @FXML private Label orderStatusLabel;
    @FXML private ComboBox<String> paymentMethodCombo;
    @FXML private TextField discountField;
    @FXML private TableView<OrderItem> orderItemsTable;
    @FXML private TableColumn<OrderItem, String> itemNameColumn;
    @FXML private TableColumn<OrderItem, Integer> itemQuantityColumn;
    @FXML private TableColumn<OrderItem, Double> itemPriceColumn;
    @FXML private TableColumn<OrderItem, Double> itemTotalColumn;
    @FXML private Label subtotalLabel;
    @FXML private Label gstLabel;
    @FXML private Label serviceChargeLabel;
    @FXML private Label grandTotalLabel;
    @FXML private Button generateBillBtn;
    @FXML private Button markPaidBtn;

    // Current selected order for billing
    private Order selectedOrder;

    // Order manager for persistent orders
    private OrderManager orderManager = OrderManager.getInstance();
    @FXML private Label previewServiceChargeLabel;
    @FXML private Label previewServiceChargeAmount;
    @FXML private Label previewGrandTotal;
    
    private ObservableList<PreviewItemRecord> previewItems = FXCollections.observableArrayList();
    
    @FXML
    private void initialize() {
        System.out.println("BillingKOTController: Starting simplified initialization...");

        // Reset state to ensure clean initialization
        resetControllerState();

        // Use Platform.runLater to prevent hanging during FXML loading
        Platform.runLater(() -> {
            try {
                System.out.println("BillingKOTController: Setting up basic components...");

                // Only do essential setup, defer complex operations
                setupBasicComponents();

                System.out.println("BillingKOTController: Basic initialization complete");

                // Register with universal navigation manager
                UniversalNavigationManager.getInstance().setCurrentController("BillingKOTController");

                // Schedule complex operations for later
                scheduleComplexInitialization();

                // Setup keyboard shortcuts for table selection
                setupKeyboardShortcuts();

            } catch (Exception e) {
                System.err.println("Error in BillingKOTController initialization: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    /**
     * Reset controller state to ensure clean initialization
     */
    private void resetControllerState() {
        try {
            System.out.println("BillingKOTController: Resetting controller state...");

            // Clean up any existing event handlers for this controller
            // Use cleanupController which will preserve dashboard shortcuts
            SceneEventHandlerManager.getInstance().cleanupController("BillingKOTController");

            // Clear table selection state
            clearTableSelection();

            // Reset current billing table
            currentBillingTableNumber = 0;

            // Clear selected order
            selectedOrder = null;

            System.out.println("BillingKOTController: Controller state reset complete");

        } catch (Exception e) {
            System.err.println("BillingKOTController: Error resetting state: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void setupBasicComponents() {
        // Minimal setup to prevent hanging
        System.out.println("BillingKOT: Basic components setup complete");
    }

    /**
     * Setup keyboard shortcuts for table selection
     */
    private void setupKeyboardShortcuts() {
        // Wait for scene to be available
        Platform.runLater(() -> {
            try {
                // Get the root node to attach keyboard listeners
                if (groundFloorGrid != null && groundFloorGrid.getScene() != null) {
                    javafx.scene.Scene scene = groundFloorGrid.getScene();

                    // Use centralized event handler manager
                    SceneEventHandlerManager manager = SceneEventHandlerManager.getInstance();

                    // Register key event handler for table number input
                    manager.registerKeyHandler(scene, "BillingKOTController", this::handleKeyPressed);

                    // Make sure the scene can receive focus
                    if (groundFloorGrid != null) {
                        groundFloorGrid.setFocusTraversable(true);
                        groundFloorGrid.requestFocus();
                    }

                    System.out.println("BillingKOT: ✅ Keyboard shortcuts setup complete - users can type table number + Enter");
                } else {
                    // Retry after a short delay if scene is not ready
                    System.out.println("BillingKOT: ⏳ Scene not ready, retrying keyboard setup...");
                    Platform.runLater(() -> {
                        try {
                            Thread.sleep(100);
                            setupKeyboardShortcuts();
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                    });
                }
            } catch (Exception e) {
                System.err.println("BillingKOT: ❌ Error setting up keyboard shortcuts: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    /**
     * Clean up keyboard event handlers to prevent conflicts
     */
    private void cleanupKeyboardHandlers(javafx.scene.Scene scene) {
        try {
            System.out.println("BillingKOTController: Cleaning up keyboard handlers...");

            // Remove any existing key event handlers
            scene.setOnKeyPressed(null);

            System.out.println("BillingKOTController: Keyboard handlers cleaned up");

        } catch (Exception e) {
            System.err.println("BillingKOTController: Error cleaning up keyboard handlers: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // Buffer for collecting typed digits
    private StringBuilder tableNumberBuffer = new StringBuilder();
    private long lastKeyPressTime = 0;
    private static final long KEY_TIMEOUT_MS = 2000; // 2 seconds timeout

    /**
     * Handle key press events for table selection (same as See Tables functionality)
     */
    private void handleKeyPressed(javafx.scene.input.KeyEvent event) {
        try {
            System.out.println("BillingKOT: 🎹 Key pressed: " + event.getCode() + " | Text: '" + event.getText() + "' | Source: " + event.getSource().getClass().getSimpleName());

            switch (event.getCode()) {
                case DIGIT1: case NUMPAD1:
                    System.out.println("BillingKOT: 🔢 Digit 1 detected");
                    handleNumberKey(1);
                    event.consume();
                    break;
                case DIGIT2: case NUMPAD2:
                    System.out.println("BillingKOT: 🔢 Digit 2 detected");
                    handleNumberKey(2);
                    event.consume();
                    break;
                case DIGIT3: case NUMPAD3:
                    System.out.println("BillingKOT: 🔢 Digit 3 detected");
                    handleNumberKey(3);
                    event.consume();
                    break;
                case DIGIT4: case NUMPAD4:
                    System.out.println("BillingKOT: 🔢 Digit 4 detected");
                    handleNumberKey(4);
                    event.consume();
                    break;
                case DIGIT5: case NUMPAD5:
                    System.out.println("BillingKOT: 🔢 Digit 5 detected");
                    handleNumberKey(5);
                    event.consume();
                    break;
                case DIGIT6: case NUMPAD6:
                    System.out.println("BillingKOT: 🔢 Digit 6 detected");
                    handleNumberKey(6);
                    event.consume();
                    break;
                case DIGIT7: case NUMPAD7:
                    System.out.println("BillingKOT: 🔢 Digit 7 detected");
                    handleNumberKey(7);
                    event.consume();
                    break;
                case DIGIT8: case NUMPAD8:
                    System.out.println("BillingKOT: 🔢 Digit 8 detected");
                    handleNumberKey(8);
                    event.consume();
                    break;
                case DIGIT9: case NUMPAD9:
                    System.out.println("BillingKOT: 🔢 Digit 9 detected");
                    handleNumberKey(9);
                    event.consume();
                    break;
                case DIGIT0: case NUMPAD0:
                    System.out.println("BillingKOT: 🔢 Digit 0 detected");
                    handleNumberKey(0);
                    event.consume();
                    break;
                case ENTER:
                    System.out.println("BillingKOT: ⏎ Enter key detected");
                    handleEnterKey();
                    event.consume();
                    break;
                case ESCAPE:
                    System.out.println("BillingKOT: ⎋ Escape key detected");
                    // Use universal navigation for ESC handling
                    UniversalNavigationManager.getInstance().handleEscapeKey();
                    event.consume();
                    break;
                case BACK_SPACE:
                    System.out.println("BillingKOT: ⌫ Backspace key detected");
                    handleBackspace();
                    event.consume();
                    break;
                case H:
                    // Check for Ctrl+H shortcut for Hold button
                    if (event.isControlDown()) {
                        System.out.println("BillingKOT: 🔥 Ctrl+H detected - Opening Hold Order dialog");
                        Platform.runLater(() -> processHoldOrder());
                        event.consume();
                    } else {
                        System.out.println("BillingKOT: ➡️ H key passed through (no Ctrl modifier)");
                    }
                    break;
                default:
                    // Handle unknown Ctrl shortcuts gracefully to prevent freezing
                    if (event.isControlDown()) {
                        handleUnknownCtrlShortcut(event);
                        event.consume(); // Consume to prevent further processing
                    } else {
                        // Don't consume other keys - let them pass through
                        System.out.println("BillingKOT: ➡️ Other key passed through: " + event.getCode());
                    }
                    break;
            }

        } catch (Exception e) {
            System.err.println("BillingKOT: ❌ Error handling key press: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Handle unknown Ctrl shortcuts gracefully to prevent UI freezing
     */
    private void handleUnknownCtrlShortcut(javafx.scene.input.KeyEvent event) {
        try {
            String shortcut = "Ctrl+" + event.getCode().toString();
            System.out.println("BillingKOT: ⚠️ Unknown shortcut detected: " + shortcut);

            // Log the attempt but don't show error to user unless it's a common mistake
            if (isCommonMistake(event.getCode())) {
                Platform.runLater(() -> {
                    try {
                        // Show a brief, non-blocking notification for common mistakes
                        showBriefNotification("Unknown shortcut: " + shortcut + "\nPress F1 for help");
                    } catch (Exception e) {
                        System.err.println("Error showing unknown shortcut notification: " + e.getMessage());
                    }
                });
            }

            // Always consume the event to prevent further processing and potential freezing
            event.consume();

        } catch (Exception e) {
            System.err.println("Error handling unknown Ctrl shortcut: " + e.getMessage());
            e.printStackTrace();
            // Still consume the event even if error handling fails
            event.consume();
        }
    }

    /**
     * Check if the key combination is a common mistake users might make
     */
    private boolean isCommonMistake(javafx.scene.input.KeyCode keyCode) {
        // Common letters that users might accidentally press with Ctrl
        switch (keyCode) {
            case M: case N: case Q: case W: case E: case R: case Y: case U: case I: case O:
            case A: case F: case G: case J: case L: case Z: case X: case C: case V: case B:
                return true;
            default:
                return false;
        }
    }

    /**
     * Show a brief, non-blocking notification that doesn't interfere with workflow
     */
    private void showBriefNotification(String message) {
        try {
            // Create a simple, non-modal notification
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("Keyboard Shortcut");
            alert.setHeaderText(null);
            alert.setContentText(message);

            // Make it non-blocking and auto-close
            alert.show();

            // Auto-close after 3 seconds
            Timeline timeline = new Timeline(new KeyFrame(Duration.seconds(3), e -> {
                try {
                    alert.close();
                } catch (Exception ex) {
                    System.err.println("Error auto-closing notification: " + ex.getMessage());
                }
            }));
            timeline.play();

        } catch (Exception e) {
            System.err.println("Error creating brief notification: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // Current selected table number
    private int selectedTableNumber = 0;

    /**
     * Handle number key press for table selection (same as See Tables)
     */
    private void handleNumberKey(int digit) {
        // Add digit to buffer
        tableNumberBuffer.append(digit);

        // Parse current number
        try {
            selectedTableNumber = Integer.parseInt(tableNumberBuffer.toString());
            System.out.println("BillingKOT: Table number buffer: " + tableNumberBuffer.toString() + " -> " + selectedTableNumber);

            // Update visual feedback (optional - could highlight the table)
            updateTableSelectionVisual(selectedTableNumber);

        } catch (NumberFormatException e) {
            // Clear buffer if invalid
            clearTableSelection();
        }
    }

    /**
     * Handle Enter key press to open selected table (same as See Tables)
     */
    private void handleEnterKey() {
        if (selectedTableNumber > 0) {
            System.out.println("BillingKOT: ✅ ENTER pressed - Opening table " + selectedTableNumber + " for menu selection...");

            // Store the table number to avoid race conditions
            final int tableNum = selectedTableNumber;

            // Clear selection immediately to prevent double-clicks
            clearTableSelection();

            // Run on JavaFX Application Thread to ensure proper navigation
            javafx.application.Platform.runLater(() -> {
                try {
                    System.out.println("BillingKOT: 🚀 Starting table selection for Table " + tableNum);
                    selectTable(tableNum);
                    System.out.println("BillingKOT: ✅ Table selection completed for Table " + tableNum);
                } catch (Exception e) {
                    System.err.println("BillingKOT: ❌ Error in handleEnterKey for Table " + tableNum + ": " + e.getMessage());
                    e.printStackTrace();
                    showAlert("Navigation Error", "Failed to open table " + tableNum + ": " + e.getMessage());
                }
            });
        } else {
            System.out.println("BillingKOT: ⚠️ No table selected");
            showAlert("No Selection", "Please select a table number first (e.g., press 1 for Table 1)");
        }
    }

    /**
     * Handle backspace to remove last digit
     */
    private void handleBackspace() {
        if (tableNumberBuffer.length() > 0) {
            tableNumberBuffer.setLength(tableNumberBuffer.length() - 1);

            if (tableNumberBuffer.length() > 0) {
                try {
                    selectedTableNumber = Integer.parseInt(tableNumberBuffer.toString());
                    System.out.println("BillingKOT: Table number buffer: " + tableNumberBuffer.toString() + " -> " + selectedTableNumber);
                    updateTableSelectionVisual(selectedTableNumber);
                } catch (NumberFormatException e) {
                    clearTableSelection();
                }
            } else {
                clearTableSelection();
            }
        }
    }

    /**
     * Clear table selection
     */
    private void clearTableSelection() {
        tableNumberBuffer.setLength(0);
        selectedTableNumber = 0;
        System.out.println("BillingKOT: Table selection cleared");
        // Clear any visual highlights
        updateTableSelectionVisual(0);
    }

    /**
     * Update visual feedback for selected table (optional)
     */
    private void updateTableSelectionVisual(int tableNumber) {
        // This could highlight the selected table button
        // For now, just log the selection
        if (tableNumber > 0) {
            System.out.println("BillingKOT: Table " + tableNumber + " visually selected");
        } else {
            System.out.println("BillingKOT: Table selection visual cleared");
        }
    }

    /**
     * Select a table by number - check if it has running order or is empty
     */
    private void selectTable(int tableNumber) {
        try {
            System.out.println("BillingKOT: 🔍 Checking table " + tableNumber + " status...");

            // Validate table number range
            if (tableNumber < 1 || tableNumber > 26) {
                showAlert("Invalid Table", "Table number must be between 1 and 26.");
                return;
            }

            // Check if table has an active/running order using OrderManager (faster than database)
            Order activeOrder = orderManager.getOrderForTable(tableNumber);

            if (activeOrder != null && !activeOrder.getItems().isEmpty()) {
                // Table has running order - show billing interface with existing items
                System.out.println("BillingKOT: ✅ Table " + tableNumber + " has running order with " +
                                 activeOrder.getItems().size() + " items (Total: ₹" +
                                 String.format("%.2f", activeOrder.calculateTotal()) + ") - showing billing interface");
                showBillingForRunningOrder(tableNumber, activeOrder);
            } else {
                // Table is empty - show menu selection for new order
                System.out.println("BillingKOT: 📝 Table " + tableNumber + " is empty - showing menu selection for new order");
                navigateToMenu("Table " + tableNumber);
            }

        } catch (Exception e) {
            System.err.println("BillingKOT: ❌ Error selecting table " + tableNumber + ": " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Failed to select table " + tableNumber + ": " + e.getMessage());
        }
    }

    /**
     * Show billing interface for a table with running order
     */
    private void showBillingForRunningOrder(int tableNumber, Order activeOrder) {
        try {
            System.out.println("BillingKOT: 💰 Opening billing interface for Table " + tableNumber + " with running order");
            System.out.println("BillingKOT: 📋 Order has " + activeOrder.getItems().size() + " items, Total: ₹" +
                             String.format("%.2f", activeOrder.calculateTotal()));

            // Set current table number for billing operations
            this.currentBillingTableNumber = tableNumber;

            // Create the billing dialog interface (like in the user's screenshot)
            showBillingDialog(tableNumber, activeOrder);

        } catch (Exception e) {
            System.err.println("BillingKOT: ❌ Error opening billing interface: " + e.getMessage());
            e.printStackTrace();
            showAlert("Billing Error", "Failed to open billing for Table " + tableNumber + ": " + e.getMessage());
        }
    }

    /**
     * Show the billing dialog interface that matches the user's screenshot
     */
    private void showBillingDialog(int tableNumber, Order activeOrder) {
        try {
            System.out.println("BillingKOT: 🎯 Creating billing dialog for Table " + tableNumber);

            // Create a large dialog that mimics the user's screenshot interface
            Dialog<ButtonType> dialog = new Dialog<>();
            dialog.setTitle("Table " + tableNumber + " - Order Management & Billing");
            dialog.setHeaderText(null);
            dialog.setResizable(true);

            // Create main layout (horizontal split like in the screenshot)
            HBox mainLayout = new HBox(20);
            mainLayout.setPrefWidth(1200);
            mainLayout.setPrefHeight(700);
            mainLayout.setPadding(new Insets(20));

            // LEFT SIDE: Menu Categories and Items (like in the screenshot)
            VBox leftSide = createMenuSide();
            leftSide.setPrefWidth(500);

            // RIGHT SIDE: Order Details with Payment Options (like in the screenshot)
            VBox rightSide = createOrderSideForBilling(tableNumber, activeOrder);
            rightSide.setPrefWidth(600);

            mainLayout.getChildren().addAll(leftSide, rightSide);

            // Set the dialog content
            dialog.getDialogPane().setContent(mainLayout);

            // Add custom buttons
            ButtonType closeButtonType = new ButtonType("Close", ButtonBar.ButtonData.CANCEL_CLOSE);
            dialog.getDialogPane().getButtonTypes().addAll(closeButtonType);

            // Style the dialog
            dialog.getDialogPane().getStylesheets().add(getClass().getResource("/css/application.css").toExternalForm());
            dialog.getDialogPane().getStyleClass().add("billing-dialog");

            // Add keyboard shortcut handling to the billing dialog
            setupBillingDialogKeyboardShortcuts(dialog, tableNumber);

            System.out.println("BillingKOT: ✅ Billing dialog created successfully");

            // Show the dialog with proper error handling
            Platform.runLater(() -> {
                try {
                    dialog.showAndWait();
                } catch (Exception ex) {
                    System.err.println("Error showing billing dialog: " + ex.getMessage());
                    ex.printStackTrace();
                }
            });

        } catch (Exception e) {
            System.err.println("BillingKOT: ❌ Error creating billing dialog: " + e.getMessage());
            e.printStackTrace();
            showAlert("Dialog Error", "Failed to create billing dialog: " + e.getMessage());
        }
    }

    /**
     * Setup keyboard shortcuts for the billing dialog
     */
    private void setupBillingDialogKeyboardShortcuts(Dialog<ButtonType> dialog, int tableNumber) {
        try {
            System.out.println("BillingKOT: 🎹 Setting up keyboard shortcuts for billing dialog");

            // Add keyboard event handler to the dialog pane
            dialog.getDialogPane().setOnKeyPressed(event -> {
                try {
                    System.out.println("BillingKOT: 🎹 Key pressed in billing dialog: " + event.getCode() +
                                     " | Ctrl: " + event.isControlDown());

                    switch (event.getCode()) {
                        case H:
                            // Check for Ctrl+H shortcut for Hold button
                            if (event.isControlDown()) {
                                System.out.println("BillingKOT: 🔥 Ctrl+H detected in billing dialog - Opening Hold Order dialog");
                                event.consume();

                                // Process hold order for the current table
                                Platform.runLater(() -> {
                                    try {
                                        processHoldOrderForTable(tableNumber);
                                    } catch (Exception e) {
                                        System.err.println("Error processing hold order from billing dialog: " + e.getMessage());
                                        e.printStackTrace();
                                    }
                                });
                            }
                            break;
                        case ESCAPE:
                            // ESC key to close dialog
                            System.out.println("BillingKOT: ⎋ ESC pressed in billing dialog - closing");
                            event.consume();
                            dialog.close();
                            break;
                        default:
                            // Handle unknown Ctrl shortcuts gracefully
                            if (event.isControlDown() && !event.getCode().isModifierKey()) {
                                handleUnknownCtrlShortcut(event);
                            }
                            break;
                    }

                } catch (Exception e) {
                    System.err.println("BillingKOT: ❌ Error handling key press in billing dialog: " + e.getMessage());
                    e.printStackTrace();
                }
            });

            // Make sure the dialog pane can receive focus for keyboard events
            dialog.getDialogPane().setFocusTraversable(true);

            // Request focus when dialog is shown
            Platform.runLater(() -> {
                try {
                    dialog.getDialogPane().requestFocus();
                    System.out.println("BillingKOT: ✅ Billing dialog keyboard shortcuts setup complete");
                } catch (Exception e) {
                    System.err.println("Error requesting focus for billing dialog: " + e.getMessage());
                }
            });

        } catch (Exception e) {
            System.err.println("BillingKOT: ❌ Error setting up billing dialog keyboard shortcuts: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Process hold order for a specific table
     */
    private void processHoldOrderForTable(int tableNumber) {
        try {
            System.out.println("BillingKOT: 🔥 Processing hold order for Table " + tableNumber);

            // Set the current table number for the hold operation
            this.currentBillingTableNumber = tableNumber;

            // Call the existing hold order processing method
            processHoldOrder();

        } catch (Exception e) {
            System.err.println("BillingKOT: ❌ Error processing hold order for table " + tableNumber + ": " + e.getMessage());
            e.printStackTrace();
            showAlert("Hold Order Error", "Failed to process hold order for Table " + tableNumber + ": " + e.getMessage());
        }
    }

    /**
     * Create the right side of the billing dialog showing order details and payment options
     * This matches the layout in the user's screenshot
     */
    private VBox createOrderSideForBilling(int tableNumber, Order order) {
        VBox orderSide = new VBox(15);
        orderSide.setStyle("-fx-background-color: white; -fx-padding: 20; -fx-border-color: #dee2e6; -fx-border-width: 1;");

        // Header with table info (like in screenshot)
        VBox headerBox = new VBox(5);
        Label tableLabel = new Label("Table " + tableNumber);
        tableLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        Label dateLabel = new Label("Date and Time: " + java.time.LocalDateTime.now().format(
            java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
        dateLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #6c757d;");

        // Service type buttons (like in screenshot)
        HBox serviceButtons = new HBox(10);
        Button dineInBtn = new Button("Dine In");
        Button deliveryBtn = new Button("Delivery");
        Button pickUpBtn = new Button("Pick Up");

        dineInBtn.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 4 8;");
        deliveryBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 4 8;");
        pickUpBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 4 8;");

        serviceButtons.getChildren().addAll(dineInBtn, deliveryBtn, pickUpBtn);
        headerBox.getChildren().addAll(tableLabel, dateLabel, serviceButtons);

        // Customer info section (like in screenshot)
        VBox customerBox = new VBox(5);
        Label mobileLabel = new Label("Mobile:");
        TextField mobileField = new TextField("9044142334");
        mobileField.setStyle("-fx-font-size: 12px;");

        Label nameLabel = new Label("Name:");
        TextField nameField = new TextField("Bharat Malik");
        nameField.setStyle("-fx-font-size: 12px;");

        customerBox.getChildren().addAll(mobileLabel, mobileField, nameLabel, nameField);

        // Items header (like in screenshot)
        Label itemsHeader = new Label("ITEMS");
        itemsHeader.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // Items table header row (like in screenshot)
        HBox itemsTableHeader = new HBox();
        itemsTableHeader.setStyle("-fx-background-color: #f8f9fa; -fx-padding: 8; -fx-border-color: #dee2e6; -fx-border-width: 0 0 1 0;");

        Label itemsCol = new Label("ITEMS");
        itemsCol.setPrefWidth(150);
        itemsCol.setStyle("-fx-font-weight: bold; -fx-font-size: 11px;");

        Label checkCol = new Label("CHECK");
        checkCol.setPrefWidth(60);
        checkCol.setStyle("-fx-font-weight: bold; -fx-font-size: 11px; -fx-alignment: center;");

        Label qtyCol = new Label("QTY.");
        qtyCol.setPrefWidth(60);
        qtyCol.setStyle("-fx-font-weight: bold; -fx-font-size: 11px; -fx-alignment: center;");

        Label deleteCol = new Label("DELETE");
        deleteCol.setPrefWidth(60);
        deleteCol.setStyle("-fx-font-weight: bold; -fx-font-size: 11px; -fx-alignment: center;");

        Label priceCol = new Label("PRICE");
        priceCol.setPrefWidth(80);
        priceCol.setStyle("-fx-font-weight: bold; -fx-font-size: 11px; -fx-alignment: center-right;");

        itemsTableHeader.getChildren().addAll(itemsCol, checkCol, qtyCol, deleteCol, priceCol);

        // Order items container (scrollable like in screenshot)
        VBox orderItemsContainer = new VBox(2);
        ScrollPane itemsScrollPane = new ScrollPane(orderItemsContainer);
        itemsScrollPane.setPrefHeight(200);
        itemsScrollPane.setStyle("-fx-background-color: white; -fx-border-color: #dee2e6; -fx-border-width: 1;");
        itemsScrollPane.setFitToWidth(true);

        // Add order items (like in screenshot)
        for (OrderItem item : order.getItems()) {
            HBox itemRow = createBillingOrderItemRow(item);
            orderItemsContainer.getChildren().add(itemRow);
        }

        // Payment options section (like in screenshot)
        VBox paymentSection = createBillingPaymentSection();

        // Total section (like in screenshot)
        VBox totalBox = new VBox(5);
        totalBox.setStyle("-fx-background-color: #f8f9fa; -fx-padding: 10; -fx-border-color: #dee2e6; -fx-border-width: 1;");

        Label totalLabel = new Label("Total: ₹" + String.format("%.2f", order.calculateTotal()));
        totalLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #dc3545;");

        totalBox.getChildren().add(totalLabel);

        orderSide.getChildren().addAll(headerBox, customerBox, itemsHeader, itemsTableHeader, itemsScrollPane, paymentSection, totalBox);
        return orderSide;
    }

    /**
     * Create an order item row for the billing interface (like in screenshot)
     */
    private HBox createBillingOrderItemRow(OrderItem item) {
        HBox row = new HBox();
        row.setStyle("-fx-padding: 8; -fx-border-color: #dee2e6; -fx-border-width: 0 0 1 0;");
        row.setAlignment(Pos.CENTER_LEFT);

        // Item name
        Label itemName = new Label(item.getMenuItem().getName());
        itemName.setPrefWidth(150);
        itemName.setStyle("-fx-font-size: 12px;");

        // Check mark (like in screenshot)
        Label checkMark = new Label("✓");
        checkMark.setPrefWidth(60);
        checkMark.setStyle("-fx-font-size: 12px; -fx-text-fill: #28a745; -fx-alignment: center;");

        // Quantity with +/- buttons (like in screenshot)
        HBox qtyBox = new HBox(2);
        qtyBox.setPrefWidth(60);
        qtyBox.setAlignment(Pos.CENTER);

        Button minusBtn = new Button("-");
        minusBtn.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 2 6; -fx-min-width: 20;");
        minusBtn.setOnAction(e -> updateItemQuantity(item, -1));

        Label qtyLabel = new Label(String.valueOf(item.getQuantity()));
        qtyLabel.setStyle("-fx-font-size: 12px; -fx-min-width: 20; -fx-alignment: center;");

        Button plusBtn = new Button("+");
        plusBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 2 6; -fx-min-width: 20;");
        plusBtn.setOnAction(e -> updateItemQuantity(item, 1));

        qtyBox.getChildren().addAll(minusBtn, qtyLabel, plusBtn);

        // Delete button (like in screenshot)
        Button deleteBtn = new Button("✕");
        deleteBtn.setPrefWidth(60);
        deleteBtn.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 4 8; -fx-alignment: center;");
        deleteBtn.setOnAction(e -> removeOrderItem(item));

        // Price
        Label priceLabel = new Label(String.format("%.2f", item.getTotalPrice()));
        priceLabel.setPrefWidth(80);
        priceLabel.setStyle("-fx-font-size: 12px; -fx-alignment: center-right;");

        row.getChildren().addAll(itemName, checkMark, qtyBox, deleteBtn, priceLabel);
        return row;
    }

    /**
     * Create payment options section (like in screenshot)
     */
    private VBox createBillingPaymentSection() {
        VBox paymentSection = new VBox(10);
        paymentSection.setStyle("-fx-padding: 10 0;");

        Label paymentLabel = new Label("PAYMENT OPTIONS");
        paymentLabel.setStyle("-fx-font-size: 12px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // Payment method buttons (like in screenshot)
        HBox paymentMethodsRow = new HBox(8);
        paymentMethodsRow.setAlignment(Pos.CENTER_LEFT);

        Button cashBtn = new Button("Cash");
        cashBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 6 12; -fx-font-weight: bold;");
        cashBtn.setOnAction(e -> processCashPayment());

        Button cardBtn = new Button("Card");
        cardBtn.setStyle("-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 6 12; -fx-font-weight: bold;");
        cardBtn.setOnAction(e -> processCardPayment());

        Button upiBtn = new Button("UPI");
        upiBtn.setStyle("-fx-background-color: #6f42c1; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 6 12; -fx-font-weight: bold;");
        upiBtn.setOnAction(e -> processUPIPayment());

        Button dueBtn = new Button("Due");
        dueBtn.setStyle("-fx-background-color: #fd7e14; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 6 12; -fx-font-weight: bold;");
        dueBtn.setOnAction(e -> processDuePayment());

        Button partBtn = new Button("Part Payment");
        partBtn.setStyle("-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 6 12; -fx-font-weight: bold;");
        partBtn.setOnAction(e -> processPartPayment());

        Button holdBtn = new Button("Hold");
        holdBtn.setStyle("-fx-background-color: #fd7e14; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 6 12; -fx-font-weight: bold;");
        holdBtn.setOnAction(e -> processHoldOrder());

        paymentMethodsRow.getChildren().addAll(cashBtn, cardBtn, upiBtn, dueBtn, partBtn, holdBtn);

        // Additional options row (like in screenshot)
        HBox additionalOptionsRow = new HBox(12);
        additionalOptionsRow.setAlignment(Pos.CENTER_LEFT);

        CheckBox loyaltyCheckBox = new CheckBox("Loyalty Customer");
        loyaltyCheckBox.setStyle("-fx-font-size: 10px;");

        Button smsBtn = new Button("Send SMS Feedback");
        smsBtn.setStyle("-fx-background-color: #20c997; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 4 8;");

        additionalOptionsRow.getChildren().addAll(loyaltyCheckBox, smsBtn);

        // Special offers row (like in screenshot)
        HBox offersRow = new HBox(8);
        offersRow.setAlignment(Pos.CENTER_LEFT);

        Button bogoBtn = new Button("BOGO Offer");
        bogoBtn.setStyle("-fx-background-color: #e83e8c; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 4 8;");

        Button splitBtn = new Button("Split Bill");
        splitBtn.setStyle("-fx-background-color: #6f42c1; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 4 8;");

        Button compBtn = new Button("Complimentary");
        compBtn.setStyle("-fx-background-color: #20c997; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 4 8;");

        Button discountBtn = new Button("Apply Discount");
        discountBtn.setStyle("-fx-background-color: #fd7e14; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 4 8;");

        offersRow.getChildren().addAll(bogoBtn, splitBtn, compBtn, discountBtn);

        paymentSection.getChildren().addAll(paymentLabel, paymentMethodsRow, additionalOptionsRow, offersRow);
        return paymentSection;
    }

    /**
     * Update item quantity in the order
     */
    private void updateItemQuantity(OrderItem item, int change) {
        try {
            int currentTableNumber = getCurrentTableNumber();
            int newQuantity = item.getQuantity() + change;

            System.out.println("BillingKOT: Updating " + item.getMenuItem().getName() +
                             " quantity from " + item.getQuantity() + " to " + newQuantity);

            // Use OrderManager's updateItemQuantity method
            orderManager.updateItemQuantity(currentTableNumber, item, newQuantity);

            System.out.println("BillingKOT: ✅ Item quantity updated successfully");

        } catch (Exception e) {
            System.err.println("BillingKOT: ❌ Error updating item quantity: " + e.getMessage());
            showAlert("Update Error", "Failed to update item quantity: " + e.getMessage());
        }
    }

    /**
     * Remove an item from the order
     */
    private void removeOrderItem(OrderItem item) {
        try {
            int currentTableNumber = getCurrentTableNumber();
            System.out.println("BillingKOT: Removing " + item.getMenuItem().getName() + " from table " + currentTableNumber);

            // Use OrderManager's removeItemFromTable method
            orderManager.removeItemFromTable(currentTableNumber, item);

            System.out.println("BillingKOT: ✅ Item removed successfully");

            // Show confirmation
            showAlert("Item Removed", item.getMenuItem().getName() + " has been removed from the order.");

        } catch (Exception e) {
            System.err.println("BillingKOT: ❌ Error removing item: " + e.getMessage());
            showAlert("Remove Error", "Failed to remove item: " + e.getMessage());
        }
    }

    // Store current table number for billing operations
    private int currentBillingTableNumber = 0;

    // Track open dialogs to prevent multiple dialogs
    private Dialog<?> currentOpenDialog = null;

    // Add field to store the menu items grid for filtering
    private ScrollPane menuItemsScrollPane;
    private GridPane currentMenuItemsGrid;

    // Store references to search components for cross-component updates
    private TextField currentSearchField;
    private ComboBox<String> currentCategoryCombo;

    /**
     * Get current table number from context
     */
    private int getCurrentTableNumber() {
        return currentBillingTableNumber;
    }

    /**
     * Set current table number for billing operations
     */
    private void setCurrentTableNumber(int tableNumber) {
        this.currentBillingTableNumber = tableNumber;
    }

    /**
     * Navigate to menu for selected table (same as See Tables functionality)
     */
    private void navigateToMenu(String tableName) {
        try {
            System.out.println("BillingKOT: 🚀 Starting navigation to menu for " + tableName);

            // Check if groundFloorGrid and scene are available
            if (groundFloorGrid == null) {
                System.err.println("BillingKOT: ❌ groundFloorGrid is null");
                showAlert("Navigation Error", "UI component not available");
                return;
            }

            if (groundFloorGrid.getScene() == null) {
                System.err.println("BillingKOT: ❌ Scene is null");
                showAlert("Navigation Error", "Scene not available");
                return;
            }

            // Load the MenuSelection view (order entry page) for the specific table
            System.out.println("BillingKOT: 📄 Loading MenuSelection.fxml...");
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/MenuSelection.fxml"));

            // Use a timeout for loading to prevent hanging
            javafx.scene.Parent menuSelectionView;
            try {
                menuSelectionView = loader.load();
                System.out.println("BillingKOT: ✅ MenuSelection FXML loaded successfully");
            } catch (Exception loadException) {
                System.err.println("BillingKOT: ❌ Failed to load MenuSelection.fxml: " + loadException.getMessage());
                loadException.printStackTrace();
                showAlert("Loading Error", "Failed to load menu interface: " + loadException.getMessage());
                return;
            }

            // Get the controller and set the table data
            com.restaurant.controller.MenuSelectionController menuController = loader.getController();
            if (menuController != null) {
                // Set the table information
                System.out.println("BillingKOT: 🏷️ Setting table info to: " + tableName);
                try {
                    menuController.setTableInfo(tableName);
                    System.out.println("BillingKOT: ✅ Table info set successfully");
                } catch (Exception setTableException) {
                    System.err.println("BillingKOT: ❌ Failed to set table info: " + setTableException.getMessage());
                    setTableException.printStackTrace();
                    showAlert("Setup Error", "Failed to setup table information: " + setTableException.getMessage());
                    return;
                }
            } else {
                System.err.println("BillingKOT: ❌ MenuSelectionController is null");
                showAlert("Navigation Error", "Failed to get menu controller");
                return;
            }

            // Get current stage and set the scene root (correct approach)
            System.out.println("BillingKOT: 🎭 Getting current stage...");
            javafx.stage.Stage stage = (javafx.stage.Stage) groundFloorGrid.getScene().getWindow();

            if (stage == null) {
                System.err.println("BillingKOT: ❌ Stage is null");
                showAlert("Navigation Error", "Window not available");
                return;
            }

            System.out.println("BillingKOT: 🔄 Setting new scene root...");
            stage.getScene().setRoot(menuSelectionView);
            stage.setTitle("Restaurant Management - " + tableName + " - Menu Selection");

            System.out.println("BillingKOT: 🎉 Navigation completed successfully to Menu Selection page for " + tableName);

        } catch (Exception e) {
            System.err.println("BillingKOT: ❌ ERROR during navigation: " + e.getMessage());
            e.printStackTrace();
            showAlert("Navigation Error", "Failed to open menu for " + tableName + ": " + e.getMessage());
        }
    }

    private void scheduleComplexInitialization() {
        // Schedule complex operations for background thread
        Task<Void> initTask = new Task<Void>() {
            @Override
            protected Void call() throws Exception {
                Thread.sleep(100); // Small delay to ensure UI is ready
                return null;
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    try {
                        // Initialize tables first
                        initializeTables();
                        System.out.println("BillingKOT: Tables initialized");

                        // Initialize table combos for Move KOT functionality
                        initializeTableCombos();

                        // Setup toggle buttons
                        setupToggleButtons();

                        System.out.println("BillingKOT: Complex initialization complete");
                    } catch (Exception e) {
                        System.err.println("Error in complex BillingKOT init: " + e.getMessage());
                        e.printStackTrace();
                    }
                });
            }
        };

        Thread initThread = new Thread(initTask, "BillingKOT-Init");
        initThread.setDaemon(true);
        initThread.start();
    }
    
    private void initializeTables() {
        try {
            System.out.println("BillingKOTController: Initializing tables");

            // Clear existing tables
            if (groundFloorGrid != null) {
                groundFloorGrid.getChildren().clear();
            }

            if (partyHallGrid != null) {
                partyHallGrid.getChildren().clear();
            }

            // Create tables for ground floor (4 rows x 5 columns)
            int groundFloorTables = 20;
            int groundFloorColumns = 5;

            for (int i = 1; i <= groundFloorTables; i++) {
                // Create table button
                Button tableBtn = createTableButton(i);

                // Add to grid
                int row = (i - 1) / groundFloorColumns;
                int col = (i - 1) % groundFloorColumns;

                if (groundFloorGrid != null) {
                    groundFloorGrid.add(tableBtn, col, row);
                }
            }

            // Create tables for party hall (2 rows x 3 columns)
            int partyHallTables = 6;
            int partyHallColumns = 3;

            for (int i = 21; i <= 20 + partyHallTables; i++) {
                // Create table button
                Button tableBtn = createTableButton(i);

                // Add to grid
                int row = (i - 21) / partyHallColumns;
                int col = (i - 21) % partyHallColumns;

                if (partyHallGrid != null) {
                    partyHallGrid.add(tableBtn, col, row);
                }
            }

            System.out.println("BillingKOTController: Tables initialized successfully");
        } catch (Exception e) {
            System.err.println("Error initializing tables: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private Button createTableButton(int tableNumber) {
        Button btn = new Button();
        btn.getStyleClass().add("table-button");

        // Set size
        btn.setPrefWidth(100);
        btn.setPrefHeight(80);

        // For now, create sample data to demonstrate functionality
        // Later this will be replaced with actual database calls
        boolean hasActiveOrder = (tableNumber % 4 == 0); // Every 4th table has an order for demo

        if (hasActiveOrder) {
            // Table has active order - show running amount and eye icon
            double sampleAmount = 150.0 + (tableNumber * 25.0); // Sample amount
            String buttonText = "Table " + tableNumber + "\n👁 ₹" + String.format("%.2f", sampleAmount);
            btn.setText(buttonText);

            // Set random status for demonstration
            String[] statuses = {"running-table", "running-kot-table", "printed-table"};
            int statusIndex = tableNumber % statuses.length;
            btn.getStyleClass().add(statuses[statusIndex]);

            // Set action for active table
            btn.setOnAction(e -> handleActiveTableClick(tableNumber));
        } else {
            // Empty table
            btn.setText("Table " + tableNumber);
            btn.getStyleClass().add("blank-table");

            // Set action for empty table
            btn.setOnAction(e -> handleEmptyTableClick(tableNumber));
        }

        return btn;
    }

    private void handleEmptyTableClick(int tableNumber) {
        System.out.println("BillingKOTController: Empty table " + tableNumber + " clicked - going directly to menu");

        // Go directly to menu interface for empty tables (no dialog)
        navigateToOrderEntry(tableNumber);
    }

    private void handleActiveTableClick(int tableNumber) {
        System.out.println("BillingKOTController: Active table " + tableNumber + " clicked - showing full order interface");

        // Show the full order interface directly as a dialog (like in your image)
        showFullOrderInterface(tableNumber);
    }

    private Order getActiveOrderForTable(int tableNumber) {
        try {
            // For demo purposes, create sample order with items
            if (tableNumber % 4 == 0) { // Only tables that have active orders
                Order order = new Order();
                order.setId(tableNumber * 100); // Sample order ID
                order.setTableNumber(tableNumber);
                order.setStatus("RUNNING");

                // Add sample order items
                addSampleOrderItems(order, tableNumber);

                return order;
            }
        } catch (Exception e) {
            System.err.println("Error getting active order for table " + tableNumber + ": " + e.getMessage());
        }
        return null;
    }

    private void addSampleOrderItems(Order order, int tableNumber) {
        // Create sample menu items and order items for demonstration
        try {
            // Sample items based on table number for variety
            String[] itemNames = {"Raj Kachori", "Strawberry Mojito", "Dahi Ke Sholay", "Cheese Garlic Bread", "Chilli Mushroom"};
            double[] prices = {100.00, 75.00, 80.00, 120.00, 90.00};
            int[] quantities = {1, 2, 1, 1, 1};

            int itemCount = Math.min(3 + (tableNumber % 3), itemNames.length); // 3-5 items per order

            for (int i = 0; i < itemCount; i++) {
                int itemIndex = (tableNumber + i) % itemNames.length;

                // Create MenuItem using the proper constructor
                MenuItem menuItem = new MenuItem(
                    i + 1,
                    itemNames[itemIndex],
                    prices[itemIndex],
                    "Sample Category",
                    1 // categoryId
                );

                // Create OrderItem
                OrderItem orderItem = new OrderItem(menuItem, quantities[itemIndex]);
                order.addItem(orderItem);
            }
        } catch (Exception e) {
            System.err.println("Error adding sample order items: " + e.getMessage());
        }
    }



    private void showFullOrderInterface(int tableNumber) {
        System.out.println("BillingKOTController: Creating full order interface for table " + tableNumber);

        try {
            // Get the active order for this table
            Order activeOrder = getActiveOrderForTable(tableNumber);
            if (activeOrder == null) {
                activeOrder = new Order();
                activeOrder.setTableNumber(tableNumber);
                activeOrder.setStatus("NEW");
            }

            // Create a large dialog that mimics your image interface
            Dialog<ButtonType> dialog = new Dialog<>();
            dialog.setTitle("Table " + tableNumber + " - Order Management");
            dialog.setHeaderText(null);

            // Create main layout (horizontal split like in your image)
            HBox mainLayout = new HBox(20);
            mainLayout.setPrefWidth(1200);
            mainLayout.setPrefHeight(700);
            mainLayout.setPadding(new Insets(20));

            // LEFT SIDE: Menu Categories and Items (like in your image)
            VBox leftSide = createMenuSide();
            leftSide.setPrefWidth(500);

            // RIGHT SIDE: Order Details (like in your image)
            VBox rightSide = createOrderSide(tableNumber, activeOrder);
            rightSide.setPrefWidth(600);

            // Create bottom button bar
            VBox buttonBar = createBottomButtonBar(tableNumber, dialog);

            // Create main container with button bar at bottom
            VBox mainContainer = new VBox(10);
            mainContainer.getChildren().addAll(mainLayout, buttonBar);

            mainLayout.getChildren().addAll(leftSide, rightSide);

            dialog.getDialogPane().setContent(mainContainer);

            // Only add a close button to the dialog pane
            ButtonType closeBtn = new ButtonType("Close", ButtonBar.ButtonData.CANCEL_CLOSE);
            dialog.getDialogPane().getButtonTypes().add(closeBtn);

            // Ensure dialog is properly sized to show all content including buttons
            dialog.getDialogPane().setPrefSize(1300, 950);
            dialog.setResizable(true);
            dialog.getDialogPane().setMaxHeight(950);
            dialog.getDialogPane().setMinHeight(950);

            // Add keyboard shortcut handling to this dialog too
            setupBillingDialogKeyboardShortcuts(dialog, tableNumber);

            // Show dialog with proper error handling
            try {
                dialog.showAndWait();
            } catch (Exception ex) {
                System.err.println("Error showing main billing dialog: " + ex.getMessage());
                ex.printStackTrace();
                showAlert("Dialog Error", "Failed to show billing dialog. Please try again.");
            }

        } catch (Exception e) {
            System.err.println("BillingKOTController: Error creating full order interface: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Failed to open order interface: " + e.getMessage());
        }
    }

    private VBox createMenuSide() {
        VBox menuSide = new VBox(0);
        menuSide.setStyle("-fx-background-color: white;");

        // Top search bar (like in your image)
        HBox searchBox = new HBox(10);
        searchBox.setStyle("-fx-padding: 10; -fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 0 0 1 0;");

        TextField searchField = new TextField();
        searchField.setPromptText("Search Item");
        searchField.setPrefWidth(200);
        searchField.setStyle("-fx-padding: 5;");

        // Store reference for cross-component updates
        currentSearchField = searchField;

        ComboBox<String> categoryCombo = new ComboBox<>();
        categoryCombo.getItems().addAll("All Items", "Tandoori Roti", "Biryani", "Tandoori", "Papad & Salad", "Mutton Gravy", "Sea Food", "Bulk Order",
                                       "Soup (Veg)", "Soup (Non-Veg)", "Noodles (Veg)", "Noodles (Non-Veg)", "Rice (Veg)", "Rice (Non-Veg)",
                                       "Chinese Gravy", "Indian & Punjabi (Veg)", "Chicken Gravy", "Starters (Veg)", "Starters (Non-Veg)", "Egg Dishes");
        categoryCombo.setValue("All Items");
        categoryCombo.setPrefWidth(150);

        // Store reference for cross-component updates
        currentCategoryCombo = categoryCombo;

        // Add search functionality - filter items as user types
        searchField.textProperty().addListener((observable, oldValue, newValue) -> {
            String currentCategory = categoryCombo.getValue();
            if (currentCategory == null) {
                currentCategory = "All Items";
            }
            filterMenuItems(currentCategory, newValue);
        });

        // Add listener to filter items when category is selected from dropdown
        categoryCombo.setOnAction(e -> {
            String selectedCategory = categoryCombo.getValue();
            String searchText = searchField.getText();
            if (selectedCategory != null) {
                filterMenuItems(selectedCategory, searchText);
            }
        });

        // Discount help label
        Label discountHelp = new Label("💡 Single click to add item, Double click for discount options");
        discountHelp.setStyle("-fx-font-size: 10px; -fx-text-fill: #6c757d; -fx-font-style: italic;");

        searchBox.getChildren().addAll(searchField, categoryCombo, discountHelp);

        // Main content area with categories and items side by side
        HBox mainContent = new HBox(0);

        // Left: Categories list (exactly like in your image)
        VBox categoriesPanel = new VBox(0);
        categoriesPanel.setPrefWidth(120);
        categoriesPanel.setStyle("-fx-background-color: #6c757d;");

        String[] categories = {"All Items", "Tandoori Roti", "Biryani", "Tandoori", "Papad & Salad", "Mutton Gravy", "Sea Food", "Bulk Order",
                              "Soup (Veg)", "Soup (Non-Veg)", "Noodles (Veg)", "Noodles (Non-Veg)", "Rice (Veg)", "Rice (Non-Veg)",
                              "Chinese Gravy", "Indian & Punjabi (Veg)", "Chicken Gravy", "Starters (Veg)", "Starters (Non-Veg)", "Egg Dishes"};

        for (int i = 0; i < categories.length; i++) {
            String category = categories[i];
            Button categoryBtn = new Button(category);
            categoryBtn.setPrefWidth(120);
            categoryBtn.setPrefHeight(35);
            categoryBtn.setMaxWidth(Double.MAX_VALUE);

            if (i == 0) { // "All Items" selected by default
                categoryBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-size: 11px; -fx-padding: 8; -fx-alignment: CENTER-LEFT;");
            } else {
                categoryBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-size: 11px; -fx-padding: 8; -fx-alignment: CENTER-LEFT;");
            }

            categoryBtn.setOnAction(e -> selectCategory(category, categoriesPanel));
            categoriesPanel.getChildren().add(categoryBtn);
        }

        ScrollPane categoriesScroll = new ScrollPane(categoriesPanel);
        categoriesScroll.setFitToWidth(true);
        categoriesScroll.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        categoriesScroll.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        categoriesScroll.setStyle("-fx-background-color: #6c757d;");

        // Right: Menu items grid (exactly like in your image)
        currentMenuItemsGrid = createMenuItemsGrid();
        menuItemsScrollPane = new ScrollPane(currentMenuItemsGrid);
        menuItemsScrollPane.setFitToWidth(true);
        menuItemsScrollPane.setStyle("-fx-background-color: white;");
        menuItemsScrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        menuItemsScrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);

        mainContent.getChildren().addAll(categoriesScroll, menuItemsScrollPane);
        HBox.setHgrow(menuItemsScrollPane, Priority.ALWAYS);

        menuSide.getChildren().addAll(searchBox, mainContent);
        VBox.setVgrow(mainContent, Priority.ALWAYS);

        return menuSide;
    }

    private GridPane createMenuItemsGrid() {
        GridPane grid = new GridPane();
        grid.setHgap(2);
        grid.setVgap(2);
        grid.setPadding(new Insets(5));
        grid.setStyle("-fx-background-color: white;");

        // Use the complete 233-item menu instead of hardcoded popular items
        List<MenuItem> allMenuItems = getComplete233MenuItems();

        int col = 0, row = 0;
        for (MenuItem menuItem : allMenuItems) {
            VBox itemCard = createMenuItemCard(menuItem.getName(), menuItem.getPrice());
            grid.add(itemCard, col, row);

            col++;
            if (col >= 4) { // 4 columns like in your image
                col = 0;
                row++;
            }
        }

        return grid;
    }

    private VBox createMenuItemCard(String itemName, double price) {
        VBox card = new VBox(2);
        card.setPrefWidth(110);
        card.setPrefHeight(85); // Increased height for discount info

        // Different colors for different items like in your image
        String backgroundColor = getItemCardColor(itemName);
        card.setStyle("-fx-background-color: " + backgroundColor + "; -fx-border-color: #333; -fx-border-width: 1; -fx-padding: 5; -fx-cursor: hand;");

        Label nameLabel = new Label(itemName);
        nameLabel.setStyle("-fx-font-size: 9px; -fx-font-weight: bold; -fx-text-fill: #333; -fx-wrap-text: true;");
        nameLabel.setWrapText(true);
        nameLabel.setMaxWidth(100);
        nameLabel.setMaxHeight(35);

        if (price > 0) {
            // Price section with discount
            VBox priceSection = createPriceSection(itemName, price);
            card.getChildren().addAll(nameLabel, priceSection);
        } else {
            card.getChildren().add(nameLabel);
        }

        // Add click handler with discount options
        card.setOnMouseClicked(e -> {
            if (e.getClickCount() == 1) {
                addItemToOrder(itemName, price);
            } else if (e.getClickCount() == 2) {
                showDiscountOptions(itemName, price);
            }
        });

        // Hover effect
        card.setOnMouseEntered(e -> card.setStyle("-fx-background-color: #e3f2fd; -fx-border-color: #2196f3; -fx-border-width: 2; -fx-padding: 4; -fx-cursor: hand;"));
        card.setOnMouseExited(e -> card.setStyle("-fx-background-color: " + backgroundColor + "; -fx-border-color: #333; -fx-border-width: 1; -fx-padding: 5; -fx-cursor: hand;"));

        return card;
    }

    private String getItemCardColor(String itemName) {
        // Match colors from your image
        if (itemName.contains("Chicken") || itemName.contains("Angara")) {
            return "#ffcccb"; // Light red
        } else if (itemName.contains("Cheese") || itemName.contains("Garlic")) {
            return "#c8e6c9"; // Light green
        } else if (itemName.contains("Chilli") || itemName.contains("Mushroom")) {
            return "#fff3e0"; // Light orange
        } else if (itemName.contains("Strawberry") || itemName.contains("Mojito")) {
            return "#f3e5f5"; // Light purple
        } else if (itemName.contains("Open Item")) {
            return "#f5f5f5"; // Light gray
        } else {
            return "#e1f5fe"; // Light blue (default)
        }
    }

    private void selectCategory(String category, VBox categoriesPanel) {
        // Update button styles to show selection
        for (Node node : categoriesPanel.getChildren()) {
            if (node instanceof Button) {
                Button btn = (Button) node;
                if (btn.getText().equals(category)) {
                    btn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-size: 11px; -fx-padding: 8; -fx-alignment: CENTER-LEFT;");
                } else {
                    btn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-size: 11px; -fx-padding: 8; -fx-alignment: CENTER-LEFT;");
                }
            }
        }

        // Update the dropdown to match the selected category
        if (currentCategoryCombo != null && !category.equals(currentCategoryCombo.getValue())) {
            currentCategoryCombo.setValue(category);
        }

        // Get current search text and filter items
        String searchText = currentSearchField != null ? currentSearchField.getText() : "";
        filterMenuItems(category, searchText);
    }

    private void loadMenuItemsForCategory(String category) {
        // Delegate to the new filterMenuItems method with empty search
        filterMenuItems(category, "");
    }

    /**
     * Filter menu items based on category and search text
     */
    private void filterMenuItems(String category, String searchText) {
        System.out.println("Filtering items for category: " + category + ", search: '" + searchText + "'");

        if (menuItemsScrollPane == null) {
            System.out.println("Menu items scroll pane not initialized yet");
            return;
        }

        try {
            // Get all menu items
            List<MenuItem> allMenuItems = getComplete233MenuItems();
            List<MenuItem> filteredItems;

            // Filter items based on category first
            if ("All Items".equals(category)) {
                filteredItems = allMenuItems;
            } else {
                filteredItems = allMenuItems.stream()
                    .filter(item -> category.equals(item.getCategory()))
                    .collect(Collectors.toList());
            }

            // Then filter by search text if provided (including shortcuts)
            if (searchText != null && !searchText.trim().isEmpty()) {
                String searchLower = searchText.toLowerCase().trim();
                filteredItems = filteredItems.stream()
                    .filter(item -> MenuShortcuts.matchesItem(item.getName(), searchLower))
                    .collect(Collectors.toList());
            }

            // Create new grid with filtered items
            GridPane newGrid = new GridPane();
            newGrid.setHgap(2);
            newGrid.setVgap(2);
            newGrid.setPadding(new Insets(5));
            newGrid.setStyle("-fx-background-color: white;");

            int col = 0, row = 0;
            for (MenuItem menuItem : filteredItems) {
                VBox itemCard = createMenuItemCard(menuItem.getName(), menuItem.getPrice());
                newGrid.add(itemCard, col, row);

                col++;
                if (col >= 4) { // 4 columns like in your image
                    col = 0;
                    row++;
                }
            }

            // Update the scroll pane content
            Platform.runLater(() -> {
                menuItemsScrollPane.setContent(newGrid);
                currentMenuItemsGrid = newGrid;
            });

            String searchInfo = searchText != null && !searchText.trim().isEmpty() ?
                " (search: '" + searchText + "')" : "";
            System.out.println("✅ Loaded " + filteredItems.size() + " items for category: " + category + searchInfo);

        } catch (Exception e) {
            System.err.println("Error filtering menu items: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void addItemToOrder(String itemName, double price) {
        try {
            System.out.println("Adding to order: " + itemName + " - ₹" + price);

            // Get current table number
            int currentTableNumber = getCurrentTableNumber();
            if (currentTableNumber <= 0) {
                showAlert("Error", "No table selected. Please select a table first.");
                return;
            }

            // Create MenuItem object
            MenuItem menuItem = findMenuItemByName(itemName, price);
            if (menuItem == null) {
                // Create a new MenuItem if not found
                menuItem = new MenuItem(
                    generateMenuItemId(itemName),
                    itemName,
                    price,
                    "Main Course", // Default category
                    1 // Default category ID
                );
            }

            // Add item to order using OrderManager
            orderManager.addItemToTable(currentTableNumber, menuItem, 1);

            System.out.println("✅ Item added successfully: " + itemName + " to Table " + currentTableNumber);

            // Show success feedback
            showAlert("Item Added",
                     "✅ " + itemName + " added to Table " + currentTableNumber + "\n" +
                     "Price: ₹" + String.format("%.2f", price) + "\n\n" +
                     "Total items in order: " + orderManager.getOrderForTable(currentTableNumber).getItems().size());

        } catch (Exception e) {
            System.err.println("Error adding item to order: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Failed to add item to order: " + e.getMessage());
        }
    }

    /**
     * Find a MenuItem by name and price from the complete menu
     */
    private MenuItem findMenuItemByName(String itemName, double price) {
        try {
            // Search through all menu items to find a match
            List<MenuItem> allMenuItems = getAllMenuItems();

            for (MenuItem item : allMenuItems) {
                if (item.getName().equalsIgnoreCase(itemName) &&
                    Math.abs(item.getPrice() - price) < 0.01) { // Allow small price differences
                    return item;
                }
            }

            // If exact match not found, try partial name match
            for (MenuItem item : allMenuItems) {
                if (item.getName().toLowerCase().contains(itemName.toLowerCase())) {
                    return item;
                }
            }

        } catch (Exception e) {
            System.err.println("Error finding menu item: " + e.getMessage());
        }

        return null; // Not found
    }

    /**
     * Generate a unique ID for a menu item based on its name
     */
    private int generateMenuItemId(String itemName) {
        // Simple hash-based ID generation
        return Math.abs(itemName.hashCode() % 10000) + 1000;
    }

    /**
     * Get all menu items (complete 233-item menu)
     */
    private List<MenuItem> getAllMenuItems() {
        List<MenuItem> allItems = new ArrayList<>();

        // Tandoori Roti - 13 items
        allItems.addAll(Arrays.asList(
            new MenuItem(1, "Roti", 20.0, "Tandoori Roti", 1),
            new MenuItem(2, "Butter Roti", 25.0, "Tandoori Roti", 2),
            new MenuItem(3, "Naan", 35.0, "Tandoori Roti", 3),
            new MenuItem(4, "Butter Naan", 40.0, "Tandoori Roti", 4),
            new MenuItem(5, "Garlic Naan", 70.0, "Tandoori Roti", 5),
            new MenuItem(6, "Khulcha", 40.0, "Tandoori Roti", 6),
            new MenuItem(7, "Butter Khulcha", 45.0, "Tandoori Roti", 7),
            new MenuItem(8, "Lacha Paratha", 45.0, "Tandoori Roti", 8),
            new MenuItem(9, "Butter Paratha", 45.0, "Tandoori Roti", 9),
            new MenuItem(10, "Aloo Paratha", 70.0, "Tandoori Roti", 10),
            new MenuItem(11, "Gobi Paratha", 70.0, "Tandoori Roti", 11),
            new MenuItem(12, "Paneer Paratha", 120.0, "Tandoori Roti", 12),
            new MenuItem(13, "Veg Kheema Paratha", 120.0, "Tandoori Roti", 13)
        ));

        // Biryani (Veg/Non Veg) - 14 items
        allItems.addAll(Arrays.asList(
            new MenuItem(14, "Steam Basmati Rice (H/F)", 60.0, "Biryani", 14),
            new MenuItem(15, "Jeera Rice (H/F)", 70.0, "Biryani", 15),
            new MenuItem(16, "Veg Pulao", 170.0, "Biryani", 16),
            new MenuItem(17, "Veg Biryani", 180.0, "Biryani", 17),
            new MenuItem(18, "Egg Biryani", 190.0, "Biryani", 18),
            new MenuItem(19, "Paneer Pulav", 180.0, "Biryani", 19),
            new MenuItem(20, "Paneer Biryani", 190.0, "Biryani", 20),
            new MenuItem(21, "Chicken Biryani", 220.0, "Biryani", 21),
            new MenuItem(22, "Chi. Dum Biryani", 230.0, "Biryani", 22),
            new MenuItem(23, "Chicken Hyderabadi Biryani", 230.0, "Biryani", 23),
            new MenuItem(24, "Mutton Pulav", 250.0, "Biryani", 24),
            new MenuItem(25, "Mutton Biryani", 280.0, "Biryani", 25),
            new MenuItem(26, "Mutton Dum Biryani", 300.0, "Biryani", 26),
            new MenuItem(27, "Mutton Hyderabadi Biryani", 300.0, "Biryani", 27)
        ));

        // Add more categories as needed - for now return what we have
        return allItems;
    }

    /**
     * Get the complete 233-item menu for the billing interface
     */
    private List<MenuItem> getComplete233MenuItems() {
        List<MenuItem> allItems = new ArrayList<>();

        // Tandoori Roti - 13 items
        allItems.addAll(Arrays.asList(
            new MenuItem(1, "Roti", 20.0, "Tandoori Roti", 1),
            new MenuItem(2, "Butter Roti", 25.0, "Tandoori Roti", 2),
            new MenuItem(3, "Naan", 35.0, "Tandoori Roti", 3),
            new MenuItem(4, "Butter Naan", 40.0, "Tandoori Roti", 4),
            new MenuItem(5, "Garlic Naan", 70.0, "Tandoori Roti", 5),
            new MenuItem(6, "Khulcha", 40.0, "Tandoori Roti", 6),
            new MenuItem(7, "Butter Khulcha", 45.0, "Tandoori Roti", 7),
            new MenuItem(8, "Lacha Paratha", 45.0, "Tandoori Roti", 8),
            new MenuItem(9, "Butter Paratha", 45.0, "Tandoori Roti", 9),
            new MenuItem(10, "Aloo Paratha", 70.0, "Tandoori Roti", 10),
            new MenuItem(11, "Gobi Paratha", 70.0, "Tandoori Roti", 11),
            new MenuItem(12, "Paneer Paratha", 120.0, "Tandoori Roti", 12),
            new MenuItem(13, "Veg Kheema Paratha", 120.0, "Tandoori Roti", 13)
        ));

        // Biryani (Veg/Non Veg) - 14 items
        allItems.addAll(Arrays.asList(
            new MenuItem(14, "Steam Basmati Rice (H/F)", 60.0, "Biryani", 14),
            new MenuItem(15, "Jeera Rice (H/F)", 70.0, "Biryani", 15),
            new MenuItem(16, "Veg Pulao", 170.0, "Biryani", 16),
            new MenuItem(17, "Veg Biryani", 180.0, "Biryani", 17),
            new MenuItem(18, "Egg Biryani", 190.0, "Biryani", 18),
            new MenuItem(19, "Paneer Pulav", 180.0, "Biryani", 19),
            new MenuItem(20, "Paneer Biryani", 190.0, "Biryani", 20),
            new MenuItem(21, "Chicken Biryani", 220.0, "Biryani", 21),
            new MenuItem(22, "Chi. Dum Biryani", 230.0, "Biryani", 22),
            new MenuItem(23, "Chicken Hyderabadi Biryani", 230.0, "Biryani", 23),
            new MenuItem(24, "Mutton Pulav", 250.0, "Biryani", 24),
            new MenuItem(25, "Mutton Biryani", 280.0, "Biryani", 25),
            new MenuItem(26, "Mutton Dum Biryani", 300.0, "Biryani", 26),
            new MenuItem(27, "Mutton Hyderabadi Biryani", 300.0, "Biryani", 27)
        ));

        // Tandoori (Veg) - 6 items
        allItems.addAll(Arrays.asList(
            new MenuItem(28, "Paneer Tikka", 200.0, "Tandoori", 28),
            new MenuItem(29, "Paneer Malai Tikka", 220.0, "Tandoori", 29),
            new MenuItem(30, "Veg Seek Kabab", 190.0, "Tandoori", 30),
            new MenuItem(31, "Mushroom Tikka", 200.0, "Tandoori", 31),
            new MenuItem(32, "Baby Corn Tikka", 190.0, "Tandoori", 32),
            new MenuItem(33, "Chilly Milly Kabab", 200.0, "Tandoori", 33)
        ));

        // Tandoori Chicken - 13 items
        allItems.addAll(Arrays.asList(
            new MenuItem(34, "Chicken Tikka", 210.0, "Tandoori", 34),
            new MenuItem(35, "Chicken Tandoori Half", 210.0, "Tandoori", 35),
            new MenuItem(36, "Chicken Tandoori Full", 400.0, "Tandoori", 36),
            new MenuItem(37, "Chicken Pahadi Tandoori Half", 220.0, "Tandoori", 37),
            new MenuItem(38, "Chicken Pahadi Tandoori Full", 410.0, "Tandoori", 38),
            new MenuItem(39, "Chicken Lemon Tandoori Half", 240.0, "Tandoori", 39),
            new MenuItem(40, "Chicken Lemon Tandoori Full", 420.0, "Tandoori", 40),
            new MenuItem(41, "Chicken Kalimiri Kabab", 260.0, "Tandoori", 41),
            new MenuItem(42, "Chicken Banjara Kabab", 260.0, "Tandoori", 42),
            new MenuItem(43, "Chicken Sholay Kabab", 260.0, "Tandoori", 43),
            new MenuItem(44, "Chicken Hariyali Kabab", 260.0, "Tandoori", 44),
            new MenuItem(45, "Chicken Tangri Kabab", 200.0, "Tandoori", 45),
            new MenuItem(46, "Chicken Rajwadi Kabab", 300.0, "Tandoori", 46)
        ));

        // Papad & Salad - 8 items
        allItems.addAll(Arrays.asList(
            new MenuItem(47, "Roasted Papad", 20.0, "Papad & Salad", 47),
            new MenuItem(48, "Fry Papad", 25.0, "Papad & Salad", 48),
            new MenuItem(49, "Masala Papad", 50.0, "Papad & Salad", 49),
            new MenuItem(50, "Green Salad", 90.0, "Papad & Salad", 50),
            new MenuItem(51, "Raita", 100.0, "Papad & Salad", 51),
            new MenuItem(52, "Boondi Raita", 130.0, "Papad & Salad", 52),
            new MenuItem(53, "Schzewan Sauce Extra", 20.0, "Papad & Salad", 53),
            new MenuItem(54, "Fry Noodles Extra", 20.0, "Papad & Salad", 54)
        ));

        // Mutton Gravy - 8 items
        allItems.addAll(Arrays.asList(
            new MenuItem(55, "Mutton Masala", 330.0, "Mutton Gravy", 55),
            new MenuItem(56, "Mutton Kadhai", 330.0, "Mutton Gravy", 56),
            new MenuItem(57, "Mutton Kolhapuri", 330.0, "Mutton Gravy", 57),
            new MenuItem(58, "Mutton Hyderabadi", 330.0, "Mutton Gravy", 58),
            new MenuItem(59, "Mutton Handi (Half) 6pcs", 470.0, "Mutton Gravy", 59),
            new MenuItem(60, "Mutton Handi (Full) 12pcs", 750.0, "Mutton Gravy", 60),
            new MenuItem(61, "Mutton Do Pyaza", 330.0, "Mutton Gravy", 61),
            new MenuItem(62, "Mutton Shukar", 350.0, "Mutton Gravy", 62)
        ));

        // Sea Food - 11 items
        allItems.addAll(Arrays.asList(
            new MenuItem(63, "Bangda Fry", 130.0, "Sea Food", 63),
            new MenuItem(64, "Bangda Masala", 180.0, "Sea Food", 64),
            new MenuItem(65, "Mandeli Oil Fry", 150.0, "Sea Food", 65),
            new MenuItem(66, "Surmai Tawa Fry", 195.0, "Sea Food", 66),
            new MenuItem(67, "Surmai Koliwada", 195.0, "Sea Food", 67),
            new MenuItem(68, "Prawns Tawa Fry", 255.0, "Sea Food", 68),
            new MenuItem(69, "Prawns Koliwada", 250.0, "Sea Food", 69),
            new MenuItem(70, "Surmai Gavan Curry", 195.0, "Sea Food", 70),
            new MenuItem(71, "Surmai Masala", 195.0, "Sea Food", 71),
            new MenuItem(82, "Fish Curry", 220.0, "Sea Food", 82),
            new MenuItem(83, "Prawn Curry", 280.0, "Sea Food", 83)
        ));

        // Bulk Order (Per KG) - 10 items
        allItems.addAll(Arrays.asList(
            new MenuItem(72, "Paneer Pulav", 800.0, "Bulk Order", 72),
            new MenuItem(73, "Veg Biryani", 800.0, "Bulk Order", 73),
            new MenuItem(74, "Chicken Biryani", 1000.0, "Bulk Order", 74),
            new MenuItem(75, "Mutton Biryani", 1300.0, "Bulk Order", 75),
            new MenuItem(76, "Veg Pulav", 700.0, "Bulk Order", 76),
            new MenuItem(77, "Chicken Masala", 900.0, "Bulk Order", 77),
            new MenuItem(78, "Jira Rice", 650.0, "Bulk Order", 78),
            new MenuItem(79, "Steam Rice", 650.0, "Bulk Order", 79),
            new MenuItem(80, "Chicken Malai Tikka", 240.0, "Tandoori", 80),
            new MenuItem(81, "Chicken Seekh Kabab", 280.0, "Tandoori", 81)
        ));

        // Soup (Veg) - 5 items
        allItems.addAll(Arrays.asList(
            new MenuItem(84, "Manchow Soup", 110.0, "Soup (Veg)", 84),
            new MenuItem(85, "Schezwan Soup", 110.0, "Soup (Veg)", 85),
            new MenuItem(86, "Noodles Soup", 110.0, "Soup (Veg)", 86),
            new MenuItem(87, "Clear Soup", 110.0, "Soup (Veg)", 87),
            new MenuItem(88, "Hot N Sour Soup", 110.0, "Soup (Veg)", 88)
        ));

        // Soup (Non-Veg) - 6 items
        allItems.addAll(Arrays.asList(
            new MenuItem(89, "Chicken Manchow Soup", 120.0, "Soup (Non-Veg)", 89),
            new MenuItem(90, "Chicken Hot N Sour Soup", 120.0, "Soup (Non-Veg)", 90),
            new MenuItem(91, "Chicken Lung Fung Soup", 120.0, "Soup (Non-Veg)", 91),
            new MenuItem(92, "Chicken Schezwan Soup", 120.0, "Soup (Non-Veg)", 92),
            new MenuItem(93, "Chicken Noodles Soup", 120.0, "Soup (Non-Veg)", 93),
            new MenuItem(94, "Chicken Clear Soup", 120.0, "Soup (Non-Veg)", 94)
        ));

        // Noodles (Veg) - 9 items
        allItems.addAll(Arrays.asList(
            new MenuItem(95, "Veg Hakka Noodles", 160.0, "Noodles (Veg)", 95),
            new MenuItem(96, "Veg Schezwan Noodles", 170.0, "Noodles (Veg)", 96),
            new MenuItem(97, "Veg Singapore Noodles", 190.0, "Noodles (Veg)", 97),
            new MenuItem(98, "Veg Hong Kong Noodles", 190.0, "Noodles (Veg)", 98),
            new MenuItem(99, "Veg Mushroom Noodles", 180.0, "Noodles (Veg)", 99),
            new MenuItem(100, "Veg Manchurian Noodles", 190.0, "Noodles (Veg)", 100),
            new MenuItem(101, "Veg Sherpa Noodles", 220.0, "Noodles (Veg)", 101),
            new MenuItem(102, "Veg Triple Sch. Noodles", 220.0, "Noodles (Veg)", 102),
            new MenuItem(103, "Veg Chilly Garlic Noodles", 220.0, "Noodles (Veg)", 103)
        ));

        // Noodles (Non-Veg) - 12 items
        allItems.addAll(Arrays.asList(
            new MenuItem(104, "Egg Hakka Noodles", 160.0, "Noodles (Non-Veg)", 104),
            new MenuItem(105, "Egg Schezwan Noodles", 170.0, "Noodles (Non-Veg)", 105),
            new MenuItem(106, "Chicken Hakka Noodles", 180.0, "Noodles (Non-Veg)", 106),
            new MenuItem(107, "Chi. Schezwan Noodles", 190.0, "Noodles (Non-Veg)", 107),
            new MenuItem(108, "Chi. Singapore Noodles", 200.0, "Noodles (Non-Veg)", 108),
            new MenuItem(109, "Chi. Hong Kong Noodles", 200.0, "Noodles (Non-Veg)", 109),
            new MenuItem(110, "Chi. Mushroom Noodles", 200.0, "Noodles (Non-Veg)", 110),
            new MenuItem(111, "Chi. Triple Schezwan Noodles", 250.0, "Noodles (Non-Veg)", 111),
            new MenuItem(112, "Chi. Sherpa Noodles", 250.0, "Noodles (Non-Veg)", 112),
            new MenuItem(113, "Chi. Thousand Noodles", 280.0, "Noodles (Non-Veg)", 113),
            new MenuItem(114, "Chi. Chilly Basil Noodles", 250.0, "Noodles (Non-Veg)", 114),
            new MenuItem(115, "Chicken Chilly Garlic Noodles", 250.0, "Noodles (Non-Veg)", 115)
        ));

        // Rice (Veg) - 12 items
        allItems.addAll(Arrays.asList(
            new MenuItem(116, "Veg Fry Rice", 170.0, "Rice (Veg)", 116),
            new MenuItem(117, "Veg Schezwan Rice", 180.0, "Rice (Veg)", 117),
            new MenuItem(118, "Veg Singapore Rice", 190.0, "Rice (Veg)", 118),
            new MenuItem(119, "Veg Hong Kong Rice", 190.0, "Rice (Veg)", 119),
            new MenuItem(120, "Veg Schezwan Combination Rice", 190.0, "Rice (Veg)", 120),
            new MenuItem(121, "Veg Manchurian Rice", 210.0, "Rice (Veg)", 121),
            new MenuItem(122, "Veg Triple Schoz. Rice", 210.0, "Rice (Veg)", 122),
            new MenuItem(123, "Paneer Fry Rice", 200.0, "Rice (Veg)", 123),
            new MenuItem(124, "Paneer Schezwan Rice", 200.0, "Rice (Veg)", 124),
            new MenuItem(125, "Veg Sherpa Rice", 230.0, "Rice (Veg)", 125),
            new MenuItem(126, "Veg Thousand Rice", 230.0, "Rice (Veg)", 126),
            new MenuItem(127, "Veg Chilly Basil Rice", 200.0, "Rice (Veg)", 127)
        ));

        // Rice (Non-Veg) - 16 items
        allItems.addAll(Arrays.asList(
            new MenuItem(128, "Chi. Schezwan Rice", 190.0, "Rice (Non-Veg)", 128),
            new MenuItem(129, "Chi. Singapore Rice", 200.0, "Rice (Non-Veg)", 129),
            new MenuItem(130, "Chi. Hong Kong Rice", 200.0, "Rice (Non-Veg)", 130),
            new MenuItem(131, "Chi. Sez. Combination Rice", 210.0, "Rice (Non-Veg)", 131),
            new MenuItem(132, "Chi. Burn Garlic Rice", 210.0, "Rice (Non-Veg)", 132),
            new MenuItem(133, "Chi. Chilly Garlic Rice", 210.0, "Rice (Non-Veg)", 133),
            new MenuItem(134, "Chi. Manchurian Rice", 250.0, "Rice (Non-Veg)", 134),
            new MenuItem(135, "Chi. Triple Schoz. Rice", 250.0, "Rice (Non-Veg)", 135),
            new MenuItem(136, "Chi. Sherpa Rice", 260.0, "Rice (Non-Veg)", 136),
            new MenuItem(137, "Chi. Thousand Rice", 300.0, "Rice (Non-Veg)", 137),
            new MenuItem(138, "Chi. Jadoo Rice", 280.0, "Rice (Non-Veg)", 138),
            new MenuItem(139, "Chi. Ginger Garlic Rice", 210.0, "Rice (Non-Veg)", 139),
            new MenuItem(140, "Chi. Chilly Basil Rice", 220.0, "Rice (Non-Veg)", 140),
            new MenuItem(141, "Egg Fry Rice", 170.0, "Rice (Non-Veg)", 141),
            new MenuItem(142, "Egg Schezwan Rice", 180.0, "Rice (Non-Veg)", 142),
            new MenuItem(143, "Chi. Fry Rice", 180.0, "Rice (Non-Veg)", 143)
        ));

        // Chinese Gravy - 9 items
        allItems.addAll(Arrays.asList(
            new MenuItem(144, "Manchurian Gravy / Chilly", 180.0, "Chinese Gravy", 144),
            new MenuItem(145, "Schezwan Gravy", 180.0, "Chinese Gravy", 145),
            new MenuItem(146, "Chilly Gravy", 180.0, "Chinese Gravy", 146),
            new MenuItem(147, "Kum Pav Gravy", 180.0, "Chinese Gravy", 147),
            new MenuItem(148, "Hot Garlic Gravy", 190.0, "Chinese Gravy", 148),
            new MenuItem(149, "Oyster Gravy", 190.0, "Chinese Gravy", 149),
            new MenuItem(150, "Paneer Sch. Gravy", 190.0, "Chinese Gravy", 150),
            new MenuItem(151, "Paneer Manch. Gravy", 190.0, "Chinese Gravy", 151),
            new MenuItem(152, "Paneer Chilly Gravy", 190.0, "Chinese Gravy", 152)
        ));

        // Indian & Punjabi (Veg) - 28 items (Part 1)
        allItems.addAll(Arrays.asList(
            new MenuItem(153, "Dal Fry", 130.0, "Indian & Punjabi (Veg)", 153),
            new MenuItem(154, "Dal Tadka", 150.0, "Indian & Punjabi (Veg)", 154),
            new MenuItem(155, "Dal Palak", 150.0, "Indian & Punjabi (Veg)", 155),
            new MenuItem(156, "Dal Khichadi (1000ML)", 220.0, "Indian & Punjabi (Veg)", 156),
            new MenuItem(157, "Palak Khichadi (1000ML)", 240.0, "Indian & Punjabi (Veg)", 157),
            new MenuItem(158, "Dal Khichadi Tadka (1000ML)", 240.0, "Indian & Punjabi (Veg)", 158),
            new MenuItem(159, "Palak Khichadi Tadka (1000ML)", 240.0, "Indian & Punjabi (Veg)", 159),
            new MenuItem(160, "Mix Veg", 180.0, "Indian & Punjabi (Veg)", 160),
            new MenuItem(161, "Veg Kadhai", 190.0, "Indian & Punjabi (Veg)", 161),
            new MenuItem(162, "Veg Kolhapuri", 190.0, "Indian & Punjabi (Veg)", 162),
            new MenuItem(163, "Veg Tawa", 190.0, "Indian & Punjabi (Veg)", 163),
            new MenuItem(164, "Veg Lajawab", 190.0, "Indian & Punjabi (Veg)", 164),
            new MenuItem(165, "Veg Chilly Milly", 220.0, "Indian & Punjabi (Veg)", 165),
            new MenuItem(166, "Aloo Mutter", 170.0, "Indian & Punjabi (Veg)", 166)
        ));

        // Indian & Punjabi (Veg) - 28 items (Part 2)
        allItems.addAll(Arrays.asList(
            new MenuItem(167, "Veg Handi (Half/Full)", 210.0, "Indian & Punjabi (Veg)", 167),
            new MenuItem(168, "Paneer Masala", 200.0, "Indian & Punjabi (Veg)", 168),
            new MenuItem(169, "Paneer Mutter Masala", 200.0, "Indian & Punjabi (Veg)", 169),
            new MenuItem(170, "Paneer Butter Masala", 220.0, "Indian & Punjabi (Veg)", 170),
            new MenuItem(171, "Paneer Kadhai", 200.0, "Indian & Punjabi (Veg)", 171),
            new MenuItem(172, "Paneer Bhurji Masala", 220.0, "Indian & Punjabi (Veg)", 172),
            new MenuItem(173, "Paneer Mutter", 200.0, "Indian & Punjabi (Veg)", 173),
            new MenuItem(174, "Palak Paneer", 200.0, "Indian & Punjabi (Veg)", 174),
            new MenuItem(175, "Mushroom Masala", 210.0, "Indian & Punjabi (Veg)", 175),
            new MenuItem(176, "Mushroom Tikka Masala", 230.0, "Indian & Punjabi (Veg)", 176),
            new MenuItem(177, "Lasuni Palak", 190.0, "Indian & Punjabi (Veg)", 177),
            new MenuItem(178, "Veg Maratha", 250.0, "Indian & Punjabi (Veg)", 178),
            new MenuItem(179, "Sev Bhaji", 180.0, "Indian & Punjabi (Veg)", 179),
            new MenuItem(180, "Masala Fry Masala", 200.0, "Indian & Punjabi (Veg)", 180)
        ));

        // Chicken Gravy - 19 items (Part 1)
        allItems.addAll(Arrays.asList(
            new MenuItem(181, "Chicken Masala", 210.0, "Chicken Gravy", 181),
            new MenuItem(182, "Chicken Curry", 210.0, "Chicken Gravy", 182),
            new MenuItem(183, "Chicken Kadhai", 240.0, "Chicken Gravy", 183),
            new MenuItem(184, "Chicken Bhurjani", 240.0, "Chicken Gravy", 184),
            new MenuItem(185, "Chicken Tawa Masala", 260.0, "Chicken Gravy", 185),
            new MenuItem(186, "Chicken Gayti Masala", 260.0, "Chicken Gravy", 186),
            new MenuItem(187, "Chicken Tikka Masala", 260.0, "Chicken Gravy", 187),
            new MenuItem(188, "Chicken Maratha", 280.0, "Chicken Gravy", 188),
            new MenuItem(189, "Chicken Lasuni Masala", 260.0, "Chicken Gravy", 189),
            new MenuItem(190, "Chicken Japeta (H/F)", 310.0, "Chicken Gravy", 190)
        ));

        // Chicken Gravy - 19 items (Part 2)
        allItems.addAll(Arrays.asList(
            new MenuItem(191, "Butter Chicken (H/F)", 290.0, "Chicken Gravy", 191),
            new MenuItem(192, "Chicken Malvani Masala", 260.0, "Chicken Gravy", 192),
            new MenuItem(193, "Chicken Tikka Lemon Masala", 260.0, "Chicken Gravy", 193),
            new MenuItem(194, "Chicken Hyderabadi Masala", 260.0, "Chicken Gravy", 194),
            new MenuItem(195, "Chicken Mughlai", 260.0, "Chicken Gravy", 195),
            new MenuItem(196, "Chicken Pahadi Masala", 260.0, "Chicken Gravy", 196),
            new MenuItem(197, "Chicken Handi (Half) 6pcs", 260.0, "Chicken Gravy", 197),
            new MenuItem(198, "Chicken Handi (Full) 12pcs", 480.0, "Chicken Gravy", 198),
            new MenuItem(199, "Chicken Do Pyaza", 260.0, "Chicken Gravy", 199)
        ));

        // Starters (Veg) - 14 items
        allItems.addAll(Arrays.asList(
            new MenuItem(200, "Veg Manchurian / Chilly", 190.0, "Starters (Veg)", 200),
            new MenuItem(201, "Veg Chinese Bhel", 190.0, "Starters (Veg)", 201),
            new MenuItem(202, "Mushroom Chilly/ Manchurian", 200.0, "Starters (Veg)", 202),
            new MenuItem(203, "Paneer Chilly/ Manchurian", 220.0, "Starters (Veg)", 203),
            new MenuItem(204, "Paneer Crispy", 220.0, "Starters (Veg)", 204),
            new MenuItem(205, "Paneer Singapur", 220.0, "Starters (Veg)", 205),
            new MenuItem(206, "Veg Crispy", 200.0, "Starters (Veg)", 206),
            new MenuItem(207, "Crispy Chilly Potato", 220.0, "Starters (Veg)", 207),
            new MenuItem(208, "Honey Chilly Potato", 220.0, "Starters (Veg)", 208),
            new MenuItem(209, "Paneer Shangai Wok", 250.0, "Starters (Veg)", 209),
            new MenuItem(210, "Paneer Schezwan Wok", 250.0, "Starters (Veg)", 210),
            new MenuItem(211, "Paneer Chilly Basil Wok", 260.0, "Starters (Veg)", 211),
            new MenuItem(212, "Paneer Honey Chilly Wok", 260.0, "Starters (Veg)", 212),
            new MenuItem(213, "Paneer Kum Pav Wok", 260.0, "Starters (Veg)", 213)
        ));

        // Starters (Non-Veg) - 14 items
        allItems.addAll(Arrays.asList(
            new MenuItem(214, "Chi. Chinese Bhel", 180.0, "Starters (Non-Veg)", 214),
            new MenuItem(215, "Chi. Chilly/Manchurian", 220.0, "Starters (Non-Veg)", 215),
            new MenuItem(216, "Chi. Schezwan", 220.0, "Starters (Non-Veg)", 216),
            new MenuItem(217, "Chi. Chilly Garlic Wok", 230.0, "Starters (Non-Veg)", 217),
            new MenuItem(218, "Chi. Kum Pav Wok", 260.0, "Starters (Non-Veg)", 218),
            new MenuItem(219, "Chi. Crispy", 250.0, "Starters (Non-Veg)", 219),
            new MenuItem(220, "Chi. Singapur", 250.0, "Starters (Non-Veg)", 220),
            new MenuItem(221, "Chi. Lamba", 250.0, "Starters (Non-Veg)", 221),
            new MenuItem(222, "Chi. Oyster Sauces", 250.0, "Starters (Non-Veg)", 222),
            new MenuItem(223, "Chi. Black Paper Wok", 250.0, "Starters (Non-Veg)", 223),
            new MenuItem(224, "Chi. Lollypop 8 Pc.", 230.0, "Starters (Non-Veg)", 224),
            new MenuItem(225, "Chi. Lollypop Schzwn/Hnypp", 300.0, "Starters (Non-Veg)", 225),
            new MenuItem(226, "Chi. Honey Chilly", 270.0, "Starters (Non-Veg)", 226),
            new MenuItem(227, "Chi. Chilly Basil Wok", 270.0, "Starters (Non-Veg)", 227)
        ));

        // Egg Dishes - 6 items
        allItems.addAll(Arrays.asList(
            new MenuItem(228, "Boiled Egg", 40.0, "Egg Dishes", 228),
            new MenuItem(229, "Egg Omlete", 50.0, "Egg Dishes", 229),
            new MenuItem(230, "Egg Bhurji", 110.0, "Egg Dishes", 230),
            new MenuItem(231, "Egg Masala", 170.0, "Egg Dishes", 231),
            new MenuItem(232, "Egg Curry", 170.0, "Egg Dishes", 232),
            new MenuItem(233, "Anda Ghotala", 180.0, "Egg Dishes", 233)
        ));

        return allItems;
    }

    private VBox createPriceSection(String itemName, double originalPrice) {
        VBox priceSection = new VBox(1);
        priceSection.setAlignment(Pos.CENTER);

        // Check if item has discount
        double discountPercent = getItemDiscount(itemName);

        if (discountPercent > 0) {
            // Show original price with strikethrough
            Label originalPriceLabel = new Label("₹" + String.format("%.0f", originalPrice));
            originalPriceLabel.setStyle("-fx-font-size: 7px; -fx-text-fill: #999; -fx-strikethrough: true;");

            // Calculate and show discounted price
            double discountedPrice = originalPrice * (1 - discountPercent / 100);
            Label discountedPriceLabel = new Label("₹" + String.format("%.0f", discountedPrice));
            discountedPriceLabel.setStyle("-fx-font-size: 9px; -fx-font-weight: bold; -fx-text-fill: #e74c3c;");

            // Show discount percentage
            Label discountLabel = new Label(String.format("%.0f%% OFF", discountPercent));
            discountLabel.setStyle("-fx-font-size: 6px; -fx-text-fill: white; -fx-background-color: #e74c3c; " +
                                 "-fx-padding: 1 2; -fx-background-radius: 2;");

            priceSection.getChildren().addAll(originalPriceLabel, discountedPriceLabel, discountLabel);
        } else {
            // Show regular price
            Label priceLabel = new Label("₹" + String.format("%.0f", originalPrice));
            priceLabel.setStyle("-fx-font-size: 9px; -fx-text-fill: #666;");
            priceSection.getChildren().add(priceLabel);
        }

        return priceSection;
    }

    private double getItemDiscount(String itemName) {
        // Sample discount data - in real app, this would come from database
        switch (itemName.toLowerCase()) {
            case "strawberry mojito":
                return 10.0; // 10% discount
            case "cheese garlic bread":
                return 15.0; // 15% discount
            case "chicken angara (boneless)":
                return 20.0; // 20% discount
            case "dahi ke shola":
                return 5.0;  // 5% discount
            case "raj kachori":
                return 12.0; // 12% discount
            case "grilled paneer sandwich":
                return 8.0;  // 8% discount
            case "chilli mushroom":
                return 18.0; // 18% discount
            default:
                return 0.0;  // No discount
        }
    }

    private void showDiscountOptions(String itemName, double originalPrice) {
        try {
            // Create discount options dialog
            Dialog<ButtonType> discountDialog = new Dialog<>();
            discountDialog.setTitle("Discount Options");
            discountDialog.setHeaderText("Apply Discount to: " + itemName + "\nOriginal Price: ₹" + String.format("%.2f", originalPrice));

            // Create discount options
            VBox discountContent = new VBox(10);
            discountContent.setPadding(new Insets(20));

            // Quick discount buttons
            Label quickLabel = new Label("Quick Discounts:");
            quickLabel.setStyle("-fx-font-weight: bold;");

            HBox quickDiscounts = new HBox(10);
            quickDiscounts.setAlignment(Pos.CENTER);

            Button discount5 = new Button("5% OFF");
            discount5.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-padding: 8 12;");
            discount5.setOnAction(e -> applyDiscountAndAdd(itemName, originalPrice, 5.0, discountDialog));

            Button discount10 = new Button("10% OFF");
            discount10.setStyle("-fx-background-color: #ffc107; -fx-text-fill: black; -fx-padding: 8 12;");
            discount10.setOnAction(e -> applyDiscountAndAdd(itemName, originalPrice, 10.0, discountDialog));

            Button discount15 = new Button("15% OFF");
            discount15.setStyle("-fx-background-color: #fd7e14; -fx-text-fill: white; -fx-padding: 8 12;");
            discount15.setOnAction(e -> applyDiscountAndAdd(itemName, originalPrice, 15.0, discountDialog));

            Button discount20 = new Button("20% OFF");
            discount20.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-padding: 8 12;");
            discount20.setOnAction(e -> applyDiscountAndAdd(itemName, originalPrice, 20.0, discountDialog));

            quickDiscounts.getChildren().addAll(discount5, discount10, discount15, discount20);

            // Custom discount input
            Label customLabel = new Label("Custom Discount:");
            customLabel.setStyle("-fx-font-weight: bold;");

            HBox customDiscountBox = new HBox(10);
            customDiscountBox.setAlignment(Pos.CENTER);

            TextField customDiscountField = new TextField();
            customDiscountField.setPromptText("Enter % (0-100)");
            customDiscountField.setPrefWidth(120);

            Button applyCustom = new Button("Apply Custom");
            applyCustom.setStyle("-fx-background-color: #6f42c1; -fx-text-fill: white; -fx-padding: 8 12;");
            applyCustom.setOnAction(e -> {
                try {
                    double customDiscount = Double.parseDouble(customDiscountField.getText());
                    if (customDiscount >= 0 && customDiscount <= 100) {
                        applyDiscountAndAdd(itemName, originalPrice, customDiscount, discountDialog);
                    } else {
                        showAlert("Invalid Discount", "Discount must be between 0% and 100%");
                    }
                } catch (NumberFormatException ex) {
                    showAlert("Invalid Input", "Please enter a valid number");
                }
            });

            customDiscountBox.getChildren().addAll(customDiscountField, applyCustom);

            // No discount option
            Button noDiscount = new Button("Add Without Discount");
            noDiscount.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-padding: 8 16;");
            noDiscount.setOnAction(e -> {
                addItemToOrder(itemName, originalPrice);
                discountDialog.close();
            });

            discountContent.getChildren().addAll(
                quickLabel,
                quickDiscounts,
                customLabel,
                customDiscountBox,
                noDiscount
            );

            discountDialog.getDialogPane().setContent(discountContent);
            discountDialog.getDialogPane().getButtonTypes().add(ButtonType.CANCEL);
            discountDialog.showAndWait();

        } catch (Exception e) {
            showAlert("Error", "Failed to show discount options: " + e.getMessage());
        }
    }

    private void applyDiscountAndAdd(String itemName, double originalPrice, double discountPercent, Dialog<ButtonType> dialog) {
        try {
            double discountedPrice = originalPrice * (1 - discountPercent / 100);
            double discountAmount = originalPrice - discountedPrice;

            // Add item with discount
            addItemToOrderWithDiscount(itemName, originalPrice, discountedPrice, discountPercent);

            // Show confirmation
            showAlert("Discount Applied",
                     "Item: " + itemName + "\n" +
                     "Original Price: ₹" + String.format("%.2f", originalPrice) + "\n" +
                     "Discount: " + String.format("%.1f", discountPercent) + "% (₹" + String.format("%.2f", discountAmount) + ")\n" +
                     "Final Price: ₹" + String.format("%.2f", discountedPrice) + "\n\n" +
                     "Item added to order with discount!");

            dialog.close();

        } catch (Exception e) {
            showAlert("Error", "Failed to apply discount: " + e.getMessage());
        }
    }

    private void addItemToOrderWithDiscount(String itemName, double originalPrice, double discountedPrice, double discountPercent) {
        try {
            System.out.println("Adding to order with discount: " + itemName +
                              " - Original: ₹" + originalPrice +
                              " - Discounted: ₹" + discountedPrice +
                              " (" + discountPercent + "% off)");

            // Get current table number
            int currentTableNumber = getCurrentTableNumber();
            if (currentTableNumber <= 0) {
                showAlert("Error", "No table selected. Please select a table first.");
                return;
            }

            // Create MenuItem object with discounted price
            MenuItem menuItem = findMenuItemByName(itemName, originalPrice);
            if (menuItem == null) {
                // Create a new MenuItem if not found
                menuItem = new MenuItem(
                    generateMenuItemId(itemName),
                    itemName,
                    discountedPrice, // Use discounted price
                    "Main Course", // Default category
                    1 // Default category ID
                );
            } else {
                // Create a copy with discounted price
                menuItem = new MenuItem(
                    menuItem.getId(),
                    menuItem.getName(),
                    discountedPrice, // Use discounted price
                    menuItem.getCategory(),
                    menuItem.getCategoryId()
                );
            }

            // Add item to order using OrderManager
            orderManager.addItemToTable(currentTableNumber, menuItem, 1);

            System.out.println("✅ Discounted item added successfully: " + itemName + " to Table " + currentTableNumber);

        } catch (Exception e) {
            System.err.println("Error adding discounted item to order: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Failed to add discounted item to order: " + e.getMessage());
        }
    }

    private VBox createBottomButtonBar(int tableNumber, Dialog<ButtonType> dialog) {
        VBox buttonBarContainer = new VBox(5);
        buttonBarContainer.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 1 0 0 0;");
        buttonBarContainer.setPadding(new Insets(10));

        // Create action buttons - First row (Primary actions)
        HBox firstRowButtons = new HBox(5);
        firstRowButtons.setAlignment(Pos.CENTER);

        Button saveBtn = new Button("Save Order");
        saveBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-size: 9px; -fx-padding: 5 10; -fx-font-weight: bold;");
        saveBtn.setOnAction(e -> saveOrder(tableNumber));

        Button savePrintBtn = new Button("Save & Print");
        savePrintBtn.setStyle("-fx-background-color: #20c997; -fx-text-fill: white; -fx-font-size: 9px; -fx-padding: 5 10; -fx-font-weight: bold;");
        savePrintBtn.setOnAction(e -> saveAndPrint(tableNumber));

        Button saveEBillBtn = new Button("Save & eBill");
        saveEBillBtn.setStyle("-fx-background-color: #6f42c1; -fx-text-fill: white; -fx-font-size: 9px; -fx-padding: 5 10; -fx-font-weight: bold;");
        saveEBillBtn.setOnAction(e -> saveAndEBill(tableNumber));

        Button kotPrintBtn = new Button("KOT & Print");
        kotPrintBtn.setStyle("-fx-background-color: #e83e8c; -fx-text-fill: white; -fx-font-size: 9px; -fx-padding: 5 10; -fx-font-weight: bold;");
        kotPrintBtn.setOnAction(e -> kotAndPrint(tableNumber));

        firstRowButtons.getChildren().addAll(saveBtn, savePrintBtn, saveEBillBtn, kotPrintBtn);

        // Create action buttons - Second row (Secondary actions)
        HBox secondRowButtons = new HBox(5);
        secondRowButtons.setAlignment(Pos.CENTER);

        Button printKOTBtn = new Button("Print KOT");
        printKOTBtn.setStyle("-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-font-size: 9px; -fx-padding: 5 10; -fx-font-weight: bold;");
        printKOTBtn.setOnAction(e -> printKOT(tableNumber));

        Button generateBillBtn = new Button("Generate Bill");
        generateBillBtn.setStyle("-fx-background-color: #ffc107; -fx-text-fill: black; -fx-font-size: 9px; -fx-padding: 5 10; -fx-font-weight: bold;");
        generateBillBtn.setOnAction(e -> generateBill(tableNumber));

        Button holdOrderBtn = new Button("Hold Order");
        holdOrderBtn.setStyle("-fx-background-color: #fd7e14; -fx-text-fill: white; -fx-font-size: 9px; -fx-padding: 5 10; -fx-font-weight: bold;");
        holdOrderBtn.setOnAction(e -> holdOrder(tableNumber));

        Button closeBtn = new Button("Close");
        closeBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-size: 9px; -fx-padding: 5 10; -fx-font-weight: bold;");
        closeBtn.setOnAction(e -> {
            try {
                System.out.println("BillingKOTController: Close button clicked");
                Platform.runLater(() -> {
                    try {
                        dialog.close();
                        System.out.println("BillingKOTController: Dialog closed successfully");
                    } catch (Exception ex) {
                        System.err.println("Error closing dialog: " + ex.getMessage());
                        ex.printStackTrace();
                    }
                });
            } catch (Exception ex) {
                System.err.println("Error in close button handler: " + ex.getMessage());
                ex.printStackTrace();
            }
        });

        secondRowButtons.getChildren().addAll(printKOTBtn, generateBillBtn, holdOrderBtn, closeBtn);

        buttonBarContainer.getChildren().addAll(firstRowButtons, secondRowButtons);
        return buttonBarContainer;
    }

    // Button action implementations
    private void saveOrder(int tableNumber) {
        try {
            System.out.println("BillingKOTController: Saving order for Table " + tableNumber);

            // Get current order from the table
            Order currentOrder = getCurrentOrder(tableNumber);
            if (currentOrder == null || currentOrder.getItems().isEmpty()) {
                showAlert("Warning", "No items in the order to save!");
                return;
            }

            // Update order status
            currentOrder.setStatus("SAVED");
            currentOrder.setTimestamp(java.time.LocalDateTime.now());

            // Save to database (simulated)
            saveOrderToDatabase(currentOrder);

            // Update table status
            updateTableStatus(tableNumber, "OCCUPIED");

            showAlert("Success", "Order saved successfully for Table " + tableNumber +
                     "\nTotal Amount: ₹" + String.format("%.2f", currentOrder.getTotalAmount()));

        } catch (Exception e) {
            showAlert("Error", "Failed to save order: " + e.getMessage());
        }
    }

    private void saveAndPrint(int tableNumber) {
        try {
            System.out.println("BillingKOTController: Save & Print for Table " + tableNumber);

            // First save the order
            saveOrder(tableNumber);

            // Then print the order
            Order currentOrder = getCurrentOrder(tableNumber);
            if (currentOrder != null) {
                printOrderReceipt(currentOrder);
                showAlert("Success", "Order saved and receipt printed for Table " + tableNumber);
            }

        } catch (Exception e) {
            showAlert("Error", "Failed to save and print: " + e.getMessage());
        }
    }

    private void saveAndEBill(int tableNumber) {
        try {
            System.out.println("BillingKOTController: Save & eBill for Table " + tableNumber);

            // First save the order
            saveOrder(tableNumber);

            // Get customer details for eBill
            Order currentOrder = getCurrentOrder(tableNumber);
            if (currentOrder != null) {
                String customerEmail = getCustomerEmail(tableNumber);
                String customerMobile = getCustomerMobile(tableNumber);

                if (customerEmail != null && !customerEmail.isEmpty()) {
                    sendEBillEmail(currentOrder, customerEmail);
                }

                if (customerMobile != null && !customerMobile.isEmpty()) {
                    sendEBillSMS(currentOrder, customerMobile);
                }

                showAlert("Success", "Order saved and eBill sent for Table " + tableNumber);
            }

        } catch (Exception e) {
            showAlert("Error", "Failed to save and send eBill: " + e.getMessage());
        }
    }

    private void kotAndPrint(int tableNumber) {
        try {
            System.out.println("BillingKOTController: KOT & Print for Table " + tableNumber);

            Order currentOrder = getCurrentOrder(tableNumber);
            if (currentOrder == null || currentOrder.getItems().isEmpty()) {
                showAlert("Warning", "No items in the order to print KOT!");
                return;
            }

            // Generate KOT number
            String kotNumber = generateKOTNumber();
            // Note: KOT number would be stored in a separate field or notes
            currentOrder.setNotes("KOT: " + kotNumber);

            // Print KOT to kitchen
            printKOTToKitchen(currentOrder);

            // Update order status
            currentOrder.setStatus("KOT_PRINTED");

            showAlert("Success", "KOT #" + kotNumber + " printed for Table " + tableNumber);

        } catch (Exception e) {
            showAlert("Error", "Failed to print KOT: " + e.getMessage());
        }
    }

    private void printKOT(int tableNumber) {
        try {
            System.out.println("BillingKOTController: Printing KOT for Table " + tableNumber);

            Order currentOrder = getCurrentOrder(tableNumber);
            if (currentOrder == null || currentOrder.getItems().isEmpty()) {
                showAlert("Warning", "No items in the order to print KOT!");
                return;
            }

            // Print KOT
            printKOTToKitchen(currentOrder);
            showAlert("Success", "KOT printed for Table " + tableNumber);

        } catch (Exception e) {
            showAlert("Error", "Failed to print KOT: " + e.getMessage());
        }
    }

    private void generateBill(int tableNumber) {
        try {
            System.out.println("BillingKOTController: Generating bill for Table " + tableNumber);

            Order currentOrder = getCurrentOrder(tableNumber);
            if (currentOrder == null || currentOrder.getItems().isEmpty()) {
                showAlert("Warning", "No items in the order to generate bill!");
                return;
            }

            // Calculate final amounts
            calculateFinalBill(currentOrder);

            // Generate bill number
            String billNumber = generateBillNumber();
            // Note: Bill number would be stored in notes or separate field
            currentOrder.setNotes("BILL: " + billNumber);

            // Update order status
            currentOrder.setStatus("BILL_GENERATED");

            // Show bill details
            showBillDialog(currentOrder);

        } catch (Exception e) {
            showAlert("Error", "Failed to generate bill: " + e.getMessage());
        }
    }

    private void holdOrder(int tableNumber) {
        try {
            System.out.println("BillingKOTController: Order held for Table " + tableNumber);

            Order currentOrder = getCurrentOrder(tableNumber);
            if (currentOrder == null || currentOrder.getItems().isEmpty()) {
                showAlert("Warning", "No items in the order to hold!");
                return;
            }

            // Update order status to hold
            currentOrder.setStatus("ON_HOLD");

            // Save the held order
            saveOrderToDatabase(currentOrder);

            // Update table status
            updateTableStatus(tableNumber, "ON_HOLD");

            showAlert("Success", "Order held for Table " + tableNumber +
                     "\nYou can resume this order later.");

        } catch (Exception e) {
            showAlert("Error", "Failed to hold order: " + e.getMessage());
        }
    }

    // Payment method implementations
    private void processCashPayment() {
        try {
            System.out.println("Processing cash payment");

            // Get current order total
            double totalAmount = getCurrentOrderTotal();
            if (totalAmount <= 0) {
                showAlert("Warning", "No order amount to process payment!");
                return;
            }

            // Show cash payment dialog
            TextInputDialog dialog = new TextInputDialog(String.valueOf(totalAmount));
            dialog.setTitle("Cash Payment");
            dialog.setHeaderText("Process Cash Payment");
            dialog.setContentText("Total Amount: ₹" + String.format("%.2f", totalAmount) +
                                 "\nEnter received amount:");

            Optional<String> result = dialog.showAndWait();
            if (result.isPresent()) {
                try {
                    double receivedAmount = Double.parseDouble(result.get());
                    if (receivedAmount >= totalAmount) {
                        double change = receivedAmount - totalAmount;
                        processPayment("CASH", totalAmount, receivedAmount);

                        String message = "Cash payment processed successfully!" +
                                       "\nTotal: ₹" + String.format("%.2f", totalAmount) +
                                       "\nReceived: ₹" + String.format("%.2f", receivedAmount);
                        if (change > 0) {
                            message += "\nChange to return: ₹" + String.format("%.2f", change);
                        }
                        showAlert("Payment Success", message);
                    } else {
                        showAlert("Error", "Received amount is less than total amount!");
                    }
                } catch (NumberFormatException e) {
                    showAlert("Error", "Please enter a valid amount!");
                }
            }

        } catch (Exception e) {
            showAlert("Error", "Failed to process cash payment: " + e.getMessage());
        }
    }

    private void processCardPayment() {
        try {
            System.out.println("Processing card payment");

            double totalAmount = getCurrentOrderTotal();
            if (totalAmount <= 0) {
                showAlert("Warning", "No order amount to process payment!");
                return;
            }

            // Show card payment options
            ChoiceDialog<String> dialog = new ChoiceDialog<>("Credit Card",
                "Credit Card", "Debit Card", "Contactless");
            dialog.setTitle("Card Payment");
            dialog.setHeaderText("Select Card Type");
            dialog.setContentText("Total Amount: ₹" + String.format("%.2f", totalAmount) +
                                 "\nChoose card type:");

            Optional<String> result = dialog.showAndWait();
            if (result.isPresent()) {
                String cardType = result.get();

                // Simulate card processing
                showAlert("Processing", "Processing " + cardType + " payment...\nPlease wait...");

                // Process payment
                processPayment("CARD_" + cardType.toUpperCase().replace(" ", "_"), totalAmount, totalAmount);

                showAlert("Payment Success", cardType + " payment processed successfully!" +
                         "\nAmount: ₹" + String.format("%.2f", totalAmount) +
                         "\nTransaction completed.");
            }

        } catch (Exception e) {
            showAlert("Error", "Failed to process card payment: " + e.getMessage());
        }
    }

    private void processUPIPayment() {
        try {
            System.out.println("Processing UPI payment");

            double totalAmount = getCurrentOrderTotal();
            if (totalAmount <= 0) {
                showAlert("Warning", "No order amount to process payment!");
                return;
            }

            // Show UPI payment dialog
            TextInputDialog dialog = new TextInputDialog();
            dialog.setTitle("UPI Payment");
            dialog.setHeaderText("Process UPI Payment");
            dialog.setContentText("Total Amount: ₹" + String.format("%.2f", totalAmount) +
                                 "\nEnter UPI Transaction ID:");

            Optional<String> result = dialog.showAndWait();
            if (result.isPresent()) {
                String transactionId = result.get().trim();
                if (!transactionId.isEmpty()) {
                    // Process UPI payment
                    processPayment("UPI", totalAmount, totalAmount);

                    showAlert("Payment Success", "UPI payment processed successfully!" +
                             "\nAmount: ₹" + String.format("%.2f", totalAmount) +
                             "\nTransaction ID: " + transactionId);
                } else {
                    showAlert("Error", "Please enter a valid transaction ID!");
                }
            }

        } catch (Exception e) {
            showAlert("Error", "Failed to process UPI payment: " + e.getMessage());
        }
    }

    private void processDuePayment() {
        try {
            System.out.println("Processing due payment");

            double totalAmount = getCurrentOrderTotal();
            if (totalAmount <= 0) {
                showAlert("Warning", "No order amount to process payment!");
                return;
            }

            // Get customer details for due payment
            TextInputDialog customerDialog = new TextInputDialog();
            customerDialog.setTitle("Due Payment");
            customerDialog.setHeaderText("Customer Details Required");
            customerDialog.setContentText("Enter customer name or phone number:");

            Optional<String> customerResult = customerDialog.showAndWait();
            if (customerResult.isPresent()) {
                String customerInfo = customerResult.get().trim();
                if (!customerInfo.isEmpty()) {
                    // Process due payment
                    processDuePaymentRecord(customerInfo, totalAmount);

                    showAlert("Payment Success", "Due payment recorded successfully!" +
                             "\nCustomer: " + customerInfo +
                             "\nAmount: ₹" + String.format("%.2f", totalAmount) +
                             "\nAdded to customer account.");
                } else {
                    showAlert("Error", "Customer information is required for due payment!");
                }
            }

        } catch (Exception e) {
            showAlert("Error", "Failed to process due payment: " + e.getMessage());
        }
    }

    private void processPartPayment() {
        try {
            System.out.println("Processing part payment");

            double totalAmount = getCurrentOrderTotal();
            if (totalAmount <= 0) {
                showAlert("Warning", "No order amount to process payment!");
                return;
            }

            // Show part payment dialog
            TextInputDialog dialog = new TextInputDialog();
            dialog.setTitle("Part Payment");
            dialog.setHeaderText("Process Partial Payment");
            dialog.setContentText("Total Amount: ₹" + String.format("%.2f", totalAmount) +
                                 "\nEnter partial amount to pay:");

            Optional<String> result = dialog.showAndWait();
            if (result.isPresent()) {
                try {
                    double partialAmount = Double.parseDouble(result.get());
                    if (partialAmount > 0 && partialAmount < totalAmount) {
                        double remainingAmount = totalAmount - partialAmount;

                        // Process partial payment
                        processPartialPayment(partialAmount, remainingAmount);

                        showAlert("Payment Success", "Partial payment processed successfully!" +
                                 "\nPaid Amount: ₹" + String.format("%.2f", partialAmount) +
                                 "\nRemaining Amount: ₹" + String.format("%.2f", remainingAmount));
                    } else {
                        showAlert("Error", "Partial amount must be greater than 0 and less than total amount!");
                    }
                } catch (NumberFormatException e) {
                    showAlert("Error", "Please enter a valid amount!");
                }
            }

        } catch (Exception e) {
            showAlert("Error", "Failed to process part payment: " + e.getMessage());
        }
    }

    /**
     * Process Hold Order - Prompts for reason and holds the order
     */
    private void processHoldOrder() {
        System.out.println("BillingKOTController: Processing hold order");

        // Check if there's an active table selected
        int tableNumber = getCurrentTableNumber();
        if (tableNumber <= 0) {
            System.out.println("BillingKOTController: No table selected - cannot hold order");
            Platform.runLater(() -> showAlert("No Table Selected", "Please select a table with an active order to hold."));
            return;
        }

        // Check if there's an active order for this table
        Order currentOrder = getCurrentOrder(tableNumber);
        if (currentOrder == null) {
            System.out.println("BillingKOTController: No active order found for table " + tableNumber);
            Platform.runLater(() -> showAlert("No Active Order", "No active order found for Table " + tableNumber + " to hold."));
            return;
        }

        System.out.println("BillingKOTController: Found active order for table " + tableNumber + " - proceeding with hold dialog");

        // Create a dialog to get the hold reason
        Dialog<String> dialog = new Dialog<>();
        dialog.setTitle("Hold Order");
        dialog.setHeaderText("Please provide a reason for holding this order:");

        // Set the button types
        ButtonType holdButtonType = new ButtonType("Hold Order", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(holdButtonType, ButtonType.CANCEL);

        // Create the reason input field
        VBox content = new VBox(10);
        content.setStyle("-fx-padding: 20;");

        Label instructionLabel = new Label("Reason for holding the order:");
        instructionLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 12px;");

        TextArea reasonTextArea = new TextArea();
        reasonTextArea.setPromptText("Enter reason for holding the order (e.g., Customer requested delay, Kitchen issue, etc.)");
        reasonTextArea.setPrefRowCount(4);
        reasonTextArea.setPrefColumnCount(40);
        reasonTextArea.setWrapText(true);

        // Add some common reasons as buttons for quick selection
        Label quickReasonsLabel = new Label("Quick Reasons (Press 1-4 or click):");
        quickReasonsLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 11px; -fx-text-fill: #666;");

        // Add keyboard shortcut instruction
        Label shortcutLabel = new Label("💡 Tip: Press Ctrl+H to open this dialog, Enter to submit, ESC to cancel");
        shortcutLabel.setStyle("-fx-font-size: 9px; -fx-text-fill: #888; -fx-font-style: italic;");

        HBox quickReasonsBox = new HBox(8);
        quickReasonsBox.setAlignment(Pos.CENTER_LEFT);

        Button customerRequestBtn = new Button("1. Customer Request");
        customerRequestBtn.setStyle("-fx-background-color: #e9ecef; -fx-text-fill: #495057; -fx-font-size: 9px; -fx-padding: 4 8;");
        customerRequestBtn.setOnAction(e -> {
            reasonTextArea.setText("Customer requested to hold the order");
            reasonTextArea.requestFocus();
        });

        Button kitchenIssueBtn = new Button("2. Kitchen Issue");
        kitchenIssueBtn.setStyle("-fx-background-color: #e9ecef; -fx-text-fill: #495057; -fx-font-size: 9px; -fx-padding: 4 8;");
        kitchenIssueBtn.setOnAction(e -> {
            reasonTextArea.setText("Kitchen issue - order needs to be held");
            reasonTextArea.requestFocus();
        });

        Button paymentIssueBtn = new Button("3. Payment Issue");
        paymentIssueBtn.setStyle("-fx-background-color: #e9ecef; -fx-text-fill: #495057; -fx-font-size: 9px; -fx-padding: 4 8;");
        paymentIssueBtn.setOnAction(e -> {
            reasonTextArea.setText("Payment issue - holding order until resolved");
            reasonTextArea.requestFocus();
        });

        Button otherBtn = new Button("4. Other");
        otherBtn.setStyle("-fx-background-color: #e9ecef; -fx-text-fill: #495057; -fx-font-size: 9px; -fx-padding: 4 8;");
        otherBtn.setOnAction(e -> {
            reasonTextArea.setText("");
            reasonTextArea.requestFocus();
        });

        quickReasonsBox.getChildren().addAll(customerRequestBtn, kitchenIssueBtn, paymentIssueBtn, otherBtn);

        content.getChildren().addAll(instructionLabel, reasonTextArea, quickReasonsLabel, quickReasonsBox, shortcutLabel);
        dialog.getDialogPane().setContent(content);

        // Add keyboard shortcuts for quick reasons (1-4 keys)
        dialog.getDialogPane().setOnKeyPressed(keyEvent -> {
            switch (keyEvent.getCode()) {
                case DIGIT1:
                case NUMPAD1:
                    customerRequestBtn.fire();
                    keyEvent.consume();
                    break;
                case DIGIT2:
                case NUMPAD2:
                    kitchenIssueBtn.fire();
                    keyEvent.consume();
                    break;
                case DIGIT3:
                case NUMPAD3:
                    paymentIssueBtn.fire();
                    keyEvent.consume();
                    break;
                case DIGIT4:
                case NUMPAD4:
                    otherBtn.fire();
                    keyEvent.consume();
                    break;
                case ESCAPE:
                    // ESC key to cancel
                    System.out.println("BillingKOTController: ESC pressed in hold dialog - canceling");
                    dialog.close();
                    keyEvent.consume();
                    break;
                default:
                    // Let other keys pass through
                    break;
            }
        });

        // Enable/Disable hold button depending on whether reason is provided
        Node holdButton = dialog.getDialogPane().lookupButton(holdButtonType);
        holdButton.setDisable(true);

        // Add listener to enable button when text is entered
        reasonTextArea.textProperty().addListener((observable, oldValue, newValue) -> {
            holdButton.setDisable(newValue.trim().isEmpty());
        });

        // Add Enter key handler to the text area for quick submission
        reasonTextArea.setOnKeyPressed(keyEvent -> {
            if (keyEvent.getCode() == KeyCode.ENTER && !keyEvent.isShiftDown()) {
                // Enter key pressed (without Shift) - submit the hold request
                if (!reasonTextArea.getText().trim().isEmpty()) {
                    System.out.println("BillingKOTController: Enter key pressed in hold reason dialog");
                    keyEvent.consume();

                    // Trigger the hold button action
                    Platform.runLater(() -> {
                        try {
                            // Get the reason and close dialog
                            String reason = reasonTextArea.getText().trim();
                            dialog.setResult(reason);
                            dialog.close();
                        } catch (Exception e) {
                            System.err.println("Error handling Enter key in hold dialog: " + e.getMessage());
                            e.printStackTrace();
                        }
                    });
                }
            } else if (keyEvent.getCode() == KeyCode.ENTER && keyEvent.isShiftDown()) {
                // Shift+Enter - add new line (default behavior)
                System.out.println("BillingKOTController: Shift+Enter pressed - adding new line");
            }
        });

        // Convert the result when the hold button is clicked
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == holdButtonType) {
                return reasonTextArea.getText().trim();
            }
            return null;
        });

        // Show dialog and process result asynchronously
        try {
            Optional<String> result = dialog.showAndWait();
            result.ifPresent(reason -> {
                if (!reason.isEmpty()) {
                    // Process hold operation in background to prevent UI blocking
                    Platform.runLater(() -> holdOrderWithReason(reason));
                }
            });
        } catch (Exception e) {
            System.err.println("Error showing hold dialog: " + e.getMessage());
            e.printStackTrace();
            Platform.runLater(() -> showAlert("Error", "Failed to show hold dialog: " + e.getMessage()));
        }
    }

    /**
     * Hold the order with the provided reason (non-blocking)
     */
    private void holdOrderWithReason(String reason) {
        try {
            System.out.println("BillingKOTController: Holding order with reason: " + reason);

            // Get current order details
            int tableNumber = getCurrentTableNumber();
            Order currentOrder = getCurrentOrder(tableNumber);

            if (currentOrder == null) {
                System.err.println("No active order found to hold for table: " + tableNumber);
                Platform.runLater(() -> showAlert("Error", "No active order found to hold."));
                return;
            }

            System.out.println("BillingKOTController: Found existing order for table " + tableNumber + " with " + currentOrder.getItems().size() + " items");

            // Update order status to HELD
            currentOrder.setStatus("HELD");

            // Add hold reason to order notes
            String currentNotes = currentOrder.getNotes() != null ? currentOrder.getNotes() : "";
            String holdNote = "\n[HELD] " + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) +
                             " - Reason: " + reason;
            currentOrder.setNotes(currentNotes + holdNote);

            // Perform database update in background
            Task<Boolean> holdTask = new Task<Boolean>() {
                @Override
                protected Boolean call() throws Exception {
                    System.out.println("BillingKOTController: Updating order status to HELD in database...");
                    return OrderDAO.updateOrderStatus(currentOrder.getId(), "HELD");
                }

                @Override
                protected void succeeded() {
                    Platform.runLater(() -> {
                        try {
                            Boolean success = getValue();
                            if (success) {
                                System.out.println("BillingKOTController: Database update successful");

                                // Update the order manager to mark order as held
                                if (orderManager != null) {
                                    orderManager.markOrderHeld(tableNumber, reason);
                                }

                                // Show success message (non-blocking)
                                showAlert("Order Held",
                                    "Order for Table " + tableNumber + " has been held.\n\n" +
                                    "Reason: " + reason + "\n\n" +
                                    "The order can be resumed later from the order management screen.");

                                // Refresh the display (non-blocking)
                                refreshTables();

                                System.out.println("Order successfully held for Table " + tableNumber + " with reason: " + reason);

                            } else {
                                System.err.println("Failed to update order status in database");
                                showAlert("Error", "Failed to hold the order. Please try again.");
                            }
                        } catch (Exception e) {
                            System.err.println("Error in hold task success handler: " + e.getMessage());
                            e.printStackTrace();
                            showAlert("Error", "An error occurred while processing the hold request: " + e.getMessage());
                        }
                    });
                }

                @Override
                protected void failed() {
                    Platform.runLater(() -> {
                        Throwable exception = getException();
                        System.err.println("Hold task failed: " + exception.getMessage());
                        exception.printStackTrace();
                        showAlert("Error", "Failed to hold the order: " + exception.getMessage());
                    });
                }
            };

            // Run the task in background thread
            Thread holdThread = new Thread(holdTask);
            holdThread.setDaemon(true);
            holdThread.start();

        } catch (Exception e) {
            System.err.println("Error in holdOrderWithReason: " + e.getMessage());
            e.printStackTrace();
            Platform.runLater(() -> showAlert("Error", "An error occurred while holding the order: " + e.getMessage()));
        }
    }

    /**
     * Safe dialog handling utility to prevent UI blocking
     */
    private void showDialogSafely(Dialog<?> dialog) {
        try {
            // Close any existing dialog first
            if (currentOpenDialog != null) {
                try {
                    currentOpenDialog.close();
                } catch (Exception e) {
                    System.err.println("Error closing existing dialog: " + e.getMessage());
                }
                currentOpenDialog = null;
            }

            // Set current dialog
            currentOpenDialog = dialog;

            // Set dialog properties to prevent hanging
            dialog.setResizable(true);

            // Add close request handler
            dialog.setOnCloseRequest(e -> {
                System.out.println("Dialog close requested");
                currentOpenDialog = null;
            });

            // Add result converter to handle close
            dialog.setResultConverter(buttonType -> {
                currentOpenDialog = null;
                return null;
            });

            // Show dialog with timeout protection
            try {
                dialog.showAndWait();
            } catch (Exception e) {
                System.err.println("Error showing dialog: " + e.getMessage());
                e.printStackTrace();
            } finally {
                currentOpenDialog = null;
            }

        } catch (Exception e) {
            System.err.println("Error in showDialogSafely: " + e.getMessage());
            e.printStackTrace();
            currentOpenDialog = null;
        }
    }

    /**
     * Force close any open dialogs
     */
    private void forceCloseDialogs() {
        try {
            if (currentOpenDialog != null) {
                Platform.runLater(() -> {
                    try {
                        currentOpenDialog.close();
                    } catch (Exception e) {
                        System.err.println("Error force closing dialog: " + e.getMessage());
                    } finally {
                        currentOpenDialog = null;
                    }
                });
            }
        } catch (Exception e) {
            System.err.println("Error in forceCloseDialogs: " + e.getMessage());
        }
    }

    // Offer and feature implementations
    private void sendSMSFeedback() {
        System.out.println("Sending SMS feedback");
        showAlert("SMS Feedback", "SMS feedback request sent to customer mobile number.");
    }

    private void applyBOGOOffer() {
        System.out.println("Applying BOGO offer");
        showAlert("BOGO Offer", "Buy One Get One offer applied! Second item will be free.");
    }

    private void splitBill() {
        System.out.println("Splitting bill");
        showAlert("Split Bill", "Bill will be split. Please specify number of splits.");
    }

    private void markComplimentary() {
        System.out.println("Marking order as complimentary");
        showAlert("Complimentary", "Order marked as complimentary. No payment required.");
    }

    private void applyDiscountOffer() {
        System.out.println("Applying discount");
        showAlert("Discount", "Please enter discount percentage or amount.");
    }

    // Helper methods for order management
    private Order getCurrentOrder(int tableNumber) {
        try {
            // Get order from OrderManager first
            Order order = orderManager.getOrderForTable(tableNumber);

            if (order != null) {
                System.out.println("BillingKOTController: Found existing order for table " + tableNumber +
                                 " with " + order.getItems().size() + " items");
                return order;
            }

            // If no order exists in OrderManager, check if there's a sample order for demo
            if (tableNumber % 4 == 0) { // Only certain tables have demo orders
                System.out.println("BillingKOTController: Creating demo order for table " + tableNumber);
                order = new Order();
                order.setTableNumber(tableNumber);
                order.setStatus("ACTIVE");

                // Add sample items for demo
                MenuItem menuItem1 = new MenuItem(1, "Chicken Biryani", 220.0, "Biryani", 1);
                OrderItem item1 = new OrderItem(menuItem1, 2);

                MenuItem menuItem2 = new MenuItem(2, "Paneer Tikka", 200.0, "Tandoori", 2);
                OrderItem item2 = new OrderItem(menuItem2, 1);

                order.addItem(item1);
                order.addItem(item2);

                // Save to OrderManager for consistency
                orderManager.saveOrderForTable(tableNumber, order);

                return order;
            }

            System.out.println("BillingKOTController: No order found for table " + tableNumber);
            return null;

        } catch (Exception e) {
            System.err.println("Error getting current order: " + e.getMessage());
            return null;
        }
    }

    private double getCurrentOrderTotal() {
        try {
            // Get total from OrderManager for all active tables
            double total = 0.0;
            for (Integer tableNumber : orderManager.getActiveTableNumbers()) {
                total += orderManager.getTableOrderTotal(tableNumber);
            }
            return total > 0 ? total : 420.0; // Fallback to sample total if no orders
        } catch (Exception e) {
            System.err.println("Error calculating order total: " + e.getMessage());
            return 420.0; // Fallback total
        }
    }

    private void saveOrderToDatabase(Order order) {
        try {
            // Save order to database
            System.out.println("Saving order to database: " + order.getTableNumber());
            // In real implementation, use OrderDAO to save
            // orderDAO.save(order);
        } catch (Exception e) {
            System.err.println("Error saving order to database: " + e.getMessage());
        }
    }

    private void updateTableStatus(int tableNumber, String status) {
        try {
            // Update table status
            System.out.println("Updating table " + tableNumber + " status to: " + status);
            // In real implementation, update table status in database
        } catch (Exception e) {
            System.err.println("Error updating table status: " + e.getMessage());
        }
    }

    private void printOrderReceipt(Order order) {
        try {
            System.out.println("Printing receipt for order: " + order.getTableNumber());
            // In real implementation, use PrintService to print receipt
            // PrintService.printReceipt(order);
        } catch (Exception e) {
            System.err.println("Error printing receipt: " + e.getMessage());
        }
    }

    private String getCustomerEmail(int tableNumber) {
        // Get customer email from table or order
        return "<EMAIL>"; // Sample email
    }

    private String getCustomerMobile(int tableNumber) {
        // Get customer mobile from table or order
        return "9034142334"; // Sample mobile from UI
    }

    private void sendEBillEmail(Order order, String email) {
        try {
            System.out.println("Sending eBill to email: " + email);
            // In real implementation, send email with bill details
        } catch (Exception e) {
            System.err.println("Error sending eBill email: " + e.getMessage());
        }
    }

    private void sendEBillSMS(Order order, String mobile) {
        try {
            System.out.println("Sending eBill SMS to: " + mobile);
            // In real implementation, send SMS with bill details
        } catch (Exception e) {
            System.err.println("Error sending eBill SMS: " + e.getMessage());
        }
    }

    private String generateKOTNumber() {
        // Generate unique KOT number
        return "KOT" + System.currentTimeMillis();
    }

    private String generateBillNumber() {
        // Generate unique bill number
        return "BILL" + System.currentTimeMillis();
    }

    private void printKOTToKitchen(Order order) {
        try {
            System.out.println("Printing KOT to kitchen for order: " + order.getTableNumber());
            // In real implementation, print KOT to kitchen printer
        } catch (Exception e) {
            System.err.println("Error printing KOT: " + e.getMessage());
        }
    }

    private void calculateFinalBill(Order order) {
        try {
            // Calculate taxes, service charges, discounts
            double subtotal = order.getTotalAmount();
            double gst = subtotal * 0.18; // 18% GST
            double serviceCharge = subtotal * 0.10; // 10% service charge
            double total = subtotal + gst + serviceCharge;

            // Set calculated amounts (would need to add these fields to Order model)
            System.out.println("Bill calculation - Subtotal: " + subtotal +
                             ", GST: " + gst + ", Service: " + serviceCharge +
                             ", Total: " + total);
        } catch (Exception e) {
            System.err.println("Error calculating bill: " + e.getMessage());
        }
    }

    private void showBillDialog(Order order) {
        try {
            // Show detailed bill dialog
            Alert billDialog = new Alert(Alert.AlertType.INFORMATION);
            billDialog.setTitle("Bill Generated");
            billDialog.setHeaderText("Bill #" + generateBillNumber());

            String billDetails = "Table: " + order.getTableNumber() + "\n" +
                               "Items: " + order.getItems().size() + "\n" +
                               "Subtotal: ₹" + String.format("%.2f", order.getTotalAmount()) + "\n" +
                               "GST (18%): ₹" + String.format("%.2f", order.getTotalAmount() * 0.18) + "\n" +
                               "Service (10%): ₹" + String.format("%.2f", order.getTotalAmount() * 0.10) + "\n" +
                               "Total: ₹" + String.format("%.2f", order.getTotalAmount() * 1.28);

            billDialog.setContentText(billDetails);
            billDialog.showAndWait();
        } catch (Exception e) {
            System.err.println("Error showing bill dialog: " + e.getMessage());
        }
    }

    // Payment processing helper methods
    private void processPayment(String paymentMethod, double amount, double receivedAmount) {
        try {
            System.out.println("Processing payment: " + paymentMethod +
                             ", Amount: " + amount + ", Received: " + receivedAmount);

            // Create payment record
            // In real implementation, save payment details to database
            // PaymentRecord payment = new PaymentRecord();
            // payment.setMethod(paymentMethod);
            // payment.setAmount(amount);
            // payment.setReceivedAmount(receivedAmount);
            // payment.setTimestamp(LocalDateTime.now());
            // paymentDAO.save(payment);

            // Update order status to paid
            // Order order = getCurrentOrder(tableNumber);
            // order.setStatus("PAID");
            // order.setPaymentMethod(paymentMethod);
            // saveOrderToDatabase(order);

        } catch (Exception e) {
            System.err.println("Error processing payment: " + e.getMessage());
        }
    }

    private void processDuePaymentRecord(String customerInfo, double amount) {
        try {
            System.out.println("Recording due payment for customer: " + customerInfo +
                             ", Amount: " + amount);

            // Create due payment record
            // In real implementation, save to customer account
            // DuePayment duePayment = new DuePayment();
            // duePayment.setCustomerInfo(customerInfo);
            // duePayment.setAmount(amount);
            // duePayment.setDate(LocalDate.now());
            // duePaymentDAO.save(duePayment);

        } catch (Exception e) {
            System.err.println("Error recording due payment: " + e.getMessage());
        }
    }

    private void processPartialPayment(double paidAmount, double remainingAmount) {
        try {
            System.out.println("Processing partial payment - Paid: " + paidAmount +
                             ", Remaining: " + remainingAmount);

            // Create partial payment record
            // In real implementation, save partial payment and update order
            // PartialPayment partialPayment = new PartialPayment();
            // partialPayment.setPaidAmount(paidAmount);
            // partialPayment.setRemainingAmount(remainingAmount);
            // partialPayment.setTimestamp(LocalDateTime.now());
            // partialPaymentDAO.save(partialPayment);

        } catch (Exception e) {
            System.err.println("Error processing partial payment: " + e.getMessage());
        }
    }

    private HBox createOrderItemRow(String itemName, int quantity, double price) {
        HBox row = new HBox(10);
        row.setAlignment(Pos.CENTER_LEFT);
        row.setStyle("-fx-padding: 2;");

        // Red X button (like in your image)
        Button removeBtn = new Button("X");
        removeBtn.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 2 6; -fx-min-width: 20; -fx-pref-width: 20;");

        // Item name
        Label nameLabel = new Label(itemName);
        nameLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #333;");
        nameLabel.setPrefWidth(120);

        // Quantity controls
        Button minusBtn = new Button("-");
        minusBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 2 6; -fx-min-width: 20;");

        Label qtyLabel = new Label(String.valueOf(quantity));
        qtyLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #333; -fx-min-width: 20; -fx-alignment: center;");

        Button plusBtn = new Button("+");
        plusBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 2 6; -fx-min-width: 20;");

        // Price
        Label priceLabel = new Label(String.format("%.2f", price));
        priceLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #333; -fx-min-width: 40; -fx-alignment: center-right;");

        row.getChildren().addAll(removeBtn, nameLabel, minusBtn, qtyLabel, plusBtn, priceLabel);
        return row;
    }

    private VBox createOrderSide(int tableNumber, Order order) {
        VBox orderSide = new VBox(15);
        orderSide.setStyle("-fx-background-color: white; -fx-padding: 15;");

        // Top header with table info and buttons (exactly like in your image)
        HBox topHeaderBox = new HBox(15);
        topHeaderBox.setAlignment(Pos.CENTER_LEFT);
        topHeaderBox.setStyle("-fx-padding: 10; -fx-background-color: #f8f9fa; -fx-border-color: #dee2e6; -fx-border-width: 0 0 1 0;");

        // Left side: Table info
        VBox tableInfoBox = new VBox(5);
        Label tableLabel = new Label("Table " + tableNumber);
        tableLabel.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #333;");

        Label dateTimeLabel = new Label("Date and Time: " + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
        dateTimeLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #666;");

        tableInfoBox.getChildren().addAll(tableLabel, dateTimeLabel);

        // Center: Order type buttons (exactly like in your image)
        HBox orderTypeBox = new HBox(8);
        orderTypeBox.setAlignment(Pos.CENTER);

        Button dineInBtn = new Button("Dine In");
        Button deliveryBtn = new Button("Delivery");
        Button pickUpBtn = new Button("Pick Up");

        dineInBtn.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-padding: 6 12; -fx-font-size: 11px;");
        deliveryBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-padding: 6 12; -fx-font-size: 11px;");
        pickUpBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-padding: 6 12; -fx-font-size: 11px;");

        orderTypeBox.getChildren().addAll(dineInBtn, deliveryBtn, pickUpBtn);

        topHeaderBox.getChildren().addAll(tableInfoBox, new Region(), orderTypeBox);
        HBox.setHgrow(tableInfoBox, Priority.NEVER);

        // Customer info section (like in your image)
        VBox customerBox = new VBox(8);
        customerBox.setStyle("-fx-background-color: #f8f9fa; -fx-padding: 10; -fx-border-color: #dee2e6; -fx-border-width: 1;");

        HBox mobileBox = new HBox(10);
        Label mobileLabel = new Label("Mobile:");
        TextField mobileField = new TextField("9034142334");
        mobileField.setPrefWidth(150);
        mobileBox.getChildren().addAll(mobileLabel, mobileField);

        HBox nameBox = new HBox(10);
        Label nameLabel = new Label("Name:");
        TextField nameField = new TextField("Bharat Malik");
        nameField.setPrefWidth(150);
        nameBox.getChildren().addAll(nameLabel, nameField);

        customerBox.getChildren().addAll(mobileBox, nameBox);

        // Order items section (like in your image)
        Label itemsHeader = new Label("ITEMS");
        itemsHeader.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #333;");

        VBox orderItemsContainer = createOrderItemsContainer(order);
        orderItemsContainer.setPrefHeight(150);

        // Payment options section
        VBox paymentSection = createPaymentOptionsSection();

        // Total section (like in your image)
        VBox totalBox = new VBox(5);
        totalBox.setStyle("-fx-background-color: #f8f9fa; -fx-padding: 10; -fx-border-color: #dee2e6; -fx-border-width: 1;");

        Label totalLabel = new Label("Total: ₹" + String.format("%.2f", order.calculateTotal()));
        totalLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #dc3545;");

        totalBox.getChildren().add(totalLabel);

        orderSide.getChildren().addAll(topHeaderBox, customerBox, itemsHeader, orderItemsContainer, paymentSection, totalBox);
        return orderSide;
    }

    private VBox createOrderItemsContainer(Order order) {
        VBox container = new VBox(5);
        container.setStyle("-fx-background-color: white; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-padding: 10;");

        // Header row
        HBox headerRow = new HBox(10);
        headerRow.setStyle("-fx-background-color: #f8f9fa; -fx-padding: 8; -fx-border-color: #dee2e6; -fx-border-width: 0 0 1 0;");

        Label itemHeader = new Label("ITEMS");
        itemHeader.setPrefWidth(150);
        itemHeader.setStyle("-fx-font-weight: bold; -fx-font-size: 12px;");

        Label checkHeader = new Label("CHECK");
        checkHeader.setPrefWidth(60);
        checkHeader.setStyle("-fx-font-weight: bold; -fx-font-size: 12px;");

        Label qtyHeader = new Label("QTY.");
        qtyHeader.setPrefWidth(120);
        qtyHeader.setStyle("-fx-font-weight: bold; -fx-font-size: 12px;");

        Label deleteHeader = new Label("DELETE");
        deleteHeader.setPrefWidth(60);
        deleteHeader.setStyle("-fx-font-weight: bold; -fx-font-size: 12px;");

        Label priceHeader = new Label("PRICE");
        priceHeader.setPrefWidth(80);
        priceHeader.setStyle("-fx-font-weight: bold; -fx-font-size: 12px;");

        headerRow.getChildren().addAll(itemHeader, checkHeader, qtyHeader, deleteHeader, priceHeader);
        container.getChildren().add(headerRow);

        // Add item rows
        for (OrderItem item : order.getItems()) {
            HBox itemRow = createOrderItemRowWithControls(item, container, order);
            container.getChildren().add(itemRow);
        }

        return container;
    }

    private HBox createOrderItemRowWithControls(OrderItem orderItem, VBox container, Order order) {
        HBox row = new HBox(10);
        row.setAlignment(Pos.CENTER_LEFT);
        row.setStyle("-fx-padding: 5; -fx-border-color: #dee2e6; -fx-border-width: 0 0 1 0;");

        // Item name
        Label itemName = new Label(orderItem.getMenuItem().getName());
        itemName.setPrefWidth(150);
        itemName.setStyle("-fx-font-size: 12px;");

        // Check mark
        Label checkMark = new Label("✓");
        checkMark.setPrefWidth(60);
        checkMark.setStyle("-fx-font-size: 12px; -fx-text-fill: #28a745; -fx-font-weight: bold;");

        // Quantity controls
        HBox qtyBox = new HBox(5);
        qtyBox.setPrefWidth(120);
        qtyBox.setAlignment(Pos.CENTER_LEFT);

        Button minusBtn = new Button("-");
        minusBtn.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-font-weight: bold; -fx-min-width: 25; -fx-min-height: 25; -fx-font-size: 12px;");

        Label qtyLabel = new Label(String.valueOf(orderItem.getQuantity()));
        qtyLabel.setStyle("-fx-font-weight: bold; -fx-min-width: 30; -fx-alignment: center; -fx-font-size: 12px;");

        Button plusBtn = new Button("+");
        plusBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-weight: bold; -fx-min-width: 25; -fx-min-height: 25; -fx-font-size: 12px;");

        // Button actions
        minusBtn.setOnAction(e -> {
            if (orderItem.getQuantity() > 1) {
                orderItem.setQuantity(orderItem.getQuantity() - 1);
                qtyLabel.setText(String.valueOf(orderItem.getQuantity()));
                updateOrderTotal();
            }
        });

        plusBtn.setOnAction(e -> {
            orderItem.setQuantity(orderItem.getQuantity() + 1);
            qtyLabel.setText(String.valueOf(orderItem.getQuantity()));
            updateOrderTotal();
        });

        qtyBox.getChildren().addAll(minusBtn, qtyLabel, plusBtn);

        // Delete button
        Button deleteBtn = new Button("✕");
        deleteBtn.setPrefWidth(60);
        deleteBtn.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-font-weight: bold; -fx-min-width: 30; -fx-min-height: 30; -fx-font-size: 12px;");
        deleteBtn.setOnAction(e -> {
            container.getChildren().remove(row);
            order.getItems().remove(orderItem);
            updateOrderTotal();
        });

        // Price
        Label priceLabel = new Label(String.format("%.2f", orderItem.getTotalPrice()));
        priceLabel.setPrefWidth(80);
        priceLabel.setStyle("-fx-font-size: 12px; -fx-font-weight: bold;");

        row.getChildren().addAll(itemName, checkMark, qtyBox, deleteBtn, priceLabel);
        return row;
    }

    private void updateOrderTotal() {
        // This method would update the total display in the UI
        // For now, just print to console
        System.out.println("Order total updated");
    }

    private VBox createPaymentOptionsSection() {
        VBox paymentSection = new VBox(8);
        paymentSection.setStyle("-fx-background-color: white; -fx-padding: 12; -fx-border-color: #dee2e6; -fx-border-width: 1;");

        // Payment options header
        Label paymentHeader = new Label("PAYMENT OPTIONS");
        paymentHeader.setStyle("-fx-font-size: 12px; -fx-font-weight: bold; -fx-text-fill: #333;");

        // Payment method buttons
        HBox paymentMethodsRow = new HBox(8);
        paymentMethodsRow.setAlignment(Pos.CENTER_LEFT);

        Button cashBtn = new Button("Cash");
        cashBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 6 12; -fx-font-weight: bold;");
        cashBtn.setOnAction(e -> processCashPayment());

        Button cardBtn = new Button("Card");
        cardBtn.setStyle("-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 6 12; -fx-font-weight: bold;");
        cardBtn.setOnAction(e -> processCardPayment());

        Button upiBtn = new Button("UPI");
        upiBtn.setStyle("-fx-background-color: #6f42c1; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 6 12; -fx-font-weight: bold;");
        upiBtn.setOnAction(e -> processUPIPayment());

        Button dueBtn = new Button("Due");
        dueBtn.setStyle("-fx-background-color: #ffc107; -fx-text-fill: black; -fx-font-size: 10px; -fx-padding: 6 12; -fx-font-weight: bold;");
        dueBtn.setOnAction(e -> processDuePayment());

        Button partBtn = new Button("Part Payment");
        partBtn.setStyle("-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 6 12; -fx-font-weight: bold;");
        partBtn.setOnAction(e -> processPartPayment());

        Button holdBtn = new Button("Hold");
        holdBtn.setStyle("-fx-background-color: #fd7e14; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 6 12; -fx-font-weight: bold;");
        holdBtn.setOnAction(e -> processHoldOrder());

        paymentMethodsRow.getChildren().addAll(cashBtn, cardBtn, upiBtn, dueBtn, partBtn, holdBtn);

        // Additional payment options
        HBox additionalOptionsRow = new HBox(12);
        additionalOptionsRow.setAlignment(Pos.CENTER_LEFT);

        CheckBox loyaltyCheckBox = new CheckBox("Loyalty Customer");
        loyaltyCheckBox.setStyle("-fx-font-size: 10px;");
        loyaltyCheckBox.setOnAction(e -> System.out.println("Loyalty customer: " + loyaltyCheckBox.isSelected()));

        Button smsBtn = new Button("Send SMS Feedback");
        smsBtn.setStyle("-fx-background-color: #fd7e14; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 6 12; -fx-font-weight: bold;");
        smsBtn.setOnAction(e -> sendSMSFeedback());

        additionalOptionsRow.getChildren().addAll(loyaltyCheckBox, smsBtn);

        // Offer features row
        HBox offerFeaturesRow = new HBox(8);
        offerFeaturesRow.setAlignment(Pos.CENTER_LEFT);

        Button bogoBtn = new Button("BOGO Offer");
        bogoBtn.setStyle("-fx-background-color: #e83e8c; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 6 12; -fx-font-weight: bold;");
        bogoBtn.setOnAction(e -> applyBOGOOffer());

        Button splitBillBtn = new Button("Split Bill");
        splitBillBtn.setStyle("-fx-background-color: #6610f2; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 6 12; -fx-font-weight: bold;");
        splitBillBtn.setOnAction(e -> splitBill());

        Button complimentaryBtn = new Button("Complimentary");
        complimentaryBtn.setStyle("-fx-background-color: #20c997; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 6 12; -fx-font-weight: bold;");
        complimentaryBtn.setOnAction(e -> markComplimentary());

        Button discountBtn = new Button("Apply Discount");
        discountBtn.setStyle("-fx-background-color: #fd7e14; -fx-text-fill: white; -fx-font-size: 10px; -fx-padding: 6 12; -fx-font-weight: bold;");
        discountBtn.setOnAction(e -> applyDiscountOffer());

        offerFeaturesRow.getChildren().addAll(bogoBtn, splitBillBtn, complimentaryBtn, discountBtn);

        paymentSection.getChildren().addAll(paymentHeader, paymentMethodsRow, additionalOptionsRow, offerFeaturesRow);
        return paymentSection;
    }

    private void showOrderItemsDialog(int tableNumber, Order order) {
        // Create a custom dialog to show order items
        Dialog<ButtonType> dialog = new Dialog<>();
        dialog.setTitle("Table " + tableNumber + " - Order Details");
        dialog.setHeaderText("KOT - Table " + tableNumber + " - Order #" + order.getId());

        // Create main content
        VBox mainContent = new VBox(15);
        mainContent.setPadding(new Insets(20));
        mainContent.setPrefWidth(600);
        mainContent.setPrefHeight(500);

        // Order header info
        HBox headerInfo = new HBox(20);
        headerInfo.setAlignment(Pos.CENTER_LEFT);

        Label dateTimeLabel = new Label("Date and Time: " + java.time.LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
        dateTimeLabel.setStyle("-fx-font-weight: bold;");

        Label statusLabel = new Label("Status: " + order.getStatus());
        statusLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #28a745;");

        headerInfo.getChildren().addAll(dateTimeLabel, statusLabel);

        // Create table for order items
        TableView<OrderItem> itemsTable = new TableView<>();
        itemsTable.setPrefHeight(300);

        // Create columns
        TableColumn<OrderItem, String> itemNameCol = new TableColumn<>("ITEMS");
        itemNameCol.setPrefWidth(200);
        itemNameCol.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleStringProperty(cellData.getValue().getMenuItem().getName()));

        TableColumn<OrderItem, String> checkItemsCol = new TableColumn<>("CHECK ITEMS");
        checkItemsCol.setPrefWidth(120);
        checkItemsCol.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleStringProperty("✓"));
        checkItemsCol.setStyle("-fx-alignment: CENTER;");

        TableColumn<OrderItem, Integer> qtyCol = new TableColumn<>("QTY.");
        qtyCol.setPrefWidth(80);
        qtyCol.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleObjectProperty<>(cellData.getValue().getQuantity()));
        qtyCol.setStyle("-fx-alignment: CENTER;");

        TableColumn<OrderItem, String> priceCol = new TableColumn<>("PRICE");
        priceCol.setPrefWidth(100);
        priceCol.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleStringProperty(String.format("%.2f", cellData.getValue().getTotalPrice())));
        priceCol.setStyle("-fx-alignment: CENTER-RIGHT;");

        itemsTable.getColumns().addAll(itemNameCol, checkItemsCol, qtyCol, priceCol);

        // Add order items to table
        ObservableList<OrderItem> orderItems = FXCollections.observableArrayList(order.getItems());
        itemsTable.setItems(orderItems);

        // Total section
        HBox totalSection = new HBox(10);
        totalSection.setAlignment(Pos.CENTER_RIGHT);
        totalSection.setPadding(new Insets(10, 0, 0, 0));

        Label totalLabel = new Label("Total: ");
        totalLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 16px;");

        Label totalAmount = new Label(String.format("₹%.2f", order.calculateTotal()));
        totalAmount.setStyle("-fx-font-weight: bold; -fx-font-size: 16px; -fx-text-fill: #dc3545;");

        totalSection.getChildren().addAll(totalLabel, totalAmount);

        // Add all components to main content
        mainContent.getChildren().addAll(headerInfo, itemsTable, totalSection);

        // Create action buttons
        ButtonType viewEditBtn = new ButtonType("View/Edit Order", ButtonBar.ButtonData.OK_DONE);
        ButtonType printKOTBtn = new ButtonType("Print KOT", ButtonBar.ButtonData.OTHER);
        ButtonType generateBillBtn = new ButtonType("Generate Bill", ButtonBar.ButtonData.OTHER);
        ButtonType closeBtn = new ButtonType("Close", ButtonBar.ButtonData.CANCEL_CLOSE);

        dialog.getDialogPane().setContent(mainContent);
        dialog.getDialogPane().getButtonTypes().addAll(viewEditBtn, printKOTBtn, generateBillBtn, closeBtn);

        // Handle button actions
        dialog.showAndWait().ifPresent(response -> {
            if (response == viewEditBtn) {
                System.out.println("BillingKOTController: Viewing/editing order for Table " + tableNumber);
                navigateToOrderEntry(tableNumber);
            } else if (response == printKOTBtn) {
                System.out.println("BillingKOTController: Printing KOT for Table " + tableNumber);
                showAlert("Print KOT", "KOT printed for Table " + tableNumber);
                refreshTables();
            } else if (response == generateBillBtn) {
                System.out.println("BillingKOTController: Generating bill for Table " + tableNumber);
                showAlert("Generate Bill", "Bill generated for Table " + tableNumber);
                refreshTables();
            }
        });
    }

    private void navigateToOrderEntry(int tableNumber) {
        try {
            System.out.println("BillingKOTController: Starting navigation to menu selection for table " + tableNumber);

            // Use MenuSelection.fxml instead of OrderEntry.fxml for better reliability
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/MenuSelection.fxml"));
            javafx.scene.Parent menuSelectionView = loader.load();
            System.out.println("BillingKOTController: MenuSelection FXML loaded successfully");

            // Get the controller and set the table data
            Object controller = loader.getController();
            if (controller instanceof com.restaurant.controller.MenuSelectionController) {
                System.out.println("BillingKOTController: MenuSelectionController obtained, setting table info");
                com.restaurant.controller.MenuSelectionController menuController =
                    (com.restaurant.controller.MenuSelectionController) controller;

                // Set the table information
                menuController.setTableInfo("Table " + tableNumber);
                System.out.println("BillingKOTController: Table info set to Table " + tableNumber);
            } else {
                System.err.println("BillingKOTController: Controller is not MenuSelectionController: " +
                    (controller != null ? controller.getClass().getName() : "null"));
            }

            // Get current stage and navigate using the simple approach
            javafx.stage.Stage stage = (javafx.stage.Stage) groundFloorGrid.getScene().getWindow();
            if (stage != null) {
                System.out.println("BillingKOTController: Setting new scene root for MenuSelection");
                stage.getScene().setRoot(menuSelectionView);
                stage.setTitle("Restaurant Management - Table " + tableNumber + " - Menu Selection");
                System.out.println("BillingKOTController: Navigation to MenuSelection completed successfully");
            } else {
                System.err.println("BillingKOTController: Could not get current stage");
                showAlert("Navigation Error", "Could not get current window to navigate");
            }

        } catch (Exception e) {
            System.err.println("BillingKOTController: Error navigating to menu selection: " + e.getMessage());
            e.printStackTrace();
            showAlert("Navigation Error", "Failed to open menu selection page: " + e.getMessage());
        }
    }

    private javafx.stage.Stage getCurrentStage() {
        try {
            // Try multiple methods to get the current stage
            if (groundFloorGrid != null && groundFloorGrid.getScene() != null) {
                javafx.stage.Stage stage = (javafx.stage.Stage) groundFloorGrid.getScene().getWindow();
                if (stage != null) {
                    System.out.println("BillingKOTController: Found stage via groundFloorGrid");
                    return stage;
                }
            }

            // Try using SceneEventHandlerManager's primary stage
            try {
                javafx.stage.Stage primaryStage = com.restaurant.util.SceneEventHandlerManager.getInstance().getPrimaryStage();
                if (primaryStage != null) {
                    System.out.println("BillingKOTController: Found stage via SceneEventHandlerManager");
                    return primaryStage;
                }
            } catch (Exception ex) {
                System.out.println("BillingKOTController: SceneEventHandlerManager method failed: " + ex.getMessage());
            }

            // Alternative method using any available node
            if (groundFloorGrid != null) {
                javafx.scene.Scene scene = groundFloorGrid.getScene();
                if (scene != null) {
                    javafx.stage.Stage stage = (javafx.stage.Stage) scene.getWindow();
                    if (stage != null) {
                        System.out.println("BillingKOTController: Found stage via scene");
                        return stage;
                    }
                }
            }

            System.err.println("BillingKOTController: Could not find stage through any method");
            return null;

        } catch (Exception e) {
            System.err.println("BillingKOTController: Error getting current stage: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    private void initializeTableCombos() {
        try {
            System.out.println("BillingKOTController: Initializing table combos");

            // Populate table combos for Move KOT functionality
            ObservableList<String> tableNumbers = FXCollections.observableArrayList();

            // Add table numbers (1-20 for example)
            for (int i = 1; i <= 20; i++) {
                tableNumbers.add("Table " + i);
            }

            // Set items for both combos
            if (fromTableCombo != null) {
                fromTableCombo.setItems(tableNumbers);
            }

            if (toTableCombo != null) {
                toTableCombo.setItems(tableNumbers);
            }

            System.out.println("BillingKOTController: Table combos initialized successfully");
        } catch (Exception e) {
            System.err.println("Error initializing table combos: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void setupToggleButtons() {
        try {
            System.out.println("BillingKOTController: Setting up toggle buttons");

            // Create toggle group for delivery/pickup
            ToggleGroup modeGroup = new ToggleGroup();

            if (deliveryToggle != null && pickUpToggle != null) {
                deliveryToggle.setToggleGroup(modeGroup);
                pickUpToggle.setToggleGroup(modeGroup);

                // Set default state
                deliveryToggle.setSelected(false);
                pickUpToggle.setSelected(false);
            }

            System.out.println("BillingKOTController: Toggle buttons setup complete");
        } catch (Exception e) {
            System.err.println("Error setting up toggle buttons: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void setupSpinners() {
        // Setup GST rate spinner
        gstRateSpinner.setValueFactory(new SpinnerValueFactory.DoubleSpinnerValueFactory(0.0, 50.0, 18.0, 0.5));
        gstRateSpinner.setEditable(true);
        
        // Setup service charge spinner
        serviceChargeRateSpinner.setValueFactory(new SpinnerValueFactory.DoubleSpinnerValueFactory(0.0, 25.0, 10.0, 0.5));
        serviceChargeRateSpinner.setEditable(true);
    }
    
    private void setupPreviewTable() {
        previewItemColumn.setCellValueFactory(new PropertyValueFactory<>("itemName"));
        previewQtyColumn.setCellValueFactory(new PropertyValueFactory<>("quantity"));
        previewPriceColumn.setCellValueFactory(new PropertyValueFactory<>("price"));
        previewTotalColumn.setCellValueFactory(new PropertyValueFactory<>("total"));

        previewItemsTable.setItems(previewItems);
    }

    private void setupOrderDetailsTable() {
        // Setup order items table columns
        itemNameColumn.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleStringProperty(cellData.getValue().getMenuItem().getName()));
        itemQuantityColumn.setCellValueFactory(new PropertyValueFactory<>("quantity"));
        itemPriceColumn.setCellValueFactory(new PropertyValueFactory<>("price"));
        itemTotalColumn.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleDoubleProperty(cellData.getValue().getTotalPrice()).asObject());

        // Format currency columns
        itemPriceColumn.setCellFactory(column -> new TableCell<OrderItem, Double>() {
            @Override
            protected void updateItem(Double price, boolean empty) {
                super.updateItem(price, empty);
                if (empty || price == null) {
                    setText(null);
                } else {
                    setText(String.format("₹%.2f", price));
                }
            }
        });

        itemTotalColumn.setCellFactory(column -> new TableCell<OrderItem, Double>() {
            @Override
            protected void updateItem(Double total, boolean empty) {
                super.updateItem(total, empty);
                if (empty || total == null) {
                    setText(null);
                } else {
                    setText(String.format("₹%.2f", total));
                }
            }
        });
    }

    private void setupPaymentMethods() {
        paymentMethodCombo.setItems(FXCollections.observableArrayList(
            "Cash", "Credit Card", "Debit Card", "Digital Wallet", "UPI"
        ));
        paymentMethodCombo.setValue("Cash");

        // Initialize discount field
        discountField.setText("0");
    }
    
    private void loadDefaultSettings() {
        enableGSTCheckbox.setSelected(true);
        gstNumberField.setText("27XXXXX1234X1ZX");
        enableServiceChargeCheckbox.setSelected(true);

        // Setup printer combo boxes
        setupPrinterComboBoxes();
    }

    private void setupPrinterComboBoxes() {
        // Setup invoice printer combo
        invoicePrinterCombo.setItems(FXCollections.observableArrayList(
            "Default Printer",
            "Thermal Printer 1",
            "Laser Printer"
        ));
        invoicePrinterCombo.setValue("Default Printer");

        // Setup KOT printer combo
        kotPrinterCombo.setItems(FXCollections.observableArrayList(
            "Kitchen Printer",
            "Thermal Printer 2",
            "Default Printer"
        ));
        kotPrinterCombo.setValue("Kitchen Printer");
    }
    
    private void loadSampleOrders() {
        sampleOrderCombo.setItems(FXCollections.observableArrayList(
            "Order #1025 - Table 5 (₹850.00)",
            "Order #1024 - Table 3 (₹650.00)",
            "Order #1023 - Takeaway (₹1,200.00)",
            "Order #1022 - Table 7 (₹750.00)"
        ));
        sampleOrderCombo.setValue("Order #1025 - Table 5 (₹850.00)");
    }
    
    private void setupListeners() {
        enableGSTCheckbox.selectedProperty().addListener((obs, oldVal, newVal) -> refreshPreview());
        gstRateSpinner.valueProperty().addListener((obs, oldVal, newVal) -> refreshPreview());
        enableServiceChargeCheckbox.selectedProperty().addListener((obs, oldVal, newVal) -> refreshPreview());
        serviceChargeRateSpinner.valueProperty().addListener((obs, oldVal, newVal) -> refreshPreview());
        sampleOrderCombo.valueProperty().addListener((obs, oldVal, newVal) -> refreshPreview());
    }
    
    @FXML
    private void openSettings() {
        showAlert("Info", "Opening advanced billing settings...");
    }
    
    @FXML
    private void testInvoicePrinter() {
        String printer = invoicePrinterCombo.getValue();
        showAlert("Printer Test", "Sending test print to: " + printer + "\n\nTest invoice printed successfully!");
    }
    
    @FXML
    private void testKOTPrinter() {
        String printer = kotPrinterCombo.getValue();
        showAlert("Printer Test", "Sending test KOT to: " + printer + "\n\nTest KOT printed successfully!");
    }
    
    @FXML
    private void saveConfiguration() {
        // Save configuration to database or properties file
        showAlert("Success", "Configuration saved successfully!\n\n" +
            "GST: " + (enableGSTCheckbox.isSelected() ? "Enabled (" + gstRateSpinner.getValue() + "%)" : "Disabled") + "\n" +
            "Service Charge: " + (enableServiceChargeCheckbox.isSelected() ? "Enabled (" + serviceChargeRateSpinner.getValue() + "%)" : "Disabled") + "\n" +
            "Invoice Printer: " + invoicePrinterCombo.getValue() + "\n" +
            "KOT Printer: " + kotPrinterCombo.getValue());
    }
    
    @FXML
    private void refreshPreview() {
        loadSampleItems();
        updatePreviewCalculations();
        updatePreviewVisibility();
    }
    
    private void loadSampleItems() {
        previewItems.clear();
        previewItems.addAll(
            new PreviewItemRecord("Chicken Biryani", 2, "₹220.00", "₹440.00"),
            new PreviewItemRecord("Paneer Tikka", 1, "₹200.00", "₹200.00"),
            new PreviewItemRecord("Garlic Naan", 3, "₹70.00", "₹210.00"),
            new PreviewItemRecord("Green Salad", 1, "₹90.00", "₹90.00")
        );
    }
    
    private void updatePreviewCalculations() {
        double subtotal = 975.00; // Sample subtotal
        
        // Update GST
        if (enableGSTCheckbox.isSelected()) {
            double gstRate = gstRateSpinner.getValue();
            double gstAmount = subtotal * (gstRate / 100);
            previewGSTLabel.setText("GST (" + gstRate + "%):");
            previewGSTAmount.setText(String.format("₹%.2f", gstAmount));
            gstNumberLabel.setText("GST No: " + gstNumberField.getText());
        }
        
        // Update Service Charge
        if (enableServiceChargeCheckbox.isSelected()) {
            double serviceRate = serviceChargeRateSpinner.getValue();
            double serviceAmount = subtotal * (serviceRate / 100);
            previewServiceChargeLabel.setText("Service Charge (" + serviceRate + "%):");
            previewServiceChargeAmount.setText(String.format("₹%.2f", serviceAmount));
        }
        
        // Calculate grand total
        double grandTotal = subtotal;
        if (enableGSTCheckbox.isSelected()) {
            grandTotal += subtotal * (gstRateSpinner.getValue() / 100);
        }
        if (enableServiceChargeCheckbox.isSelected()) {
            grandTotal += subtotal * (serviceChargeRateSpinner.getValue() / 100);
        }
        
        previewSubtotal.setText(String.format("₹%.2f", subtotal));
        previewGrandTotal.setText(String.format("₹%.2f", grandTotal));
    }
    
    private void updatePreviewVisibility() {
        previewGSTRow.setVisible(enableGSTCheckbox.isSelected());
        previewGSTRow.setManaged(enableGSTCheckbox.isSelected());
        
        previewServiceChargeRow.setVisible(enableServiceChargeCheckbox.isSelected());
        previewServiceChargeRow.setManaged(enableServiceChargeCheckbox.isSelected());
        
        gstNumberLabel.setVisible(enableGSTCheckbox.isSelected());
        gstNumberLabel.setManaged(enableGSTCheckbox.isSelected());
    }
    
    @FXML
    private void printSampleInvoice() {
        String printer = invoicePrinterCombo.getValue();
        showAlert("Print Invoice", "Printing sample invoice to: " + printer + "\n\nSample invoice printed successfully!");
    }
    
    @FXML
    private void printSampleKOT() {
        String printer = kotPrinterCombo.getValue();
        showAlert("Print KOT", "Printing sample KOT to: " + printer + "\n\nSample KOT printed successfully!");
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    // Table View Methods
    private void setupTables() {
        createTableButtons();
    }

    private void createTableButtons() {
        // Clear existing buttons
        groundFloorGrid.getChildren().clear();
        partyHallGrid.getChildren().clear();

        // Get real table status data
        List<TableStatus> tableStatuses = OrderDAO.getAllTableStatuses();

        // Ground Floor Tables (1-20) - Now includes all tables on ground floor only
        int col = 0, row = 0;
        for (int i = 1; i <= 20; i++) {
            TableStatus tableStatus = getTableStatusById(tableStatuses, i);
            Button tableBtn = createTableButtonWithStatus(tableStatus);
            groundFloorGrid.add(tableBtn, col, row);
            col++;
            if (col >= 5) { // 5 tables per row
                col = 0;
                row++;
            }
        }

        // Party Hall Tables (Hall 1, Hall 2)
        Button hall1Btn = createTableButton(0, "BLANK");
        hall1Btn.setText("Hall 1");
        hall1Btn.setPrefWidth(120);
        partyHallGrid.add(hall1Btn, 0, 0);

        Button hall2Btn = createTableButton(0, "BLANK");
        hall2Btn.setText("Hall 2");
        hall2Btn.setPrefWidth(120);
        partyHallGrid.add(hall2Btn, 1, 0);
    }

    private TableStatus getTableStatusById(List<TableStatus> tableStatuses, int tableNumber) {
        return tableStatuses.stream()
                .filter(ts -> ts.getTableNumber() == tableNumber)
                .findFirst()
                .orElse(new TableStatus(tableNumber));
    }

    private Button createTableButton(int tableNumber, String status) {
        Button btn = new Button(tableNumber > 0 ? String.valueOf(tableNumber) : "Hall");
        btn.setPrefWidth(80);
        btn.setPrefHeight(60);
        btn.setFont(Font.font("System", FontWeight.BOLD, 14));

        // Set style based on status
        updateTableButtonStyle(btn, status);

        // Add click handler
        btn.setOnAction(e -> handleTableClick(tableNumber, btn));

        return btn;
    }

    private Button createTableButtonWithStatus(TableStatus tableStatus) {
        Button btn = new Button();
        btn.setPrefWidth(90);  // Increased width for better visibility
        btn.setPrefHeight(70); // Increased height for better visibility
        btn.setFont(Font.font("System", FontWeight.BOLD, 9));

        int tableNumber = tableStatus.getTableNumber();

        // Check OrderManager for active orders first
        boolean hasOrderManagerOrder = orderManager.hasActiveOrder(tableNumber);
        double orderTotal = orderManager.getTableOrderTotal(tableNumber);
        String orderStatus = orderManager.getTableStatus(tableNumber);

        // Create button content based on order status
        if (hasOrderManagerOrder || tableStatus.hasActiveOrder()) {
            // Show timing, table number with eye icon, and amount for active orders
            String amount = hasOrderManagerOrder ?
                String.format("₹%.0f", orderTotal) :
                tableStatus.getFormattedAmount();

            String content = tableStatus.getElapsedTime() + "\n" +
                           tableNumber + " 👀\n" +
                           amount;
            btn.setText(content);
            btn.setFont(Font.font("System", FontWeight.BOLD, 10));

            // Update table status based on OrderManager status
            if (hasOrderManagerOrder) {
                switch (orderStatus) {
                    case "OCCUPIED":
                        btn.getStyleClass().add("running-table");
                        break;
                    case "RUNNING_KOT":
                        btn.getStyleClass().add("running-kot-table");
                        break;
                    case "PRINTED":
                        btn.getStyleClass().add("printed-table");
                        break;
                    default:
                        btn.getStyleClass().add("running-table");
                }
            } else {
                btn.getStyleClass().add(tableStatus.getStyleClass());
            }
        } else {
            // Just show table number for blank tables
            btn.setText(String.valueOf(tableNumber));
            btn.setFont(Font.font("System", FontWeight.BOLD, 16));
            btn.getStyleClass().add("blank-table");
        }

        // Remove all existing style classes first
        btn.getStyleClass().removeAll("blank-table", "running-table", "printed-table", "paid-table", "running-kot-table");

        // Add click handler - if has active order, go directly to table view
        if (hasOrderManagerOrder || tableStatus.hasActiveOrder()) {
            btn.setOnAction(e -> handleActiveTableClick(tableNumber, tableStatus.getOrderId()));
        } else {
            btn.setOnAction(e -> handleTableClick(tableNumber, btn));
        }

        return btn;
    }

    private void updateTableButtonStyle(Button btn, String status) {
        btn.getStyleClass().removeAll("blank-table", "running-table", "printed-table", "paid-table", "running-kot-table");

        switch (status.toUpperCase()) {
            case "BLANK":
                btn.getStyleClass().add("blank-table");
                break;
            case "RUNNING":
                btn.getStyleClass().add("running-table");
                break;
            case "PRINTED":
                btn.getStyleClass().add("printed-table");
                break;
            case "PAID":
                btn.getStyleClass().add("paid-table");
                break;
            case "RUNNING_KOT":
                btn.getStyleClass().add("running-kot-table");
                break;
        }
    }

    private void handleTableClick(int tableNumber, Button tableBtn) {
        // Load active order for this table
        Order activeOrder = OrderDAO.getActiveOrderByTable(tableNumber);

        if (activeOrder != null) {
            // Show order details and billing interface
            loadOrderForBilling(activeOrder);
        } else {
            showAlert("No Active Order", "Table " + tableNumber + " has no active order to bill.");
        }
    }

    private void loadOrderForBilling(Order order) {
        selectedOrder = order;

        // Update order information
        orderDetailsTitle.setText("Order Details - Table " + order.getTableNumber());
        orderIdLabel.setText(String.valueOf(order.getId()));
        tableNumberLabel.setText(order.isTakeaway() ? "Takeaway" : "Table " + order.getTableNumber());
        orderTimeLabel.setText(order.getTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderStatusLabel.setText(order.getStatus());

        // Load order items into table
        orderItemsTable.setItems(FXCollections.observableArrayList(order.getItems()));

        // Update order summary
        updateOrderSummary();

        // Show the order details section
        orderDetailsSection.setVisible(true);
        orderDetailsSection.setManaged(true);

        // Enable/disable buttons based on order status
        boolean isPaid = "PAID".equals(order.getStatus());
        generateBillBtn.setDisable(false);
        markPaidBtn.setDisable(isPaid);

        System.out.println("Loaded order " + order.getId() + " for table " + order.getTableNumber() + " with " + order.getItems().size() + " items");
    }

    private void updateOrderSummary() {
        if (selectedOrder == null) return;

        double subtotal = selectedOrder.calculateSubtotal();
        double gst = selectedOrder.calculateGST();
        double serviceCharge = selectedOrder.calculateServiceCharge();

        // Apply discount if any
        double discount = 0;
        try {
            String discountText = discountField.getText().trim();
            if (!discountText.isEmpty()) {
                discount = Double.parseDouble(discountText);
                if (discount > 100) discount = 100; // Max 100% discount
                if (discount < 0) discount = 0; // No negative discount
            }
        } catch (NumberFormatException e) {
            discount = 0;
        }

        double discountAmount = subtotal * (discount / 100);
        double discountedSubtotal = subtotal - discountAmount;
        double finalGst = discountedSubtotal * 0.18;
        double finalServiceCharge = discountedSubtotal * 0.10;
        double grandTotal = discountedSubtotal + finalGst + finalServiceCharge;

        subtotalLabel.setText(String.format("₹%.2f", subtotal));
        gstLabel.setText(String.format("₹%.2f", finalGst));
        serviceChargeLabel.setText(String.format("₹%.2f", finalServiceCharge));
        grandTotalLabel.setText(String.format("₹%.2f", grandTotal));
    }

    @FXML
    private void closeOrderDetails() {
        try {
            System.out.println("BillingKOTController: Closing order details");

            Platform.runLater(() -> {
                try {
                    // Hide the order details section
                    if (orderDetailsSection != null) {
                        orderDetailsSection.setVisible(false);
                        orderDetailsSection.setManaged(false);
                    }
                    selectedOrder = null;

                    // Clear all fields safely
                    if (orderDetailsTitle != null) orderDetailsTitle.setText("Order Details");
                    if (orderIdLabel != null) orderIdLabel.setText("-");
                    if (tableNumberLabel != null) tableNumberLabel.setText("-");
                    if (orderTimeLabel != null) orderTimeLabel.setText("-");
                    if (orderStatusLabel != null) orderStatusLabel.setText("-");
                    if (orderItemsTable != null) orderItemsTable.setItems(FXCollections.observableArrayList());
                    if (subtotalLabel != null) subtotalLabel.setText("₹0.00");
                    if (gstLabel != null) gstLabel.setText("₹0.00");
                    if (serviceChargeLabel != null) serviceChargeLabel.setText("₹0.00");
                    if (grandTotalLabel != null) grandTotalLabel.setText("₹0.00");
                    if (discountField != null) discountField.setText("0");

                    System.out.println("BillingKOTController: Order details closed successfully");
                } catch (Exception e) {
                    System.err.println("Error closing order details: " + e.getMessage());
                    e.printStackTrace();
                }
            });
        } catch (Exception e) {
            System.err.println("Error in closeOrderDetails: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @FXML
    private void applyDiscount() {
        updateOrderSummary();
    }

    @FXML
    private void generateBill() {
        if (selectedOrder == null) {
            showAlert("No Order Selected", "Please select an order to generate bill.");
            return;
        }

        try {
            // Mark bill as generated in OrderManager
            orderManager.markBillGenerated(selectedOrder.getTableNumber());

            // Generate and print bill
            PrintService.generateBill(selectedOrder);
            showAlert("Success", "Bill generated successfully!");

            // Refresh tables to show updated status
            refreshTables();
        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Failed to generate bill: " + e.getMessage());
        }
    }

    @FXML
    private void markPaid() {
        if (selectedOrder == null) {
            showAlert("No Order Selected", "Please select an order to mark as paid.");
            return;
        }

        try {
            // Update order status to paid
            if (OrderDAO.updateOrderStatus(selectedOrder.getId(), "PAID")) {
                selectedOrder.setStatus("PAID");

                // Mark as paid in OrderManager and clear the order
                orderManager.markOrderPaid(selectedOrder.getTableNumber());

                // Close order details and refresh tables
                closeOrderDetails();
                refreshTables();

                showAlert("Success", "Order marked as paid and table is now available!");
            } else {
                showAlert("Error", "Failed to update order status.");
            }
        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Failed to mark order as paid: " + e.getMessage());
        }
    }

    @FXML
    private void markTableAvailable() {
        if (selectedOrder == null) {
            showAlert("No Order Selected", "Please select an order to mark as paid.");
            return;
        }

        Alert confirmation = new Alert(Alert.AlertType.CONFIRMATION);
        confirmation.setTitle("Mark as Paid");
        confirmation.setHeaderText("Mark Order as Paid and Table Available");
        confirmation.setContentText("Are you sure you want to mark Order #" + selectedOrder.getId() +
                                   " as paid and table " + selectedOrder.getTableNumber() + " as available?");

        if (confirmation.showAndWait().orElse(ButtonType.CANCEL) == ButtonType.OK) {
            if (OrderDAO.updateOrderStatus(selectedOrder.getId(), "PAID")) {
                selectedOrder.setStatus("PAID");

                // Close order details and refresh tables
                closeOrderDetails();
                refreshTables();

                showAlert("Success", "Order marked as paid and table is now available!");
            } else {
                showAlert("Error", "Failed to update order status.");
            }
        }
    }

    @FXML
    private void printKOT() {
        if (selectedOrder == null) {
            showAlert("No Order Selected", "Please select an order to print KOT.");
            return;
        }

        try {
            // Print KOT
            PrintService.printKOT(selectedOrder);
            showAlert("Success", "KOT printed successfully!");
        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Failed to print KOT: " + e.getMessage());
        }
    }

    private void handleActiveTableClick(int tableNumber, int orderId) {
        // Handle click on active table with eye button - go directly to table view
        System.out.println("Navigating to active table: " + tableNumber + " with order ID: " + orderId);

        try {
            // Load the order details for this table
            Order order = OrderDAO.getOrderById(orderId);
            if (order != null) {
                // Navigate to order entry/table view with this specific order
                navigateToTableView(tableNumber, order);
            } else {
                showAlert("Order Not Found", "Could not find order details for table " + tableNumber);
            }
        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Failed to load order details: " + e.getMessage());
        }
    }

    private void navigateToTableView(int tableNumber, Order order) {
        try {
            System.out.println("DEBUG: Starting navigation to table " + tableNumber + " order entry page");

            // Load the OrderEntry view (menu selection page) for the specific table
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/OrderEntry.fxml"));
            javafx.scene.Parent root = loader.load();

            System.out.println("DEBUG: OrderEntry FXML loaded successfully");

            // Get the controller and set the table data
            Object controller = loader.getController();
            if (controller instanceof com.restaurant.controller.OrderEntryController) {
                com.restaurant.controller.OrderEntryController orderController =
                    (com.restaurant.controller.OrderEntryController) controller;

                System.out.println("DEBUG: OrderEntryController obtained, setting table number: " + tableNumber);

                // Set the table number to load existing order for this table
                orderController.setTableNumber(String.valueOf(tableNumber));

                System.out.println("DEBUG: Table number set, OrderEntry should now load existing order");

                // The OrderEntry page will automatically:
                // 1. Show the menu categories and items
                // 2. Load existing order items for this table
                // 3. Allow adding new items to the order
                // 4. Show order total and action buttons (Save Order, Print KOT, Generate Bill)
            } else {
                System.out.println("DEBUG: ERROR - Controller is not OrderEntryController: " +
                                 (controller != null ? controller.getClass().getName() : "null"));
            }

            // Get current stage and set new scene to the Order Entry/Menu Selection page
            javafx.stage.Stage stage = (javafx.stage.Stage) ((javafx.scene.Node)
                groundFloorGrid.getParent().getParent().getParent().getParent()).getScene().getWindow();
            stage.setScene(new javafx.scene.Scene(root));
            stage.setTitle("Restaurant Management - Table " + tableNumber + " - Order Entry");

            System.out.println("DEBUG: Navigation completed successfully to Order Entry page");

        } catch (Exception e) {
            System.out.println("DEBUG: ERROR during navigation: " + e.getMessage());
            e.printStackTrace();
            showAlert("Navigation Error", "Failed to open order entry page: " + e.getMessage());
        }
    }

    @FXML
    private void refreshTables() {
        try {
            System.out.println("BillingKOTController: Refreshing tables");

            // Use Platform.runLater to prevent UI blocking
            Platform.runLater(() -> {
                try {
                    // Add some test data if no orders exist
                    addTestDataIfNeeded();

                    // Refresh table statuses using the new method
                    initializeTables();

                    System.out.println("BillingKOTController: Tables refreshed successfully");
                } catch (Exception e) {
                    System.err.println("Error refreshing tables: " + e.getMessage());
                    e.printStackTrace();
                }
            });
        } catch (Exception e) {
            System.err.println("Error in refreshTables: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void addTestDataIfNeeded() {
        try {
            // Check if we have any orders today
            List<TableStatus> statuses = OrderDAO.getAllTableStatuses();
            boolean hasActiveOrders = statuses.stream().anyMatch(TableStatus::hasActiveOrder);

            if (!hasActiveOrders) {
                // Add some test orders
                addTestOrder(4, "WORKING", 185.00, 5); // 5 minutes ago
                addTestOrder(5, "CONFIRMED", 195.00, 3); // 3 minutes ago
                addTestOrder(6, "READY", 160.00, 2); // 2 minutes ago
                addTestOrder(13, "PENDING", 250.00, 1); // 1 minute ago
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void addTestOrder(int tableNumber, String status, double amount, int minutesAgo) {
        try {
            var conn = com.restaurant.model.DatabaseManager.getConnection();
            var ps = conn.prepareStatement(
                "INSERT INTO orders (table_number, is_takeaway, status, total_amount, timestamp) VALUES (?, ?, ?, ?, datetime('now', '-" + minutesAgo + " minutes'))");

            ps.setInt(1, tableNumber);
            ps.setBoolean(2, false);
            ps.setString(3, status);
            ps.setDouble(4, amount);

            ps.executeUpdate();
            conn.close();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @FXML
    private void toggleDeliveryMode() {
        boolean isDeliveryMode = deliveryToggle.isSelected();
        if (isDeliveryMode) {
            pickUpToggle.setSelected(false);
            System.out.println("BillingKOTController: Delivery mode enabled");
            showDeliveryOrdersDialog();
        } else {
            System.out.println("BillingKOTController: Delivery mode disabled");
            // Reset toggle state
            deliveryToggle.setSelected(false);
        }
    }

    @FXML
    private void togglePickUpMode() {
        boolean isPickUpMode = pickUpToggle.isSelected();
        if (isPickUpMode) {
            deliveryToggle.setSelected(false);
            System.out.println("BillingKOTController: Pick Up mode enabled");
            showPickUpOrdersDialog();
        } else {
            System.out.println("BillingKOTController: Pick Up mode disabled");
            // Reset toggle state
            pickUpToggle.setSelected(false);
        }
    }

    /**
     * Show delivery orders management dialog
     */
    private void showDeliveryOrdersDialog() {
        try {
            System.out.println("BillingKOTController: Opening delivery orders dialog");

            // Create dialog
            Dialog<ButtonType> dialog = new Dialog<>();
            dialog.setTitle("🚚 Delivery Mode");
            dialog.setHeaderText("Delivery Orders Management");

            // Create content
            VBox content = createDeliveryOrdersContent();

            // Set dialog content
            dialog.getDialogPane().setContent(content);
            dialog.getDialogPane().setPrefSize(1000, 700);

            // Add buttons
            ButtonType okButton = new ButtonType("OK", ButtonBar.ButtonData.OK_DONE);
            ButtonType newOrderButton = new ButtonType("New Delivery Order", ButtonBar.ButtonData.OTHER);
            dialog.getDialogPane().getButtonTypes().addAll(newOrderButton, okButton);

            // Style the dialog
            dialog.getDialogPane().getStylesheets().add(getClass().getResource("/css/application.css").toExternalForm());
            dialog.getDialogPane().getStyleClass().add("delivery-dialog");

            // Add keyboard shortcut handling to the dialog
            setupDeliveryDialogKeyboardShortcuts(dialog);

            // Handle button actions
            dialog.setResultConverter(buttonType -> {
                if (buttonType == newOrderButton) {
                    Platform.runLater(() -> {
                        try {
                            createNewDeliveryOrder();
                        } catch (Exception e) {
                            System.err.println("Error creating new delivery order: " + e.getMessage());
                            e.printStackTrace();
                        }
                    });
                }
                return buttonType;
            });

            // Show dialog
            dialog.showAndWait();

            // Reset toggle when dialog closes
            Platform.runLater(() -> deliveryToggle.setSelected(false));

        } catch (Exception e) {
            System.err.println("BillingKOTController: Error showing delivery orders dialog: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Failed to open delivery orders dialog: " + e.getMessage());
        }
    }

    /**
     * Create delivery orders content
     */
    private VBox createDeliveryOrdersContent() {
        try {
            VBox mainContainer = new VBox(15);
            mainContainer.setStyle("-fx-padding: 20; -fx-background-color: #f8f9fa;");

            // Header section
            HBox headerSection = new HBox(15);
            headerSection.setStyle("-fx-alignment: center-left; -fx-padding: 10; -fx-background-color: white; " +
                                 "-fx-border-radius: 8; -fx-background-radius: 8;");

            Label titleLabel = new Label("🚚 Active Delivery Orders");
            titleLabel.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

            Label countLabel = new Label("(" + getActiveDeliveryOrdersCount() + " orders)");
            countLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #7f8c8d;");

            Region spacer = new Region();
            HBox.setHgrow(spacer, Priority.ALWAYS);

            Button refreshButton = new Button("🔄 Refresh");
            refreshButton.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-border-radius: 5; " +
                                 "-fx-background-radius: 5; -fx-padding: 8 15;");
            refreshButton.setOnAction(e -> refreshDeliveryOrders());

            headerSection.getChildren().addAll(titleLabel, countLabel, spacer, refreshButton);

            // Orders container with scroll pane
            ScrollPane scrollPane = new ScrollPane();
            scrollPane.setFitToWidth(true);
            scrollPane.setStyle("-fx-background-color: transparent; -fx-border-color: transparent;");

            VBox ordersContainer = new VBox(10);
            ordersContainer.setStyle("-fx-padding: 10;");

            // Create sample delivery orders
            ordersContainer.getChildren().addAll(createSampleDeliveryOrders());

            scrollPane.setContent(ordersContainer);
            VBox.setVgrow(scrollPane, Priority.ALWAYS);

            mainContainer.getChildren().addAll(headerSection, scrollPane);

            return mainContainer;

        } catch (Exception e) {
            System.err.println("Error creating delivery orders content: " + e.getMessage());
            e.printStackTrace();

            VBox errorContainer = new VBox(10);
            errorContainer.setStyle("-fx-padding: 20; -fx-alignment: center;");
            errorContainer.getChildren().add(new Label("Error loading delivery orders: " + e.getMessage()));
            return errorContainer;
        }
    }

    /**
     * Get count of active delivery orders
     */
    private int getActiveDeliveryOrdersCount() {
        // In a real implementation, this would query the database
        return 5; // Sample count
    }

    /**
     * Refresh delivery orders
     */
    private void refreshDeliveryOrders() {
        try {
            System.out.println("BillingKOTController: Refreshing delivery orders");
            showAlert("Refresh", "Delivery orders refreshed successfully");
        } catch (Exception e) {
            System.err.println("Error refreshing delivery orders: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Create sample delivery orders for demonstration
     */
    private java.util.List<VBox> createSampleDeliveryOrders() {
        java.util.List<VBox> orders = new java.util.ArrayList<>();

        try {
            // Sample delivery orders
            String[][] orderData = {
                {"D001", "Swiggy", "John Doe", "9876543210", "123 Main St, City", "₹450.75", "PREPARING", "#e74c3c"},
                {"D002", "Zomato", "Jane Smith", "9876543211", "456 Oak Ave, Town", "₹320.50", "READY", "#f39c12"},
                {"D003", "Uber Eats", "Mike Johnson", "9876543212", "789 Pine Rd, Village", "₹275.25", "OUT_FOR_DELIVERY", "#3498db"},
                {"D004", "Direct", "Sarah Wilson", "9876543213", "321 Elm St, City", "₹520.00", "PREPARING", "#e74c3c"},
                {"D005", "Swiggy", "David Brown", "9876543214", "654 Maple Dr, Town", "₹380.75", "READY", "#f39c12"}
            };

            for (String[] data : orderData) {
                VBox orderCard = createDeliveryOrderCard(data[0], data[1], data[2], data[3], data[4], data[5], data[6], data[7]);
                orders.add(orderCard);
            }

        } catch (Exception e) {
            System.err.println("Error creating sample delivery orders: " + e.getMessage());
            e.printStackTrace();
        }

        return orders;
    }

    /**
     * Create delivery order card
     */
    private VBox createDeliveryOrderCard(String orderId, String platform, String customerName,
                                       String phone, String address, String amount, String status, String statusColor) {
        try {
            VBox card = new VBox(12);
            card.setStyle("-fx-background-color: white; -fx-border-color: " + statusColor + "; -fx-border-width: 2; " +
                         "-fx-border-radius: 8; -fx-background-radius: 8; -fx-padding: 15; " +
                         "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);");

            // Header with order ID and platform
            HBox header = new HBox(10);
            header.setStyle("-fx-alignment: center-left;");

            Label orderIdLabel = new Label("Order #" + orderId);
            orderIdLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

            Label platformLabel = new Label(platform);
            platformLabel.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-padding: 4 8; " +
                                 "-fx-border-radius: 12; -fx-background-radius: 12; -fx-font-size: 12px;");

            Region spacer1 = new Region();
            HBox.setHgrow(spacer1, Priority.ALWAYS);

            Label statusLabel = new Label(status.replace("_", " "));
            statusLabel.setStyle("-fx-background-color: " + statusColor + "; -fx-text-fill: white; -fx-padding: 4 8; " +
                               "-fx-border-radius: 12; -fx-background-radius: 12; -fx-font-size: 12px; -fx-font-weight: bold;");

            header.getChildren().addAll(orderIdLabel, platformLabel, spacer1, statusLabel);

            // Customer info
            VBox customerInfo = new VBox(5);
            customerInfo.setStyle("-fx-background-color: #f8f9fa; -fx-padding: 10; -fx-border-radius: 5; -fx-background-radius: 5;");

            Label customerLabel = new Label("👤 " + customerName);
            customerLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

            Label phoneLabel = new Label("📞 " + phone);
            phoneLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #7f8c8d;");

            Label addressLabel = new Label("📍 " + address);
            addressLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #7f8c8d;");
            addressLabel.setWrapText(true);

            customerInfo.getChildren().addAll(customerLabel, phoneLabel, addressLabel);

            // Amount and actions
            HBox footer = new HBox(10);
            footer.setStyle("-fx-alignment: center-left;");

            Label amountLabel = new Label(amount);
            amountLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #27ae60;");

            Region spacer2 = new Region();
            HBox.setHgrow(spacer2, Priority.ALWAYS);

            // Action buttons based on status
            HBox actionButtons = createDeliveryActionButtons(orderId, status);

            footer.getChildren().addAll(amountLabel, spacer2, actionButtons);

            card.getChildren().addAll(header, customerInfo, footer);

            return card;

        } catch (Exception e) {
            System.err.println("Error creating delivery order card: " + e.getMessage());
            e.printStackTrace();

            VBox errorCard = new VBox();
            errorCard.getChildren().add(new Label("Error loading order: " + orderId));
            return errorCard;
        }
    }

    /**
     * Create action buttons for delivery orders based on status
     */
    private HBox createDeliveryActionButtons(String orderId, String status) {
        HBox buttonContainer = new HBox(8);
        buttonContainer.setStyle("-fx-alignment: center-right;");

        try {
            switch (status) {
                case "PREPARING":
                    Button readyButton = new Button("✅ Mark Ready");
                    readyButton.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-border-radius: 5; " +
                                       "-fx-background-radius: 5; -fx-padding: 6 12; -fx-font-size: 12px;");
                    readyButton.setOnAction(e -> updateDeliveryOrderStatus(orderId, "READY"));
                    buttonContainer.getChildren().add(readyButton);
                    break;

                case "READY":
                    Button dispatchButton = new Button("🚚 Dispatch");
                    dispatchButton.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-border-radius: 5; " +
                                          "-fx-background-radius: 5; -fx-padding: 6 12; -fx-font-size: 12px;");
                    dispatchButton.setOnAction(e -> updateDeliveryOrderStatus(orderId, "OUT_FOR_DELIVERY"));
                    buttonContainer.getChildren().add(dispatchButton);
                    break;

                case "OUT_FOR_DELIVERY":
                    Button deliveredButton = new Button("✅ Delivered");
                    deliveredButton.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-border-radius: 5; " +
                                           "-fx-background-radius: 5; -fx-padding: 6 12; -fx-font-size: 12px;");
                    deliveredButton.setOnAction(e -> updateDeliveryOrderStatus(orderId, "DELIVERED"));
                    buttonContainer.getChildren().add(deliveredButton);
                    break;
            }

            // Common buttons for all statuses
            Button viewButton = new Button("👁️ View");
            viewButton.setStyle("-fx-background-color: #95a5a6; -fx-text-fill: white; -fx-border-radius: 5; " +
                              "-fx-background-radius: 5; -fx-padding: 6 12; -fx-font-size: 12px;");
            viewButton.setOnAction(e -> viewDeliveryOrderDetails(orderId));

            Button printButton = new Button("🖨️ Print");
            printButton.setStyle("-fx-background-color: #9b59b6; -fx-text-fill: white; -fx-border-radius: 5; " +
                               "-fx-background-radius: 5; -fx-padding: 6 12; -fx-font-size: 12px;");
            printButton.setOnAction(e -> printDeliveryOrder(orderId));

            buttonContainer.getChildren().addAll(viewButton, printButton);

        } catch (Exception e) {
            System.err.println("Error creating delivery action buttons: " + e.getMessage());
            e.printStackTrace();
        }

        return buttonContainer;
    }

    /**
     * Update delivery order status
     */
    private void updateDeliveryOrderStatus(String orderId, String newStatus) {
        try {
            System.out.println("BillingKOTController: Updating order " + orderId + " status to " + newStatus);
            showAlert("Status Updated", "Order " + orderId + " status updated to " + newStatus.replace("_", " "));
        } catch (Exception e) {
            System.err.println("Error updating delivery order status: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * View delivery order details
     */
    private void viewDeliveryOrderDetails(String orderId) {
        try {
            System.out.println("BillingKOTController: Viewing details for order " + orderId);
            showAlert("Order Details", "Viewing details for order " + orderId + "\n\nThis would show complete order information including items, customer details, and delivery tracking.");
        } catch (Exception e) {
            System.err.println("Error viewing delivery order details: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Print delivery order
     */
    private void printDeliveryOrder(String orderId) {
        try {
            System.out.println("BillingKOTController: Printing delivery order " + orderId);
            showAlert("Print Order", "Printing delivery order " + orderId + "\n\nThis would send the order to the printer for delivery slip generation.");
        } catch (Exception e) {
            System.err.println("Error printing delivery order: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Show pickup orders management dialog
     */
    private void showPickUpOrdersDialog() {
        try {
            System.out.println("BillingKOTController: Opening pickup orders dialog");

            // Create dialog
            Dialog<ButtonType> dialog = new Dialog<>();
            dialog.setTitle("🥡 Pick Up Mode");
            dialog.setHeaderText("Pick Up Orders Management");

            // Create content
            VBox content = createPickUpOrdersContent();

            // Set dialog content
            dialog.getDialogPane().setContent(content);
            dialog.getDialogPane().setPrefSize(1000, 700);

            // Add buttons
            ButtonType okButton = new ButtonType("OK", ButtonBar.ButtonData.OK_DONE);
            ButtonType newOrderButton = new ButtonType("New Pick Up Order", ButtonBar.ButtonData.OTHER);
            dialog.getDialogPane().getButtonTypes().addAll(newOrderButton, okButton);

            // Style the dialog
            dialog.getDialogPane().getStylesheets().add(getClass().getResource("/css/application.css").toExternalForm());
            dialog.getDialogPane().getStyleClass().add("pickup-dialog");

            // Add keyboard shortcut handling to the dialog
            setupPickUpDialogKeyboardShortcuts(dialog);

            // Handle button actions
            dialog.setResultConverter(buttonType -> {
                if (buttonType == newOrderButton) {
                    Platform.runLater(() -> {
                        try {
                            createNewPickUpOrder();
                        } catch (Exception e) {
                            System.err.println("Error creating new pickup order: " + e.getMessage());
                            e.printStackTrace();
                        }
                    });
                }
                return buttonType;
            });

            // Show dialog
            dialog.showAndWait();

            // Reset toggle when dialog closes
            Platform.runLater(() -> pickUpToggle.setSelected(false));

        } catch (Exception e) {
            System.err.println("BillingKOTController: Error showing pickup orders dialog: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Failed to open pickup orders dialog: " + e.getMessage());
        }
    }

    /**
     * Create pickup orders content
     */
    private VBox createPickUpOrdersContent() {
        try {
            VBox mainContainer = new VBox(15);
            mainContainer.setStyle("-fx-padding: 20; -fx-background-color: #f8f9fa;");

            // Header section
            HBox headerSection = new HBox(15);
            headerSection.setStyle("-fx-alignment: center-left; -fx-padding: 10; -fx-background-color: white; " +
                                 "-fx-border-radius: 8; -fx-background-radius: 8;");

            Label titleLabel = new Label("🥡 Active Pick Up Orders");
            titleLabel.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

            Label countLabel = new Label("(" + getActivePickUpOrdersCount() + " orders)");
            countLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #7f8c8d;");

            Region spacer = new Region();
            HBox.setHgrow(spacer, Priority.ALWAYS);

            Button refreshButton = new Button("🔄 Refresh");
            refreshButton.setStyle("-fx-background-color: #e67e22; -fx-text-fill: white; -fx-border-radius: 5; " +
                                 "-fx-background-radius: 5; -fx-padding: 8 15;");
            refreshButton.setOnAction(e -> refreshPickUpOrders());

            headerSection.getChildren().addAll(titleLabel, countLabel, spacer, refreshButton);

            // Orders container with scroll pane
            ScrollPane scrollPane = new ScrollPane();
            scrollPane.setFitToWidth(true);
            scrollPane.setStyle("-fx-background-color: transparent; -fx-border-color: transparent;");

            VBox ordersContainer = new VBox(10);
            ordersContainer.setStyle("-fx-padding: 10;");

            // Create sample pickup orders
            ordersContainer.getChildren().addAll(createSamplePickUpOrders());

            scrollPane.setContent(ordersContainer);
            VBox.setVgrow(scrollPane, Priority.ALWAYS);

            mainContainer.getChildren().addAll(headerSection, scrollPane);

            return mainContainer;

        } catch (Exception e) {
            System.err.println("Error creating pickup orders content: " + e.getMessage());
            e.printStackTrace();

            VBox errorContainer = new VBox(10);
            errorContainer.setStyle("-fx-padding: 20; -fx-alignment: center;");
            errorContainer.getChildren().add(new Label("Error loading pickup orders: " + e.getMessage()));
            return errorContainer;
        }
    }

    /**
     * Get count of active pickup orders
     */
    private int getActivePickUpOrdersCount() {
        // In a real implementation, this would query the database
        return 3; // Sample count
    }

    /**
     * Refresh pickup orders
     */
    private void refreshPickUpOrders() {
        try {
            System.out.println("BillingKOTController: Refreshing pickup orders");
            showAlert("Refresh", "Pick up orders refreshed successfully");
        } catch (Exception e) {
            System.err.println("Error refreshing pickup orders: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Create sample pickup orders for demonstration
     */
    private java.util.List<VBox> createSamplePickUpOrders() {
        java.util.List<VBox> orders = new java.util.ArrayList<>();

        try {
            // Sample pickup orders
            String[][] orderData = {
                {"P001", "Phone Order", "Alice Johnson", "9876543215", "₹280.50", "READY", "#f39c12"},
                {"P002", "Walk-in", "Bob Smith", "9876543216", "₹150.25", "PREPARING", "#e74c3c"},
                {"P003", "Online", "Carol Davis", "9876543217", "₹420.75", "READY", "#f39c12"}
            };

            for (String[] data : orderData) {
                VBox orderCard = createPickUpOrderCard(data[0], data[1], data[2], data[3], data[4], data[5], data[6]);
                orders.add(orderCard);
            }

        } catch (Exception e) {
            System.err.println("Error creating sample pickup orders: " + e.getMessage());
            e.printStackTrace();
        }

        return orders;
    }

    /**
     * Create pickup order card
     */
    private VBox createPickUpOrderCard(String orderId, String orderType, String customerName,
                                     String phone, String amount, String status, String statusColor) {
        try {
            VBox card = new VBox(12);
            card.setStyle("-fx-background-color: white; -fx-border-color: " + statusColor + "; -fx-border-width: 2; " +
                         "-fx-border-radius: 8; -fx-background-radius: 8; -fx-padding: 15; " +
                         "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);");

            // Header with order ID and type
            HBox header = new HBox(10);
            header.setStyle("-fx-alignment: center-left;");

            Label orderIdLabel = new Label("Order #" + orderId);
            orderIdLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

            Label typeLabel = new Label(orderType);
            typeLabel.setStyle("-fx-background-color: #e67e22; -fx-text-fill: white; -fx-padding: 4 8; " +
                             "-fx-border-radius: 12; -fx-background-radius: 12; -fx-font-size: 12px;");

            Region spacer1 = new Region();
            HBox.setHgrow(spacer1, Priority.ALWAYS);

            Label statusLabel = new Label(status.replace("_", " "));
            statusLabel.setStyle("-fx-background-color: " + statusColor + "; -fx-text-fill: white; -fx-padding: 4 8; " +
                               "-fx-border-radius: 12; -fx-background-radius: 12; -fx-font-size: 12px; -fx-font-weight: bold;");

            header.getChildren().addAll(orderIdLabel, typeLabel, spacer1, statusLabel);

            // Customer info
            VBox customerInfo = new VBox(5);
            customerInfo.setStyle("-fx-background-color: #f8f9fa; -fx-padding: 10; -fx-border-radius: 5; -fx-background-radius: 5;");

            Label customerLabel = new Label("👤 " + customerName);
            customerLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

            Label phoneLabel = new Label("📞 " + phone);
            phoneLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #7f8c8d;");

            customerInfo.getChildren().addAll(customerLabel, phoneLabel);

            // Amount and actions
            HBox footer = new HBox(10);
            footer.setStyle("-fx-alignment: center-left;");

            Label amountLabel = new Label(amount);
            amountLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #27ae60;");

            Region spacer2 = new Region();
            HBox.setHgrow(spacer2, Priority.ALWAYS);

            // Action buttons based on status
            HBox actionButtons = createPickUpActionButtons(orderId, status);

            footer.getChildren().addAll(amountLabel, spacer2, actionButtons);

            card.getChildren().addAll(header, customerInfo, footer);

            return card;

        } catch (Exception e) {
            System.err.println("Error creating pickup order card: " + e.getMessage());
            e.printStackTrace();

            VBox errorCard = new VBox();
            errorCard.getChildren().add(new Label("Error loading order: " + orderId));
            return errorCard;
        }
    }

    /**
     * Create action buttons for pickup orders based on status
     */
    private HBox createPickUpActionButtons(String orderId, String status) {
        HBox buttonContainer = new HBox(8);
        buttonContainer.setStyle("-fx-alignment: center-right;");

        try {
            switch (status) {
                case "PREPARING":
                    Button readyButton = new Button("✅ Mark Ready");
                    readyButton.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-border-radius: 5; " +
                                       "-fx-background-radius: 5; -fx-padding: 6 12; -fx-font-size: 12px;");
                    readyButton.setOnAction(e -> updatePickUpOrderStatus(orderId, "READY"));
                    buttonContainer.getChildren().add(readyButton);
                    break;

                case "READY":
                    Button pickedUpButton = new Button("🎯 Picked Up");
                    pickedUpButton.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-border-radius: 5; " +
                                          "-fx-background-radius: 5; -fx-padding: 6 12; -fx-font-size: 12px;");
                    pickedUpButton.setOnAction(e -> updatePickUpOrderStatus(orderId, "PICKED_UP"));
                    buttonContainer.getChildren().add(pickedUpButton);
                    break;
            }

            // Common buttons for all statuses
            Button viewButton = new Button("👁️ View");
            viewButton.setStyle("-fx-background-color: #95a5a6; -fx-text-fill: white; -fx-border-radius: 5; " +
                              "-fx-background-radius: 5; -fx-padding: 6 12; -fx-font-size: 12px;");
            viewButton.setOnAction(e -> viewPickUpOrderDetails(orderId));

            Button printButton = new Button("🖨️ Print");
            printButton.setStyle("-fx-background-color: #9b59b6; -fx-text-fill: white; -fx-border-radius: 5; " +
                               "-fx-background-radius: 5; -fx-padding: 6 12; -fx-font-size: 12px;");
            printButton.setOnAction(e -> printPickUpOrder(orderId));

            buttonContainer.getChildren().addAll(viewButton, printButton);

        } catch (Exception e) {
            System.err.println("Error creating pickup action buttons: " + e.getMessage());
            e.printStackTrace();
        }

        return buttonContainer;
    }

    /**
     * Update pickup order status
     */
    private void updatePickUpOrderStatus(String orderId, String newStatus) {
        try {
            System.out.println("BillingKOTController: Updating pickup order " + orderId + " status to " + newStatus);
            showAlert("Status Updated", "Order " + orderId + " status updated to " + newStatus.replace("_", " "));
        } catch (Exception e) {
            System.err.println("Error updating pickup order status: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * View pickup order details
     */
    private void viewPickUpOrderDetails(String orderId) {
        try {
            System.out.println("BillingKOTController: Viewing details for pickup order " + orderId);
            showAlert("Order Details", "Viewing details for pickup order " + orderId + "\n\nThis would show complete order information including items and customer details.");
        } catch (Exception e) {
            System.err.println("Error viewing pickup order details: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Print pickup order
     */
    private void printPickUpOrder(String orderId) {
        try {
            System.out.println("BillingKOTController: Printing pickup order " + orderId);
            showAlert("Print Order", "Printing pickup order " + orderId + "\n\nThis would send the order to the printer for pickup slip generation.");
        } catch (Exception e) {
            System.err.println("Error printing pickup order: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Setup keyboard shortcuts for delivery dialog
     */
    private void setupDeliveryDialogKeyboardShortcuts(Dialog<ButtonType> dialog) {
        try {
            System.out.println("BillingKOTController: Setting up keyboard shortcuts for delivery dialog");

            dialog.getDialogPane().setOnKeyPressed(event -> {
                try {
                    switch (event.getCode()) {
                        case ESCAPE:
                            System.out.println("BillingKOTController: ESC pressed in delivery dialog - closing");
                            event.consume();
                            dialog.close();
                            break;
                        case N:
                            if (event.isControlDown()) {
                                System.out.println("BillingKOTController: Ctrl+N pressed - creating new delivery order");
                                event.consume();
                                Platform.runLater(() -> createNewDeliveryOrder());
                            }
                            break;
                        case R:
                            if (event.isControlDown()) {
                                System.out.println("BillingKOTController: Ctrl+R pressed - refreshing delivery orders");
                                event.consume();
                                Platform.runLater(() -> refreshDeliveryOrders());
                            }
                            break;
                        default:
                            if (event.isControlDown() && !event.getCode().isModifierKey()) {
                                handleUnknownCtrlShortcut(event);
                            }
                            break;
                    }
                } catch (Exception e) {
                    System.err.println("Error handling key press in delivery dialog: " + e.getMessage());
                    e.printStackTrace();
                }
            });

            dialog.getDialogPane().setFocusTraversable(true);
            Platform.runLater(() -> {
                try {
                    dialog.getDialogPane().requestFocus();
                } catch (Exception e) {
                    System.err.println("Error requesting focus for delivery dialog: " + e.getMessage());
                }
            });

        } catch (Exception e) {
            System.err.println("Error setting up delivery dialog keyboard shortcuts: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Setup keyboard shortcuts for pickup dialog
     */
    private void setupPickUpDialogKeyboardShortcuts(Dialog<ButtonType> dialog) {
        try {
            System.out.println("BillingKOTController: Setting up keyboard shortcuts for pickup dialog");

            dialog.getDialogPane().setOnKeyPressed(event -> {
                try {
                    switch (event.getCode()) {
                        case ESCAPE:
                            System.out.println("BillingKOTController: ESC pressed in pickup dialog - closing");
                            event.consume();
                            dialog.close();
                            break;
                        case N:
                            if (event.isControlDown()) {
                                System.out.println("BillingKOTController: Ctrl+N pressed - creating new pickup order");
                                event.consume();
                                Platform.runLater(() -> createNewPickUpOrder());
                            }
                            break;
                        case R:
                            if (event.isControlDown()) {
                                System.out.println("BillingKOTController: Ctrl+R pressed - refreshing pickup orders");
                                event.consume();
                                Platform.runLater(() -> refreshPickUpOrders());
                            }
                            break;
                        default:
                            if (event.isControlDown() && !event.getCode().isModifierKey()) {
                                handleUnknownCtrlShortcut(event);
                            }
                            break;
                    }
                } catch (Exception e) {
                    System.err.println("Error handling key press in pickup dialog: " + e.getMessage());
                    e.printStackTrace();
                }
            });

            dialog.getDialogPane().setFocusTraversable(true);
            Platform.runLater(() -> {
                try {
                    dialog.getDialogPane().requestFocus();
                } catch (Exception e) {
                    System.err.println("Error requesting focus for pickup dialog: " + e.getMessage());
                }
            });

        } catch (Exception e) {
            System.err.println("Error setting up pickup dialog keyboard shortcuts: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Create new delivery order
     */
    private void createNewDeliveryOrder() {
        try {
            System.out.println("BillingKOTController: Creating new delivery order");
            showAlert("New Delivery Order", "Creating new delivery order...\n\nThis would open a form to create a new delivery order with customer details, items, and delivery address.");
        } catch (Exception e) {
            System.err.println("Error creating new delivery order: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Create new pickup order
     */
    private void createNewPickUpOrder() {
        try {
            System.out.println("BillingKOTController: Creating new pickup order");
            showAlert("New Pick Up Order", "Creating new pickup order...\n\nThis would open a form to create a new pickup order with customer details and items.");
        } catch (Exception e) {
            System.err.println("Error creating new pickup order: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @FXML
    private void addTable() {
        // Show dialog to add new table
        TextInputDialog dialog = new TextInputDialog();
        dialog.setTitle("Add New Table");
        dialog.setHeaderText("Add a new table to the restaurant");
        dialog.setContentText("Table Number:");

        dialog.showAndWait().ifPresent(tableNumber -> {
            try {
                int tableNum = Integer.parseInt(tableNumber);
                showAlert("Table Added", "Table " + tableNum + " has been added successfully.");
                refreshTables();
            } catch (NumberFormatException e) {
                showAlert("Error", "Please enter a valid table number!");
            }
        });
    }

    @FXML
    private void openTableReservation() {
        System.out.println("BillingKOTController: Opening table reservation");
        showAlert("Table Reservations", "Table reservation management will be implemented.");
    }

    @FXML
    private void openContestations() {
        System.out.println("BillingKOTController: Opening contestations");
        showAlert("Contestations", "Contestation management will be implemented.");
    }

    @FXML
    private void moveKOT() {
        String fromTable = fromTableCombo.getValue();
        String toTable = toTableCombo.getValue();

        if (fromTable == null || toTable == null) {
            showAlert("Error", "Please select both source and destination tables!");
            return;
        }

        if (fromTable.equals(toTable)) {
            showAlert("Error", "Source and destination tables cannot be the same!");
            return;
        }

        // Show confirmation dialog
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("Move KOT/Items");
        confirmAlert.setHeaderText("Move items from " + fromTable + " to " + toTable + "?");
        confirmAlert.setContentText("This will transfer all items from the source table to the destination table.");

        confirmAlert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                showAlert("Items Moved", "Items have been moved from " + fromTable + " to " + toTable + " successfully!");
                refreshTables();
            }
        });
    }

    // Inner class for preview items
    public static class PreviewItemRecord {
        private String itemName;
        private int quantity;
        private String price;
        private String total;
        
        public PreviewItemRecord(String itemName, int quantity, String price, String total) {
            this.itemName = itemName;
            this.quantity = quantity;
            this.price = price;
            this.total = total;
        }
        
        // Getters and setters
        public String getItemName() { return itemName; }
        public void setItemName(String itemName) { this.itemName = itemName; }
        
        public int getQuantity() { return quantity; }
        public void setQuantity(int quantity) { this.quantity = quantity; }
        
        public String getPrice() { return price; }
        public void setPrice(String price) { this.price = price; }
        
        public String getTotal() { return total; }
        public void setTotal(String total) { this.total = total; }
    }

    // Inner class for table reservations
    public static class ReservationRecord {
        private final javafx.beans.property.SimpleStringProperty name;
        private final javafx.beans.property.SimpleStringProperty table;
        private final javafx.beans.property.SimpleStringProperty date;
        private final javafx.beans.property.SimpleStringProperty time;
        private final javafx.beans.property.SimpleStringProperty guests;

        public ReservationRecord(String name, String table, String date, String time, String guests) {
            this.name = new javafx.beans.property.SimpleStringProperty(name);
            this.table = new javafx.beans.property.SimpleStringProperty(table);
            this.date = new javafx.beans.property.SimpleStringProperty(date);
            this.time = new javafx.beans.property.SimpleStringProperty(time);
            this.guests = new javafx.beans.property.SimpleStringProperty(guests);
        }

        public javafx.beans.property.StringProperty nameProperty() { return name; }
        public javafx.beans.property.StringProperty tableProperty() { return table; }
        public javafx.beans.property.StringProperty dateProperty() { return date; }
        public javafx.beans.property.StringProperty timeProperty() { return time; }
        public javafx.beans.property.StringProperty guestsProperty() { return guests; }
    }

    // Inner class for contestations
    public static class ContestationRecord {
        private final javafx.beans.property.SimpleStringProperty orderId;
        private final javafx.beans.property.SimpleStringProperty table;
        private final javafx.beans.property.SimpleStringProperty date;
        private final javafx.beans.property.SimpleStringProperty amount;
        private final javafx.beans.property.SimpleStringProperty status;

        public ContestationRecord(String orderId, String table, String date, String amount, String status) {
            this.orderId = new javafx.beans.property.SimpleStringProperty(orderId);
            this.table = new javafx.beans.property.SimpleStringProperty(table);
            this.date = new javafx.beans.property.SimpleStringProperty(date);
            this.amount = new javafx.beans.property.SimpleStringProperty(amount);
            this.status = new javafx.beans.property.SimpleStringProperty(status);
        }

        public javafx.beans.property.StringProperty orderIdProperty() { return orderId; }
        public javafx.beans.property.StringProperty tableProperty() { return table; }
        public javafx.beans.property.StringProperty dateProperty() { return date; }
        public javafx.beans.property.StringProperty amountProperty() { return amount; }
        public javafx.beans.property.StringProperty statusProperty() { return status; }
    }
}
