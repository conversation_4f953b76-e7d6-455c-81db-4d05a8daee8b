package com.restaurant.controller;

import com.restaurant.model.User;
import com.restaurant.model.Activity;
import com.restaurant.model.ActivityDAO;
import com.restaurant.model.Order;
import com.restaurant.model.OrderItem;
import com.restaurant.model.MenuItem;
import com.restaurant.model.BillRecord;
import com.restaurant.controller.HoldKOTController;
import com.restaurant.service.NotificationService;
import com.restaurant.util.KeyboardShortcutManager;
import com.restaurant.util.SceneEventHandlerManager;
import com.restaurant.util.ScrollPaneUtil;
import com.restaurant.util.UniversalNavigationManager;
import com.restaurant.controller.ScrollPaneManager;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.concurrent.Task;
import javafx.application.Platform;
import java.util.ArrayList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.Alert;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyCodeCombination;
import javafx.scene.input.KeyCombination;
import javafx.scene.layout.BorderPane;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.scene.input.KeyCode;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.restaurant.util.SmartNotificationManager;

public class DashboardController {

    // Static reference for global access
    private static DashboardController instance;

    // Static getter for global access
    public static DashboardController getInstance() {
        return instance;
    }

    // Flag to prevent multiple handler setups
    private boolean handlersInitialized = false;

    // Keyboard shortcut manager
    private KeyboardShortcutManager shortcutManager;

    // Background task executor for safe async operations
    private ExecutorService backgroundTaskExecutor;

    @FXML private BorderPane mainContainer;
    @FXML private Label userLabel;
    @FXML private Button menuManagementBtn;
    @FXML private Button homeBtn;
    @FXML private Button notificationsBtn;
    @FXML private Button navHomeBtn;
    @FXML private Button navNotificationsBtn;
    @FXML private StackPane contentArea;
    @FXML private VBox notificationPanelContainer;
    @FXML private ScrollPane dashboardHome;

    // AI-Powered Feature Buttons
    @FXML private Button aiForecasterBtn;
    @FXML private Button smartAssistantBtn;
    @FXML private Button floatingAIButton;

    // Dashboard Home elements
    @FXML private Label todayOrdersLabel;
    @FXML private Label todayRevenueLabel;
    @FXML private Label activeOrdersLabel;
    @FXML private Label staffCountLabel;
    @FXML private TableView<Activity> recentActivityTable;
    @FXML private TableColumn<Activity, String> activityTimeColumn;
    @FXML private TableColumn<Activity, String> activityTypeColumn;
    @FXML private TableColumn<Activity, String> activityDescriptionColumn;
    @FXML private TableColumn<Activity, String> activityUserColumn;
    
    private User currentUser;

    // Performance and stability improvements
    private volatile boolean isProcessingShortcut = false;
    private volatile boolean isDialogOpen = false;
    private long lastShortcutTime = 0;
    private static final long SHORTCUT_DEBOUNCE_MS = 300; // 300ms debounce
    private final Object shortcutLock = new Object();

    @FXML
    private void initialize() {
        try {
            System.out.println("DashboardController.initialize() called - Starting crash-safe initialization");

            // Set static instance for global access
            instance = this;

            // Set up crash prevention first
            setupCrashPrevention();

            // Initialize with a default admin user if currentUser is null
            if (currentUser == null) {
                System.out.println("currentUser is null, creating default admin user");
                // Create a default admin user for testing
                currentUser = new User(1, "admin", "ADMIN");
                if (userLabel != null) {
                    userLabel.setText("Logged in as: " + currentUser.getUsername() + " (" + currentUser.getRole() + ")");
                    System.out.println("User label updated with default user");
                } else {
                    System.out.println("userLabel is null");
                }
            } else {
                System.out.println("currentUser already set: " + currentUser.getUsername());
            }

            // Initialize smart notification manager
            Platform.runLater(() -> {
                try {
                    if (mainContainer != null && mainContainer.getScene() != null && mainContainer.getScene().getWindow() != null) {
                        SmartNotificationManager.getInstance().initialize((Stage) mainContainer.getScene().getWindow());
                        System.out.println("SmartNotificationManager initialized with primary stage");
                    }
                } catch (Exception e) {
                    System.err.println("Failed to initialize SmartNotificationManager: " + e.getMessage());
                }
            });

            // Initialize dashboard components with safe execution
            System.out.println("Initializing dashboard home...");
            safeExecute("initializeDashboardHome", this::initializeDashboardHome);

            // Load notification panel
            System.out.println("Loading notification panel...");
            safeExecute("loadNotificationPanel", this::loadNotificationPanel);

            // Configure all scroll panes
            safeExecute("configureAllScrollPanes", this::configureAllScrollPanes);

            // Initialize keyboard shortcuts
            safeExecute("initializeKeyboardShortcuts", this::initializeKeyboardShortcuts);

            System.out.println("Dashboard initialization complete - All components loaded safely");

            // Register with universal navigation manager
            UniversalNavigationManager.getInstance().setCurrentController("DashboardController");

        } catch (Exception e) {
            System.err.println("CRITICAL ERROR in DashboardController.initialize(): " + e.getMessage());
            e.printStackTrace();

            // Don't let initialization failure crash the app
            try {
                showSafeAlert("Dashboard Initialization Error",
                    "Dashboard failed to initialize properly: " + e.getMessage() +
                    "\n\nSome features may not work correctly. Please restart the application if problems persist.");
            } catch (Exception alertError) {
                System.err.println("Failed to show initialization error alert: " + alertError.getMessage());
            }
        }
    }

    /**
     * Set up crash prevention mechanisms
     */
    private void setupCrashPrevention() {
        try {
            // Set up exception handler for the current thread
            Thread.currentThread().setUncaughtExceptionHandler((thread, exception) -> {
                System.err.println("CRITICAL: Uncaught exception in DashboardController thread: " + exception.getMessage());
                exception.printStackTrace();

                // Try to show error dialog without crashing
                Platform.runLater(() -> {
                    try {
                        showSafeAlert("Critical Error",
                            "A critical error occurred in the dashboard: " + exception.getMessage() +
                            "\n\nThe application will attempt to continue. Please save your work and restart if problems persist.");
                    } catch (Exception e) {
                        System.err.println("Failed to show critical error dialog: " + e.getMessage());
                    }
                });
            });

            // Stop any existing background tasks that might be causing issues
            stopAllBackgroundTasks();

            System.out.println("Crash prevention mechanisms set up for DashboardController");
        } catch (Exception e) {
            System.err.println("Failed to set up crash prevention: " + e.getMessage());
        }
    }

    /**
     * Stop all background tasks to prevent NullPointerException loops
     */
    private void stopAllBackgroundTasks() {
        try {
            // Stop any running scheduled tasks
            if (backgroundTaskExecutor != null && !backgroundTaskExecutor.isShutdown()) {
                backgroundTaskExecutor.shutdownNow();
                System.out.println("Background task executor stopped");
            }

            // Reset the executor for new tasks
            backgroundTaskExecutor = Executors.newCachedThreadPool(r -> {
                Thread t = new Thread(r, "DashboardBackground-" + System.currentTimeMillis());
                t.setDaemon(true);
                t.setUncaughtExceptionHandler((thread, ex) -> {
                    System.err.println("Background task error in " + thread.getName() + ": " + ex.getMessage());
                    // Don't print stack trace to avoid spam
                });
                return t;
            });

        } catch (Exception e) {
            System.err.println("Error stopping background tasks: " + e.getMessage());
        }
    }

    /**
     * Cleanup method to be called when the controller is destroyed
     */
    public void cleanup() {
        try {
            if (backgroundTaskExecutor != null && !backgroundTaskExecutor.isShutdown()) {
                backgroundTaskExecutor.shutdownNow();
                System.out.println("Dashboard background tasks cleaned up");
            }
        } catch (Exception e) {
            System.err.println("Error during dashboard cleanup: " + e.getMessage());
        }
    }

    /**
     * Safe execution wrapper for initialization methods
     */
    private void safeExecute(String operationName, Runnable operation) {
        try {
            System.out.println("Safely executing: " + operationName);
            operation.run();
            System.out.println("Successfully completed: " + operationName);
        } catch (Exception e) {
            System.err.println("Error in " + operationName + ": " + e.getMessage());
            e.printStackTrace();
            // Don't show alert for every initialization error to avoid dialog spam
            // Just log and continue
        }
    }

    /**
     * Initialize keyboard shortcuts for fast billing and order management
     */
    private void initializeKeyboardShortcuts() {
        try {
            shortcutManager = KeyboardShortcutManager.getInstance();

            // Register shortcuts when scene is available
            Platform.runLater(() -> {
                if (mainContainer != null && mainContainer.getScene() != null) {
                    // Register with KeyboardShortcutManager
                    shortcutManager.registerShortcuts(mainContainer.getScene());

                    // Also register with SceneEventHandlerManager to preserve these shortcuts
                    SceneEventHandlerManager sceneManager = SceneEventHandlerManager.getInstance();
                    sceneManager.registerDashboardGlobalHandler(mainContainer.getScene(),
                        shortcutManager.getGlobalKeyHandler());

                    setupCustomShortcutActions();

                    // Register global ESC handler for dashboard
                    UniversalNavigationManager.getInstance().registerGlobalEscHandler(mainContainer.getScene());

                    System.out.println("Keyboard shortcuts initialized for fast billing");
                }
            });

        } catch (Exception e) {
            System.err.println("Failed to initialize keyboard shortcuts: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Setup custom actions for keyboard shortcuts specific to dashboard
     */
    private void setupCustomShortcutActions() {
        if (shortcutManager == null) return;

        try {
            // Update shortcut actions with actual functionality
            shortcutManager.updateShortcutAction(
                new KeyCodeCombination(KeyCode.N, KeyCombination.CONTROL_DOWN),
                this::handleNewOrderShortcut
            );

            // Ctrl+H (Hold KOT) shortcut removed to prevent application freezing

            shortcutManager.updateShortcutAction(
                new KeyCodeCombination(KeyCode.S, KeyCombination.CONTROL_DOWN),
                this::handleSettleBillShortcut
            );

            shortcutManager.updateShortcutAction(
                new KeyCodeCombination(KeyCode.P, KeyCombination.CONTROL_DOWN),
                this::handlePrintKOTShortcut
            );

            shortcutManager.updateShortcutAction(
                new KeyCodeCombination(KeyCode.D, KeyCombination.CONTROL_DOWN),
                this::handleApplyDiscountShortcut
            );

            shortcutManager.updateShortcutAction(
                new KeyCodeCombination(KeyCode.A, KeyCombination.CONTROL_DOWN),
                this::handleAdminSettingsShortcut
            );

            shortcutManager.updateShortcutAction(
                new KeyCodeCombination(KeyCode.L, KeyCombination.CONTROL_DOWN),
                this::handleLogoutShortcut
            );

            shortcutManager.updateShortcutAction(
                new KeyCodeCombination(KeyCode.M, KeyCombination.CONTROL_DOWN),
                this::handleMenuManagementShortcut
            );

            shortcutManager.updateShortcutAction(
                new KeyCodeCombination(KeyCode.U, KeyCombination.CONTROL_DOWN),
                this::handleUserManagementShortcut
            );

            shortcutManager.updateShortcutAction(
                new KeyCodeCombination(KeyCode.T, KeyCombination.CONTROL_DOWN),
                this::handleTableSwitchShortcut
            );

            shortcutManager.updateShortcutAction(
                new KeyCodeCombination(KeyCode.R, KeyCombination.CONTROL_DOWN),
                this::handleReprintBillShortcut
            );

            // Note: Ctrl+B removed to avoid conflict with MenuSelectionController's Generate Bill shortcut
            // Previous Bills functionality can be accessed via menu or other shortcut

            // Quick payment shortcuts
            shortcutManager.updateShortcutAction(
                new KeyCodeCombination(KeyCode.F2),
                this::handleQuickCashPayment
            );

            shortcutManager.updateShortcutAction(
                new KeyCodeCombination(KeyCode.F3),
                this::handleQuickCardPayment
            );

            shortcutManager.updateShortcutAction(
                new KeyCodeCombination(KeyCode.F4),
                this::handleQuickUPIPayment
            );

            System.out.println("Custom shortcut actions configured for dashboard");

        } catch (Exception e) {
            System.err.println("Error setting up custom shortcut actions: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // Keyboard Shortcut Handler Methods

    /**
     * Safe shortcut handler wrapper to prevent crashes and handle rapid clicks
     */
    private void safeShortcutHandler(String shortcutName, Runnable action) {
        synchronized (shortcutLock) {
            long currentTime = System.currentTimeMillis();

            // Debounce rapid key presses
            if (currentTime - lastShortcutTime < SHORTCUT_DEBOUNCE_MS) {
                System.out.println("⚠️ Shortcut " + shortcutName + " ignored - too rapid");
                return;
            }

            // Prevent concurrent shortcut processing
            if (isProcessingShortcut) {
                System.out.println("⚠️ Shortcut " + shortcutName + " ignored - already processing");
                return;
            }

            // Prevent opening multiple dialogs
            if (isDialogOpen) {
                System.out.println("⚠️ Shortcut " + shortcutName + " ignored - dialog already open");
                return;
            }

            lastShortcutTime = currentTime;
            isProcessingShortcut = true;

            Platform.runLater(() -> {
                try {
                    System.out.println("🚀 " + shortcutName + " shortcut activated");
                    action.run();
                } catch (Exception e) {
                    System.err.println("Error in " + shortcutName + " shortcut: " + e.getMessage());
                    e.printStackTrace();
                    showSafeAlert("Error", "Failed to execute " + shortcutName + ": " + e.getMessage());
                } finally {
                    isProcessingShortcut = false;
                }
            });
        }
    }

    /**
     * Safe alert dialog that won't crash if called multiple times
     */
    private void showSafeAlert(String title, String message) {
        try {
            if (!isDialogOpen) {
                isDialogOpen = true;
                Platform.runLater(() -> {
                    try {
                        Alert alert = new Alert(Alert.AlertType.INFORMATION);
                        alert.setTitle(title);
                        alert.setHeaderText(null);
                        alert.setContentText(message);
                        alert.showAndWait();
                    } catch (Exception e) {
                        System.err.println("Error showing alert: " + e.getMessage());
                    } finally {
                        isDialogOpen = false;
                    }
                });
            }
        } catch (Exception e) {
            System.err.println("Critical error in showSafeAlert: " + e.getMessage());
        }
    }

    /**
     * Handle Ctrl+N - New Order shortcut
     */
    private void handleNewOrderShortcut() {
        safeShortcutHandler("New Order", this::openNewOrderDialog);
    }

    /**
     * Open New Order dialog (thread-safe)
     */
    private void openNewOrderDialog() {
        if (isDialogOpen) {
            System.out.println("⚠️ New Order dialog ignored - another dialog is open");
            return;
        }

        try {
            isDialogOpen = true;

            // Create new order dialog
            Alert orderDialog = new Alert(Alert.AlertType.CONFIRMATION);
            orderDialog.setTitle("🍽️ New Order");
            orderDialog.setHeaderText("Create New Order");

            // Create content with table selection
            VBox content = new VBox(10);
            content.getChildren().addAll(
                new Label("Select table for new order:"),
                createTableSelectionGrid(),
                new Label("💡 Tip: Double-click on a table to start ordering")
            );

            orderDialog.getDialogPane().setContent(content);
            orderDialog.getDialogPane().setPrefWidth(400);
            orderDialog.getDialogPane().setPrefHeight(300);

            // Set dialog close handler
            orderDialog.setOnHidden(e -> isDialogOpen = false);

            orderDialog.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    // Create new order for selected table
                    Order newOrder = createSampleOrder();
                    showSafeAlert("Success", "New order created for Table " + newOrder.getTableNumber() +
                                 "\n\nOrder ID: " + newOrder.getId() +
                                 "\nItems: " + newOrder.getItems().size() +
                                 "\nTotal: ₹" + String.format("%.2f", newOrder.calculateTotal()));
                    System.out.println("New order created: " + newOrder.getId());
                }
            });

        } catch (Exception e) {
            System.err.println("Failed to open new order dialog: " + e.getMessage());
            showSafeAlert("Error", "Failed to create new order: " + e.getMessage());
        } finally {
            isDialogOpen = false;
        }
    }

    /**
     * Create table selection grid
     */
    private GridPane createTableSelectionGrid() {
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);

        // Create table buttons (3x3 grid)
        for (int row = 0; row < 3; row++) {
            for (int col = 0; col < 3; col++) {
                int tableNumber = (row * 3) + col + 1;
                Button tableBtn = new Button("Table " + tableNumber);
                tableBtn.setPrefSize(80, 60);
                tableBtn.setStyle("-fx-background-color: #10B981; -fx-text-fill: white; -fx-font-weight: bold;");

                final int finalTableNumber = tableNumber;
                tableBtn.setOnAction(e -> {
                    System.out.println("Table " + finalTableNumber + " selected for new order");
                    tableBtn.getScene().getWindow().hide();
                });

                grid.add(tableBtn, col, row);
            }
        }

        return grid;
    }

    // Ctrl+H (Hold KOT) shortcut and handler removed to prevent application freezing

    // Hold KOT dialog method removed to prevent application freezing

    /**
     * Create a sample order for demonstration
     */
    private Order createSampleOrder() {
        Order order = new Order();
        order.setId(1001);
        order.setTableNumber(5);
        order.setStatus("PENDING");
        order.setTakeaway(false);

        // Add sample items using correct constructors
        MenuItem item1 = new MenuItem(1, "Butter Chicken", 350.0, "Main Course", 1);
        MenuItem item2 = new MenuItem(2, "Garlic Naan", 80.0, "Bread", 2);
        MenuItem item3 = new MenuItem(3, "Basmati Rice", 120.0, "Rice", 3);

        OrderItem orderItem1 = new OrderItem(item1, 2);
        OrderItem orderItem2 = new OrderItem(item2, 3);
        OrderItem orderItem3 = new OrderItem(item3, 1);

        order.addItem(orderItem1);
        order.addItem(orderItem2);
        order.addItem(orderItem3);

        return order;
    }

    /**
     * Handle Ctrl+S - Settle Bill shortcut
     */
    private void handleSettleBillShortcut() {
        safeShortcutHandler("Settle Bill", this::openSettleBillDialog);
    }

    /**
     * Open Settle Bill dialog
     */
    private void openSettleBillDialog() {
        try {
            // Create sample order for billing
            Order orderToBill = createSampleOrder();
            double totalAmount = orderToBill.calculateTotal();

            Alert billDialog = new Alert(Alert.AlertType.CONFIRMATION);
            billDialog.setTitle("💳 Settle Bill");
            billDialog.setHeaderText("Process Payment & Complete Order");

            // Create bill content
            VBox billContent = new VBox(15);
            billContent.getChildren().addAll(
                new Label("Order Details:"),
                createBillSummary(orderToBill),
                new Label("Payment Method:"),
                createPaymentMethodSelection(),
                new Label("💡 Use F2 (Cash), F3 (Card), F4 (UPI) for quick payment")
            );

            billDialog.getDialogPane().setContent(billContent);
            billDialog.getDialogPane().setPrefWidth(450);
            billDialog.getDialogPane().setPrefHeight(400);

            billDialog.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    // Process payment
                    processPayment(orderToBill, totalAmount);
                }
            });

        } catch (Exception e) {
            System.err.println("Failed to open settle bill dialog: " + e.getMessage());
            showAlert("Error", "Failed to settle bill: " + e.getMessage());
        }
    }

    /**
     * Create bill summary
     */
    private VBox createBillSummary(Order order) {
        VBox summary = new VBox(5);
        summary.setStyle("-fx-background-color: #F8F9FA; -fx-padding: 10; -fx-border-color: #E5E7EB; -fx-border-radius: 5;");

        summary.getChildren().add(new Label("Table " + order.getTableNumber() + " - Order #" + order.getId()));

        for (OrderItem item : order.getItems()) {
            Label itemLabel = new Label(String.format("%dx %s - ₹%.2f",
                item.getQuantity(),
                item.getMenuItem().getName(),
                item.getTotalPrice()));
            summary.getChildren().add(itemLabel);
        }

        Label totalLabel = new Label("Total: ₹" + String.format("%.2f", order.calculateTotal()));
        totalLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 16px;");
        summary.getChildren().add(totalLabel);

        return summary;
    }

    /**
     * Create payment method selection
     */
    private HBox createPaymentMethodSelection() {
        HBox paymentMethods = new HBox(10);

        Button cashBtn = new Button("💵 Cash (F2)");
        cashBtn.setStyle("-fx-background-color: #10B981; -fx-text-fill: white; -fx-font-weight: bold;");
        cashBtn.setPrefWidth(120);

        Button cardBtn = new Button("💳 Card (F3)");
        cardBtn.setStyle("-fx-background-color: #3B82F6; -fx-text-fill: white; -fx-font-weight: bold;");
        cardBtn.setPrefWidth(120);

        Button upiBtn = new Button("📱 UPI (F4)");
        upiBtn.setStyle("-fx-background-color: #8B5CF6; -fx-text-fill: white; -fx-font-weight: bold;");
        upiBtn.setPrefWidth(120);

        paymentMethods.getChildren().addAll(cashBtn, cardBtn, upiBtn);
        return paymentMethods;
    }

    /**
     * Process payment
     */
    private void processPayment(Order order, double amount) {
        try {
            // Simulate payment processing
            System.out.println("Processing payment for Order #" + order.getId() + " - ₹" + amount);

            // Update order status
            order.setStatus("PAID");

            showAlert("Payment Successful",
                     "💳 Payment Processed Successfully!\n\n" +
                     "Order #" + order.getId() + "\n" +
                     "Table " + order.getTableNumber() + "\n" +
                     "Amount: ₹" + String.format("%.2f", amount) + "\n" +
                     "Status: PAID\n\n" +
                     "Receipt printed successfully!");

            System.out.println("Payment completed for Order #" + order.getId());

        } catch (Exception e) {
            System.err.println("Payment processing failed: " + e.getMessage());
            showAlert("Payment Failed", "Failed to process payment: " + e.getMessage());
        }
    }

    /**
     * Handle Ctrl+P - Print KOT shortcut
     */
    private void handlePrintKOTShortcut() {
        safeShortcutHandler("Print KOT", this::printKOT);
    }

    /**
     * Print Kitchen Order Ticket
     */
    private void printKOT() {
        try {
            // Create sample order for KOT
            Order orderToPrint = createSampleOrder();

            Alert kotDialog = new Alert(Alert.AlertType.INFORMATION);
            kotDialog.setTitle("🖨️ Print KOT");
            kotDialog.setHeaderText("Kitchen Order Ticket");

            // Create KOT content
            VBox kotContent = new VBox(10);
            kotContent.getChildren().addAll(
                createKOTHeader(orderToPrint),
                createKOTItems(orderToPrint),
                createKOTFooter()
            );

            kotDialog.getDialogPane().setContent(kotContent);
            kotDialog.getDialogPane().setPrefWidth(400);
            kotDialog.getDialogPane().setPrefHeight(350);

            // Add print button
            ButtonType printButton = new ButtonType("🖨️ Print to Kitchen", ButtonBar.ButtonData.OK_DONE);
            kotDialog.getButtonTypes().setAll(printButton, ButtonType.CANCEL);

            kotDialog.showAndWait().ifPresent(response -> {
                if (response == printButton) {
                    // Simulate printing
                    simulateKOTPrinting(orderToPrint);
                }
            });

        } catch (Exception e) {
            System.err.println("Failed to print KOT: " + e.getMessage());
            showAlert("Error", "Failed to print KOT: " + e.getMessage());
        }
    }

    /**
     * Create KOT header
     */
    private VBox createKOTHeader(Order order) {
        VBox header = new VBox(5);
        header.setStyle("-fx-background-color: #F3F4F6; -fx-padding: 10; -fx-border-color: #D1D5DB;");

        Label restaurantName = new Label("🍽️ RESTAURANT MANAGEMENT SYSTEM");
        restaurantName.setStyle("-fx-font-weight: bold; -fx-font-size: 14px;");

        Label kotTitle = new Label("KITCHEN ORDER TICKET");
        kotTitle.setStyle("-fx-font-weight: bold; -fx-font-size: 12px;");

        Label orderInfo = new Label("Order #" + order.getId() + " | Table " + order.getTableNumber());
        orderInfo.setStyle("-fx-font-weight: bold;");

        Label timestamp = new Label("Time: " + LocalDateTime.now().format(
            java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss")));

        header.getChildren().addAll(restaurantName, kotTitle, orderInfo, timestamp);
        return header;
    }

    /**
     * Create KOT items list
     */
    private VBox createKOTItems(Order order) {
        VBox items = new VBox(5);
        items.setStyle("-fx-padding: 10;");

        Label itemsHeader = new Label("ITEMS TO PREPARE:");
        itemsHeader.setStyle("-fx-font-weight: bold; -fx-underline: true;");
        items.getChildren().add(itemsHeader);

        for (OrderItem item : order.getItems()) {
            HBox itemRow = new HBox(10);

            Label quantity = new Label(item.getQuantity() + "x");
            quantity.setStyle("-fx-font-weight: bold; -fx-min-width: 30px;");

            Label itemName = new Label(item.getMenuItem().getName());
            itemName.setStyle("-fx-font-size: 12px;");

            Label category = new Label("[" + item.getMenuItem().getCategory() + "]");
            category.setStyle("-fx-font-size: 10px; -fx-text-fill: #6B7280;");

            itemRow.getChildren().addAll(quantity, itemName, category);
            items.getChildren().add(itemRow);
        }

        return items;
    }

    /**
     * Create KOT footer
     */
    private VBox createKOTFooter() {
        VBox footer = new VBox(5);
        footer.setStyle("-fx-background-color: #F3F4F6; -fx-padding: 10; -fx-border-color: #D1D5DB;");

        Label instructions = new Label("⚠️ KITCHEN INSTRUCTIONS:");
        instructions.setStyle("-fx-font-weight: bold; -fx-font-size: 10px;");

        Label note1 = new Label("• Prepare items in order listed");
        Label note2 = new Label("• Check for allergies and special requests");
        Label note3 = new Label("• Notify server when ready");

        note1.setStyle("-fx-font-size: 9px;");
        note2.setStyle("-fx-font-size: 9px;");
        note3.setStyle("-fx-font-size: 9px;");

        footer.getChildren().addAll(instructions, note1, note2, note3);
        return footer;
    }

    /**
     * Simulate KOT printing
     */
    private void simulateKOTPrinting(Order order) {
        try {
            System.out.println("🖨️ Printing KOT for Order #" + order.getId());

            // Simulate printing delay
            Thread.sleep(1000);

            showAlert("KOT Printed Successfully",
                     "🖨️ Kitchen Order Ticket Printed!\n\n" +
                     "Order #" + order.getId() + "\n" +
                     "Table " + order.getTableNumber() + "\n" +
                     "Items: " + order.getItems().size() + "\n\n" +
                     "✅ Sent to kitchen printer\n" +
                     "✅ Kitchen staff notified\n" +
                     "✅ Order status updated");

            System.out.println("KOT printing completed for Order #" + order.getId());

        } catch (Exception e) {
            System.err.println("KOT printing failed: " + e.getMessage());
            showAlert("Printing Failed", "Failed to print KOT: " + e.getMessage());
        }
    }

    /**
     * Handle Ctrl+D - Apply Discount shortcut
     */
    private void handleApplyDiscountShortcut() {
        safeShortcutHandler("Apply Discount", this::openDiscountDialog);
    }

    /**
     * Open discount dialog
     */
    private void openDiscountDialog() {
        try {
            Order orderForDiscount = createSampleOrder();
            double originalTotal = orderForDiscount.calculateTotal();

            Alert discountDialog = new Alert(Alert.AlertType.CONFIRMATION);
            discountDialog.setTitle("💰 Apply Discount");
            discountDialog.setHeaderText("Apply Discount to Order");

            VBox discountContent = new VBox(15);
            discountContent.getChildren().addAll(
                new Label("Order #" + orderForDiscount.getId() + " - Table " + orderForDiscount.getTableNumber()),
                new Label("Original Total: ₹" + String.format("%.2f", originalTotal)),
                createDiscountOptions(originalTotal)
            );

            discountDialog.getDialogPane().setContent(discountContent);
            discountDialog.getDialogPane().setPrefWidth(400);
            discountDialog.getDialogPane().setPrefHeight(300);

            discountDialog.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    applyDiscount(orderForDiscount, 10.0); // Default 10% discount
                }
            });

        } catch (Exception e) {
            System.err.println("Failed to open discount dialog: " + e.getMessage());
            showAlert("Error", "Failed to apply discount: " + e.getMessage());
        }
    }

    /**
     * Create discount options
     */
    private VBox createDiscountOptions(double originalTotal) {
        VBox options = new VBox(10);

        Label optionsLabel = new Label("Select Discount:");
        optionsLabel.setStyle("-fx-font-weight: bold;");

        // Discount buttons
        HBox discountButtons = new HBox(10);

        Button discount5 = new Button("5%");
        discount5.setStyle("-fx-background-color: #10B981; -fx-text-fill: white;");
        discount5.setPrefWidth(60);

        Button discount10 = new Button("10%");
        discount10.setStyle("-fx-background-color: #3B82F6; -fx-text-fill: white;");
        discount10.setPrefWidth(60);

        Button discount15 = new Button("15%");
        discount15.setStyle("-fx-background-color: #8B5CF6; -fx-text-fill: white;");
        discount15.setPrefWidth(60);

        Button discount20 = new Button("20%");
        discount20.setStyle("-fx-background-color: #EF4444; -fx-text-fill: white;");
        discount20.setPrefWidth(60);

        discountButtons.getChildren().addAll(discount5, discount10, discount15, discount20);

        // Discount preview
        VBox preview = new VBox(5);
        preview.setStyle("-fx-background-color: #F3F4F6; -fx-padding: 10; -fx-border-color: #D1D5DB;");

        Label previewLabel = new Label("Discount Preview (10%):");
        previewLabel.setStyle("-fx-font-weight: bold;");

        double discountAmount = originalTotal * 0.10;
        double finalTotal = originalTotal - discountAmount;

        Label discountAmountLabel = new Label("Discount Amount: -₹" + String.format("%.2f", discountAmount));
        Label finalTotalLabel = new Label("Final Total: ₹" + String.format("%.2f", finalTotal));
        finalTotalLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 14px;");

        preview.getChildren().addAll(previewLabel, discountAmountLabel, finalTotalLabel);

        options.getChildren().addAll(optionsLabel, discountButtons, preview);
        return options;
    }

    /**
     * Apply discount to order
     */
    private void applyDiscount(Order order, double discountPercentage) {
        try {
            double originalTotal = order.calculateTotal();
            double discountAmount = originalTotal * (discountPercentage / 100);
            double finalTotal = originalTotal - discountAmount;

            showAlert("Discount Applied Successfully",
                     "💰 Discount Applied!\n\n" +
                     "Order #" + order.getId() + "\n" +
                     "Table " + order.getTableNumber() + "\n\n" +
                     "Original Total: ₹" + String.format("%.2f", originalTotal) + "\n" +
                     "Discount (" + discountPercentage + "%): -₹" + String.format("%.2f", discountAmount) + "\n" +
                     "Final Total: ₹" + String.format("%.2f", finalTotal) + "\n\n" +
                     "✅ Discount applied successfully!");

            System.out.println("Discount applied: " + discountPercentage + "% on Order #" + order.getId());

        } catch (Exception e) {
            System.err.println("Failed to apply discount: " + e.getMessage());
            showAlert("Discount Failed", "Failed to apply discount: " + e.getMessage());
        }
    }

    /**
     * Handle Ctrl+A - Admin Settings shortcut
     */
    private void handleAdminSettingsShortcut() {
        Platform.runLater(() -> {
            try {
                System.out.println("🚀 Admin Settings shortcut activated (Ctrl+A)");
                openAdminSettingsDialog();
            } catch (Exception e) {
                System.err.println("Error handling Admin Settings shortcut: " + e.getMessage());
                showAlert("Error", "Failed to open admin settings: " + e.getMessage());
            }
        });
    }

    /**
     * Open admin settings dialog
     */
    private void openAdminSettingsDialog() {
        try {
            Alert settingsDialog = new Alert(Alert.AlertType.INFORMATION);
            settingsDialog.setTitle("⚙️ Admin Settings");
            settingsDialog.setHeaderText("System Configuration & Management");

            VBox settingsContent = new VBox(15);
            settingsContent.getChildren().addAll(
                createSettingsSection("🏪 Restaurant Settings",
                    "• Restaurant Name & Logo\n• Operating Hours\n• Table Configuration\n• Tax Settings"),
                createSettingsSection("👥 User Management",
                    "• Staff Accounts\n• Role Permissions\n• Access Control\n• Password Policies"),
                createSettingsSection("🖨️ Printer Settings",
                    "• Kitchen Printer\n• Receipt Printer\n• KOT Templates\n• Print Preferences"),
                createSettingsSection("💾 Data Management",
                    "• Database Backup\n• Data Export\n• System Logs\n• Performance Monitoring")
            );

            settingsDialog.getDialogPane().setContent(settingsContent);
            settingsDialog.getDialogPane().setPrefWidth(500);
            settingsDialog.getDialogPane().setPrefHeight(400);

            settingsDialog.showAndWait();

        } catch (Exception e) {
            System.err.println("Failed to open admin settings: " + e.getMessage());
            showAlert("Error", "Failed to open admin settings: " + e.getMessage());
        }
    }

    /**
     * Create settings section
     */
    private VBox createSettingsSection(String title, String content) {
        VBox section = new VBox(5);
        section.setStyle("-fx-background-color: #F8F9FA; -fx-padding: 10; -fx-border-color: #E5E7EB; -fx-border-radius: 5;");

        Label titleLabel = new Label(title);
        titleLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 14px;");

        Label contentLabel = new Label(content);
        contentLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #6B7280;");

        section.getChildren().addAll(titleLabel, contentLabel);
        return section;
    }

    /**
     * Handle F2 - Quick Cash Payment
     */
    private void handleQuickCashPayment() {
        safeShortcutHandler("Quick Cash Payment", () -> processQuickPayment("CASH", "💵"));
    }

    /**
     * Handle F3 - Quick Card Payment
     */
    private void handleQuickCardPayment() {
        safeShortcutHandler("Quick Card Payment", () -> processQuickPayment("CARD", "💳"));
    }

    /**
     * Handle F4 - Quick UPI Payment
     */
    private void handleQuickUPIPayment() {
        safeShortcutHandler("Quick UPI Payment", () -> processQuickPayment("UPI", "📱"));
    }

    /**
     * Process quick payment
     */
    private void processQuickPayment(String paymentMethod, String icon) {
        try {
            Order orderForPayment = createSampleOrder();
            double amount = orderForPayment.calculateTotal();

            Alert paymentDialog = new Alert(Alert.AlertType.CONFIRMATION);
            paymentDialog.setTitle(icon + " Quick " + paymentMethod + " Payment");
            paymentDialog.setHeaderText("Process " + paymentMethod + " Payment");

            VBox paymentContent = new VBox(15);
            paymentContent.getChildren().addAll(
                new Label("Order #" + orderForPayment.getId() + " - Table " + orderForPayment.getTableNumber()),
                new Label("Amount: ₹" + String.format("%.2f", amount)),
                new Label("Payment Method: " + icon + " " + paymentMethod),
                createQuickPaymentDetails(paymentMethod, amount)
            );

            paymentDialog.getDialogPane().setContent(paymentContent);
            paymentDialog.getDialogPane().setPrefWidth(400);
            paymentDialog.getDialogPane().setPrefHeight(250);

            paymentDialog.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    completeQuickPayment(orderForPayment, paymentMethod, amount, icon);
                }
            });

        } catch (Exception e) {
            System.err.println("Quick payment processing failed: " + e.getMessage());
            showAlert("Payment Failed", "Failed to process " + paymentMethod + " payment: " + e.getMessage());
        }
    }

    /**
     * Create quick payment details
     */
    private VBox createQuickPaymentDetails(String paymentMethod, double amount) {
        VBox details = new VBox(5);
        details.setStyle("-fx-background-color: #F3F4F6; -fx-padding: 10; -fx-border-color: #D1D5DB; -fx-border-radius: 5;");

        switch (paymentMethod) {
            case "CASH":
                details.getChildren().addAll(
                    new Label("💵 Cash Payment Details:"),
                    new Label("• Amount Received: ₹" + String.format("%.2f", amount)),
                    new Label("• Change to Return: ₹0.00"),
                    new Label("• Receipt: Will be printed")
                );
                break;
            case "CARD":
                details.getChildren().addAll(
                    new Label("💳 Card Payment Details:"),
                    new Label("• Card Terminal: Ready"),
                    new Label("• Amount: ₹" + String.format("%.2f", amount)),
                    new Label("• Transaction: Pending approval")
                );
                break;
            case "UPI":
                details.getChildren().addAll(
                    new Label("📱 UPI Payment Details:"),
                    new Label("• QR Code: Generated"),
                    new Label("• Amount: ₹" + String.format("%.2f", amount)),
                    new Label("• Status: Waiting for payment")
                );
                break;
        }

        return details;
    }

    /**
     * Complete quick payment
     */
    private void completeQuickPayment(Order order, String paymentMethod, double amount, String icon) {
        try {
            // Simulate payment processing
            Thread.sleep(1500);

            order.setStatus("PAID");

            showAlert("Payment Successful",
                     icon + " " + paymentMethod + " Payment Completed!\n\n" +
                     "Order #" + order.getId() + "\n" +
                     "Table " + order.getTableNumber() + "\n" +
                     "Amount: ₹" + String.format("%.2f", amount) + "\n" +
                     "Method: " + paymentMethod + "\n" +
                     "Status: PAID\n\n" +
                     "✅ Payment processed successfully\n" +
                     "✅ Receipt printed\n" +
                     "✅ Table available for new orders");

            System.out.println("Quick " + paymentMethod + " payment completed for Order #" + order.getId());

        } catch (Exception e) {
            System.err.println("Quick payment completion failed: " + e.getMessage());
            showAlert("Payment Failed", "Failed to complete " + paymentMethod + " payment: " + e.getMessage());
        }
    }

    /**
     * Handle Ctrl+T - See Tables shortcut
     */
    private void handleTableSwitchShortcut() {
        safeShortcutHandler("See Tables", this::quickSeeTables);
    }



    /**
     * Open table switch dialog with keyboard navigation
     */
    private void openTableSwitchDialog() {
        if (isDialogOpen) {
            System.out.println("⚠️ Table Switch dialog ignored - another dialog is open");
            return;
        }

        try {
            isDialogOpen = true;

            // Create table switch dialog
            Alert tableDialog = new Alert(Alert.AlertType.INFORMATION);
            tableDialog.setTitle("🍽️ Table Selection");
            tableDialog.setHeaderText("Select Table for Menu Access");

            // Create content with table grid and instructions
            VBox content = new VBox(15);
            content.getChildren().addAll(
                new Label("🎯 Quick Table Access:"),
                new Label("Press number keys (1-9) to go directly to table menu"),
                new Label("Or click on a table below:"),
                createInteractiveTableGrid(),
                new Label("💡 Tips:"),
                new Label("• Press 1-9 for tables 1-9"),
                new Label("• Press 0 for table 10"),
                new Label("• ESC to cancel")
            );

            tableDialog.getDialogPane().setContent(content);
            tableDialog.getDialogPane().setPrefWidth(500);
            tableDialog.getDialogPane().setPrefHeight(400);

            // Set dialog close handler
            tableDialog.setOnHidden(e -> isDialogOpen = false);

            // Add keyboard event handler for number keys
            tableDialog.getDialogPane().setOnKeyPressed(event -> {
                try {
                    int tableNumber = -1;

                    // Handle number keys
                    switch (event.getCode()) {
                        case DIGIT1: case NUMPAD1: tableNumber = 1; break;
                        case DIGIT2: case NUMPAD2: tableNumber = 2; break;
                        case DIGIT3: case NUMPAD3: tableNumber = 3; break;
                        case DIGIT4: case NUMPAD4: tableNumber = 4; break;
                        case DIGIT5: case NUMPAD5: tableNumber = 5; break;
                        case DIGIT6: case NUMPAD6: tableNumber = 6; break;
                        case DIGIT7: case NUMPAD7: tableNumber = 7; break;
                        case DIGIT8: case NUMPAD8: tableNumber = 8; break;
                        case DIGIT9: case NUMPAD9: tableNumber = 9; break;
                        case DIGIT0: case NUMPAD0: tableNumber = 10; break;
                        case ESCAPE:
                            tableDialog.close();
                            return;
                        default:
                            return; // Ignore other keys
                    }

                    if (tableNumber > 0) {
                        // Close dialog and navigate to table menu
                        tableDialog.close();
                        navigateToTableMenu(tableNumber);
                        event.consume();
                    }
                } catch (Exception e) {
                    System.err.println("Error handling table selection key: " + e.getMessage());
                }
            });

            // Make dialog focusable for keyboard events
            tableDialog.getDialogPane().requestFocus();

            // Custom buttons
            ButtonType selectButton = new ButtonType("Select Table", ButtonBar.ButtonData.OK_DONE);
            tableDialog.getButtonTypes().setAll(selectButton, ButtonType.CANCEL);

            tableDialog.showAndWait();

        } catch (Exception e) {
            System.err.println("Failed to open table switch dialog: " + e.getMessage());
            showSafeAlert("Error", "Failed to open table selection: " + e.getMessage());
        } finally {
            isDialogOpen = false;
        }
    }

    /**
     * Create interactive table grid with click handlers
     */
    private GridPane createInteractiveTableGrid() {
        GridPane grid = new GridPane();
        grid.setHgap(15);
        grid.setVgap(15);
        grid.setStyle("-fx-alignment: center; -fx-padding: 20;");

        // Create table buttons (3x4 grid for 12 tables)
        for (int row = 0; row < 4; row++) {
            for (int col = 0; col < 3; col++) {
                int tableNumber = (row * 3) + col + 1;
                if (tableNumber > 12) break; // Limit to 12 tables

                Button tableBtn = new Button("Table " + tableNumber);
                tableBtn.setPrefSize(100, 80);

                // Style based on table status (simulate different states)
                String style = getTableButtonStyle(tableNumber);
                tableBtn.setStyle(style);

                // Add table status indicator
                String statusText = getTableStatusText(tableNumber);
                tableBtn.setText("Table " + tableNumber + "\n" + statusText);

                final int finalTableNumber = tableNumber;
                tableBtn.setOnAction(e -> {
                    // Close parent dialog
                    ((Stage) tableBtn.getScene().getWindow()).close();
                    navigateToTableMenu(finalTableNumber);
                });

                // Add hover effects
                tableBtn.setOnMouseEntered(e -> {
                    tableBtn.setStyle(style + "-fx-scale-x: 1.05; -fx-scale-y: 1.05;");
                });
                tableBtn.setOnMouseExited(e -> {
                    tableBtn.setStyle(style);
                });

                grid.add(tableBtn, col, row);
            }
        }

        return grid;
    }

    /**
     * Get table button style based on status
     */
    private String getTableButtonStyle(int tableNumber) {
        // Simulate different table states
        switch (tableNumber % 4) {
            case 0: // Available
                return "-fx-background-color: #10B981; -fx-text-fill: white; -fx-font-weight: bold; -fx-border-radius: 8; -fx-background-radius: 8;";
            case 1: // Occupied
                return "-fx-background-color: #EF4444; -fx-text-fill: white; -fx-font-weight: bold; -fx-border-radius: 8; -fx-background-radius: 8;";
            case 2: // Reserved
                return "-fx-background-color: #F59E0B; -fx-text-fill: white; -fx-font-weight: bold; -fx-border-radius: 8; -fx-background-radius: 8;";
            case 3: // Billing
                return "-fx-background-color: #3B82F6; -fx-text-fill: white; -fx-font-weight: bold; -fx-border-radius: 8; -fx-background-radius: 8;";
            default:
                return "-fx-background-color: #6B7280; -fx-text-fill: white; -fx-font-weight: bold; -fx-border-radius: 8; -fx-background-radius: 8;";
        }
    }

    /**
     * Get table status text
     */
    private String getTableStatusText(int tableNumber) {
        switch (tableNumber % 4) {
            case 0: return "✅ Available";
            case 1: return "🔴 Occupied";
            case 2: return "🟡 Reserved";
            case 3: return "💳 Billing";
            default: return "❓ Unknown";
        }
    }

    /**
     * Navigate to table menu
     */
    private void navigateToTableMenu(int tableNumber) {
        try {
            System.out.println("🍽️ Navigating to Table " + tableNumber + " menu");

            // Create table menu dialog
            Alert menuDialog = new Alert(Alert.AlertType.INFORMATION);
            menuDialog.setTitle("🍽️ Table " + tableNumber + " - Menu");
            menuDialog.setHeaderText("Table " + tableNumber + " Order Management");

            VBox menuContent = new VBox(15);
            menuContent.getChildren().addAll(
                createTableInfoHeader(tableNumber),
                createQuickMenuActions(),
                createCurrentOrderSummary(tableNumber),
                new Label("💡 Use keyboard shortcuts for faster service:")
            );

            menuDialog.getDialogPane().setContent(menuContent);
            menuDialog.getDialogPane().setPrefWidth(600);
            menuDialog.getDialogPane().setPrefHeight(500);

            // Custom buttons for table actions
            ButtonType addItemBtn = new ButtonType("➕ Add Item", ButtonBar.ButtonData.OTHER);
            ButtonType printKOTBtn = new ButtonType("🖨️ Print KOT", ButtonBar.ButtonData.OTHER);
            ButtonType billBtn = new ButtonType("💳 Generate Bill", ButtonBar.ButtonData.OTHER);
            menuDialog.getButtonTypes().setAll(addItemBtn, printKOTBtn, billBtn, ButtonType.CLOSE);

            menuDialog.showAndWait().ifPresent(response -> {
                if (response == addItemBtn) {
                    handleAddItemToTable(tableNumber);
                } else if (response == printKOTBtn) {
                    handlePrintKOTForTable(tableNumber);
                } else if (response == billBtn) {
                    handleGenerateBillForTable(tableNumber);
                }
            });

        } catch (Exception e) {
            System.err.println("Failed to navigate to table menu: " + e.getMessage());
            showSafeAlert("Error", "Failed to open table " + tableNumber + " menu: " + e.getMessage());
        }
    }

    /**
     * Create table info header
     */
    private VBox createTableInfoHeader(int tableNumber) {
        VBox header = new VBox(5);
        header.setStyle("-fx-background-color: #F3F4F6; -fx-padding: 15; -fx-border-color: #D1D5DB; -fx-border-radius: 8; -fx-background-radius: 8;");

        Label tableInfo = new Label("🍽️ Table " + tableNumber + " - " + getTableStatusText(tableNumber));
        tableInfo.setStyle("-fx-font-weight: bold; -fx-font-size: 16px;");

        Label timeInfo = new Label("⏰ Session Time: " + java.time.LocalTime.now().format(
            java.time.format.DateTimeFormatter.ofPattern("HH:mm")));

        Label guestInfo = new Label("👥 Guests: " + (2 + (tableNumber % 4)) + " people");

        header.getChildren().addAll(tableInfo, timeInfo, guestInfo);
        return header;
    }

    /**
     * Create quick menu actions
     */
    private HBox createQuickMenuActions() {
        HBox actions = new HBox(10);
        actions.setStyle("-fx-alignment: center;");

        Button addItemBtn = new Button("➕ Add Item (Ctrl+N)");
        addItemBtn.setStyle("-fx-background-color: #10B981; -fx-text-fill: white; -fx-font-weight: bold;");
        addItemBtn.setPrefWidth(150);

        Button kotBtn = new Button("🖨️ Print KOT (Ctrl+P)");
        kotBtn.setStyle("-fx-background-color: #3B82F6; -fx-text-fill: white; -fx-font-weight: bold;");
        kotBtn.setPrefWidth(150);

        Button billBtn = new Button("💳 Bill (Ctrl+S)");
        billBtn.setStyle("-fx-background-color: #8B5CF6; -fx-text-fill: white; -fx-font-weight: bold;");
        billBtn.setPrefWidth(150);

        actions.getChildren().addAll(addItemBtn, kotBtn, billBtn);
        return actions;
    }

    /**
     * Create current order summary
     */
    private VBox createCurrentOrderSummary(int tableNumber) {
        VBox summary = new VBox(10);
        summary.setStyle("-fx-background-color: #F8F9FA; -fx-padding: 15; -fx-border-color: #E5E7EB; -fx-border-radius: 8; -fx-background-radius: 8;");

        Label summaryHeader = new Label("📋 Current Order:");
        summaryHeader.setStyle("-fx-font-weight: bold; -fx-font-size: 14px;");

        // Sample order items
        VBox items = new VBox(5);
        items.getChildren().addAll(
            new Label("• 2x Butter Chicken - ₹700.00"),
            new Label("• 1x Garlic Naan - ₹80.00"),
            new Label("• 1x Basmati Rice - ₹120.00")
        );

        Label total = new Label("Total: ₹900.00");
        total.setStyle("-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #059669;");

        summary.getChildren().addAll(summaryHeader, items, total);
        return summary;
    }

    /**
     * Handle add item to table
     */
    private void handleAddItemToTable(int tableNumber) {
        System.out.println("Adding item to Table " + tableNumber);
        showSafeAlert("Add Item", "Opening menu for Table " + tableNumber + "\n\nUse Ctrl+N for quick item addition");
    }

    /**
     * Handle print KOT for table
     */
    private void handlePrintKOTForTable(int tableNumber) {
        System.out.println("Printing KOT for Table " + tableNumber);
        showSafeAlert("Print KOT", "KOT printed for Table " + tableNumber + "\n\n✅ Sent to kitchen\n✅ Order updated");
    }

    /**
     * Handle generate bill for table
     */
    private void handleGenerateBillForTable(int tableNumber) {
        System.out.println("Generating bill for Table " + tableNumber);
        showSafeAlert("Generate Bill", "Bill generated for Table " + tableNumber + "\n\nTotal: ₹900.00\nReady for payment");
    }

    /**
     * Handle Ctrl+R - Reprint Last Bill shortcut
     */
    private void handleReprintBillShortcut() {
        safeShortcutHandler("Reprint Last Bill", this::openReprintBillDialog);
    }

    /**
     * Open reprint bill dialog
     */
    private void openReprintBillDialog() {
        try {
            Alert reprintDialog = new Alert(Alert.AlertType.CONFIRMATION);
            reprintDialog.setTitle("🖨️ Reprint Last Bill");
            reprintDialog.setHeaderText("Reprint Previous Bill");

            VBox reprintContent = new VBox(15);
            reprintContent.getChildren().addAll(
                new Label("Select bill to reprint:"),
                createRecentBillsList(),
                new Label("💡 Select a bill and click OK to reprint")
            );

            reprintDialog.getDialogPane().setContent(reprintContent);
            reprintDialog.getDialogPane().setPrefWidth(500);
            reprintDialog.getDialogPane().setPrefHeight(400);

            reprintDialog.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    reprintSelectedBill();
                }
            });

        } catch (Exception e) {
            System.err.println("Failed to open reprint dialog: " + e.getMessage());
            showAlert("Error", "Failed to open reprint dialog: " + e.getMessage());
        }
    }

    /**
     * Create recent bills list
     */
    private TableView<BillRecord> createRecentBillsList() {
        TableView<BillRecord> billsTable = new TableView<>();

        // Create columns
        TableColumn<BillRecord, String> billIdColumn = new TableColumn<>("Bill ID");
        billIdColumn.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleStringProperty(cellData.getValue().getBillId()));
        billIdColumn.setPrefWidth(100);

        TableColumn<BillRecord, Integer> tableColumn = new TableColumn<>("Table");
        tableColumn.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleIntegerProperty(cellData.getValue().getTableNumber()).asObject());
        tableColumn.setPrefWidth(60);

        TableColumn<BillRecord, String> timeColumn = new TableColumn<>("Time");
        timeColumn.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleStringProperty(cellData.getValue().getFormattedTime()));
        timeColumn.setPrefWidth(120);

        TableColumn<BillRecord, Double> amountColumn = new TableColumn<>("Amount");
        amountColumn.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleDoubleProperty(cellData.getValue().getAmount()).asObject());
        amountColumn.setPrefWidth(100);

        TableColumn<BillRecord, String> statusColumn = new TableColumn<>("Status");
        statusColumn.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleStringProperty(cellData.getValue().getStatus()));
        statusColumn.setPrefWidth(80);

        billsTable.getColumns().addAll(billIdColumn, tableColumn, timeColumn, amountColumn, statusColumn);

        // Add sample data
        ObservableList<BillRecord> bills = FXCollections.observableArrayList();
        bills.add(new BillRecord("BILL001", 5, LocalDateTime.now().minusMinutes(15), 1060.0, "PAID"));
        bills.add(new BillRecord("BILL002", 3, LocalDateTime.now().minusMinutes(30), 750.0, "PAID"));
        bills.add(new BillRecord("BILL003", 7, LocalDateTime.now().minusMinutes(45), 920.0, "PAID"));
        bills.add(new BillRecord("BILL004", 2, LocalDateTime.now().minusHours(1), 650.0, "PAID"));
        bills.add(new BillRecord("BILL005", 8, LocalDateTime.now().minusHours(2), 1200.0, "PAID"));

        billsTable.setItems(bills);
        billsTable.setPrefHeight(200);

        return billsTable;
    }

    /**
     * Reprint selected bill
     */
    private void reprintSelectedBill() {
        try {
            // Simulate bill reprinting
            BillRecord lastBill = new BillRecord("BILL001", 5, LocalDateTime.now().minusMinutes(15), 1060.0, "PAID");

            Alert reprintConfirm = new Alert(Alert.AlertType.INFORMATION);
            reprintConfirm.setTitle("🖨️ Bill Reprinted");
            reprintConfirm.setHeaderText("Bill Reprint Successful");

            VBox reprintDetails = new VBox(10);
            reprintDetails.getChildren().addAll(
                new Label("✅ Bill reprinted successfully!"),
                new Label(""),
                new Label("Bill Details:"),
                new Label("Bill ID: " + lastBill.getBillId()),
                new Label("Table: " + lastBill.getTableNumber()),
                new Label("Amount: ₹" + String.format("%.2f", lastBill.getAmount())),
                new Label("Time: " + lastBill.getFormattedTime()),
                new Label("Status: " + lastBill.getStatus()),
                new Label(""),
                new Label("🖨️ Receipt sent to printer"),
                new Label("📧 Copy saved to records")
            );

            reprintConfirm.getDialogPane().setContent(reprintDetails);
            reprintConfirm.getDialogPane().setPrefWidth(350);
            reprintConfirm.showAndWait();

            System.out.println("Bill reprinted: " + lastBill.getBillId());

        } catch (Exception e) {
            System.err.println("Failed to reprint bill: " + e.getMessage());
            showAlert("Reprint Failed", "Failed to reprint bill: " + e.getMessage());
        }
    }

    /**
     * Handle Ctrl+B - Previous Bills shortcut
     */
    private void handlePreviousBillsShortcut() {
        safeShortcutHandler("Previous Bills", this::openPreviousBillsDialog);
    }

    /**
     * Open previous bills dialog
     */
    private void openPreviousBillsDialog() {
        try {
            Alert billsDialog = new Alert(Alert.AlertType.INFORMATION);
            billsDialog.setTitle("📋 Previous Bills");
            billsDialog.setHeaderText("View & Manage Previous Bills");

            VBox billsContent = new VBox(15);
            billsContent.getChildren().addAll(
                createBillsFilterSection(),
                createPreviousBillsTable(),
                createBillsActionButtons()
            );

            billsDialog.getDialogPane().setContent(billsContent);
            billsDialog.getDialogPane().setPrefWidth(700);
            billsDialog.getDialogPane().setPrefHeight(500);

            // Add custom buttons
            ButtonType viewButton = new ButtonType("👁️ View Details", ButtonBar.ButtonData.OTHER);
            ButtonType reprintButton = new ButtonType("🖨️ Reprint", ButtonBar.ButtonData.OTHER);
            billsDialog.getButtonTypes().setAll(viewButton, reprintButton, ButtonType.CLOSE);

            billsDialog.showAndWait().ifPresent(response -> {
                if (response == viewButton) {
                    viewBillDetails();
                } else if (response == reprintButton) {
                    reprintSelectedBill();
                }
            });

        } catch (Exception e) {
            System.err.println("Failed to open previous bills dialog: " + e.getMessage());
            showAlert("Error", "Failed to open previous bills: " + e.getMessage());
        }
    }

    /**
     * Create bills filter section
     */
    private HBox createBillsFilterSection() {
        HBox filterSection = new HBox(10);
        filterSection.setStyle("-fx-background-color: #F8F9FA; -fx-padding: 10; -fx-border-color: #E5E7EB; -fx-border-radius: 5;");

        Label filterLabel = new Label("Filter:");
        filterLabel.setStyle("-fx-font-weight: bold;");

        ComboBox<String> dateFilter = new ComboBox<>();
        dateFilter.getItems().addAll("Today", "Yesterday", "Last 7 days", "Last 30 days", "All");
        dateFilter.setValue("Today");
        dateFilter.setPrefWidth(120);

        ComboBox<String> statusFilter = new ComboBox<>();
        statusFilter.getItems().addAll("All", "PAID", "PENDING", "CANCELLED");
        statusFilter.setValue("All");
        statusFilter.setPrefWidth(100);

        TextField searchField = new TextField();
        searchField.setPromptText("Search by Bill ID or Table...");
        searchField.setPrefWidth(200);

        Button refreshBtn = new Button("🔄 Refresh");
        refreshBtn.setStyle("-fx-background-color: #3B82F6; -fx-text-fill: white;");

        filterSection.getChildren().addAll(
            filterLabel,
            new Label("Date:"), dateFilter,
            new Label("Status:"), statusFilter,
            new Label("Search:"), searchField,
            refreshBtn
        );

        return filterSection;
    }

    /**
     * Create previous bills table
     */
    private TableView<BillRecord> createPreviousBillsTable() {
        TableView<BillRecord> billsTable = new TableView<>();

        // Create columns
        TableColumn<BillRecord, String> billIdColumn = new TableColumn<>("Bill ID");
        billIdColumn.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleStringProperty(cellData.getValue().getBillId()));
        billIdColumn.setPrefWidth(100);

        TableColumn<BillRecord, Integer> tableColumn = new TableColumn<>("Table");
        tableColumn.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleIntegerProperty(cellData.getValue().getTableNumber()).asObject());
        tableColumn.setPrefWidth(60);

        TableColumn<BillRecord, String> timeColumn = new TableColumn<>("Date & Time");
        timeColumn.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleStringProperty(cellData.getValue().getFormattedTime()));
        timeColumn.setPrefWidth(140);

        TableColumn<BillRecord, Double> amountColumn = new TableColumn<>("Amount");
        amountColumn.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleDoubleProperty(cellData.getValue().getAmount()).asObject());
        amountColumn.setPrefWidth(100);

        TableColumn<BillRecord, String> statusColumn = new TableColumn<>("Status");
        statusColumn.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleStringProperty(cellData.getValue().getStatus()));
        statusColumn.setPrefWidth(80);

        TableColumn<BillRecord, String> paymentColumn = new TableColumn<>("Payment");
        paymentColumn.setCellValueFactory(cellData ->
            new javafx.beans.property.SimpleStringProperty(cellData.getValue().getPaymentMethod()));
        paymentColumn.setPrefWidth(80);

        billsTable.getColumns().addAll(billIdColumn, tableColumn, timeColumn, amountColumn, statusColumn, paymentColumn);

        // Add sample data
        ObservableList<BillRecord> bills = FXCollections.observableArrayList();
        bills.add(new BillRecord("BILL001", 5, LocalDateTime.now().minusMinutes(15), 1060.0, "PAID", "CASH"));
        bills.add(new BillRecord("BILL002", 3, LocalDateTime.now().minusMinutes(30), 750.0, "PAID", "CARD"));
        bills.add(new BillRecord("BILL003", 7, LocalDateTime.now().minusMinutes(45), 920.0, "PAID", "UPI"));
        bills.add(new BillRecord("BILL004", 2, LocalDateTime.now().minusHours(1), 650.0, "PAID", "CASH"));
        bills.add(new BillRecord("BILL005", 8, LocalDateTime.now().minusHours(2), 1200.0, "PAID", "CARD"));
        bills.add(new BillRecord("BILL006", 4, LocalDateTime.now().minusHours(3), 890.0, "PAID", "UPI"));
        bills.add(new BillRecord("BILL007", 6, LocalDateTime.now().minusHours(4), 1150.0, "PAID", "CASH"));
        bills.add(new BillRecord("BILL008", 1, LocalDateTime.now().minusHours(5), 580.0, "PAID", "CARD"));

        billsTable.setItems(bills);
        billsTable.setPrefHeight(250);

        return billsTable;
    }

    /**
     * Create bills action buttons
     */
    private HBox createBillsActionButtons() {
        HBox actionButtons = new HBox(10);

        Button viewDetailsBtn = new Button("👁️ View Details");
        viewDetailsBtn.setStyle("-fx-background-color: #10B981; -fx-text-fill: white;");
        viewDetailsBtn.setPrefWidth(120);

        Button reprintBtn = new Button("🖨️ Reprint");
        reprintBtn.setStyle("-fx-background-color: #3B82F6; -fx-text-fill: white;");
        reprintBtn.setPrefWidth(100);

        Button exportBtn = new Button("📊 Export");
        exportBtn.setStyle("-fx-background-color: #8B5CF6; -fx-text-fill: white;");
        exportBtn.setPrefWidth(100);

        Button emailBtn = new Button("📧 Email");
        emailBtn.setStyle("-fx-background-color: #F59E0B; -fx-text-fill: white;");
        emailBtn.setPrefWidth(100);

        actionButtons.getChildren().addAll(viewDetailsBtn, reprintBtn, exportBtn, emailBtn);
        return actionButtons;
    }

    /**
     * View bill details
     */
    private void viewBillDetails() {
        try {
            BillRecord selectedBill = new BillRecord("BILL001", 5, LocalDateTime.now().minusMinutes(15), 1060.0, "PAID", "CASH");

            Alert detailsDialog = new Alert(Alert.AlertType.INFORMATION);
            detailsDialog.setTitle("📋 Bill Details");
            detailsDialog.setHeaderText("Bill #" + selectedBill.getBillId() + " - Table " + selectedBill.getTableNumber());

            VBox detailsContent = new VBox(10);
            detailsContent.getChildren().addAll(
                createBillHeader(selectedBill),
                createBillItemsList(),
                createBillSummary(selectedBill)
            );

            detailsDialog.getDialogPane().setContent(detailsContent);
            detailsDialog.getDialogPane().setPrefWidth(450);
            detailsDialog.getDialogPane().setPrefHeight(400);
            detailsDialog.showAndWait();

        } catch (Exception e) {
            System.err.println("Failed to view bill details: " + e.getMessage());
            showAlert("Error", "Failed to view bill details: " + e.getMessage());
        }
    }

    /**
     * Create bill header
     */
    private VBox createBillHeader(BillRecord bill) {
        VBox header = new VBox(5);
        header.setStyle("-fx-background-color: #F3F4F6; -fx-padding: 10; -fx-border-color: #D1D5DB; -fx-border-radius: 5;");

        Label billInfo = new Label("Bill ID: " + bill.getBillId() + " | Table: " + bill.getTableNumber());
        billInfo.setStyle("-fx-font-weight: bold; -fx-font-size: 14px;");

        Label timeInfo = new Label("Date & Time: " + bill.getFormattedTime());
        Label statusInfo = new Label("Status: " + bill.getStatus() + " | Payment: " + bill.getPaymentMethod());

        header.getChildren().addAll(billInfo, timeInfo, statusInfo);
        return header;
    }

    /**
     * Create bill items list
     */
    private VBox createBillItemsList() {
        VBox itemsList = new VBox(5);
        itemsList.setStyle("-fx-padding: 10;");

        Label itemsHeader = new Label("ITEMS ORDERED:");
        itemsHeader.setStyle("-fx-font-weight: bold; -fx-underline: true;");
        itemsList.getChildren().add(itemsHeader);

        // Sample items
        String[] items = {
            "2x Butter Chicken - ₹700.00",
            "3x Garlic Naan - ₹240.00",
            "1x Basmati Rice - ₹120.00"
        };

        for (String item : items) {
            Label itemLabel = new Label(item);
            itemLabel.setStyle("-fx-font-size: 12px; -fx-padding: 2 0 2 10;");
            itemsList.getChildren().add(itemLabel);
        }

        return itemsList;
    }

    /**
     * Create bill summary
     */
    private VBox createBillSummary(BillRecord bill) {
        VBox summary = new VBox(5);
        summary.setStyle("-fx-background-color: #F3F4F6; -fx-padding: 10; -fx-border-color: #D1D5DB; -fx-border-radius: 5;");

        Label subtotal = new Label("Subtotal: ₹1060.00");
        Label tax = new Label("Tax (18%): ₹190.80");
        Label total = new Label("Total: ₹" + String.format("%.2f", bill.getAmount()));
        total.setStyle("-fx-font-weight: bold; -fx-font-size: 14px;");

        summary.getChildren().addAll(subtotal, tax, total);
        return summary;
    }

    /**
     * Handle Ctrl+L - Logout shortcut
     */
    private void handleLogoutShortcut() {
        Platform.runLater(() -> {
            try {
                System.out.println("🚀 Logout shortcut activated (Ctrl+L)");
                Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
                alert.setTitle("Logout Confirmation");
                alert.setHeaderText("Confirm Logout");
                alert.setContentText("Are you sure you want to logout?\n\nThis will end your current session and return to the login screen.");

                alert.showAndWait().ifPresent(response -> {
                    if (response == ButtonType.OK) {
                        System.out.println("User confirmed logout via keyboard shortcut");
                        // Implement actual logout functionality here
                    }
                });
            } catch (Exception e) {
                System.err.println("Error handling Logout shortcut: " + e.getMessage());
            }
        });
    }

    /**
     * Handle Ctrl+M - Menu Management shortcut
     */
    private void handleMenuManagementShortcut() {
        Platform.runLater(() -> {
            try {
                System.out.println("🚀 Menu Management shortcut activated (Ctrl+M)");
                if (menuManagementBtn != null) {
                    menuManagementBtn.fire(); // Trigger the existing menu management button
                } else {
                    Alert alert = new Alert(Alert.AlertType.INFORMATION);
                    alert.setTitle("Menu Management");
                    alert.setHeaderText("Access Menu Management");
                    alert.setContentText("Menu Management functionality will be implemented here.\n\nThis allows quick access to add, edit, or remove menu items.");
                    alert.showAndWait();
                }
            } catch (Exception e) {
                System.err.println("Error handling Menu Management shortcut: " + e.getMessage());
            }
        });
    }

    /**
     * Handle Ctrl+U - User Management shortcut
     */
    private void handleUserManagementShortcut() {
        Platform.runLater(() -> {
            try {
                System.out.println("🚀 User Management shortcut activated (Ctrl+U)");
                Alert alert = new Alert(Alert.AlertType.INFORMATION);
                alert.setTitle("User Management");
                alert.setHeaderText("Access User Management");
                alert.setContentText("User Management functionality will be implemented here.\n\nThis allows quick access to manage staff accounts and permissions.");
                alert.showAndWait();
            } catch (Exception e) {
                System.err.println("Error handling User Management shortcut: " + e.getMessage());
            }
        });
    }

    public void initData(User user) {
        System.out.println("DashboardController.initData() called with user: " + (user != null ? user.getUsername() : "null"));
        this.currentUser = user;
        if (userLabel != null) {
            userLabel.setText("Logged in as: " + user.getUsername() + " (" + user.getRole() + ")");
            System.out.println("User label updated: " + userLabel.getText());
        } else {
            System.out.println("userLabel is null in initData");
        }

        // Only show menu management for admin users
        if (menuManagementBtn != null) {
            menuManagementBtn.setVisible(user.isAdmin());
            System.out.println("Menu management button visibility set to: " + user.isAdmin());
        } else {
            System.out.println("menuManagementBtn is null");
        }

        // Initialize dashboard home
        System.out.println("Calling initializeDashboardHome from initData...");
        initializeDashboardHome();

        // Setup double-click handlers (only once)
        setupStatCardDoubleClickHandlers();

        // Load dashboard home by default
        System.out.println("Calling loadDashboardHome from initData...");
        loadDashboardHome();
        System.out.println("initData completed successfully");
    }

    private void initializeDashboardHome() {
        // Initialize activity table columns
        if (activityTimeColumn != null) {
            activityTimeColumn.setCellValueFactory(new PropertyValueFactory<>("time"));
            activityTypeColumn.setCellValueFactory(new PropertyValueFactory<>("displayType"));
            activityDescriptionColumn.setCellValueFactory(new PropertyValueFactory<>("shortDescription"));
            activityUserColumn.setCellValueFactory(new PropertyValueFactory<>("user"));
        }

        // Load sample data
        updateDashboardStats();
        loadRecentActivity();
    }

    private void updateDashboardStats() {
        // Update stats labels asynchronously to prevent UI blocking
        if (todayOrdersLabel == null) {
            return;
        }

        // Show loading state
        showStatsLoadingState();

        // Load stats asynchronously
        Task<DashboardStats> loadStatsTask = new Task<DashboardStats>() {
            @Override
            protected DashboardStats call() throws Exception {
                // Simulate database calls - replace with actual DAO calls
                Thread.sleep(50); // Small delay to show loading

                // In a real implementation, these would be actual database calls:
                // int todayOrders = OrderDAO.getTodayOrderCount();
                // double todayRevenue = OrderDAO.getTodayRevenue();
                // int activeOrders = OrderDAO.getActiveOrderCount();
                // int staffCount = EmployeeDAO.getActiveStaffCount();

                return new DashboardStats(25, 12450.0, 8, 12);
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    try {
                        DashboardStats stats = getValue();
                        todayOrdersLabel.setText(String.valueOf(stats.todayOrders));
                        todayRevenueLabel.setText("₹" + String.format("%.2f", stats.todayRevenue));
                        activeOrdersLabel.setText(String.valueOf(stats.activeOrders));
                        staffCountLabel.setText(String.valueOf(stats.staffCount));
                        System.out.println("Dashboard stats loaded successfully");
                    } catch (Exception e) {
                        e.printStackTrace();
                        loadFallbackStats();
                    }
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    System.err.println("Failed to load dashboard stats: " + getException().getMessage());
                    loadFallbackStats();
                });
            }
        };

        // Run the task using the managed executor to prevent thread leaks
        if (backgroundTaskExecutor != null && !backgroundTaskExecutor.isShutdown()) {
            backgroundTaskExecutor.submit(loadStatsTask);
        } else {
            // Fallback to direct execution if executor is not available
            System.err.println("Background executor not available, loading fallback stats");
            Platform.runLater(() -> loadFallbackStats());
        }
    }

    private void showStatsLoadingState() {
        if (todayOrdersLabel != null) {
            todayOrdersLabel.setText("...");
            todayRevenueLabel.setText("...");
            activeOrdersLabel.setText("...");
            staffCountLabel.setText("...");
        }
    }

    private void loadFallbackStats() {
        if (todayOrdersLabel != null) {
            todayOrdersLabel.setText("25");
            todayRevenueLabel.setText("₹12,450.00");
            activeOrdersLabel.setText("8");
            staffCountLabel.setText("12");
        }
    }

    /**
     * Setup double-click handlers for dashboard statistics cards
     */
    private void setupStatCardDoubleClickHandlers() {
        // Prevent multiple handler setups
        if (handlersInitialized || todayOrdersLabel == null) {
            return;
        }

        try {
            // Today's Orders - Double tap to show order management
            todayOrdersLabel.setOnMouseClicked(event -> {
                if (event.getClickCount() == 2) {
                    event.consume(); // Prevent event bubbling
                    handleTodayOrdersDoubleClick();
                }
            });

            // Today's Revenue - Double tap to show revenue details
            todayRevenueLabel.setOnMouseClicked(event -> {
                if (event.getClickCount() == 2) {
                    event.consume(); // Prevent event bubbling
                    handleTodayRevenueDoubleClick();
                }
            });

            // Active Orders - Double tap to show active orders list
            activeOrdersLabel.setOnMouseClicked(event -> {
                if (event.getClickCount() == 2) {
                    event.consume(); // Prevent event bubbling
                    handleActiveOrdersDoubleClick();
                }
            });

            // Staff Members - Double tap to show staff management
            staffCountLabel.setOnMouseClicked(event -> {
                if (event.getClickCount() == 2) {
                    event.consume(); // Prevent event bubbling
                    handleStaffMembersDoubleClick();
                }
            });

            handlersInitialized = true;
            System.out.println("Double-click handlers setup for dashboard stats (initialized once)");

        } catch (Exception e) {
            System.err.println("Error setting up double-click handlers: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Handle double-click on Today's Orders stat card
     */
    private void handleTodayOrdersDoubleClick() {
        // Run on JavaFX Application Thread to prevent UI freezing
        Platform.runLater(() -> {
            try {
                System.out.println("Today's Orders double-clicked - Opening Order Management");

                // Show detailed order information dialog
                Alert alert = new Alert(Alert.AlertType.INFORMATION);
                alert.setTitle("Today's Orders Details");
                alert.setHeaderText("Order Summary for Today");

                // Get current stats safely
                String ordersText = (todayOrdersLabel != null) ? todayOrdersLabel.getText() : "0";
                String revenueText = (todayRevenueLabel != null) ? todayRevenueLabel.getText() : "₹0.00";

                // Parse orders count safely
                int ordersCount = 0;
                try {
                    ordersCount = Integer.parseInt(ordersText.equals("...") ? "0" : ordersText);
                } catch (NumberFormatException e) {
                    ordersCount = 25; // fallback value
                }

                String content = "📊 Total Orders: " + ordersText + "\n" +
                               "💰 Total Revenue: " + revenueText + "\n\n" +
                               "📈 Order Breakdown:\n" +
                               "• Completed Orders: " + Math.max(0, ordersCount - 3) + "\n" +
                               "• Pending Orders: 3\n" +
                               "• Cancelled Orders: 0\n\n" +
                               "🕒 Peak Hours: 12:00 PM - 2:00 PM\n" +
                               "📱 Online Orders: 60%\n" +
                               "🏪 Dine-in Orders: 40%\n\n" +
                               "Double-click to navigate to Order Management for detailed view.";

                alert.setContentText(content);
                alert.showAndWait();

            } catch (Exception e) {
                e.printStackTrace();
                System.err.println("Failed to show order details: " + e.getMessage());
            }
        });
    }

    /**
     * Handle double-click on Today's Revenue stat card
     */
    private void handleTodayRevenueDoubleClick() {
        // Run on JavaFX Application Thread to prevent UI freezing
        Platform.runLater(() -> {
            try {
                System.out.println("Today's Revenue double-clicked - Opening Revenue Details");

                Alert alert = new Alert(Alert.AlertType.INFORMATION);
                alert.setTitle("Today's Revenue Details");
                alert.setHeaderText("Revenue Breakdown for Today");

                // Get current stats safely
                String revenueText = (todayRevenueLabel != null) ? todayRevenueLabel.getText() : "₹0.00";
                String ordersText = (todayOrdersLabel != null) ? todayOrdersLabel.getText() : "0";

                // Calculate average order value safely
                double avgOrderValue = 0.0;
                try {
                    double revenue = Double.parseDouble(revenueText.replace("₹", "").replace(",", ""));
                    int orders = Integer.parseInt(ordersText.equals("...") ? "1" : ordersText);
                    avgOrderValue = (orders > 0) ? revenue / orders : 0.0;
                } catch (NumberFormatException e) {
                    avgOrderValue = 498.0; // fallback value
                }

                String content = "💰 Total Revenue: " + revenueText + "\n" +
                               "📊 Total Orders: " + ordersText + "\n\n" +
                               "💳 Payment Methods:\n" +
                               "• Cash: ₹4,500.00 (36%)\n" +
                               "• Card: ₹5,200.00 (42%)\n" +
                               "• UPI: ₹2,750.00 (22%)\n\n" +
                               "🍽️ Category Wise:\n" +
                               "• Main Course: ₹7,800.00 (63%)\n" +
                               "• Beverages: ₹2,450.00 (20%)\n" +
                               "• Appetizers: ₹1,500.00 (12%)\n" +
                               "• Desserts: ₹700.00 (5%)\n\n" +
                               "📈 Average Order Value: ₹" + String.format("%.2f", avgOrderValue);

                alert.setContentText(content);
                alert.showAndWait();

            } catch (Exception e) {
                e.printStackTrace();
                System.err.println("Failed to show revenue details: " + e.getMessage());
            }
        });
    }

    /**
     * Handle double-click on Active Orders stat card
     */
    private void handleActiveOrdersDoubleClick() {
        // Run on JavaFX Application Thread to prevent UI freezing
        Platform.runLater(() -> {
            try {
                System.out.println("Active Orders double-clicked - Opening Active Orders List");

                Alert alert = new Alert(Alert.AlertType.INFORMATION);
                alert.setTitle("Active Orders Details");
                alert.setHeaderText("Currently Active Orders");

                // Get current stats safely
                String activeOrdersText = (activeOrdersLabel != null) ? activeOrdersLabel.getText() : "0";

                String content = "🍽️ Active Orders: " + activeOrdersText + "\n\n" +
                               "📋 Order Status Breakdown:\n" +
                               "• Preparing: 3 orders\n" +
                               "• Ready to Serve: 2 orders\n" +
                               "• Waiting for Payment: 1 order\n" +
                               "• Kitchen Queue: 2 orders\n\n" +
                               "⏱️ Average Preparation Time: 18 minutes\n" +
                               "🔥 Priority Orders: 1\n" +
                               "📱 Online Orders: 3\n" +
                               "🏪 Dine-in Orders: 5\n\n" +
                               "🚨 Urgent Actions Needed:\n" +
                               "• Table 5: Order ready for 8 minutes\n" +
                               "• Table 12: Special dietary requirements\n\n" +
                               "Double-click to navigate to Order Management for real-time tracking.";

                alert.setContentText(content);
                alert.showAndWait();

            } catch (Exception e) {
                e.printStackTrace();
                System.err.println("Failed to show active orders details: " + e.getMessage());
            }
        });
    }

    /**
     * Handle double-click on Staff Members stat card
     */
    private void handleStaffMembersDoubleClick() {
        // Run on JavaFX Application Thread to prevent UI freezing
        Platform.runLater(() -> {
            try {
                System.out.println("Staff Members double-clicked - Opening Staff Management");

                Alert alert = new Alert(Alert.AlertType.INFORMATION);
                alert.setTitle("Staff Members Details");
                alert.setHeaderText("Current Staff Information");

                // Get current stats safely
                String staffCountText = (staffCountLabel != null) ? staffCountLabel.getText() : "0";

                String content = "👥 Total Staff: " + staffCountText + " members\n\n" +
                               "📊 Staff by Department:\n" +
                               "• Kitchen Staff: 4 members\n" +
                               "• Waiters/Servers: 5 members\n" +
                               "• Management: 2 members\n" +
                               "• Cleaning Staff: 1 member\n\n" +
                               "⏰ Current Shift Status:\n" +
                               "• On Duty: 10 members\n" +
                               "• On Break: 2 members\n" +
                               "• Off Duty: 0 members\n\n" +
                               "📈 Performance Today:\n" +
                               "• Average Service Time: 12 minutes\n" +
                               "• Customer Satisfaction: 4.7/5\n" +
                               "• Orders Served: 25\n\n" +
                               "🎯 Top Performers:\n" +
                               "• John Doe (Waiter) - 8 orders served\n" +
                               "• Jane Smith (Chef) - 15 dishes prepared\n\n" +
                               "Double-click to navigate to Staff Management for detailed view.";

                alert.setContentText(content);
                alert.showAndWait();

            } catch (Exception e) {
                e.printStackTrace();
                System.err.println("Failed to show staff details: " + e.getMessage());
            }
        });
    }

    // Helper class for dashboard statistics
    private static class DashboardStats {
        final int todayOrders;
        final double todayRevenue;
        final int activeOrders;
        final int staffCount;

        DashboardStats(int todayOrders, double todayRevenue, int activeOrders, int staffCount) {
            this.todayOrders = todayOrders;
            this.todayRevenue = todayRevenue;
            this.activeOrders = activeOrders;
            this.staffCount = staffCount;
        }
    }

    private void loadRecentActivity() {
        if (recentActivityTable == null) {
            return;
        }

        // Show loading indicator
        showActivityLoadingState();

        // Load activities asynchronously to prevent UI blocking
        Task<List<Activity>> loadActivitiesTask = new Task<List<Activity>>() {
            @Override
            protected List<Activity> call() throws Exception {
                try {
                    // Small delay to show loading state
                    Thread.sleep(50);

                    // Load activities with timeout protection
                    List<Activity> activities = ActivityDAO.getRecentActivities(10);

                    if (activities == null || activities.isEmpty()) {
                        System.out.println("No activities returned from DAO, will use fallback");
                        return new ArrayList<>(); // Return empty list instead of null
                    }

                    return activities;
                } catch (Exception e) {
                    System.err.println("Error in loadActivitiesTask.call(): " + e.getMessage());
                    e.printStackTrace();
                    throw e; // Re-throw to trigger failed() method
                }
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    try {
                        List<Activity> activities = getValue();

                        if (activities == null || activities.isEmpty()) {
                            System.out.println("No activities loaded, using fallback");
                            loadFallbackActivities();
                            return;
                        }

                        ObservableList<Activity> observableActivities = FXCollections.observableArrayList(activities);

                        // Ensure table is still available
                        if (recentActivityTable != null) {
                            recentActivityTable.setItems(observableActivities);
                            hideActivityLoadingState();
                            System.out.println("Recent activities loaded successfully: " + activities.size() + " items");
                        } else {
                            System.err.println("Recent activity table is null during succeeded()");
                        }

                    } catch (Exception e) {
                        System.err.println("Error in loadActivitiesTask.succeeded(): " + e.getMessage());
                        e.printStackTrace();
                        loadFallbackActivities();
                    }
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    Throwable exception = getException();
                    String errorMsg = exception != null ? exception.getMessage() : "Unknown error";
                    System.err.println("Failed to load recent activities: " + errorMsg);
                    if (exception != null) {
                        exception.printStackTrace();
                    }
                    loadFallbackActivities();
                });
            }
        };

        // Run the task using the managed executor to prevent thread leaks
        if (backgroundTaskExecutor != null && !backgroundTaskExecutor.isShutdown()) {
            backgroundTaskExecutor.submit(loadActivitiesTask);
        } else {
            // Fallback to direct execution if executor is not available
            System.err.println("Background executor not available, loading fallback activities");
            Platform.runLater(() -> loadFallbackActivities());
        }
    }

    private void showActivityLoadingState() {
        if (recentActivityTable != null) {
            // Create a placeholder item to show loading
            ObservableList<Activity> loadingList = FXCollections.observableArrayList();
            Activity loadingActivity = new Activity("System", "Loading recent activities...", "System");
            loadingList.add(loadingActivity);
            recentActivityTable.setItems(loadingList);
        }
    }

    private void hideActivityLoadingState() {
        // Loading state is automatically hidden when real data is loaded
    }

    private void loadFallbackActivities() {
        try {
            // Check if table is available
            if (recentActivityTable == null) {
                System.err.println("Recent activity table is not initialized");
                return;
            }

            ObservableList<Activity> fallbackActivities = FXCollections.observableArrayList();

            // Create fallback activities with current time
            LocalDateTime now = LocalDateTime.now();
            fallbackActivities.add(Activity.createOrderActivity("New order #1025 created for Table 5", "John Doe"));
            fallbackActivities.add(Activity.createPaymentActivity("Order #1024 paid ₹850.00", "Admin"));
            fallbackActivities.add(Activity.createMenuActivity("Added new item: Chicken Biryani", "Admin"));
            fallbackActivities.add(Activity.createUserActivity("New staff member added", "Admin"));
            fallbackActivities.add(Activity.createOrderActivity("Order #1023 completed", "Jane Smith"));
            fallbackActivities.add(Activity.createInventoryActivity("Stock updated: Chicken - 50kg", "Manager"));
            fallbackActivities.add(Activity.createTableActivity("Table 7 status changed to Available", "Waiter"));
            fallbackActivities.add(Activity.createSystemActivity("Daily backup completed successfully"));

            recentActivityTable.setItems(fallbackActivities);
            hideActivityLoadingState();
            System.out.println("Loaded fallback activities: " + fallbackActivities.size() + " items");
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("Failed to load even fallback activities: " + e.getMessage());
        }
    }
    
    @FXML
    private void loadDashboardHome() {
        try {
            System.out.println("Loading dashboard home...");

            // Check for null components
            if (mainContainer == null) {
                System.err.println("mainContainer is null in loadDashboardHome");
                showSafeAlert("Error", "UI not properly initialized. Please restart the application.");
                return;
            }

            if (contentArea == null) {
                System.err.println("contentArea is null in loadDashboardHome");
                showSafeAlert("Error", "Dashboard content area not found. Please restart the application.");
                return;
            }

            // Set the center back to the original dashboard home content
            mainContainer.setCenter(contentArea);

            // Refresh dashboard data safely
            updateDashboardStats();
            loadRecentActivity();

            System.out.println("Dashboard home loaded successfully");
        } catch (Exception e) {
            System.err.println("Error in loadDashboardHome: " + e.getMessage());
            e.printStackTrace();
            showSafeAlert("Error", "Failed to load dashboard home: " + e.getMessage());
        }
    }

    @FXML
    private void loadUserManagement() {
        try {
            System.out.println("Loading user management...");
            if (currentUser == null) {
                System.err.println("currentUser is null in loadUserManagement");
                showSafeAlert("Error", "User session not found. Please log in again.");
                return;
            }
            if (!currentUser.isAdmin()) {
                showSafeAlert("Access Denied", "Only administrators can access user management.");
                return;
            }
            loadView("/fxml/UserManagement.fxml");
        } catch (Exception e) {
            System.err.println("Error in loadUserManagement: " + e.getMessage());
            e.printStackTrace();
            showSafeAlert("Error", "Failed to load user management: " + e.getMessage());
        }
    }

    @FXML
    private void loadStaffRecord() {
        try {
            System.out.println("Loading staff record...");
            loadView("/fxml/StaffRecord.fxml");
        } catch (Exception e) {
            System.err.println("Error in loadStaffRecord: " + e.getMessage());
            e.printStackTrace();
            showSafeAlert("Error", "Failed to load staff record: " + e.getMessage());
        }
    }

    @FXML
    private void loadOrderManagement() {
        try {
            System.out.println("Loading order management...");
            loadView("/fxml/OrderManagement.fxml");
        } catch (Exception e) {
            System.err.println("Error in loadOrderManagement: " + e.getMessage());
            e.printStackTrace();
            showSafeAlert("Error", "Failed to load order management: " + e.getMessage());
        }
    }

    @FXML
    private void loadOrderEntry() {
        try {
            System.out.println("Loading order entry...");
            loadView("/fxml/OrderEntry.fxml");
        } catch (Exception e) {
            System.err.println("Error in loadOrderEntry: " + e.getMessage());
            e.printStackTrace();
            showSafeAlert("Error", "Failed to load order entry: " + e.getMessage());
        }
    }

    @FXML
    public void loadBillingKOT() {
        try {
            System.out.println("Loading billing KOT...");
            if (currentUser == null) {
                System.err.println("currentUser is null in loadBillingKOT");
                showSafeAlert("Error", "User session not found. Please log in again.");
                return;
            }
            if (!currentUser.isAdmin()) {
                showSafeAlert("Access Denied", "Only administrators can access billing.");
                return;
            }
            loadView("/fxml/BillingKOT.fxml");
        } catch (Exception e) {
            System.err.println("Error in loadBillingKOT: " + e.getMessage());
            e.printStackTrace();
            showSafeAlert("Error", "Failed to load billing: " + e.getMessage());
        }
    }

    @FXML
    private void loadSettings() {
        try {
            System.out.println("Loading settings...");
            if (currentUser == null) {
                System.err.println("currentUser is null in loadSettings");
                showSafeAlert("Error", "User session not found. Please log in again.");
                return;
            }
            if (!currentUser.isAdmin()) {
                showSafeAlert("Access Denied", "Only administrators can access settings.");
                return;
            }
            loadView("/fxml/Settings.fxml");
        } catch (Exception e) {
            System.err.println("Error in loadSettings: " + e.getMessage());
            e.printStackTrace();
            showSafeAlert("Error", "Failed to load settings: " + e.getMessage());
        }
    }

    private void loadView(String fxmlPath) {
        try {
            System.out.println("Attempting to load view: " + fxmlPath);

            // Add null check for mainContainer
            if (mainContainer == null) {
                System.err.println("mainContainer is null, cannot load view: " + fxmlPath);
                showSafeAlert("Error", "UI not properly initialized. Please restart the application.");
                return;
            }

            // Check if we're on the JavaFX Application Thread
            if (!Platform.isFxApplicationThread()) {
                System.err.println("loadView called from non-JavaFX thread for: " + fxmlPath);
                Platform.runLater(() -> loadView(fxmlPath));
                return;
            }

            // Load the FXML file
            FXMLLoader loader = new FXMLLoader(getClass().getResource(fxmlPath));
            if (loader.getLocation() == null) {
                System.err.println("FXML file not found: " + fxmlPath);
                showSafeAlert("Error", "View file not found: " + fxmlPath);
                return;
            }

            Parent view = loader.load();
            if (view == null) {
                System.err.println("Failed to load view from: " + fxmlPath);
                showSafeAlert("Error", "Failed to load view: " + fxmlPath);
                return;
            }

            // Set the view in the main container
            mainContainer.setCenter(view);
            System.out.println("Successfully loaded view: " + fxmlPath);

        } catch (Exception e) {
            System.err.println("CRITICAL ERROR loading view: " + fxmlPath + " - " + e.getMessage());
            e.printStackTrace();

            // Don't let this crash the application
            try {
                showSafeAlert("View Loading Error",
                    "Failed to load " + fxmlPath + "\n\n" +
                    "Error: " + e.getMessage() + "\n\n" +
                    "The application will continue running. Try a different action or restart if problems persist.");
            } catch (Exception alertError) {
                System.err.println("Failed to show error alert: " + alertError.getMessage());
            }
        }
    }

    /**
     * Safe quick action handler to prevent UI freezing
     */
    private void safeQuickAction(String actionName, Runnable action) {
        // Prevent multiple quick actions from running simultaneously
        if (backgroundTaskExecutor == null || backgroundTaskExecutor.isShutdown()) {
            System.err.println("Background executor not available for quick action: " + actionName);
            showSafeAlert("Error", "Quick action temporarily unavailable. Please try again.");
            return;
        }

        // Run the action in background to prevent UI blocking
        backgroundTaskExecutor.submit(() -> {
            try {
                System.out.println("Executing quick action: " + actionName);
                Platform.runLater(() -> {
                    try {
                        action.run();
                        System.out.println("Quick action completed: " + actionName);
                    } catch (Exception e) {
                        System.err.println("Error in quick action " + actionName + ": " + e.getMessage());
                        showSafeAlert("Error", "Failed to " + actionName.toLowerCase() + ": " + e.getMessage());
                    }
                });
            } catch (Exception e) {
                System.err.println("Background error in quick action " + actionName + ": " + e.getMessage());
                Platform.runLater(() -> showSafeAlert("Error", "Quick action failed: " + e.getMessage()));
            }
        });
    }

    // Simple Quick Action Methods - Just 4 buttons as requested
    @FXML
    private void quickManageStaff() {
        safeQuickAction("Manage Staff", this::loadUserManagement);
    }

    @FXML
    private void quickMenuManagement() {
        safeQuickAction("Menu Management", this::loadMenuManagement);
    }

    @FXML
    private void quickInventoryCheck() {
        safeQuickAction("Inventory Check", this::loadInventoryManagement);
    }

    @FXML
    private void quickSeeTables() {
        safeQuickAction("See Tables", () -> loadView("/fxml/MinimalTableManagement.fxml"));
    }

    private void configureAllScrollPanes() {
        // Configure all scroll panes using the manager
        Platform.runLater(() -> {
            if (mainContainer != null) {
                ScrollPaneManager.getInstance().configureAllScrollPanes(mainContainer);
                System.out.println("Dashboard scroll panes configured with ScrollPaneManager");
            }
        });
    }

    private void showQuickActionDialog(String title, String message) {
        // Use smart notification instead of blocking dialog
        SmartNotificationManager.getInstance().showInfo(title, message);
    }
    
    @FXML
    private void loadFinishList() {
        try {
            System.out.println("Loading finish list...");
            if (currentUser == null) {
                System.err.println("currentUser is null in loadFinishList");
                showSafeAlert("Error", "User session not found. Please log in again.");
                return;
            }
            if (!currentUser.isAdmin()) {
                showSafeAlert("Access Denied", "Only administrators can access the finish list.");
                return;
            }

            if (mainContainer == null) {
                System.err.println("mainContainer is null in loadFinishList");
                showSafeAlert("Error", "UI not properly initialized. Please restart the application.");
                return;
            }

            Parent view = FXMLLoader.load(getClass().getResource("/fxml/FinishListPanel.fxml"));
            mainContainer.setCenter(view);
            System.out.println("Finish list loaded successfully");
        } catch (Exception e) {
            System.err.println("Error in loadFinishList: " + e.getMessage());
            e.printStackTrace();
            showSafeAlert("Error", "Failed to load finish list: " + e.getMessage());
        }
    }

    @FXML
    private void loadMenuManagement() {
        try {
            System.out.println("Loading menu management...");
            if (currentUser == null) {
                System.err.println("currentUser is null in loadMenuManagement");
                showSafeAlert("Error", "User session not found. Please log in again.");
                return;
            }
            if (!currentUser.isAdmin()) {
                showSafeAlert("Access Denied", "Only administrators can access menu management.");
                return;
            }

            if (mainContainer == null) {
                System.err.println("mainContainer is null in loadMenuManagement");
                showSafeAlert("Error", "UI not properly initialized. Please restart the application.");
                return;
            }

            Parent view = FXMLLoader.load(getClass().getResource("/fxml/MenuManagement.fxml"));
            mainContainer.setCenter(view);
            System.out.println("Menu management loaded successfully");
        } catch (Exception e) {
            System.err.println("Error in loadMenuManagement: " + e.getMessage());
            e.printStackTrace();
            showSafeAlert("Error", "Failed to load menu management: " + e.getMessage());
        }
    }

    private void loadInventoryManagement() {
        try {
            System.out.println("Loading inventory management...");

            if (mainContainer == null) {
                System.err.println("mainContainer is null in loadInventoryManagement");
                showSafeAlert("Error", "UI not properly initialized. Please restart the application.");
                return;
            }

            Parent inventoryView = FXMLLoader.load(getClass().getResource("/fxml/InventoryManagement.fxml"));

            if (mainContainer.getScene() == null) {
                System.err.println("mainContainer scene is null in loadInventoryManagement");
                showSafeAlert("Error", "UI scene not found. Please restart the application.");
                return;
            }

            Stage stage = (Stage) mainContainer.getScene().getWindow();
            if (stage == null) {
                System.err.println("stage is null in loadInventoryManagement");
                showSafeAlert("Error", "Application window not found. Please restart the application.");
                return;
            }

            stage.getScene().setRoot(inventoryView);
            System.out.println("Inventory management loaded successfully");
        } catch (Exception e) {
            System.err.println("Error in loadInventoryManagement: " + e.getMessage());
            e.printStackTrace();
            showSafeAlert("Error", "Failed to load inventory management: " + e.getMessage());
        }
    }

    @FXML
    private void loadRecipeManagement() {
        try {
            System.out.println("Loading recipe management...");

            if (mainContainer == null) {
                System.err.println("mainContainer is null in loadRecipeManagement");
                showSafeAlert("Error", "UI not properly initialized. Please restart the application.");
                return;
            }

            Parent recipeView = FXMLLoader.load(getClass().getResource("/fxml/RecipeManagement.fxml"));

            if (mainContainer.getScene() == null) {
                System.err.println("mainContainer scene is null in loadRecipeManagement");
                showSafeAlert("Error", "UI scene not found. Please restart the application.");
                return;
            }

            Stage stage = (Stage) mainContainer.getScene().getWindow();
            if (stage == null) {
                System.err.println("stage is null in loadRecipeManagement");
                showSafeAlert("Error", "Application window not found. Please restart the application.");
                return;
            }

            stage.getScene().setRoot(recipeView);
            System.out.println("Recipe management loaded successfully");
        } catch (Exception e) {
            System.err.println("Error in loadRecipeManagement: " + e.getMessage());
            e.printStackTrace();
            showSafeAlert("Error", "Failed to load recipe management: " + e.getMessage());
        }
    }

    @FXML
    private void loadPurchaseOrderRequest() {
        try {
            System.out.println("Loading purchase order request...");

            if (mainContainer == null) {
                System.err.println("mainContainer is null in loadPurchaseOrderRequest");
                showSafeAlert("Error", "UI not properly initialized. Please restart the application.");
                return;
            }

            Parent purchaseOrderView = FXMLLoader.load(getClass().getResource("/fxml/PurchaseOrderRequest.fxml"));

            if (mainContainer.getScene() == null) {
                System.err.println("mainContainer scene is null in loadPurchaseOrderRequest");
                showSafeAlert("Error", "UI scene not found. Please restart the application.");
                return;
            }

            Stage stage = (Stage) mainContainer.getScene().getWindow();
            if (stage == null) {
                System.err.println("stage is null in loadPurchaseOrderRequest");
                showSafeAlert("Error", "Application window not found. Please restart the application.");
                return;
            }

            stage.getScene().setRoot(purchaseOrderView);
            System.out.println("Purchase order request loaded successfully");
        } catch (Exception e) {
            System.err.println("Error in loadPurchaseOrderRequest: " + e.getMessage());
            e.printStackTrace();
            showSafeAlert("Error", "Failed to load purchase order request: " + e.getMessage());
        }
    }

    @FXML
    private void loadCustomerCRM() {
        try {
            System.out.println("Loading customer CRM...");

            if (mainContainer == null) {
                System.err.println("mainContainer is null in loadCustomerCRM");
                showSafeAlert("Error", "UI not properly initialized. Please restart the application.");
                return;
            }

            Parent crmView = FXMLLoader.load(getClass().getResource("/fxml/CustomerCRM.fxml"));

            if (mainContainer.getScene() == null) {
                System.err.println("mainContainer scene is null in loadCustomerCRM");
                showSafeAlert("Error", "UI scene not found. Please restart the application.");
                return;
            }

            Stage stage = (Stage) mainContainer.getScene().getWindow();
            if (stage == null) {
                System.err.println("stage is null in loadCustomerCRM");
                showSafeAlert("Error", "Application window not found. Please restart the application.");
                return;
            }

            stage.getScene().setRoot(crmView);
            System.out.println("Customer CRM loaded successfully");
        } catch (Exception e) {
            System.err.println("Error in loadCustomerCRM: " + e.getMessage());
            e.printStackTrace();
            showSafeAlert("Error", "Failed to load customer CRM: " + e.getMessage());
        }
    }

    @FXML
    private void loadReports() {
        try {
            Parent view = FXMLLoader.load(getClass().getResource("/fxml/Reports.fxml"));
            mainContainer.setCenter(view);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @FXML
    private void goToHome() {
        try {
            System.out.println("Navigating to home...");
            // Navigate to dashboard home
            loadDashboardHome();
        } catch (Exception e) {
            System.err.println("Error in goToHome: " + e.getMessage());
            e.printStackTrace();
            showSafeAlert("Error", "Failed to navigate to home: " + e.getMessage());
        }
    }

    @FXML
    private void showNotifications() {
        try {
            System.out.println("Refreshing notifications...");
            // Since panel is always visible, just ensure it's showing and refresh
            if (notificationPanelContainer != null) {
                notificationPanelContainer.setVisible(true);
                notificationPanelContainer.setManaged(true);
                System.out.println("Notification panel refreshed");
            } else {
                System.err.println("notificationPanelContainer is null");
                showSafeAlert("Error", "Notification panel not found. Please restart the application.");
            }
        } catch (Exception e) {
            System.err.println("Error in showNotifications: " + e.getMessage());
            e.printStackTrace();
            showSafeAlert("Error", "Failed to show notifications: " + e.getMessage());
        }
    }

    private void loadNotificationPanel() {
        try {
            System.out.println("Loading notification panel FXML...");
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/NotificationPanel.fxml"));
            Parent notificationPanel = loader.load();

            if (notificationPanelContainer != null) {
                notificationPanelContainer.getChildren().clear();
                notificationPanelContainer.getChildren().add(notificationPanel);

                // Set the notification panel to grow and fill the container
                VBox.setVgrow(notificationPanel, javafx.scene.layout.Priority.ALWAYS);

                // Set explicit height constraints to ensure full height
                if (notificationPanel instanceof javafx.scene.layout.Region) {
                    javafx.scene.layout.Region region = (javafx.scene.layout.Region) notificationPanel;
                    region.prefHeightProperty().bind(notificationPanelContainer.heightProperty());
                    region.minHeightProperty().bind(notificationPanelContainer.heightProperty());
                }

                // Always show the notification panel (make it always visible)
                notificationPanelContainer.setVisible(true);
                notificationPanelContainer.setManaged(true);

                System.out.println("Notification panel loaded successfully and set to always visible");
            } else {
                System.err.println("notificationPanelContainer is null");
            }
        } catch (IOException e) {
            System.err.println("Error loading notification panel: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @FXML
    private void logout() {
        try {
            Parent loginView = FXMLLoader.load(getClass().getResource("/fxml/Login.fxml"));
            mainContainer.getScene().setRoot(loginView);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // ActivityRecord class removed - now using Activity model from com.restaurant.model.Activity

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showNotificationsDialog() {
        // Create a comprehensive notifications dialog
        Alert notificationAlert = new Alert(Alert.AlertType.INFORMATION);
        notificationAlert.setTitle("🔔 Notifications");
        notificationAlert.setHeaderText("Restaurant Notifications & Alerts");

        // Build notification content
        StringBuilder notifications = new StringBuilder();
        notifications.append("📋 Recent Activity:\n");
        notifications.append("• New order received for Table 5\n");
        notifications.append("• Kitchen completed order #1234\n");
        notifications.append("• Table 3 requested bill\n\n");

        notifications.append("⚠️ System Alerts:\n");
        notifications.append("• Low stock: Chicken (5 kg remaining)\n");
        notifications.append("• Table 7 waiting for 15+ minutes\n");
        notifications.append("• Daily backup completed successfully\n\n");

        notifications.append("📊 Today's Summary:\n");
        notifications.append("• Total Orders: 25\n");
        notifications.append("• Revenue: ₹12,450.00\n");
        notifications.append("• Active Tables: 8/20\n");

        notificationAlert.setContentText(notifications.toString());
        notificationAlert.showAndWait();
    }

    // AI-Powered Feature Methods

    @FXML
    private void openAIForecaster() {
        try {
            System.out.println("Opening AI Forecaster...");
            showAIForecasterDialog();
        } catch (Exception e) {
            System.err.println("Error in openAIForecaster: " + e.getMessage());
            e.printStackTrace();
            showSafeAlert("Error", "Failed to open AI Forecaster: " + e.getMessage());
        }
    }



    private void showAIForecasterDialog() {
        try {
            // Load the AI Forecaster FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/AIForecasterSimple.fxml"));
            Parent forecasterRoot = loader.load();

            // Create new scene and stage
            Scene forecasterScene = new Scene(forecasterRoot);
            Stage forecasterStage = new Stage();
            forecasterStage.setTitle("AI Sales Forecaster - WOK KA TADKA");
            forecasterStage.setScene(forecasterScene);
            forecasterStage.setMaximized(true);
            forecasterStage.initModality(Modality.APPLICATION_MODAL);

            // Show the forecaster window
            forecasterStage.showAndWait();

        } catch (Exception e) {
            e.printStackTrace();
            // Fallback to simple alert if FXML loading fails
            Alert errorAlert = new Alert(Alert.AlertType.ERROR);
            errorAlert.setTitle("Error");
            errorAlert.setHeaderText("Unable to open AI Forecaster");
            errorAlert.setContentText("There was an error loading the AI Forecaster interface: " + e.getMessage());
            errorAlert.showAndWait();
        }
    }







    /**
     * Demo method to test smart notifications
     */
    public void demoSmartNotifications() {
        SmartNotificationManager manager = SmartNotificationManager.getInstance();

        // Demo different notification types
        manager.showSuccess("Order Complete", "Order #1234 has been completed successfully!");

        Platform.runLater(() -> {
            try { Thread.sleep(1000); } catch (InterruptedException e) {}
            manager.showInfo("Table Update", "Table 5 is now available for seating");
        });

        Platform.runLater(() -> {
            try { Thread.sleep(2000); } catch (InterruptedException e) {}
            manager.showWarning("Low Stock", "Chicken Biryani is running low (3 portions left)");
        });

        Platform.runLater(() -> {
            try { Thread.sleep(3000); } catch (InterruptedException e) {}
            manager.showOrderNotification("SWIGGY", "SW12345", "John Doe", 450.75);
        });

        Platform.runLater(() -> {
            try { Thread.sleep(4000); } catch (InterruptedException e) {}
            manager.showOrderNotification("ZOMATO", "ZM67890", "Jane Smith", 320.50);
        });
    }

    @FXML
    private void openSmartAIAssistant() {
        System.out.println("Opening Smart AI Assistant...");
        showSmartAIAssistantDialog();
    }

    private void showSmartAIAssistantDialog() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/SmartAIAssistant.fxml"));
            Parent root = loader.load();

            Stage stage = new Stage();
            stage.setTitle("🤖 Smart AI Assistant");
            stage.setScene(new Scene(root, 900, 700));
            stage.initModality(Modality.APPLICATION_MODAL);
            stage.setResizable(true);
            stage.show();

        } catch (Exception e) {
            e.printStackTrace();
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Error");
            alert.setHeaderText("Unable to open Smart AI Assistant");
            alert.setContentText("There was an error loading the Smart AI Assistant interface:\n" + e.getMessage());
            alert.showAndWait();
        }
    }

}