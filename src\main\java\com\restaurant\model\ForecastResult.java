package com.restaurant.model;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * Model class representing the result of an AI forecast analysis
 */
public class ForecastResult {
    private List<SalesData> historicalData;
    private List<SalesData> forecastData;
    private double predictedSales;
    private double growthPercentage;
    private double confidenceLevel;
    private String topCategory;
    private double topCategoryPercent;
    private LocalDate forecastStartDate;
    private LocalDate forecastEndDate;
    private Map<String, Double> categoryBreakdown;
    private Map<String, Double> channelBreakdown;
    private String forecastSummary;
    private List<String> insights;
    private double upperConfidenceBound;
    private double lowerConfidenceBound;

    // Constructors
    public ForecastResult() {}

    public ForecastResult(List<SalesData> historicalData, List<SalesData> forecastData) {
        this.historicalData = historicalData;
        this.forecastData = forecastData;
    }

    // Getters and Setters
    public List<SalesData> getHistoricalData() {
        return historicalData;
    }

    public void setHistoricalData(List<SalesData> historicalData) {
        this.historicalData = historicalData;
    }

    public List<SalesData> getForecastData() {
        return forecastData;
    }

    public void setForecastData(List<SalesData> forecastData) {
        this.forecastData = forecastData;
    }

    public double getPredictedSales() {
        return predictedSales;
    }

    public void setPredictedSales(double predictedSales) {
        this.predictedSales = predictedSales;
    }

    public double getGrowthPercentage() {
        return growthPercentage;
    }

    public void setGrowthPercentage(double growthPercentage) {
        this.growthPercentage = growthPercentage;
    }

    public double getConfidenceLevel() {
        return confidenceLevel;
    }

    public void setConfidenceLevel(double confidenceLevel) {
        this.confidenceLevel = confidenceLevel;
    }

    public String getTopCategory() {
        return topCategory;
    }

    public void setTopCategory(String topCategory) {
        this.topCategory = topCategory;
    }

    public double getTopCategoryPercent() {
        return topCategoryPercent;
    }

    public void setTopCategoryPercent(double topCategoryPercent) {
        this.topCategoryPercent = topCategoryPercent;
    }

    public LocalDate getForecastStartDate() {
        return forecastStartDate;
    }

    public void setForecastStartDate(LocalDate forecastStartDate) {
        this.forecastStartDate = forecastStartDate;
    }

    public LocalDate getForecastEndDate() {
        return forecastEndDate;
    }

    public void setForecastEndDate(LocalDate forecastEndDate) {
        this.forecastEndDate = forecastEndDate;
    }

    public Map<String, Double> getCategoryBreakdown() {
        return categoryBreakdown;
    }

    public void setCategoryBreakdown(Map<String, Double> categoryBreakdown) {
        this.categoryBreakdown = categoryBreakdown;
    }

    public Map<String, Double> getChannelBreakdown() {
        return channelBreakdown;
    }

    public void setChannelBreakdown(Map<String, Double> channelBreakdown) {
        this.channelBreakdown = channelBreakdown;
    }

    public String getForecastSummary() {
        return forecastSummary;
    }

    public void setForecastSummary(String forecastSummary) {
        this.forecastSummary = forecastSummary;
    }

    public List<String> getInsights() {
        return insights;
    }

    public void setInsights(List<String> insights) {
        this.insights = insights;
    }

    public double getUpperConfidenceBound() {
        return upperConfidenceBound;
    }

    public void setUpperConfidenceBound(double upperConfidenceBound) {
        this.upperConfidenceBound = upperConfidenceBound;
    }

    public double getLowerConfidenceBound() {
        return lowerConfidenceBound;
    }

    public void setLowerConfidenceBound(double lowerConfidenceBound) {
        this.lowerConfidenceBound = lowerConfidenceBound;
    }

    /**
     * Calculate total forecast sales
     */
    public double getTotalForecastSales() {
        return forecastData != null ? 
            forecastData.stream().mapToDouble(SalesData::getSales).sum() : 0.0;
    }

    /**
     * Calculate average daily forecast sales
     */
    public double getAverageDailyForecastSales() {
        return forecastData != null && !forecastData.isEmpty() ? 
            getTotalForecastSales() / forecastData.size() : 0.0;
    }

    /**
     * Get forecast period in days
     */
    public int getForecastPeriodDays() {
        return forecastData != null ? forecastData.size() : 0;
    }

    @Override
    public String toString() {
        return "ForecastResult{" +
                "predictedSales=" + predictedSales +
                ", growthPercentage=" + growthPercentage +
                ", confidenceLevel=" + confidenceLevel +
                ", topCategory='" + topCategory + '\'' +
                ", forecastPeriodDays=" + getForecastPeriodDays() +
                '}';
    }
}
