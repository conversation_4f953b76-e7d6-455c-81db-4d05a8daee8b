package com.restaurant.model;

import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Data Access Object for Purchase Orders
 */
public class PurchaseOrderDAO {

    /**
     * Get all purchase orders
     */
    public static List<PurchaseOrder> getAllPurchaseOrders() {
        List<PurchaseOrder> orders = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(
                     "SELECT po.*, s.name as supplier_name " +
                     "FROM purchase_orders po " +
                     "LEFT JOIN suppliers s ON po.supplier_id = s.id " +
                     "ORDER BY po.created_at DESC")) {
            
            while (rs.next()) {
                PurchaseOrder order = createPurchaseOrderFromResultSet(rs);
                orders.add(order);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return orders;
    }

    /**
     * Get purchase order by ID
     */
    public static PurchaseOrder getPurchaseOrderById(int id) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT po.*, s.name as supplier_name " +
                     "FROM purchase_orders po " +
                     "LEFT JOIN suppliers s ON po.supplier_id = s.id " +
                     "WHERE po.id = ?")) {
            
            ps.setInt(1, id);
            ResultSet rs = ps.executeQuery();
            
            if (rs.next()) {
                return createPurchaseOrderFromResultSet(rs);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * Add new purchase order
     */
    public static boolean addPurchaseOrder(PurchaseOrder order) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "INSERT INTO purchase_orders (request_number, to_location, item, quantity, status, " +
                     "start_date, end_date, created_by, notes, unit_price, total_amount, supplier_id, " +
                     "created_at, last_updated) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", 
                     Statement.RETURN_GENERATED_KEYS)) {
            
            ps.setString(1, order.getRequestNumber());
            ps.setString(2, order.getToLocation());
            ps.setString(3, order.getItem());
            ps.setString(4, order.getQuantity());
            ps.setString(5, order.getStatus());
            ps.setDate(6, order.getStartDate() != null ? Date.valueOf(order.getStartDate()) : null);
            ps.setDate(7, order.getEndDate() != null ? Date.valueOf(order.getEndDate()) : null);
            ps.setString(8, order.getCreatedBy());
            ps.setString(9, order.getNotes());
            ps.setDouble(10, order.getUnitPrice());
            ps.setDouble(11, order.getTotalAmount());
            ps.setInt(12, order.getSupplierId());
            ps.setTimestamp(13, Timestamp.valueOf(order.getCreatedAt()));
            ps.setTimestamp(14, Timestamp.valueOf(order.getLastUpdated()));
            
            int result = ps.executeUpdate();
            
            if (result > 0) {
                ResultSet rs = ps.getGeneratedKeys();
                if (rs.next()) {
                    order.setId(rs.getInt(1));
                }
                return true;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * Update purchase order
     */
    public static boolean updatePurchaseOrder(PurchaseOrder order) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "UPDATE purchase_orders SET request_number = ?, to_location = ?, item = ?, " +
                     "quantity = ?, status = ?, start_date = ?, end_date = ?, notes = ?, " +
                     "unit_price = ?, total_amount = ?, supplier_id = ?, last_updated = ? WHERE id = ?")) {
            
            ps.setString(1, order.getRequestNumber());
            ps.setString(2, order.getToLocation());
            ps.setString(3, order.getItem());
            ps.setString(4, order.getQuantity());
            ps.setString(5, order.getStatus());
            ps.setDate(6, order.getStartDate() != null ? Date.valueOf(order.getStartDate()) : null);
            ps.setDate(7, order.getEndDate() != null ? Date.valueOf(order.getEndDate()) : null);
            ps.setString(8, order.getNotes());
            ps.setDouble(9, order.getUnitPrice());
            ps.setDouble(10, order.getTotalAmount());
            ps.setInt(11, order.getSupplierId());
            ps.setTimestamp(12, Timestamp.valueOf(LocalDateTime.now()));
            ps.setInt(13, order.getId());
            
            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * Delete purchase order
     */
    public static boolean deletePurchaseOrder(int id) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("DELETE FROM purchase_orders WHERE id = ?")) {
            
            ps.setInt(1, id);
            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * Get purchase orders by status
     */
    public static List<PurchaseOrder> getPurchaseOrdersByStatus(String status) {
        List<PurchaseOrder> orders = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT po.*, s.name as supplier_name " +
                     "FROM purchase_orders po " +
                     "LEFT JOIN suppliers s ON po.supplier_id = s.id " +
                     "WHERE po.status = ? ORDER BY po.created_at DESC")) {
            
            ps.setString(1, status);
            ResultSet rs = ps.executeQuery();
            
            while (rs.next()) {
                PurchaseOrder order = createPurchaseOrderFromResultSet(rs);
                orders.add(order);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return orders;
    }

    /**
     * Get purchase orders by date range
     */
    public static List<PurchaseOrder> getPurchaseOrdersByDateRange(LocalDate startDate, LocalDate endDate) {
        List<PurchaseOrder> orders = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT po.*, s.name as supplier_name " +
                     "FROM purchase_orders po " +
                     "LEFT JOIN suppliers s ON po.supplier_id = s.id " +
                     "WHERE po.start_date >= ? AND po.end_date <= ? " +
                     "ORDER BY po.created_at DESC")) {
            
            ps.setDate(1, Date.valueOf(startDate));
            ps.setDate(2, Date.valueOf(endDate));
            ResultSet rs = ps.executeQuery();
            
            while (rs.next()) {
                PurchaseOrder order = createPurchaseOrderFromResultSet(rs);
                orders.add(order);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return orders;
    }

    /**
     * Generate next request number
     */
    public static String generateNextRequestNumber() {
        try (Connection conn = DatabaseManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(
                     "SELECT MAX(CAST(SUBSTR(request_number, 2) AS INTEGER)) as max_num " +
                     "FROM purchase_orders WHERE request_number LIKE 'P%'")) {
            
            if (rs.next()) {
                int maxNum = rs.getInt("max_num");
                return String.format("P%05d", maxNum + 1);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return "P00001";
    }

    /**
     * Helper method to create PurchaseOrder from ResultSet
     */
    private static PurchaseOrder createPurchaseOrderFromResultSet(ResultSet rs) throws SQLException {
        PurchaseOrder order = new PurchaseOrder(
            rs.getInt("id"),
            rs.getString("request_number"),
            rs.getString("to_location"),
            rs.getString("item"),
            rs.getString("quantity"),
            rs.getString("status")
        );
        
        Date startDate = rs.getDate("start_date");
        if (startDate != null) order.setStartDate(startDate.toLocalDate());
        
        Date endDate = rs.getDate("end_date");
        if (endDate != null) order.setEndDate(endDate.toLocalDate());
        
        order.setCreatedBy(rs.getString("created_by"));
        order.setNotes(rs.getString("notes"));
        order.setUnitPrice(rs.getDouble("unit_price"));
        order.setTotalAmount(rs.getDouble("total_amount"));
        order.setSupplierId(rs.getInt("supplier_id"));
        order.setSupplierName(rs.getString("supplier_name"));
        order.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
        order.setLastUpdated(rs.getTimestamp("last_updated").toLocalDateTime());
        
        return order;
    }
}
