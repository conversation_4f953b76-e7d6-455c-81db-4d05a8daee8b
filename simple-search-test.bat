@echo off
echo SIMPLE DISH SEARCH TEST...
echo.

echo WHAT TO TEST:
echo 1. Go to Table Management
echo 2. Look for search field in header
echo 3. Type "chicken" slowly
echo 4. Watch for results below search field
echo.

echo EXPECTED:
echo ✅ Search field visible with placeholder text
echo ✅ Results appear after typing 2+ characters
echo ✅ Dish cards show with name, price, category
echo.

java -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\pdfbox\2.0.27\pdfbox-2.0.27.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\fontbox\2.0.27\fontbox-2.0.27.jar;%USERPROFILE%\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar" --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar" --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics com.restaurant.RestaurantApp

echo.
echo TEST RESULTS:
echo.
echo Did you see the search field? (Y/N)
echo Did typing show results? (Y/N)
echo Did results show dish cards? (Y/N)
echo.
echo If NO to any above, run debug-dish-search.bat for detailed analysis
echo.
pause
