package com.restaurant.controller;

import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Parent;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import java.net.URL;
import java.util.ResourceBundle;

public class SimpleTableManagementController implements Initializable {

    @FXML private GridPane tablesGrid;
    @FXML private VBox table1, table2, table3, table4, table5, table6, table7, table8, table9;
    @FXML private Label table1Status, table2Status, table3Status, table4Status, table5Status, 
                        table6Status, table7Status, table8Status, table9Status;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("SimpleTableManagementController.initialize() called");
        
        // Ensure all tables are visible
        setupTableVisibility();
        
        // Initialize table statuses
        initializeTableStatuses();
        
        System.out.println("SimpleTableManagementController initialization complete");
    }
    
    private void setupTableVisibility() {
        System.out.println("Setting up table visibility...");
        
        // Make sure GridPane is visible
        if (tablesGrid != null) {
            tablesGrid.setVisible(true);
            tablesGrid.setManaged(true);
            System.out.println("GridPane visibility set to true");
        }
        
        // Make sure all table cards are visible
        VBox[] tables = {table1, table2, table3, table4, table5, table6, table7, table8, table9};
        for (int i = 0; i < tables.length; i++) {
            if (tables[i] != null) {
                tables[i].setVisible(true);
                tables[i].setManaged(true);
                System.out.println("Table " + (i + 1) + " visibility set to true");
            }
        }
    }
    
    private void initializeTableStatuses() {
        System.out.println("Initializing table statuses...");
        
        // Set initial status for each table
        if (table1Status != null) table1Status.setText("Available");
        if (table2Status != null) table2Status.setText("Order Pending");
        if (table3Status != null) table3Status.setText("Available");
        if (table4Status != null) table4Status.setText("Ready to Serve");
        if (table5Status != null) table5Status.setText("Preparing");
        if (table6Status != null) table6Status.setText("Available");
        if (table7Status != null) table7Status.setText("KOT Printed");
        if (table8Status != null) table8Status.setText("Available");
        if (table9Status != null) table9Status.setText("Completed");
        
        System.out.println("Table statuses initialized");
    }

    @FXML
    private void goBack() {
        System.out.println("Going back to dashboard...");
        try {
            // Load the dashboard FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/Dashboard.fxml"));
            Parent dashboardView = loader.load();

            // Get the current stage and set the new scene
            Stage stage = (Stage) tablesGrid.getScene().getWindow();
            stage.getScene().setRoot(dashboardView);
            
            System.out.println("Successfully navigated back to dashboard");
        } catch (Exception e) {
            System.err.println("Error navigating back to dashboard: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @FXML
    private void refreshTables() {
        System.out.println("Refreshing tables...");
        
        // Re-initialize table statuses
        initializeTableStatuses();
        
        // Ensure visibility
        setupTableVisibility();
        
        showAlert("Refresh", "Tables refreshed successfully!");
    }

    @FXML
    private void viewTableDetails() {
        System.out.println("View table details called");
        showAlert("Table Details", "Table details functionality will be implemented here.");
    }

    @FXML
    private void selectTable() {
        System.out.println("Select table called");
        showAlert("Table Selected", "Table selection functionality will be implemented here.");
    }

    @FXML
    private void addItemsToTable() {
        System.out.println("Add items to table called");
        showAlert("Add Items", "Add items functionality will be implemented here.");
    }

    // Handle individual table button clicks
    @FXML
    private void handleTable1View() {
        showTableDetails("Table 1", "Available", "No active orders");
    }

    @FXML
    private void handleTable1Select() {
        showAlert("Table 1", "Table 1 selected for new order");
    }

    @FXML
    private void handleTable2View() {
        showTableDetails("Table 2", "Order Pending", "Order #1001\nAmount: ₹450\nItems: 3");
    }

    @FXML
    private void handleTable2Add() {
        showAlert("Table 2", "Adding items to Table 2 (Order #1001)");
    }

    @FXML
    private void handleTable3View() {
        showTableDetails("Table 3", "Available", "No active orders");
    }

    @FXML
    private void handleTable3Select() {
        showAlert("Table 3", "Table 3 selected for new order");
    }

    @FXML
    private void handleTable4View() {
        showTableDetails("Table 4", "Ready to Serve", "Order #1002\nAmount: ₹680\nItems: 5\nReady for serving");
    }

    @FXML
    private void handleTable4Add() {
        showAlert("Table 4", "Adding items to Table 4 (Order #1002)");
    }

    @FXML
    private void handleTable5View() {
        showTableDetails("Table 5", "Preparing", "Order #1003\nAmount: ₹320\nItems: 2\nEstimated time: 15 mins");
    }

    @FXML
    private void handleTable5Add() {
        showAlert("Table 5", "Adding items to Table 5 (Order #1003)");
    }

    @FXML
    private void handleTable6View() {
        showTableDetails("Table 6", "Available", "No active orders");
    }

    @FXML
    private void handleTable6Select() {
        showAlert("Table 6", "Table 6 selected for new order");
    }

    @FXML
    private void handleTable7View() {
        showTableDetails("Table 7", "KOT Printed", "Order #1004\nAmount: ₹520\nItems: 4\nKOT sent to kitchen");
    }

    @FXML
    private void handleTable7Add() {
        showAlert("Table 7", "Adding items to Table 7 (Order #1004)");
    }

    @FXML
    private void handleTable8View() {
        showTableDetails("Table 8", "Available", "No active orders");
    }

    @FXML
    private void handleTable8Select() {
        showAlert("Table 8", "Table 8 selected for new order");
    }

    @FXML
    private void handleTable9View() {
        showTableDetails("Table 9", "Completed", "Order #1005\nAmount: ₹750\nItems: 6\nReady for billing");
    }

    @FXML
    private void handleTable9Add() {
        showAlert("Table 9", "Adding items to Table 9 (Order #1005)");
    }

    private void showTableDetails(String tableName, String status, String details) {
        StringBuilder message = new StringBuilder();
        message.append(tableName).append(" Details\n\n");
        message.append("Status: ").append(status).append("\n");
        message.append("Seats: 4\n\n");
        message.append(details);
        
        showAlert("Table Details", message.toString());
    }

    private void showAlert(String title, String message) {
        try {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        } catch (Exception e) {
            System.err.println("Error showing alert: " + e.getMessage());
        }
    }
}
