package com.restaurant.controller;

import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.application.Platform;
import javafx.scene.Parent;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.TextField;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.VBox;
import javafx.scene.layout.HBox;
import javafx.scene.layout.GridPane;
import javafx.geometry.Pos;
import javafx.stage.Stage;
import com.restaurant.service.OrderManager;
import com.restaurant.util.UniversalNavigationManager;

import java.net.URL;
import java.util.ResourceBundle;

public class MinimalTableController implements Initializable {

    @FXML private GridPane tablesGrid;
    @FXML private Label statusLabel;

    private TextField searchField;
    private boolean isSearchMode = false;
    private boolean searchActivatedByCtrlS = false; // Track if search was activated by Ctrl+S

    private OrderManager orderManager = OrderManager.getInstance();
    private java.util.Set<Integer> existingTables = new java.util.HashSet<>();
    private StringBuilder tableNumberBuffer = new StringBuilder();
    private int selectedTableNumber = 0;
    private VBox selectedTableCard = null;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("MinimalTableController initialized successfully!");

        // Initialize existing tables (1-9 from FXML)
        for (int i = 1; i <= 9; i++) {
            existingTables.add(i);
        }

        // Set up keyboard navigation
        setupKeyboardNavigation();

        // Register with universal navigation manager
        UniversalNavigationManager.getInstance().setCurrentController("MinimalTableController");

        // Register global ESC handler when scene is available
        Platform.runLater(() -> {
            if (tablesGrid != null && tablesGrid.getScene() != null) {
                UniversalNavigationManager.getInstance().registerGlobalEscHandler(tablesGrid.getScene());
                System.out.println("MinimalTableController: Global ESC handler registered");
            }
        });
    }

    /**
     * Setup keyboard navigation for table selection
     */
    private void setupKeyboardNavigation() {
        // Make the grid focusable
        tablesGrid.setFocusTraversable(true);

        // Add keyboard event handler
        tablesGrid.setOnKeyPressed(event -> {
            System.out.println("Key pressed: " + event.getCode());

            switch (event.getCode()) {
                case DIGIT1: case NUMPAD1:
                    handleNumberKey(1);
                    break;
                case DIGIT2: case NUMPAD2:
                    handleNumberKey(2);
                    break;
                case DIGIT3: case NUMPAD3:
                    handleNumberKey(3);
                    break;
                case DIGIT4: case NUMPAD4:
                    handleNumberKey(4);
                    break;
                case DIGIT5: case NUMPAD5:
                    handleNumberKey(5);
                    break;
                case DIGIT6: case NUMPAD6:
                    handleNumberKey(6);
                    break;
                case DIGIT7: case NUMPAD7:
                    handleNumberKey(7);
                    break;
                case DIGIT8: case NUMPAD8:
                    handleNumberKey(8);
                    break;
                case DIGIT9: case NUMPAD9:
                    handleNumberKey(9);
                    break;
                case DIGIT0: case NUMPAD0:
                    handleNumberKey(0);
                    break;
                case ENTER:
                    handleEnterKey();
                    break;
                case ESCAPE:
                    // Handle ESC key with proper logic
                    handleMainEscapeKey();
                    event.consume();
                    break;
                case BACK_SPACE:
                    handleBackspace();
                    break;
                case S:
                    if (event.isControlDown()) {
                        System.out.println("CTRL+S DETECTED in main keyboard handler");
                        handleSearchShortcut();
                        event.consume();
                        return;
                    }
                    break;
                default:
                    // Ignore other keys
                    break;
            }

            event.consume();
        });

        // Request focus when the scene is ready
        javafx.application.Platform.runLater(() -> {
            if (tablesGrid.getScene() != null) {
                tablesGrid.requestFocus();
            }
        });
    }

    /**
     * Handle number key press for table selection
     */
    private void handleNumberKey(int digit) {
        // Add digit to buffer
        tableNumberBuffer.append(digit);

        // Parse current number
        try {
            selectedTableNumber = Integer.parseInt(tableNumberBuffer.toString());
            System.out.println("Table number buffer: " + tableNumberBuffer.toString() + " -> " + selectedTableNumber);

            // Update visual selection
            updateTableSelection(selectedTableNumber);

            // Update status label
            updateStatusLabel();

        } catch (NumberFormatException e) {
            // Clear buffer if invalid
            clearSelection();
        }
    }

    /**
     * Handle Enter key press to open selected table
     */
    private void handleEnterKey() {
        if (selectedTableNumber > 0) {
            System.out.println("Opening table " + selectedTableNumber + " menu...");
            selectTable(selectedTableNumber);
        } else {
            System.out.println("No table selected");
            showAlert("No Selection", "Please select a table number first (e.g., press 1 for Table 1)");
        }
    }

    /**
     * Handle backspace to remove last digit
     */
    private void handleBackspace() {
        if (tableNumberBuffer.length() > 0) {
            tableNumberBuffer.deleteCharAt(tableNumberBuffer.length() - 1);

            if (tableNumberBuffer.length() > 0) {
                try {
                    selectedTableNumber = Integer.parseInt(tableNumberBuffer.toString());
                    updateTableSelection(selectedTableNumber);
                    updateStatusLabel();
                } catch (NumberFormatException e) {
                    clearSelection();
                }
            } else {
                clearSelection();
            }
        }
    }

    /**
     * Clear current selection
     */
    private void clearSelection() {
        tableNumberBuffer.setLength(0);
        selectedTableNumber = 0;

        // Remove visual selection
        if (selectedTableCard != null) {
            selectedTableCard.setStyle(selectedTableCard.getStyle().replace(
                "-fx-border-color: #007bff; -fx-border-width: 4px;",
                "-fx-border-color: #28a745; -fx-border-width: 3px;"
            ));
            selectedTableCard = null;
        }

        System.out.println("Selection cleared");
        updateStatusLabel();
    }

    /**
     * Update status label to show current selection
     */
    private void updateStatusLabel() {
        if (statusLabel != null) {
            if (selectedTableNumber > 0) {
                statusLabel.setText("Selected: Table " + selectedTableNumber + " (Press Enter to open)");
                statusLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #007bff; -fx-font-weight: bold;");
            } else if (tableNumberBuffer.length() > 0) {
                statusLabel.setText("Typing: " + tableNumberBuffer.toString() + "... (Press Enter when done)");
                statusLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #ffc107; -fx-font-weight: bold;");
            } else {
                statusLabel.setText("Type table number + Enter to open");
                statusLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #6c757d; -fx-font-weight: normal;");
            }
        }
    }

    /**
     * Update visual selection for table
     */
    private void updateTableSelection(int tableNumber) {
        // Clear previous selection
        if (selectedTableCard != null) {
            selectedTableCard.setStyle(selectedTableCard.getStyle().replace(
                "-fx-border-color: #007bff; -fx-border-width: 4px;",
                "-fx-border-color: #28a745; -fx-border-width: 3px;"
            ));
        }

        // Find and highlight new selection
        selectedTableCard = findTableCard(tableNumber);
        if (selectedTableCard != null) {
            selectedTableCard.setStyle(selectedTableCard.getStyle().replace(
                "-fx-border-color: #28a745; -fx-border-width: 3px;",
                "-fx-border-color: #007bff; -fx-border-width: 4px;"
            ));
            System.out.println("Table " + tableNumber + " selected visually");
        } else {
            System.out.println("Table " + tableNumber + " not found in current view");
        }
    }

    /**
     * Find table card by table number
     */
    private VBox findTableCard(int tableNumber) {
        // Search through grid children to find the table card
        for (javafx.scene.Node node : tablesGrid.getChildren()) {
            if (node instanceof VBox) {
                VBox card = (VBox) node;

                // Check if this card represents the target table
                for (javafx.scene.Node child : card.getChildren()) {
                    if (child instanceof Label) {
                        Label label = (Label) child;
                        String text = label.getText();

                        // Check if label contains "Table X" where X is our target number
                        if (text.contains("Table " + tableNumber)) {
                            return card;
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * Handle Ctrl+S search shortcut
     */
    private void handleSearchShortcut() {
        System.out.println("Search shortcut activated (Ctrl+S)");

        if (isSearchMode) {
            // If already in search mode, hide search and go back
            System.out.println("Already in search mode, hiding and going back");
            hideSearch();
            Platform.runLater(() -> {
                UniversalNavigationManager.getInstance().handleEscapeKey();
            });
        } else {
            searchActivatedByCtrlS = true; // Mark that search was activated by Ctrl+S
            System.out.println("Setting searchActivatedByCtrlS = true");
            showSearch();
        }
    }

    /**
     * Handle ESC key in search field with proper navigation logic
     */
    private void handleSearchFieldEscape() {
        System.out.println("ESC pressed in search field, searchActivatedByCtrlS=" + searchActivatedByCtrlS);

        if (isSearchMode) {
            // If search was activated by Ctrl+S and field is empty, go back immediately
            if (searchActivatedByCtrlS && (searchField == null || searchField.getText().trim().isEmpty())) {
                System.out.println("Search activated by Ctrl+S and empty, going back immediately");
                hideSearch();
                searchActivatedByCtrlS = false; // Reset flag

                // Immediately trigger global navigation
                Platform.runLater(() -> {
                    System.out.println("Triggering immediate global navigation");
                    UniversalNavigationManager.getInstance().handleEscapeKey();
                });
                return;
            }

            // If search has text, clear it first
            if (searchField != null && !searchField.getText().trim().isEmpty()) {
                System.out.println("Clearing search text");
                searchField.clear();
                return;
            }

            // If search is empty, hide search
            System.out.println("Hiding search mode");
            hideSearch();
            searchActivatedByCtrlS = false; // Reset flag

        } else {
            // If not in search mode, use global navigation
            System.out.println("Not in search mode, using global ESC navigation");
            UniversalNavigationManager.getInstance().handleEscapeKey();
        }
    }

    /**
     * Handle ESC key in main table view
     */
    private void handleMainEscapeKey() {
        System.out.println("ESC pressed in main table view, searchActivatedByCtrlS=" + searchActivatedByCtrlS);

        // If search mode is active, handle based on how it was activated
        if (isSearchMode) {
            if (searchActivatedByCtrlS) {
                System.out.println("Search activated by Ctrl+S, going back immediately");
                hideSearch();
                searchActivatedByCtrlS = false; // Reset flag

                // Immediately trigger global navigation
                Platform.runLater(() -> {
                    System.out.println("Triggering immediate global navigation from main handler");
                    UniversalNavigationManager.getInstance().handleEscapeKey();
                });
            } else {
                System.out.println("Search mode active, hiding search");
                hideSearch();
            }
        } else {
            // Use global navigation for back/dashboard functionality
            System.out.println("Using global ESC navigation");
            UniversalNavigationManager.getInstance().handleEscapeKey();
        }
    }

    /**
     * Show search overlay
     */
    private void showSearch() {
        try {
            isSearchMode = true;

            // Create search field if not exists
            if (searchField == null) {
                createSearchField();
            }

            // Add search field to the scene
            if (tablesGrid.getParent() instanceof VBox) {
                VBox parent = (VBox) tablesGrid.getParent();

                // Create search container
                HBox searchContainer = new HBox(10);
                searchContainer.setAlignment(Pos.CENTER);
                searchContainer.setStyle("-fx-background-color: rgba(0, 123, 255, 0.1); -fx-padding: 15;");

                Label searchLabel = new Label("🔍 Search Tables:");
                searchLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #007bff;");

                searchContainer.getChildren().addAll(searchLabel, searchField);

                // Insert search container after header (index 1)
                if (parent.getChildren().size() > 1) {
                    parent.getChildren().add(1, searchContainer);
                }
            }

            // Focus on search field
            javafx.application.Platform.runLater(() -> {
                searchField.requestFocus();
                searchField.selectAll();
            });

            // Update status
            statusLabel.setText("🔍 Search mode active - Type to filter tables (Ctrl+S to close)");
            statusLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #007bff; -fx-font-weight: bold;");

        } catch (Exception e) {
            System.err.println("Error showing search: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Hide search overlay
     */
    private void hideSearch() {
        try {
            isSearchMode = false;
            searchActivatedByCtrlS = false; // Reset the flag when hiding search

            // Remove search container from parent
            if (tablesGrid.getParent() instanceof VBox) {
                VBox parent = (VBox) tablesGrid.getParent();

                // Find and remove search container
                parent.getChildren().removeIf(node ->
                    node instanceof HBox &&
                    node.getStyle().contains("rgba(0, 123, 255, 0.1)")
                );
            }

            // Clear search and show all tables
            if (searchField != null) {
                searchField.clear();
            }
            filterTables("");

            // Restore focus to grid
            javafx.application.Platform.runLater(() -> tablesGrid.requestFocus());

            // Update status
            statusLabel.setText("Type table number + Enter to open");
            statusLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #6c757d; -fx-font-weight: normal;");

        } catch (Exception e) {
            System.err.println("Error hiding search: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Create search field
     */
    private void createSearchField() {
        searchField = new TextField();
        searchField.setPromptText("Type table number or status...");
        searchField.setPrefWidth(300);
        searchField.setStyle(
            "-fx-background-color: white; " +
            "-fx-border-color: #007bff; " +
            "-fx-border-width: 2; " +
            "-fx-border-radius: 5; " +
            "-fx-background-radius: 5; " +
            "-fx-padding: 8 12;"
        );

        // Add real-time search
        searchField.textProperty().addListener((observable, oldValue, newValue) -> {
            filterTables(newValue);
        });

        // Handle Enter key in search
        searchField.setOnKeyPressed(event -> {
            switch (event.getCode()) {
                case ENTER:
                    // If only one table visible, select it
                    selectFirstVisibleTable();
                    break;
                case ESCAPE:
                    // DIRECT FIX: If search was activated by Ctrl+S, go back immediately
                    System.out.println("ESC pressed in search field");
                    System.out.println("searchActivatedByCtrlS: " + searchActivatedByCtrlS);
                    System.out.println("searchField text: '" + (searchField.getText() == null ? "null" : searchField.getText()) + "'");

                    // Direct approach: If Ctrl+S was used and field is empty, go back immediately
                    if (searchActivatedByCtrlS) {
                        System.out.println("DIRECT FIX: Ctrl+S was used, going back immediately");

                        // Hide search first
                        isSearchMode = false;
                        searchActivatedByCtrlS = false;

                        if (searchField != null && searchField.getParent() != null) {
                            ((javafx.scene.layout.Pane) searchField.getParent()).getChildren().remove(searchField);
                        }

                        // Focus back to main grid
                        if (tablesGrid != null) {
                            tablesGrid.requestFocus();
                        }

                        event.consume();

                        // Go back immediately
                        Platform.runLater(() -> {
                            System.out.println("EXECUTING BACK NAVIGATION NOW");
                            try {
                                UniversalNavigationManager.getInstance().goBack();
                            } catch (Exception e) {
                                System.err.println("Error in back navigation: " + e.getMessage());
                                // Fallback to dashboard
                                UniversalNavigationManager.getInstance().goToDashboard();
                            }
                        });

                        return; // Exit early
                    }

                    // Normal ESC behavior for other cases
                    handleSearchFieldEscape();
                    event.consume();
                    break;
                case S:
                    if (event.isControlDown()) {
                        System.out.println("Ctrl+S in search field - toggling search");
                        hideSearch();
                        event.consume();
                    }
                    break;
                default:
                    // For any other key, if this was activated by Ctrl+S and field is empty,
                    // we might want to go back on next ESC
                    break;
            }
        });
    }

    /**
     * Filter tables based on search text
     */
    private void filterTables(String searchText) {
        try {
            String search = searchText.toLowerCase().trim();

            for (javafx.scene.Node node : tablesGrid.getChildren()) {
                if (node instanceof VBox) {
                    VBox tableCard = (VBox) node;
                    boolean shouldShow = true;

                    if (!search.isEmpty()) {
                        shouldShow = false;

                        // Search through all labels in the table card
                        for (javafx.scene.Node child : tableCard.getChildren()) {
                            if (child instanceof Label) {
                                Label label = (Label) child;
                                String text = label.getText().toLowerCase();

                                // Match table number, status, or any text
                                if (text.contains(search) ||
                                    text.contains("table " + search) ||
                                    (search.matches("\\d+") && text.contains("table " + search))) {
                                    shouldShow = true;
                                    break;
                                }
                            }
                        }
                    }

                    // Show/hide table card
                    tableCard.setVisible(shouldShow);
                    tableCard.setManaged(shouldShow);
                }
            }

            System.out.println("Filtered tables with search: '" + search + "'");

        } catch (Exception e) {
            System.err.println("Error filtering tables: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Select first visible table
     */
    private void selectFirstVisibleTable() {
        try {
            for (javafx.scene.Node node : tablesGrid.getChildren()) {
                if (node instanceof VBox && node.isVisible()) {
                    VBox tableCard = (VBox) node;

                    // Find table number from label
                    for (javafx.scene.Node child : tableCard.getChildren()) {
                        if (child instanceof Label) {
                            Label label = (Label) child;
                            String text = label.getText();

                            if (text.startsWith("Table ")) {
                                try {
                                    String numberStr = text.substring(6).trim();
                                    int tableNumber = Integer.parseInt(numberStr);

                                    hideSearch();
                                    selectTable(tableNumber);
                                    return;

                                } catch (NumberFormatException e) {
                                    // Continue searching
                                }
                            }
                        }
                    }
                }
            }

            System.out.println("No visible table found to select");

        } catch (Exception e) {
            System.err.println("Error selecting first visible table: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @FXML
    private void goBack() {
        try {
            System.out.println("MinimalTableController: Going back to dashboard...");
            // Use universal navigation manager for robust back navigation
            UniversalNavigationManager.getInstance().goBack();
        } catch (Exception e) {
            System.err.println("MinimalTableController: Error going back: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Could not go back: " + e.getMessage());
        }
    }

    private void navigateToMenu(String tableNumber) {
        System.out.println("Navigating to menu for " + tableNumber);
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/MenuSelection.fxml"));
            Parent menuView = loader.load();

            // Get the controller and set the table information
            MenuSelectionController menuController = loader.getController();
            if (menuController != null) {
                menuController.setTableInfo(tableNumber);
                System.out.println("Table info set to: " + tableNumber);
            }

            // Actually navigate to the menu by replacing the current scene root
            try {
                // Get the current stage from any window
                javafx.stage.Window window = javafx.stage.Window.getWindows().stream()
                    .filter(w -> w instanceof javafx.stage.Stage)
                    .findFirst()
                    .orElse(null);

                if (window instanceof javafx.stage.Stage) {
                    javafx.stage.Stage stage = (javafx.stage.Stage) window;
                    stage.getScene().setRoot(menuView);
                    System.out.println("Successfully navigated to menu for " + tableNumber);
                } else {
                    System.err.println("Could not find stage for navigation");
                    showAlert("Menu Loaded", "Menu for " + tableNumber + " loaded successfully!\n\nTable context: " + tableNumber);
                }
            } catch (Exception navError) {
                System.err.println("Navigation error: " + navError.getMessage());
                showAlert("Menu Loaded", "Menu for " + tableNumber + " loaded successfully!\n\nTable context: " + tableNumber);
            }

        } catch (Exception e) {
            System.err.println("Error loading menu: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Could not load menu. Error: " + e.getMessage());
        }
    }

    @FXML
    private void refreshTables() {
        System.out.println("Tables refreshed!");
        showAlert("Refresh", "Tables have been refreshed successfully!");
    }

    @FXML
    private void addNewTable() {
        try {
            System.out.println("Adding new table...");

            // Create dialog for adding new table
            javafx.scene.control.TextInputDialog dialog = new javafx.scene.control.TextInputDialog();
            dialog.setTitle("Add New Table");
            dialog.setHeaderText("Add a new table to the restaurant");
            dialog.setContentText("Enter table number:");

            // Set dialog styling
            dialog.getDialogPane().getStylesheets().add(
                getClass().getResource("/css/application.css").toExternalForm()
            );

            java.util.Optional<String> result = dialog.showAndWait();

            if (result.isPresent() && !result.get().trim().isEmpty()) {
                String input = result.get().trim();

                try {
                    int newTableNumber = Integer.parseInt(input);

                    // Validate table number
                    if (newTableNumber <= 0) {
                        showAlert("Invalid Input", "Table number must be a positive number!");
                        return;
                    }

                    if (newTableNumber > 50) {
                        showAlert("Invalid Input", "Table number cannot exceed 50!");
                        return;
                    }

                    // Check if table already exists
                    if (existingTables.contains(newTableNumber)) {
                        showAlert("Table Exists", "Table " + newTableNumber + " already exists!");
                        return;
                    }

                    // Add the new table dynamically
                    addTableToGrid(newTableNumber);

                    // Add to existing tables set
                    existingTables.add(newTableNumber);

                    // Show success message
                    showAlert("Success",
                             "Table " + newTableNumber + " has been added successfully!\n\n" +
                             "The table is now available for use.");

                    System.out.println("Table " + newTableNumber + " added to the system");

                } catch (NumberFormatException e) {
                    showAlert("Invalid Input", "Please enter a valid table number!");
                }
            }

        } catch (Exception e) {
            System.err.println("Error adding new table: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Failed to add new table: " + e.getMessage());
        }
    }

    /**
     * Add a new table to the grid dynamically
     */
    private void addTableToGrid(int tableNumber) {
        try {
            // Create new table card
            VBox tableCard = createTableCard(tableNumber);

            // Calculate position in grid (3 columns)
            int totalTables = existingTables.size();
            int row = totalTables / 3;
            int col = totalTables % 3;

            // Add to grid
            tablesGrid.add(tableCard, col, row);

            System.out.println("Added Table " + tableNumber + " to grid at position (" + row + ", " + col + ")");

        } catch (Exception e) {
            System.err.println("Error adding table to grid: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Create a new table card
     */
    private VBox createTableCard(int tableNumber) {
        VBox tableCard = new VBox();
        tableCard.setAlignment(Pos.CENTER);
        tableCard.setSpacing(15.0);
        tableCard.setStyle("-fx-background-color: white; -fx-border-color: #28a745; -fx-border-width: 3px; " +
                          "-fx-border-radius: 10px; -fx-background-radius: 10px; -fx-padding: 20px; " +
                          "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);");

        // Table number label
        Label tableLabel = new Label("Table " + tableNumber);
        tableLabel.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // Seats label
        Label seatsLabel = new Label("👥 4 seats");
        seatsLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #6c757d;");

        // Status label
        Label statusLabel = new Label("Available");
        statusLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #28a745;");

        // Buttons
        HBox buttonBox = new HBox();
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.setSpacing(10.0);

        Button viewButton = new Button("👁️");
        viewButton.setStyle("-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-padding: 8px 12px; -fx-background-radius: 5px;");
        viewButton.setOnAction(e -> viewTable(tableNumber));

        Button selectButton = new Button("Select");
        selectButton.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-padding: 8px 12px; -fx-background-radius: 5px;");
        selectButton.setOnAction(e -> selectTable(tableNumber));

        buttonBox.getChildren().addAll(viewButton, selectButton);

        // Add double-click handler
        tableCard.setOnMouseClicked(e -> {
            if (e.getClickCount() == 2) {
                selectTable(tableNumber);
            }
        });

        tableCard.getChildren().addAll(tableLabel, seatsLabel, statusLabel, buttonBox);

        return tableCard;
    }

    /**
     * Handle view table action
     */
    private void viewTable(int tableNumber) {
        System.out.println("Viewing Table " + tableNumber);

        // Check if table has active order
        boolean hasOrder = orderManager.hasActiveOrder(tableNumber);
        double orderTotal = orderManager.getTableOrderTotal(tableNumber);

        String message = "Table " + tableNumber + " Details:\n\n";
        message += "Status: " + (hasOrder ? "Occupied" : "Available") + "\n";
        message += "Seats: 4\n";

        if (hasOrder) {
            message += "Current Order Total: ₹" + String.format("%.2f", orderTotal) + "\n";
            message += "Items: " + orderManager.getTableOrderItemCount(tableNumber) + "\n";
        }

        showAlert("Table Details", message);
    }

    /**
     * Handle select table action
     */
    private void selectTable(int tableNumber) {
        System.out.println("Selecting Table " + tableNumber);
        navigateToMenu("Table " + tableNumber);
    }

    @FXML
    private void handleTableDoubleClick(MouseEvent event) {
        if (event.getClickCount() == 2) {
            // Get the table VBox that was clicked
            VBox tableVBox = (VBox) event.getSource();

            // Extract table number from the VBox children (first Label contains "Table X")
            String tableNumber = extractTableNumber(tableVBox);

            System.out.println("Double-clicked on " + tableNumber);
            navigateToMenu(tableNumber);
        }
    }

    private String extractTableNumber(VBox tableVBox) {
        try {
            // The first child should be a Label with "Table X" text
            if (tableVBox.getChildren().size() > 0) {
                javafx.scene.control.Label tableLabel = (javafx.scene.control.Label) tableVBox.getChildren().get(0);
                return tableLabel.getText(); // Returns "Table 1", "Table 2", etc.
            }
        } catch (Exception e) {
            System.err.println("Error extracting table number: " + e.getMessage());
        }
        return "Unknown Table";
    }

    // Table 1 actions
    @FXML
    private void viewTable1() {
        showAlert("Table 1 Details", "Table 1\nStatus: Available\nSeats: 4\nNo active orders");
    }

    @FXML
    private void selectTable1() {
        System.out.println("Table 1 selected for new order");
        navigateToMenu("Table 1");
    }

    // Table 2 actions
    @FXML
    private void viewTable2() {
        showAlert("Table 2 Details", "Table 2\nStatus: Order Pending\nSeats: 4\nOrder #1001\nAmount: ₹450\nItems: 3");
    }

    @FXML
    private void addToTable2() {
        System.out.println("Adding items to Table 2");
        navigateToMenu("Table 2");
    }

    // Table 3 actions
    @FXML
    private void viewTable3() {
        showAlert("Table 3 Details", "Table 3\nStatus: Available\nSeats: 4\nNo active orders");
    }

    @FXML
    private void selectTable3() {
        System.out.println("Table 3 selected for new order");
        navigateToMenu("Table 3");
    }

    // Table 4 actions
    @FXML
    private void viewTable4() {
        showAlert("Table 4 Details", "Table 4\nStatus: Ready to Serve\nSeats: 4\nOrder #1002\nAmount: ₹680\nItems: 5");
    }

    @FXML
    private void addToTable4() {
        System.out.println("Adding items to Table 4");
        navigateToMenu("Table 4");
    }

    // Table 5 actions
    @FXML
    private void viewTable5() {
        showAlert("Table 5 Details", "Table 5\nStatus: Preparing\nSeats: 4\nOrder #1003\nAmount: ₹320\nItems: 2");
    }

    @FXML
    private void addToTable5() {
        System.out.println("Adding items to Table 5");
        navigateToMenu("Table 5");
    }

    // Table 6 actions
    @FXML
    private void viewTable6() {
        showAlert("Table 6 Details", "Table 6\nStatus: Available\nSeats: 4\nNo active orders");
    }

    @FXML
    private void selectTable6() {
        System.out.println("Table 6 selected for new order");
        navigateToMenu("Table 6");
    }

    // Table 7 actions
    @FXML
    private void viewTable7() {
        showAlert("Table 7 Details", "Table 7\nStatus: KOT Printed\nSeats: 4\nOrder #1004\nAmount: ₹520\nItems: 4");
    }

    @FXML
    private void addToTable7() {
        System.out.println("Adding items to Table 7");
        navigateToMenu("Table 7");
    }

    // Table 8 actions
    @FXML
    private void viewTable8() {
        showAlert("Table 8 Details", "Table 8\nStatus: Available\nSeats: 4\nNo active orders");
    }

    @FXML
    private void selectTable8() {
        System.out.println("Table 8 selected for new order");
        navigateToMenu("Table 8");
    }

    // Table 9 actions
    @FXML
    private void viewTable9() {
        showAlert("Table 9 Details", "Table 9\nStatus: Completed\nSeats: 4\nOrder #1005\nAmount: ₹750\nItems: 6");
    }

    @FXML
    private void addToTable9() {
        System.out.println("Adding items to Table 9");
        navigateToMenu("Table 9");
    }

    private void showAlert(String title, String message) {
        try {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        } catch (Exception e) {
            System.err.println("Error showing alert: " + e.getMessage());
        }
    }
}
