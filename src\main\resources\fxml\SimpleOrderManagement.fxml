<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.OrderManagementController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-padding: 20px; -fx-background-color: white;">
         <children>
            <Button onAction="#goBack" text="← Back" />
            <Label text="Order Management" style="-fx-font-size: 24px; -fx-font-weight: bold;" />
         </children>
      </HBox>
      
      <!-- Search and Filters -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-padding: 20px; -fx-background-color: #f8f9fa;">
         <children>
            <TextField fx:id="searchField" promptText="Search orders..." prefWidth="200.0" />
            <ComboBox fx:id="statusFilterCombo" promptText="Status" prefWidth="120.0" />
            <ComboBox fx:id="typeFilterCombo" promptText="Type" prefWidth="120.0" />
            <Button onAction="#applyFilters" text="Search" />
            <Button onAction="#refreshOrders" text="Refresh" />
         </children>
      </HBox>
      
      <!-- Orders Table -->
      <TableView fx:id="ordersTable" style="-fx-background-color: white;">
         <columns>
            <TableColumn fx:id="orderIdColumn" text="Order ID" prefWidth="80.0" />
            <TableColumn fx:id="tableNumberColumn" text="Table" prefWidth="100.0" />
            <TableColumn fx:id="customerNameColumn" text="Customer" prefWidth="120.0" />
            <TableColumn fx:id="orderTypeColumn" text="Type" prefWidth="100.0" />
            <TableColumn fx:id="statusColumn" text="Status" prefWidth="100.0" />
            <TableColumn fx:id="itemsCountColumn" text="Items" prefWidth="80.0" />
            <TableColumn fx:id="totalAmountColumn" text="Amount" prefWidth="100.0" />
            <TableColumn fx:id="orderTimeColumn" text="Time" prefWidth="150.0" />
            <TableColumn fx:id="actionsColumn" text="Actions" prefWidth="150.0" />
         </columns>
      </TableView>
      
      <!-- Order Details Dialog (Simple) -->
      <VBox fx:id="orderDetailsDialog" managed="false" visible="false" style="-fx-background-color: rgba(0,0,0,0.5); -fx-padding: 50px;">
         <children>
            <VBox style="-fx-background-color: white; -fx-padding: 20px; -fx-spacing: 15px; -fx-max-width: 600px;">
               <children>
                  <HBox alignment="CENTER_LEFT" spacing="10.0">
                     <children>
                        <Label fx:id="orderDetailsTitle" text="Order Details" style="-fx-font-size: 18px; -fx-font-weight: bold;" />
                        <Button onAction="#closeOrderDetailsDialog" text="✕" style="-fx-background-color: transparent;" />
                     </children>
                  </HBox>
                  
                  <GridPane hgap="10.0" vgap="10.0">
                     <columnConstraints>
                        <ColumnConstraints minWidth="100.0" />
                        <ColumnConstraints minWidth="200.0" />
                     </columnConstraints>
                     <children>
                        <Label text="Order ID:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <Label fx:id="detailOrderId" text="-" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="Table:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <Label fx:id="detailTableNumber" text="-" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label text="Customer:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <Label fx:id="detailCustomerName" text="-" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        
                        <Label text="Type:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <Label fx:id="detailOrderType" text="-" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                        
                        <Label text="Status:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                        <Label fx:id="detailStatus" text="-" GridPane.columnIndex="1" GridPane.rowIndex="4" />
                        
                        <Label text="Time:" GridPane.columnIndex="0" GridPane.rowIndex="5" />
                        <Label fx:id="detailOrderTime" text="-" GridPane.columnIndex="1" GridPane.rowIndex="5" />
                     </children>
                  </GridPane>
                  
                  <Label text="Order Items:" style="-fx-font-weight: bold;" />
                  <TableView fx:id="orderItemsTable" prefHeight="200.0">
                     <columns>
                        <TableColumn fx:id="itemNameColumn" text="Item" prefWidth="150.0" />
                        <TableColumn fx:id="itemQuantityColumn" text="Qty" prefWidth="60.0" />
                        <TableColumn fx:id="itemPriceColumn" text="Price" prefWidth="80.0" />
                        <TableColumn fx:id="itemTotalColumn" text="Total" prefWidth="80.0" />
                        <TableColumn fx:id="itemNotesColumn" text="Notes" prefWidth="100.0" />
                        <TableColumn fx:id="itemActionsColumn" text="Actions" prefWidth="100.0" />
                     </columns>
                  </TableView>
                  
                  <HBox alignment="CENTER_RIGHT" spacing="10.0">
                     <children>
                        <Button fx:id="addItemButton" onAction="#showAddItemDialog" text="➕ Add Item" />
                     </children>
                  </HBox>
                  
                  <GridPane hgap="10.0" vgap="5.0">
                     <columnConstraints>
                        <ColumnConstraints minWidth="100.0" />
                        <ColumnConstraints minWidth="100.0" />
                     </columnConstraints>
                     <children>
                        <Label text="Subtotal:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <Label fx:id="detailSubtotal" text="₹0.00" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="GST (18%):" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <Label fx:id="detailGST" text="₹0.00" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label text="Service Charge:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <Label fx:id="detailServiceCharge" text="₹0.00" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        
                        <Label text="Grand Total:" style="-fx-font-weight: bold;" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <Label fx:id="detailGrandTotal" text="₹0.00" style="-fx-font-weight: bold;" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                     </children>
                  </GridPane>
                  
                  <HBox alignment="CENTER_RIGHT" spacing="10.0">
                     <children>
                        <Button onAction="#closeOrderDetailsDialog" text="Close" />
                     </children>
                  </HBox>
               </children>
            </VBox>
         </children>
      </VBox>
   </children>
</VBox>
