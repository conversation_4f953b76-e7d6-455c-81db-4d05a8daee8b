<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.restaurant</groupId>
    <artifactId>restaurant-management</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <javafx.version>17.0.2</javafx.version>
        <javafx.maven.plugin.version>0.0.8</javafx.maven.plugin.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!-- NetBeans properties -->
        <netbeans.hint.jdkPlatform>JDK_23</netbeans.hint.jdkPlatform>
        <netbeans.hint.license>apache20</netbeans.hint.license>

        <!-- JavaFX Runtime Arguments for NetBeans -->
        <exec.vmArgs>--module-path ${project.build.directory}/dependency --add-modules javafx.controls,javafx.fxml,javafx.media --add-exports javafx.base/com.sun.javafx.runtime=ALL-UNNAMED --add-exports javafx.controls/com.sun.javafx.scene.control.behavior=ALL-UNNAMED --add-exports javafx.controls/com.sun.javafx.scene.control=ALL-UNNAMED --add-opens javafx.controls/javafx.scene.control=ALL-UNNAMED --add-opens javafx.graphics/javafx.scene=ALL-UNNAMED -Djava.awt.headless=false -Dprism.order=sw</exec.vmArgs>
        <exec.mainClass>com.restaurant.RestaurantApp</exec.mainClass>

        <!-- NetBeans execution preferences -->
        <netbeans.run.params>dependency:copy-dependencies antrun:run</netbeans.run.params>
    </properties>

    <dependencies>
        <!-- JavaFX -->
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-controls</artifactId>
            <version>${javafx.version}</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-fxml</artifactId>
            <version>${javafx.version}</version>
        </dependency>
        
        <!-- SQLite JDBC -->
        <dependency>
            <groupId>org.xerial</groupId>
            <artifactId>sqlite-jdbc</artifactId>
            <version>3.45.1.0</version>
        </dependency>
        
        <!-- BCrypt for password hashing -->
        <dependency>
            <groupId>org.mindrot</groupId>
            <artifactId>jbcrypt</artifactId>
            <version>0.4</version>
        </dependency>
        
        <!-- PDF Generation -->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.27</version>
        </dependency>

        <!-- JavaFX Media for MP3 playback -->
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-media</artifactId>
            <version>${javafx.version}</version>
        </dependency>

        <!-- JUnit 5 Testing Framework -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>5.10.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>5.10.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <version>5.10.1</version>
            <scope>test</scope>
        </dependency>

        <!-- Mockito for mocking in tests -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>5.8.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>5.8.0</version>
            <scope>test</scope>
        </dependency>

        <!-- TestFX for JavaFX testing -->
        <dependency>
            <groupId>org.testfx</groupId>
            <artifactId>testfx-core</artifactId>
            <version>4.0.18</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testfx</groupId>
            <artifactId>testfx-junit5</artifactId>
            <version>4.0.18</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.openjfx</groupId>
                <artifactId>javafx-maven-plugin</artifactId>
                <version>0.0.8</version>
                <configuration>
                    <mainClass>com.restaurant.RestaurantApp</mainClass>
                    <commandlineArgs>--add-modules javafx.controls,javafx.fxml,javafx.media</commandlineArgs>
                    <jvmArgs>
                        <jvmArg>-Xmx2048m</jvmArg>
                        <jvmArg>-Xms512m</jvmArg>
                        <jvmArg>--add-modules</jvmArg>
                        <jvmArg>javafx.controls,javafx.fxml,javafx.media</jvmArg>
                        <jvmArg>--add-exports</jvmArg>
                        <jvmArg>javafx.base/com.sun.javafx.runtime=ALL-UNNAMED</jvmArg>
                        <jvmArg>--add-exports</jvmArg>
                        <jvmArg>javafx.controls/com.sun.javafx.scene.control.behavior=ALL-UNNAMED</jvmArg>
                        <jvmArg>--add-exports</jvmArg>
                        <jvmArg>javafx.controls/com.sun.javafx.scene.control=ALL-UNNAMED</jvmArg>
                        <jvmArg>--add-opens</jvmArg>
                        <jvmArg>javafx.controls/javafx.scene.control=ALL-UNNAMED</jvmArg>
                        <jvmArg>--add-opens</jvmArg>
                        <jvmArg>javafx.graphics/javafx.scene=ALL-UNNAMED</jvmArg>
                        <jvmArg>-Djava.awt.headless=false</jvmArg>
                        <jvmArg>-Dprism.order=sw</jvmArg>
                        <jvmArg>-Dprism.verbose=true</jvmArg>
                    </jvmArgs>
                </configuration>
            </plugin>

            <!-- Maven dependency plugin to copy dependencies -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/dependency</outputDirectory>
                            <overWriteReleases>false</overWriteReleases>
                            <overWriteSnapshots>false</overWriteSnapshots>
                            <overWriteIfNewer>true</overWriteIfNewer>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Exec Maven Plugin for NetBeans compatibility -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <mainClass>${exec.mainClass}</mainClass>
                    <classpathScope>runtime</classpathScope>
                    <includePluginDependencies>true</includePluginDependencies>
                    <commandlineArgs>${exec.vmArgs}</commandlineArgs>
                </configuration>
                <executions>
                    <execution>
                        <id>default-cli</id>
                        <goals>
                            <goal>java</goal>
                        </goals>
                        <configuration>
                            <mainClass>com.restaurant.RestaurantApp</mainClass>
                            <classpathScope>runtime</classpathScope>
                            <options>
                                <option>--module-path</option>
                                <option>${project.build.directory}/dependency</option>
                                <option>--add-modules</option>
                                <option>javafx.controls,javafx.fxml,javafx.media</option>
                                <option>--add-exports</option>
                                <option>javafx.base/com.sun.javafx.runtime=ALL-UNNAMED</option>
                                <option>--add-exports</option>
                                <option>javafx.controls/com.sun.javafx.scene.control.behavior=ALL-UNNAMED</option>
                                <option>--add-exports</option>
                                <option>javafx.controls/com.sun.javafx.scene.control=ALL-UNNAMED</option>
                                <option>--add-opens</option>
                                <option>javafx.controls/javafx.scene.control=ALL-UNNAMED</option>
                                <option>--add-opens</option>
                                <option>javafx.graphics/javafx.scene=ALL-UNNAMED</option>
                                <option>-Djava.awt.headless=false</option>
                                <option>-Dprism.order=sw</option>
                            </options>
                        </configuration>
                    </execution>
                </executions>

            </plugin>

            <!-- Maven AntRun Plugin for reliable JavaFX execution -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <target>
                        <java classname="com.restaurant.RestaurantApp" fork="true" failonerror="true">
                            <classpath>
                                <path refid="maven.runtime.classpath"/>
                                <pathelement location="${project.build.outputDirectory}"/>
                            </classpath>
                            <jvmarg value="--module-path"/>
                            <jvmarg value="${project.build.directory}/dependency"/>
                            <jvmarg value="--add-modules"/>
                            <jvmarg value="javafx.controls,javafx.fxml,javafx.media"/>
                            <jvmarg value="--add-exports"/>
                            <jvmarg value="javafx.base/com.sun.javafx.runtime=ALL-UNNAMED"/>
                            <jvmarg value="--add-exports"/>
                            <jvmarg value="javafx.controls/com.sun.javafx.scene.control.behavior=ALL-UNNAMED"/>
                            <jvmarg value="--add-exports"/>
                            <jvmarg value="javafx.controls/com.sun.javafx.scene.control=ALL-UNNAMED"/>
                            <jvmarg value="--add-opens"/>
                            <jvmarg value="javafx.controls/javafx.scene.control=ALL-UNNAMED"/>
                            <jvmarg value="--add-opens"/>
                            <jvmarg value="javafx.graphics/javafx.scene=ALL-UNNAMED"/>
                            <jvmarg value="-Djava.awt.headless=false"/>
                            <jvmarg value="-Dprism.order=sw"/>
                            <jvmarg value="-Dprism.verbose=false"/>
                        </java>
                    </target>
                </configuration>
            </plugin>

            <!-- Maven clean plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <version>3.2.0</version>
            </plugin>

            <!-- Maven Jarsigner Plugin for signing artifacts -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jarsigner-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <skip>true</skip> <!-- Skip by default, enable when needed -->
                </configuration>
                <executions>
                    <execution>
                        <id>sign</id>
                        <goals>
                            <goal>sign</goal>
                        </goals>
                        <phase>package</phase>
                        <configuration>
                            <archiveDirectory>${project.build.directory}</archiveDirectory>
                            <includes>
                                <include>**/*.jar</include>
                            </includes>
                            <!-- Keystore configuration (optional) -->
                            <keystore>${user.home}/.keystore</keystore>
                            <alias>mykey</alias>
                            <storepass>changeit</storepass>
                            <keypass>changeit</keypass>
                            <verbose>true</verbose>
                        </configuration>
                    </execution>
                    <execution>
                        <id>verify</id>
                        <goals>
                            <goal>verify</goal>
                        </goals>
                        <phase>verify</phase>
                        <configuration>
                            <archiveDirectory>${project.build.directory}</archiveDirectory>
                            <includes>
                                <include>**/*.jar</include>
                            </includes>
                            <verbose>true</verbose>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Maven resources plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.1</version>
            </plugin>
        </plugins>
    </build>

    <!-- Profiles for different execution methods -->
    <profiles>
        <profile>
            <id>run-app</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>3.1.0</version>
                        <executions>
                            <execution>
                                <id>run-javafx-app</id>
                                <phase>compile</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target>
                                        <java classname="com.restaurant.RestaurantApp" fork="true" failonerror="true">
                                            <classpath>
                                                <path refid="maven.runtime.classpath"/>
                                                <pathelement location="${project.build.outputDirectory}"/>
                                            </classpath>
                                            <jvmarg value="--module-path"/>
                                            <jvmarg value="${project.build.directory}/dependency"/>
                                            <jvmarg value="--add-modules"/>
                                            <jvmarg value="javafx.controls,javafx.fxml,javafx.media"/>
                                            <jvmarg value="--add-exports"/>
                                            <jvmarg value="javafx.base/com.sun.javafx.runtime=ALL-UNNAMED"/>
                                            <jvmarg value="--add-exports"/>
                                            <jvmarg value="javafx.controls/com.sun.javafx.scene.control.behavior=ALL-UNNAMED"/>
                                            <jvmarg value="--add-exports"/>
                                            <jvmarg value="javafx.controls/com.sun.javafx.scene.control=ALL-UNNAMED"/>
                                            <jvmarg value="--add-opens"/>
                                            <jvmarg value="javafx.controls/javafx.scene.control=ALL-UNNAMED"/>
                                            <jvmarg value="--add-opens"/>
                                            <jvmarg value="javafx.graphics/javafx.scene=ALL-UNNAMED"/>
                                            <jvmarg value="-Djava.awt.headless=false"/>
                                            <jvmarg value="-Dprism.order=sw"/>
                                        </java>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>signed-execution</id>
            <properties>
                <maven.jarsigner.skip>false</maven.jarsigner.skip>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-jarsigner-plugin</artifactId>
                        <version>3.1.0</version>
                        <configuration>
                            <skip>false</skip>
                        </configuration>
                        <executions>
                            <execution>
                                <id>sign-jar</id>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                                <phase>package</phase>
                                <configuration>
                                    <archiveDirectory>${project.build.directory}</archiveDirectory>
                                    <includes>
                                        <include>**/*.jar</include>
                                    </includes>
                                    <!-- Self-signed certificate for development -->
                                    <keystore>${project.build.directory}/keystore.jks</keystore>
                                    <alias>selfsigned</alias>
                                    <storepass>password</storepass>
                                    <keypass>password</keypass>
                                    <verbose>true</verbose>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>

                    <!-- Generate self-signed certificate for development -->
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>keytool-maven-plugin</artifactId>
                        <version>1.7</version>
                        <executions>
                            <execution>
                                <id>generate-keystore</id>
                                <goals>
                                    <goal>generateKeyPair</goal>
                                </goals>
                                <phase>generate-resources</phase>
                                <configuration>
                                    <keystore>${project.build.directory}/keystore.jks</keystore>
                                    <alias>selfsigned</alias>
                                    <storepass>password</storepass>
                                    <keypass>password</keypass>
                                    <dname>CN=Restaurant App, OU=Development, O=Restaurant Management, L=City, ST=State, C=US</dname>
                                    <keyalg>RSA</keyalg>
                                    <keysize>2048</keysize>
                                    <validity>365</validity>
                                    <skipIfExist>true</skipIfExist>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>secure-javafx</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>3.1.0</version>
                        <executions>
                            <execution>
                                <id>run-signed-javafx-app</id>
                                <phase>compile</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target>
                                        <java classname="com.restaurant.RestaurantApp" fork="true" failonerror="true">
                                            <classpath>
                                                <path refid="maven.runtime.classpath"/>
                                                <pathelement location="${project.build.outputDirectory}"/>
                                            </classpath>
                                            <jvmarg value="--module-path"/>
                                            <jvmarg value="${project.build.directory}/dependency"/>
                                            <jvmarg value="--add-modules"/>
                                            <jvmarg value="javafx.controls,javafx.fxml,javafx.media"/>
                                            <jvmarg value="--add-exports"/>
                                            <jvmarg value="javafx.base/com.sun.javafx.runtime=ALL-UNNAMED"/>
                                            <jvmarg value="--add-exports"/>
                                            <jvmarg value="javafx.controls/com.sun.javafx.scene.control.behavior=ALL-UNNAMED"/>
                                            <jvmarg value="--add-exports"/>
                                            <jvmarg value="javafx.controls/com.sun.javafx.scene.control=ALL-UNNAMED"/>
                                            <jvmarg value="--add-opens"/>
                                            <jvmarg value="javafx.controls/javafx.scene.control=ALL-UNNAMED"/>
                                            <jvmarg value="--add-opens"/>
                                            <jvmarg value="javafx.graphics/javafx.scene=ALL-UNNAMED"/>
                                            <jvmarg value="-Djava.awt.headless=false"/>
                                            <jvmarg value="-Dprism.order=sw"/>
                                            <!-- Security and signing related JVM args -->
                                            <jvmarg value="-Djava.security.policy=all.policy"/>
                                            <jvmarg value="-Dtrust_all_cert=true"/>
                                            <jvmarg value="-Dcom.sun.net.useSystemProxies=true"/>
                                        </java>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>