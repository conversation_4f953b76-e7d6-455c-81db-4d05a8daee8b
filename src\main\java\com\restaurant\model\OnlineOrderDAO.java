package com.restaurant.model;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import com.restaurant.util.CentralizedNotificationManager;
import com.restaurant.service.NotificationService;

/**
 * Data Access Object for managing online orders from <PERSON>wiggy and Zomato
 */
public class OnlineOrderDAO {
    
    private static final String DB_URL = "*************************";
    
    // Initialize database tables
    public static void initializeDatabase() {
        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            createOnlineOrdersTable(conn);
            createOnlineOrderItemsTable(conn);
            System.out.println("Online orders database tables initialized successfully");
        } catch (SQLException e) {
            System.err.println("Error initializing online orders database: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void createOnlineOrdersTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS online_orders (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    "order_id TEXT NOT NULL UNIQUE, " +
                    "platform TEXT NOT NULL, " +
                    "customer_name TEXT NOT NULL, " +
                    "customer_phone TEXT, " +
                    "delivery_address TEXT, " +
                    "status TEXT NOT NULL DEFAULT 'PREPARING', " +
                    "total_amount REAL NOT NULL, " +
                    "order_time TIMESTAMP NOT NULL, " +
                    "status_updated_time TIMESTAMP NOT NULL, " +
                    "special_instructions TEXT, " +
                    "estimated_prep_time INTEGER DEFAULT 30, " +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                    ")";
        
        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
        }
    }
    
    private static void createOnlineOrderItemsTable(Connection conn) throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS online_order_items (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    "online_order_id INTEGER NOT NULL, " +
                    "item_name TEXT NOT NULL, " +
                    "quantity INTEGER NOT NULL, " +
                    "unit_price REAL NOT NULL, " +
                    "total_price REAL NOT NULL, " +
                    "special_instructions TEXT, " +
                    "category TEXT, " +
                    "FOREIGN KEY (online_order_id) REFERENCES online_orders (id)" +
                    ")";
        
        try (Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
        }
    }
    
    // Create a new online order
    public static int createOnlineOrder(OnlineOrder order) {
        String sql = "INSERT INTO online_orders (order_id, platform, customer_name, customer_phone, " +
                    "delivery_address, status, total_amount, order_time, " +
                    "status_updated_time, special_instructions, estimated_prep_time) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = DriverManager.getConnection(DB_URL);
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            pstmt.setString(1, order.getOrderId());
            pstmt.setString(2, order.getPlatform().name());
            pstmt.setString(3, order.getCustomerName());
            pstmt.setString(4, order.getCustomerPhone());
            pstmt.setString(5, order.getDeliveryAddress());
            pstmt.setString(6, order.getStatus().name());
            pstmt.setDouble(7, order.getTotalAmount());
            pstmt.setTimestamp(8, Timestamp.valueOf(order.getOrderTime()));
            pstmt.setTimestamp(9, Timestamp.valueOf(order.getStatusUpdatedTime()));
            pstmt.setString(10, order.getSpecialInstructions());
            pstmt.setInt(11, order.getEstimatedPrepTime());
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows > 0) {
                try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                    if (generatedKeys.next()) {
                        int orderId = generatedKeys.getInt(1);
                        order.setId(orderId);
                        
                        // Insert order items
                        if (order.getItems() != null) {
                            for (OnlineOrderItem item : order.getItems()) {
                                item.setOnlineOrderId(orderId);
                                createOnlineOrderItem(item);
                            }
                        }

                        // TRIGGER MP3 NOTIFICATION FOR NEW ORDERS
                        if (order.getStatus() == OnlineOrder.OrderStatus.NEW) {
                            triggerNewOrderNotification(order);
                        }

                        return orderId;
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("Error creating online order: " + e.getMessage());
            e.printStackTrace();
        }
        return -1;
    }
    
    // Create an online order item
    public static boolean createOnlineOrderItem(OnlineOrderItem item) {
        String sql = "INSERT INTO online_order_items (online_order_id, item_name, quantity, unit_price, " +
                    "total_price, special_instructions, category) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = DriverManager.getConnection(DB_URL);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, item.getOnlineOrderId());
            pstmt.setString(2, item.getItemName());
            pstmt.setInt(3, item.getQuantity());
            pstmt.setDouble(4, item.getUnitPrice());
            pstmt.setDouble(5, item.getTotalPrice());
            pstmt.setString(6, item.getSpecialInstructions());
            pstmt.setString(7, item.getCategory());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("Error creating online order item: " + e.getMessage());
            e.printStackTrace();
        }
        return false;
    }
    
    // Get all online orders
    public static List<OnlineOrder> getAllOnlineOrders() {
        List<OnlineOrder> orders = new ArrayList<>();
        String sql = "SELECT * FROM online_orders ORDER BY order_time DESC";
        
        try (Connection conn = DriverManager.getConnection(DB_URL);
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                OnlineOrder order = mapResultSetToOnlineOrder(rs);
                order.setItems(getOnlineOrderItems(order.getId()));
                orders.add(order);
            }
        } catch (SQLException e) {
            System.err.println("Error getting all online orders: " + e.getMessage());
            e.printStackTrace();
        }
        return orders;
    }
    
    // Get online orders by status
    public static List<OnlineOrder> getOnlineOrdersByStatus(OnlineOrder.OrderStatus status) {
        List<OnlineOrder> orders = new ArrayList<>();
        String sql = "SELECT * FROM online_orders WHERE status = ? ORDER BY order_time ASC";
        
        try (Connection conn = DriverManager.getConnection(DB_URL);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, status.name());
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    OnlineOrder order = mapResultSetToOnlineOrder(rs);
                    order.setItems(getOnlineOrderItems(order.getId()));
                    orders.add(order);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting online orders by status: " + e.getMessage());
            e.printStackTrace();
        }
        return orders;
    }
    
    // Update order status
    public static boolean updateOrderStatus(int orderId, OnlineOrder.OrderStatus newStatus) {
        String sql = "UPDATE online_orders SET status = ?, status_updated_time = ? WHERE id = ?";
        
        try (Connection conn = DriverManager.getConnection(DB_URL);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, newStatus.name());
            pstmt.setTimestamp(2, Timestamp.valueOf(LocalDateTime.now()));
            pstmt.setInt(3, orderId);
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("Error updating order status: " + e.getMessage());
            e.printStackTrace();
        }
        return false;
    }
    
    // Get order items for a specific order
    public static List<OnlineOrderItem> getOnlineOrderItems(int onlineOrderId) {
        List<OnlineOrderItem> items = new ArrayList<>();
        String sql = "SELECT * FROM online_order_items WHERE online_order_id = ? ORDER BY id";
        
        try (Connection conn = DriverManager.getConnection(DB_URL);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, onlineOrderId);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    OnlineOrderItem item = mapResultSetToOnlineOrderItem(rs);
                    items.add(item);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting online order items: " + e.getMessage());
            e.printStackTrace();
        }
        return items;
    }
    
    // Helper method to map ResultSet to OnlineOrder
    private static OnlineOrder mapResultSetToOnlineOrder(ResultSet rs) throws SQLException {
        OnlineOrder order = new OnlineOrder();
        order.setId(rs.getInt("id"));
        order.setOrderId(rs.getString("order_id"));
        order.setPlatform(OnlineOrder.Platform.valueOf(rs.getString("platform")));
        order.setCustomerName(rs.getString("customer_name"));
        order.setCustomerPhone(rs.getString("customer_phone"));
        order.setDeliveryAddress(rs.getString("delivery_address"));
        order.setStatus(OnlineOrder.OrderStatus.valueOf(rs.getString("status")));
        order.setTotalAmount(rs.getDouble("total_amount"));
        order.setOrderTime(rs.getTimestamp("order_time").toLocalDateTime());
        order.setStatusUpdatedTime(rs.getTimestamp("status_updated_time").toLocalDateTime());
        order.setSpecialInstructions(rs.getString("special_instructions"));
        order.setEstimatedPrepTime(rs.getInt("estimated_prep_time"));
        return order;
    }
    
    // Helper method to map ResultSet to OnlineOrderItem
    private static OnlineOrderItem mapResultSetToOnlineOrderItem(ResultSet rs) throws SQLException {
        OnlineOrderItem item = new OnlineOrderItem();
        item.setId(rs.getInt("id"));
        item.setOnlineOrderId(rs.getInt("online_order_id"));
        item.setItemName(rs.getString("item_name"));
        item.setQuantity(rs.getInt("quantity"));
        item.setUnitPrice(rs.getDouble("unit_price"));
        item.setTotalPrice(rs.getDouble("total_price"));
        item.setSpecialInstructions(rs.getString("special_instructions"));
        item.setCategory(rs.getString("category"));
        return item;
    }

    /**
     * Trigger MP3 notification for new orders
     * This ensures MP3 sounds play automatically when orders are created
     */
    private static void triggerNewOrderNotification(OnlineOrder order) {
        try {
            System.out.println("🔔 TRIGGERING MP3 NOTIFICATION for new order: " + order.getOrderId() +
                             " (" + order.getPlatform().name() + ")");

            // Get the centralized notification manager
            CentralizedNotificationManager soundManager = CentralizedNotificationManager.getInstance();

            // Trigger platform-specific MP3 notification with continuous ringing
            if (order.getPlatform() == OnlineOrder.Platform.SWIGGY) {
                soundManager.notifyNewSwiggyOrder(order);
                System.out.println("🟠 SWIGGY MP3 notification triggered for order: " + order.getOrderId());
            } else if (order.getPlatform() == OnlineOrder.Platform.ZOMATO) {
                soundManager.notifyNewZomatoOrder(order);
                System.out.println("🔴 ZOMATO MP3 notification triggered for order: " + order.getOrderId());
            } else if (order.getPlatform() == OnlineOrder.Platform.WOK_KA_TADKA) {
                soundManager.notifyNewWokKaTadkaOrder(order);
                System.out.println("🟢 WOK KA TADKA MP3 notification triggered for order: " + order.getOrderId());
            }

            // Also add to notification service for UI display
            NotificationService notificationService = NotificationService.getInstance();
            notificationService.notifyOnlineOrder(order.getPlatform().name(),
                                                order.getOrderId(),
                                                order.getTotalAmount());

            System.out.println("✅ MP3 notification system activated for order: " + order.getOrderId());

        } catch (Exception e) {
            System.err.println("Error triggering new order notification: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
