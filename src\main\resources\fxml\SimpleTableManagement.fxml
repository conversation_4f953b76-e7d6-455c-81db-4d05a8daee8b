<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.SimpleTableManagementController" stylesheets="@../css/application.css">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="module-header">
         <children>
            <Button mnemonicParsing="false" onAction="#goBack" styleClass="back-button" text="← Back" />
            <VBox spacing="5.0">
               <children>
                  <Label styleClass="module-title" text="Table Management">
                     <font>
                        <Font name="System Bold" size="24.0" />
                     </font>
                  </Label>
                  <Label styleClass="module-subtitle" text="Select a table to take order" />
               </children>
            </VBox>
            <Region HBox.hgrow="ALWAYS" />
            <Button mnemonicParsing="false" onAction="#refreshTables" styleClass="refresh-button" text="🔄 Refresh" />
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </HBox>
      
      <!-- Status Legend -->
      <HBox alignment="CENTER_LEFT" spacing="30.0" styleClass="status-legend">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="8.0">
               <children>
                  <Label styleClass="status-indicator-free" text="●" />
                  <Label text="Available" />
               </children>
            </HBox>
            <HBox alignment="CENTER_LEFT" spacing="8.0">
               <children>
                  <Label styleClass="status-indicator-occupied" text="●" />
                  <Label text="Occupied" />
               </children>
            </HBox>
            <HBox alignment="CENTER_LEFT" spacing="8.0">
               <children>
                  <Label styleClass="status-indicator-preparing" text="●" />
                  <Label text="Preparing" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>
      
      <!-- Tables Grid -->
      <ScrollPane styleClass="tables-scroll" VBox.vgrow="ALWAYS" fitToWidth="true" fitToHeight="true">
         <content>
            <GridPane fx:id="tablesGrid" styleClass="tables-grid" hgap="20.0" vgap="20.0" alignment="CENTER">
               <columnConstraints>
                  <ColumnConstraints hgrow="SOMETIMES" minWidth="200.0" prefWidth="250.0" />
                  <ColumnConstraints hgrow="SOMETIMES" minWidth="200.0" prefWidth="250.0" />
                  <ColumnConstraints hgrow="SOMETIMES" minWidth="200.0" prefWidth="250.0" />
               </columnConstraints>
               <rowConstraints>
                  <RowConstraints minHeight="200.0" prefHeight="220.0" vgrow="SOMETIMES" />
                  <RowConstraints minHeight="200.0" prefHeight="220.0" vgrow="SOMETIMES" />
                  <RowConstraints minHeight="200.0" prefHeight="220.0" vgrow="SOMETIMES" />
               </rowConstraints>
               <children>
                  <!-- Table 1 -->
                  <VBox fx:id="table1" styleClass="simple-table-card" GridPane.columnIndex="0" GridPane.rowIndex="0">
                     <children>
                        <Label styleClass="table-number" text="Table 1">
                           <font>
                              <Font name="System Bold" size="20.0" />
                           </font>
                        </Label>
                        <Label text="👥 4 seats" styleClass="table-seats" />
                        <Label fx:id="table1Status" text="Available" styleClass="table-status-available" />
                        <Region VBox.vgrow="ALWAYS" />
                        <HBox alignment="CENTER" spacing="10.0">
                           <children>
                              <Button onAction="#handleTable1View" styleClass="simple-eye-btn" text="👁️" />
                              <Button onAction="#handleTable1Select" styleClass="simple-select-btn" text="Select" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 2 -->
                  <VBox fx:id="table2" styleClass="simple-table-card" GridPane.columnIndex="1" GridPane.rowIndex="0">
                     <children>
                        <Label styleClass="table-number" text="Table 2">
                           <font>
                              <Font name="System Bold" size="20.0" />
                           </font>
                        </Label>
                        <Label text="👥 4 seats" styleClass="table-seats" />
                        <Label fx:id="table2Status" text="Order Pending" styleClass="table-status-occupied" />
                        <Label text="Order #1001" styleClass="order-badge-simple" />
                        <Region VBox.vgrow="ALWAYS" />
                        <HBox alignment="CENTER" spacing="10.0">
                           <children>
                              <Button onAction="#handleTable2View" styleClass="simple-eye-btn" text="👁️" />
                              <Button onAction="#handleTable2Add" styleClass="simple-add-btn" text="+" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 3 -->
                  <VBox fx:id="table3" styleClass="simple-table-card" GridPane.columnIndex="2" GridPane.rowIndex="0">
                     <children>
                        <Label styleClass="table-number" text="Table 3">
                           <font>
                              <Font name="System Bold" size="20.0" />
                           </font>
                        </Label>
                        <Label text="👥 4 seats" styleClass="table-seats" />
                        <Label fx:id="table3Status" text="Available" styleClass="table-status-available" />
                        <Region VBox.vgrow="ALWAYS" />
                        <HBox alignment="CENTER" spacing="10.0">
                           <children>
                              <Button onAction="#viewTableDetails" styleClass="simple-eye-btn" text="👁️" userData="3" />
                              <Button onAction="#selectTable" styleClass="simple-select-btn" text="Select" userData="3" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 4 -->
                  <VBox fx:id="table4" styleClass="simple-table-card" GridPane.columnIndex="0" GridPane.rowIndex="1">
                     <children>
                        <Label styleClass="table-number" text="Table 4">
                           <font>
                              <Font name="System Bold" size="20.0" />
                           </font>
                        </Label>
                        <Label text="👥 4 seats" styleClass="table-seats" />
                        <Label fx:id="table4Status" text="Ready to Serve" styleClass="table-status-ready" />
                        <Label text="Order #1002" styleClass="order-badge-simple" />
                        <Region VBox.vgrow="ALWAYS" />
                        <HBox alignment="CENTER" spacing="10.0">
                           <children>
                              <Button onAction="#viewTableDetails" styleClass="simple-eye-btn" text="👁️" userData="4" />
                              <Button onAction="#addItemsToTable" styleClass="simple-add-btn" text="+" userData="4" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 5 -->
                  <VBox fx:id="table5" styleClass="simple-table-card" GridPane.columnIndex="1" GridPane.rowIndex="1">
                     <children>
                        <Label styleClass="table-number" text="Table 5">
                           <font>
                              <Font name="System Bold" size="20.0" />
                           </font>
                        </Label>
                        <Label text="👥 4 seats" styleClass="table-seats" />
                        <Label fx:id="table5Status" text="Preparing" styleClass="table-status-preparing" />
                        <Label text="Order #1003" styleClass="order-badge-simple" />
                        <Region VBox.vgrow="ALWAYS" />
                        <HBox alignment="CENTER" spacing="10.0">
                           <children>
                              <Button onAction="#viewTableDetails" styleClass="simple-eye-btn" text="👁️" userData="5" />
                              <Button onAction="#addItemsToTable" styleClass="simple-add-btn" text="+" userData="5" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 6 -->
                  <VBox fx:id="table6" styleClass="simple-table-card" GridPane.columnIndex="2" GridPane.rowIndex="1">
                     <children>
                        <Label styleClass="table-number" text="Table 6">
                           <font>
                              <Font name="System Bold" size="20.0" />
                           </font>
                        </Label>
                        <Label text="👥 4 seats" styleClass="table-seats" />
                        <Label fx:id="table6Status" text="Available" styleClass="table-status-available" />
                        <Region VBox.vgrow="ALWAYS" />
                        <HBox alignment="CENTER" spacing="10.0">
                           <children>
                              <Button onAction="#viewTableDetails" styleClass="simple-eye-btn" text="👁️" userData="6" />
                              <Button onAction="#selectTable" styleClass="simple-select-btn" text="Select" userData="6" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 7 -->
                  <VBox fx:id="table7" styleClass="simple-table-card" GridPane.columnIndex="0" GridPane.rowIndex="2">
                     <children>
                        <Label styleClass="table-number" text="Table 7">
                           <font>
                              <Font name="System Bold" size="20.0" />
                           </font>
                        </Label>
                        <Label text="👥 4 seats" styleClass="table-seats" />
                        <Label fx:id="table7Status" text="KOT Printed" styleClass="table-status-kot" />
                        <Label text="Order #1004" styleClass="order-badge-simple" />
                        <Region VBox.vgrow="ALWAYS" />
                        <HBox alignment="CENTER" spacing="10.0">
                           <children>
                              <Button onAction="#viewTableDetails" styleClass="simple-eye-btn" text="👁️" userData="7" />
                              <Button onAction="#addItemsToTable" styleClass="simple-add-btn" text="+" userData="7" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 8 -->
                  <VBox fx:id="table8" styleClass="simple-table-card" GridPane.columnIndex="1" GridPane.rowIndex="2">
                     <children>
                        <Label styleClass="table-number" text="Table 8">
                           <font>
                              <Font name="System Bold" size="20.0" />
                           </font>
                        </Label>
                        <Label text="👥 4 seats" styleClass="table-seats" />
                        <Label fx:id="table8Status" text="Available" styleClass="table-status-available" />
                        <Region VBox.vgrow="ALWAYS" />
                        <HBox alignment="CENTER" spacing="10.0">
                           <children>
                              <Button onAction="#viewTableDetails" styleClass="simple-eye-btn" text="👁️" userData="8" />
                              <Button onAction="#selectTable" styleClass="simple-select-btn" text="Select" userData="8" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 9 -->
                  <VBox fx:id="table9" styleClass="simple-table-card" GridPane.columnIndex="2" GridPane.rowIndex="2">
                     <children>
                        <Label styleClass="table-number" text="Table 9">
                           <font>
                              <Font name="System Bold" size="20.0" />
                           </font>
                        </Label>
                        <Label text="👥 4 seats" styleClass="table-seats" />
                        <Label fx:id="table9Status" text="Completed" styleClass="table-status-completed" />
                        <Label text="Order #1005" styleClass="order-badge-simple" />
                        <Region VBox.vgrow="ALWAYS" />
                        <HBox alignment="CENTER" spacing="10.0">
                           <children>
                              <Button onAction="#viewTableDetails" styleClass="simple-eye-btn" text="👁️" userData="9" />
                              <Button onAction="#addItemsToTable" styleClass="simple-add-btn" text="+" userData="9" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>
            </GridPane>
         </content>
      </ScrollPane>
   </children>
</VBox>
