<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<StackPane xmlns="http://javafx.com/javafx/17.0.2-ea" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.StaffRecordController" stylesheets="@../css/application.css">
   <children>
      <!-- Main Content -->
      <VBox>
         <children>
            <!-- Header Section -->
            <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="module-header">
               <children>
                  <Button mnemonicParsing="false" onAction="#goBack" styleClass="back-button" text="← Back" />
                  <Label styleClass="module-title" text="👥 Staff Record">
                     <font>
                        <Font name="System Bold" size="24.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button mnemonicParsing="false" onAction="#addNewStaff" styleClass="primary-button" text="➕ Add New Staff" />
               </children>
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>
            </HBox>

            <!-- Search and Filter Section -->
            <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="filter-section">
               <children>
                  <Label text="Search:" />
                  <TextField fx:id="searchField" promptText="Search by Name, ID, Contact, or Role..." styleClass="search-field" prefWidth="300.0" />
                  <Label text="Department:" />
                  <ComboBox fx:id="departmentFilterCombo" promptText="All Departments" styleClass="filter-combo" prefWidth="150.0" />
                  <Label text="Status:" />
                  <ComboBox fx:id="statusFilterCombo" promptText="All Status" styleClass="filter-combo" prefWidth="120.0" />
                  <Button mnemonicParsing="false" onAction="#refreshStaffList" styleClass="refresh-button" text="🔄 Refresh" />
               </children>
               <padding>
                  <Insets bottom="15.0" left="20.0" right="20.0" top="5.0" />
               </padding>
            </HBox>

            <!-- Staff Table -->
            <TableView fx:id="staffTable" styleClass="data-table" VBox.vgrow="ALWAYS">
               <columns>
                  <TableColumn fx:id="idColumn" prefWidth="60.0" text="ID" />
                  <TableColumn fx:id="nameColumn" prefWidth="120.0" text="Name" />
                  <TableColumn fx:id="employeeIdColumn" prefWidth="100.0" text="Emp ID" />
                  <TableColumn fx:id="departmentColumn" prefWidth="100.0" text="Department" />
                  <TableColumn fx:id="positionColumn" prefWidth="120.0" text="Position" />
                  <TableColumn fx:id="contactColumn" prefWidth="120.0" text="Contact" />
                  <TableColumn fx:id="emailColumn" prefWidth="150.0" text="Email" />
                  <TableColumn fx:id="joinDateColumn" prefWidth="100.0" text="Join Date" />
                  <TableColumn fx:id="statusColumn" prefWidth="80.0" text="Status" />
                  <TableColumn fx:id="salaryColumn" prefWidth="100.0" text="Salary" />
                  <TableColumn fx:id="actionsColumn" prefWidth="120.0" text="Actions" />
               </columns>
               <VBox.margin>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
               </VBox.margin>
            </TableView>
         </children>
      </VBox>

      <!-- Staff Form Dialog -->
      <StackPane fx:id="staffFormDialog" styleClass="modal-overlay" visible="false">
         <children>
            <VBox styleClass="modal-dialog" spacing="15.0" maxWidth="600.0" maxHeight="700.0">
               <children>
                  <!-- Dialog Header -->
                  <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="modal-header">
                     <children>
                        <Label fx:id="dialogTitle" styleClass="modal-title" text="Add New Staff Member">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        <Region HBox.hgrow="ALWAYS" />
                        <Button mnemonicParsing="false" onAction="#cancelStaff" styleClass="close-button" text="✕" />
                     </children>
                  </HBox>

                  <!-- Form Content in ScrollPane -->
                  <ScrollPane fitToWidth="true" styleClass="form-scroll" VBox.vgrow="ALWAYS">
                     <content>
                        <VBox spacing="15.0" styleClass="form-container">
                           <children>
                              <!-- Basic Information -->
                              <Label styleClass="section-title" text="Basic Information" />
                              
                              <HBox spacing="15.0">
                                 <children>
                                    <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label text="Full Name *" />
                                          <TextField fx:id="nameField" promptText="Enter full name" styleClass="form-field" />
                                       </children>
                                    </VBox>
                                    <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label text="Employee ID *" />
                                          <TextField fx:id="employeeIdField" promptText="e.g., EMP001" styleClass="form-field" />
                                       </children>
                                    </VBox>
                                 </children>
                              </HBox>

                              <HBox spacing="15.0">
                                 <children>
                                    <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label text="Department *" />
                                          <ComboBox fx:id="departmentCombo" promptText="Select department" styleClass="form-field" maxWidth="Infinity" />
                                       </children>
                                    </VBox>
                                    <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label text="Position *" />
                                          <TextField fx:id="positionField" promptText="e.g., Head Chef" styleClass="form-field" />
                                       </children>
                                    </VBox>
                                 </children>
                              </HBox>

                              <!-- Contact Information -->
                              <Label styleClass="section-title" text="Contact Information" />
                              
                              <HBox spacing="15.0">
                                 <children>
                                    <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label text="Phone Number" />
                                          <TextField fx:id="phoneField" promptText="Enter phone number" styleClass="form-field" />
                                       </children>
                                    </VBox>
                                    <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label text="Email Address" />
                                          <TextField fx:id="emailField" promptText="Enter email address" styleClass="form-field" />
                                       </children>
                                    </VBox>
                                 </children>
                              </HBox>

                              <VBox spacing="5.0">
                                 <children>
                                    <Label text="Address" />
                                    <TextField fx:id="addressField" promptText="Enter full address" styleClass="form-field" />
                                 </children>
                              </VBox>

                              <!-- Employment Details -->
                              <Label styleClass="section-title" text="Employment Details" />
                              
                              <HBox spacing="15.0">
                                 <children>
                                    <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label text="Join Date" />
                                          <DatePicker fx:id="joinDatePicker" promptText="Select join date" styleClass="form-field" maxWidth="Infinity" />
                                       </children>
                                    </VBox>
                                    <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label text="Status" />
                                          <ComboBox fx:id="statusCombo" promptText="Select status" styleClass="form-field" maxWidth="Infinity" />
                                       </children>
                                    </VBox>
                                 </children>
                              </HBox>

                              <HBox spacing="15.0">
                                 <children>
                                    <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label text="Salary (₹)" />
                                          <TextField fx:id="salaryField" promptText="Enter salary amount" styleClass="form-field" />
                                       </children>
                                    </VBox>
                                    <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label text="Shift Timing" />
                                          <ComboBox fx:id="shiftCombo" promptText="Select shift" styleClass="form-field" maxWidth="Infinity" />
                                       </children>
                                    </VBox>
                                 </children>
                              </HBox>

                              <!-- Additional Notes -->
                              <VBox spacing="5.0">
                                 <children>
                                    <Label text="Notes" />
                                    <TextArea fx:id="notesArea" promptText="Additional notes or comments..." styleClass="form-field" prefRowCount="3" />
                                 </children>
                              </VBox>
                           </children>
                           <padding>
                              <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                           </padding>
                        </VBox>
                     </content>
                  </ScrollPane>

                  <!-- Dialog Footer -->
                  <HBox alignment="CENTER_RIGHT" spacing="10.0" styleClass="modal-footer">
                     <children>
                        <Button mnemonicParsing="false" onAction="#cancelStaff" styleClass="secondary-button" text="Cancel" />
                        <Button mnemonicParsing="false" onAction="#saveStaff" styleClass="primary-button" text="Save Staff" />
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>
            </VBox>
         </children>
      </StackPane>
   </children>
</StackPane>
