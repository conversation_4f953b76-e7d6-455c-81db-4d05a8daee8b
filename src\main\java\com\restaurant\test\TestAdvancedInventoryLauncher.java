package com.restaurant.test;

import com.restaurant.model.DatabaseManager;

/**
 * Simple test launcher for Advanced Inventory Management
 * This tests database initialization and basic functionality
 */
public class TestAdvancedInventoryLauncher {

    public static void main(String[] args) {
        System.out.println("=== Testing Advanced Inventory System ===");
        
        try {
            // Test database connection and initialization
            System.out.println("1. Testing database connection...");
            DatabaseManager.getConnection().close();
            System.out.println("   ✓ Database connection successful");
            
            // Test that tables exist
            System.out.println("2. Testing database tables...");
            try (var conn = DatabaseManager.getConnection();
                 var stmt = conn.createStatement()) {
                
                // Check if purchase_orders table exists
                var rs1 = stmt.executeQuery("SELECT name FROM sqlite_master WHERE type='table' AND name='purchase_orders'");
                if (rs1.next()) {
                    System.out.println("   ✓ purchase_orders table exists");
                } else {
                    System.out.println("   ✗ purchase_orders table missing");
                }
                
                // Check if internal_transfers table exists
                var rs2 = stmt.executeQuery("SELECT name FROM sqlite_master WHERE type='table' AND name='internal_transfers'");
                if (rs2.next()) {
                    System.out.println("   ✓ internal_transfers table exists");
                } else {
                    System.out.println("   ✗ internal_transfers table missing");
                }
            }
            
            System.out.println("3. Advanced Inventory System is ready!");
            System.out.println("   You can now:");
            System.out.println("   - Open Inventory Management from the main app");
            System.out.println("   - Click '📋 Purchase Orders & Transfers' button");
            System.out.println("   - Or run AdvancedInventoryLauncher directly");
            
        } catch (Exception e) {
            System.err.println("Error testing Advanced Inventory System: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== Test Completed ===");
    }
}
