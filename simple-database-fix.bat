@echo off
echo 🔧 SIMPLE DATABASE FIX - NO COMPILATION NEEDED...
echo.

echo STEP 1: Deleting old database...
if exist restaurant.db (
    del restaurant.db
    echo ✅ Deleted restaurant.db
) else (
    echo ℹ️ No existing database found
)

echo.
echo STEP 2: Creating database with SQLite command line...
echo.

echo Creating users table and admin user directly...

echo .timeout 3000 > create_admin.sql
echo CREATE TABLE users ( >> create_admin.sql
echo     id INTEGER PRIMARY KEY AUTOINCREMENT, >> create_admin.sql
echo     username TEXT UNIQUE NOT NULL, >> create_admin.sql
echo     password TEXT NOT NULL, >> create_admin.sql
echo     role TEXT NOT NULL >> create_admin.sql
echo ); >> create_admin.sql
echo. >> create_admin.sql
echo CREATE TABLE online_orders ( >> create_admin.sql
echo     id INTEGER PRIMARY KEY AUTOINCREMENT, >> create_admin.sql
echo     order_id TEXT UNIQUE NOT NULL, >> create_admin.sql
echo     platform TEXT NOT NULL, >> create_admin.sql
echo     customer_name TEXT NOT NULL, >> create_admin.sql
echo     customer_phone TEXT, >> create_admin.sql
echo     delivery_address TEXT, >> create_admin.sql
echo     status TEXT NOT NULL, >> create_admin.sql
echo     total_amount REAL NOT NULL, >> create_admin.sql
echo     order_time TEXT NOT NULL, >> create_admin.sql
echo     status_updated_time TEXT, >> create_admin.sql
echo     special_instructions TEXT, >> create_admin.sql
echo     estimated_prep_time INTEGER >> create_admin.sql
echo ); >> create_admin.sql
echo. >> create_admin.sql
echo -- Insert admin user with BCrypt hash for 'admin123' >> create_admin.sql
echo INSERT INTO users (username, password, role) VALUES ('admin', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Uo0aBOBjXoXebXeQABkc4ILoLGxjvO', 'ADMIN'); >> create_admin.sql
echo. >> create_admin.sql
echo -- Insert staff user with BCrypt hash for 'staff123' >> create_admin.sql
echo INSERT INTO users (username, password, role) VALUES ('staff', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'STAFF'); >> create_admin.sql
echo. >> create_admin.sql
echo .quit >> create_admin.sql

echo ✅ SQL script created
echo.

echo STEP 3: Executing SQL script...

sqlite3 restaurant.db < create_admin.sql

if %ERRORLEVEL% equ 0 (
    echo ✅ Database created successfully
) else (
    echo ❌ Database creation failed
    echo.
    echo Trying alternative method...
    echo.
    
    echo Creating database manually...
    sqlite3 restaurant.db "CREATE TABLE users (id INTEGER PRIMARY KEY AUTOINCREMENT, username TEXT UNIQUE NOT NULL, password TEXT NOT NULL, role TEXT NOT NULL);"
    sqlite3 restaurant.db "CREATE TABLE online_orders (id INTEGER PRIMARY KEY AUTOINCREMENT, order_id TEXT UNIQUE NOT NULL, platform TEXT NOT NULL, customer_name TEXT NOT NULL, customer_phone TEXT, delivery_address TEXT, status TEXT NOT NULL, total_amount REAL NOT NULL, order_time TEXT NOT NULL, status_updated_time TEXT, special_instructions TEXT, estimated_prep_time INTEGER);"
    sqlite3 restaurant.db "INSERT INTO users (username, password, role) VALUES ('admin', '$2a$10$N9qo8uLOickgx2ZMRZoMye.Uo0aBOBjXoXebXeQABkc4ILoLGxjvO', 'ADMIN');"
    sqlite3 restaurant.db "INSERT INTO users (username, password, role) VALUES ('staff', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 'STAFF');"
    
    echo ✅ Manual database creation completed
)

echo.
echo STEP 4: Verifying database...

echo Checking users table:
sqlite3 restaurant.db "SELECT id, username, role FROM users;"

echo.
echo STEP 5: Cleaning up...
if exist create_admin.sql del create_admin.sql

echo.
echo 🎉 DATABASE SETUP COMPLETE!
echo.
echo 🔑 LOGIN CREDENTIALS:
echo Username: admin
echo Password: admin123
echo Role: ADMIN (select from dropdown)
echo.
echo 🔑 BACKUP CREDENTIALS:
echo Username: staff
echo Password: staff123
echo Role: STAFF (select from dropdown)
echo.

echo STEP 6: Starting application...
echo.

java -Dprism.order=sw ^
     -Dprism.allowhidpi=false ^
     -Djavafx.animation.pulse=60 ^
     -Djava.awt.headless=false ^
     -Dsqlite.purejava=false ^
     -Xms512m ^
     -Xmx2g ^
     -XX:+UseG1GC ^
     -XX:MaxGCPauseMillis=200 ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics,javafx.media ^
     -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\pdfbox\2.0.27\pdfbox-2.0.27.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\fontbox\2.0.27\fontbox-2.0.27.jar;%USERPROFILE%\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2.jar" ^
     com.restaurant.RestaurantApp

echo.
echo 🎉 SIMPLE DATABASE FIX COMPLETE!
echo.
echo LOGIN INSTRUCTIONS:
echo 1. Username: admin
echo 2. Password: admin123
echo 3. Role: ADMIN (MUST select from dropdown)
echo 4. Click "Login to Dashboard →"
echo 5. Click "🍽️ Finish List" to test MP3 audio
echo.
echo If login still fails, the issue might be in the authentication code.
echo The database now has the correct admin user with proper password hash.
echo.

pause
