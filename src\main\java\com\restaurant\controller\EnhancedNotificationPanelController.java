package com.restaurant.controller;

import com.restaurant.model.*;
import com.restaurant.service.NotificationService;
import com.restaurant.util.CentralizedNotificationManager;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.*;

import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;

import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;


/**
 * Enhanced Notification Panel Controller with Order Acceptance/Rejection
 * This is where users accept or reject orders - ALL SOUNDS MANAGED HERE
 */
public class EnhancedNotificationPanelController implements Initializable {
    
    // FXML Components
    @FXML private ScrollPane notificationScrollPane;
    @FXML private VBox notificationContainer;
    @FXML private Label unreadCountLabel;
    @FXML private Label lastUpdateLabel;
    @FXML private Button refreshBtn;
    @FXML private Button clearAllBtn;
    @FXML private Button markAllReadBtn;
    
    // Filter tabs
    @FXML private Button allFilterBtn;
    @FXML private Button ordersFilterBtn;
    @FXML private Button urgentFilterBtn;
    @FXML private Button systemFilterBtn;
    
    // Status indicators
    @FXML private Label swiggyStatusLabel;
    @FXML private Label zomatoStatusLabel;
    @FXML private Label systemStatusLabel;
    
    // Services
    private NotificationService notificationService;
    private CentralizedNotificationManager soundManager;
    private ScheduledExecutorService updateTimer;
    
    // Data
    private ObservableList<Notification> currentNotifications = FXCollections.observableArrayList();
    private Map<String, OnlineOrder> pendingOrders = new HashMap<>();
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("EnhancedNotificationPanelController: Initializing...");
        
        // Initialize services
        notificationService = NotificationService.getInstance();
        soundManager = CentralizedNotificationManager.getInstance();
        
        // Setup UI
        setupNotificationListener();
        setupAutoRefresh();
        loadNotifications();
        
        // Initialize status
        updateUnreadCount();
        updateLastUpdateTime();
        updateStatusIndicators();
        
        System.out.println("EnhancedNotificationPanelController: Initialization complete");
        System.out.println("🔔 ORDER ACCEPTANCE/REJECTION READY - All sounds centralized here");
    }
    
    private void setupNotificationListener() {
        // Listen for new notifications
        notificationService.addNotificationListener(this::handleNewNotification);
    }
    
    private void setupAutoRefresh() {
        updateTimer = Executors.newSingleThreadScheduledExecutor();
        updateTimer.scheduleAtFixedRate(() -> {
            Platform.runLater(() -> {
                loadNotifications();
                updateUnreadCount();
                updateLastUpdateTime();
            });
        }, 10, 10, TimeUnit.SECONDS);
    }
    
    private void handleNewNotification(Notification notification) {
        Platform.runLater(() -> {
            // Handle different notification types with appropriate sounds
            switch (notification.getType()) {
                case ONLINE_ORDER:
                    handleNewOnlineOrder(notification);
                    break;
                case KOT_ALERT:
                    soundManager.notifyInfo("KOT Alert", notification.getMessage());
                    break;
                case SYSTEM:
                    soundManager.notifyInfo("System Alert", notification.getMessage());
                    break;
                default:
                    soundManager.notifyInfo(notification.getTitle(), notification.getMessage());
                    break;
            }
            
            // Refresh display
            loadNotifications();
            updateUnreadCount();
        });
    }
    
    /**
     * Handle new online order notification with platform-specific sounds
     */
    private void handleNewOnlineOrder(Notification notification) {
        try {
            // Extract order information
            String platform = notification.getSource(); // "Swiggy", "Zomato", etc.
            String orderNumber = extractOrderNumber(notification.getMessage());
            
            // Create mock order for sound management
            OnlineOrder order = createOrderFromNotification(notification, orderNumber, platform);
            pendingOrders.put(orderNumber, order);
            
            // Trigger platform-specific sound with continuous ringing
            if ("Swiggy".equalsIgnoreCase(platform)) {
                soundManager.notifyNewSwiggyOrder(order);
                System.out.println("🟠 NEW SWIGGY ORDER in notifications: " + orderNumber);
            } else if ("Zomato".equalsIgnoreCase(platform)) {
                soundManager.notifyNewZomatoOrder(order);
                System.out.println("🔴 NEW ZOMATO ORDER in notifications: " + orderNumber);
            } else if ("Wok Ka Tadka".equalsIgnoreCase(platform)) {
                soundManager.notifyNewWokKaTadkaOrder(order);
                System.out.println("🟢 NEW WOK KA TADKA ORDER in notifications: " + orderNumber);
            } else {
                soundManager.notifyInfo("New Online Order",
                    "Order " + orderNumber + " from " + platform);
                System.out.println("💻 NEW ONLINE ORDER in notifications: " + orderNumber);
            }
            
            // Update status indicators
            updateStatusIndicators();
            
        } catch (Exception e) {
            System.err.println("Error handling new online order: " + e.getMessage());
            soundManager.notifyError("Order Processing Error", 
                "Failed to process new order notification");
        }
    }
    
    private void loadNotifications() {
        try {
            currentNotifications.clear();
            currentNotifications.addAll(notificationService.getNotifications());
            displayNotifications();
        } catch (Exception e) {
            System.err.println("Error loading notifications: " + e.getMessage());
        }
    }
    
    private void displayNotifications() {
        notificationContainer.getChildren().clear();
        
        if (currentNotifications.isEmpty()) {
            Label noNotificationsLabel = new Label("No notifications");
            noNotificationsLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #7f8c8d; -fx-padding: 20px;");
            notificationContainer.getChildren().add(noNotificationsLabel);
            return;
        }
        
        for (Notification notification : currentNotifications) {
            VBox notificationCard = createNotificationCard(notification);
            notificationContainer.getChildren().add(notificationCard);
        }
    }
    
    private VBox createNotificationCard(Notification notification) {
        VBox card = new VBox(10);
        card.setPadding(new Insets(15));
        card.setStyle(getCardStyle(notification));
        
        // Header with icon, title, and timestamp
        HBox header = createNotificationHeader(notification);
        
        // Message content
        Label messageLabel = new Label(notification.getMessage());
        messageLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #2c3e50; -fx-wrap-text: true;");
        messageLabel.setWrapText(true);
        
        // Action buttons for all actionable notifications
        HBox actionBox = createActionButtons(notification);

        card.getChildren().addAll(header, messageLabel);
        if (actionBox.getChildren().size() > 0) {
            card.getChildren().add(actionBox);
        }
        
        return card;
    }
    
    private HBox createNotificationHeader(Notification notification) {
        HBox header = new HBox(10);
        header.setAlignment(Pos.CENTER_LEFT);
        
        // Type icon
        Label iconLabel = new Label(notification.getType().getIcon());
        iconLabel.setStyle("-fx-font-size: 16px;");
        
        // Title
        Label titleLabel = new Label(notification.getTitle());
        titleLabel.setFont(Font.font("System", FontWeight.BOLD, 14));
        titleLabel.setStyle("-fx-text-fill: " + getPriorityColor(notification.getPriority()) + ";");
        
        // Source platform
        Label sourceLabel = new Label(notification.getSource());
        sourceLabel.setStyle(getPlatformStyle(notification.getSource()));
        
        // Timestamp
        Label timeLabel = new Label(notification.getTimestamp().format(
            DateTimeFormatter.ofPattern("HH:mm")));
        timeLabel.setStyle("-fx-font-size: 10px; -fx-text-fill: #7f8c8d;");
        
        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);
        
        header.getChildren().addAll(iconLabel, titleLabel, sourceLabel, spacer, timeLabel);
        return header;
    }

    /**
     * Create action buttons for all actionable notifications
     */
    private HBox createActionButtons(Notification notification) {
        HBox actionBox = new HBox(10);
        actionBox.setAlignment(Pos.CENTER_LEFT);

        if (!notification.isActionable()) {
            return actionBox;
        }

        switch (notification.getType()) {
            case ONLINE_ORDER:
                String orderNumber = extractOrderNumber(notification.getMessage());
                String platform = notification.getSource();

                // ACCEPT BUTTON
                Button acceptBtn = new Button("✅ Accept Order");
                acceptBtn.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; " +
                                 "-fx-font-weight: bold; -fx-padding: 8px 16px; -fx-background-radius: 5px;");
                acceptBtn.setOnAction(e -> acceptOrder(orderNumber, platform, notification));

                // REJECT BUTTON
                Button rejectBtn = new Button("❌ Reject Order");
                rejectBtn.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; " +
                                 "-fx-font-weight: bold; -fx-padding: 8px 16px; -fx-background-radius: 5px;");
                rejectBtn.setOnAction(e -> rejectOrder(orderNumber, platform, notification));

                actionBox.getChildren().addAll(acceptBtn, rejectBtn);
                break;

            case KOT_ALERT:
                Button viewKOTBtn = new Button("👁️ View KOT");
                Button printKOTBtn = new Button("🖨️ Print");
                viewKOTBtn.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; " +
                                  "-fx-font-weight: bold; -fx-padding: 8px 16px; -fx-background-radius: 5px;");
                printKOTBtn.setStyle("-fx-background-color: #f39c12; -fx-text-fill: white; " +
                                   "-fx-font-weight: bold; -fx-padding: 8px 16px; -fx-background-radius: 5px;");
                viewKOTBtn.setOnAction(e -> handleViewKOT(notification));
                printKOTBtn.setOnAction(e -> handlePrintKOT(notification));
                actionBox.getChildren().addAll(viewKOTBtn, printKOTBtn);
                break;

            case BILLING:
                Button generateBillBtn = new Button("💰 Generate Bill");
                generateBillBtn.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; " +
                                       "-fx-font-weight: bold; -fx-padding: 8px 16px; -fx-background-radius: 5px;");
                generateBillBtn.setOnAction(e -> handleGenerateBill(notification));
                actionBox.getChildren().add(generateBillBtn);
                break;

            case SYSTEM:
                Button acknowledgeBtn = new Button("✅ Acknowledge");
                acknowledgeBtn.setStyle("-fx-background-color: #95a5a6; -fx-text-fill: white; " +
                                      "-fx-font-weight: bold; -fx-padding: 8px 16px; -fx-background-radius: 5px;");
                acknowledgeBtn.setOnAction(e -> handleAcknowledge(notification));
                actionBox.getChildren().add(acknowledgeBtn);
                break;

            case KITCHEN:
                Button readyBtn = new Button("✅ Ready");
                Button delayBtn = new Button("⏰ Delay");
                readyBtn.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; " +
                                "-fx-font-weight: bold; -fx-padding: 8px 16px; -fx-background-radius: 5px;");
                delayBtn.setStyle("-fx-background-color: #f39c12; -fx-text-fill: white; " +
                                "-fx-font-weight: bold; -fx-padding: 8px 16px; -fx-background-radius: 5px;");
                readyBtn.setOnAction(e -> handleKitchenReady(notification));
                delayBtn.setOnAction(e -> handleKitchenDelay(notification));
                actionBox.getChildren().addAll(readyBtn, delayBtn);
                break;

            default:
                // No specific actions for other types
                break;
        }

        return actionBox;
    }

    /**
     * ACCEPT ORDER - Stop ringing and move to preparation
     */
    private void acceptOrder(String orderNumber, String platform, Notification notification) {
        try {
            System.out.println("✅ ACCEPTING ORDER: " + orderNumber + " from " + platform);

            // Stop continuous ringing
            soundManager.notifyOrderAccepted(orderNumber, platform);

            // Remove from pending orders
            pendingOrders.remove(orderNumber);

            // Mark notification as read and non-actionable
            notification.setRead(true);
            notification.setActionable(false);

            // Show success notification
            soundManager.notifySuccess("Order Accepted",
                "Order " + orderNumber + " (" + platform + ") accepted and sent to kitchen");

            // Refresh display
            loadNotifications();
            updateUnreadCount();
            updateStatusIndicators();

            System.out.println("✅ Order " + orderNumber + " successfully accepted");

        } catch (Exception e) {
            System.err.println("Error accepting order: " + e.getMessage());
            soundManager.notifyError("Accept Failed",
                "Failed to accept order " + orderNumber + ": " + e.getMessage());
        }
    }

    /**
     * REJECT ORDER - Stop ringing and mark as rejected
     */
    private void rejectOrder(String orderNumber, String platform, Notification notification) {
        try {
            // Show confirmation dialog
            Alert confirmation = new Alert(Alert.AlertType.CONFIRMATION);
            confirmation.setTitle("Reject Order");
            confirmation.setHeaderText("Reject Order " + orderNumber);
            confirmation.setContentText("Are you sure you want to reject this " + platform + " order?\n" +
                                      "This action cannot be undone.");

            confirmation.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    System.out.println("❌ REJECTING ORDER: " + orderNumber + " from " + platform);

                    // Stop continuous ringing and play rejection sound
                    soundManager.notifyOrderRejected(orderNumber, platform);

                    // Remove from pending orders
                    pendingOrders.remove(orderNumber);

                    // Mark notification as read and non-actionable
                    notification.setRead(true);
                    notification.setActionable(false);

                    // Show rejection confirmation (sound already played above)
                    System.out.println("✅ Order " + orderNumber + " rejection confirmed");

                    // Refresh display
                    loadNotifications();
                    updateUnreadCount();
                    updateStatusIndicators();

                    System.out.println("❌ Order " + orderNumber + " successfully rejected");
                }
            });

        } catch (Exception e) {
            System.err.println("Error rejecting order: " + e.getMessage());
            soundManager.notifyError("Reject Failed",
                "Failed to reject order " + orderNumber + ": " + e.getMessage());
        }
    }

    // KOT Alert handlers
    private void handleViewKOT(Notification notification) {
        try {
            notification.setRead(true);
            System.out.println("👁️ Viewing KOT: " + notification.getTitle());
            soundManager.notifyInfo("KOT Viewed", "KOT details opened for " + notification.getTitle());
            loadNotifications();
            updateUnreadCount();
        } catch (Exception e) {
            System.err.println("Error viewing KOT: " + e.getMessage());
            soundManager.notifyError("View Failed", "Failed to view KOT details");
        }
    }

    private void handlePrintKOT(Notification notification) {
        try {
            notification.setRead(true);
            notification.setActionable(false);
            System.out.println("🖨️ Printing KOT: " + notification.getTitle());
            soundManager.notifySuccess("KOT Printed", "KOT sent to kitchen printer");
            loadNotifications();
            updateUnreadCount();
        } catch (Exception e) {
            System.err.println("Error printing KOT: " + e.getMessage());
            soundManager.notifyError("Print Failed", "Failed to print KOT");
        }
    }

    // Billing handlers
    private void handleGenerateBill(Notification notification) {
        try {
            notification.setRead(true);
            notification.setActionable(false);
            System.out.println("💰 Generating bill: " + notification.getTitle());
            soundManager.notifySuccess("Bill Generated", "Bill generated for " + notification.getTitle());
            loadNotifications();
            updateUnreadCount();
        } catch (Exception e) {
            System.err.println("Error generating bill: " + e.getMessage());
            soundManager.notifyError("Bill Failed", "Failed to generate bill");
        }
    }

    // System handlers
    private void handleAcknowledge(Notification notification) {
        try {
            notification.setRead(true);
            notification.setActionable(false);
            System.out.println("✅ Acknowledged: " + notification.getTitle());
            soundManager.notifyInfo("Acknowledged", "System notification acknowledged");
            loadNotifications();
            updateUnreadCount();
        } catch (Exception e) {
            System.err.println("Error acknowledging notification: " + e.getMessage());
            soundManager.notifyError("Acknowledge Failed", "Failed to acknowledge notification");
        }
    }

    // Kitchen handlers
    private void handleKitchenReady(Notification notification) {
        try {
            notification.setRead(true);
            notification.setActionable(false);
            System.out.println("✅ Kitchen item ready: " + notification.getTitle());
            soundManager.notifySuccess("Item Ready", "Kitchen item marked as ready");
            loadNotifications();
            updateUnreadCount();
        } catch (Exception e) {
            System.err.println("Error marking kitchen item ready: " + e.getMessage());
            soundManager.notifyError("Ready Failed", "Failed to mark item as ready");
        }
    }

    private void handleKitchenDelay(Notification notification) {
        try {
            notification.setRead(true);
            System.out.println("⏰ Kitchen item delayed: " + notification.getTitle());
            soundManager.notifyWarning("Item Delayed", "Kitchen item marked as delayed");
            loadNotifications();
            updateUnreadCount();
        } catch (Exception e) {
            System.err.println("Error handling kitchen delay: " + e.getMessage());
            soundManager.notifyError("Delay Failed", "Failed to mark item as delayed");
        }
    }
    

    

    

    

    
    // Helper methods
    private String extractOrderNumber(String message) {
        // Extract order number from message like "Order #SW12345 - ₹250.00"
        try {
            if (message.contains("Order #")) {
                int start = message.indexOf("#") + 1;
                int end = message.indexOf(" ", start);
                if (end == -1) end = message.length();
                return message.substring(start, end);
            }
        } catch (Exception e) {
            System.err.println("Error extracting order number: " + e.getMessage());
        }
        return "UNKNOWN" + System.currentTimeMillis();
    }
    
    private OnlineOrder createOrderFromNotification(Notification notification, String orderNumber, String platform) {
        OnlineOrder order = new OnlineOrder();
        order.setOrderId(orderNumber);
        order.setPlatform(OnlineOrder.Platform.valueOf(platform.toUpperCase()));
        order.setCustomerName("Customer " + orderNumber);
        order.setStatus(OnlineOrder.OrderStatus.NEW);
        order.setOrderTime(LocalDateTime.now());
        
        // Extract amount from message
        try {
            String message = notification.getMessage();
            if (message.contains("₹")) {
                String amountStr = message.substring(message.indexOf("₹") + 1);
                order.setTotalAmount(Double.parseDouble(amountStr));
            } else {
                order.setTotalAmount(250.0); // Default amount
            }
        } catch (Exception e) {
            order.setTotalAmount(250.0); // Default amount
        }
        
        return order;
    }
    

    
    private String getCardStyle(Notification notification) {
        String baseStyle = "-fx-background-color: white; -fx-border-radius: 8px; -fx-background-radius: 8px; " +
                          "-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2); -fx-border-width: 2px; ";
        
        if (!notification.isRead()) {
            baseStyle += "-fx-border-color: #3498db; -fx-background-color: #f8f9fa;";
        } else {
            baseStyle += "-fx-border-color: #e9ecef;";
        }
        
        return baseStyle;
    }
    
    private String getPriorityColor(Notification.Priority priority) {
        switch (priority) {
            case URGENT: return "#e74c3c";
            case HIGH: return "#f39c12";
            case MEDIUM: return "#3498db";
            case LOW: return "#27ae60";
            default: return "#2c3e50";
        }
    }
    
    private String getPlatformStyle(String platform) {
        switch (platform.toLowerCase()) {
            case "swiggy":
                return "-fx-background-color: #ff6b35; -fx-text-fill: white; -fx-padding: 2px 8px; " +
                       "-fx-background-radius: 12px; -fx-font-size: 10px; -fx-font-weight: bold;";
            case "zomato":
                return "-fx-background-color: #e23744; -fx-text-fill: white; -fx-padding: 2px 8px; " +
                       "-fx-background-radius: 12px; -fx-font-size: 10px; -fx-font-weight: bold;";
            default:
                return "-fx-background-color: #34495e; -fx-text-fill: white; -fx-padding: 2px 8px; " +
                       "-fx-background-radius: 12px; -fx-font-size: 10px; -fx-font-weight: bold;";
        }
    }
    
    private void updateUnreadCount() {
        long unreadCount = currentNotifications.stream()
            .filter(n -> !n.isRead())
            .count();
        
        Platform.runLater(() -> {
            if (unreadCountLabel != null) {
                unreadCountLabel.setText(String.valueOf(unreadCount));
            }
        });
    }
    
    private void updateLastUpdateTime() {
        Platform.runLater(() -> {
            if (lastUpdateLabel != null) {
                lastUpdateLabel.setText("Last updated: " + 
                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
            }
        });
    }
    
    private void updateStatusIndicators() {
        Platform.runLater(() -> {
            long swiggyPending = pendingOrders.values().stream()
                .filter(o -> o.getPlatform() == OnlineOrder.Platform.SWIGGY)
                .count();
            long zomatoPending = pendingOrders.values().stream()
                .filter(o -> o.getPlatform() == OnlineOrder.Platform.ZOMATO)
                .count();
            
            if (swiggyStatusLabel != null) {
                swiggyStatusLabel.setText("Pending: " + swiggyPending);
            }
            if (zomatoStatusLabel != null) {
                zomatoStatusLabel.setText("Pending: " + zomatoPending);
            }
            if (systemStatusLabel != null) {
                systemStatusLabel.setText("Total Pending: " + pendingOrders.size());
            }
        });
    }
    
    // FXML Event Handlers
    @FXML
    private void refreshNotifications() {
        loadNotifications();
        updateUnreadCount();
        updateLastUpdateTime();
        soundManager.notifyInfo("Notifications Refreshed", "Notification list updated");
    }
    
    @FXML
    private void clearAllNotifications() {
        Alert confirmation = new Alert(Alert.AlertType.CONFIRMATION);
        confirmation.setTitle("Clear All Notifications");
        confirmation.setHeaderText("Clear All Notifications");
        confirmation.setContentText("Are you sure you want to clear all notifications?\n" +
                                  "This will also stop all pending order alerts.");
        
        confirmation.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                currentNotifications.clear();
                pendingOrders.clear();
                displayNotifications();
                updateUnreadCount();
                updateStatusIndicators();
                soundManager.notifySuccess("Notifications Cleared", "All notifications cleared");
            }
        });
    }
    
    @FXML
    private void markAllAsRead() {
        currentNotifications.forEach(n -> n.setRead(true));
        displayNotifications();
        updateUnreadCount();
        soundManager.notifyInfo("Marked as Read", "All notifications marked as read");
    }
    
    public void cleanup() {
        if (updateTimer != null && !updateTimer.isShutdown()) {
            updateTimer.shutdown();
        }
        System.out.println("EnhancedNotificationPanelController: Cleanup complete");
    }
}
