package com.restaurant.controller;

import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.input.KeyCode;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.text.Font;
import javafx.stage.Stage;
import com.restaurant.service.OrderManager;
import com.restaurant.util.SceneEventHandlerManager;
import com.restaurant.util.UniversalNavigationManager;

import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.Collectors;
import com.restaurant.util.MenuShortcuts;

public class MenuSelectionController implements Initializable {
    
    // FXML Components
    @FXML private Label tableLabel;
    @FXML private Button dineInTab, deliveryTab, pickupTab;
    @FXML private TextField searchField;
    @FXML private ListView<String> categoriesList;
    @FXML private GridPane menuGrid;
    @FXML private Label itemCountLabel;
    @FXML private VBox orderItemsContainer;
    @FXML private Label subtotalLabel, gstLabel, serviceChargeLabel, totalLabel, orderDiscountLabel;
    @FXML private Button applyOrderDiscountBtn;
    
    // Data
    private String selectedTable = "Table 1";
    private int currentTableNumber = 1;
    private String orderType = "Dine In";

    // Store stage reference for reliable navigation
    private Stage currentStage;
    private ObservableList<OrderItem> orderItems = FXCollections.observableArrayList();
    private List<MenuItem> menuItems = new ArrayList<>();
    private double orderDiscountPercent = 0.0; // Order-level discount percentage
    private OrderManager orderManager = OrderManager.getInstance();

    // Search navigation
    private List<VBox> currentSearchResults = new ArrayList<>();
    private int selectedSearchIndex = -1;
    private boolean isSearchNavigationMode = false;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("🚀 MenuSelectionController.initialize() called - Setting up Ctrl+K handler");
        setupMenuItems();
        setupCategories();
        setupOrderCalculations();
        setupEscapeKeyHandler();
        setupRealTimeSearch(); // Add real-time search functionality
        updateOrderDisplay();

        // Additional keyboard handler setup
        setupAdditionalKeyboardHandlers();

        System.out.println("✅ MenuSelectionController initialization complete - Ctrl+K should be working");

        // Add a test button to verify the billing dialog works
        Platform.runLater(() -> {
            try {
                Thread.sleep(500); // Wait for UI to be ready
                addTestButton();
                captureStageReference(); // Capture stage for reliable navigation
                setupUniversalNavigation(); // Setup universal navigation
            } catch (Exception e) {
                System.err.println("❌ Error adding test button: " + e.getMessage());
            }
        });

        // Register with universal navigation manager
        UniversalNavigationManager.getInstance().setCurrentController("MenuSelectionController");
    }

    /**
     * Capture stage reference for reliable navigation
     */
    private void captureStageReference() {
        try {
            // Try multiple UI elements to find the stage
            if (tableLabel != null && tableLabel.getScene() != null && tableLabel.getScene().getWindow() != null) {
                currentStage = (Stage) tableLabel.getScene().getWindow();
                System.out.println("MenuSelectionController: Stage captured via tableLabel");
                return;
            }
            if (menuGrid != null && menuGrid.getScene() != null && menuGrid.getScene().getWindow() != null) {
                currentStage = (Stage) menuGrid.getScene().getWindow();
                System.out.println("MenuSelectionController: Stage captured via menuGrid");
                return;
            }
            if (dineInTab != null && dineInTab.getScene() != null && dineInTab.getScene().getWindow() != null) {
                currentStage = (Stage) dineInTab.getScene().getWindow();
                System.out.println("MenuSelectionController: Stage captured via dineInTab");
                return;
            }

            System.out.println("MenuSelectionController: Could not capture stage reference");
        } catch (Exception e) {
            System.err.println("MenuSelectionController: Error capturing stage reference: " + e.getMessage());
        }
    }

    /**
     * Setup universal navigation with ESC key handling
     */
    private void setupUniversalNavigation() {
        try {
            if (tableLabel != null && tableLabel.getScene() != null) {
                Scene scene = tableLabel.getScene();

                // Add ESC key handler for universal navigation
                scene.setOnKeyPressed(event -> {
                    if (event.getCode() == KeyCode.ESCAPE) {
                        event.consume();
                        UniversalNavigationManager.getInstance().handleEscapeKey();
                    }
                });

                System.out.println("MenuSelectionController: Universal navigation setup complete");
            }
        } catch (Exception e) {
            System.err.println("MenuSelectionController: Error setting up universal navigation: " + e.getMessage());
        }
    }

    /**
     * Add a test button to verify billing dialog works
     */
    private void addTestButton() {
        try {
            if (orderItemsContainer != null) {
                Button testButton = new Button("🧪 Test Ctrl+K (Click Me!)");
                testButton.setStyle("-fx-background-color: #ff6b6b; -fx-text-fill: white; -fx-padding: 10 20; -fx-font-size: 14px; -fx-font-weight: bold;");
                testButton.setOnAction(e -> {
                    System.out.println("🧪 Test button clicked - calling openBillingAndKOT()");
                    openBillingAndKOT();
                });

                // Add to the top of the order container
                orderItemsContainer.getChildren().add(0, testButton);
                System.out.println("✅ Test button added successfully");
            } else {
                System.out.println("❌ orderItemsContainer is null - cannot add test button");
            }
        } catch (Exception e) {
            System.err.println("❌ Error adding test button: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void setupAdditionalKeyboardHandlers() {
        // Set up keyboard handlers on multiple nodes to ensure they work
        Platform.runLater(() -> {
            try {
                // Wait a bit for the scene to be fully loaded
                Thread.sleep(200);

                // Try setting up on various nodes
                if (menuGrid != null && menuGrid.getScene() != null) {
                    System.out.println("Setting keyboard handler on menuGrid scene");
                    menuGrid.getScene().setOnKeyPressed(this::handleGlobalKeyPress);
                }

                if (orderItemsContainer != null && orderItemsContainer.getScene() != null) {
                    System.out.println("Setting keyboard handler on orderItemsContainer scene");
                    orderItemsContainer.getScene().setOnKeyPressed(this::handleGlobalKeyPress);
                }

                // Also try setting on the root container
                if (menuGrid != null && menuGrid.getParent() != null) {
                    System.out.println("Setting keyboard handler on menuGrid parent");
                    menuGrid.getParent().setOnKeyPressed(this::handleGlobalKeyPress);
                }

            } catch (Exception e) {
                System.err.println("Error in additional keyboard setup: " + e.getMessage());
            }
        });

        // Also try immediate setup with a timer
        setupKeyboardHandlerWithTimer();
    }

    private void setupKeyboardHandlerWithTimer() {
        // Try setting up keyboard handler every 500ms for 5 seconds
        final int[] attempts = {0};
        final int maxAttempts = 10;

        javafx.animation.Timeline timeline = new javafx.animation.Timeline(
            new javafx.animation.KeyFrame(javafx.util.Duration.millis(500), e -> {
                attempts[0]++;
                System.out.println("Keyboard handler setup attempt " + attempts[0]);

                boolean success = trySetupKeyboardHandler();
                if (success) {
                    System.out.println("Keyboard handler setup successful on attempt " + attempts[0]);
                    return; // Stop trying
                }

                if (attempts[0] >= maxAttempts) {
                    System.out.println("Keyboard handler setup failed after " + maxAttempts + " attempts");
                }
            })
        );
        timeline.setCycleCount(maxAttempts);
        timeline.play();
    }

    private boolean trySetupKeyboardHandler() {
        try {
            // Try multiple approaches
            if (menuGrid != null && menuGrid.getScene() != null) {
                menuGrid.getScene().setOnKeyPressed(this::handleGlobalKeyPress);
                System.out.println("✅ Keyboard handler set on menuGrid.getScene()");
                return true;
            }

            if (searchField != null && searchField.getScene() != null) {
                searchField.getScene().setOnKeyPressed(this::handleGlobalKeyPress);
                System.out.println("✅ Keyboard handler set on searchField.getScene()");
                return true;
            }

            if (orderItemsContainer != null && orderItemsContainer.getScene() != null) {
                orderItemsContainer.getScene().setOnKeyPressed(this::handleGlobalKeyPress);
                System.out.println("✅ Keyboard handler set on orderItemsContainer.getScene()");
                return true;
            }

            return false;
        } catch (Exception e) {
            System.err.println("Error in trySetupKeyboardHandler: " + e.getMessage());
            return false;
        }
    }

    private void setupRealTimeSearch() {
        if (searchField != null) {
            // Set up real-time search as user types
            searchField.textProperty().addListener((observable, oldValue, newValue) -> {
                System.out.println("Search text changed: '" + oldValue + "' -> '" + newValue + "'");
                // Filter menu items in real-time
                filterMenuItemsRealTime(newValue);

                // Reset search navigation when text changes
                resetSearchNavigation();
            });

            // Add keyboard navigation for search results
            searchField.setOnKeyPressed(event -> {
                handleSearchKeyNavigation(event);
            });

            System.out.println("Real-time search functionality setup complete");
        } else {
            System.err.println("ERROR: searchField is null in MenuSelectionController!");
        }
    }

    /**
     * Handle keyboard navigation in search field
     */
    private void handleSearchKeyNavigation(javafx.scene.input.KeyEvent event) {
        switch (event.getCode()) {
            case DOWN:
            case RIGHT:
                if (!currentSearchResults.isEmpty()) {
                    navigateSearchResults(1);
                    event.consume();
                }
                break;

            case UP:
            case LEFT:
                if (!currentSearchResults.isEmpty()) {
                    navigateSearchResults(-1);
                    event.consume();
                }
                break;

            case ENTER:
                if (selectedSearchIndex >= 0 && selectedSearchIndex < currentSearchResults.size()) {
                    selectCurrentSearchResultAndKeepSearchOpen();
                    event.consume();
                } else if (!currentSearchResults.isEmpty()) {
                    // If no specific item selected, select first result
                    selectedSearchIndex = 0;
                    selectCurrentSearchResultAndKeepSearchOpen();
                    event.consume();
                }
                break;

            case ESCAPE:
                // Clear search and reset navigation
                searchField.clear();
                resetSearchNavigation();
                event.consume();
                break;

            default:
                // For other keys, reset navigation mode only if not arrow keys
                if (!event.getCode().isArrowKey() && !event.getCode().isNavigationKey()) {
                    resetSearchNavigation();
                }
                break;
        }
    }

    /**
     * Reset search navigation state
     */
    private void resetSearchNavigation() {
        selectedSearchIndex = -1;
        isSearchNavigationMode = false;

        // Remove highlight from all search results
        for (VBox result : currentSearchResults) {
            removeSearchHighlight(result);
        }
    }

    /**
     * Navigate through search results
     */
    private void navigateSearchResults(int direction) {
        if (currentSearchResults.isEmpty()) return;

        // Remove highlight from current selection
        if (selectedSearchIndex >= 0 && selectedSearchIndex < currentSearchResults.size()) {
            removeSearchHighlight(currentSearchResults.get(selectedSearchIndex));
        }

        // Calculate new index
        selectedSearchIndex += direction;

        // Wrap around
        if (selectedSearchIndex >= currentSearchResults.size()) {
            selectedSearchIndex = 0;
        } else if (selectedSearchIndex < 0) {
            selectedSearchIndex = currentSearchResults.size() - 1;
        }

        // Highlight new selection
        if (selectedSearchIndex >= 0 && selectedSearchIndex < currentSearchResults.size()) {
            highlightSearchResult(currentSearchResults.get(selectedSearchIndex));
            isSearchNavigationMode = true;

            // Scroll to selected item
            scrollToSearchResult(currentSearchResults.get(selectedSearchIndex));

            // Update search field placeholder to show current selection
            updateSearchFieldWithSelection();
        }

        System.out.println("Search navigation: selected index " + selectedSearchIndex + " of " + currentSearchResults.size());
    }

    /**
     * Update search field to show current selection info
     */
    private void updateSearchFieldWithSelection() {
        if (selectedSearchIndex >= 0 && selectedSearchIndex < currentSearchResults.size()) {
            VBox selectedCard = currentSearchResults.get(selectedSearchIndex);
            MenuItem selectedItem = findMenuItemFromCard(selectedCard);

            if (selectedItem != null) {
                // Temporarily update placeholder to show selected item
                String originalPrompt = searchField.getPromptText();
                searchField.setPromptText("Selected: " + selectedItem.getName() + " - Press Enter to add");

                // Restore original prompt after 2 seconds if no action taken
                Platform.runLater(() -> {
                    new Thread(() -> {
                        try {
                            Thread.sleep(2000);
                            Platform.runLater(() -> {
                                if (searchField != null && searchField.getPromptText().startsWith("Selected:")) {
                                    searchField.setPromptText("Type to search, ↑↓←→ to navigate, Enter to add...");
                                }
                            });
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                    }).start();
                });
            }
        }
    }

    /**
     * Select current search result (add to order) and keep search open
     */
    private void selectCurrentSearchResultAndKeepSearchOpen() {
        if (selectedSearchIndex >= 0 && selectedSearchIndex < currentSearchResults.size()) {
            VBox selectedCard = currentSearchResults.get(selectedSearchIndex);

            // Find the MenuItem associated with this card
            MenuItem selectedItem = findMenuItemFromCard(selectedCard);

            if (selectedItem != null) {
                // Add item to order
                addToOrder(selectedItem);

                // Show feedback but keep search open
                showItemAddedFeedback(selectedItem.getName());

                // Keep the current selection highlighted for visual feedback
                showTemporaryAddedHighlight(selectedCard);

                System.out.println("Selected and added item: " + selectedItem.getName() + " (search remains open)");
            }
        }
    }

    /**
     * Select current search result (add to order) - legacy method for compatibility
     */
    private void selectCurrentSearchResult() {
        if (selectedSearchIndex >= 0 && selectedSearchIndex < currentSearchResults.size()) {
            VBox selectedCard = currentSearchResults.get(selectedSearchIndex);

            // Find the MenuItem associated with this card
            MenuItem selectedItem = findMenuItemFromCard(selectedCard);

            if (selectedItem != null) {
                // Add item to order
                addToOrder(selectedItem);

                // Clear search and reset
                searchField.clear();
                resetSearchNavigation();

                // Show feedback
                showItemAddedFeedback(selectedItem.getName());

                System.out.println("Selected and added item: " + selectedItem.getName());
            }
        }
    }

    /**
     * Highlight search result
     */
    private void highlightSearchResult(VBox card) {
        if (card != null) {
            card.setStyle(card.getStyle() +
                "-fx-border-color: #007bff; " +
                "-fx-border-width: 3px; " +
                "-fx-background-color: rgba(0, 123, 255, 0.1);"
            );
        }
    }

    /**
     * Remove search highlight
     */
    private void removeSearchHighlight(VBox card) {
        if (card != null) {
            String style = card.getStyle();
            style = style.replaceAll("-fx-border-color: #007bff;", "");
            style = style.replaceAll("-fx-border-width: 3px;", "");
            style = style.replaceAll("-fx-background-color: rgba\\(0, 123, 255, 0\\.1\\);", "");
            card.setStyle(style);
        }
    }

    /**
     * Scroll to search result
     */
    private void scrollToSearchResult(VBox card) {
        // Find the ScrollPane containing the menu grid
        javafx.scene.Node parent = menuGrid.getParent();
        while (parent != null && !(parent instanceof ScrollPane)) {
            parent = parent.getParent();
        }

        if (parent instanceof ScrollPane) {
            ScrollPane scrollPane = (ScrollPane) parent;

            // Calculate the position of the card relative to the grid
            double cardY = card.getBoundsInParent().getMinY();
            double gridHeight = menuGrid.getHeight();
            double scrollPosition = cardY / gridHeight;

            // Scroll to position
            scrollPane.setVvalue(scrollPosition);
        }
    }

    /**
     * Find MenuItem from card
     */
    private MenuItem findMenuItemFromCard(VBox card) {
        // Look for the item name in the card's children
        for (javafx.scene.Node child : card.getChildren()) {
            if (child instanceof Label) {
                Label label = (Label) child;
                String itemName = label.getText();

                // Find matching menu item
                for (MenuItem item : menuItems) {
                    if (item.getName().equals(itemName)) {
                        return item;
                    }
                }
            }
        }
        return null;
    }

    /**
     * Show item added feedback
     */
    private void showItemAddedFeedback(String itemName) {
        // Create temporary feedback label
        Label feedback = new Label("✅ Added: " + itemName);
        feedback.setStyle(
            "-fx-background-color: #28a745; " +
            "-fx-text-fill: white; " +
            "-fx-padding: 8 16; " +
            "-fx-background-radius: 20; " +
            "-fx-font-weight: bold;"
        );

        // Position near search field
        if (searchField.getParent() instanceof javafx.scene.layout.Pane) {
            javafx.scene.layout.Pane parent = (javafx.scene.layout.Pane) searchField.getParent();

            feedback.setLayoutX(searchField.getLayoutX());
            feedback.setLayoutY(searchField.getLayoutY() + searchField.getHeight() + 5);

            parent.getChildren().add(feedback);

            // Remove after 2 seconds
            Platform.runLater(() -> {
                new Thread(() -> {
                    try {
                        Thread.sleep(2000);
                        Platform.runLater(() -> parent.getChildren().remove(feedback));
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }).start();
            });
        }
    }

    private void filterMenuItemsRealTime(String searchText) {
        System.out.println("Filtering menu items in real-time: '" + searchText + "'");

        if (searchText == null || searchText.trim().isEmpty()) {
            // Show all items when search is empty
            displayMenuItems(menuItems);
            currentSearchResults.clear();
            return;
        }

        // Filter items that match the search text (including shortcuts)
        String lowerSearch = searchText.toLowerCase();
        List<MenuItem> filtered = menuItems.stream()
            .filter(item -> MenuShortcuts.matchesItem(item.getName(), lowerSearch) ||
                           item.getCategory().toLowerCase().contains(lowerSearch))
            .collect(Collectors.toList());

        System.out.println("Found " + filtered.size() + " matching items for: '" + searchText + "'");

        // Display filtered results immediately
        displayMenuItems(filtered);

        // Update search results for navigation
        updateSearchResults(filtered);
    }

    /**
     * Update current search results for keyboard navigation
     */
    private void updateSearchResults(List<MenuItem> filteredItems) {
        currentSearchResults.clear();

        // Collect all visible menu item cards
        for (javafx.scene.Node node : menuGrid.getChildren()) {
            if (node instanceof VBox && node.isVisible()) {
                VBox card = (VBox) node;

                // Check if this card represents one of our filtered items
                MenuItem cardItem = findMenuItemFromCard(card);
                if (cardItem != null && filteredItems.contains(cardItem)) {
                    currentSearchResults.add(card);
                }
            }
        }

        System.out.println("Updated search results: " + currentSearchResults.size() + " navigable items");
    }

    /**
     * Show temporary highlight when item is added
     */
    private void showTemporaryAddedHighlight(VBox card) {
        if (card != null) {
            // Save original style
            String originalStyle = card.getStyle();

            // Apply green "added" highlight
            card.setStyle(originalStyle +
                "-fx-border-color: #28a745; " +
                "-fx-border-width: 4px; " +
                "-fx-background-color: rgba(40, 167, 69, 0.2);"
            );

            // Restore original highlight after 1 second
            Platform.runLater(() -> {
                new Thread(() -> {
                    try {
                        Thread.sleep(1000);
                        Platform.runLater(() -> {
                            if (card != null) {
                                // Restore the blue selection highlight
                                highlightSearchResult(card);
                            }
                        });
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }).start();
            });
        }
    }

    public void setTableInfo(String tableName) {
        this.selectedTable = tableName;

        // Extract table number from table name (e.g., "Table 4" -> 4)
        try {
            if (tableName.startsWith("Table ")) {
                currentTableNumber = Integer.parseInt(tableName.substring(6));
            } else {
                currentTableNumber = Integer.parseInt(tableName);
            }
        } catch (NumberFormatException e) {
            currentTableNumber = 1; // Default to table 1
        }

        if (tableLabel != null) {
            tableLabel.setText(tableName);
        }

        // Load existing order for this table
        loadExistingOrderForTable();

        // Set up keyboard handler now that the view is loaded
        System.out.println("setTableInfo called - setting up Ctrl+K keyboard handler");
        setupKeyboardHandlerForLoadedView();
    }

    private void setupKeyboardHandlerForLoadedView() {
        Platform.runLater(() -> {
            try {
                Thread.sleep(100); // Small delay to ensure scene is ready

                // Clean up any existing handlers first
                cleanupEventHandlers();

                // Try to set up keyboard handler on the scene with high priority
                if (tableLabel != null && tableLabel.getScene() != null) {
                    System.out.println("✅ Setting up HIGH PRIORITY Ctrl+K handler on tableLabel scene");

                    // Use centralized event handler manager
                    SceneEventHandlerManager manager = SceneEventHandlerManager.getInstance();

                    // Register event filter (higher priority than event handler)
                    manager.registerKeyFilter(tableLabel.getScene(), "MenuSelectionController", this::handleHighPriorityKeyPress);

                    // Register regular handler as backup
                    manager.registerKeyHandler(tableLabel.getScene(), "MenuSelectionController", this::handleGlobalKeyPress);

                    // Set focus to make sure key events are captured
                    tableLabel.getScene().getRoot().requestFocus();
                    System.out.println("✅ Scene focus requested for keyboard events");
                    return;
                }

                if (menuGrid != null && menuGrid.getScene() != null) {
                    System.out.println("✅ Setting up HIGH PRIORITY Ctrl+K handler on menuGrid scene");

                    // Use centralized event handler manager
                    SceneEventHandlerManager manager = SceneEventHandlerManager.getInstance();

                    // Register event filter and handler
                    manager.registerKeyFilter(menuGrid.getScene(), "MenuSelectionController", this::handleHighPriorityKeyPress);
                    manager.registerKeyHandler(menuGrid.getScene(), "MenuSelectionController", this::handleGlobalKeyPress);

                    menuGrid.getScene().getRoot().requestFocus();
                    return;
                }

                System.out.println("❌ Could not set up keyboard handler - no scene available");

            } catch (Exception e) {
                System.err.println("Error setting up keyboard handler: " + e.getMessage());
            }
        });
    }

    /**
     * High priority key press handler (event filter)
     */
    private void handleHighPriorityKeyPress(javafx.scene.input.KeyEvent event) {
        System.out.println("🔍 HIGH PRIORITY FILTER: Key=" + event.getCode() + ", Ctrl=" + event.isControlDown());

        // Only handle Ctrl+K here to ensure it gets priority
        if (event.getCode() == KeyCode.K && event.isControlDown()) {
            System.out.println("🎯 HIGH PRIORITY: MenuSelectionController Ctrl+K detected - opening table-specific billing");
            openBillingAndKOT();
            event.consume(); // Consume to prevent any other handlers
        }
    }

    /**
     * Load existing order for the current table from OrderManager
     */
    private void loadExistingOrderForTable() {
        try {
            com.restaurant.model.Order existingOrder = orderManager.getOrderForTable(currentTableNumber);

            if (existingOrder != null && !existingOrder.getItems().isEmpty()) {
                System.out.println("MenuSelectionController: Loading existing order for table " + currentTableNumber +
                                 " with " + existingOrder.getItems().size() + " items");

                // Clear current order items
                orderItems.clear();

                // Convert Order items to OrderItem (inner class)
                for (com.restaurant.model.OrderItem modelOrderItem : existingOrder.getItems()) {
                    // Create inner class OrderItem from model OrderItem
                    MenuItem menuItem = new MenuItem(
                        modelOrderItem.getMenuItem().getId(),
                        modelOrderItem.getMenuItem().getName(),
                        modelOrderItem.getPrice(), // Use the price from OrderItem (may be discounted)
                        modelOrderItem.getMenuItem().getCategory()
                    );

                    OrderItem orderItem = new OrderItem(menuItem, modelOrderItem.getQuantity());
                    orderItems.add(orderItem);
                }

                // Update display
                updateOrderDisplay();

                System.out.println("MenuSelectionController: Successfully loaded " + orderItems.size() +
                                 " items for table " + currentTableNumber);
            } else {
                System.out.println("MenuSelectionController: No existing order found for table " + currentTableNumber);
                // Clear any existing items
                orderItems.clear();
                updateOrderDisplay();
            }
        } catch (Exception e) {
            System.err.println("Error loading existing order for table " + currentTableNumber + ": " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void setupMenuItems() {
        // Clear existing items and add complete menu items (233 items) from the restaurant menu
        menuItems.clear();

        // Tandoori Roti - 13 items
        menuItems.add(new MenuItem(1, "Roti", 20.00, "Tandoori Roti"));
        menuItems.add(new MenuItem(2, "Butter Roti", 25.00, "Tandoori Roti"));
        menuItems.add(new MenuItem(3, "Naan", 35.00, "Tandoori Roti"));
        menuItems.add(new MenuItem(4, "Butter Naan", 40.00, "Tandoori Roti"));
        menuItems.add(new MenuItem(5, "Garlic Naan", 70.00, "Tandoori Roti"));
        menuItems.add(new MenuItem(6, "Khulcha", 40.00, "Tandoori Roti"));
        menuItems.add(new MenuItem(7, "Butter Khulcha", 45.00, "Tandoori Roti"));
        menuItems.add(new MenuItem(8, "Lacha Paratha", 45.00, "Tandoori Roti"));
        menuItems.add(new MenuItem(9, "Butter Paratha", 45.00, "Tandoori Roti"));
        menuItems.add(new MenuItem(10, "Aloo Paratha", 70.00, "Tandoori Roti"));
        menuItems.add(new MenuItem(11, "Chi. Kheema Paratha", 120.00, "Tandoori Roti"));
        menuItems.add(new MenuItem(12, "Paneer Paratha", 120.00, "Tandoori Roti"));
        menuItems.add(new MenuItem(13, "Veg Kheema Paratha", 120.00, "Tandoori Roti"));

        // Biryani (Veg/Non Veg) - 14 items
        menuItems.add(new MenuItem(14, "Steam Basmati Rice (H/F)", 60.00, "Biryani"));
        menuItems.add(new MenuItem(15, "Jeera Rice (H/F)", 70.00, "Biryani"));
        menuItems.add(new MenuItem(16, "Veg Pulao", 170.00, "Biryani"));
        menuItems.add(new MenuItem(17, "Veg Biryani", 180.00, "Biryani"));
        menuItems.add(new MenuItem(18, "Egg Biryani", 190.00, "Biryani"));
        menuItems.add(new MenuItem(19, "Paneer Pulav", 180.00, "Biryani"));
        menuItems.add(new MenuItem(20, "Paneer Biryani", 190.00, "Biryani"));
        menuItems.add(new MenuItem(21, "Chicken Biryani", 220.00, "Biryani"));
        menuItems.add(new MenuItem(22, "Chi. Dum Biryani", 230.00, "Biryani"));
        menuItems.add(new MenuItem(23, "Chicken Hyderabadi Biryani", 230.00, "Biryani"));
        menuItems.add(new MenuItem(24, "Chicken Tikka Biryani", 250.00, "Biryani"));
        menuItems.add(new MenuItem(25, "Mutton Biryani", 280.00, "Biryani"));
        menuItems.add(new MenuItem(26, "Mutton Dum Biryani", 300.00, "Biryani"));
        menuItems.add(new MenuItem(27, "Mutton Hyderabadi Biryani", 300.00, "Biryani"));

        // Tandoori (Veg) - 6 items
        menuItems.add(new MenuItem(28, "Paneer Tikka", 200.00, "Tandoori"));
        menuItems.add(new MenuItem(29, "Paneer Malai Tikka", 220.00, "Tandoori"));
        menuItems.add(new MenuItem(30, "Veg Seek Kabab", 190.00, "Tandoori"));
        menuItems.add(new MenuItem(31, "Mushroom Tikka", 200.00, "Tandoori"));
        menuItems.add(new MenuItem(32, "Baby Corn Tikka", 190.00, "Tandoori"));
        menuItems.add(new MenuItem(33, "Chilly Milly Kabab", 200.00, "Tandoori"));

        // Tandoori Chicken - 13 items
        menuItems.add(new MenuItem(34, "Chicken Tikka", 210.00, "Tandoori"));
        menuItems.add(new MenuItem(35, "Chicken Tandoori Half", 210.00, "Tandoori"));
        menuItems.add(new MenuItem(36, "Chicken Tandoori Full", 400.00, "Tandoori"));
        menuItems.add(new MenuItem(37, "Chicken Pahadi Tandoori Half", 220.00, "Tandoori"));
        menuItems.add(new MenuItem(38, "Chicken Pahadi Tandoori Full", 410.00, "Tandoori"));
        menuItems.add(new MenuItem(39, "Chicken Lemon Tandoori Half", 240.00, "Tandoori"));
        menuItems.add(new MenuItem(40, "Chicken Lemon Tandoori Full", 420.00, "Tandoori"));
        menuItems.add(new MenuItem(41, "Chicken Kalimiri Kabab", 260.00, "Tandoori"));
        menuItems.add(new MenuItem(42, "Chicken Banjara Kabab", 260.00, "Tandoori"));
        menuItems.add(new MenuItem(43, "Chicken Sholay Kabab", 260.00, "Tandoori"));
        menuItems.add(new MenuItem(44, "Chicken Sikkh Kabab", 280.00, "Tandoori"));
        menuItems.add(new MenuItem(45, "Chicken Tangri Kabab", 200.00, "Tandoori"));
        menuItems.add(new MenuItem(46, "Chicken Rajwadi Kabab", 300.00, "Tandoori"));

        // Papad & Salad - 8 items
        menuItems.add(new MenuItem(47, "Roasted Papad", 20.00, "Papad & Salad"));
        menuItems.add(new MenuItem(48, "Fry Papad", 25.00, "Papad & Salad"));
        menuItems.add(new MenuItem(49, "Masala Papad", 50.00, "Papad & Salad"));
        menuItems.add(new MenuItem(50, "Green Salad", 90.00, "Papad & Salad"));
        menuItems.add(new MenuItem(51, "Raita", 100.00, "Papad & Salad"));
        menuItems.add(new MenuItem(52, "Boondi Raita", 130.00, "Papad & Salad"));
        menuItems.add(new MenuItem(53, "Schzewan Sauce Extra", 20.00, "Papad & Salad"));
        menuItems.add(new MenuItem(54, "Fry Noodles Extra", 20.00, "Papad & Salad"));

        // Mutton Gravy - 8 items
        menuItems.add(new MenuItem(55, "Mutton Masala", 330.00, "Mutton Gravy"));
        menuItems.add(new MenuItem(56, "Mutton Kadhai", 330.00, "Mutton Gravy"));
        menuItems.add(new MenuItem(57, "Mutton Kolhapuri", 330.00, "Mutton Gravy"));
        menuItems.add(new MenuItem(58, "Mutton Hyderabadi", 330.00, "Mutton Gravy"));
        menuItems.add(new MenuItem(59, "Mutton Handi (Half) 6pcs", 470.00, "Mutton Gravy"));
        menuItems.add(new MenuItem(60, "Mutton Handi (Full) 12pcs", 750.00, "Mutton Gravy"));
        menuItems.add(new MenuItem(61, "Mutton Do Pyaza", 330.00, "Mutton Gravy"));
        menuItems.add(new MenuItem(62, "Mutton Shukar", 350.00, "Mutton Gravy"));

        // Sea Food - 11 items
        menuItems.add(new MenuItem(63, "Bangda Fry", 130.00, "Sea Food"));
        menuItems.add(new MenuItem(64, "Bangda Masala", 180.00, "Sea Food"));
        menuItems.add(new MenuItem(65, "Mandeli Oil Fry", 150.00, "Sea Food"));
        menuItems.add(new MenuItem(66, "Surmai Tawa Fry", 195.00, "Sea Food"));
        menuItems.add(new MenuItem(67, "Surmai Koliwada", 195.00, "Sea Food"));
        menuItems.add(new MenuItem(68, "Prawns Tawa Fry", 255.00, "Sea Food"));
        menuItems.add(new MenuItem(69, "Prawns Koliwada", 250.00, "Sea Food"));
        menuItems.add(new MenuItem(70, "Surmai Gavan Curry", 195.00, "Sea Food"));
        menuItems.add(new MenuItem(71, "Surmai Masala", 195.00, "Sea Food"));
        menuItems.add(new MenuItem(82, "Fish Curry", 220.00, "Sea Food"));
        menuItems.add(new MenuItem(83, "Prawn Curry", 280.00, "Sea Food"));

        // Bulk Order (Per KG) - 10 items
        menuItems.add(new MenuItem(72, "Paneer Pulav", 800.00, "Bulk Order"));
        menuItems.add(new MenuItem(73, "Veg Biryani", 800.00, "Bulk Order"));
        menuItems.add(new MenuItem(74, "Chicken Biryani", 1000.00, "Bulk Order"));
        menuItems.add(new MenuItem(75, "Mutton Biryani", 1300.00, "Bulk Order"));
        menuItems.add(new MenuItem(76, "Veg Pulav", 700.00, "Bulk Order"));
        menuItems.add(new MenuItem(77, "Chicken Masala", 900.00, "Bulk Order"));
        menuItems.add(new MenuItem(78, "Jira Rice", 650.00, "Bulk Order"));
        menuItems.add(new MenuItem(79, "Steam Rice", 650.00, "Bulk Order"));
        menuItems.add(new MenuItem(80, "Chicken Malai Tikka", 240.00, "Tandoori"));
        menuItems.add(new MenuItem(81, "Chicken Seekh Kabab", 280.00, "Tandoori"));

        // Soup (Veg) - 5 items
        menuItems.add(new MenuItem(84, "Manchow Soup", 110.00, "Soup (Veg)"));
        menuItems.add(new MenuItem(85, "Schezwan Soup", 110.00, "Soup (Veg)"));
        menuItems.add(new MenuItem(86, "Noodles Soup", 110.00, "Soup (Veg)"));
        menuItems.add(new MenuItem(87, "Clear Soup", 110.00, "Soup (Veg)"));
        menuItems.add(new MenuItem(88, "Hot N Sour Soup", 110.00, "Soup (Veg)"));

        // Soup (Non-Veg) - 6 items
        menuItems.add(new MenuItem(89, "Chicken Manchow Soup", 120.00, "Soup (Non-Veg)"));
        menuItems.add(new MenuItem(90, "Chicken Hot N Sour Soup", 120.00, "Soup (Non-Veg)"));
        menuItems.add(new MenuItem(91, "Chicken Lung Fung Soup", 120.00, "Soup (Non-Veg)"));
        menuItems.add(new MenuItem(92, "Chicken Schezwan Soup", 120.00, "Soup (Non-Veg)"));
        menuItems.add(new MenuItem(93, "Chicken Noodles Soup", 120.00, "Soup (Non-Veg)"));
        menuItems.add(new MenuItem(94, "Chicken Clear Soup", 120.00, "Soup (Non-Veg)"));

        // Noodles (Veg) - 9 items
        menuItems.add(new MenuItem(95, "Veg Hakka Noodles", 160.00, "Noodles (Veg)"));
        menuItems.add(new MenuItem(96, "Veg Schezwan Noodles", 170.00, "Noodles (Veg)"));
        menuItems.add(new MenuItem(97, "Veg Singapore Noodles", 190.00, "Noodles (Veg)"));
        menuItems.add(new MenuItem(98, "Veg Hong Kong Noodles", 190.00, "Noodles (Veg)"));
        menuItems.add(new MenuItem(99, "Veg Mushroom Noodles", 180.00, "Noodles (Veg)"));
        menuItems.add(new MenuItem(100, "Veg Manchurian Noodles", 190.00, "Noodles (Veg)"));
        menuItems.add(new MenuItem(101, "Veg Sherpa Noodles", 220.00, "Noodles (Veg)"));
        menuItems.add(new MenuItem(102, "Veg Triple Sch. Noodles", 220.00, "Noodles (Veg)"));
        menuItems.add(new MenuItem(103, "Veg Chilly Garlic Noodles", 220.00, "Noodles (Veg)"));

        // Noodles (Non-Veg) - 12 items
        menuItems.add(new MenuItem(104, "Egg Hakka Noodles", 160.00, "Noodles (Non-Veg)"));
        menuItems.add(new MenuItem(105, "Egg Schezwan Noodles", 170.00, "Noodles (Non-Veg)"));
        menuItems.add(new MenuItem(106, "Chicken Hakka Noodles", 180.00, "Noodles (Non-Veg)"));
        menuItems.add(new MenuItem(107, "Chi. Schezwan Noodles", 190.00, "Noodles (Non-Veg)"));
        menuItems.add(new MenuItem(108, "Chi. Singapore Noodles", 200.00, "Noodles (Non-Veg)"));
        menuItems.add(new MenuItem(109, "Chi. Hong Kong Noodles", 200.00, "Noodles (Non-Veg)"));
        menuItems.add(new MenuItem(110, "Chi. Mushroom Noodles", 200.00, "Noodles (Non-Veg)"));
        menuItems.add(new MenuItem(111, "Chi. Triple Schezwan Noodles", 250.00, "Noodles (Non-Veg)"));
        menuItems.add(new MenuItem(112, "Chi. Sherpa Noodles", 250.00, "Noodles (Non-Veg)"));
        menuItems.add(new MenuItem(113, "Chi. Thousand Noodles", 280.00, "Noodles (Non-Veg)"));
        menuItems.add(new MenuItem(114, "Chi. Chilly Basil Noodles", 250.00, "Noodles (Non-Veg)"));
        menuItems.add(new MenuItem(115, "Chicken Chilly Garlic Noodles", 250.00, "Noodles (Non-Veg)"));

        // Rice (Veg) - 12 items
        menuItems.add(new MenuItem(116, "Veg Fry Rice", 170.00, "Rice (Veg)"));
        menuItems.add(new MenuItem(117, "Veg Schezwan Rice", 180.00, "Rice (Veg)"));
        menuItems.add(new MenuItem(118, "Veg Singapore Rice", 190.00, "Rice (Veg)"));
        menuItems.add(new MenuItem(119, "Veg Hong Kong Rice", 190.00, "Rice (Veg)"));
        menuItems.add(new MenuItem(120, "Veg Schezwan Combination Rice", 190.00, "Rice (Veg)"));
        menuItems.add(new MenuItem(121, "Veg Manchurian Rice", 210.00, "Rice (Veg)"));
        menuItems.add(new MenuItem(122, "Veg Triple Schoz. Rice", 210.00, "Rice (Veg)"));
        menuItems.add(new MenuItem(123, "Paneer Fry Rice", 200.00, "Rice (Veg)"));
        menuItems.add(new MenuItem(124, "Paneer Schezwan Rice", 200.00, "Rice (Veg)"));
        menuItems.add(new MenuItem(125, "Veg Sherpa Rice", 230.00, "Rice (Veg)"));
        menuItems.add(new MenuItem(126, "Veg Thousand Rice", 230.00, "Rice (Veg)"));
        menuItems.add(new MenuItem(127, "Veg Chilly Basil Rice", 200.00, "Rice (Veg)"));

        // Rice (Non-Veg) - 16 items
        menuItems.add(new MenuItem(128, "Chi. Schezwan Rice", 190.00, "Rice (Non-Veg)"));
        menuItems.add(new MenuItem(129, "Chi. Singapore Rice", 200.00, "Rice (Non-Veg)"));
        menuItems.add(new MenuItem(130, "Chi. Hong Kong Rice", 200.00, "Rice (Non-Veg)"));
        menuItems.add(new MenuItem(131, "Chi. Sez. Combination Rice", 210.00, "Rice (Non-Veg)"));
        menuItems.add(new MenuItem(132, "Chi. Burn Garlic Rice", 210.00, "Rice (Non-Veg)"));
        menuItems.add(new MenuItem(133, "Chi. Chilly Garlic Rice", 210.00, "Rice (Non-Veg)"));
        menuItems.add(new MenuItem(134, "Chi. Manchurian Rice", 250.00, "Rice (Non-Veg)"));
        menuItems.add(new MenuItem(135, "Chi. Triple Schoz. Rice", 250.00, "Rice (Non-Veg)"));
        menuItems.add(new MenuItem(136, "Chi. Sherpa Rice", 260.00, "Rice (Non-Veg)"));
        menuItems.add(new MenuItem(137, "Chi. Thousand Rice", 300.00, "Rice (Non-Veg)"));
        menuItems.add(new MenuItem(138, "Chi. Jadoo Rice", 280.00, "Rice (Non-Veg)"));
        menuItems.add(new MenuItem(139, "Chi. Ginger Garlic Rice", 210.00, "Rice (Non-Veg)"));
        menuItems.add(new MenuItem(140, "Chi. Chilly Basil Rice", 220.00, "Rice (Non-Veg)"));
        menuItems.add(new MenuItem(141, "Egg Fry Rice", 170.00, "Rice (Non-Veg)"));
        menuItems.add(new MenuItem(142, "Egg Schezwan Rice", 180.00, "Rice (Non-Veg)"));
        menuItems.add(new MenuItem(143, "Chi. Fry Rice", 180.00, "Rice (Non-Veg)"));

        // Chinese Gravy - 9 items
        menuItems.add(new MenuItem(144, "Manchurian Gravy / Chilly", 180.00, "Chinese Gravy"));
        menuItems.add(new MenuItem(145, "Schezwan Gravy", 180.00, "Chinese Gravy"));
        menuItems.add(new MenuItem(146, "Chilly Gravy", 180.00, "Chinese Gravy"));
        menuItems.add(new MenuItem(147, "Kum Pav Gravy", 180.00, "Chinese Gravy"));
        menuItems.add(new MenuItem(148, "Hot Garlic Gravy", 190.00, "Chinese Gravy"));
        menuItems.add(new MenuItem(149, "Oyster Gravy", 190.00, "Chinese Gravy"));
        menuItems.add(new MenuItem(150, "Paneer Sch. Gravy", 190.00, "Chinese Gravy"));
        menuItems.add(new MenuItem(151, "Paneer Manch. Gravy", 190.00, "Chinese Gravy"));
        menuItems.add(new MenuItem(152, "Paneer Chilly Gravy", 190.00, "Chinese Gravy"));

        // Indian & Punjabi (Veg) - 28 items (Part 1)
        menuItems.add(new MenuItem(153, "Dal Fry", 130.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(154, "Dal Tadka", 150.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(155, "Dal Palak", 150.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(156, "Dal Khichadi (1000ML)", 220.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(157, "Palak Khichadi (1000ML)", 240.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(158, "Dal Khichadi Tadka (1000ML)", 240.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(159, "Palak Khichadi Tadka (1000ML)", 240.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(160, "Mix Veg", 180.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(161, "Veg Kadhai", 190.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(162, "Veg Kolhapuri", 190.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(163, "Veg Tawa", 190.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(164, "Veg Lajawab", 190.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(165, "Veg Chilly Milly", 220.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(166, "Aloo Mutter", 170.00, "Indian & Punjabi (Veg)"));

        // Indian & Punjabi (Veg) - 28 items (Part 2)
        menuItems.add(new MenuItem(167, "Veg Handi (Half/Full)", 210.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(168, "Paneer Masala", 200.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(169, "Paneer Mutter Masala", 200.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(170, "Paneer Butter Masala", 220.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(171, "Paneer Kadhai", 200.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(172, "Paneer Bhurji Masala", 220.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(173, "Paneer Mutter", 200.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(174, "Palak Paneer", 200.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(175, "Mushroom Masala", 210.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(176, "Mushroom Tikka Masala", 230.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(177, "Lasuni Palak", 190.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(178, "Veg Maratha", 250.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(179, "Sev Bhaji", 180.00, "Indian & Punjabi (Veg)"));
        menuItems.add(new MenuItem(180, "Masala Fry Masala", 200.00, "Indian & Punjabi (Veg)"));

        // Chicken Gravy - 19 items (Part 1)
        menuItems.add(new MenuItem(181, "Chicken Masala", 210.00, "Chicken Gravy"));
        menuItems.add(new MenuItem(182, "Chicken Curry", 210.00, "Chicken Gravy"));
        menuItems.add(new MenuItem(183, "Chicken Kadhai", 240.00, "Chicken Gravy"));
        menuItems.add(new MenuItem(184, "Chicken Bhurjani", 240.00, "Chicken Gravy"));
        menuItems.add(new MenuItem(185, "Chicken Tawa Masala", 260.00, "Chicken Gravy"));
        menuItems.add(new MenuItem(186, "Chicken Gayti Masala", 260.00, "Chicken Gravy"));
        menuItems.add(new MenuItem(187, "Chicken Tikka Masala", 260.00, "Chicken Gravy"));
        menuItems.add(new MenuItem(188, "Chicken Maratha", 280.00, "Chicken Gravy"));
        menuItems.add(new MenuItem(189, "Chicken Lasuni Masala", 260.00, "Chicken Gravy"));
        menuItems.add(new MenuItem(190, "Chicken Japeta (H/F)", 310.00, "Chicken Gravy"));

        // Chicken Gravy - 19 items (Part 2)
        menuItems.add(new MenuItem(191, "Butter Chicken (H/F)", 290.00, "Chicken Gravy"));
        menuItems.add(new MenuItem(192, "Chicken Malvani Masala", 260.00, "Chicken Gravy"));
        menuItems.add(new MenuItem(193, "Chicken Tikka Lemon Masala", 260.00, "Chicken Gravy"));
        menuItems.add(new MenuItem(194, "Chicken Hyderabadi Masala", 260.00, "Chicken Gravy"));
        menuItems.add(new MenuItem(195, "Chicken Mughlai", 260.00, "Chicken Gravy"));
        menuItems.add(new MenuItem(196, "Chicken Pahadi Masala", 260.00, "Chicken Gravy"));
        menuItems.add(new MenuItem(197, "Chicken Handi (Half) 6pcs", 260.00, "Chicken Gravy"));
        menuItems.add(new MenuItem(198, "Chicken Handi (Full) 12pcs", 480.00, "Chicken Gravy"));
        menuItems.add(new MenuItem(199, "Chicken Do Pyaza", 260.00, "Chicken Gravy"));

        // Starters (Veg) - 14 items
        menuItems.add(new MenuItem(200, "Veg Manchurian / Chilly", 190.00, "Starters (Veg)"));
        menuItems.add(new MenuItem(201, "Veg Chinese Bhel", 190.00, "Starters (Veg)"));
        menuItems.add(new MenuItem(202, "Mushroom Chilly/ Manchurian", 200.00, "Starters (Veg)"));
        menuItems.add(new MenuItem(203, "Paneer Chilly/ Manchurian", 220.00, "Starters (Veg)"));
        menuItems.add(new MenuItem(204, "Paneer Crispy", 220.00, "Starters (Veg)"));
        menuItems.add(new MenuItem(205, "Paneer Singapur", 220.00, "Starters (Veg)"));
        menuItems.add(new MenuItem(206, "Veg Crispy", 200.00, "Starters (Veg)"));
        menuItems.add(new MenuItem(207, "Crispy Chilly Potato", 220.00, "Starters (Veg)"));
        menuItems.add(new MenuItem(208, "Honey Chilly Potato", 220.00, "Starters (Veg)"));
        menuItems.add(new MenuItem(209, "Paneer Shangai Wok", 250.00, "Starters (Veg)"));
        menuItems.add(new MenuItem(210, "Paneer Schezwan Wok", 250.00, "Starters (Veg)"));
        menuItems.add(new MenuItem(211, "Paneer Chilly Basil Wok", 260.00, "Starters (Veg)"));
        menuItems.add(new MenuItem(212, "Paneer Honey Chilly Wok", 260.00, "Starters (Veg)"));
        menuItems.add(new MenuItem(213, "Paneer Kum Pav Wok", 260.00, "Starters (Veg)"));

        // Starters (Non-Veg) - 14 items
        menuItems.add(new MenuItem(214, "Chi. Chinese Bhel", 180.00, "Starters (Non-Veg)"));
        menuItems.add(new MenuItem(215, "Chi. Chilly/Manchurian", 220.00, "Starters (Non-Veg)"));
        menuItems.add(new MenuItem(216, "Chi. Schezwan", 220.00, "Starters (Non-Veg)"));
        menuItems.add(new MenuItem(217, "Chi. Chilly Garlic Wok", 230.00, "Starters (Non-Veg)"));
        menuItems.add(new MenuItem(218, "Chi. Kum Pav Wok", 260.00, "Starters (Non-Veg)"));
        menuItems.add(new MenuItem(219, "Chi. Crispy", 250.00, "Starters (Non-Veg)"));
        menuItems.add(new MenuItem(220, "Chi. Singapur", 250.00, "Starters (Non-Veg)"));
        menuItems.add(new MenuItem(221, "Chi. Lamba", 250.00, "Starters (Non-Veg)"));
        menuItems.add(new MenuItem(222, "Chi. Oyster Sauces", 250.00, "Starters (Non-Veg)"));
        menuItems.add(new MenuItem(223, "Chi. Black Paper Wok", 250.00, "Starters (Non-Veg)"));
        menuItems.add(new MenuItem(224, "Chi. Lollypop 8 Pc.", 230.00, "Starters (Non-Veg)"));
        menuItems.add(new MenuItem(225, "Chi. Lollypop Schzwn/Hnypp", 300.00, "Starters (Non-Veg)"));
        menuItems.add(new MenuItem(226, "Chi. Honey Chilly", 270.00, "Starters (Non-Veg)"));
        menuItems.add(new MenuItem(227, "Chi. Chilly Basil Wok", 270.00, "Starters (Non-Veg)"));

        // Egg Dishes - 6 items
        menuItems.add(new MenuItem(228, "Boiled Egg", 40.00, "Egg Dishes"));
        menuItems.add(new MenuItem(229, "Egg Omlete", 50.00, "Egg Dishes"));
        menuItems.add(new MenuItem(230, "Egg Bhurji", 110.00, "Egg Dishes"));
        menuItems.add(new MenuItem(231, "Egg Masala", 170.00, "Egg Dishes"));
        menuItems.add(new MenuItem(232, "Egg Curry", 170.00, "Egg Dishes"));
        menuItems.add(new MenuItem(233, "Anda Ghotala", 180.00, "Egg Dishes"));

        displayMenuItems(menuItems);
    }
    
    private void setupCategories() {
        // Populate categories list
        ObservableList<String> categories = FXCollections.observableArrayList(
            "Favorite Items", "Tandoori Roti", "Biryani", "Soup (Veg)", "Soup (Non-Veg)",
            "Noodles (Veg)", "Noodles (Non-Veg)", "Rice (Veg)", "Rice (Non-Veg)",
            "Chinese Gravy", "Indian & Punjabi (Veg)", "Chicken Gravy", "Starters (Veg)",
            "Starters (Non-Veg)", "Egg Dishes", "Tandoori", "Papad & Salad", "Mutton Gravy", "Sea Food"
        );
        categoriesList.setItems(categories);

        categoriesList.getSelectionModel().selectedItemProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal != null) {
                filterMenuByCategory(newVal);
            }
        });

        // Select first category by default
        Platform.runLater(() -> {
            if (!categoriesList.getItems().isEmpty()) {
                categoriesList.getSelectionModel().selectFirst();
            }
        });
    }
    
    private void filterMenuByCategory(String category) {
        if ("Favorite Items".equals(category)) {
            displayMenuItems(menuItems); // Show all items
        } else {
            List<MenuItem> filtered = menuItems.stream()
                .filter(item -> category.equals(item.getCategory()))
                .toList();
            displayMenuItems(filtered);
        }
    }
    
    private void displayMenuItems(List<MenuItem> items) {
        menuGrid.getChildren().clear();
        
        int col = 0, row = 0;
        for (MenuItem item : items) {
            VBox itemCard = createMenuItemCard(item);
            menuGrid.add(itemCard, col, row);
            
            col++;
            if (col >= 4) {
                col = 0;
                row++;
            }
        }
    }
    
    private VBox createMenuItemCard(MenuItem item) {
        VBox card = new VBox();
        card.getStyleClass().add("menu-item-card");
        card.setSpacing(3);
        card.setPadding(new Insets(8));

        Label nameLabel = new Label(item.getName());
        nameLabel.getStyleClass().add("item-name");
        nameLabel.setFont(Font.font("System", 11));
        nameLabel.setWrapText(true);

        // Price section with discount support
        VBox priceSection = createPriceSection(item);

        // Button section with Add and Discount options
        HBox buttonSection = new HBox(5);
        buttonSection.setAlignment(Pos.CENTER);

        Button addButton = new Button("Add");
        addButton.getStyleClass().add("add-button");
        addButton.setStyle("-fx-font-size: 10px; -fx-padding: 4 8;");
        addButton.setOnAction(e -> addToOrder(item));

        Button discountButton = new Button("💰");
        discountButton.setStyle("-fx-font-size: 10px; -fx-padding: 4 6; -fx-background-color: #ffc107; -fx-text-fill: black;");
        discountButton.setOnAction(e -> showDiscountDialog(item));

        buttonSection.getChildren().addAll(addButton, discountButton);

        card.getChildren().addAll(nameLabel, priceSection, buttonSection);
        return card;
    }

    private VBox createPriceSection(MenuItem item) {
        VBox priceSection = new VBox(1);
        priceSection.setAlignment(Pos.CENTER);

        // Check if item has discount
        double discountPercent = getItemDiscount(item.getName());

        if (discountPercent > 0) {
            // Show original price with strikethrough
            Label originalPriceLabel = new Label("₹" + String.format("%.0f", item.getPrice()));
            originalPriceLabel.setStyle("-fx-font-size: 9px; -fx-text-fill: #999; -fx-strikethrough: true;");

            // Calculate and show discounted price
            double discountedPrice = item.getPrice() * (1 - discountPercent / 100);
            Label discountedPriceLabel = new Label("₹" + String.format("%.0f", discountedPrice));
            discountedPriceLabel.getStyleClass().add("item-price");
            discountedPriceLabel.setStyle("-fx-font-size: 11px; -fx-font-weight: bold; -fx-text-fill: #e74c3c;");

            // Show discount percentage
            Label discountLabel = new Label(String.format("%.0f%% OFF", discountPercent));
            discountLabel.setStyle("-fx-font-size: 8px; -fx-text-fill: white; -fx-background-color: #e74c3c; " +
                                 "-fx-padding: 1 3; -fx-background-radius: 3;");

            priceSection.getChildren().addAll(originalPriceLabel, discountedPriceLabel, discountLabel);
        } else {
            // Show regular price
            Label priceLabel = new Label("₹" + String.format("%.2f", item.getPrice()));
            priceLabel.getStyleClass().add("item-price");
            priceSection.getChildren().add(priceLabel);
        }

        return priceSection;
    }

    private double getItemDiscount(String itemName) {
        // Sample discount data - in real app, this would come from database
        switch (itemName.toLowerCase()) {
            case "chicken angara":
                return 20.0; // 20% discount
            case "cheese garlic bread":
                return 15.0; // 15% discount
            case "grilled paneer sandwich":
                return 8.0;  // 8% discount
            case "chili mushroom":
                return 18.0; // 18% discount
            default:
                return 0.0;  // No discount
        }
    }

    private void showDiscountDialog(MenuItem item) {
        try {
            // Create discount options dialog
            Dialog<ButtonType> discountDialog = new Dialog<>();
            discountDialog.setTitle("Apply Discount");
            discountDialog.setHeaderText("Item: " + item.getName() + "\nOriginal Price: ₹" + String.format("%.2f", item.getPrice()));

            // Create discount options
            VBox discountContent = new VBox(10);
            discountContent.setPadding(new Insets(20));

            // Quick discount buttons
            Label quickLabel = new Label("Quick Discounts:");
            quickLabel.setStyle("-fx-font-weight: bold;");

            HBox quickDiscounts = new HBox(8);
            quickDiscounts.setAlignment(Pos.CENTER);

            Button discount5 = new Button("5%");
            discount5.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-padding: 6 10; -fx-font-size: 10px;");
            discount5.setOnAction(e -> applyDiscountAndAdd(item, 5.0, discountDialog));

            Button discount10 = new Button("10%");
            discount10.setStyle("-fx-background-color: #ffc107; -fx-text-fill: black; -fx-padding: 6 10; -fx-font-size: 10px;");
            discount10.setOnAction(e -> applyDiscountAndAdd(item, 10.0, discountDialog));

            Button discount15 = new Button("15%");
            discount15.setStyle("-fx-background-color: #fd7e14; -fx-text-fill: white; -fx-padding: 6 10; -fx-font-size: 10px;");
            discount15.setOnAction(e -> applyDiscountAndAdd(item, 15.0, discountDialog));

            Button discount20 = new Button("20%");
            discount20.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-padding: 6 10; -fx-font-size: 10px;");
            discount20.setOnAction(e -> applyDiscountAndAdd(item, 20.0, discountDialog));

            quickDiscounts.getChildren().addAll(discount5, discount10, discount15, discount20);

            // Custom discount input
            Label customLabel = new Label("Custom Discount:");
            customLabel.setStyle("-fx-font-weight: bold;");

            HBox customDiscountBox = new HBox(8);
            customDiscountBox.setAlignment(Pos.CENTER);

            TextField customDiscountField = new TextField();
            customDiscountField.setPromptText("Enter %");
            customDiscountField.setPrefWidth(80);

            Button applyCustom = new Button("Apply");
            applyCustom.setStyle("-fx-background-color: #6f42c1; -fx-text-fill: white; -fx-padding: 6 12; -fx-font-size: 10px;");
            applyCustom.setOnAction(e -> {
                try {
                    double customDiscount = Double.parseDouble(customDiscountField.getText());
                    if (customDiscount >= 0 && customDiscount <= 100) {
                        applyDiscountAndAdd(item, customDiscount, discountDialog);
                    } else {
                        showAlert("Invalid Discount", "Discount must be between 0% and 100%");
                    }
                } catch (NumberFormatException ex) {
                    showAlert("Invalid Input", "Please enter a valid number");
                }
            });

            customDiscountBox.getChildren().addAll(customDiscountField, applyCustom);

            // Remove discount option
            Button removeDiscount = new Button("Remove Discount");
            removeDiscount.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-padding: 8 16; -fx-font-size: 10px;");
            removeDiscount.setOnAction(e -> {
                // Remove discount from the menu item itself, don't add to order
                removeDiscountFromMenuItem(item);
                discountDialog.close();
            });

            discountContent.getChildren().addAll(
                quickLabel,
                quickDiscounts,
                customLabel,
                customDiscountBox,
                removeDiscount
            );

            discountDialog.getDialogPane().setContent(discountContent);
            discountDialog.getDialogPane().getButtonTypes().add(ButtonType.CANCEL);
            discountDialog.showAndWait();

        } catch (Exception e) {
            showAlert("Error", "Failed to show discount options: " + e.getMessage());
        }
    }

    private void applyDiscountAndAdd(MenuItem item, double discountPercent, Dialog<ButtonType> dialog) {
        try {
            double originalPrice = item.getPrice();
            double discountedPrice = originalPrice * (1 - discountPercent / 100);
            double discountAmount = originalPrice - discountedPrice;

            // Create a new MenuItem with discounted price for this order
            MenuItem discountedItem = new MenuItem(
                item.getId(),
                item.getName() + " (" + String.format("%.0f", discountPercent) + "% OFF)",
                discountedPrice,
                item.getCategory()
            );

            // Add discounted item to order
            addToOrder(discountedItem);

            // Show confirmation
            showAlert("Discount Applied",
                     "Item: " + item.getName() + "\n" +
                     "Original Price: ₹" + String.format("%.2f", originalPrice) + "\n" +
                     "Discount: " + String.format("%.1f", discountPercent) + "% (₹" + String.format("%.2f", discountAmount) + ")\n" +
                     "Final Price: ₹" + String.format("%.2f", discountedPrice));

            dialog.close();

        } catch (Exception e) {
            showAlert("Error", "Failed to apply discount: " + e.getMessage());
        }
    }

    @FXML
    private void addToOrder(MenuItem item) {
        // Check if item already exists in order
        OrderItem existingItem = orderItems.stream()
            .filter(orderItem -> orderItem.getMenuItem().getId() == item.getId())
            .findFirst()
            .orElse(null);

        if (existingItem != null) {
            existingItem.setQuantity(existingItem.getQuantity() + 1);
        } else {
            orderItems.add(new OrderItem(item, 1));
        }

        updateOrderDisplay();

        // Save to OrderManager
        saveCurrentOrderToManager();
    }

    /**
     * Save current order to OrderManager for persistence across views
     */
    private void saveCurrentOrderToManager() {
        try {
            // Create model Order from current order items
            com.restaurant.model.Order modelOrder = new com.restaurant.model.Order();
            modelOrder.setTableNumber(currentTableNumber);
            modelOrder.setStatus("NEW");
            modelOrder.setTimestamp(java.time.LocalDateTime.now());

            // Convert OrderItem (inner class) to model OrderItem
            for (OrderItem orderItem : orderItems) {
                // Create model MenuItem
                com.restaurant.model.MenuItem modelMenuItem = new com.restaurant.model.MenuItem(
                    orderItem.getMenuItem().getId(),
                    orderItem.getMenuItem().getName(),
                    orderItem.getMenuItem().getPrice(),
                    orderItem.getMenuItem().getCategory(),
                    0 // categoryId - default to 0
                );

                // Create model OrderItem
                com.restaurant.model.OrderItem modelOrderItem = new com.restaurant.model.OrderItem(
                    modelMenuItem,
                    orderItem.getQuantity()
                );

                modelOrder.addItem(modelOrderItem);
            }

            // Save to OrderManager
            orderManager.saveOrderForTable(currentTableNumber, modelOrder);

            System.out.println("MenuSelectionController: Saved order for table " + currentTableNumber +
                             " with " + orderItems.size() + " items to OrderManager");

        } catch (Exception e) {
            System.err.println("Error saving order to OrderManager: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void updateOrderDisplay() {
        // Update item count
        int totalItems = orderItems.stream().mapToInt(OrderItem::getQuantity).sum();
        itemCountLabel.setText(totalItems + " Items");
        
        // Clear and rebuild order items container
        orderItemsContainer.getChildren().clear();
        
        if (orderItems.isEmpty()) {
            Label emptyLabel = new Label("No items added yet");
            emptyLabel.getStyleClass().add("empty-order");
            orderItemsContainer.getChildren().add(emptyLabel);
        } else {
            for (OrderItem orderItem : orderItems) {
                HBox itemRow = createOrderItemRow(orderItem);
                orderItemsContainer.getChildren().add(itemRow);
            }
        }
        
        // Update totals
        updateOrderTotals();
    }
    
    private HBox createOrderItemRow(OrderItem orderItem) {
        HBox row = new HBox();
        row.setSpacing(8);
        row.getStyleClass().add("order-item-row");
        row.setAlignment(Pos.CENTER_LEFT);

        // Item name with discount indicator
        VBox nameSection = new VBox(2);
        nameSection.setMaxWidth(140);

        Label nameLabel = new Label(orderItem.getMenuItem().getName());
        nameLabel.setMaxWidth(140);
        nameLabel.setWrapText(true);
        nameLabel.setStyle("-fx-font-size: 11px; -fx-font-weight: bold;");

        // Check if item has discount applied (indicated by name containing discount info)
        if (orderItem.getMenuItem().getName().contains("% OFF")) {
            nameLabel.setStyle("-fx-font-size: 11px; -fx-font-weight: bold; -fx-text-fill: #e74c3c;");

            // Add discount badge
            Label discountBadge = new Label("DISCOUNTED");
            discountBadge.setStyle("-fx-font-size: 8px; -fx-text-fill: white; -fx-background-color: #e74c3c; " +
                                 "-fx-padding: 1 4; -fx-background-radius: 3;");
            nameSection.getChildren().addAll(nameLabel, discountBadge);
        } else {
            nameSection.getChildren().add(nameLabel);
        }

        // Quantity controls
        HBox quantitySection = new HBox(3);
        quantitySection.setAlignment(Pos.CENTER);

        Button minusBtn = new Button("-");
        minusBtn.getStyleClass().add("quantity-button");
        minusBtn.setStyle("-fx-font-size: 10px; -fx-padding: 2 6; -fx-background-color: #dc3545; -fx-text-fill: white;");
        minusBtn.setOnAction(e -> {
            if (orderItem.getQuantity() > 1) {
                orderItem.setQuantity(orderItem.getQuantity() - 1);
            } else {
                orderItems.remove(orderItem);
            }
            updateOrderDisplay();
            saveCurrentOrderToManager();
        });

        Label qtyLabel = new Label(String.valueOf(orderItem.getQuantity()));
        qtyLabel.setMinWidth(25);
        qtyLabel.setStyle("-fx-alignment: center; -fx-font-weight: bold; -fx-font-size: 11px;");

        Button plusBtn = new Button("+");
        plusBtn.getStyleClass().add("quantity-button");
        plusBtn.setStyle("-fx-font-size: 10px; -fx-padding: 2 6; -fx-background-color: #28a745; -fx-text-fill: white;");
        plusBtn.setOnAction(e -> {
            orderItem.setQuantity(orderItem.getQuantity() + 1);
            updateOrderDisplay();
            saveCurrentOrderToManager();
        });

        quantitySection.getChildren().addAll(minusBtn, qtyLabel, plusBtn);

        // Price section
        VBox priceSection = new VBox(1);
        priceSection.setAlignment(Pos.CENTER_RIGHT);
        priceSection.setMinWidth(60);

        double itemTotal = orderItem.getMenuItem().getPrice() * orderItem.getQuantity();
        Label priceLabel = new Label("₹" + String.format("%.2f", itemTotal));
        priceLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 11px;");

        priceSection.getChildren().add(priceLabel);

        // Action buttons section
        HBox actionSection = new HBox(3);
        actionSection.setAlignment(Pos.CENTER);

        // Discount button for this order item
        Button discountBtn = new Button("💰");
        discountBtn.setStyle("-fx-font-size: 9px; -fx-padding: 3 5; -fx-background-color: #ffc107; -fx-text-fill: black;");
        discountBtn.setOnAction(e -> showOrderItemDiscountDialog(orderItem));

        // Delete button
        Button deleteBtn = new Button("🗑");
        deleteBtn.setStyle("-fx-font-size: 9px; -fx-padding: 3 5; -fx-background-color: #dc3545; -fx-text-fill: white;");
        deleteBtn.setOnAction(e -> {
            orderItems.remove(orderItem);
            updateOrderDisplay();
            saveCurrentOrderToManager();
        });

        actionSection.getChildren().addAll(discountBtn, deleteBtn);

        row.getChildren().addAll(nameSection, quantitySection, priceSection, actionSection);
        return row;
    }
    
    private void updateOrderTotals() {
        double subtotal = orderItems.stream()
            .mapToDouble(item -> item.getMenuItem().getPrice() * item.getQuantity())
            .sum();

        // Apply order-level discount
        double orderDiscountAmount = subtotal * (orderDiscountPercent / 100);
        double discountedSubtotal = subtotal - orderDiscountAmount;

        double gst = discountedSubtotal * 0.18; // 18% GST on discounted amount
        double serviceCharge = discountedSubtotal * 0.05; // 5% service charge on discounted amount
        double total = discountedSubtotal + gst + serviceCharge;

        subtotalLabel.setText("₹" + String.format("%.2f", subtotal));
        orderDiscountLabel.setText("-₹" + String.format("%.2f", orderDiscountAmount));
        gstLabel.setText("₹" + String.format("%.2f", gst));
        serviceChargeLabel.setText("₹" + String.format("%.2f", serviceCharge));
        totalLabel.setText("₹" + String.format("%.2f", total));

        // Update discount label color based on whether discount is applied
        if (orderDiscountAmount > 0) {
            orderDiscountLabel.setStyle("-fx-text-fill: #e74c3c; -fx-font-weight: bold;");
        } else {
            orderDiscountLabel.setStyle("-fx-text-fill: #6c757d;");
        }
    }
    
    private void setupOrderCalculations() {
        // This method can be used for any additional order calculation setup
    }
    
    private void setupEscapeKeyHandler() {
        // Try immediate setup first
        setupKeyboardHandlerImmediate();

        // Also try with Platform.runLater as backup
        Platform.runLater(() -> {
            setupKeyboardHandlerImmediate();
        });

        // Try again after a short delay
        Platform.runLater(() -> {
            try {
                Thread.sleep(100);
                setupKeyboardHandlerImmediate();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
    }

    private void setupKeyboardHandlerImmediate() {
        try {
            // Try multiple approaches to set up keyboard handler
            if (tableLabel != null && tableLabel.getScene() != null) {
                System.out.println("Setting up keyboard handler on tableLabel.getScene()");
                tableLabel.getScene().setOnKeyPressed(this::handleGlobalKeyPress);
            }

            // Also try setting on the root node
            if (tableLabel != null && tableLabel.getParent() != null) {
                System.out.println("Setting up keyboard handler on parent node");
                tableLabel.getParent().setOnKeyPressed(this::handleGlobalKeyPress);
            }

            // Try setting on any available scene
            if (searchField != null && searchField.getScene() != null) {
                System.out.println("Setting up keyboard handler on searchField.getScene()");
                searchField.getScene().setOnKeyPressed(this::handleGlobalKeyPress);
            }

        } catch (Exception e) {
            System.err.println("Error setting up keyboard handler: " + e.getMessage());
        }
    }

    private void handleGlobalKeyPress(javafx.scene.input.KeyEvent event) {
        System.out.println("🔍 REGULAR HANDLER: MenuSelectionController - Key=" + event.getCode() + ", Ctrl=" + event.isControlDown());

        if (event.getCode() == KeyCode.ESCAPE) {
            // Escape for back/cancel
            handleEscapeKey();
            event.consume();
        } else if (event.getCode() == KeyCode.ENTER) {
            if (event.isControlDown()) {
                // Ctrl+Enter for force confirm
                handleForceConfirm();
                event.consume();
            } else {
                // Enter for finish/confirm
                handleEnterKey();
                event.consume();
            }
        } else if (event.getCode() == KeyCode.S && event.isControlDown()) {
            // Ctrl+S for Search
            handleSearchShortcut();
            event.consume();
        } else if (event.getCode() == KeyCode.N && event.isControlDown()) {
            // Ctrl+N for Save Order
            saveOrder();
            event.consume();
        } else if (event.getCode() == KeyCode.P && event.isControlDown()) {
            // Ctrl+P for Print KOT
            printKOT();
            event.consume();
        } else if (event.getCode() == KeyCode.B && event.isControlDown()) {
            // Ctrl+B for Generate Bill
            System.out.println("Ctrl+B detected - calling generateBill()");
            generateBill();
            event.consume();
        } else if (event.getCode() == KeyCode.D && event.isControlDown()) {
            // Ctrl+D for Order Discount
            showOrderDiscountDialog();
            event.consume();
        } else if (event.getCode() == KeyCode.K && event.isControlDown()) {
            // Ctrl+K for Billing and KOT - TABLE SPECIFIC
            System.out.println("MenuSelectionController: Ctrl+K detected - opening table-specific Billing and KOT");
            openBillingAndKOT();
            event.consume(); // IMPORTANT: Consume the event to prevent global handler
        }
    }

    /**
     * Handle Escape key - back/cancel functionality
     */
    private void handleEscapeKey() {
        System.out.println("Escape key pressed - going back");
        goBack();
    }

    /**
     * Handle Enter key - finish/confirm functionality
     */
    private void handleEnterKey() {
        System.out.println("Enter key pressed - finishing current task");

        // Check current context and perform appropriate action
        if (!orderItems.isEmpty()) {
            // If there are items in order, save the order
            saveOrder();
        } else {
            // If no items, show message
            showAlert("No Action", "Add items to the order first, then press Enter to save.");
        }
    }

    /**
     * Handle Ctrl+Enter - force confirm functionality
     */
    private void handleForceConfirm() {
        System.out.println("Ctrl+Enter pressed - force confirming action");

        // Check current context and perform critical action
        if (!orderItems.isEmpty()) {
            // Force generate bill (skip save and KOT steps)
            generateBill();
        } else {
            // If no items, show message
            showAlert("No Action", "Add items to the order first, then press Ctrl+Enter to generate bill directly.");
        }
    }

    /**
     * Handle Ctrl+K - Open Billing and KOT view
     */
    private void openBillingAndKOT() {
        System.out.println("🎯 openBillingAndKOT() method called!");
        System.out.println("📋 Current table: " + selectedTable);
        System.out.println("📦 Order items count: " + (orderItems != null ? orderItems.size() : "null"));

        try {
            // Create billing and KOT management dialog
            Dialog<ButtonType> billingDialog = new Dialog<>();
            billingDialog.setTitle("Billing & KOT Management - " + selectedTable);
            billingDialog.setHeaderText("Table: " + selectedTable + " | Current Order Management");

            // Create content for the dialog
            VBox content = createBillingAndKOTContent();

            billingDialog.getDialogPane().setContent(content);
            billingDialog.getDialogPane().getButtonTypes().addAll(ButtonType.CLOSE);

            // Set dialog size
            billingDialog.getDialogPane().setPrefSize(700, 600);

            System.out.println("✅ Billing dialog created, about to show...");

            // Show dialog
            billingDialog.showAndWait();

            System.out.println("✅ Billing dialog closed");

        } catch (Exception e) {
            System.err.println("❌ Error opening Billing and KOT view: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Failed to open Billing and KOT view: " + e.getMessage());
        }
    }

    /**
     * Create content for Billing and KOT dialog
     */
    private VBox createBillingAndKOTContent() {
        VBox content = new VBox(15);
        content.setPadding(new Insets(20));
        content.setStyle("-fx-background-color: #f8f9fa;");

        // Order Summary Section
        Label orderSummaryLabel = new Label("📋 Current Order Summary");
        orderSummaryLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        VBox orderSummary = createOrderSummarySection();

        // KOT Actions Section
        Label kotLabel = new Label("🍳 Kitchen Order Ticket (KOT) Actions");
        kotLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        HBox kotActions = createKOTActionsSection();

        // Billing Actions Section
        Label billingLabel = new Label("💰 Billing Actions");
        billingLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        HBox billingActions = createBillingActionsSection();

        // Quick Stats Section
        Label statsLabel = new Label("📊 Quick Statistics");
        statsLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        VBox quickStats = createQuickStatsSection();

        content.getChildren().addAll(
            orderSummaryLabel, orderSummary,
            new Separator(),
            kotLabel, kotActions,
            new Separator(),
            billingLabel, billingActions,
            new Separator(),
            statsLabel, quickStats
        );

        return content;
    }

    /**
     * Create order summary section
     */
    private VBox createOrderSummarySection() {
        VBox section = new VBox(8);
        section.setStyle("-fx-background-color: white; -fx-padding: 15; -fx-border-radius: 5; -fx-background-radius: 5;");

        if (orderItems.isEmpty()) {
            Label emptyLabel = new Label("No items in current order");
            emptyLabel.setStyle("-fx-text-fill: #6c757d; -fx-font-style: italic;");
            section.getChildren().add(emptyLabel);
        } else {
            // Show order items
            for (OrderItem item : orderItems) {
                HBox itemRow = new HBox(10);
                itemRow.setAlignment(Pos.CENTER_LEFT);

                Label nameLabel = new Label(item.getMenuItem().getName());
                nameLabel.setStyle("-fx-font-weight: bold;");
                nameLabel.setPrefWidth(200);

                Label qtyLabel = new Label("Qty: " + item.getQuantity());
                qtyLabel.setPrefWidth(80);

                Label priceLabel = new Label("₹" + String.format("%.2f", item.getMenuItem().getPrice() * item.getQuantity()));
                priceLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #28a745;");

                itemRow.getChildren().addAll(nameLabel, qtyLabel, priceLabel);
                section.getChildren().add(itemRow);
            }

            // Add total
            Separator separator = new Separator();
            section.getChildren().add(separator);

            double total = orderItems.stream()
                .mapToDouble(item -> item.getMenuItem().getPrice() * item.getQuantity())
                .sum();

            HBox totalRow = new HBox();
            totalRow.setAlignment(Pos.CENTER_RIGHT);
            Label totalLabel = new Label("Total: ₹" + String.format("%.2f", total));
            totalLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #dc3545;");
            totalRow.getChildren().add(totalLabel);
            section.getChildren().add(totalRow);
        }

        return section;
    }

    /**
     * Create KOT actions section
     */
    private HBox createKOTActionsSection() {
        HBox section = new HBox(10);
        section.setAlignment(Pos.CENTER);

        Button printKOTBtn = new Button("🖨️ Print KOT");
        printKOTBtn.setStyle("-fx-background-color: #007bff; -fx-text-fill: white; -fx-padding: 10 20; -fx-font-size: 12px;");
        printKOTBtn.setOnAction(e -> {
            printKOT();
        });

        Button holdKOTBtn = new Button("⏸️ Hold KOT");
        holdKOTBtn.setStyle("-fx-background-color: #ffc107; -fx-text-fill: black; -fx-padding: 10 20; -fx-font-size: 12px;");
        holdKOTBtn.setOnAction(e -> {
            showAlert("Hold KOT", "KOT has been held for " + selectedTable);
        });

        Button kotStatusBtn = new Button("📋 KOT Status");
        kotStatusBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-padding: 10 20; -fx-font-size: 12px;");
        kotStatusBtn.setOnAction(e -> {
            showAlert("KOT Status", "Current KOT status: Ready to print");
        });

        section.getChildren().addAll(printKOTBtn, holdKOTBtn, kotStatusBtn);
        return section;
    }

    /**
     * Create billing actions section
     */
    private HBox createBillingActionsSection() {
        HBox section = new HBox(10);
        section.setAlignment(Pos.CENTER);

        Button saveOrderBtn = new Button("💾 Save Order");
        saveOrderBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-padding: 10 20; -fx-font-size: 12px;");
        saveOrderBtn.setOnAction(e -> {
            saveOrder();
        });

        Button generateBillBtn = new Button("🧾 Generate Bill");
        generateBillBtn.setStyle("-fx-background-color: #dc3545; -fx-text-fill: white; -fx-padding: 10 20; -fx-font-size: 12px;");
        generateBillBtn.setOnAction(e -> {
            generateBill();
        });

        Button discountBtn = new Button("💰 Apply Discount");
        discountBtn.setStyle("-fx-background-color: #fd7e14; -fx-text-fill: white; -fx-padding: 10 20; -fx-font-size: 12px;");
        discountBtn.setOnAction(e -> {
            showOrderDiscountDialog();
        });

        section.getChildren().addAll(saveOrderBtn, generateBillBtn, discountBtn);
        return section;
    }

    /**
     * Create quick stats section
     */
    private VBox createQuickStatsSection() {
        VBox section = new VBox(8);
        section.setStyle("-fx-background-color: #e9ecef; -fx-padding: 15; -fx-border-radius: 5; -fx-background-radius: 5;");

        // Calculate stats
        int totalItems = orderItems.stream().mapToInt(OrderItem::getQuantity).sum();
        double subtotal = orderItems.stream()
            .mapToDouble(item -> item.getMenuItem().getPrice() * item.getQuantity())
            .sum();

        // Create stats rows
        HBox itemsRow = createStatsRow("📦 Total Items:", String.valueOf(totalItems));
        HBox subtotalRow = createStatsRow("💵 Subtotal:", "₹" + String.format("%.2f", subtotal));
        HBox tableRow = createStatsRow("🏪 Table:", selectedTable);
        HBox statusRow = createStatsRow("📊 Status:", orderItems.isEmpty() ? "Empty" : "Active");

        section.getChildren().addAll(itemsRow, subtotalRow, tableRow, statusRow);
        return section;
    }

    /**
     * Create a stats row
     */
    private HBox createStatsRow(String label, String value) {
        HBox row = new HBox();
        row.setAlignment(Pos.CENTER_LEFT);

        Label labelText = new Label(label);
        labelText.setStyle("-fx-font-weight: bold;");
        labelText.setPrefWidth(120);

        Label valueText = new Label(value);
        valueText.setStyle("-fx-text-fill: #495057;");

        row.getChildren().addAll(labelText, valueText);
        return row;
    }

    /**
     * Handle Ctrl+S search shortcut
     */
    private void handleSearchShortcut() {
        System.out.println("Search shortcut activated (Ctrl+S) in Menu Selection");

        if (searchField != null) {
            // Focus on search field and select all text
            Platform.runLater(() -> {
                searchField.requestFocus();
                searchField.selectAll();
            });

            // Show visual feedback
            showSearchFeedback();
        }
    }

    /**
     * Show visual feedback for search activation
     */
    private void showSearchFeedback() {
        if (searchField != null) {
            // Temporarily highlight the search field
            String originalStyle = searchField.getStyle();

            searchField.setStyle(originalStyle +
                "-fx-border-color: #007bff; " +
                "-fx-border-width: 3px; " +
                "-fx-background-color: rgba(0, 123, 255, 0.1);"
            );

            // Update placeholder text to show navigation hint
            String originalPrompt = searchField.getPromptText();
            searchField.setPromptText("Type to search, ↑↓ to navigate, Enter to add...");

            // Reset style and prompt after 3 seconds
            Platform.runLater(() -> {
                new Thread(() -> {
                    try {
                        Thread.sleep(3000);
                        Platform.runLater(() -> {
                            if (searchField != null) {
                                searchField.setStyle(originalStyle);
                                searchField.setPromptText(originalPrompt);
                            }
                        });
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }).start();
            });
        }
    }
    
    // Tab Selection Methods
    @FXML
    private void selectDineIn() {
        setActiveTab(dineInTab);
        orderType = "Dine In";
    }
    
    @FXML
    private void selectDelivery() {
        setActiveTab(deliveryTab);
        orderType = "Delivery";
    }
    
    @FXML
    private void selectPickup() {
        setActiveTab(pickupTab);
        orderType = "Pick Up";
    }
    
    private void setActiveTab(Button activeTab) {
        // Remove active class from all tabs
        dineInTab.getStyleClass().remove("tab-active");
        deliveryTab.getStyleClass().remove("tab-active");
        pickupTab.getStyleClass().remove("tab-active");
        
        // Add active class to selected tab
        activeTab.getStyleClass().add("tab-active");
    }
    
    @FXML
    private void searchItems() {
        // This method is called by search button or Enter key
        // But now we use the real-time filtering method
        String searchText = searchField.getText();
        filterMenuItemsRealTime(searchText);
    }
    
    @FXML
    private void saveOrder() {
        if (orderItems.isEmpty()) {
            showAlert("No Items", "Please add items to the order before saving.");
            return;
        }
        
        // In real app, save to database
        showAlert("Order Saved", "Order for " + selectedTable + " has been saved successfully!");
    }
    
    @FXML
    private void printKOT() {
        if (orderItems.isEmpty()) {
            showAlert("No Items", "Please add items to the order before printing KOT.");
            return;
        }
        
        // In real app, print KOT
        showAlert("KOT Printed", "Kitchen Order Ticket has been sent to the kitchen.");
    }
    
    @FXML
    private void generateBill() {
        System.out.println("generateBill() method called");
        if (orderItems.isEmpty()) {
            System.out.println("Order is empty - showing alert");
            showAlert("No Items", "Please add items to the order before generating bill.");
            return;
        }

        System.out.println("Generating bill for " + selectedTable);
        // In real app, generate and print bill
        showAlert("Bill Generated", "Bill for " + selectedTable + " has been generated successfully!");
    }

    @FXML
    private void showOrderDiscountDialog() {
        try {
            // Create order discount dialog
            Dialog<ButtonType> discountDialog = new Dialog<>();
            discountDialog.setTitle("Apply Order Discount");

            double currentSubtotal = orderItems.stream()
                .mapToDouble(item -> item.getMenuItem().getPrice() * item.getQuantity())
                .sum();

            discountDialog.setHeaderText("Current Order Total: ₹" + String.format("%.2f", currentSubtotal) +
                                       (orderDiscountPercent > 0 ?
                                        "\nCurrent Discount: " + String.format("%.1f", orderDiscountPercent) + "%" : ""));

            // Create discount options
            VBox discountContent = new VBox(12);
            discountContent.setPadding(new Insets(20));

            // Quick discount buttons
            Label quickLabel = new Label("Quick Order Discounts:");
            quickLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 14px;");

            HBox quickDiscounts = new HBox(10);
            quickDiscounts.setAlignment(Pos.CENTER);

            Button discount5 = new Button("5%");
            discount5.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-padding: 8 16; -fx-font-size: 12px;");
            discount5.setOnAction(e -> applyOrderDiscount(5.0, discountDialog));

            Button discount10 = new Button("10%");
            discount10.setStyle("-fx-background-color: #ffc107; -fx-text-fill: black; -fx-padding: 8 16; -fx-font-size: 12px;");
            discount10.setOnAction(e -> applyOrderDiscount(10.0, discountDialog));

            Button discount15 = new Button("15%");
            discount15.setStyle("-fx-background-color: #fd7e14; -fx-text-fill: white; -fx-padding: 8 16; -fx-font-size: 12px;");
            discount15.setOnAction(e -> applyOrderDiscount(15.0, discountDialog));

            Button discount20 = new Button("20%");
            discount20.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-padding: 8 16; -fx-font-size: 12px;");
            discount20.setOnAction(e -> applyOrderDiscount(20.0, discountDialog));

            quickDiscounts.getChildren().addAll(discount5, discount10, discount15, discount20);

            // Custom discount input
            Label customLabel = new Label("Custom Order Discount:");
            customLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 14px;");

            HBox customDiscountBox = new HBox(10);
            customDiscountBox.setAlignment(Pos.CENTER);

            TextField customDiscountField = new TextField();
            customDiscountField.setPromptText("Enter discount %");
            customDiscountField.setPrefWidth(120);
            if (orderDiscountPercent > 0) {
                customDiscountField.setText(String.format("%.1f", orderDiscountPercent));
            }

            Button applyCustom = new Button("Apply Custom");
            applyCustom.setStyle("-fx-background-color: #6f42c1; -fx-text-fill: white; -fx-padding: 8 16; -fx-font-size: 12px;");
            applyCustom.setOnAction(e -> {
                try {
                    double customDiscount = Double.parseDouble(customDiscountField.getText());
                    if (customDiscount >= 0 && customDiscount <= 100) {
                        applyOrderDiscount(customDiscount, discountDialog);
                    } else {
                        showAlert("Invalid Discount", "Discount must be between 0% and 100%");
                    }
                } catch (NumberFormatException ex) {
                    showAlert("Invalid Input", "Please enter a valid number");
                }
            });

            customDiscountBox.getChildren().addAll(customDiscountField, applyCustom);

            // Remove discount option
            Button removeDiscount = new Button("Remove Order Discount");
            removeDiscount.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-padding: 10 20; -fx-font-size: 12px;");
            removeDiscount.setOnAction(e -> {
                orderDiscountPercent = 0.0;
                updateOrderDisplay();
                showAlert("Discount Removed", "Order discount has been removed.");
                discountDialog.close();
            });

            discountContent.getChildren().addAll(
                quickLabel,
                quickDiscounts,
                customLabel,
                customDiscountBox,
                removeDiscount
            );

            discountDialog.getDialogPane().setContent(discountContent);
            discountDialog.getDialogPane().getButtonTypes().add(ButtonType.CANCEL);
            discountDialog.showAndWait();

        } catch (Exception e) {
            showAlert("Error", "Failed to show order discount options: " + e.getMessage());
        }
    }

    private void applyOrderDiscount(double discountPercent, Dialog<ButtonType> dialog) {
        try {
            double currentSubtotal = orderItems.stream()
                .mapToDouble(item -> item.getMenuItem().getPrice() * item.getQuantity())
                .sum();

            double discountAmount = currentSubtotal * (discountPercent / 100);

            orderDiscountPercent = discountPercent;
            updateOrderDisplay();

            // Show confirmation
            showAlert("Order Discount Applied",
                     "Order Discount: " + String.format("%.1f", discountPercent) + "%\n" +
                     "Discount Amount: ₹" + String.format("%.2f", discountAmount) + "\n" +
                     "Applied to entire order total!");

            dialog.close();

        } catch (Exception e) {
            showAlert("Error", "Failed to apply order discount: " + e.getMessage());
        }
    }
    
    @FXML
    private void goBack() {
        try {
            // Clean up event handlers before navigating away
            cleanupEventHandlers();

            // Use universal navigation manager for robust back navigation
            UniversalNavigationManager.getInstance().goBack();

        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Could not go back: " + e.getMessage());
        }
    }

    /**
     * Robust navigation to dashboard that works in all scenarios
     */
    private void navigateToDashboard() {
        try {
            System.out.println("MenuSelectionController: Starting robust navigation to dashboard...");

            // Load dashboard FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/Dashboard.fxml"));
            Parent dashboardView = loader.load();

            // Method 1: Try to get stage from any available UI element
            Stage stage = getStageFromCurrentContext();

            if (stage != null) {
                performNavigation(stage, dashboardView, loader);
                return;
            }

            // Method 2: Use SceneEventHandlerManager to get the primary stage
            stage = SceneEventHandlerManager.getInstance().getPrimaryStage();
            if (stage != null) {
                System.out.println("MenuSelectionController: Using primary stage from SceneEventHandlerManager");
                performNavigation(stage, dashboardView, loader);
                return;
            }

            // Method 3: Get stage from JavaFX Application Thread
            Platform.runLater(() -> {
                try {
                    Stage fallbackStage = getStageFromApplicationContext();
                    if (fallbackStage != null) {
                        performNavigation(fallbackStage, dashboardView, loader);
                    } else {
                        // Method 4: Create new stage if absolutely necessary (last resort)
                        createNewStageForDashboard(dashboardView, loader);
                    }
                } catch (Exception e) {
                    System.err.println("MenuSelectionController: All navigation methods failed: " + e.getMessage());
                    showAlert("Error", "Could not return to dashboard. Please restart the application.");
                }
            });

        } catch (Exception e) {
            System.err.println("MenuSelectionController: Navigation failed: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Navigation failed: " + e.getMessage());
        }
    }

    /**
     * Get stage from current UI context
     */
    private Stage getStageFromCurrentContext() {
        try {
            // Try all UI elements in order of reliability
            Node[] elements = {tableLabel, menuGrid, dineInTab, deliveryTab, pickupTab,
                             searchField, categoriesList, orderItemsContainer};

            for (Node element : elements) {
                if (element != null && element.getScene() != null &&
                    element.getScene().getWindow() instanceof Stage) {
                    Stage stage = (Stage) element.getScene().getWindow();
                    System.out.println("MenuSelectionController: Found stage via " + element.getClass().getSimpleName());
                    return stage;
                }
            }

            System.out.println("MenuSelectionController: No stage found from current UI context");
            return null;

        } catch (Exception e) {
            System.err.println("MenuSelectionController: Error getting stage from current context: " + e.getMessage());
            return null;
        }
    }

    /**
     * Get stage from JavaFX application context
     */
    private Stage getStageFromApplicationContext() {
        try {
            // Get all open stages
            for (javafx.stage.Window window : javafx.stage.Window.getWindows()) {
                if (window instanceof Stage && window.isShowing()) {
                    System.out.println("MenuSelectionController: Found stage from application context");
                    return (Stage) window;
                }
            }

            System.out.println("MenuSelectionController: No stage found from application context");
            return null;

        } catch (Exception e) {
            System.err.println("MenuSelectionController: Error getting stage from application context: " + e.getMessage());
            return null;
        }
    }

    /**
     * Perform the actual navigation
     */
    private void performNavigation(Stage stage, Parent dashboardView, FXMLLoader loader) {
        try {
            // Set the new scene root
            stage.getScene().setRoot(dashboardView);

            // Initialize dashboard controller
            DashboardController dashboardController = loader.getController();
            if (dashboardController != null) {
                System.out.println("MenuSelectionController: Successfully navigated back to dashboard");
            }

        } catch (Exception e) {
            System.err.println("MenuSelectionController: Error performing navigation: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Create new stage as last resort
     */
    private void createNewStageForDashboard(Parent dashboardView, FXMLLoader loader) {
        try {
            System.out.println("MenuSelectionController: Creating new stage as last resort");

            Stage newStage = new Stage();
            Scene scene = new Scene(dashboardView);
            newStage.setScene(scene);
            newStage.setTitle("Restaurant Management System");
            newStage.setMaximized(true);

            // Initialize dashboard controller
            DashboardController dashboardController = loader.getController();
            if (dashboardController != null) {
                System.out.println("MenuSelectionController: Dashboard loaded in new stage");
            }

            newStage.show();

            // Close any existing stages that might be orphaned
            for (javafx.stage.Window window : javafx.stage.Window.getWindows()) {
                if (window instanceof Stage && window != newStage && window.isShowing()) {
                    ((Stage) window).close();
                }
            }

        } catch (Exception e) {
            System.err.println("MenuSelectionController: Error creating new stage: " + e.getMessage());
            showAlert("Error", "Could not create new window. Please restart the application.");
        }
    }



    /**
     * Clean up event handlers to prevent conflicts when navigating away
     */
    private void cleanupEventHandlers() {
        try {
            System.out.println("MenuSelectionController: Cleaning up event handlers...");

            // Use centralized event handler manager for cleanup
            // Use cleanupNonDashboardHandlers to preserve dashboard global shortcuts
            Scene scene = null;

            // Try to find a valid scene from available UI elements
            if (tableLabel != null && tableLabel.getScene() != null) {
                scene = tableLabel.getScene();
            } else if (menuGrid != null && menuGrid.getScene() != null) {
                scene = menuGrid.getScene();
            } else if (dineInTab != null && dineInTab.getScene() != null) {
                scene = dineInTab.getScene();
            }

            if (scene != null) {
                SceneEventHandlerManager.getInstance().cleanupNonDashboardHandlers(scene);
                System.out.println("MenuSelectionController: Event handlers cleaned up successfully");
            } else {
                System.out.println("MenuSelectionController: No valid scene found for cleanup");
            }

            // Clear any stored references
            selectedTable = null;
            currentTableNumber = 0;

        } catch (Exception e) {
            System.err.println("MenuSelectionController: Error during cleanup: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void showOrderItemDiscountDialog(OrderItem orderItem) {
        try {
            // Create discount options dialog for existing order item
            Dialog<ButtonType> discountDialog = new Dialog<>();
            discountDialog.setTitle("Apply Discount to Order Item");
            discountDialog.setHeaderText("Item: " + orderItem.getMenuItem().getName() +
                                       "\nQuantity: " + orderItem.getQuantity() +
                                       "\nCurrent Price: ₹" + String.format("%.2f", orderItem.getMenuItem().getPrice()));

            // Create discount options
            VBox discountContent = new VBox(10);
            discountContent.setPadding(new Insets(20));

            // Quick discount buttons
            Label quickLabel = new Label("Quick Discounts:");
            quickLabel.setStyle("-fx-font-weight: bold;");

            HBox quickDiscounts = new HBox(8);
            quickDiscounts.setAlignment(Pos.CENTER);

            Button discount5 = new Button("5%");
            discount5.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-padding: 6 10; -fx-font-size: 10px;");
            discount5.setOnAction(e -> applyDiscountToOrderItem(orderItem, 5.0, discountDialog));

            Button discount10 = new Button("10%");
            discount10.setStyle("-fx-background-color: #ffc107; -fx-text-fill: black; -fx-padding: 6 10; -fx-font-size: 10px;");
            discount10.setOnAction(e -> applyDiscountToOrderItem(orderItem, 10.0, discountDialog));

            Button discount15 = new Button("15%");
            discount15.setStyle("-fx-background-color: #fd7e14; -fx-text-fill: white; -fx-padding: 6 10; -fx-font-size: 10px;");
            discount15.setOnAction(e -> applyDiscountToOrderItem(orderItem, 15.0, discountDialog));

            Button discount20 = new Button("20%");
            discount20.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-padding: 6 10; -fx-font-size: 10px;");
            discount20.setOnAction(e -> applyDiscountToOrderItem(orderItem, 20.0, discountDialog));

            quickDiscounts.getChildren().addAll(discount5, discount10, discount15, discount20);

            // Custom discount input
            Label customLabel = new Label("Custom Discount:");
            customLabel.setStyle("-fx-font-weight: bold;");

            HBox customDiscountBox = new HBox(8);
            customDiscountBox.setAlignment(Pos.CENTER);

            TextField customDiscountField = new TextField();
            customDiscountField.setPromptText("Enter %");
            customDiscountField.setPrefWidth(80);

            Button applyCustom = new Button("Apply");
            applyCustom.setStyle("-fx-background-color: #6f42c1; -fx-text-fill: white; -fx-padding: 6 12; -fx-font-size: 10px;");
            applyCustom.setOnAction(e -> {
                try {
                    double customDiscount = Double.parseDouble(customDiscountField.getText());
                    if (customDiscount >= 0 && customDiscount <= 100) {
                        applyDiscountToOrderItem(orderItem, customDiscount, discountDialog);
                    } else {
                        showAlert("Invalid Discount", "Discount must be between 0% and 100%");
                    }
                } catch (NumberFormatException ex) {
                    showAlert("Invalid Input", "Please enter a valid number");
                }
            });

            customDiscountBox.getChildren().addAll(customDiscountField, applyCustom);

            // Remove discount option (if already discounted)
            Button removeDiscount = new Button("Remove Discount");
            removeDiscount.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-padding: 8 16; -fx-font-size: 10px;");
            removeDiscount.setOnAction(e -> {
                removeDiscountFromOrderItem(orderItem);
                discountDialog.close();
            });

            discountContent.getChildren().addAll(
                quickLabel,
                quickDiscounts,
                customLabel,
                customDiscountBox,
                removeDiscount
            );

            discountDialog.getDialogPane().setContent(discountContent);
            discountDialog.getDialogPane().getButtonTypes().add(ButtonType.CANCEL);
            discountDialog.showAndWait();

        } catch (Exception e) {
            showAlert("Error", "Failed to show discount options: " + e.getMessage());
        }
    }

    private void applyDiscountToOrderItem(OrderItem orderItem, double discountPercent, Dialog<ButtonType> dialog) {
        try {
            MenuItem originalItem = orderItem.getMenuItem();
            double originalPrice = originalItem.getPrice();
            double discountedPrice = originalPrice * (1 - discountPercent / 100);
            double discountAmount = originalPrice - discountedPrice;

            // Create a new MenuItem with discounted price
            String discountedName = originalItem.getName();
            // Remove existing discount info if present
            if (discountedName.contains("(") && discountedName.contains("% OFF)")) {
                discountedName = discountedName.substring(0, discountedName.indexOf("(")).trim();
            }
            discountedName += " (" + String.format("%.0f", discountPercent) + "% OFF)";

            MenuItem discountedItem = new MenuItem(
                originalItem.getId(),
                discountedName,
                discountedPrice,
                originalItem.getCategory()
            );

            // Update the order item with discounted item
            int index = orderItems.indexOf(orderItem);
            if (index >= 0) {
                OrderItem newOrderItem = new OrderItem(discountedItem, orderItem.getQuantity());
                orderItems.set(index, newOrderItem);
            }

            // Update display
            updateOrderDisplay();

            // Show confirmation
            showAlert("Discount Applied",
                     "Item: " + originalItem.getName() + "\n" +
                     "Quantity: " + orderItem.getQuantity() + "\n" +
                     "Original Price: ₹" + String.format("%.2f", originalPrice) + " each\n" +
                     "Discount: " + String.format("%.1f", discountPercent) + "% (₹" + String.format("%.2f", discountAmount) + " each)\n" +
                     "New Price: ₹" + String.format("%.2f", discountedPrice) + " each\n" +
                     "Total Savings: ₹" + String.format("%.2f", discountAmount * orderItem.getQuantity()));

            dialog.close();

        } catch (Exception e) {
            showAlert("Error", "Failed to apply discount: " + e.getMessage());
        }
    }

    private void removeDiscountFromOrderItem(OrderItem orderItem) {
        try {
            MenuItem currentItem = orderItem.getMenuItem();
            String itemName = currentItem.getName();

            // Check if item has discount
            if (itemName.contains("(") && itemName.contains("% OFF)")) {
                // Extract original name
                String originalName = itemName.substring(0, itemName.indexOf("(")).trim();

                // Find original price (this is simplified - in real app, store original price)
                double originalPrice = getOriginalPrice(originalName);

                // Create original MenuItem
                MenuItem originalItem = new MenuItem(
                    currentItem.getId(),
                    originalName,
                    originalPrice,
                    currentItem.getCategory()
                );

                // Update the order item
                int index = orderItems.indexOf(orderItem);
                if (index >= 0) {
                    OrderItem newOrderItem = new OrderItem(originalItem, orderItem.getQuantity());
                    orderItems.set(index, newOrderItem);
                }

                // Update display
                updateOrderDisplay();

                showAlert("Discount Removed", "Discount has been removed from " + originalName);
            } else {
                showAlert("No Discount", "This item doesn't have any discount applied.");
            }

        } catch (Exception e) {
            showAlert("Error", "Failed to remove discount: " + e.getMessage());
        }
    }

    /**
     * Remove discount from menu item (updates the menu display)
     */
    private void removeDiscountFromMenuItem(MenuItem item) {
        try {
            String itemName = item.getName();

            // Check if item has discount
            if (itemName.contains("(") && itemName.contains("% OFF)")) {
                // Get original name and price
                String originalName = getOriginalItemName(itemName);
                double originalPrice = getOriginalPrice(originalName);

                // Update the menu item in the list
                for (int i = 0; i < menuItems.size(); i++) {
                    MenuItem menuItem = menuItems.get(i);
                    if (menuItem.getId() == item.getId()) {
                        // Replace with original item (no discount)
                        MenuItem originalItem = new MenuItem(
                            item.getId(),
                            originalName,
                            originalPrice,
                            item.getCategory()
                        );
                        menuItems.set(i, originalItem);
                        break;
                    }
                }

                // Refresh the menu display
                displayMenuItems(menuItems);

                // Show confirmation
                showAlert("Discount Removed",
                         "Discount removed from " + originalName + "\n" +
                         "Item now shows original price: ₹" + String.format("%.2f", originalPrice));

            } else {
                // No discount to remove
                showAlert("No Discount", "This item doesn't have any discount to remove.");
            }

        } catch (Exception e) {
            System.err.println("Error removing discount from menu item: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Failed to remove discount: " + e.getMessage());
        }
    }

    /**
     * Get original item name without discount text
     */
    private String getOriginalItemName(String itemName) {
        if (itemName.contains("(") && itemName.contains("% OFF)")) {
            // Remove discount text like "(15% OFF)"
            return itemName.substring(0, itemName.indexOf("(")).trim();
        }
        return itemName; // No discount text found
    }

    private double getOriginalPrice(String itemName) {
        // Sample original prices - in real app, this would come from database
        switch (itemName.toLowerCase()) {
            case "aloo tikki burger": return 85.00;
            case "cheese garlic bread": return 120.00;
            case "chicken angara": return 180.00;
            case "chili mushroom": return 150.00;
            case "dahi ke shola": return 95.00;
            case "fry masala papad": return 65.00;
            case "green salad": return 80.00;
            case "grilled paneer sandwich": return 140.00;
            case "hakka noodles": return 130.00;
            case "masala dosa": return 95.00;
            case "omelette (3 eggs)": return 60.00;
            case "oreo shake": return 85.00;
            case "paneer wrap": return 140.00;
            case "raj kachori": return 100.00;
            case "rasmalai": return 75.00;
            case "salted lassi": return 85.00;
            case "spl shahi paneer": return 180.00;
            case "spring roll": return 120.00;
            case "strawberry mojito": return 75.00;
            case "sweet corn soup": return 65.00;
            case "tandoori momos (8 pcs)": return 120.00;
            case "tandoori pasta": return 150.00;
            case "veg burger": return 95.00;
            default: return 100.00; // Default price
        }
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    // Data Classes
    public static class MenuItem {
        private int id;
        private String name;
        private double price;
        private String category;
        
        public MenuItem(int id, String name, double price, String category) {
            this.id = id;
            this.name = name;
            this.price = price;
            this.category = category;
        }
        
        // Getters
        public int getId() { return id; }
        public String getName() { return name; }
        public double getPrice() { return price; }
        public String getCategory() { return category; }
    }
    
    public static class OrderItem {
        private MenuItem menuItem;
        private int quantity;
        
        public OrderItem(MenuItem menuItem, int quantity) {
            this.menuItem = menuItem;
            this.quantity = quantity;
        }
        
        // Getters and Setters
        public MenuItem getMenuItem() { return menuItem; }
        public int getQuantity() { return quantity; }
        public void setQuantity(int quantity) { this.quantity = quantity; }
    }
}
