<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.shape.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.VoiceInputController">
   <!-- Compact Voice Input Component -->
   <VBox fx:id="voiceInputContainer" styleClass="voice-input-container" spacing="12.0">
      <children>
         <!-- Voice Activation Section -->
         <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="voice-activation-section">
            <children>
               <!-- Mic <PERSON><PERSON> with Animation Container -->
               <StackPane styleClass="mic-button-container">
                  <children>
                     <!-- Animated Pulse Ring -->
                     <Circle fx:id="pulseRing" radius="30.0" styleClass="pulse-ring" visible="false" />
                     
                     <!-- Waveform Animation Container -->
                     <HBox fx:id="waveformContainer" alignment="CENTER" spacing="2.0" styleClass="waveform-container" visible="false">
                        <children>
                           <Rectangle fx:id="wave1" width="3.0" height="8.0" styleClass="waveform-bar" />
                           <Rectangle fx:id="wave2" width="3.0" height="12.0" styleClass="waveform-bar" />
                           <Rectangle fx:id="wave3" width="3.0" height="16.0" styleClass="waveform-bar" />
                           <Rectangle fx:id="wave4" width="3.0" height="20.0" styleClass="waveform-bar" />
                           <Rectangle fx:id="wave5" width="3.0" height="16.0" styleClass="waveform-bar" />
                           <Rectangle fx:id="wave6" width="3.0" height="12.0" styleClass="waveform-bar" />
                           <Rectangle fx:id="wave7" width="3.0" height="8.0" styleClass="waveform-bar" />
                        </children>
                     </HBox>
                     
                     <!-- Main Mic Button -->
                     <Button fx:id="micButton" onAction="#handleMicButtonClick" styleClass="voice-mic-button">
                        <graphic>
                           <Label fx:id="micIcon" text="🎤" styleClass="mic-icon" />
                        </graphic>
                        <tooltip>
                           <Tooltip fx:id="micTooltip" text="Click to speak" />
                        </tooltip>
                     </Button>
                  </children>
               </StackPane>
               
               <!-- Voice Status and Controls -->
               <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                  <children>
                     <!-- Status Text -->
                     <HBox alignment="CENTER_LEFT" spacing="8.0">
                        <children>
                           <Label fx:id="statusIcon" text="💬" styleClass="voice-status-icon" />
                           <Text fx:id="statusText" styleClass="voice-status-text" text="Ready to listen" />
                        </children>
                     </HBox>
                     
                     <!-- Quick Command Hints -->
                     <HBox fx:id="commandHints" alignment="CENTER_LEFT" spacing="10.0" styleClass="command-hints" visible="false">
                        <children>
                           <Label text="Try:" styleClass="hint-label" />
                           <Button text="Forecast" styleClass="hint-button" onAction="#insertHint" userData="forecast next 30 days" />
                           <Button text="Search" styleClass="hint-button" onAction="#insertHint" userData="search butter chicken" />
                           <Button text="Table" styleClass="hint-button" onAction="#insertHint" userData="open table 5" />
                        </children>
                     </HBox>
                  </children>
               </VBox>
               
               <!-- Voice Settings -->
               <MenuButton fx:id="voiceSettingsButton" styleClass="voice-settings-button" text="⚙️">
                  <items>
                     <CheckMenuItem fx:id="autoSubmitMenuItem" text="Auto-submit commands" selected="true" />
                     <CheckMenuItem fx:id="showHintsMenuItem" text="Show command hints" selected="true" />
                     <SeparatorMenuItem />
                     <MenuItem fx:id="testMicMenuItem" text="Test microphone" onAction="#testMicrophone" />
                     <MenuItem fx:id="voiceHelpMenuItem" text="Voice commands help" onAction="#showVoiceHelp" />
                  </items>
               </MenuButton>
            </children>
         </HBox>
         
         <!-- Speech-to-Text Display Area -->
         <VBox fx:id="transcriptionArea" styleClass="transcription-area" visible="false" spacing="10.0">
            <children>
               <!-- Transcription Header -->
               <HBox alignment="CENTER_LEFT" spacing="10.0">
                  <children>
                     <Label text="📝" styleClass="transcription-icon" />
                     <Text text="Speech Recognition" styleClass="transcription-title" />
                     <Region HBox.hgrow="ALWAYS" />
                     <Button fx:id="clearTranscriptionButton" text="🗑️" styleClass="clear-button" onAction="#clearTranscription" />
                  </children>
               </HBox>
               
               <!-- Live Transcription Display -->
               <ScrollPane styleClass="transcription-scroll" fitToWidth="true" prefHeight="80.0">
                  <content>
                     <TextArea fx:id="transcriptionText" styleClass="transcription-text" 
                              promptText="Your speech will appear here..." 
                              wrapText="true" editable="true" prefRowCount="3" />
                  </content>
               </ScrollPane>
               
               <!-- Command Processing Status -->
               <HBox fx:id="commandStatus" alignment="CENTER_LEFT" spacing="10.0" styleClass="command-status" visible="false">
                  <children>
                     <Label fx:id="commandStatusIcon" text="🔄" styleClass="command-status-icon" />
                     <Text fx:id="commandStatusText" styleClass="command-status-text" text="Processing command..." />
                     <Region HBox.hgrow="ALWAYS" />
                     <ProgressIndicator fx:id="commandProgress" styleClass="command-progress" prefWidth="20.0" prefHeight="20.0" visible="false" />
                  </children>
               </HBox>
               
               <!-- Action Buttons -->
               <HBox spacing="12.0" alignment="CENTER_LEFT">
                  <children>
                     <Button fx:id="submitCommandButton" text="✅ Execute Command" styleClass="voice-submit-button" 
                            onAction="#submitCommand" disable="true" />
                     <Button fx:id="tryAgainButton" text="🔄 Try Again" styleClass="voice-retry-button" 
                            onAction="#tryAgain" />
                     <Button fx:id="cancelVoiceButton" text="❌ Cancel" styleClass="voice-cancel-button" 
                            onAction="#cancelVoice" />
                  </children>
               </HBox>
            </children>
         </VBox>
         
         <!-- Command Suggestions Panel -->
         <VBox fx:id="suggestionsPanel" styleClass="suggestions-panel" visible="false" spacing="8.0">
            <children>
               <HBox alignment="CENTER_LEFT" spacing="8.0">
                  <children>
                     <Label text="💡" styleClass="suggestions-icon" />
                     <Text text="Command Suggestions" styleClass="suggestions-title" />
                  </children>
               </HBox>
               
               <FlowPane fx:id="suggestionsFlow" hgap="8.0" vgap="6.0" styleClass="suggestions-flow">
                  <!-- Dynamic suggestion buttons will be added here -->
               </FlowPane>
            </children>
         </VBox>
         
         <!-- Command Match Result -->
         <HBox fx:id="matchResultContainer" alignment="CENTER_LEFT" spacing="12.0" styleClass="match-result-container" visible="false">
            <children>
               <Label fx:id="matchResultIcon" text="✅" styleClass="match-result-icon" />
               <VBox spacing="4.0" HBox.hgrow="ALWAYS">
                  <children>
                     <Text fx:id="matchResultTitle" styleClass="match-result-title" text="Command Recognized" />
                     <Text fx:id="matchResultDescription" styleClass="match-result-description" text="Executing forecast for next 30 days..." />
                  </children>
               </VBox>
               <Button fx:id="executeMatchButton" text="▶️ Execute" styleClass="execute-match-button" onAction="#executeMatchedCommand" />
            </children>
         </HBox>
         
         <!-- Error/Feedback Display -->
         <HBox fx:id="errorContainer" alignment="CENTER_LEFT" spacing="12.0" styleClass="error-container" visible="false">
            <children>
               <Label fx:id="errorIcon" text="❌" styleClass="error-icon" />
               <VBox spacing="4.0" HBox.hgrow="ALWAYS">
                  <children>
                     <Text fx:id="errorTitle" styleClass="error-title" text="Command Not Understood" />
                     <Text fx:id="errorDescription" styleClass="error-description" text="Please try speaking more clearly or use a different command." />
                  </children>
               </VBox>
               <Button fx:id="retryErrorButton" text="🔄 Retry" styleClass="retry-error-button" onAction="#retryVoiceInput" />
            </children>
         </HBox>
      </children>
   </VBox>
</VBox>
