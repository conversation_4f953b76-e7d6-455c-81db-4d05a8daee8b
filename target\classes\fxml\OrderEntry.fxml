<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.OrderEntryController" stylesheets="@../css/application.css">
   <children>
      <HBox spacing="20.0" styleClass="order-header">
         <children>
            <Button mnemonicParsing="false" onAction="#goBack" styleClass="back-button" text="← Back" />
            <VBox spacing="10.0">
               <children>
                  <Label styleClass="section-title" text="Order Details">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  <HBox spacing="15.0">
                     <children>
                        <VBox spacing="5.0">
                           <children>
                              <Label text="Table Number:" />
                              <TextField fx:id="tableNumberField" prefWidth="100.0" promptText="Table #" />
                           </children>
                        </VBox>
                        <VBox spacing="5.0">
                           <children>
                              <Label text="Order Type:" />
                              <CheckBox fx:id="takeawayCheckbox" text="Takeaway" />
                           </children>
                        </VBox>
                     </children>
                  </HBox>
               </children>
            </VBox>
            <Region HBox.hgrow="ALWAYS" />
            <VBox alignment="TOP_RIGHT" spacing="10.0">
               <children>
                  <Button fx:id="newOrderBtn" mnemonicParsing="false" onAction="#newOrder" styleClass="action-button, new-button" text="🆕 New Order" />
                  <Button fx:id="saveOrderBtn" mnemonicParsing="false" onAction="#saveOrder" styleClass="action-button, save-button" text="💾 Save Order" />
               </children>
            </VBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </HBox>
      
      <HBox spacing="20.0" VBox.vgrow="ALWAYS">
         <children>
            <!-- Menu Items Section -->
            <VBox prefWidth="400.0" spacing="10.0" styleClass="menu-section">
               <children>
                  <Label styleClass="section-title" text="Menu Items">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  
                  <HBox spacing="10.0">
                     <children>
                        <ComboBox fx:id="categoryComboBox" onAction="#filterByCategory" prefWidth="150.0" promptText="All Categories" />
                        <TextField fx:id="searchField" onKeyReleased="#searchItems" prefWidth="200.0" promptText="Search items..." />
                     </children>
                  </HBox>
                  
                  <TableView fx:id="menuTable" VBox.vgrow="ALWAYS">
                     <columns>
                        <TableColumn fx:id="nameColumn" prefWidth="200.0" text="Item Name" />
                        <TableColumn fx:id="priceColumn" prefWidth="80.0" text="Price" />
                        <TableColumn fx:id="categoryColumn" prefWidth="100.0" text="Category" />
                     </columns>
                  </TableView>
                  
                  <HBox spacing="10.0">
                     <children>
                        <Label text="Quantity:" />
                        <Spinner fx:id="quantitySpinner" prefWidth="80.0" />
                        <Button mnemonicParsing="false" onAction="#addToOrder" styleClass="action-button, add-button" text="➕ Add to Order" />
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            
            <!-- Current Order Section -->
            <VBox prefWidth="400.0" spacing="10.0" styleClass="order-section">
               <children>
                  <Label styleClass="section-title" text="Current Order">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
                  
                  <TableView fx:id="orderTable" VBox.vgrow="ALWAYS">
                     <columns>
                        <TableColumn fx:id="orderItemColumn" prefWidth="180.0" text="Item" />
                        <TableColumn fx:id="orderQuantityColumn" prefWidth="60.0" text="Qty" />
                        <TableColumn fx:id="orderPriceColumn" prefWidth="80.0" text="Price" />
                        <TableColumn fx:id="orderTotalColumn" prefWidth="80.0" text="Total" />
                     </columns>
                  </TableView>
                  
                  <HBox spacing="10.0">
                     <children>
                        <Button mnemonicParsing="false" onAction="#removeFromOrder" styleClass="action-button, remove-button" text="❌ Remove" />
                        <Button mnemonicParsing="false" onAction="#clearOrder" styleClass="action-button, clear-button" text="🗑️ Clear All" />
                     </children>
                  </HBox>
                  
                  <!-- Order Summary -->
                  <VBox spacing="8.0" styleClass="order-summary">
                     <children>
                        <HBox>
                           <children>
                              <Label text="Subtotal:" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Label fx:id="subtotalLabel" text="₹0.00" />
                           </children>
                        </HBox>
                        <HBox>
                           <children>
                              <Label text="GST (18%):" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Label fx:id="gstLabel" text="₹0.00" />
                           </children>
                        </HBox>
                        <HBox>
                           <children>
                              <Label text="Service Charge (10%):" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Label fx:id="serviceChargeLabel" text="₹0.00" />
                           </children>
                        </HBox>
                        <Separator />
                        <HBox>
                           <children>
                              <Label styleClass="total-label" text="Grand Total:">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Region HBox.hgrow="ALWAYS" />
                              <Label fx:id="grandTotalLabel" styleClass="total-amount" text="₹0.00">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                           </children>
                        </HBox>
                     </children>
                     <padding>
                        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                     </padding>
                  </VBox>
                  
                  <HBox spacing="10.0">
                     <children>
                        <Button fx:id="generateKOTBtn" mnemonicParsing="false" onAction="#generateKOT" prefWidth="120.0" styleClass="action-button, kot-button" text="📄 Generate KOT" />
                        <Button fx:id="generateBillBtn" mnemonicParsing="false" onAction="#generateBill" prefWidth="120.0" styleClass="action-button, bill-button" text="🧾 Generate Bill" />
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" />
         </padding>
      </HBox>
   </children>
</VBox>
