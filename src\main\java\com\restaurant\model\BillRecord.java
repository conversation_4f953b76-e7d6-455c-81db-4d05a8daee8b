package com.restaurant.model;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Model class representing a bill record for previous bills functionality
 * Used for displaying and managing historical billing data
 */
public class BillRecord {
    
    private String billId;
    private int tableNumber;
    private LocalDateTime billTime;
    private double amount;
    private String status;
    private String paymentMethod;
    private String customerName;
    private String staffName;
    private int itemCount;
    private double taxAmount;
    private double discountAmount;
    
    // Constructors
    public BillRecord() {
        this.billTime = LocalDateTime.now();
        this.status = "PENDING";
        this.paymentMethod = "CASH";
    }
    
    public BillRecord(String billId, int tableNumber, LocalDateTime billTime, double amount, String status) {
        this();
        this.billId = billId;
        this.tableNumber = tableNumber;
        this.billTime = billTime;
        this.amount = amount;
        this.status = status;
    }
    
    public BillRecord(String billId, int tableNumber, LocalDateTime billTime, double amount, String status, String paymentMethod) {
        this(billId, tableNumber, billTime, amount, status);
        this.paymentMethod = paymentMethod;
    }
    
    // Getters and Setters
    
    public String getBillId() {
        return billId;
    }
    
    public void setBillId(String billId) {
        this.billId = billId;
    }
    
    public int getTableNumber() {
        return tableNumber;
    }
    
    public void setTableNumber(int tableNumber) {
        this.tableNumber = tableNumber;
    }
    
    public LocalDateTime getBillTime() {
        return billTime;
    }
    
    public void setBillTime(LocalDateTime billTime) {
        this.billTime = billTime;
    }
    
    public double getAmount() {
        return amount;
    }
    
    public void setAmount(double amount) {
        this.amount = amount;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getPaymentMethod() {
        return paymentMethod;
    }
    
    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }
    
    public String getCustomerName() {
        return customerName;
    }
    
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
    
    public String getStaffName() {
        return staffName;
    }
    
    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }
    
    public int getItemCount() {
        return itemCount;
    }
    
    public void setItemCount(int itemCount) {
        this.itemCount = itemCount;
    }
    
    public double getTaxAmount() {
        return taxAmount;
    }
    
    public void setTaxAmount(double taxAmount) {
        this.taxAmount = taxAmount;
    }
    
    public double getDiscountAmount() {
        return discountAmount;
    }
    
    public void setDiscountAmount(double discountAmount) {
        this.discountAmount = discountAmount;
    }
    
    // Utility methods
    
    /**
     * Get formatted time for display
     */
    public String getFormattedTime() {
        if (billTime != null) {
            return billTime.format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm"));
        }
        return "";
    }
    
    /**
     * Get formatted date only
     */
    public String getFormattedDate() {
        if (billTime != null) {
            return billTime.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"));
        }
        return "";
    }
    
    /**
     * Get formatted time only
     */
    public String getFormattedTimeOnly() {
        if (billTime != null) {
            return billTime.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        }
        return "";
    }
    
    /**
     * Get formatted amount with currency
     */
    public String getFormattedAmount() {
        return "₹" + String.format("%.2f", amount);
    }
    
    /**
     * Get status icon
     */
    public String getStatusIcon() {
        switch (status) {
            case "PAID":
                return "✅";
            case "PENDING":
                return "⏳";
            case "CANCELLED":
                return "❌";
            case "REFUNDED":
                return "↩️";
            default:
                return "❓";
        }
    }
    
    /**
     * Get payment method icon
     */
    public String getPaymentMethodIcon() {
        switch (paymentMethod) {
            case "CASH":
                return "💵";
            case "CARD":
                return "💳";
            case "UPI":
                return "📱";
            case "ONLINE":
                return "🌐";
            default:
                return "💰";
        }
    }
    
    /**
     * Check if bill is from today
     */
    public boolean isToday() {
        if (billTime != null) {
            LocalDateTime now = LocalDateTime.now();
            return billTime.toLocalDate().equals(now.toLocalDate());
        }
        return false;
    }
    
    /**
     * Check if bill is recent (within last hour)
     */
    public boolean isRecent() {
        if (billTime != null) {
            LocalDateTime now = LocalDateTime.now();
            return billTime.isAfter(now.minusHours(1));
        }
        return false;
    }
    
    /**
     * Get bill summary for display
     */
    public String getSummary() {
        return String.format("Bill %s - Table %d - %s - %s", 
                           billId, tableNumber, getFormattedAmount(), status);
    }
    
    /**
     * Calculate subtotal (amount without tax)
     */
    public double getSubtotal() {
        return amount - taxAmount;
    }
    
    /**
     * Calculate net amount (after discount)
     */
    public double getNetAmount() {
        return amount - discountAmount;
    }
    
    @Override
    public String toString() {
        return "BillRecord{" +
                "billId='" + billId + '\'' +
                ", tableNumber=" + tableNumber +
                ", billTime=" + billTime +
                ", amount=" + amount +
                ", status='" + status + '\'' +
                ", paymentMethod='" + paymentMethod + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        BillRecord that = (BillRecord) obj;
        return billId != null ? billId.equals(that.billId) : that.billId == null;
    }
    
    @Override
    public int hashCode() {
        return billId != null ? billId.hashCode() : 0;
    }
    
    // Static factory methods
    
    /**
     * Create a paid bill record
     */
    public static BillRecord createPaidBill(String billId, int tableNumber, double amount, String paymentMethod) {
        BillRecord bill = new BillRecord(billId, tableNumber, LocalDateTime.now(), amount, "PAID", paymentMethod);
        return bill;
    }
    
    /**
     * Create a pending bill record
     */
    public static BillRecord createPendingBill(String billId, int tableNumber, double amount) {
        BillRecord bill = new BillRecord(billId, tableNumber, LocalDateTime.now(), amount, "PENDING");
        return bill;
    }
    
    /**
     * Create a sample bill for testing
     */
    public static BillRecord createSampleBill() {
        BillRecord bill = new BillRecord();
        bill.setBillId("BILL" + System.currentTimeMillis());
        bill.setTableNumber(5);
        bill.setAmount(1060.0);
        bill.setStatus("PAID");
        bill.setPaymentMethod("CASH");
        bill.setItemCount(3);
        bill.setTaxAmount(190.80);
        bill.setDiscountAmount(0.0);
        return bill;
    }
}
