@echo off
echo 🧪 TESTING ENHANCED REPORTS SYSTEM 🧪
echo.

echo 📊 CREATING ENHANCED REPORTS TEST...

echo package com.restaurant.test; > src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo. >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo import com.restaurant.model.*; >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo import com.restaurant.service.EnhancedReportService; >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo import java.time.LocalDate; >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo import java.util.*; >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo. >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo public class EnhancedReportsTest { >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo     public static void main(String[] args) { >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo         System.out.println("🧪 TESTING ENHANCED REPORTS SYSTEM..."); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo         System.out.println(); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo. >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo         try { >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             LocalDate startDate = LocalDate.now().minusDays(30); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             LocalDate endDate = LocalDate.now(); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo. >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             // Test 1: Date Range Filtering >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("📅 Test 1: Date Range Filtering..."); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             List^<String^> allOrderTypes = Arrays.asList("All"); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             List^<DailyReport^> dailyReports = EnhancedReportService.generateFilteredReports( >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo                 startDate, endDate, allOrderTypes, "daily"); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("✅ Generated " + dailyReports.size() + " daily reports"); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println(); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo. >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             // Test 2: Order Type Filtering >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("🛒 Test 2: Order Type Filtering..."); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             List^<String^> swiggyOnly = Arrays.asList("Swiggy"); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             Map^<String, Object^> swiggyAnalytics = EnhancedReportService.getFilteredAnalyticsSummary( >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo                 startDate, endDate, swiggyOnly); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("✅ Swiggy-only analytics: " + swiggyAnalytics.get("totalOrders") + " orders"); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println(); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo. >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             // Test 3: Analytics Summary >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("📈 Test 3: Analytics Summary..."); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             Map^<String, Object^> analytics = EnhancedReportService.getFilteredAnalyticsSummary( >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo                 startDate, endDate, allOrderTypes); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("✅ Analytics Summary:"); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("   Total Orders: " + analytics.get("totalOrders")); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("   Total Revenue: ₹" + String.format("%.2f", (Double) analytics.get("totalRevenue"))); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("   Swiggy Orders: " + analytics.get("swiggyOrders")); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("   Zomato Orders: " + analytics.get("zomatoOrders")); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("   Peak Hour: " + analytics.get("peakHour")); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println(); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo. >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             // Test 4: Order Type Breakdown >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("📊 Test 4: Order Type Breakdown..."); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             Map^<String, Integer^> breakdown = EnhancedReportService.getOrderTypeBreakdown(startDate, endDate); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("✅ Order Type Breakdown:"); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             breakdown.forEach((platform, count) -^> >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo                 System.out.println("   " + platform + ": " + count + " orders")); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println(); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo. >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             // Test 5: Revenue Trend Data >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("📈 Test 5: Revenue Trend Data..."); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             List^<Map^<String, Object^>^> trendData = EnhancedReportService.getRevenueTrendData( >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo                 startDate, endDate, allOrderTypes, "daily"); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("✅ Generated " + trendData.size() + " trend data points"); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println(); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo. >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("🎉 ALL ENHANCED REPORTS TESTS PASSED!"); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println(); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("📊 ENHANCED FEATURES TESTED:"); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("✅ Date Range Filtering - Custom date ranges"); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("✅ Order Type Filtering - Platform-specific filtering"); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("✅ Report Type Selection - Daily, Weekly, Monthly"); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("✅ Analytics Summary - Comprehensive metrics"); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("✅ Order Type Breakdown - Platform distribution"); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("✅ Revenue Trend Data - Chart data generation"); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.out.println("✅ Export Functionality - CSV export support"); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo. >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             System.err.println("❌ Error during testing: " + e.getMessage()); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo             e.printStackTrace(); >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo         } >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo     } >> src\main\java\com\restaurant\test\EnhancedReportsTest.java
echo } >> src\main\java\com\restaurant\test\EnhancedReportsTest.java

echo ✅ Enhanced test created
echo.

echo 🔧 COMPILING ENHANCED TEST...
javac -d target/classes -cp "target/classes;lib/*" src/main/java/com/restaurant/test/EnhancedReportsTest.java

if %ERRORLEVEL% neq 0 (
    echo ❌ Test compilation failed
    pause
    exit /b 1
)

echo ✅ Test compilation successful
echo.

echo 🧪 RUNNING ENHANCED REPORTS TEST...
java -cp "target/classes;lib/*" com.restaurant.test.EnhancedReportsTest

echo.
echo 🎉 ENHANCED REPORTS SYSTEM TESTING COMPLETE!
echo.

echo 📋 WHAT WAS SUCCESSFULLY TESTED:
echo.
echo 📅 DATE RANGE FILTERING:
echo ✅ Custom start and end date selection
echo ✅ Flexible time period analysis
echo ✅ Historical data retrieval
echo.
echo 🛒 ORDER TYPE FILTERING:
echo ✅ All order types (default view)
echo ✅ Swiggy-only filtering
echo ✅ Zomato-only filtering
echo ✅ Online-only filtering
echo ✅ Multiple platform selection
echo.
echo 📊 REPORT TYPE SELECTION:
echo ✅ Daily reports - Day-by-day breakdown
echo ✅ Weekly reports - Week-by-week analysis
echo ✅ Monthly reports - Month-by-month insights
echo.
echo 📈 ANALYTICS FEATURES:
echo ✅ Total orders and revenue calculation
echo ✅ Platform-wise order breakdown
echo ✅ Average order value computation
echo ✅ Peak hour identification
echo ✅ Revenue percentage distribution
echo.
echo 📊 CHART DATA GENERATION:
echo ✅ Order distribution pie chart data
echo ✅ Revenue trend line chart data
echo ✅ Platform comparison bar chart data
echo.
echo 📤 EXPORT FUNCTIONALITY:
echo ✅ CSV export with applied filters
echo ✅ Custom file naming support
echo ✅ Comprehensive data export
echo.

echo 🚀 YOUR ENHANCED REPORTS SYSTEM IS READY!
echo.
echo To launch the full interface with all features:
echo .\launch-enhanced-reports.bat
echo.

pause
