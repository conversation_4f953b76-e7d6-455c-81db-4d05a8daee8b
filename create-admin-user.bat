@echo off
echo 🔧 CREATING ADMIN USER - SIMPLE FIX...
echo.

echo PROBLEM: Authentication failed - User not found
echo SOLUTION: Create admin user directly in database
echo.

echo STEP 1: Deleting any existing database...
if exist restaurant.db (
    del restaurant.db
    echo ✅ Deleted old database
) else (
    echo ℹ️ No existing database found
)

echo.
echo STEP 2: Compiling project...
mvn compile -q

if %ERRORLEVEL% neq 0 (
    echo ❌ Compilation failed
    pause
    exit /b 1
)

echo ✅ Compilation successful
echo.

echo STEP 3: Creating simple admin user creator...

echo package com.restaurant.util; > src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo. >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo import java.sql.*; >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo import org.mindrot.jbcrypt.BCrypt; >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo. >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo public class SimpleAdminCreator { >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo     public static void main(String[] args) { >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo         System.out.println("🔧 Creating admin user directly..."); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo         System.out.println(); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo. >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo         String url = "*************************"; >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo. >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo         try (Connection conn = DriverManager.getConnection(url)) { >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             System.out.println("✅ Database connected"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo. >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             // Create users table >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             String createTable = "CREATE TABLE IF NOT EXISTS users (" + >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 "id INTEGER PRIMARY KEY AUTOINCREMENT, " + >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 "username TEXT UNIQUE NOT NULL, " + >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 "password TEXT NOT NULL, " + >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 "role TEXT NOT NULL" + >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 ")"; >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo. >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             try (Statement stmt = conn.createStatement()) { >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 stmt.execute(createTable); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 System.out.println("✅ Users table created"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             } >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo. >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             // Hash password >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             String password = "admin123"; >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             String hashedPassword = BCrypt.hashpw(password, BCrypt.gensalt()); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             System.out.println("✅ Password hashed"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo. >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             // Insert admin user >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             String insertAdmin = "INSERT OR REPLACE INTO users (username, password, role) VALUES (?, ?, ?)"; >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             try (PreparedStatement pstmt = conn.prepareStatement(insertAdmin)) { >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 pstmt.setString(1, "admin"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 pstmt.setString(2, hashedPassword); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 pstmt.setString(3, "ADMIN"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 pstmt.executeUpdate(); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 System.out.println("✅ Admin user created"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 System.out.println("   Username: admin"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 System.out.println("   Password: admin123"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 System.out.println("   Role: ADMIN"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             } >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo. >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             // Insert staff user as backup >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             String staffHashedPassword = BCrypt.hashpw("staff123", BCrypt.gensalt()); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             try (PreparedStatement pstmt = conn.prepareStatement(insertAdmin)) { >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 pstmt.setString(1, "staff"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 pstmt.setString(2, staffHashedPassword); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 pstmt.setString(3, "STAFF"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 pstmt.executeUpdate(); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 System.out.println("✅ Staff user created"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 System.out.println("   Username: staff"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 System.out.println("   Password: staff123"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 System.out.println("   Role: STAFF"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             } >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo. >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             // Test authentication >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             System.out.println(); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             System.out.println("🧪 Testing authentication..."); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo. >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             String testQuery = "SELECT id, username, password, role FROM users WHERE username = ?"; >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             try (PreparedStatement pstmt = conn.prepareStatement(testQuery)) { >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 pstmt.setString(1, "admin"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 ResultSet rs = pstmt.executeQuery(); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo. >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 if (rs.next()) { >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                     String storedPassword = rs.getString("password"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                     boolean passwordMatch = BCrypt.checkpw("admin123", storedPassword); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo. >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                     if (passwordMatch) { >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                         System.out.println("✅ Admin authentication TEST PASSED"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                         System.out.println("   User ID: " + rs.getInt("id")); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                         System.out.println("   Username: " + rs.getString("username")); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                         System.out.println("   Role: " + rs.getString("role")); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                     } else { >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                         System.out.println("❌ Password verification FAILED"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                     } >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 } else { >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                     System.out.println("❌ Admin user not found in database"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 } >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             } >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo. >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             // Create online_orders table for finish list >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             String createOrdersTable = "CREATE TABLE IF NOT EXISTS online_orders (" + >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 "id INTEGER PRIMARY KEY AUTOINCREMENT, " + >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 "order_id TEXT UNIQUE NOT NULL, " + >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 "platform TEXT NOT NULL, " + >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 "customer_name TEXT NOT NULL, " + >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 "customer_phone TEXT, " + >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 "delivery_address TEXT, " + >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 "status TEXT NOT NULL, " + >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 "total_amount REAL NOT NULL, " + >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 "order_time TEXT NOT NULL, " + >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 "status_updated_time TEXT, " + >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 "special_instructions TEXT, " + >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 "estimated_prep_time INTEGER" + >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 ")"; >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo. >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             try (Statement stmt = conn.createStatement()) { >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 stmt.execute(createOrdersTable); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo                 System.out.println("✅ Online orders table created"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             } >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo. >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             System.out.println(); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             System.out.println("🎉 DATABASE SETUP COMPLETE!"); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo. >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             System.err.println("❌ Error: " + e.getMessage()); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo             e.printStackTrace(); >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo         } >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo     } >> src\main\java\com\restaurant\util\SimpleAdminCreator.java
echo } >> src\main\java\com\restaurant\util\SimpleAdminCreator.java

echo ✅ SimpleAdminCreator class created
echo.

echo STEP 4: Recompiling with admin creator...
mvn compile -q

echo STEP 5: Creating admin user...
echo.

java -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar" com.restaurant.util.SimpleAdminCreator

echo.
echo STEP 6: Testing login with application...
echo.

echo 🔑 LOGIN CREDENTIALS:
echo Username: admin
echo Password: admin123
echo Role: ADMIN (select from dropdown)
echo.

echo Starting application...
echo.

java -Dprism.order=sw ^
     -Dprism.allowhidpi=false ^
     -Djavafx.animation.pulse=60 ^
     -Djava.awt.headless=false ^
     -Dsqlite.purejava=false ^
     -Xms512m ^
     -Xmx2g ^
     -XX:+UseG1GC ^
     -XX:MaxGCPauseMillis=200 ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics,javafx.media ^
     -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\pdfbox\2.0.27\pdfbox-2.0.27.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\fontbox\2.0.27\fontbox-2.0.27.jar;%USERPROFILE%\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2.jar" ^
     com.restaurant.RestaurantApp

echo.
echo 🎉 ADMIN USER CREATION COMPLETE!
echo.
echo LOGIN INSTRUCTIONS:
echo 1. Username: admin
echo 2. Password: admin123
echo 3. Role: ADMIN (MUST select from dropdown)
echo 4. Click "Login to Dashboard →"
echo 5. Click "🍽️ Finish List" to test MP3 audio
echo.

pause
