@echo off
echo CREATING SIMPLIFIED MAIN APPLICATION...
echo.

echo This will create a simplified version of RestaurantApp
echo that removes complex components one by one to find the issue.
echo.

echo Step 1: Backup original RestaurantApp...
copy "src\main\java\com\restaurant\RestaurantApp.java" "src\main\java\com\restaurant\RestaurantApp-original-backup.java"

echo Step 2: Creating simplified RestaurantApp...

echo package com.restaurant; > "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo. >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo import javafx.application.Application; >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo import javafx.fxml.FXMLLoader; >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo import javafx.scene.Scene; >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo import javafx.scene.control.Alert; >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo import javafx.scene.control.Button; >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo import javafx.scene.control.Label; >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo import javafx.scene.layout.VBox; >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo import javafx.stage.Stage; >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo. >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo public class RestaurantApp extends Application { >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo. >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo     @Override >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo     public void start(Stage primaryStage^) { >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo         System.out.println("RestaurantApp: Starting simplified version..."); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo. >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo         try { >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo             // Simple UI without FXML >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo             Label titleLabel = new Label("Restaurant Management System"); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo             titleLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold;"); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo. >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo             Button loginButton = new Button("Login ^(Simplified^)"); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo             loginButton.setOnAction(e -^> { >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo                 Alert alert = new Alert(Alert.AlertType.INFORMATION^); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo                 alert.setTitle("Success"); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo                 alert.setHeaderText("Login Successful"); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo                 alert.setContentText("Simplified app is working!"); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo                 alert.showAndWait(^); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo             }^); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo. >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo             VBox root = new VBox(20^); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo             root.setStyle("-fx-padding: 50px; -fx-alignment: center;"); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo             root.getChildren(^).addAll(titleLabel, loginButton^); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo. >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo             Scene scene = new Scene(root, 600, 400^); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo             primaryStage.setTitle("Restaurant Management - Simplified"); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo             primaryStage.setScene(scene^); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo             primaryStage.show(^); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo. >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo             System.out.println("RestaurantApp: Simplified version started successfully!"); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo. >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo         } catch (Exception e^) { >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo             System.err.println("ERROR in simplified RestaurantApp: " + e.getMessage(^)^); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo             e.printStackTrace(^); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo         } >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo     } >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo. >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo     public static void main(String[] args^) { >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo         System.out.println("RestaurantApp: Starting simplified main..."); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo         launch(args^); >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo     } >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"
echo } >> "src\main\java\com\restaurant\RestaurantApp-simplified.java"

echo Step 3: Replace main app with simplified version...
copy "src\main\java\com\restaurant\RestaurantApp-simplified.java" "src\main\java\com\restaurant\RestaurantApp.java"

echo Step 4: Compiling simplified app...
call mvn clean compile -q

if %ERRORLEVEL% neq 0 (
    echo ❌ COMPILATION FAILED
    mvn clean compile
    pause
    exit /b 1
)

echo ✅ Simplified app compiled successfully!
echo.

echo SIMPLIFIED APP CREATED:
echo ✅ Removed FXML loading
echo ✅ Removed CSS loading  
echo ✅ Removed database initialization
echo ✅ Removed notification system
echo ✅ Removed complex UI components
echo ✅ Simple JavaFX UI only
echo.

echo To test simplified app: run test-simplified-app.bat
echo To restore original: copy "src\main\java\com\restaurant\RestaurantApp-original-backup.java" "src\main\java\com\restaurant\RestaurantApp.java"
echo.
pause
