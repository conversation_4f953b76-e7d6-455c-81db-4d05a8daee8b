<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" stylesheets="@../css/application.css">
   <top>
      <VBox spacing="10.0" styleClass="header-section">
         <padding>
            <Insets bottom="10.0" left="20.0" right="20.0" top="20.0" />
         </padding>
         
         <!-- Header -->
         <HBox alignment="CENTER">
            <Label styleClass="module-title" text="🚀 Keyboard Shortcuts – Fast Billing with Just Your Keyboard">
               <font>
                  <Font name="System Bold" size="20.0" />
               </font>
            </Label>
         </HBox>
         
         <Label styleClass="subtitle" text="Boost your speed and efficiency by using the following keyboard shortcuts across the POS system:">
            <font>
               <Font size="14.0" />
            </font>
         </Label>
      </VBox>
   </top>
   
   <center>
      <ScrollPane fitToWidth="true" styleClass="shortcuts-scroll">
         <VBox spacing="20.0" styleClass="shortcuts-container">
            <padding>
               <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
            </padding>
            
            <!-- Billing & Order Management Shortcuts -->
            <VBox spacing="10.0" styleClass="shortcut-section">
               <Label styleClass="section-header" text="📋 BILLING &amp; ORDER MANAGEMENT">
                  <font>
                     <Font name="System Bold" size="16.0" />
                  </font>
               </Label>
               
               <GridPane hgap="20.0" vgap="10.0">
                  <columnConstraints>
                     <ColumnConstraints minWidth="120.0" />
                     <ColumnConstraints hgrow="ALWAYS" />
                  </columnConstraints>
                  
                  <Label text="Ctrl + N" styleClass="shortcut-key" GridPane.rowIndex="0" GridPane.columnIndex="0" />
                  <Label text="New Order" styleClass="shortcut-desc" GridPane.rowIndex="0" GridPane.columnIndex="1" />
                  
                  <Label text="Ctrl + H" styleClass="shortcut-key" GridPane.rowIndex="1" GridPane.columnIndex="0" />
                  <Label text="Hold KOT" styleClass="shortcut-desc" GridPane.rowIndex="1" GridPane.columnIndex="1" />
                  
                  <Label text="Ctrl + S" styleClass="shortcut-key" GridPane.rowIndex="2" GridPane.columnIndex="0" />
                  <Label text="Settle Bill" styleClass="shortcut-desc" GridPane.rowIndex="2" GridPane.columnIndex="1" />
                  
                  <Label text="Ctrl + P" styleClass="shortcut-key" GridPane.rowIndex="3" GridPane.columnIndex="0" />
                  <Label text="Print KOT" styleClass="shortcut-desc" GridPane.rowIndex="3" GridPane.columnIndex="1" />
                  
                  <Label text="Del / Backspace" styleClass="shortcut-key" GridPane.rowIndex="4" GridPane.columnIndex="0" />
                  <Label text="Delete Item from Order" styleClass="shortcut-desc" GridPane.rowIndex="4" GridPane.columnIndex="1" />
                  
                  <Label text="Ctrl + D" styleClass="shortcut-key" GridPane.rowIndex="5" GridPane.columnIndex="0" />
                  <Label text="Apply Discount" styleClass="shortcut-desc" GridPane.rowIndex="5" GridPane.columnIndex="1" />
                  
                  <Label text="Ctrl + B" styleClass="shortcut-key" GridPane.rowIndex="6" GridPane.columnIndex="0" />
                  <Label text="Open Previous Bills" styleClass="shortcut-desc" GridPane.rowIndex="6" GridPane.columnIndex="1" />
                  
                  <Label text="Ctrl + R" styleClass="shortcut-key" GridPane.rowIndex="7" GridPane.columnIndex="0" />
                  <Label text="Reprint Last Bill" styleClass="shortcut-desc" GridPane.rowIndex="7" GridPane.columnIndex="1" />
               </GridPane>
            </VBox>
            
            <!-- Navigation & Management Shortcuts -->
            <VBox spacing="10.0" styleClass="shortcut-section">
               <Label styleClass="section-header" text="🎛️ NAVIGATION &amp; MANAGEMENT">
                  <font>
                     <Font name="System Bold" size="16.0" />
                  </font>
               </Label>
               
               <GridPane hgap="20.0" vgap="10.0">
                  <columnConstraints>
                     <ColumnConstraints minWidth="120.0" />
                     <ColumnConstraints hgrow="ALWAYS" />
                  </columnConstraints>
                  
                  <Label text="Ctrl + A" styleClass="shortcut-key" GridPane.rowIndex="0" GridPane.columnIndex="0" />
                  <Label text="Open Settings/Admin" styleClass="shortcut-desc" GridPane.rowIndex="0" GridPane.columnIndex="1" />
                  
                  <Label text="Ctrl + L" styleClass="shortcut-key" GridPane.rowIndex="1" GridPane.columnIndex="0" />
                  <Label text="Log Out" styleClass="shortcut-desc" GridPane.rowIndex="1" GridPane.columnIndex="1" />
                  
                  <Label text="Ctrl + F" styleClass="shortcut-key" GridPane.rowIndex="2" GridPane.columnIndex="0" />
                  <Label text="Search Menu Item" styleClass="shortcut-desc" GridPane.rowIndex="2" GridPane.columnIndex="1" />
                  
                  <Label text="Ctrl + T" styleClass="shortcut-key" GridPane.rowIndex="3" GridPane.columnIndex="0" />
                  <Label text="Switch Table" styleClass="shortcut-desc" GridPane.rowIndex="3" GridPane.columnIndex="1" />
                  
                  <Label text="Ctrl + M" styleClass="shortcut-key" GridPane.rowIndex="4" GridPane.columnIndex="0" />
                  <Label text="Menu Management" styleClass="shortcut-desc" GridPane.rowIndex="4" GridPane.columnIndex="1" />
                  
                  <Label text="Ctrl + U" styleClass="shortcut-key" GridPane.rowIndex="5" GridPane.columnIndex="0" />
                  <Label text="User Management" styleClass="shortcut-desc" GridPane.rowIndex="5" GridPane.columnIndex="1" />
                  
                  <Label text="Ctrl + I" styleClass="shortcut-key" GridPane.rowIndex="6" GridPane.columnIndex="0" />
                  <Label text="Inventory" styleClass="shortcut-desc" GridPane.rowIndex="6" GridPane.columnIndex="1" />
               </GridPane>
            </VBox>
            
            <!-- Quick Payments Shortcuts -->
            <VBox spacing="10.0" styleClass="shortcut-section">
               <Label styleClass="section-header" text="💳 QUICK PAYMENTS">
                  <font>
                     <Font name="System Bold" size="16.0" />
                  </font>
               </Label>
               
               <GridPane hgap="20.0" vgap="10.0">
                  <columnConstraints>
                     <ColumnConstraints minWidth="120.0" />
                     <ColumnConstraints hgrow="ALWAYS" />
                  </columnConstraints>
                  
                  <Label text="F2" styleClass="shortcut-key" GridPane.rowIndex="0" GridPane.columnIndex="0" />
                  <Label text="Quick Cash Payment" styleClass="shortcut-desc" GridPane.rowIndex="0" GridPane.columnIndex="1" />
                  
                  <Label text="F3" styleClass="shortcut-key" GridPane.rowIndex="1" GridPane.columnIndex="0" />
                  <Label text="Quick Card Payment" styleClass="shortcut-desc" GridPane.rowIndex="1" GridPane.columnIndex="1" />
                  
                  <Label text="F4" styleClass="shortcut-key" GridPane.rowIndex="2" GridPane.columnIndex="0" />
                  <Label text="Quick UPI Payment" styleClass="shortcut-desc" GridPane.rowIndex="2" GridPane.columnIndex="1" />
               </GridPane>
            </VBox>
            
            <!-- System Shortcuts -->
            <VBox spacing="10.0" styleClass="shortcut-section">
               <Label styleClass="section-header" text="🔧 SYSTEM">
                  <font>
                     <Font name="System Bold" size="16.0" />
                  </font>
               </Label>
               
               <GridPane hgap="20.0" vgap="10.0">
                  <columnConstraints>
                     <ColumnConstraints minWidth="120.0" />
                     <ColumnConstraints hgrow="ALWAYS" />
                  </columnConstraints>
                  
                  <Label text="F1" styleClass="shortcut-key" GridPane.rowIndex="0" GridPane.columnIndex="0" />
                  <Label text="Show This Help" styleClass="shortcut-desc" GridPane.rowIndex="0" GridPane.columnIndex="1" />
                  
                  <Label text="F5" styleClass="shortcut-key" GridPane.rowIndex="1" GridPane.columnIndex="0" />
                  <Label text="Refresh/Reload" styleClass="shortcut-desc" GridPane.rowIndex="1" GridPane.columnIndex="1" />
                  
                  <Label text="F12" styleClass="shortcut-key" GridPane.rowIndex="2" GridPane.columnIndex="0" />
                  <Label text="Emergency/Manager Call" styleClass="shortcut-desc" GridPane.rowIndex="2" GridPane.columnIndex="1" />
                  
                  <Label text="Esc" styleClass="shortcut-key" GridPane.rowIndex="3" GridPane.columnIndex="0" />
                  <Label text="Cancel/Back" styleClass="shortcut-desc" GridPane.rowIndex="3" GridPane.columnIndex="1" />
                  
                  <Label text="Enter" styleClass="shortcut-key" GridPane.rowIndex="4" GridPane.columnIndex="0" />
                  <Label text="Confirm/Select" styleClass="shortcut-desc" GridPane.rowIndex="4" GridPane.columnIndex="1" />
               </GridPane>
            </VBox>
            
            <!-- Tips Section -->
            <VBox spacing="10.0" styleClass="tips-section">
               <Label styleClass="section-header" text="💡 TIPS">
                  <font>
                     <Font name="System Bold" size="16.0" />
                  </font>
               </Label>
               
               <VBox spacing="5.0" styleClass="tips-list">
                  <Label text="• Type item name directly to search" styleClass="tip-item" />
                  <Label text="• Type number before item (e.g., 2Burger) to add quantity" styleClass="tip-item" />
                  <Label text="• Use Tab to navigate between fields" styleClass="tip-item" />
                  <Label text="• Use Arrow keys to navigate lists" styleClass="tip-item" />
                  <Label text="• Press F1 anytime to see this help screen" styleClass="tip-item" />
               </VBox>
            </VBox>
         </VBox>
      </ScrollPane>
   </center>
   
   <bottom>
      <HBox alignment="CENTER" spacing="20.0" styleClass="footer-section">
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
         <Button text="Close" styleClass="close-button" />
         <Button text="Print Shortcuts" styleClass="print-button" />
      </HBox>
   </bottom>
</BorderPane>
