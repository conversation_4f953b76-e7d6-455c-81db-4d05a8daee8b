package com.restaurant.controller;

import javafx.application.Platform;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Alert;
import javafx.scene.input.KeyCode;
import javafx.stage.Stage;

/**
 * Base controller class that provides common functionality for all controllers
 * including ESC key handling and navigation methods.
 */
public abstract class BaseController {
    
    /**
     * Setup ESC key handler for the given node
     * @param node Any JavaFX node that has access to the scene
     */
    protected void setupEscapeKeyHandler(Node node) {
        Platform.runLater(() -> {
            if (node != null && node.getScene() != null) {
                node.getScene().setOnKeyPressed(event -> {
                    if (event.getCode() == KeyCode.ESCAPE) {
                        goBack();
                    }
                });
            }
        });
    }
    
    /**
     * Navigate back to the dashboard
     */
    protected void goBack() {
        try {
            // Load the dashboard FXML
            Parent dashboardView = FXMLLoader.load(getClass().getResource("/fxml/Dashboard.fxml"));
            
            // Get the current stage and set the new scene
            Stage stage = getCurrentStage();
            if (stage != null) {
                stage.getScene().setRoot(dashboardView);
            }
        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Could not return to dashboard");
        }
    }
    
    /**
     * Get the current stage from any node in the scene
     * This method should be overridden by subclasses to provide a specific node
     */
    protected abstract Stage getCurrentStage();
    
    /**
     * Show an information alert dialog
     * @param title The title of the alert
     * @param message The message to display
     */
    protected void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    /**
     * Show a confirmation alert dialog
     * @param title The title of the alert
     * @param message The message to display
     * @return true if user clicked OK, false otherwise
     */
    protected boolean showConfirmation(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        return alert.showAndWait().orElse(null) == javafx.scene.control.ButtonType.OK;
    }
    
    /**
     * Show an error alert dialog
     * @param title The title of the alert
     * @param message The error message to display
     */
    protected void showError(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
