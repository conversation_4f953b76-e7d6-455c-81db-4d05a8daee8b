 package com.restaurant.model;

import java.sql.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import org.mindrot.jbcrypt.BCrypt;

public class DatabaseManager {
    private static final String DB_URL = "*****************************************************************************************************************";
    private static volatile boolean isInitialized = false;
    private static final ReentrantLock initLock = new ReentrantLock();

    // Thread-local connections to ensure thread safety
    private static final ThreadLocal<Connection> threadLocalConnection = new ThreadLocal<>();

    // Keep track of all connections for cleanup
    private static final ConcurrentHashMap<Thread, Connection> activeConnections = new ConcurrentHashMap<>();

    /**
     * Get a thread-safe database connection
     * Each thread gets its own connection to prevent SQLite crashes
     */
    public static Connection getConnection() throws SQLException {
        Connection conn = threadLocalConnection.get();

        // Check if connection exists and is valid
        if (conn == null || conn.isClosed()) {
            conn = createNewConnection();
            threadLocalConnection.set(conn);
            activeConnections.put(Thread.currentThread(), conn);
        }

        // Initialize database if needed (only once)
        if (!isInitialized) {
            initializeDatabase();
        }

        return conn;
    }

    /**
     * Create a new database connection with optimized settings
     */
    private static Connection createNewConnection() throws SQLException {
        Connection conn = DriverManager.getConnection(DB_URL);

        // Configure connection for better performance and reliability
        try (Statement stmt = conn.createStatement()) {
            // Enable WAL mode for better concurrency
            stmt.execute("PRAGMA journal_mode=WAL");
            // Set synchronous mode for better performance
            stmt.execute("PRAGMA synchronous=NORMAL");
            // Enable foreign key constraints
            stmt.execute("PRAGMA foreign_keys=ON");
            // Set busy timeout to handle concurrent access
            stmt.execute("PRAGMA busy_timeout=30000");
            // Optimize cache size
            stmt.execute("PRAGMA cache_size=10000");
        }

        return conn;
    }

    /**
     * Initialize database tables (thread-safe)
     */
    private static void initializeDatabase() throws SQLException {
        if (isInitialized) {
            return;
        }

        initLock.lock();
        try {
            if (!isInitialized) {
                // Use a dedicated connection for initialization
                try (Connection initConn = DriverManager.getConnection(DB_URL)) {
                    initDatabase(initConn);
                    isInitialized = true;
                }
            }
        } finally {
            initLock.unlock();
        }
    }

    private static void initDatabase(Connection connection) {
        try (Statement stmt = connection.createStatement()) {
            // Create users table
            stmt.execute("CREATE TABLE IF NOT EXISTS users (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "username TEXT UNIQUE NOT NULL," +
                    "password_hash TEXT NOT NULL," +
                    "role TEXT NOT NULL)");
            
            // Create menu_categories table
            stmt.execute("CREATE TABLE IF NOT EXISTS menu_categories (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "name TEXT UNIQUE NOT NULL)");
            
            // Create menu_items table
            stmt.execute("CREATE TABLE IF NOT EXISTS menu_items (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "name TEXT NOT NULL," +
                    "price REAL NOT NULL," +
                    "category_id INTEGER," +
                    "FOREIGN KEY (category_id) REFERENCES menu_categories(id))");
            
            // Create orders table
            stmt.execute("CREATE TABLE IF NOT EXISTS orders (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "table_number INTEGER," +
                    "is_takeaway BOOLEAN," +
                    "status TEXT," +
                    "timestamp DATETIME DEFAULT CURRENT_TIMESTAMP," +
                    "total_amount REAL)");
            
            // Create order_items table
            stmt.execute("CREATE TABLE IF NOT EXISTS order_items (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "order_id INTEGER," +
                    "item_id INTEGER," +
                    "quantity INTEGER," +
                    "price REAL," +
                    "FOREIGN KEY (order_id) REFERENCES orders(id)," +
                    "FOREIGN KEY (item_id) REFERENCES menu_items(id))");

            // Create business_config table
            stmt.execute("CREATE TABLE IF NOT EXISTS business_config (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "config_key TEXT NOT NULL UNIQUE," +
                    "config_value TEXT NOT NULL," +
                    "description TEXT," +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                    "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP)");

            // Create suppliers table
            stmt.execute("CREATE TABLE IF NOT EXISTS suppliers (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "name TEXT NOT NULL," +
                    "contact_person TEXT," +
                    "phone TEXT," +
                    "email TEXT," +
                    "address TEXT," +
                    "category TEXT," +
                    "status TEXT DEFAULT 'Active'," +
                    "notes TEXT," +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                    "last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP)");

            // Create purchase_orders table
            stmt.execute("CREATE TABLE IF NOT EXISTS purchase_orders (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "request_number TEXT NOT NULL UNIQUE," +
                    "to_location TEXT NOT NULL," +
                    "item TEXT NOT NULL," +
                    "quantity TEXT NOT NULL," +
                    "status TEXT DEFAULT 'Saved'," +
                    "start_date DATE," +
                    "end_date DATE," +
                    "created_by TEXT," +
                    "notes TEXT," +
                    "unit_price REAL DEFAULT 0," +
                    "total_amount REAL DEFAULT 0," +
                    "supplier_id INTEGER," +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                    "last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                    "FOREIGN KEY (supplier_id) REFERENCES suppliers(id))");

            // Create internal_transfers table
            stmt.execute("CREATE TABLE IF NOT EXISTS internal_transfers (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "request_number TEXT NOT NULL UNIQUE," +
                    "from_location TEXT NOT NULL," +
                    "to_location TEXT NOT NULL," +
                    "item TEXT NOT NULL," +
                    "quantity TEXT NOT NULL," +
                    "status TEXT DEFAULT 'Saved'," +
                    "start_date DATE," +
                    "end_date DATE," +
                    "created_by TEXT," +
                    "approved_by TEXT," +
                    "notes TEXT," +
                    "transfer_reason TEXT," +
                    "transfer_date TIMESTAMP," +
                    "delivery_date TIMESTAMP," +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                    "last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP)");

            // Create inventory_items table
            stmt.execute("CREATE TABLE IF NOT EXISTS inventory_items (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "name TEXT NOT NULL," +
                    "quantity REAL NOT NULL DEFAULT 0," +
                    "unit TEXT NOT NULL," +
                    "category TEXT," +
                    "min_threshold REAL DEFAULT 0," +
                    "supplier_id INTEGER," +
                    "status TEXT DEFAULT 'In Stock'," +
                    "last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                    "FOREIGN KEY (supplier_id) REFERENCES suppliers(id))");

            // Create employees table
            stmt.execute("CREATE TABLE IF NOT EXISTS employees (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "name TEXT NOT NULL," +
                    "employee_id TEXT UNIQUE NOT NULL," +
                    "department TEXT," +
                    "position TEXT," +
                    "phone TEXT," +
                    "email TEXT," +
                    "address TEXT," +
                    "date_of_joining DATE," +
                    "status TEXT DEFAULT 'Active'," +
                    "salary REAL DEFAULT 0," +
                    "shift_timing TEXT," +
                    "notes TEXT," +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                    "last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP)");

            // Create attendance table
            stmt.execute("CREATE TABLE IF NOT EXISTS attendance (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "employee_id INTEGER NOT NULL," +
                    "attendance_date DATE NOT NULL," +
                    "check_in_time TIME," +
                    "check_out_time TIME," +
                    "status TEXT DEFAULT 'Present'," +
                    "hours_worked REAL DEFAULT 0," +
                    "notes TEXT," +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                    "last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                    "FOREIGN KEY (employee_id) REFERENCES employees(id)," +
                    "UNIQUE(employee_id, attendance_date))");

            // Create settings table
            stmt.execute("CREATE TABLE IF NOT EXISTS settings (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "setting_key TEXT NOT NULL UNIQUE," +
                    "setting_value TEXT," +
                    "last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP)");

            // Create activities table for dashboard
            stmt.execute("CREATE TABLE IF NOT EXISTS activities (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "activity_type TEXT NOT NULL," +
                    "description TEXT NOT NULL," +
                    "user_id INTEGER," +
                    "timestamp DATETIME DEFAULT CURRENT_TIMESTAMP," +
                    "details TEXT," +
                    "FOREIGN KEY (user_id) REFERENCES users(id))");

            // Insert default admin user if not exists
            try (PreparedStatement ps = connection.prepareStatement(
                    "INSERT OR IGNORE INTO users (username, password_hash, role) VALUES (?, ?, ?)")) {
                ps.setString(1, "admin");
                ps.setString(2, BCrypt.hashpw("admin123", BCrypt.gensalt())); // Using BCrypt for password hashing
                ps.setString(3, "ADMIN");
                ps.executeUpdate();

                // Insert default staff user if not exists
                ps.setString(1, "staff");
                ps.setString(2, BCrypt.hashpw("staff123", BCrypt.gensalt()));
                ps.setString(3, "STAFF");
                ps.executeUpdate();
            }

            // Insert default menu categories
            try (PreparedStatement psCategories = connection.prepareStatement("INSERT OR IGNORE INTO menu_categories (name) VALUES (?)")) {
                String[] categories = {"Appetizers", "Main Course", "Beverages", "Desserts"};
                for (String category : categories) {
                    psCategories.setString(1, category);
                    psCategories.executeUpdate();
                }
            }

            // Insert sample menu items
            try (PreparedStatement psItems = connection.prepareStatement(
                    "INSERT OR IGNORE INTO menu_items (name, price, category_id) VALUES (?, ?, ?)")) {

                // Appetizers (category_id = 1)
                psItems.setString(1, "Spring Rolls"); psItems.setDouble(2, 8.99); psItems.setInt(3, 1); psItems.executeUpdate();
                psItems.setString(1, "Chicken Wings"); psItems.setDouble(2, 12.99); psItems.setInt(3, 1); psItems.executeUpdate();
                psItems.setString(1, "Garlic Bread"); psItems.setDouble(2, 6.99); psItems.setInt(3, 1); psItems.executeUpdate();

                // Main Course (category_id = 2)
                psItems.setString(1, "Grilled Chicken"); psItems.setDouble(2, 18.99); psItems.setInt(3, 2); psItems.executeUpdate();
                psItems.setString(1, "Beef Steak"); psItems.setDouble(2, 24.99); psItems.setInt(3, 2); psItems.executeUpdate();
                psItems.setString(1, "Vegetable Pasta"); psItems.setDouble(2, 14.99); psItems.setInt(3, 2); psItems.executeUpdate();
                psItems.setString(1, "Fish & Chips"); psItems.setDouble(2, 16.99); psItems.setInt(3, 2); psItems.executeUpdate();

                // Beverages (category_id = 3)
                psItems.setString(1, "Coca Cola"); psItems.setDouble(2, 2.99); psItems.setInt(3, 3); psItems.executeUpdate();
                psItems.setString(1, "Fresh Orange Juice"); psItems.setDouble(2, 4.99); psItems.setInt(3, 3); psItems.executeUpdate();
                psItems.setString(1, "Coffee"); psItems.setDouble(2, 3.99); psItems.setInt(3, 3); psItems.executeUpdate();
                psItems.setString(1, "Tea"); psItems.setDouble(2, 2.99); psItems.setInt(3, 3); psItems.executeUpdate();

                // Desserts (category_id = 4)
                psItems.setString(1, "Chocolate Cake"); psItems.setDouble(2, 7.99); psItems.setInt(3, 4); psItems.executeUpdate();
                psItems.setString(1, "Ice Cream"); psItems.setDouble(2, 5.99); psItems.setInt(3, 4); psItems.executeUpdate();
            }

            // Initialize business configuration (after database is set up)
            initializeBusinessConfig();

            // Initialize default settings
            SettingsDAO.initializeDefaultSettings();

            // Insert default suppliers
            insertDefaultSuppliers();

            // Insert default employees
            insertDefaultEmployees();

            // Insert default inventory items
            insertDefaultInventoryItems();

            // Initialize activities table and insert sample data
            ActivityDAO.initializeActivitiesTable();
            ActivityDAO.insertSampleActivities();

        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    /**
     * Initialize business configuration without circular dependency
     */
    private static void initializeBusinessConfig() {
        try (Connection conn = getConnection();
             PreparedStatement ps = conn.prepareStatement(
                "INSERT OR IGNORE INTO business_config (config_key, config_value, created_at, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)")) {

            // Day end time
            ps.setString(1, "day_end_time");
            ps.setString(2, "02:00");
            ps.executeUpdate();

            // Other default configs
            ps.setString(1, "restaurant_name");
            ps.setString(2, "WOK KA TADKA");
            ps.executeUpdate();

            ps.setString(1, "currency");
            ps.setString(2, "₹");
            ps.executeUpdate();

            ps.setString(1, "tax_rate");
            ps.setString(2, "18.0");
            ps.executeUpdate();

            ps.setString(1, "service_charge_rate");
            ps.setString(2, "10.0");
            ps.executeUpdate();

        } catch (SQLException e) {
            System.err.println("Error initializing business config: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Force recreate default users - useful for debugging login issues
     */
    public static void recreateDefaultUsers() {
        try (Connection conn = getConnection()) {
            // Delete existing users
            PreparedStatement deletePs = conn.prepareStatement("DELETE FROM users WHERE username IN ('admin', 'staff')");
            deletePs.executeUpdate();
            System.out.println("Deleted existing default users");

            // Insert fresh users
            PreparedStatement ps = conn.prepareStatement(
                    "INSERT INTO users (username, password_hash, role) VALUES (?, ?, ?)");

            // Admin user
            ps.setString(1, "admin");
            ps.setString(2, BCrypt.hashpw("admin123", BCrypt.gensalt()));
            ps.setString(3, "ADMIN");
            ps.executeUpdate();
            System.out.println("Created admin user");

            // Staff user
            ps.setString(1, "staff");
            ps.setString(2, BCrypt.hashpw("staff123", BCrypt.gensalt()));
            ps.setString(3, "STAFF");
            ps.executeUpdate();
            System.out.println("Created staff user");

        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private static void insertDefaultSuppliers() {
        try (Connection conn = getConnection();
             PreparedStatement ps = conn.prepareStatement(
                "INSERT OR IGNORE INTO suppliers (name, contact_person, phone, email, category, status) VALUES (?, ?, ?, ?, ?, ?)")) {

            // Default supplier
            ps.setString(1, "Local Market");
            ps.setString(2, "Market Manager");
            ps.setString(3, "+91 98765 00001");
            ps.setString(4, "<EMAIL>");
            ps.setString(5, "General");
            ps.setString(6, "Active");
            ps.executeUpdate();

            // Fresh Vegetables Co
            ps.setString(1, "Fresh Vegetables Co");
            ps.setString(2, "Raj Kumar");
            ps.setString(3, "+91 98765 00002");
            ps.setString(4, "<EMAIL>");
            ps.setString(5, "Vegetables");
            ps.setString(6, "Active");
            ps.executeUpdate();

            // Premium Meats
            ps.setString(1, "Premium Meats");
            ps.setString(2, "Suresh Singh");
            ps.setString(3, "+91 98765 00003");
            ps.setString(4, "<EMAIL>");
            ps.setString(5, "Meat");
            ps.setString(6, "Active");
            ps.executeUpdate();

            // Local Dairy Farm
            ps.setString(1, "Local Dairy Farm");
            ps.setString(2, "Amit Sharma");
            ps.setString(3, "+91 98765 00004");
            ps.setString(4, "<EMAIL>");
            ps.setString(5, "Dairy");
            ps.setString(6, "Active");
            ps.executeUpdate();

        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private static void insertDefaultEmployees() {
        try (Connection conn = getConnection();
             PreparedStatement ps = conn.prepareStatement(
                "INSERT OR IGNORE INTO employees (name, employee_id, department, position, phone, email, salary, shift_timing, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)")) {

            // Kitchen staff
            ps.setString(1, "John Doe");
            ps.setString(2, "EMP001");
            ps.setString(3, "Kitchen");
            ps.setString(4, "Head Chef");
            ps.setString(5, "+91 98765 11001");
            ps.setString(6, "<EMAIL>");
            ps.setDouble(7, 35000.0);
            ps.setString(8, "Full Day");
            ps.setString(9, "Active");
            ps.executeUpdate();

            // Service staff
            ps.setString(1, "Jane Smith");
            ps.setString(2, "EMP002");
            ps.setString(3, "Service");
            ps.setString(4, "Waiter");
            ps.setString(5, "+91 98765 11002");
            ps.setString(6, "<EMAIL>");
            ps.setDouble(7, 25000.0);
            ps.setString(8, "Evening");
            ps.setString(9, "Active");
            ps.executeUpdate();

            // Kitchen assistant
            ps.setString(1, "Mike Johnson");
            ps.setString(2, "EMP003");
            ps.setString(3, "Kitchen");
            ps.setString(4, "Cook");
            ps.setString(5, "+91 98765 11003");
            ps.setString(6, "<EMAIL>");
            ps.setDouble(7, 28000.0);
            ps.setString(8, "Morning");
            ps.setString(9, "Active");
            ps.executeUpdate();

            // Management
            ps.setString(1, "Sarah Wilson");
            ps.setString(2, "EMP004");
            ps.setString(3, "Management");
            ps.setString(4, "Manager");
            ps.setString(5, "+91 98765 11004");
            ps.setString(6, "<EMAIL>");
            ps.setDouble(7, 45000.0);
            ps.setString(8, "Full Day");
            ps.setString(9, "Active");
            ps.executeUpdate();

        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private static void insertDefaultInventoryItems() {
        try (Connection conn = getConnection();
             PreparedStatement ps = conn.prepareStatement(
                "INSERT OR IGNORE INTO inventory_items (name, quantity, unit, category, min_threshold, supplier_id, status) VALUES (?, ?, ?, ?, ?, ?, ?)")) {

            // Vegetables
            ps.setString(1, "Tomatoes");
            ps.setDouble(2, 15.0);
            ps.setString(3, "kg");
            ps.setString(4, "Vegetables");
            ps.setDouble(5, 3.0);
            ps.setInt(6, 2); // Fresh Vegetables Co
            ps.setString(7, "In Stock");
            ps.executeUpdate();

            ps.setString(1, "Onions");
            ps.setDouble(2, 20.0);
            ps.setString(3, "kg");
            ps.setString(4, "Vegetables");
            ps.setDouble(5, 5.0);
            ps.setInt(6, 2);
            ps.setString(7, "In Stock");
            ps.executeUpdate();

            // Meat
            ps.setString(1, "Chicken");
            ps.setDouble(2, 12.0);
            ps.setString(3, "kg");
            ps.setString(4, "Meat");
            ps.setDouble(5, 5.0);
            ps.setInt(6, 3); // Premium Meats
            ps.setString(7, "In Stock");
            ps.executeUpdate();

            // Dairy
            ps.setString(1, "Paneer");
            ps.setDouble(2, 8.0);
            ps.setString(3, "kg");
            ps.setString(4, "Dairy");
            ps.setDouble(5, 2.0);
            ps.setInt(6, 4); // Local Dairy Farm
            ps.setString(7, "In Stock");
            ps.executeUpdate();

            // Grains
            ps.setString(1, "Rice");
            ps.setDouble(2, 50.0);
            ps.setString(3, "kg");
            ps.setString(4, "Grains");
            ps.setDouble(5, 10.0);
            ps.setInt(6, 1); // Local Market
            ps.setString(7, "In Stock");
            ps.executeUpdate();

            // Low stock item for testing
            ps.setString(1, "Salt");
            ps.setDouble(2, 1.0);
            ps.setString(3, "kg");
            ps.setString(4, "Spices");
            ps.setDouble(5, 2.0);
            ps.setInt(6, 1);
            ps.setString(7, "Low Stock");
            ps.executeUpdate();

        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    /**
     * Close the current thread's database connection
     */
    public static void closeConnection() {
        Connection conn = threadLocalConnection.get();
        if (conn != null) {
            try {
                if (!conn.isClosed()) {
                    conn.close();
                }
            } catch (SQLException e) {
                e.printStackTrace();
            } finally {
                threadLocalConnection.remove();
                activeConnections.remove(Thread.currentThread());
            }
        }
    }

    /**
     * Close all active database connections
     * Should be called during application shutdown
     */
    public static void closeAllConnections() {
        for (Connection conn : activeConnections.values()) {
            try {
                if (conn != null && !conn.isClosed()) {
                    conn.close();
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        activeConnections.clear();
    }

    /**
     * Get connection count for monitoring
     */
    public static int getActiveConnectionCount() {
        return activeConnections.size();
    }

    /**
     * Test database connectivity
     */
    public static boolean testConnection() {
        try (Connection conn = getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT 1")) {
            return rs.next();
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Main method for testing database connection
     */
    public static void main(String[] args) {
        System.out.println("Testing Database Connection...");

        try {
            // Test basic connection
            if (testConnection()) {
                System.out.println("✓ Basic database connection successful");
            } else {
                System.out.println("✗ Basic database connection failed");
                return;
            }

            // Test multiple threads
            System.out.println("Testing thread safety with multiple connections...");

            Thread[] threads = new Thread[5];
            boolean[] results = new boolean[5];

            for (int i = 0; i < 5; i++) {
                final int threadIndex = i;
                threads[i] = new Thread(() -> {
                    try {
                        Connection conn = getConnection();
                        System.out.println("Thread " + threadIndex + ": Got connection");

                        // Test query
                        try (Statement stmt = conn.createStatement();
                             ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM users")) {
                            if (rs.next()) {
                                System.out.println("Thread " + threadIndex + ": Query successful, users count: " + rs.getInt(1));
                                results[threadIndex] = true;
                            }
                        }

                        Thread.sleep(100); // Simulate some work

                    } catch (Exception e) {
                        System.err.println("Thread " + threadIndex + " failed: " + e.getMessage());
                        results[threadIndex] = false;
                    }
                }, "TestThread-" + i);
            }

            // Start all threads
            for (Thread thread : threads) {
                thread.start();
            }

            // Wait for all threads to complete
            for (Thread thread : threads) {
                thread.join();
            }

            // Check results
            int successCount = 0;
            for (boolean result : results) {
                if (result) successCount++;
            }

            System.out.println("Thread safety test: " + successCount + "/5 threads successful");

            if (successCount == 5) {
                System.out.println("✓ All database connection tests passed!");
            } else {
                System.out.println("✗ Some tests failed");
            }

            // Test connection count
            System.out.println("Active connections: " + getActiveConnectionCount());

            // Cleanup
            closeAllConnections();
            System.out.println("✓ Cleanup completed");

        } catch (Exception e) {
            System.err.println("Test failed with exception: " + e.getMessage());
            e.printStackTrace();
        }
    }
}