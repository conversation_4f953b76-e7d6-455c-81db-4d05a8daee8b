package com.restaurant.model;

import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class EmployeeDAO {
    
    /**
     * Get all employees
     */
    public static List<Employee> getAllEmployees() {
        List<Employee> employees = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT * FROM employees ORDER BY name")) {
            
            while (rs.next()) {
                Employee employee = createEmployeeFromResultSet(rs);
                employees.add(employee);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return employees;
    }
    
    /**
     * Get employee by ID
     */
    public static Employee getEmployeeById(int id) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("SELECT * FROM employees WHERE id = ?")) {
            
            ps.setInt(1, id);
            ResultSet rs = ps.executeQuery();
            
            if (rs.next()) {
                return createEmployeeFromResultSet(rs);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * Get employee by employee ID
     */
    public static Employee getEmployeeByEmployeeId(String employeeId) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("SELECT * FROM employees WHERE employee_id = ?")) {
            
            ps.setString(1, employeeId);
            ResultSet rs = ps.executeQuery();
            
            if (rs.next()) {
                return createEmployeeFromResultSet(rs);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * Add new employee
     */
    public static boolean addEmployee(Employee employee) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "INSERT INTO employees (name, employee_id, department, position, phone, email, address, " +
                     "date_of_joining, status, salary, shift_timing, notes, created_at, last_updated) " +
                     "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", Statement.RETURN_GENERATED_KEYS)) {
            
            ps.setString(1, employee.getName());
            ps.setString(2, employee.getEmployeeId());
            ps.setString(3, employee.getDepartment());
            ps.setString(4, employee.getPosition());
            ps.setString(5, employee.getPhone());
            ps.setString(6, employee.getEmail());
            ps.setString(7, employee.getAddress());
            ps.setDate(8, Date.valueOf(employee.getDateOfJoining()));
            ps.setString(9, employee.getStatus());
            ps.setDouble(10, employee.getSalary());
            ps.setString(11, employee.getShiftTiming());
            ps.setString(12, employee.getNotes());
            ps.setTimestamp(13, Timestamp.valueOf(LocalDateTime.now()));
            ps.setTimestamp(14, Timestamp.valueOf(LocalDateTime.now()));
            
            int result = ps.executeUpdate();
            
            if (result > 0) {
                ResultSet rs = ps.getGeneratedKeys();
                if (rs.next()) {
                    employee.setId(rs.getInt(1));
                }
                return true;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Update employee
     */
    public static boolean updateEmployee(Employee employee) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "UPDATE employees SET name = ?, employee_id = ?, department = ?, position = ?, phone = ?, " +
                     "email = ?, address = ?, date_of_joining = ?, status = ?, salary = ?, shift_timing = ?, " +
                     "notes = ?, last_updated = ? WHERE id = ?")) {
            
            ps.setString(1, employee.getName());
            ps.setString(2, employee.getEmployeeId());
            ps.setString(3, employee.getDepartment());
            ps.setString(4, employee.getPosition());
            ps.setString(5, employee.getPhone());
            ps.setString(6, employee.getEmail());
            ps.setString(7, employee.getAddress());
            ps.setDate(8, Date.valueOf(employee.getDateOfJoining()));
            ps.setString(9, employee.getStatus());
            ps.setDouble(10, employee.getSalary());
            ps.setString(11, employee.getShiftTiming());
            ps.setString(12, employee.getNotes());
            ps.setTimestamp(13, Timestamp.valueOf(LocalDateTime.now()));
            ps.setInt(14, employee.getId());
            
            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Delete employee
     */
    public static boolean deleteEmployee(int id) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("DELETE FROM employees WHERE id = ?")) {
            
            ps.setInt(1, id);
            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Get active employees
     */
    public static List<Employee> getActiveEmployees() {
        List<Employee> employees = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("SELECT * FROM employees WHERE status = 'Active' ORDER BY name")) {
            
            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                Employee employee = createEmployeeFromResultSet(rs);
                employees.add(employee);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return employees;
    }
    
    /**
     * Get employees by department
     */
    public static List<Employee> getEmployeesByDepartment(String department) {
        List<Employee> employees = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("SELECT * FROM employees WHERE department = ? ORDER BY name")) {
            
            ps.setString(1, department);
            ResultSet rs = ps.executeQuery();
            
            while (rs.next()) {
                Employee employee = createEmployeeFromResultSet(rs);
                employees.add(employee);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return employees;
    }
    
    /**
     * Search employees
     */
    public static List<Employee> searchEmployees(String searchTerm) {
        List<Employee> employees = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT * FROM employees WHERE name LIKE ? OR employee_id LIKE ? OR department LIKE ? OR position LIKE ? ORDER BY name")) {
            
            String searchPattern = "%" + searchTerm + "%";
            ps.setString(1, searchPattern);
            ps.setString(2, searchPattern);
            ps.setString(3, searchPattern);
            ps.setString(4, searchPattern);
            
            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                Employee employee = createEmployeeFromResultSet(rs);
                employees.add(employee);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return employees;
    }
    
    /**
     * Get employee names for dropdown
     */
    public static List<String> getEmployeeNames() {
        List<String> names = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("SELECT name FROM employees WHERE status = 'Active' ORDER BY name")) {
            
            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                names.add(rs.getString("name"));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return names;
    }
    
    /**
     * Check if employee ID exists
     */
    public static boolean employeeIdExists(String employeeId) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("SELECT COUNT(*) FROM employees WHERE employee_id = ?")) {
            
            ps.setString(1, employeeId);
            ResultSet rs = ps.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Helper method to create Employee object from ResultSet
     */
    private static Employee createEmployeeFromResultSet(ResultSet rs) throws SQLException {
        Employee employee = new Employee(
            rs.getInt("id"),
            rs.getString("name"),
            rs.getString("employee_id"),
            rs.getString("department"),
            rs.getString("position")
        );
        
        employee.setPhone(rs.getString("phone"));
        employee.setEmail(rs.getString("email"));
        employee.setAddress(rs.getString("address"));
        employee.setDateOfJoining(rs.getDate("date_of_joining").toLocalDate());
        employee.setStatus(rs.getString("status"));
        employee.setSalary(rs.getDouble("salary"));
        employee.setShiftTiming(rs.getString("shift_timing"));
        employee.setNotes(rs.getString("notes"));
        employee.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
        employee.setLastUpdated(rs.getTimestamp("last_updated").toLocalDateTime());
        
        return employee;
    }
}
