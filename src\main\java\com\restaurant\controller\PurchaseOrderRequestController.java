package com.restaurant.controller;

import com.restaurant.model.PurchaseOrder;
import com.restaurant.model.Supplier;
import com.restaurant.model.InventoryItem;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.*;

import java.net.URL;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Controller for Purchase Order Request System
 */
public class PurchaseOrderRequestController implements Initializable {

    @FXML private Button backButton;
    @FXML private ComboBox<Supplier> supplierComboBox;
    @FXML private ComboBox<InventoryItem> itemComboBox;
    @FXML private TextField quantityField;
    @FXML private ComboBox<String> unitComboBox;
    @FXML private Label currentStockLabel;
    @FXML private Button saveButton;
    @FXML private Button sendRequestButton;
    @FXML private VBox ordersContainer;

    // Data
    private List<Supplier> suppliers;
    private List<InventoryItem> inventoryItems;
    private List<PurchaseOrder> purchaseOrders;
    private InventoryItem selectedItem;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        initializeData();
        setupComboBoxes();
        setupEventHandlers();
        displayPurchaseOrders();
    }

    private void initializeData() {
        // Initialize suppliers
        suppliers = new ArrayList<>();
        suppliers.add(new Supplier(1, "Parthiv Agency", "Parthiv Shah", "9876543210", "<EMAIL>", "123 Market Street"));
        suppliers.add(new Supplier(2, "Radhe Dairy", "Radhe Patel", "9876543211", "<EMAIL>", "456 Dairy Lane"));
        suppliers.add(new Supplier(3, "Fresh Vegetables Co.", "Amit Kumar", "9876543212", "<EMAIL>", "789 Green Street"));
        suppliers.add(new Supplier(4, "Spice World", "Ravi Sharma", "9876543213", "<EMAIL>", "321 Spice Avenue"));

        // Initialize inventory items with current stock
        inventoryItems = new ArrayList<>();
        inventoryItems.add(new InventoryItem(1, "Capsicum", 5.0, "Kg", "In Stock"));
        inventoryItems.add(new InventoryItem(2, "Tomatoes", 12.0, "Kg", "In Stock"));
        inventoryItems.add(new InventoryItem(3, "Onions", 8.5, "Kg", "In Stock"));
        inventoryItems.add(new InventoryItem(4, "Milk", 25.0, "Ltr", "In Stock"));
        inventoryItems.add(new InventoryItem(5, "Paneer", 3.0, "Kg", "Low Stock"));
        inventoryItems.add(new InventoryItem(6, "Butter", 2.5, "Kg", "Low Stock"));
        inventoryItems.add(new InventoryItem(7, "Turmeric Powder", 1.2, "Kg", "Low Stock"));
        inventoryItems.add(new InventoryItem(8, "Red Chili Powder", 0.8, "Kg", "Low Stock"));

        // Set suppliers for items
        inventoryItems.get(0).setSupplier("Parthiv Agency");
        inventoryItems.get(1).setSupplier("Parthiv Agency");
        inventoryItems.get(2).setSupplier("Parthiv Agency");
        inventoryItems.get(3).setSupplier("Radhe Dairy");
        inventoryItems.get(4).setSupplier("Radhe Dairy");
        inventoryItems.get(5).setSupplier("Radhe Dairy");
        inventoryItems.get(6).setSupplier("Spice World");
        inventoryItems.get(7).setSupplier("Spice World");

        // Initialize purchase orders
        purchaseOrders = new ArrayList<>();
        PurchaseOrder order1 = new PurchaseOrder("PO324G", "Main Kitchen", "Capsicum", "12 Kg", "Saved");
        order1.setSupplierName("Parthiv Agency");
        order1.setTotalAmount(5124.0);
        order1.setCreatedAt(LocalDateTime.now().minusDays(1));

        PurchaseOrder order2 = new PurchaseOrder("PO456K", "Main Kitchen", "Milk", "20 Ltr", "Processed");
        order2.setSupplierName("Radhe Dairy");
        order2.setTotalAmount(2673.0);
        order2.setCreatedAt(LocalDateTime.now().minusDays(2));

        purchaseOrders.add(order1);
        purchaseOrders.add(order2);
    }

    private void setupComboBoxes() {
        // Setup supplier combo box
        supplierComboBox.getItems().addAll(suppliers);
        supplierComboBox.setConverter(new javafx.util.StringConverter<Supplier>() {
            @Override
            public String toString(Supplier supplier) {
                return supplier != null ? supplier.getName() : "";
            }

            @Override
            public Supplier fromString(String string) {
                return suppliers.stream()
                    .filter(s -> s.getName().equals(string))
                    .findFirst()
                    .orElse(null);
            }
        });

        // Setup item combo box
        itemComboBox.getItems().addAll(inventoryItems);
        itemComboBox.setConverter(new javafx.util.StringConverter<InventoryItem>() {
            @Override
            public String toString(InventoryItem item) {
                return item != null ? item.getName() : "";
            }

            @Override
            public InventoryItem fromString(String string) {
                return inventoryItems.stream()
                    .filter(i -> i.getName().equals(string))
                    .findFirst()
                    .orElse(null);
            }
        });

        // Setup unit combo box
        unitComboBox.getItems().addAll("Kg", "Ltr", "Pcs", "Gms", "Ml", "Dozen", "Box");
        unitComboBox.setValue("Kg");
    }

    private void setupEventHandlers() {
        // Item selection handler
        itemComboBox.setOnAction(e -> {
            selectedItem = itemComboBox.getValue();
            updateCurrentStockDisplay();
            updateUnitFromItem();
        });

        // Supplier selection handler
        supplierComboBox.setOnAction(e -> {
            filterItemsBySupplier();
        });

        // Quantity field validation
        quantityField.textProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal.matches("\\d*\\.?\\d*")) {
                quantityField.setText(oldVal);
            }
        });
    }

    private void updateCurrentStockDisplay() {
        if (selectedItem != null) {
            String stockText = "Current Stock: " + selectedItem.getQuantity() + " " + selectedItem.getUnit();
            currentStockLabel.setText(stockText);

            // Add color coding based on stock level
            String status = selectedItem.getStatus();
            if ("Out of Stock".equals(status)) {
                currentStockLabel.getStyleClass().removeAll("low-stock", "normal-stock");
                currentStockLabel.getStyleClass().add("out-of-stock");
            } else if ("Low Stock".equals(status)) {
                currentStockLabel.getStyleClass().removeAll("out-of-stock", "normal-stock");
                currentStockLabel.getStyleClass().add("low-stock");
            } else {
                currentStockLabel.getStyleClass().removeAll("out-of-stock", "low-stock");
                currentStockLabel.getStyleClass().add("normal-stock");
            }
        } else {
            currentStockLabel.setText("Current Stock: --");
            currentStockLabel.getStyleClass().removeAll("out-of-stock", "low-stock", "normal-stock");
        }
    }

    private void updateUnitFromItem() {
        if (selectedItem != null && selectedItem.getUnit() != null) {
            unitComboBox.setValue(selectedItem.getUnit());
        }
    }

    private void filterItemsBySupplier() {
        Supplier selectedSupplier = supplierComboBox.getValue();
        itemComboBox.getItems().clear();
        
        if (selectedSupplier != null) {
            // Filter items by supplier
            List<InventoryItem> filteredItems = inventoryItems.stream()
                .filter(item -> selectedSupplier.getName().equals(item.getSupplier()))
                .toList();
            itemComboBox.getItems().addAll(filteredItems);
        } else {
            // Show all items if no supplier selected
            itemComboBox.getItems().addAll(inventoryItems);
        }
        
        // Clear selection and current stock display
        itemComboBox.setValue(null);
        selectedItem = null;
        updateCurrentStockDisplay();
    }

    @FXML
    private void saveRequest() {
        if (validateForm()) {
            PurchaseOrder order = createPurchaseOrderFromForm();
            order.setStatus("Saved");
            purchaseOrders.add(order);
            
            displayPurchaseOrders();
            clearForm();
            showAlert("Success", "Purchase order saved successfully!");
        }
    }

    @FXML
    private void sendRequest() {
        if (validateForm()) {
            PurchaseOrder order = createPurchaseOrderFromForm();
            order.setStatus("Processed");
            purchaseOrders.add(order);
            
            displayPurchaseOrders();
            clearForm();
            showAlert("Success", "Purchase order sent to supplier successfully!");
        }
    }

    private boolean validateForm() {
        if (supplierComboBox.getValue() == null) {
            showAlert("Validation Error", "Please select a supplier.");
            return false;
        }
        
        if (itemComboBox.getValue() == null) {
            showAlert("Validation Error", "Please select an item.");
            return false;
        }
        
        if (quantityField.getText().trim().isEmpty()) {
            showAlert("Validation Error", "Please enter quantity.");
            return false;
        }
        
        try {
            double quantity = Double.parseDouble(quantityField.getText().trim());
            if (quantity <= 0) {
                showAlert("Validation Error", "Quantity must be greater than 0.");
                return false;
            }
        } catch (NumberFormatException e) {
            showAlert("Validation Error", "Please enter a valid quantity.");
            return false;
        }
        
        return true;
    }

    private PurchaseOrder createPurchaseOrderFromForm() {
        String poNumber = generatePONumber();
        String item = selectedItem.getName();
        String quantity = quantityField.getText().trim() + " " + unitComboBox.getValue();

        PurchaseOrder order = new PurchaseOrder(poNumber, "Main Kitchen", item, quantity, "Saved");
        order.setSupplierName(supplierComboBox.getValue().getName());

        // Calculate total amount (simplified calculation)
        double qty = Double.parseDouble(quantityField.getText().trim());
        double unitPrice = 50.0; // Default price since InventoryItem doesn't have unitPrice
        order.setUnitPrice(unitPrice);
        order.setTotalAmount(qty * unitPrice);

        return order;
    }

    private String generatePONumber() {
        return "PO" + (1000 + purchaseOrders.size()) + "X";
    }

    private void clearForm() {
        supplierComboBox.setValue(null);
        itemComboBox.setValue(null);
        quantityField.clear();
        unitComboBox.setValue("Kg");
        selectedItem = null;
        updateCurrentStockDisplay();
        
        // Reset item list to show all items
        itemComboBox.getItems().clear();
        itemComboBox.getItems().addAll(inventoryItems);
    }

    private void displayPurchaseOrders() {
        ordersContainer.getChildren().clear();

        for (PurchaseOrder order : purchaseOrders) {
            HBox orderRow = createOrderRow(order);
            ordersContainer.getChildren().add(orderRow);
        }
    }

    private HBox createOrderRow(PurchaseOrder order) {
        HBox row = new HBox(15);
        row.setAlignment(Pos.CENTER_LEFT);
        row.getStyleClass().add("order-row");
        row.setPadding(new Insets(12, 15, 12, 15));

        // From (Supplier)
        Label fromLabel = new Label(order.getSupplierName());
        fromLabel.getStyleClass().add("order-cell");
        fromLabel.setPrefWidth(150);
        HBox.setHgrow(fromLabel, Priority.ALWAYS);

        // PO Number
        Label poLabel = new Label(order.getRequestNumber());
        poLabel.getStyleClass().add("order-cell");
        poLabel.setPrefWidth(80);

        // Total Amount
        Label totalLabel = new Label("₹" + String.format("%.0f", order.getTotalAmount()));
        totalLabel.getStyleClass().add("order-cell");
        totalLabel.setPrefWidth(80);

        // Status
        Label statusLabel = new Label(order.getStatus());
        statusLabel.getStyleClass().addAll("order-cell", "status-label");
        statusLabel.setPrefWidth(100);

        // Add status-specific styling
        switch (order.getStatus().toLowerCase()) {
            case "saved":
                statusLabel.getStyleClass().add("status-saved");
                break;
            case "processed":
                statusLabel.getStyleClass().add("status-processed");
                break;
            case "delivered":
                statusLabel.getStyleClass().add("status-delivered");
                break;
            case "cancelled":
                statusLabel.getStyleClass().add("status-cancelled");
                break;
        }

        row.getChildren().addAll(fromLabel, poLabel, totalLabel, statusLabel);

        return row;
    }

    @FXML
    private void goBack() {
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/Dashboard.fxml"));
            javafx.scene.Parent root = loader.load();

            javafx.stage.Stage stage = (javafx.stage.Stage) backButton.getScene().getWindow();
            stage.getScene().setRoot(root);

            System.out.println("Navigated back to dashboard from Purchase Order Request");

        } catch (Exception e) {
            System.err.println("Error navigating back to dashboard: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
