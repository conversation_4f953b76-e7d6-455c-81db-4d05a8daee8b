# Global ESC Key Implementation

## Overview

Implemented a comprehensive global ESC key handling system that works everywhere in the restaurant application. The system provides:

- **Single ESC**: Navigate back one step (like a browser back button)
- **Double ESC**: Return directly to dashboard (within 800ms)
- **Universal Coverage**: Works in all views, dialogs, and forms

## Implementation Details

### 1. UniversalNavigationManager Enhancement

**File**: `src/main/java/com/restaurant/util/UniversalNavigationManager.java`

#### Key Features Added:

1. **Global ESC Handler Registration**:
```java
public void registerGlobalEscHandler(Scene scene) {
    // Store existing key handler if any
    var existingHandler = scene.getOnKeyPressed();
    
    // Create new handler that combines existing functionality with ESC handling
    scene.setOnKeyPressed(event -> {
        // Handle ESC key globally
        if (event.getCode() == javafx.scene.input.KeyCode.ESCAPE) {
            System.out.println("UniversalNavigationManager: Global ESC key detected");
            handleEscapeKey();
            event.consume();
            return;
        }
        
        // Call existing handler if it exists
        if (existingHandler != null) {
            existingHandler.handle(event);
        }
    });
}
```

2. **Improved Double ESC Detection**:
```java
private static final long DOUBLE_ESC_THRESHOLD = 800; // 800ms for double ESC (more user-friendly)

public void handleEscapeKey() {
    long currentTime = System.currentTimeMillis();
    
    if (currentTime - lastEscapeTime < DOUBLE_ESC_THRESHOLD) {
        // Double ESC - go to dashboard
        System.out.println("UniversalNavigationManager: Double ESC detected - Going to Dashboard");
        goToDashboard();
    } else {
        // Single ESC - normal back
        System.out.println("UniversalNavigationManager: Single ESC detected - Going back");
        goBack();
    }
    
    lastEscapeTime = currentTime;
}
```

3. **Auto-Registration System**:
```java
public void initializeGlobalEscHandling() {
    try {
        // Register ESC handler for current stage if available
        autoRegisterEscHandler();
        
        // Set up a listener for future stage changes
        Platform.runLater(() -> {
            try {
                Stage stage = findStage();
                if (stage != null) {
                    // Monitor scene changes
                    stage.sceneProperty().addListener((obs, oldScene, newScene) -> {
                        if (newScene != null) {
                            registerGlobalEscHandler(newScene);
                        }
                    });
                    
                    // Register for current scene
                    if (stage.getScene() != null) {
                        registerGlobalEscHandler(stage.getScene());
                    }
                }
            } catch (Exception e) {
                System.err.println("UniversalNavigationManager: Error setting up scene listener: " + e.getMessage());
            }
        });
        
        System.out.println("UniversalNavigationManager: Global ESC handling initialized");
        
    } catch (Exception e) {
        System.err.println("UniversalNavigationManager: Failed to initialize global ESC handling: " + e.getMessage());
        e.printStackTrace();
    }
}
```

### 2. Application Startup Integration

**File**: `src/main/java/com/restaurant/RestaurantApp.java`

Added global ESC initialization after the main stage is shown:

```java
primaryStage.show();

// Initialize global ESC key handling after the stage is shown
Platform.runLater(() -> {
    try {
        com.restaurant.util.UniversalNavigationManager.getInstance().initializeGlobalEscHandling();
        System.out.println("Global ESC key handling initialized");
    } catch (Exception e) {
        System.err.println("Failed to initialize global ESC handling: " + e.getMessage());
        e.printStackTrace();
    }
});
```

### 3. Dashboard Controller Integration

**File**: `src/main/java/com/restaurant/controller/DashboardController.java`

Added explicit ESC handler registration in the dashboard:

```java
// Register global ESC handler for dashboard
UniversalNavigationManager.getInstance().registerGlobalEscHandler(mainContainer.getScene());
```

### 4. KeyboardShortcutManager Update

**File**: `src/main/java/com/restaurant/util/KeyboardShortcutManager.java`

Updated ESC key handling to delegate to UniversalNavigationManager:

```java
addShortcut(new KeyCodeCombination(KeyCode.ESCAPE), 
           "Cancel/Back", () -> {
               System.out.println("ESC key detected - delegating to UniversalNavigationManager");
               com.restaurant.util.UniversalNavigationManager.getInstance().handleEscapeKey();
           });
```

## How It Works

### Navigation Flow

1. **Single ESC Press**:
   - Checks navigation history stack
   - If history exists: Goes back to previous view
   - If no history: Goes to dashboard
   - Preserves navigation context

2. **Double ESC Press** (within 800ms):
   - Immediately navigates to dashboard
   - Clears navigation history
   - Provides quick "home" functionality

### Scene Management

1. **Auto-Registration**: ESC handler is automatically registered for every new scene
2. **Preservation**: Existing key handlers are preserved and chained
3. **Event Consumption**: ESC events are properly consumed to prevent conflicts

### Error Handling

1. **Graceful Fallback**: If navigation fails, automatically goes to dashboard
2. **Exception Safety**: All operations are wrapped in try-catch blocks
3. **Logging**: Comprehensive logging for debugging

## Benefits

### User Experience
- ✅ **Consistent Behavior**: ESC works the same way everywhere
- ✅ **Intuitive Navigation**: Single ESC = back, Double ESC = home
- ✅ **No Dead Ends**: Always provides a way to navigate
- ✅ **Fast Access**: Quick return to dashboard with double ESC

### Technical Benefits
- ✅ **Global Coverage**: Works in all views without individual implementation
- ✅ **Non-Intrusive**: Preserves existing keyboard handlers
- ✅ **Automatic**: Self-registering system requires no manual setup
- ✅ **Robust**: Comprehensive error handling and fallbacks

## Testing

### Manual Testing Steps

1. **Single ESC Test**:
   - Navigate: Dashboard → Menu Management → ESC
   - Expected: Returns to Dashboard
   - Navigate: Dashboard → Menu Management → User Management → ESC
   - Expected: Returns to Menu Management

2. **Double ESC Test**:
   - Navigate: Dashboard → Menu Management → User Management
   - Press ESC twice quickly (within 800ms)
   - Expected: Returns directly to Dashboard

3. **Universal Coverage Test**:
   - Test ESC in: All forms, dialogs, tables, input fields
   - Expected: ESC works consistently everywhere

### Console Output

When working correctly, you should see:
```
UniversalNavigationManager: Global ESC key detected
UniversalNavigationManager: Single ESC detected - Going back
UniversalNavigationManager: Navigated to [PreviousController] (History size: X)
```

For double ESC:
```
UniversalNavigationManager: Global ESC key detected
UniversalNavigationManager: Double ESC detected - Going to Dashboard
UniversalNavigationManager: Navigated to Dashboard - History cleared
```

## Troubleshooting

### If ESC Doesn't Work

1. Check console for initialization message:
   ```
   UniversalNavigationManager: Global ESC handling initialized
   ```

2. Verify scene registration:
   ```
   UniversalNavigationManager: Global ESC handler registered for scene
   ```

3. Check for conflicts with existing handlers

### Common Issues

1. **ESC Not Detected**: Scene might not have focus
2. **Double ESC Too Sensitive**: Adjust `DOUBLE_ESC_THRESHOLD` value
3. **Navigation Fails**: Check FXML file paths and controller names

## Future Enhancements

1. **Customizable Timing**: Allow users to configure double ESC threshold
2. **Visual Feedback**: Show navigation breadcrumbs
3. **Keyboard Shortcuts Help**: F1 to show all available shortcuts
4. **Navigation History**: Show recent navigation history

The global ESC key system is now fully implemented and should work consistently throughout the entire application.
