package com.restaurant.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Marketing Campaign model for CRM system
 */
public class MarketingCampaign {
    private int id;
    private String name;
    private String type; // "SMS", "WhatsApp", "Email", "Push Notification"
    private String targetSegment; // "All", "New Customer", "Regular", "High Spender", "Lapsed Customer"
    private String message;
    private String offerDetails;
    private double discountPercentage;
    private double discountAmount;
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private String status; // "Draft", "Scheduled", "Active", "Completed", "Cancelled"
    private int targetCustomers;
    private int sentCount;
    private int deliveredCount;
    private int openedCount;
    private int clickedCount;
    private int redeemedCount;
    private double conversionRate;
    private double revenue;
    private String createdBy;
    private LocalDateTime createdAt;
    private LocalDateTime lastUpdated;
    private List<String> targetCustomerIds;
    private String campaignCode;
    private boolean isAutomated;
    private String triggerCondition;

    // Constructors
    public MarketingCampaign() {
        this.createdAt = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
        this.status = "Draft";
        this.targetCustomerIds = new ArrayList<>();
        this.sentCount = 0;
        this.deliveredCount = 0;
        this.openedCount = 0;
        this.clickedCount = 0;
        this.redeemedCount = 0;
        this.conversionRate = 0.0;
        this.revenue = 0.0;
    }

    public MarketingCampaign(String name, String type, String targetSegment, String message) {
        this();
        this.name = name;
        this.type = type;
        this.targetSegment = targetSegment;
        this.message = message;
        this.campaignCode = generateCampaignCode();
    }

    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { 
        this.name = name;
        this.lastUpdated = LocalDateTime.now();
    }

    public String getType() { return type; }
    public void setType(String type) { 
        this.type = type;
        this.lastUpdated = LocalDateTime.now();
    }

    public String getTargetSegment() { return targetSegment; }
    public void setTargetSegment(String targetSegment) { 
        this.targetSegment = targetSegment;
        this.lastUpdated = LocalDateTime.now();
    }

    public String getMessage() { return message; }
    public void setMessage(String message) { 
        this.message = message;
        this.lastUpdated = LocalDateTime.now();
    }

    public String getOfferDetails() { return offerDetails; }
    public void setOfferDetails(String offerDetails) { 
        this.offerDetails = offerDetails;
        this.lastUpdated = LocalDateTime.now();
    }

    public double getDiscountPercentage() { return discountPercentage; }
    public void setDiscountPercentage(double discountPercentage) { 
        this.discountPercentage = discountPercentage;
        this.lastUpdated = LocalDateTime.now();
    }

    public double getDiscountAmount() { return discountAmount; }
    public void setDiscountAmount(double discountAmount) { 
        this.discountAmount = discountAmount;
        this.lastUpdated = LocalDateTime.now();
    }

    public LocalDateTime getStartDate() { return startDate; }
    public void setStartDate(LocalDateTime startDate) { 
        this.startDate = startDate;
        this.lastUpdated = LocalDateTime.now();
    }

    public LocalDateTime getEndDate() { return endDate; }
    public void setEndDate(LocalDateTime endDate) { 
        this.endDate = endDate;
        this.lastUpdated = LocalDateTime.now();
    }

    public String getStatus() { return status; }
    public void setStatus(String status) { 
        this.status = status;
        this.lastUpdated = LocalDateTime.now();
    }

    public int getTargetCustomers() { return targetCustomers; }
    public void setTargetCustomers(int targetCustomers) { this.targetCustomers = targetCustomers; }

    public int getSentCount() { return sentCount; }
    public void setSentCount(int sentCount) { 
        this.sentCount = sentCount;
        updateConversionRate();
        this.lastUpdated = LocalDateTime.now();
    }

    public int getDeliveredCount() { return deliveredCount; }
    public void setDeliveredCount(int deliveredCount) { 
        this.deliveredCount = deliveredCount;
        this.lastUpdated = LocalDateTime.now();
    }

    public int getOpenedCount() { return openedCount; }
    public void setOpenedCount(int openedCount) { 
        this.openedCount = openedCount;
        this.lastUpdated = LocalDateTime.now();
    }

    public int getClickedCount() { return clickedCount; }
    public void setClickedCount(int clickedCount) { 
        this.clickedCount = clickedCount;
        this.lastUpdated = LocalDateTime.now();
    }

    public int getRedeemedCount() { return redeemedCount; }
    public void setRedeemedCount(int redeemedCount) { 
        this.redeemedCount = redeemedCount;
        updateConversionRate();
        this.lastUpdated = LocalDateTime.now();
    }

    public double getConversionRate() { return conversionRate; }
    public void setConversionRate(double conversionRate) { this.conversionRate = conversionRate; }

    public double getRevenue() { return revenue; }
    public void setRevenue(double revenue) { 
        this.revenue = revenue;
        this.lastUpdated = LocalDateTime.now();
    }

    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getLastUpdated() { return lastUpdated; }
    public void setLastUpdated(LocalDateTime lastUpdated) { this.lastUpdated = lastUpdated; }

    public List<String> getTargetCustomerIds() { return targetCustomerIds; }
    public void setTargetCustomerIds(List<String> targetCustomerIds) { 
        this.targetCustomerIds = targetCustomerIds;
        this.targetCustomers = targetCustomerIds.size();
        this.lastUpdated = LocalDateTime.now();
    }

    public String getCampaignCode() { return campaignCode; }
    public void setCampaignCode(String campaignCode) { this.campaignCode = campaignCode; }

    public boolean isAutomated() { return isAutomated; }
    public void setAutomated(boolean automated) { 
        isAutomated = automated;
        this.lastUpdated = LocalDateTime.now();
    }

    public String getTriggerCondition() { return triggerCondition; }
    public void setTriggerCondition(String triggerCondition) { 
        this.triggerCondition = triggerCondition;
        this.lastUpdated = LocalDateTime.now();
    }

    // Business Logic Methods
    private void updateConversionRate() {
        if (sentCount > 0) {
            this.conversionRate = (double) redeemedCount / sentCount * 100;
        }
    }

    public void incrementSent() {
        this.sentCount++;
        updateConversionRate();
        this.lastUpdated = LocalDateTime.now();
    }

    public void incrementDelivered() {
        this.deliveredCount++;
        this.lastUpdated = LocalDateTime.now();
    }

    public void incrementOpened() {
        this.openedCount++;
        this.lastUpdated = LocalDateTime.now();
    }

    public void incrementClicked() {
        this.clickedCount++;
        this.lastUpdated = LocalDateTime.now();
    }

    public void incrementRedeemed(double orderValue) {
        this.redeemedCount++;
        this.revenue += orderValue;
        updateConversionRate();
        this.lastUpdated = LocalDateTime.now();
    }

    private String generateCampaignCode() {
        return "CAMP" + System.currentTimeMillis() % 10000;
    }

    public double getDeliveryRate() {
        return sentCount > 0 ? (double) deliveredCount / sentCount * 100 : 0;
    }

    public double getOpenRate() {
        return deliveredCount > 0 ? (double) openedCount / deliveredCount * 100 : 0;
    }

    public double getClickRate() {
        return openedCount > 0 ? (double) clickedCount / openedCount * 100 : 0;
    }

    public double getROI() {
        // Simple ROI calculation - would need campaign cost for accurate calculation
        return revenue > 0 ? revenue / 1000 * 100 : 0; // Assuming ₹1000 campaign cost
    }

    public String getFormattedRevenue() {
        return String.format("₹%.2f", revenue);
    }

    public String getFormattedConversionRate() {
        return String.format("%.1f%%", conversionRate);
    }

    public String getFormattedDeliveryRate() {
        return String.format("%.1f%%", getDeliveryRate());
    }

    public String getFormattedOpenRate() {
        return String.format("%.1f%%", getOpenRate());
    }

    public String getStatusIcon() {
        switch (status) {
            case "Draft": return "📝";
            case "Scheduled": return "⏰";
            case "Active": return "🚀";
            case "Completed": return "✅";
            case "Cancelled": return "❌";
            default: return "📋";
        }
    }

    public String getTypeIcon() {
        switch (type) {
            case "SMS": return "📱";
            case "WhatsApp": return "💬";
            case "Email": return "📧";
            case "Push Notification": return "🔔";
            default: return "📢";
        }
    }

    public boolean isActive() {
        LocalDateTime now = LocalDateTime.now();
        return "Active".equals(status) && 
               (startDate == null || !now.isBefore(startDate)) && 
               (endDate == null || !now.isAfter(endDate));
    }

    @Override
    public String toString() {
        return name + " (" + type + " - " + status + ")";
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        MarketingCampaign campaign = (MarketingCampaign) obj;
        return id == campaign.id;
    }

    @Override
    public int hashCode() {
        return Integer.hashCode(id);
    }
}
