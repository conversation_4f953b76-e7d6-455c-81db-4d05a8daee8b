@echo off
echo 🔧 JAVA DATABASE FIX - NO SQLITE3 COMMAND NEEDED...
echo.

echo STEP 1: Deleting old database...
if exist restaurant.db (
    del restaurant.db
    echo ✅ Deleted restaurant.db
) else (
    echo ℹ️ No existing database found
)

echo.
echo STEP 2: Creating simple database creator (no dependencies on existing classes)...

echo package com.restaurant.util; > src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo import java.sql.*; >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo public class DirectDatabaseCreator { >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo     public static void main(String[] args) { >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo         System.out.println("🔧 Creating database directly with Java..."); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo         System.out.println(); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo         String url = "*************************"; >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo         try (Connection conn = DriverManager.getConnection(url)) { >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             System.out.println("✅ Database connection established"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             // Create users table >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             String createUsersTable = "CREATE TABLE IF NOT EXISTS users (" + >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 "id INTEGER PRIMARY KEY AUTOINCREMENT, " + >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 "username TEXT UNIQUE NOT NULL, " + >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 "password TEXT NOT NULL, " + >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 "role TEXT NOT NULL" + >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 ")"; >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             try (Statement stmt = conn.createStatement()) { >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 stmt.execute(createUsersTable); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 System.out.println("✅ Users table created"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             } >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             // Create online_orders table >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             String createOrdersTable = "CREATE TABLE IF NOT EXISTS online_orders (" + >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 "id INTEGER PRIMARY KEY AUTOINCREMENT, " + >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 "order_id TEXT UNIQUE NOT NULL, " + >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 "platform TEXT NOT NULL, " + >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 "customer_name TEXT NOT NULL, " + >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 "customer_phone TEXT, " + >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 "delivery_address TEXT, " + >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 "status TEXT NOT NULL, " + >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 "total_amount REAL NOT NULL, " + >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 "order_time TEXT NOT NULL, " + >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 "status_updated_time TEXT, " + >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 "special_instructions TEXT, " + >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 "estimated_prep_time INTEGER" + >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 ")"; >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             try (Statement stmt = conn.createStatement()) { >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 stmt.execute(createOrdersTable); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 System.out.println("✅ Online orders table created"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             } >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             // Insert admin user with BCrypt hash for 'admin123' >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             String insertAdmin = "INSERT OR REPLACE INTO users (username, password, role) VALUES (?, ?, ?)"; >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             try (PreparedStatement pstmt = conn.prepareStatement(insertAdmin)) { >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 pstmt.setString(1, "admin"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 pstmt.setString(2, "$2a$10$N9qo8uLOickgx2ZMRZoMye.Uo0aBOBjXoXebXeQABkc4ILoLGxjvO"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 pstmt.setString(3, "ADMIN"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 pstmt.executeUpdate(); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 System.out.println("✅ Admin user created"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 System.out.println("   Username: admin"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 System.out.println("   Password: admin123"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 System.out.println("   Role: ADMIN"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             } >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             // Insert staff user with BCrypt hash for 'staff123' >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             try (PreparedStatement pstmt = conn.prepareStatement(insertAdmin)) { >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 pstmt.setString(1, "staff"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 pstmt.setString(2, "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi."); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 pstmt.setString(3, "STAFF"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 pstmt.executeUpdate(); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 System.out.println("✅ Staff user created"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 System.out.println("   Username: staff"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 System.out.println("   Password: staff123"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 System.out.println("   Role: STAFF"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             } >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             // Verify users were created >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             System.out.println(); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             System.out.println("📋 USERS IN DATABASE:"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             String selectUsers = "SELECT id, username, role FROM users"; >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             try (Statement stmt = conn.createStatement(); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                  ResultSet rs = stmt.executeQuery(selectUsers)) { >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 System.out.println("ID | Username | Role"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 System.out.println("---|----------|-----"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 while (rs.next()) { >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                     System.out.printf("%2d | %-8s | %s%n", >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                         rs.getInt("id"), >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                         rs.getString("username"), >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                         rs.getString("role")); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo                 } >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             } >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             System.out.println(); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             System.out.println("🎉 DATABASE SETUP COMPLETE!"); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             System.err.println("❌ Error: " + e.getMessage()); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo             e.printStackTrace(); >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo         } >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo     } >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java
echo } >> src\main\java\com\restaurant\util\DirectDatabaseCreator.java

echo ✅ DirectDatabaseCreator class created
echo.

echo STEP 3: Compiling only the database creator...
javac -cp "%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar" -d target/classes src/main/java/com/restaurant/util/DirectDatabaseCreator.java

if %ERRORLEVEL% neq 0 (
    echo ❌ Compilation failed, trying with Maven...
    mvn compile -q
    if %ERRORLEVEL% neq 0 (
        echo ❌ Maven compilation also failed
        pause
        exit /b 1
    )
)

echo ✅ Compilation successful
echo.

echo STEP 4: Creating database...
java -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar" com.restaurant.util.DirectDatabaseCreator

echo.
echo STEP 5: Verifying database file...
if exist restaurant.db (
    echo ✅ Database file created: restaurant.db
    dir restaurant.db
) else (
    echo ❌ Database file not found
    pause
    exit /b 1
)

echo.
echo 🔑 LOGIN CREDENTIALS:
echo Username: admin
echo Password: admin123
echo Role: ADMIN (select from dropdown)
echo.

echo STEP 6: Starting application with MP3 audio support...
echo.

java -Dprism.order=sw ^
     -Dprism.allowhidpi=false ^
     -Djavafx.animation.pulse=60 ^
     -Djava.awt.headless=false ^
     -Dsqlite.purejava=false ^
     -Xms512m ^
     -Xmx2g ^
     -XX:+UseG1GC ^
     -XX:MaxGCPauseMillis=200 ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics,javafx.media ^
     -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\pdfbox\2.0.27\pdfbox-2.0.27.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\fontbox\2.0.27\fontbox-2.0.27.jar;%USERPROFILE%\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2.jar" ^
     com.restaurant.RestaurantApp

echo.
echo 🎉 JAVA DATABASE FIX COMPLETE!
echo.
echo LOGIN INSTRUCTIONS:
echo 1. Username: admin
echo 2. Password: admin123
echo 3. Role: ADMIN (MUST select from dropdown)
echo 4. Click "Login to Dashboard →"
echo 5. Click "🍽️ Finish List" to test MP3 audio
echo.
echo 🎵 MP3 AUDIO TESTING:
echo - You should hear audio immediately when Finish List loads
echo - Audio files from sounds/ folder will be used
echo - Click "✅ Accept & Prepare" to stop audio
echo - Add test orders to hear new notifications
echo.

pause
