@echo off
echo 🔧 FIXING DATABASE COLUMN NAME ISSUE...
echo.

echo PROBLEM FOUND: UserDA<PERSON> expects 'password_hash' column but database has 'password' column
echo SOLUTION: Create database with correct column name
echo.

echo STEP 1: Delete incorrect database...
if exist restaurant.db (
    del restaurant.db
    echo ✅ Deleted database with wrong column name
)

echo.
echo STEP 2: Create database with CORRECT column names...

echo package com.restaurant.util; > src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo import java.sql.*; >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo public class CorrectDatabaseCreator { >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo     public static void main(String[] args) { >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo         System.out.println("🔧 Creating database with CORRECT column names..."); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo         try { >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             Class.forName("org.sqlite.JDBC"); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             System.out.println("✅ SQLite driver loaded"); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             System.err.println("❌ Driver error: " + e.getMessage()); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             return; >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo         } >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo         String url = "*************************"; >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo         try (Connection conn = DriverManager.getConnection(url)) { >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             System.out.println("✅ Database connected"); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             // Create users table with CORRECT column name: password_hash >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             String createTable = "CREATE TABLE users (" + >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo                 "id INTEGER PRIMARY KEY AUTOINCREMENT, " + >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo                 "username TEXT UNIQUE NOT NULL, " + >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo                 "password_hash TEXT NOT NULL, " + >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo                 "role TEXT NOT NULL)"; >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             conn.createStatement().execute(createTable); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             System.out.println("✅ Users table created with password_hash column"); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             // Insert admin with BCrypt hash for 'admin123' >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             String insert = "INSERT INTO users (username, password_hash, role) VALUES (?, ?, ?)"; >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             PreparedStatement ps = conn.prepareStatement(insert); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             ps.setString(1, "admin"); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             ps.setString(2, "$2a$10$N9qo8uLOickgx2ZMRZoMye.Uo0aBOBjXoXebXeQABkc4ILoLGxjvO"); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             ps.setString(3, "ADMIN"); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             ps.executeUpdate(); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             ps.close(); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             System.out.println("✅ Admin user created with correct column"); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             // Insert staff user >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             ps = conn.prepareStatement(insert); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             ps.setString(1, "staff"); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             ps.setString(2, "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi."); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             ps.setString(3, "STAFF"); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             ps.executeUpdate(); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             ps.close(); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             System.out.println("✅ Staff user created"); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             // Verify with correct column name >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             System.out.println(); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             System.out.println("📋 USERS WITH CORRECT COLUMNS:"); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             ResultSet rs = conn.createStatement().executeQuery("SELECT id, username, role, password_hash FROM users"); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             while (rs.next()) { >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo                 System.out.println("ID: " + rs.getInt("id") + " | Username: " + rs.getString("username") + " | Role: " + rs.getString("role")); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo                 System.out.println("Password hash: " + rs.getString("password_hash").substring(0, 20) + "..."); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo                 System.out.println("---"); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             } >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             rs.close(); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             System.out.println("🎉 SUCCESS! Database created with correct column names."); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             System.out.println("UserDAO.authenticate should now work properly!"); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo. >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             System.err.println("❌ Error: " + e.getMessage()); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo             e.printStackTrace(); >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo         } >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo     } >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java
echo } >> src\main\java\com\restaurant\util\CorrectDatabaseCreator.java

echo ✅ CorrectDatabaseCreator created
echo.

echo STEP 3: Compile and run...
javac -cp "%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar" -d target/classes src/main/java/com/restaurant/util/CorrectDatabaseCreator.java

if %ERRORLEVEL% neq 0 (
    echo ❌ Direct compilation failed, trying Maven...
    mvn compile -q
    if %ERRORLEVEL% neq 0 (
        echo ❌ Maven compilation failed
        pause
        exit /b 1
    )
)

echo.
echo STEP 4: Creating database with correct columns...
java -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar" com.restaurant.util.CorrectDatabaseCreator

echo.
echo STEP 5: Check database size...
if exist restaurant.db (
    dir restaurant.db
    echo ✅ Database created with correct column names
) else (
    echo ❌ Database creation failed
    pause
    exit /b 1
)

echo.
echo 🔑 LOGIN CREDENTIALS (should work now):
echo Username: admin
echo Password: admin123
echo Role: ADMIN
echo.

echo STEP 6: Starting application...
echo.

java -Dprism.order=sw ^
     -Dprism.allowhidpi=false ^
     -Djavafx.animation.pulse=60 ^
     -Djava.awt.headless=false ^
     -Dsqlite.purejava=false ^
     -Xms512m ^
     -Xmx2g ^
     -XX:+UseG1GC ^
     -XX:MaxGCPauseMillis=200 ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics,javafx.media ^
     -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\pdfbox\2.0.27\pdfbox-2.0.27.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\fontbox\2.0.27\fontbox-2.0.27.jar;%USERPROFILE%\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2.jar" ^
     com.restaurant.RestaurantApp

echo.
echo 🎉 COLUMN NAME FIX COMPLETE!
echo.
echo THE ISSUE WAS: UserDAO expects 'password_hash' column but database had 'password' column
echo THE FIX: Created database with correct 'password_hash' column name
echo.
echo LOGIN SHOULD NOW WORK WITH:
echo Username: admin
echo Password: admin123
echo Role: ADMIN (select from dropdown)
echo.
echo After login, click "🍽️ Finish List" to test MP3 audio!
echo.

pause
