package com.restaurant.service;

import com.restaurant.model.ForecastResult;
import com.restaurant.model.SalesData;
import com.restaurant.model.OrderDAO;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Service class for AI-powered sales forecasting
 * This is a simplified implementation that demonstrates the forecasting concept
 * In a real-world scenario, this would integrate with machine learning models
 */
public class AIForecastService {

    private static final double BASE_DAILY_SALES = 15000.0; // Base daily sales amount
    private static final double SEASONAL_VARIANCE = 0.3; // 30% seasonal variance
    private static final double RANDOM_VARIANCE = 0.15; // 15% random variance
    private static final double WEEKEND_BOOST = 1.4; // 40% boost on weekends
    private static final double GROWTH_TREND = 0.02; // 2% monthly growth trend

    /**
     * Generate AI forecast based on parameters
     */
    public ForecastResult generateForecast(Map<String, Object> parameters) {
        LocalDate startDate = (LocalDate) parameters.get("startDate");
        LocalDate endDate = (LocalDate) parameters.get("endDate");
        String category = (String) parameters.get("category");
        String forecastPeriod = (String) parameters.get("forecastPeriod");
        String salesChannel = (String) parameters.get("salesChannel");
        String location = (String) parameters.get("location");

        // Generate historical data
        List<SalesData> historicalData = generateHistoricalData(startDate, endDate, category, salesChannel, location);
        
        // Generate forecast data
        int forecastDays = parseForecastPeriod(forecastPeriod);
        List<SalesData> forecastData = generateForecastData(endDate.plusDays(1), forecastDays, category, salesChannel, location, historicalData);
        
        // Create forecast result
        ForecastResult result = new ForecastResult(historicalData, forecastData);
        
        // Calculate metrics
        calculateForecastMetrics(result, category);
        
        return result;
    }

    /**
     * Generate simulated historical sales data
     */
    private List<SalesData> generateHistoricalData(LocalDate startDate, LocalDate endDate, 
                                                  String category, String salesChannel, String location) {
        List<SalesData> data = new ArrayList<>();
        LocalDate currentDate = startDate;
        
        while (!currentDate.isAfter(endDate)) {
            double sales = calculateDailySales(currentDate, category, salesChannel, location, false);
            SalesData salesData = new SalesData(currentDate, sales, category, salesChannel, location);
            data.add(salesData);
            currentDate = currentDate.plusDays(1);
        }
        
        return data;
    }

    /**
     * Generate forecast data using AI simulation
     */
    private List<SalesData> generateForecastData(LocalDate startDate, int days, 
                                                String category, String salesChannel, String location,
                                                List<SalesData> historicalData) {
        List<SalesData> data = new ArrayList<>();
        LocalDate currentDate = startDate;
        
        // Calculate historical average for trend analysis
        double historicalAverage = historicalData.stream()
            .mapToDouble(SalesData::getSales)
            .average()
            .orElse(BASE_DAILY_SALES);
        
        for (int i = 0; i < days; i++) {
            double sales = calculateDailySales(currentDate, category, salesChannel, location, true);
            
            // Apply AI trend prediction (slight upward trend based on historical data)
            double trendMultiplier = 1.0 + (GROWTH_TREND * (i / 30.0)); // Monthly growth
            sales *= trendMultiplier;
            
            SalesData salesData = new SalesData(currentDate, sales, category, salesChannel, location);
            data.add(salesData);
            currentDate = currentDate.plusDays(1);
        }
        
        return data;
    }

    /**
     * Calculate daily sales with various factors
     */
    private double calculateDailySales(LocalDate date, String category, String salesChannel, 
                                     String location, boolean isForecast) {
        double baseSales = BASE_DAILY_SALES;
        
        // Apply category multiplier
        baseSales *= getCategoryMultiplier(category);
        
        // Apply sales channel multiplier
        baseSales *= getSalesChannelMultiplier(salesChannel);
        
        // Apply location multiplier
        baseSales *= getLocationMultiplier(location);
        
        // Apply day of week effect
        baseSales *= getDayOfWeekMultiplier(date);
        
        // Apply seasonal effect
        baseSales *= getSeasonalMultiplier(date);
        
        // Add random variance
        double variance = isForecast ? RANDOM_VARIANCE * 0.5 : RANDOM_VARIANCE; // Less variance in forecast
        double randomFactor = 1.0 + (ThreadLocalRandom.current().nextGaussian() * variance);
        baseSales *= Math.max(0.5, randomFactor); // Ensure positive sales
        
        return Math.round(baseSales * 100.0) / 100.0; // Round to 2 decimal places
    }

    private double getCategoryMultiplier(String category) {
        switch (category) {
            case "Main Course": return 1.2;
            case "Appetizers": return 0.8;
            case "Beverages": return 0.9;
            case "Desserts": return 0.7;
            default: return 1.0; // All Categories
        }
    }

    private double getSalesChannelMultiplier(String channel) {
        switch (channel) {
            case "Dine-in": return 1.1;
            case "Takeaway": return 0.9;
            case "Delivery": return 1.0;
            case "Online Orders": return 0.95;
            default: return 1.0; // All Channels
        }
    }

    private double getLocationMultiplier(String location) {
        switch (location) {
            case "Main Branch": return 1.2;
            case "Downtown": return 1.1;
            case "Mall Branch": return 0.9;
            default: return 1.0; // All Locations
        }
    }

    private double getDayOfWeekMultiplier(LocalDate date) {
        int dayOfWeek = date.getDayOfWeek().getValue();
        if (dayOfWeek >= 6) { // Saturday and Sunday
            return WEEKEND_BOOST;
        } else if (dayOfWeek == 5) { // Friday
            return 1.2;
        } else {
            return 1.0;
        }
    }

    private double getSeasonalMultiplier(LocalDate date) {
        int month = date.getMonthValue();
        // Higher sales in winter months (Nov-Feb) and summer (May-Aug)
        if (month >= 11 || month <= 2) {
            return 1.0 + SEASONAL_VARIANCE;
        } else if (month >= 5 && month <= 8) {
            return 1.0 + (SEASONAL_VARIANCE * 0.7);
        } else {
            return 1.0;
        }
    }

    private int parseForecastPeriod(String period) {
        switch (period) {
            case "7 Days": return 7;
            case "14 Days": return 14;
            case "30 Days": return 30;
            case "90 Days": return 90;
            default: return 30;
        }
    }

    /**
     * Calculate forecast metrics and insights
     */
    private void calculateForecastMetrics(ForecastResult result, String category) {
        List<SalesData> historical = result.getHistoricalData();
        List<SalesData> forecast = result.getForecastData();
        
        if (historical.isEmpty() || forecast.isEmpty()) {
            return;
        }
        
        // Calculate predicted sales
        double totalForecastSales = forecast.stream().mapToDouble(SalesData::getSales).sum();
        result.setPredictedSales(totalForecastSales);
        
        // Calculate growth percentage
        double historicalAverage = historical.stream().mapToDouble(SalesData::getSales).average().orElse(0);
        double forecastAverage = forecast.stream().mapToDouble(SalesData::getSales).average().orElse(0);
        double growthPercentage = historicalAverage > 0 ? 
            ((forecastAverage - historicalAverage) / historicalAverage) * 100 : 0;
        result.setGrowthPercentage(growthPercentage);
        
        // Set confidence level (simulated based on data consistency)
        double confidence = calculateConfidenceLevel(historical, forecast);
        result.setConfidenceLevel(confidence);
        
        // Set top category
        result.setTopCategory(category.equals("All Categories") ? "Main Course" : category);
        result.setTopCategoryPercent(category.equals("All Categories") ? 35.0 : 100.0);
        
        // Set forecast period
        if (!forecast.isEmpty()) {
            result.setForecastStartDate(forecast.get(0).getDate());
            result.setForecastEndDate(forecast.get(forecast.size() - 1).getDate());
        }
        
        // Generate insights
        List<String> insights = generateInsights(result);
        result.setInsights(insights);
        
        // Set confidence bounds
        double avgForecast = forecastAverage;
        result.setUpperConfidenceBound(avgForecast * 1.15);
        result.setLowerConfidenceBound(avgForecast * 0.85);
    }

    private double calculateConfidenceLevel(List<SalesData> historical, List<SalesData> forecast) {
        // Simulate confidence based on data variance and trend consistency
        double variance = calculateVariance(historical);
        double baseConfidence = 85.0;
        
        // Lower confidence for higher variance
        double confidenceAdjustment = Math.min(20.0, variance * 0.1);
        
        return Math.max(45.0, Math.min(95.0, baseConfidence - confidenceAdjustment));
    }

    private double calculateVariance(List<SalesData> data) {
        if (data.size() < 2) return 0.0;
        
        double mean = data.stream().mapToDouble(SalesData::getSales).average().orElse(0);
        double variance = data.stream()
            .mapToDouble(d -> Math.pow(d.getSales() - mean, 2))
            .average()
            .orElse(0);
        
        return Math.sqrt(variance) / mean * 100; // Coefficient of variation as percentage
    }

    private List<String> generateInsights(ForecastResult result) {
        List<String> insights = new ArrayList<>();
        
        double growth = result.getGrowthPercentage();
        if (growth > 5) {
            insights.add("Strong growth trend expected - consider increasing inventory");
        } else if (growth < -5) {
            insights.add("Declining trend detected - review marketing strategies");
        } else {
            insights.add("Stable sales pattern - maintain current operations");
        }
        
        if (result.getConfidenceLevel() > 80) {
            insights.add("High confidence prediction - reliable for planning");
        } else if (result.getConfidenceLevel() < 60) {
            insights.add("Lower confidence - monitor actual results closely");
        }
        
        insights.add("Weekend sales typically 40% higher than weekdays");
        insights.add("Consider seasonal promotions to boost performance");
        
        return insights;
    }
}
