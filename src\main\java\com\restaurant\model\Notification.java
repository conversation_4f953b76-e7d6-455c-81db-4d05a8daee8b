package com.restaurant.model;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Notification model for restaurant management system
 * Supports various notification types including online orders, KOT alerts, billing notifications
 */
public class Notification {
    
    public enum NotificationType {
        ONLINE_ORDER("🛒", "Online Order"),
        KOT_ALERT("📋", "KOT Alert"),
        BILLING("💰", "Billing"),
        INVENTORY("📦", "Inventory"),
        TABLE_ALERT("🍽️", "Table Alert"),
        SYSTEM("⚙️", "System"),
        SCANNER("📱", "Scanner"),
        PAYMENT("💳", "Payment"),
        KITCHEN("👨‍🍳", "Kitchen");
        
        private final String icon;
        private final String displayName;
        
        NotificationType(String icon, String displayName) {
            this.icon = icon;
            this.displayName = displayName;
        }
        
        public String getIcon() { return icon; }
        public String getDisplayName() { return displayName; }
    }
    
    public enum Priority {
        LOW("🔵", "#2196F3"),
        MEDIUM("🟡", "#FF9800"),
        HIGH("🔴", "#F44336"),
        URGENT("🚨", "#E91E63");
        
        private final String icon;
        private final String color;
        
        Priority(String icon, String color) {
            this.icon = icon;
            this.color = color;
        }
        
        public String getIcon() { return icon; }
        public String getColor() { return color; }
    }
    
    private int id;
    private NotificationType type;
    private Priority priority;
    private String title;
    private String message;
    private String source; // e.g., "Swiggy", "Zomato", "Table 5", "Kitchen"
    private LocalDateTime timestamp;
    private boolean isRead;
    private boolean isActionable;
    private String actionData; // JSON data for actions
    
    public Notification() {
        this.timestamp = LocalDateTime.now();
        this.isRead = false;
        this.isActionable = false;
    }
    
    public Notification(NotificationType type, Priority priority, String title, String message, String source) {
        this();
        this.type = type;
        this.priority = priority;
        this.title = title;
        this.message = message;
        this.source = source;
    }
    
    // Static factory methods for common notification types
    public static Notification createOnlineOrder(String platform, String orderNumber, double amount) {
        return new Notification(
            NotificationType.ONLINE_ORDER,
            Priority.HIGH,
            "New " + platform + " Order",
            "Order #" + orderNumber + " - ₹" + String.format("%.2f", amount),
            platform
        ).setActionable(true);
    }
    
    public static Notification createKOTAlert(String tableNumber, int waitingMinutes) {
        Priority priority = waitingMinutes > 20 ? Priority.URGENT : 
                           waitingMinutes > 15 ? Priority.HIGH : Priority.MEDIUM;
        return new Notification(
            NotificationType.KOT_ALERT,
            priority,
            "KOT Waiting - Table " + tableNumber,
            "Order waiting for " + waitingMinutes + " minutes",
            "Table " + tableNumber
        ).setActionable(true);
    }
    
    public static Notification createBillingAlert(String tableNumber, double amount) {
        return new Notification(
            NotificationType.BILLING,
            Priority.MEDIUM,
            "Bill Request - Table " + tableNumber,
            "Amount: ₹" + String.format("%.2f", amount),
            "Table " + tableNumber
        ).setActionable(true);
    }
    
    public static Notification createInventoryAlert(String itemName, int remainingStock) {
        Priority priority = remainingStock <= 5 ? Priority.URGENT : 
                           remainingStock <= 10 ? Priority.HIGH : Priority.MEDIUM;
        return new Notification(
            NotificationType.INVENTORY,
            priority,
            "Low Stock Alert",
            itemName + " - " + remainingStock + " units remaining",
            "Inventory"
        ).setActionable(true);
    }
    
    public static Notification createScannerAlert(String qrData, String tableNumber) {
        return new Notification(
            NotificationType.SCANNER,
            Priority.MEDIUM,
            "QR Code Scanned",
            "Table " + tableNumber + " menu accessed",
            "Scanner"
        ).setActionable(true);
    }
    
    public static Notification createKitchenAlert(String orderNumber, String status) {
        return new Notification(
            NotificationType.KITCHEN,
            Priority.MEDIUM,
            "Kitchen Update",
            "Order #" + orderNumber + " - " + status,
            "Kitchen"
        );
    }
    
    // Getters and setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public NotificationType getType() { return type; }
    public void setType(NotificationType type) { this.type = type; }
    
    public Priority getPriority() { return priority; }
    public void setPriority(Priority priority) { this.priority = priority; }
    
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
    
    public String getSource() { return source; }
    public void setSource(String source) { this.source = source; }
    
    public LocalDateTime getTimestamp() { return timestamp; }
    public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
    
    public boolean isRead() { return isRead; }
    public void setRead(boolean read) { isRead = read; }
    
    public boolean isActionable() { return isActionable; }
    public Notification setActionable(boolean actionable) { 
        this.isActionable = actionable; 
        return this;
    }
    
    public String getActionData() { return actionData; }
    public void setActionData(String actionData) { this.actionData = actionData; }
    
    // Utility methods
    public String getFormattedTimestamp() {
        return timestamp.format(DateTimeFormatter.ofPattern("HH:mm"));
    }
    
    public String getFormattedDateTime() {
        return timestamp.format(DateTimeFormatter.ofPattern("dd/MM HH:mm"));
    }
    
    public String getDisplayText() {
        return type.getIcon() + " " + priority.getIcon() + " " + title + "\n" + message;
    }
    
    public long getMinutesAgo() {
        return java.time.Duration.between(timestamp, LocalDateTime.now()).toMinutes();
    }
    
    public String getTimeAgoText() {
        long minutes = getMinutesAgo();
        if (minutes < 1) return "Just now";
        if (minutes < 60) return minutes + "m ago";
        long hours = minutes / 60;
        if (hours < 24) return hours + "h ago";
        long days = hours / 24;
        return days + "d ago";
    }
    
    @Override
    public String toString() {
        return String.format("[%s] %s %s: %s - %s (%s)", 
            getFormattedTimestamp(), 
            type.getIcon(), 
            priority.getIcon(), 
            title, 
            message, 
            source);
    }
}
