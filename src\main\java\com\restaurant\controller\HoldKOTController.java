package com.restaurant.controller;

import com.restaurant.model.Order;
import com.restaurant.model.OrderItem;
import com.restaurant.model.HeldOrder;
import com.restaurant.model.HeldOrderDAO;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.stage.Stage;
import java.net.URL;
import java.util.ResourceBundle;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Controller for Hold KOT functionality
 * Allows staff to temporarily hold orders before sending to kitchen
 */
public class HoldKOTController implements Initializable {
    
    @FXML private TableView<OrderItem> currentOrderTable;
    @FXML private TableColumn<OrderItem, String> itemNameColumn;
    @FXML private TableColumn<OrderItem, Integer> quantityColumn;
    @FXML private TableColumn<OrderItem, Double> priceColumn;
    @FXML private TableColumn<OrderItem, Double> totalColumn;
    
    @FXML private TableView<HeldOrder> heldOrdersTable;
    @FXML private TableColumn<HeldOrder, String> heldOrderIdColumn;
    @FXML private TableColumn<HeldOrder, Integer> heldTableColumn;
    @FXML private TableColumn<HeldOrder, String> heldTimeColumn;
    @FXML private TableColumn<HeldOrder, String> heldReasonColumn;
    @FXML private TableColumn<HeldOrder, Double> heldTotalColumn;
    
    @FXML private TextField tableNumberField;
    @FXML private TextArea holdReasonArea;
    @FXML private Label currentOrderTotal;
    @FXML private Label heldOrdersCount;
    @FXML private Button holdOrderBtn;
    @FXML private Button releaseOrderBtn;
    @FXML private Button deleteHeldOrderBtn;
    @FXML private Button closeBtn;
    
    private ObservableList<OrderItem> currentOrderItems;
    private ObservableList<HeldOrder> heldOrders;
    private Order currentOrder;
    private HeldOrderDAO heldOrderDAO;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        initializeDAO();
        initializeTables();
        loadHeldOrders();
        setupEventHandlers();
        
        System.out.println("HoldKOTController initialized");
    }
    
    /**
     * Initialize DAO
     */
    private void initializeDAO() {
        try {
            heldOrderDAO = new HeldOrderDAO();
        } catch (Exception e) {
            System.err.println("Failed to initialize HeldOrderDAO: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Initialize table columns and data
     */
    private void initializeTables() {
        // Current Order Table
        currentOrderItems = FXCollections.observableArrayList();
        currentOrderTable.setItems(currentOrderItems);
        
        itemNameColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getMenuItem().getName()));
        
        quantityColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleIntegerProperty(
                cellData.getValue().getQuantity()).asObject());
        
        priceColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleDoubleProperty(
                cellData.getValue().getPrice()).asObject());
        
        totalColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleDoubleProperty(
                cellData.getValue().getPrice() * cellData.getValue().getQuantity()).asObject());
        
        // Held Orders Table
        heldOrders = FXCollections.observableArrayList();
        heldOrdersTable.setItems(heldOrders);
        
        heldOrderIdColumn.setCellValueFactory(new PropertyValueFactory<>("orderId"));
        heldTableColumn.setCellValueFactory(new PropertyValueFactory<>("tableNumber"));
        heldTimeColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getHeldTime().format(DateTimeFormatter.ofPattern("HH:mm:ss"))));
        heldReasonColumn.setCellValueFactory(new PropertyValueFactory<>("reason"));
        heldTotalColumn.setCellValueFactory(new PropertyValueFactory<>("totalAmount"));
    }
    
    /**
     * Load held orders from database
     */
    private void loadHeldOrders() {
        try {
            if (heldOrderDAO != null) {
                heldOrders.clear();
                heldOrders.addAll(heldOrderDAO.getAllHeldOrders());
                updateHeldOrdersCount();
            }
        } catch (Exception e) {
            System.err.println("Failed to load held orders: " + e.getMessage());
            showAlert("Error", "Failed to load held orders: " + e.getMessage());
        }
    }
    
    /**
     * Setup event handlers
     */
    private void setupEventHandlers() {
        // Enable/disable buttons based on selection
        heldOrdersTable.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            boolean hasSelection = newSelection != null;
            releaseOrderBtn.setDisable(!hasSelection);
            deleteHeldOrderBtn.setDisable(!hasSelection);
        });
        
        // Update total when current order changes
        currentOrderTable.getItems().addListener((javafx.collections.ListChangeListener<OrderItem>) change -> {
            updateCurrentOrderTotal();
        });
    }
    
    /**
     * Set current order to be held
     */
    public void setCurrentOrder(Order order) {
        this.currentOrder = order;
        if (order != null) {
            currentOrderItems.clear();
            currentOrderItems.addAll(order.getItems());
            tableNumberField.setText(String.valueOf(order.getTableNumber()));
            updateCurrentOrderTotal();
            
            // Enable hold button if order has items
            holdOrderBtn.setDisable(currentOrderItems.isEmpty());
        }
    }
    
    /**
     * Update current order total display
     */
    private void updateCurrentOrderTotal() {
        if (currentOrder != null) {
            double total = currentOrder.calculateTotal();
            currentOrderTotal.setText(String.format("₹%.2f", total));
        } else {
            currentOrderTotal.setText("₹0.00");
        }
    }
    
    /**
     * Update held orders count display
     */
    private void updateHeldOrdersCount() {
        heldOrdersCount.setText(String.valueOf(heldOrders.size()));
    }
    
    /**
     * Handle hold order button click
     */
    @FXML
    private void handleHoldOrder() {
        if (currentOrder == null || currentOrderItems.isEmpty()) {
            showAlert("No Order", "Please select an order with items to hold.");
            return;
        }
        
        String reason = holdReasonArea.getText().trim();
        if (reason.isEmpty()) {
            reason = "No reason specified";
        }
        
        try {
            // Create held order
            HeldOrder heldOrder = new HeldOrder();
            heldOrder.setOrderId("ORD" + System.currentTimeMillis());
            heldOrder.setTableNumber(currentOrder.getTableNumber());
            heldOrder.setHeldTime(LocalDateTime.now());
            heldOrder.setReason(reason);
            heldOrder.setTotalAmount(currentOrder.calculateTotal());
            heldOrder.setOrderData(currentOrder.toJsonString()); // Serialize order data
            
            // Save to database
            if (heldOrderDAO != null) {
                heldOrderDAO.saveHeldOrder(heldOrder);
            }
            
            // Add to table
            heldOrders.add(heldOrder);
            updateHeldOrdersCount();
            
            // Clear current order
            clearCurrentOrder();
            
            showAlert("Success", "Order held successfully!\n\nOrder ID: " + heldOrder.getOrderId() + 
                     "\nTable: " + heldOrder.getTableNumber() + 
                     "\nReason: " + reason);
            
            System.out.println("Order held: " + heldOrder.getOrderId());
            
        } catch (Exception e) {
            System.err.println("Failed to hold order: " + e.getMessage());
            showAlert("Error", "Failed to hold order: " + e.getMessage());
        }
    }
    
    /**
     * Handle release order button click
     */
    @FXML
    private void handleReleaseOrder() {
        HeldOrder selectedOrder = heldOrdersTable.getSelectionModel().getSelectedItem();
        if (selectedOrder == null) {
            showAlert("No Selection", "Please select a held order to release.");
            return;
        }
        
        try {
            // Remove from database
            if (heldOrderDAO != null) {
                heldOrderDAO.deleteHeldOrder(selectedOrder.getOrderId());
            }
            
            // Remove from table
            heldOrders.remove(selectedOrder);
            updateHeldOrdersCount();
            
            showAlert("Success", "Order released successfully!\n\nOrder ID: " + selectedOrder.getOrderId() + 
                     "\nTable: " + selectedOrder.getTableNumber() + 
                     "\nThe order can now be processed normally.");
            
            System.out.println("Order released: " + selectedOrder.getOrderId());
            
        } catch (Exception e) {
            System.err.println("Failed to release order: " + e.getMessage());
            showAlert("Error", "Failed to release order: " + e.getMessage());
        }
    }
    
    /**
     * Handle delete held order button click
     */
    @FXML
    private void handleDeleteHeldOrder() {
        HeldOrder selectedOrder = heldOrdersTable.getSelectionModel().getSelectedItem();
        if (selectedOrder == null) {
            showAlert("No Selection", "Please select a held order to delete.");
            return;
        }
        
        // Confirm deletion
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("Confirm Deletion");
        confirmAlert.setHeaderText("Delete Held Order");
        confirmAlert.setContentText("Are you sure you want to permanently delete this held order?\n\n" +
                                   "Order ID: " + selectedOrder.getOrderId() + 
                                   "\nTable: " + selectedOrder.getTableNumber());
        
        confirmAlert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                try {
                    // Remove from database
                    if (heldOrderDAO != null) {
                        heldOrderDAO.deleteHeldOrder(selectedOrder.getOrderId());
                    }
                    
                    // Remove from table
                    heldOrders.remove(selectedOrder);
                    updateHeldOrdersCount();
                    
                    showAlert("Success", "Held order deleted successfully!");
                    System.out.println("Held order deleted: " + selectedOrder.getOrderId());
                    
                } catch (Exception e) {
                    System.err.println("Failed to delete held order: " + e.getMessage());
                    showAlert("Error", "Failed to delete held order: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * Handle close button click
     */
    @FXML
    private void handleClose() {
        Stage stage = (Stage) closeBtn.getScene().getWindow();
        stage.close();
    }
    
    /**
     * Clear current order display
     */
    private void clearCurrentOrder() {
        currentOrder = null;
        currentOrderItems.clear();
        tableNumberField.clear();
        holdReasonArea.clear();
        currentOrderTotal.setText("₹0.00");
        holdOrderBtn.setDisable(true);
    }
    
    /**
     * Show alert dialog
     */
    private void showAlert(String title, String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }
}
