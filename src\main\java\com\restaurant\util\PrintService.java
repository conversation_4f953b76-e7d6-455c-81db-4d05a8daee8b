package com.restaurant.util;

import com.restaurant.model.Order;
import com.restaurant.model.OrderItem;
import com.restaurant.controller.ReportsController.DailySalesReport;
import com.restaurant.controller.ReportsController.PopularItemReport;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDType1Font;

import java.awt.Desktop;
import java.io.File;
import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.util.List;

public class PrintService {

    /**
     * Sanitize text to avoid font encoding issues
     */
    private static String sanitizeText(String text) {
        if (text == null) return "";
        // Replace problematic characters that might cause encoding issues
        return text.replaceAll("[^\\x00-\\x7F]", "?") // Replace non-ASCII characters
                  .replaceAll("₹", "Rs.") // Replace rupee symbol
                  .replaceAll("€", "EUR") // Replace euro symbol
                  .replaceAll("£", "GBP") // Replace pound symbol
                  .replaceAll("¥", "YEN"); // Replace yen symbol
    }

    public static void generateKOT(Order order) throws Exception {
        File kotFile = generateKOTPDF(order);
        openFile(kotFile);
    }

    public static void generateBill(Order order) throws Exception {
        File billFile = generateBillPDF(order);
        openFile(billFile);
    }

    public static void printKOT(Order order) throws Exception {
        generateKOT(order);
    }

    public static File generateKOTPDF(Order order) throws Exception {
        File tempFile = File.createTempFile("kot_" + order.getId() + "_", ".pdf");

        try (PDDocument document = new PDDocument()) {
            PDPage page = new PDPage();
            document.addPage(page);

            try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                // Header
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 20);
                contentStream.newLineAtOffset(200, 750);
                contentStream.showText("KITCHEN ORDER TICKET");
                contentStream.endText();

                // Order details
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 14);
                contentStream.newLineAtOffset(100, 720);
                contentStream.showText("Order #: " + order.getId());
                contentStream.newLineAtOffset(0, -20);
                contentStream.showText("Date: " + order.getTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                contentStream.newLineAtOffset(0, -20);
                contentStream.showText(order.isTakeaway() ? "TAKEAWAY ORDER" : "Table: " + order.getTableNumber());
                contentStream.endText();

                // Line separator
                contentStream.moveTo(100, 670);
                contentStream.lineTo(500, 670);
                contentStream.stroke();

                // Items header
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 12);
                contentStream.newLineAtOffset(100, 650);
                contentStream.showText("ITEMS TO PREPARE:");
                contentStream.endText();

                // Items list
                float y = 620;
                for (OrderItem item : order.getItems()) {
                    contentStream.beginText();
                    contentStream.setFont(PDType1Font.HELVETICA_BOLD, 14);
                    contentStream.newLineAtOffset(120, y);
                    contentStream.showText(item.getQuantity() + "x " + sanitizeText(item.getMenuItem().getName()));
                    contentStream.endText();

                    // Category info
                    contentStream.beginText();
                    contentStream.setFont(PDType1Font.HELVETICA, 10);
                    contentStream.newLineAtOffset(140, y - 12);
                    contentStream.showText("(" + item.getMenuItem().getCategory() + ")");
                    contentStream.endText();

                    y -= 35;
                }

                // Footer
                contentStream.moveTo(100, y - 10);
                contentStream.lineTo(500, y - 10);
                contentStream.stroke();

                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 12);
                contentStream.newLineAtOffset(100, y - 30);
                contentStream.showText("Time: " + java.time.LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                contentStream.endText();

                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_OBLIQUE, 10);
                contentStream.newLineAtOffset(200, 50);
                contentStream.showText("Kitchen Copy - Please prepare items as listed above");
                contentStream.endText();
            }

            document.save(tempFile);
        }

        return tempFile;
    }

    private static void openFile(File file) throws IOException {
        if (Desktop.isDesktopSupported()) {
            Desktop.getDesktop().open(file);
        } else {
            System.out.println("File saved at: " + file.getAbsolutePath());
        }
    }
    
    public static File generateBillPDF(Order order) throws Exception {
        File tempFile = File.createTempFile("bill_", ".pdf");
        
        try (PDDocument document = new PDDocument()) {
            PDPage page = new PDPage();
            document.addPage(page);
            
            try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                // Add header
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 16);
                contentStream.newLineAtOffset(100, 700);
                contentStream.showText("RESTAURANT BILL");
                contentStream.endText();
                
                // Add order details
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA, 12);
                contentStream.newLineAtOffset(100, 680);
                contentStream.showText("Order #: " + order.getId());
                contentStream.newLineAtOffset(0, -15);
                contentStream.showText("Date: " + order.getTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
                contentStream.newLineAtOffset(0, -15);
                contentStream.showText(order.isTakeaway() ? "Takeaway" : "Table: " + order.getTableNumber());
                contentStream.endText();
                
                // Add items
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA, 12);
                contentStream.newLineAtOffset(100, 630);
                contentStream.showText("Item");
                contentStream.newLineAtOffset(200, 0);
                contentStream.showText("Qty");
                contentStream.newLineAtOffset(50, 0);
                contentStream.showText("Price");
                contentStream.newLineAtOffset(50, 0);
                contentStream.showText("Total");
                contentStream.endText();
                
                // Add line
                contentStream.moveTo(100, 625);
                contentStream.lineTo(500, 625);
                contentStream.stroke();
                
                // Add items
                float y = 610;
                for (OrderItem item : order.getItems()) {
                    contentStream.beginText();
                    contentStream.setFont(PDType1Font.HELVETICA, 10);
                    contentStream.newLineAtOffset(100, y);
                    contentStream.showText(item.getMenuItem().getName());
                    contentStream.newLineAtOffset(200, 0);
                    contentStream.showText(String.valueOf(item.getQuantity()));
                    contentStream.newLineAtOffset(50, 0);
                    contentStream.showText(String.format("%.2f", item.getPrice()));
                    contentStream.newLineAtOffset(50, 0);
                    contentStream.showText(String.format("%.2f", item.getPrice() * item.getQuantity()));
                    contentStream.endText();
                    y -= 15;
                }
                
                // Add total
                contentStream.moveTo(100, y - 5);
                contentStream.lineTo(500, y - 5);
                contentStream.stroke();
                
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 12);
                contentStream.newLineAtOffset(350, y - 20);
                contentStream.showText("Subtotal:");
                contentStream.newLineAtOffset(100, 0);
                contentStream.showText(String.format("%.2f", order.calculateTotal()));
                contentStream.endText();
                
                // Add GST
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA, 12);
                contentStream.newLineAtOffset(350, y - 35);
                contentStream.showText("GST (18%):");
                contentStream.newLineAtOffset(100, 0);
                contentStream.showText(String.format("%.2f", order.calculateGST()));
                contentStream.endText();

                // Add Service Charge
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA, 12);
                contentStream.newLineAtOffset(350, y - 50);
                contentStream.showText("Service Charge (10%):");
                contentStream.newLineAtOffset(100, 0);
                contentStream.showText(String.format("%.2f", order.calculateServiceCharge()));
                contentStream.endText();

                // Add grand total
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 14);
                contentStream.newLineAtOffset(350, y - 70);
                contentStream.showText("GRAND TOTAL:");
                contentStream.newLineAtOffset(100, 0);
                contentStream.showText(String.format("%.2f", order.calculateGrandTotal()));
                contentStream.endText();
                
                // Add footer
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_OBLIQUE, 10);
                contentStream.newLineAtOffset(200, 100);
                contentStream.showText("Thank you for dining with us!");
                contentStream.endText();
            }
            
            document.save(tempFile);
        }

        return tempFile;
    }

    // Report Export Methods
    public static void exportDailySalesReport(List<DailySalesReport> dailyReports, String fromDate, String toDate) throws Exception {
        File reportFile = generateDailySalesReportPDF(dailyReports, fromDate, toDate);
        openFile(reportFile);
    }

    public static void exportPopularItemsReport(List<PopularItemReport> popularItems, String fromDate, String toDate) throws Exception {
        File reportFile = generatePopularItemsReportPDF(popularItems, fromDate, toDate);
        openFile(reportFile);
    }

    public static void exportComprehensiveReport(List<DailySalesReport> dailyReports, List<PopularItemReport> popularItems,
                                               String fromDate, String toDate, String totalSales, String totalOrders, String averageOrder) throws Exception {
        File reportFile = generateComprehensiveReportPDF(dailyReports, popularItems, fromDate, toDate, totalSales, totalOrders, averageOrder);
        openFile(reportFile);
    }

    private static File generateDailySalesReportPDF(List<DailySalesReport> dailyReports, String fromDate, String toDate) throws Exception {
        File tempFile = File.createTempFile("daily_sales_report_", ".pdf");

        try (PDDocument document = new PDDocument()) {
            PDPage page = new PDPage();
            document.addPage(page);

            try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                // Header
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 18);
                contentStream.newLineAtOffset(200, 750);
                contentStream.showText("DAILY SALES REPORT");
                contentStream.endText();

                // Date range
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA, 12);
                contentStream.newLineAtOffset(100, 720);
                contentStream.showText("Period: " + sanitizeText(fromDate) + " to " + sanitizeText(toDate));
                contentStream.endText();

                // Table headers
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 12);
                contentStream.newLineAtOffset(100, 680);
                contentStream.showText("Date");
                contentStream.newLineAtOffset(120, 0);
                contentStream.showText("Orders");
                contentStream.newLineAtOffset(80, 0);
                contentStream.showText("Sales");
                contentStream.newLineAtOffset(100, 0);
                contentStream.showText("Avg Order");
                contentStream.endText();

                // Line separator
                contentStream.moveTo(100, 675);
                contentStream.lineTo(500, 675);
                contentStream.stroke();

                // Data rows
                float y = 650;
                double totalSales = 0;
                int totalOrders = 0;

                for (DailySalesReport report : dailyReports) {
                    contentStream.beginText();
                    contentStream.setFont(PDType1Font.HELVETICA, 10);
                    contentStream.newLineAtOffset(100, y);
                    contentStream.showText(report.getDate());
                    contentStream.newLineAtOffset(120, 0);
                    contentStream.showText(String.valueOf(report.getOrderCount()));
                    contentStream.newLineAtOffset(80, 0);
                    contentStream.showText(String.format("$%.2f", report.getTotalSales()));
                    contentStream.newLineAtOffset(100, 0);
                    contentStream.showText(String.format("$%.2f", report.getAverageOrder()));
                    contentStream.endText();

                    totalSales += report.getTotalSales();
                    totalOrders += report.getOrderCount();
                    y -= 20;
                }

                // Summary
                contentStream.moveTo(100, y - 10);
                contentStream.lineTo(500, y - 10);
                contentStream.stroke();

                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 12);
                contentStream.newLineAtOffset(100, y - 30);
                contentStream.showText("TOTAL:");
                contentStream.newLineAtOffset(120, 0);
                contentStream.showText(String.valueOf(totalOrders));
                contentStream.newLineAtOffset(80, 0);
                contentStream.showText(String.format("$%.2f", totalSales));
                contentStream.newLineAtOffset(100, 0);
                contentStream.showText(String.format("$%.2f", totalOrders > 0 ? totalSales / totalOrders : 0));
                contentStream.endText();

                // Footer
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_OBLIQUE, 10);
                contentStream.newLineAtOffset(200, 50);
                contentStream.showText("Generated on " + java.time.LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                contentStream.endText();
            }

            document.save(tempFile);
        }

        return tempFile;
    }

    private static File generatePopularItemsReportPDF(List<PopularItemReport> popularItems, String fromDate, String toDate) throws Exception {
        File tempFile = File.createTempFile("popular_items_report_", ".pdf");

        try (PDDocument document = new PDDocument()) {
            PDPage page = new PDPage();
            document.addPage(page);

            try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                // Header
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 18);
                contentStream.newLineAtOffset(180, 750);
                contentStream.showText("POPULAR ITEMS REPORT");
                contentStream.endText();

                // Date range
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA, 12);
                contentStream.newLineAtOffset(100, 720);
                contentStream.showText("Period: " + fromDate + " to " + toDate);
                contentStream.endText();

                // Table headers
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 12);
                contentStream.newLineAtOffset(100, 680);
                contentStream.showText("Item Name");
                contentStream.newLineAtOffset(200, 0);
                contentStream.showText("Qty Sold");
                contentStream.newLineAtOffset(100, 0);
                contentStream.showText("Revenue");
                contentStream.endText();

                // Line separator
                contentStream.moveTo(100, 675);
                contentStream.lineTo(500, 675);
                contentStream.stroke();

                // Data rows
                float y = 650;
                double totalRevenue = 0;
                int totalQuantity = 0;

                for (PopularItemReport item : popularItems) {
                    contentStream.beginText();
                    contentStream.setFont(PDType1Font.HELVETICA, 10);
                    contentStream.newLineAtOffset(100, y);
                    contentStream.showText(item.getItemName());
                    contentStream.newLineAtOffset(200, 0);
                    contentStream.showText(String.valueOf(item.getQuantitySold()));
                    contentStream.newLineAtOffset(100, 0);
                    contentStream.showText(String.format("$%.2f", item.getRevenue()));
                    contentStream.endText();

                    totalRevenue += item.getRevenue();
                    totalQuantity += item.getQuantitySold();
                    y -= 20;
                }

                // Summary
                contentStream.moveTo(100, y - 10);
                contentStream.lineTo(500, y - 10);
                contentStream.stroke();

                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 12);
                contentStream.newLineAtOffset(100, y - 30);
                contentStream.showText("TOTAL:");
                contentStream.newLineAtOffset(200, 0);
                contentStream.showText(String.valueOf(totalQuantity));
                contentStream.newLineAtOffset(100, 0);
                contentStream.showText(String.format("$%.2f", totalRevenue));
                contentStream.endText();

                // Footer
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_OBLIQUE, 10);
                contentStream.newLineAtOffset(200, 50);
                contentStream.showText("Generated on " + java.time.LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                contentStream.endText();
            }

            document.save(tempFile);
        }

        return tempFile;
    }

    private static File generateComprehensiveReportPDF(List<DailySalesReport> dailyReports, List<PopularItemReport> popularItems,
                                                     String fromDate, String toDate, String totalSales, String totalOrders, String averageOrder) throws Exception {
        File tempFile = File.createTempFile("comprehensive_report_", ".pdf");

        try (PDDocument document = new PDDocument()) {
            PDPage page = new PDPage();
            document.addPage(page);

            try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                // Header
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 20);
                contentStream.newLineAtOffset(150, 750);
                contentStream.showText("COMPREHENSIVE SALES REPORT");
                contentStream.endText();

                // Date range
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA, 12);
                contentStream.newLineAtOffset(100, 720);
                contentStream.showText("Period: " + fromDate + " to " + toDate);
                contentStream.endText();

                // Summary section
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 16);
                contentStream.newLineAtOffset(100, 690);
                contentStream.showText("SUMMARY");
                contentStream.endText();

                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA, 12);
                contentStream.newLineAtOffset(100, 670);
                contentStream.showText("Total Sales: " + totalSales);
                contentStream.newLineAtOffset(0, -15);
                contentStream.showText("Total Orders: " + totalOrders);
                contentStream.newLineAtOffset(0, -15);
                contentStream.showText("Average Order: " + averageOrder);
                contentStream.endText();

                // Daily Sales section
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 14);
                contentStream.newLineAtOffset(100, 620);
                contentStream.showText("DAILY SALES BREAKDOWN");
                contentStream.endText();

                // Daily sales table headers
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 10);
                contentStream.newLineAtOffset(100, 600);
                contentStream.showText("Date");
                contentStream.newLineAtOffset(80, 0);
                contentStream.showText("Orders");
                contentStream.newLineAtOffset(60, 0);
                contentStream.showText("Sales");
                contentStream.newLineAtOffset(80, 0);
                contentStream.showText("Avg Order");
                contentStream.endText();

                // Daily sales data
                float y = 580;
                for (int i = 0; i < Math.min(dailyReports.size(), 10); i++) {
                    DailySalesReport report = dailyReports.get(i);
                    contentStream.beginText();
                    contentStream.setFont(PDType1Font.HELVETICA, 9);
                    contentStream.newLineAtOffset(100, y);
                    contentStream.showText(report.getDate());
                    contentStream.newLineAtOffset(80, 0);
                    contentStream.showText(String.valueOf(report.getOrderCount()));
                    contentStream.newLineAtOffset(60, 0);
                    contentStream.showText(String.format("$%.2f", report.getTotalSales()));
                    contentStream.newLineAtOffset(80, 0);
                    contentStream.showText(String.format("$%.2f", report.getAverageOrder()));
                    contentStream.endText();
                    y -= 12;
                }

                // Popular Items section
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 14);
                contentStream.newLineAtOffset(100, y - 20);
                contentStream.showText("TOP SELLING ITEMS");
                contentStream.endText();

                // Popular items table headers
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 10);
                contentStream.newLineAtOffset(100, y - 40);
                contentStream.showText("Item Name");
                contentStream.newLineAtOffset(150, 0);
                contentStream.showText("Qty Sold");
                contentStream.newLineAtOffset(80, 0);
                contentStream.showText("Revenue");
                contentStream.endText();

                // Popular items data
                y -= 60;
                for (int i = 0; i < Math.min(popularItems.size(), 10); i++) {
                    PopularItemReport item = popularItems.get(i);
                    contentStream.beginText();
                    contentStream.setFont(PDType1Font.HELVETICA, 9);
                    contentStream.newLineAtOffset(100, y);
                    contentStream.showText(item.getItemName().length() > 20 ?
                                         item.getItemName().substring(0, 20) + "..." : item.getItemName());
                    contentStream.newLineAtOffset(150, 0);
                    contentStream.showText(String.valueOf(item.getQuantitySold()));
                    contentStream.newLineAtOffset(80, 0);
                    contentStream.showText(String.format("$%.2f", item.getRevenue()));
                    contentStream.endText();
                    y -= 12;
                }

                // Footer
                contentStream.beginText();
                contentStream.setFont(PDType1Font.HELVETICA_OBLIQUE, 10);
                contentStream.newLineAtOffset(200, 50);
                contentStream.showText("Generated on " + java.time.LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                contentStream.endText();
            }

            document.save(tempFile);
        }

        return tempFile;
    }
}