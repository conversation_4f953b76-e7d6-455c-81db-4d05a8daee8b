package com.restaurant.model;

public class OrderItem {
    private int id;
    private MenuItem menuItem;
    private int quantity;
    private double price;
    
    public OrderItem(MenuItem menuItem, int quantity) {
        this.menuItem = menuItem;
        this.quantity = quantity;
        this.price = (menuItem != null) ? menuItem.getPrice() : 0.0;
    }

    public OrderItem(int id, MenuItem menuItem, int quantity, double price) {
        this.id = id;
        this.menuItem = menuItem;
        this.quantity = quantity;
        this.price = price;
    }

    public double getTotalPrice() {
        return price * quantity;
    }

    // Getters and setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public MenuItem getMenuItem() { return menuItem; }
    public void setMenuItem(MenuItem menuItem) { this.menuItem = menuItem; }

    public int getQuantity() { return quantity; }
    public void setQuantity(int quantity) { this.quantity = quantity; }

    public double getPrice() { return price; }
    public void setPrice(double price) { this.price = price; }
}