package com.restaurant;

import javafx.application.Application;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

/**
 * Minimal test application to verify JavaFX is working
 * This bypasses all complex components to test basic functionality
 */
public class MinimalTestApp extends Application {

    @Override
    public void start(Stage primaryStage) {
        System.out.println("MinimalTestApp: Starting JavaFX application...");
        
        try {
            // Create simple UI components
            Label titleLabel = new Label("Restaurant App - Minimal Test");
            titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold;");
            
            Label statusLabel = new Label("✅ JavaFX is working!");
            statusLabel.setStyle("-fx-text-fill: green; -fx-font-size: 14px;");
            
            Button testButton = new Button("Test Button");
            testButton.setOnAction(e -> {
                System.out.println("Button clicked - JavaFX events working!");
                statusLabel.setText("✅ Button clicked - Events working!");
            });
            
            Button exitButton = new Button("Exit Application");
            exitButton.setOnAction(e -> {
                System.out.println("Exiting minimal test app...");
                primaryStage.close();
            });
            
            // Create layout
            VBox root = new VBox(20);
            root.setStyle("-fx-padding: 50px; -fx-alignment: center;");
            root.getChildren().addAll(titleLabel, statusLabel, testButton, exitButton);
            
            // Create scene
            Scene scene = new Scene(root, 400, 300);
            
            // Setup stage
            primaryStage.setTitle("Restaurant App - Minimal Test");
            primaryStage.setScene(scene);
            primaryStage.setOnCloseRequest(e -> {
                System.out.println("Window closed - Application shutting down normally");
            });
            
            System.out.println("MinimalTestApp: Showing stage...");
            primaryStage.show();
            System.out.println("MinimalTestApp: Stage shown successfully!");
            
        } catch (Exception e) {
            System.err.println("ERROR in MinimalTestApp: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        System.out.println("MinimalTestApp: Starting main method...");
        System.out.println("MinimalTestApp: Java version: " + System.getProperty("java.version"));
        System.out.println("MinimalTestApp: JavaFX version: " + System.getProperty("javafx.version"));
        
        try {
            System.out.println("MinimalTestApp: Launching JavaFX application...");
            launch(args);
            System.out.println("MinimalTestApp: Application finished normally");
        } catch (Exception e) {
            System.err.println("FATAL ERROR in MinimalTestApp main: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
}
