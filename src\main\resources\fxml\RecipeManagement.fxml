<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.RecipeManagementController">
   <top>
      <VBox styleClass="header-section">
         <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="page-header">
            <Button fx:id="backButton" onAction="#goBack" styleClass="back-button" text="← Back" />
            <Label styleClass="page-title" text="🍳 Recipe Management" />
            <Label styleClass="page-subtitle" text="Multi-Stage Recipe Conversion System" />
         </HBox>
      </VBox>
   </top>
   
   <center>
      <ScrollPane fitToWidth="true" styleClass="content-scroll">
         <VBox spacing="20.0" styleClass="main-content">
            <padding>
               <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
            </padding>
            
            <!-- Recipe Cards Section -->
            <HBox spacing="20.0" styleClass="recipe-cards-container">
               <!-- Margherita Pizza Recipe Card -->
               <VBox styleClass="recipe-card">
                  <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="recipe-card-header">
                     <Label styleClass="recipe-card-title" text="Margherita Pizza" />
                     <Button fx:id="addMargheritaBtn" onAction="#addNewMargheritaIngredient" styleClass="add-ingredient-button" text="+ Add New" />
                  </HBox>
                  
                  <!-- Ingredients Header -->
                  <HBox styleClass="ingredients-header">
                     <Label styleClass="ingredient-column-header" text="Material" HBox.hgrow="ALWAYS" />
                     <Label styleClass="ingredient-column-header" text="Qty" />
                     <Label styleClass="ingredient-column-header" text="Unit" />
                  </HBox>
                  
                  <!-- Ingredients List -->
                  <VBox fx:id="margheritaIngredientsContainer" styleClass="ingredients-container">
                     <!-- Ingredients will be added dynamically -->
                  </VBox>
               </VBox>
               
               <!-- Tomato Sauce Recipe Card -->
               <VBox styleClass="recipe-card">
                  <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="recipe-card-header">
                     <Label styleClass="recipe-card-title" text="Tomato Sauce" />
                     <Button fx:id="addTomatoBtn" onAction="#addNewTomatoIngredient" styleClass="add-ingredient-button" text="+ Add New" />
                  </HBox>
                  
                  <!-- Ingredients Header -->
                  <HBox styleClass="ingredients-header">
                     <Label styleClass="ingredient-column-header" text="Material" HBox.hgrow="ALWAYS" />
                     <Label styleClass="ingredient-column-header" text="Qty" />
                     <Label styleClass="ingredient-column-header" text="Unit" />
                  </HBox>
                  
                  <!-- Ingredients List -->
                  <VBox fx:id="tomatoIngredientsContainer" styleClass="ingredients-container">
                     <!-- Ingredients will be added dynamically -->
                  </VBox>
                  
                  <!-- Convert Button -->
                  <HBox alignment="CENTER_RIGHT" styleClass="convert-button-container">
                     <Button fx:id="convertTomatoBtn" onAction="#convertTomatoSauce" styleClass="convert-button" text="Convert" />
                  </HBox>
               </VBox>
            </HBox>
            
            <!-- Multi-Stage Recipe Section -->
            <VBox styleClass="multi-stage-section">
               <Label styleClass="section-title" text="Multi-Stage Recipe" />
               
               <!-- Multi-Stage Table -->
               <VBox styleClass="multi-stage-table">
                  <!-- Table Header -->
                  <HBox styleClass="multi-stage-header">
                     <Label styleClass="multi-stage-column-header" text="Conversion Name" HBox.hgrow="ALWAYS" />
                     <Label styleClass="multi-stage-column-header" text="Auto Convert" />
                     <Label styleClass="multi-stage-column-header" text="Action" />
                  </HBox>
                  
                  <!-- Table Content -->
                  <VBox fx:id="multiStageContainer" styleClass="multi-stage-content">
                     <!-- Multi-stage items will be added dynamically -->
                  </VBox>
               </VBox>
            </VBox>
         </VBox>
      </ScrollPane>
   </center>
</BorderPane>
