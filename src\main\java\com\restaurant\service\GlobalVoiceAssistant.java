package com.restaurant.service;

import com.restaurant.model.VoiceCommand;
import com.restaurant.model.VoiceIntent;
import com.restaurant.controller.DashboardController;

import java.util.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * Global AI Voice Assistant for Restaurant Management System
 * Provides intelligent voice control across all modules with advanced NLP
 */
public class GlobalVoiceAssistant {
    
    private static GlobalVoiceAssistant instance;
    private Map<String, VoiceIntentHandler> intentHandlers;
    private Map<String, List<String>> commandPatterns;
    private VoiceAssistantCallback callback;
    private boolean isListening = false;
    private String currentContext = "DASHBOARD";
    
    // Intent Categories
    public enum IntentCategory {
        FORECASTING, BILLING, TABLE_MANAGEMENT, MENU_SEARCH, 
        REPORTS, INVENTORY, NAVIGATION, SYSTEM_CONTROL
    }
    
    public interface VoiceAssistantCallback {
        void onCommandExecuted(VoiceIntent intent, String result);
        void onCommandFailed(String error);
        void onListeningStateChanged(boolean listening);
        void onContextChanged(String newContext);
    }
    
    public interface VoiceIntentHandler {
        String execute(VoiceIntent intent);
    }
    
    private GlobalVoiceAssistant() {
        initializeIntentHandlers();
        initializeCommandPatterns();
    }
    
    public static GlobalVoiceAssistant getInstance() {
        if (instance == null) {
            instance = new GlobalVoiceAssistant();
        }
        return instance;
    }
    
    private void initializeIntentHandlers() {
        intentHandlers = new HashMap<>();
        
        // Forecasting Intent Handler
        intentHandlers.put("FORECASTING", intent -> {
            String period = intent.getEntity("period", "30 days");
            String category = intent.getEntity("category", "all items");
            String channel = intent.getEntity("channel", "all channels");
            
            if (intent.getAction().equals("GENERATE_FORECAST")) {
                return String.format("Generating sales forecast for %s - %s via %s", category, period, channel);
            } else if (intent.getAction().equals("SHOW_PAST_FORECAST")) {
                return String.format("Displaying past forecast for %s", period);
            }
            return "Forecast command executed";
        });
        
        // Billing Intent Handler
        intentHandlers.put("BILLING", intent -> {
            String tableNumber = intent.getEntity("table", "unknown");
            String item = intent.getEntity("item", "");
            String quantity = intent.getEntity("quantity", "1");
            
            switch (intent.getAction()) {
                case "ADD_ITEM":
                    return String.format("Added %s %s to table %s", quantity, item, tableNumber);
                case "REMOVE_ITEM":
                    return String.format("Removed %s from table %s", item, tableNumber);
                case "SHOW_BILL":
                    return String.format("Displaying bill for table %s", tableNumber);
                case "PLACE_ORDER":
                    return String.format("Placing order for table %s", tableNumber);
                default:
                    return "Billing command executed";
            }
        });
        
        // Table Management Intent Handler
        intentHandlers.put("TABLE_MANAGEMENT", intent -> {
            String tableNumber = intent.getEntity("table", "unknown");
            String secondTable = intent.getEntity("second_table", "");
            
            switch (intent.getAction()) {
                case "OPEN_TABLE":
                    return String.format("Opening table %s interface", tableNumber);
                case "CLOSE_TABLE":
                    return String.format("Closing table %s", tableNumber);
                case "MERGE_TABLES":
                    return String.format("Merging table %s with table %s", tableNumber, secondTable);
                case "SHOW_TABLE_STATUS":
                    return String.format("Showing status for table %s", tableNumber);
                default:
                    return "Table management command executed";
            }
        });
        
        // Menu Search Intent Handler
        intentHandlers.put("MENU_SEARCH", intent -> {
            String searchTerm = intent.getEntity("search_term", "");
            String category = intent.getEntity("category", "");
            String priceRange = intent.getEntity("price_range", "");
            
            switch (intent.getAction()) {
                case "SEARCH_ITEM":
                    return String.format("Searching menu for: %s", searchTerm);
                case "FILTER_BY_CATEGORY":
                    return String.format("Showing %s items", category);
                case "FILTER_BY_PRICE":
                    return String.format("Showing items %s", priceRange);
                default:
                    return "Menu search executed";
            }
        });
        
        // Reports Intent Handler
        intentHandlers.put("REPORTS", intent -> {
            String reportType = intent.getEntity("report_type", "sales");
            String period = intent.getEntity("period", "today");
            String comparison = intent.getEntity("comparison", "");
            
            switch (intent.getAction()) {
                case "SHOW_SALES_REPORT":
                    return String.format("Displaying %s sales report for %s", reportType, period);
                case "COMPARE_PERIODS":
                    return String.format("Comparing %s", comparison);
                case "EXPORT_REPORT":
                    return String.format("Exporting %s report", reportType);
                default:
                    return "Report command executed";
            }
        });
        
        // Inventory Intent Handler
        intentHandlers.put("INVENTORY", intent -> {
            String item = intent.getEntity("item", "");
            String quantity = intent.getEntity("quantity", "");
            String unit = intent.getEntity("unit", "");
            
            switch (intent.getAction()) {
                case "CHECK_STOCK":
                    return String.format("Checking stock for %s", item);
                case "UPDATE_STOCK":
                    return String.format("Updating %s stock to %s %s", item, quantity, unit);
                case "SHOW_LOW_STOCK":
                    return "Displaying low stock items";
                default:
                    return "Inventory command executed";
            }
        });
        
        // Navigation Intent Handler
        intentHandlers.put("NAVIGATION", intent -> {
            String destination = intent.getEntity("destination", "dashboard");
            
            switch (intent.getAction()) {
                case "NAVIGATE_TO":
                    setContext(destination.toUpperCase());
                    return String.format("Navigating to %s", destination);
                case "GO_BACK":
                    return "Going back to previous screen";
                case "RESET_FILTERS":
                    return "Resetting all filters";
                case "LOGOUT":
                    return "Logging out of system";
                default:
                    return "Navigation command executed";
            }
        });
        
        // System Control Intent Handler
        intentHandlers.put("SYSTEM_CONTROL", intent -> {
            String action = intent.getAction();
            
            switch (action) {
                case "DOWNLOAD_REPORT":
                    return "Downloading report";
                case "REFRESH_DATA":
                    return "Refreshing data";
                case "SAVE_CHANGES":
                    return "Saving changes";
                case "CANCEL_OPERATION":
                    return "Cancelling current operation";
                default:
                    return "System command executed";
            }
        });
    }
    
    private void initializeCommandPatterns() {
        commandPatterns = new HashMap<>();
        
        // Forecasting Patterns
        commandPatterns.put("FORECASTING", Arrays.asList(
            "forecast.*?(?:for|next)\\s+(\\d+)\\s+(days?|weeks?|months?)",
            "predict.*?sales.*?(?:for\\s+)?(\\w+).*?(?:next\\s+)?(\\d+)\\s+(days?|weeks?|months?)",
            "show.*?(?:last|previous)\\s+(\\w+).*?forecast",
            "generate.*?forecast.*?(?:for\\s+)?(\\w+)",
            "forecast.*?(?:for\\s+)?(biryani|chicken|paneer|beverages?|desserts?|appetizers?)"
        ));
        
        // Billing Patterns
        commandPatterns.put("BILLING", Arrays.asList(
            "add\\s+(\\w+)\\s+(\\w+.*?)\\s+to\\s+table\\s+(\\d+)",
            "place\\s+order\\s+(?:for\\s+)?table\\s+(\\d+)",
            "remove\\s+(\\w+.*?)\\s+from\\s+table\\s+(\\d+)",
            "show\\s+(?:table\\s+)?(\\d+)\\s+bill",
            "bill\\s+(?:for\\s+)?table\\s+(\\d+)"
        ));
        
        // Table Management Patterns
        commandPatterns.put("TABLE_MANAGEMENT", Arrays.asList(
            "open\\s+table\\s+(\\d+)",
            "close\\s+table\\s+(\\d+)",
            "merge\\s+table\\s+(\\d+)\\s+(?:and|with)\\s+(?:table\\s+)?(\\d+)",
            "show\\s+table\\s+(\\d+)\\s+status",
            "table\\s+(\\d+)\\s+(?:status|info|details)"
        ));
        
        // Menu Search Patterns
        commandPatterns.put("MENU_SEARCH", Arrays.asList(
            "search\\s+(\\w+.*?)(?:\\s+in\\s+menu)?",
            "show\\s+(vegetarian|vegan|non.?vegetarian)\\s+items",
            "search\\s+(\\w+.*?)\\s+under\\s+(\\d+)\\s+rupees?",
            "find\\s+(\\w+.*?)\\s+(?:in\\s+)?menu",
            "show\\s+(?:all\\s+)?(beverages?|appetizers?|main\\s+course|desserts?)"
        ));
        
        // Reports Patterns
        commandPatterns.put("REPORTS", Arrays.asList(
            "show\\s+(?:today's|todays)\\s+sales",
            "open\\s+(\\w+.?wise)\\s+report",
            "compare\\s+(last\\s+\\w+)\\s+(?:vs|with)\\s+(this\\s+\\w+)",
            "show\\s+(\\w+)\\s+report\\s+(?:for\\s+)?(\\w+)",
            "display\\s+sales\\s+(?:for\\s+)?(today|yesterday|this\\s+week|last\\s+week)"
        ));
        
        // Inventory Patterns
        commandPatterns.put("INVENTORY", Arrays.asList(
            "check\\s+stock\\s+(?:for\\s+)?(\\w+)",
            "show\\s+low\\s+stock\\s+items",
            "update\\s+stock\\s+(?:for\\s+)?(\\w+)\\s+to\\s+(\\d+)\\s+(\\w+)",
            "stock\\s+(?:level\\s+)?(?:for\\s+)?(\\w+)",
            "inventory\\s+(?:for\\s+)?(\\w+)"
        ));
        
        // Navigation Patterns
        commandPatterns.put("NAVIGATION", Arrays.asList(
            "go\\s+to\\s+(\\w+)",
            "open\\s+(\\w+)",
            "navigate\\s+to\\s+(\\w+)",
            "show\\s+(dashboard|reports|billing|menu|tables)",
            "reset\\s+(?:all\\s+)?filters?",
            "log\\s?out"
        ));
        
        // System Control Patterns
        commandPatterns.put("SYSTEM_CONTROL", Arrays.asList(
            "download\\s+(\\w+)\\s+report",
            "refresh\\s+data",
            "save\\s+changes",
            "cancel\\s+(?:current\\s+)?operation",
            "export\\s+(\\w+)"
        ));
    }
    
    public VoiceIntent processVoiceCommand(String input) {
        if (input == null || input.trim().isEmpty()) {
            return new VoiceIntent("UNKNOWN", "INVALID", "Empty command", new HashMap<>(), 0.0);
        }
        
        String normalizedInput = input.toLowerCase().trim();
        
        // Try to match against all intent patterns
        for (Map.Entry<String, List<String>> entry : commandPatterns.entrySet()) {
            String intentCategory = entry.getKey();
            List<String> patterns = entry.getValue();
            
            for (String pattern : patterns) {
                Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                Matcher m = p.matcher(normalizedInput);
                
                if (m.find()) {
                    VoiceIntent intent = extractIntent(intentCategory, normalizedInput, m);
                    if (intent.getConfidence() > 0.7) {
                        return intent;
                    }
                }
            }
        }
        
        // Fallback: Try fuzzy matching
        return attemptFuzzyMatching(normalizedInput);
    }
    
    private VoiceIntent extractIntent(String category, String input, Matcher matcher) {
        Map<String, String> entities = new HashMap<>();
        String action = determineAction(category, input);
        
        // Extract entities based on category
        switch (category) {
            case "FORECASTING":
                extractForecastingEntities(entities, input, matcher);
                break;
            case "BILLING":
                extractBillingEntities(entities, input, matcher);
                break;
            case "TABLE_MANAGEMENT":
                extractTableEntities(entities, input, matcher);
                break;
            case "MENU_SEARCH":
                extractMenuEntities(entities, input, matcher);
                break;
            case "REPORTS":
                extractReportEntities(entities, input, matcher);
                break;
            case "INVENTORY":
                extractInventoryEntities(entities, input, matcher);
                break;
            case "NAVIGATION":
                extractNavigationEntities(entities, input, matcher);
                break;
            case "SYSTEM_CONTROL":
                extractSystemEntities(entities, input, matcher);
                break;
        }
        
        double confidence = calculateConfidence(category, action, entities, input);
        String description = generateDescription(category, action, entities);
        
        return new VoiceIntent(category, action, description, entities, confidence);
    }
    
    private String determineAction(String category, String input) {
        switch (category) {
            case "FORECASTING":
                if (input.contains("show") && (input.contains("last") || input.contains("previous"))) {
                    return "SHOW_PAST_FORECAST";
                }
                return "GENERATE_FORECAST";
                
            case "BILLING":
                if (input.contains("add")) return "ADD_ITEM";
                if (input.contains("remove")) return "REMOVE_ITEM";
                if (input.contains("show") || input.contains("bill")) return "SHOW_BILL";
                if (input.contains("place") && input.contains("order")) return "PLACE_ORDER";
                return "BILLING_ACTION";
                
            case "TABLE_MANAGEMENT":
                if (input.contains("open")) return "OPEN_TABLE";
                if (input.contains("close")) return "CLOSE_TABLE";
                if (input.contains("merge")) return "MERGE_TABLES";
                if (input.contains("status")) return "SHOW_TABLE_STATUS";
                return "TABLE_ACTION";
                
            case "MENU_SEARCH":
                if (input.contains("vegetarian") || input.contains("vegan")) return "FILTER_BY_CATEGORY";
                if (input.contains("under") && input.contains("rupees")) return "FILTER_BY_PRICE";
                return "SEARCH_ITEM";
                
            case "REPORTS":
                if (input.contains("compare")) return "COMPARE_PERIODS";
                if (input.contains("export") || input.contains("download")) return "EXPORT_REPORT";
                return "SHOW_SALES_REPORT";
                
            case "INVENTORY":
                if (input.contains("update")) return "UPDATE_STOCK";
                if (input.contains("low stock")) return "SHOW_LOW_STOCK";
                return "CHECK_STOCK";
                
            case "NAVIGATION":
                if (input.contains("reset")) return "RESET_FILTERS";
                if (input.contains("log") && input.contains("out")) return "LOGOUT";
                if (input.contains("back")) return "GO_BACK";
                return "NAVIGATE_TO";
                
            case "SYSTEM_CONTROL":
                if (input.contains("download")) return "DOWNLOAD_REPORT";
                if (input.contains("refresh")) return "REFRESH_DATA";
                if (input.contains("save")) return "SAVE_CHANGES";
                if (input.contains("cancel")) return "CANCEL_OPERATION";
                return "SYSTEM_ACTION";
                
            default:
                return "UNKNOWN_ACTION";
        }
    }
    
    public String executeVoiceIntent(VoiceIntent intent) {
        try {
            VoiceIntentHandler handler = intentHandlers.get(intent.getCategory());
            if (handler != null) {
                String result = handler.execute(intent);
                if (callback != null) {
                    callback.onCommandExecuted(intent, result);
                }
                return result;
            } else {
                String error = "No handler found for intent: " + intent.getCategory();
                if (callback != null) {
                    callback.onCommandFailed(error);
                }
                return error;
            }
        } catch (Exception e) {
            String error = "Error executing command: " + e.getMessage();
            if (callback != null) {
                callback.onCommandFailed(error);
            }
            return error;
        }
    }
    
    // Getters and Setters
    public void setCallback(VoiceAssistantCallback callback) {
        this.callback = callback;
    }
    
    public void setContext(String context) {
        this.currentContext = context;
        if (callback != null) {
            callback.onContextChanged(context);
        }
    }
    
    public String getCurrentContext() {
        return currentContext;
    }
    
    public boolean isListening() {
        return isListening;
    }
    
    public void setListening(boolean listening) {
        this.isListening = listening;
        if (callback != null) {
            callback.onListeningStateChanged(listening);
        }
    }

    // Helper Methods for Entity Extraction
    private void extractForecastingEntities(Map<String, String> entities, String input, Matcher matcher) {
        // Extract time period
        if (matcher.groupCount() >= 2) {
            String number = matcher.group(1);
            String unit = matcher.group(2);
            entities.put("period", number + " " + unit);
        }

        // Extract category
        if (input.contains("biryani")) entities.put("category", "biryani");
        else if (input.contains("chicken")) entities.put("category", "chicken");
        else if (input.contains("paneer")) entities.put("category", "paneer");
        else if (input.contains("beverages")) entities.put("category", "beverages");
        else if (input.contains("desserts")) entities.put("category", "desserts");
        else if (input.contains("appetizers")) entities.put("category", "appetizers");

        // Extract channel
        if (input.contains("dine")) entities.put("channel", "dine-in");
        else if (input.contains("delivery")) entities.put("channel", "delivery");
        else if (input.contains("takeaway")) entities.put("channel", "takeaway");
    }

    private void extractBillingEntities(Map<String, String> entities, String input, Matcher matcher) {
        if (matcher.groupCount() >= 1) {
            if (input.contains("add") && matcher.groupCount() >= 3) {
                entities.put("quantity", matcher.group(1));
                entities.put("item", matcher.group(2));
                entities.put("table", matcher.group(3));
            } else if (input.contains("remove") && matcher.groupCount() >= 2) {
                entities.put("item", matcher.group(1));
                entities.put("table", matcher.group(2));
            } else if (input.contains("table")) {
                entities.put("table", matcher.group(1));
            }
        }
    }

    private void extractTableEntities(Map<String, String> entities, String input, Matcher matcher) {
        if (matcher.groupCount() >= 1) {
            entities.put("table", matcher.group(1));
            if (matcher.groupCount() >= 2) {
                entities.put("second_table", matcher.group(2));
            }
        }
    }

    private void extractMenuEntities(Map<String, String> entities, String input, Matcher matcher) {
        if (matcher.groupCount() >= 1) {
            if (input.contains("vegetarian")) {
                entities.put("category", "vegetarian");
            } else if (input.contains("under") && input.contains("rupees")) {
                entities.put("search_term", matcher.group(1));
                if (matcher.groupCount() >= 2) {
                    entities.put("price_range", "under " + matcher.group(2) + " rupees");
                }
            } else {
                entities.put("search_term", matcher.group(1));
            }
        }
    }

    private void extractReportEntities(Map<String, String> entities, String input, Matcher matcher) {
        if (input.contains("today")) entities.put("period", "today");
        else if (input.contains("yesterday")) entities.put("period", "yesterday");
        else if (input.contains("this week")) entities.put("period", "this week");
        else if (input.contains("last week")) entities.put("period", "last week");

        if (input.contains("item-wise")) entities.put("report_type", "item-wise");
        else if (input.contains("sales")) entities.put("report_type", "sales");

        if (input.contains("compare") && matcher.groupCount() >= 2) {
            entities.put("comparison", matcher.group(1) + " vs " + matcher.group(2));
        }
    }

    private void extractInventoryEntities(Map<String, String> entities, String input, Matcher matcher) {
        if (matcher.groupCount() >= 1) {
            if (input.contains("update") && matcher.groupCount() >= 3) {
                entities.put("item", matcher.group(1));
                entities.put("quantity", matcher.group(2));
                entities.put("unit", matcher.group(3));
            } else {
                entities.put("item", matcher.group(1));
            }
        }
    }

    private void extractNavigationEntities(Map<String, String> entities, String input, Matcher matcher) {
        if (matcher.groupCount() >= 1) {
            entities.put("destination", matcher.group(1));
        }
    }

    private void extractSystemEntities(Map<String, String> entities, String input, Matcher matcher) {
        if (matcher.groupCount() >= 1) {
            entities.put("target", matcher.group(1));
        }
    }

    private double calculateConfidence(String category, String action, Map<String, String> entities, String input) {
        double confidence = 0.5; // Base confidence

        // Boost confidence based on exact matches
        if (!action.equals("UNKNOWN_ACTION")) confidence += 0.2;
        if (!entities.isEmpty()) confidence += 0.1;

        // Category-specific confidence boosts
        switch (category) {
            case "FORECASTING":
                if (input.contains("forecast") || input.contains("predict")) confidence += 0.2;
                break;
            case "BILLING":
                if (input.contains("table") && (input.contains("add") || input.contains("bill"))) confidence += 0.2;
                break;
            case "TABLE_MANAGEMENT":
                if (input.contains("table") && (input.contains("open") || input.contains("close"))) confidence += 0.2;
                break;
        }

        return Math.min(confidence, 1.0);
    }

    private String generateDescription(String category, String action, Map<String, String> entities) {
        StringBuilder desc = new StringBuilder();
        desc.append(category.toLowerCase().replace("_", " ")).append(": ");
        desc.append(action.toLowerCase().replace("_", " "));

        if (!entities.isEmpty()) {
            desc.append(" (");
            entities.forEach((key, value) -> desc.append(key).append(": ").append(value).append(", "));
            desc.setLength(desc.length() - 2); // Remove last comma
            desc.append(")");
        }

        return desc.toString();
    }

    private VoiceIntent attemptFuzzyMatching(String input) {
        // Simple fuzzy matching based on keywords
        Map<String, Integer> categoryScores = new HashMap<>();

        // Score each category based on keyword presence
        if (containsAny(input, Arrays.asList("forecast", "predict", "sales", "growth"))) {
            categoryScores.put("FORECASTING", categoryScores.getOrDefault("FORECASTING", 0) + 3);
        }

        if (containsAny(input, Arrays.asList("table", "bill", "order", "add", "remove"))) {
            categoryScores.put("BILLING", categoryScores.getOrDefault("BILLING", 0) + 3);
        }

        if (containsAny(input, Arrays.asList("search", "menu", "find", "show"))) {
            categoryScores.put("MENU_SEARCH", categoryScores.getOrDefault("MENU_SEARCH", 0) + 2);
        }

        if (containsAny(input, Arrays.asList("report", "analytics", "compare", "sales"))) {
            categoryScores.put("REPORTS", categoryScores.getOrDefault("REPORTS", 0) + 2);
        }

        if (containsAny(input, Arrays.asList("stock", "inventory", "check", "update"))) {
            categoryScores.put("INVENTORY", categoryScores.getOrDefault("INVENTORY", 0) + 2);
        }

        if (containsAny(input, Arrays.asList("go", "navigate", "open", "show"))) {
            categoryScores.put("NAVIGATION", categoryScores.getOrDefault("NAVIGATION", 0) + 1);
        }

        // Find best matching category
        String bestCategory = categoryScores.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse("UNKNOWN");

        if (categoryScores.getOrDefault(bestCategory, 0) >= 2) {
            Map<String, String> entities = new HashMap<>();
            entities.put("original_input", input);

            return new VoiceIntent(bestCategory, "FUZZY_MATCH",
                "Fuzzy match: " + bestCategory.toLowerCase(), entities, 0.6);
        }

        return new VoiceIntent("UNKNOWN", "UNRECOGNIZED",
            "Command not recognized: " + input, new HashMap<>(), 0.0);
    }

    private boolean containsAny(String text, List<String> keywords) {
        return keywords.stream().anyMatch(text::contains);
    }

    // Context-aware suggestions
    public List<String> getContextualSuggestions() {
        List<String> suggestions = new ArrayList<>();

        switch (currentContext) {
            case "DASHBOARD":
                suggestions.addAll(Arrays.asList(
                    "Show today's sales",
                    "Open AI forecaster",
                    "Go to table management",
                    "Check low stock items"
                ));
                break;
            case "FORECASTING":
                suggestions.addAll(Arrays.asList(
                    "Forecast sales for next 30 days",
                    "Predict biryani sales next week",
                    "Show last month's forecast"
                ));
                break;
            case "BILLING":
                suggestions.addAll(Arrays.asList(
                    "Add two butter naan to table 5",
                    "Show table 3 bill",
                    "Place order for table 2"
                ));
                break;
            case "TABLES":
                suggestions.addAll(Arrays.asList(
                    "Open table 6",
                    "Close table 2",
                    "Merge table 3 and 4"
                ));
                break;
            default:
                suggestions.addAll(Arrays.asList(
                    "Go to dashboard",
                    "Show today's sales",
                    "Search chicken tikka",
                    "Check stock for paneer"
                ));
        }

        return suggestions;
    }
}
