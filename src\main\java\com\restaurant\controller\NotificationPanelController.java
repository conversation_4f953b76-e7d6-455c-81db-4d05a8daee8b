package com.restaurant.controller;

import com.restaurant.model.Notification;
import com.restaurant.model.Notification.NotificationType;
import com.restaurant.model.Notification.Priority;
import com.restaurant.service.NotificationService;
import com.restaurant.util.ScrollPaneUtil;
import com.restaurant.component.CustomScrollPane;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.layout.*;

import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;

import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;
import java.util.Timer;
import java.util.TimerTask;

/**
 * Controller for the notification panel
 * Handles online orders, KOT alerts, billing notifications, scanner integration
 */
public class NotificationPanelController implements Initializable {

    @FXML
    private Label unreadCountLabel;
    @FXML
    private Button markAllReadBtn;
    @FXML
    private Button clearAllBtn;

    // Quick action buttons removed

    // Filter tabs
    @FXML
    private ToggleButton allTab;
    @FXML
    private ToggleButton onlineOrdersTab;
    @FXML
    private ToggleButton kotTab;
    @FXML
    private ToggleButton billingTab;
    @FXML
    private ToggleButton urgentTab;

    // Notification container
    @FXML
    private ScrollPane notificationScrollPane;
    @FXML
    private VBox notificationContainer;

    // Custom scroll pane for full control
    private CustomScrollPane customScrollPane;
    private VBox customNotificationContainer;

    // Integration panels and buttons removed

    // Status bar
    @FXML
    private Label lastUpdateLabel;
    @FXML
    private Label connectionStatusLabel;
    @FXML
    private Button refreshBtn;

    private NotificationService notificationService;
    private ToggleGroup filterToggleGroup;
    private Timer updateTimer;
    private int lastLoggedCount = -1; // Track last logged count to prevent spam

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        notificationService = NotificationService.getInstance();

        // Set up custom scroll pane first
        setupCustomScrollPane();

        setupFilterTabs();
        setupNotificationListener();
        loadNotifications();
        startUpdateTimer();

        // Configure scroll pane for proper scrolling
        configureScrollPane();

        // Initialize status
        updateUnreadCount();
        updateLastUpdateTime();
        updateTabCounts(); // Show initial tab counts

        // Add demo notifications to test scroll bar
        addDemoNotifications();

        // Refresh scroll pane after adding notifications
        refreshScrollPane();

        // Start periodic scroll bar check
        startScrollBarCheck();

        System.out.println("NotificationPanelController initialized with custom scroll pane");
    }

    private void setupCustomScrollPane() {
        Platform.runLater(() -> {
            if (notificationScrollPane != null && notificationContainer != null) {
                try {
                    // Simple and reliable scroll pane configuration
                    notificationScrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.ALWAYS);
                    notificationScrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
                    notificationScrollPane.setFitToWidth(true);
                    notificationScrollPane.setFitToHeight(false);

                    // Set fixed dimensions for reliable scrolling
                    notificationScrollPane.setPrefHeight(400);
                    notificationScrollPane.setMinHeight(400);
                    notificationScrollPane.setMaxHeight(400);

                    // Ensure container is tall enough to trigger scrolling
                    notificationContainer.setMinHeight(800);
                    notificationContainer.setPrefHeight(800);

                    // Apply styling
                    notificationScrollPane.getStyleClass().clear();
                    notificationScrollPane.getStyleClass().add("notification-scroll-functional");

                    System.out.println("Notification scroll pane configured - ScrollBar should be ALWAYS visible");
                } catch (Exception e) {
                    System.err.println("Failed to setup notification scroll pane: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        });
    }

    private void setupFilterTabs() {
        filterToggleGroup = new ToggleGroup();
        allTab.setToggleGroup(filterToggleGroup);
        onlineOrdersTab.setToggleGroup(filterToggleGroup);
        kotTab.setToggleGroup(filterToggleGroup);
        billingTab.setToggleGroup(filterToggleGroup);
        urgentTab.setToggleGroup(filterToggleGroup);

        allTab.setSelected(true);
    }

    private void setupNotificationListener() {
        notificationService.addNotificationListener(notification -> {
            Platform.runLater(() -> {
                loadNotifications();
                updateUnreadCount();
                updateLastUpdateTime();
            });
        });
    }

    private void loadNotifications() {
        // Use custom container if available, otherwise fall back to original
        VBox targetContainer = customNotificationContainer != null ? customNotificationContainer
                : notificationContainer;
        targetContainer.getChildren().clear();

        List<Notification> notifications = getFilteredNotifications();

        if (notifications.isEmpty()) {
            Label emptyLabel = new Label("No notifications");
            emptyLabel.setStyle("-fx-text-fill: #666; -fx-font-style: italic;");
            targetContainer.getChildren().add(emptyLabel);
        } else {
            for (Notification notification : notifications) {
                VBox notificationCard = createNotificationCard(notification);
                targetContainer.getChildren().add(notificationCard);
            }
        }

        // Simple refresh after loading notifications
        Platform.runLater(() -> {
            if (notificationScrollPane != null && notificationContainer != null) {
                int notificationCount = notificationContainer.getChildren().size();

                // Ensure container is always tall enough for scrolling
                double containerHeight = Math.max(1000, notificationCount * 120 + 200);
                notificationContainer.setMinHeight(containerHeight);
                notificationContainer.setPrefHeight(containerHeight);

                // Force scroll bar to always show
                notificationScrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.ALWAYS);

                // Simple layout refresh
                notificationContainer.requestLayout();
                notificationScrollPane.requestLayout();

                // Reduced logging to prevent console spam
                if (notificationCount != lastLoggedCount) {
                    System.out.println("Notifications loaded: " + notificationCount + ", Container height: " + containerHeight + "px");
                    lastLoggedCount = notificationCount;
                }
            }
        });

        // Update tab counts after loading notifications
        updateTabCounts();
    }

    private List<Notification> getFilteredNotifications() {
        ToggleButton selected = (ToggleButton) filterToggleGroup.getSelectedToggle();

        if (selected == onlineOrdersTab) {
            return notificationService.getNotificationsByType(NotificationType.ONLINE_ORDER);
        } else if (selected == kotTab) {
            return notificationService.getNotificationsByType(NotificationType.KOT_ALERT);
        } else if (selected == billingTab) {
            return notificationService.getNotificationsByType(NotificationType.BILLING);
        } else if (selected == urgentTab) {
            return notificationService.getUrgentNotifications();
        } else {
            return notificationService.getNotifications();
        }
    }

    private VBox createNotificationCard(Notification notification) {
        VBox card = new VBox(5);
        card.setPadding(new Insets(10));
        card.setStyle(getNotificationCardStyle(notification));

        // Header with icon, title, and time
        HBox header = new HBox(10);
        header.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        Label iconLabel = new Label(notification.getType().getIcon() + " " + notification.getPriority().getIcon());
        iconLabel.setFont(Font.font(16));

        Label titleLabel = new Label(notification.getTitle());
        titleLabel.setFont(Font.font("System", FontWeight.BOLD, 12));
        if (!notification.isRead()) {
            titleLabel.setStyle("-fx-text-fill: #1976D2;");
        }

        Region spacer = new Region();
        HBox.setHgrow(spacer, javafx.scene.layout.Priority.ALWAYS);

        Label timeLabel = new Label(notification.getTimeAgoText());
        timeLabel.setStyle("-fx-text-fill: #666; -fx-font-size: 10px;");

        header.getChildren().addAll(iconLabel, titleLabel, spacer, timeLabel);

        // Message
        Label messageLabel = new Label(notification.getMessage());
        messageLabel.setWrapText(true);
        messageLabel.setStyle("-fx-text-fill: #333; -fx-font-size: 11px;");

        // Source
        Label sourceLabel = new Label("Source: " + notification.getSource());
        sourceLabel.setStyle("-fx-text-fill: #666; -fx-font-size: 10px; -fx-font-style: italic;");

        card.getChildren().addAll(header, messageLabel, sourceLabel);

        // Add action buttons for actionable notifications
        if (notification.isActionable()) {
            HBox actionBox = createActionButtons(notification);
            card.getChildren().add(actionBox);
        }

        // Click to mark as read
        card.setOnMouseClicked(e -> {
            if (!notification.isRead()) {
                notificationService.markAsRead(notification.getId());
                loadNotifications();
                updateUnreadCount();
            }
        });

        return card;
    }

    private String getNotificationCardStyle(Notification notification) {
        String baseStyle = "-fx-background-color: white; -fx-border-color: #ddd; -fx-border-radius: 5; -fx-background-radius: 5; -fx-cursor: hand;";

        if (!notification.isRead()) {
            baseStyle += " -fx-border-width: 2; -fx-border-color: #2196F3;";
        }

        if (notification.getPriority() == Priority.URGENT) {
            baseStyle += " -fx-border-color: #F44336; -fx-border-width: 2;";
        } else if (notification.getPriority() == Priority.HIGH) {
            baseStyle += " -fx-border-color: #FF9800;";
        }

        return baseStyle;
    }

    private HBox createActionButtons(Notification notification) {
        HBox actionBox = new HBox(5);
        actionBox.setPadding(new Insets(5, 0, 0, 0));

        switch (notification.getType()) {
            case ONLINE_ORDER:
                Button acceptBtn = new Button("✅ Accept");
                Button rejectBtn = new Button("❌ Reject");
                acceptBtn.setStyle("-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-font-size: 10px;");
                rejectBtn.setStyle("-fx-background-color: #F44336; -fx-text-fill: white; -fx-font-size: 10px;");
                acceptBtn.setOnAction(e -> handleAcceptOrder(notification));
                rejectBtn.setOnAction(e -> handleRejectOrder(notification));
                actionBox.getChildren().addAll(acceptBtn, rejectBtn);
                break;

            case KOT_ALERT:
                Button viewKOTBtn = new Button("👁️ View KOT");
                Button printBtn = new Button("🖨️ Print");
                viewKOTBtn.setStyle("-fx-background-color: #2196F3; -fx-text-fill: white; -fx-font-size: 10px;");
                printBtn.setStyle("-fx-background-color: #FF9800; -fx-text-fill: white; -fx-font-size: 10px;");
                viewKOTBtn.setOnAction(e -> handleViewKOT(notification));
                printBtn.setOnAction(e -> handlePrintKOT(notification));
                actionBox.getChildren().addAll(viewKOTBtn, printBtn);
                break;

            case BILLING:
                Button generateBillBtn = new Button("💰 Generate Bill");
                generateBillBtn.setStyle("-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-font-size: 10px;");
                generateBillBtn.setOnAction(e -> handleGenerateBill(notification));
                actionBox.getChildren().add(generateBillBtn);
                break;

            case INVENTORY:
                Button updateStockBtn = new Button("📦 Update Stock");
                updateStockBtn.setStyle("-fx-background-color: #9C27B0; -fx-text-fill: white; -fx-font-size: 10px;");
                updateStockBtn.setOnAction(e -> handleUpdateStock(notification));
                actionBox.getChildren().add(updateStockBtn);
                break;

            case SCANNER:
                Button viewTableBtn = new Button("🍽️ View Table");
                viewTableBtn.setStyle("-fx-background-color: #607D8B; -fx-text-fill: white; -fx-font-size: 10px;");
                viewTableBtn.setOnAction(e -> handleViewTable(notification));
                actionBox.getChildren().add(viewTableBtn);
                break;

            case SYSTEM:
                Button acknowledgeBtn = new Button("✅ Acknowledge");
                acknowledgeBtn.setStyle("-fx-background-color: #795548; -fx-text-fill: white; -fx-font-size: 10px;");
                acknowledgeBtn.setOnAction(e -> handleAcknowledge(notification));
                actionBox.getChildren().add(acknowledgeBtn);
                break;

            case KITCHEN:
                Button readyBtn = new Button("✅ Ready");
                Button delayBtn = new Button("⏰ Delay");
                readyBtn.setStyle("-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-font-size: 10px;");
                delayBtn.setStyle("-fx-background-color: #FF9800; -fx-text-fill: white; -fx-font-size: 10px;");
                readyBtn.setOnAction(e -> handleKitchenReady(notification));
                delayBtn.setOnAction(e -> handleKitchenDelay(notification));
                actionBox.getChildren().addAll(readyBtn, delayBtn);
                break;

            case PAYMENT:
                Button confirmBtn = new Button("✅ Confirm");
                confirmBtn.setStyle("-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-font-size: 10px;");
                confirmBtn.setOnAction(e -> handleConfirmPayment(notification));
                actionBox.getChildren().add(confirmBtn);
                break;

            default:
                // No specific actions for other types
                break;
        }

        return actionBox;
    }

    private void handleAcceptOrder(Notification notification) {
        try {
            // Mark notification as read and non-actionable
            notification.setRead(true);
            notification.setActionable(false);

            // Show success message
            System.out.println("✅ Order accepted: " + notification.getTitle());

            // Refresh the notification display
            loadNotifications();
            updateUnreadCount();

        } catch (Exception e) {
            System.err.println("Error accepting order: " + e.getMessage());
        }
    }

    private void handleRejectOrder(Notification notification) {
        try {
            // Show confirmation dialog
            Alert confirmation = new Alert(Alert.AlertType.CONFIRMATION);
            confirmation.setTitle("Reject Order");
            confirmation.setHeaderText("Reject " + notification.getTitle());
            confirmation.setContentText("Are you sure you want to reject this order?\nThis action cannot be undone.");

            confirmation.showAndWait().ifPresent(response -> {
                if (response == ButtonType.OK) {
                    // Mark notification as read and non-actionable
                    notification.setRead(true);
                    notification.setActionable(false);

                    // Show rejection message
                    System.out.println("❌ Order rejected: " + notification.getTitle());

                    // Refresh the notification display
                    loadNotifications();
                    updateUnreadCount();
                }
            });

        } catch (Exception e) {
            System.err.println("Error rejecting order: " + e.getMessage());
        }
    }

    // KOT Alert handlers
    private void handleViewKOT(Notification notification) {
        try {
            notification.setRead(true);
            System.out.println("👁️ Viewing KOT: " + notification.getTitle());
            // Here you would open KOT details view
            loadNotifications();
            updateUnreadCount();
        } catch (Exception e) {
            System.err.println("Error viewing KOT: " + e.getMessage());
        }
    }

    private void handlePrintKOT(Notification notification) {
        try {
            notification.setRead(true);
            notification.setActionable(false);
            System.out.println("🖨️ Printing KOT: " + notification.getTitle());
            // Here you would trigger KOT printing
            loadNotifications();
            updateUnreadCount();
        } catch (Exception e) {
            System.err.println("Error printing KOT: " + e.getMessage());
        }
    }

    // Billing handlers
    private void handleGenerateBill(Notification notification) {
        try {
            notification.setRead(true);
            notification.setActionable(false);
            System.out.println("💰 Generating bill: " + notification.getTitle());
            // Here you would open billing interface
            loadNotifications();
            updateUnreadCount();
        } catch (Exception e) {
            System.err.println("Error generating bill: " + e.getMessage());
        }
    }

    // Inventory handlers
    private void handleUpdateStock(Notification notification) {
        try {
            notification.setRead(true);
            notification.setActionable(false);
            System.out.println("📦 Updating stock: " + notification.getTitle());
            // Here you would open inventory management
            loadNotifications();
            updateUnreadCount();
        } catch (Exception e) {
            System.err.println("Error updating stock: " + e.getMessage());
        }
    }

    // Scanner handlers
    private void handleViewTable(Notification notification) {
        try {
            notification.setRead(true);
            System.out.println("🍽️ Viewing table: " + notification.getTitle());
            // Here you would open table management
            loadNotifications();
            updateUnreadCount();
        } catch (Exception e) {
            System.err.println("Error viewing table: " + e.getMessage());
        }
    }

    // System handlers
    private void handleAcknowledge(Notification notification) {
        try {
            notification.setRead(true);
            notification.setActionable(false);
            System.out.println("✅ Acknowledged: " + notification.getTitle());
            loadNotifications();
            updateUnreadCount();
        } catch (Exception e) {
            System.err.println("Error acknowledging notification: " + e.getMessage());
        }
    }

    // Kitchen handlers
    private void handleKitchenReady(Notification notification) {
        try {
            notification.setRead(true);
            notification.setActionable(false);
            System.out.println("✅ Kitchen item ready: " + notification.getTitle());
            loadNotifications();
            updateUnreadCount();
        } catch (Exception e) {
            System.err.println("Error marking kitchen item ready: " + e.getMessage());
        }
    }

    private void handleKitchenDelay(Notification notification) {
        try {
            notification.setRead(true);
            System.out.println("⏰ Kitchen item delayed: " + notification.getTitle());
            // Here you could show delay reason dialog
            loadNotifications();
            updateUnreadCount();
        } catch (Exception e) {
            System.err.println("Error handling kitchen delay: " + e.getMessage());
        }
    }

    // Payment handlers
    private void handleConfirmPayment(Notification notification) {
        try {
            notification.setRead(true);
            notification.setActionable(false);
            System.out.println("✅ Payment confirmed: " + notification.getTitle());
            loadNotifications();
            updateUnreadCount();
        } catch (Exception e) {
            System.err.println("Error confirming payment: " + e.getMessage());
        }
    }



    private void updateUnreadCount() {
        long unreadCount = notificationService.getUnreadCount();
        unreadCountLabel.setText(String.valueOf(unreadCount));
        unreadCountLabel.setVisible(unreadCount > 0);
    }

    /**
     * Update tab button texts with notification counts
     */
    private void updateTabCounts() {
        Platform.runLater(() -> {
            try {
                // Get counts for each notification type
                long totalCount = notificationService.getTotalNotificationCount();
                long onlineCount = notificationService.getNotificationCountByType(NotificationType.ONLINE_ORDER);
                long kotCount = notificationService.getNotificationCountByType(NotificationType.KOT_ALERT);
                long billingCount = notificationService.getNotificationCountByType(NotificationType.BILLING);
                long urgentCount = notificationService.getUrgentNotificationCount();

                // Update tab button texts with counts
                if (allTab != null) {
                    allTab.setText("All" + (totalCount > 0 ? " (" + totalCount + ")" : ""));
                }
                if (onlineOrdersTab != null) {
                    onlineOrdersTab.setText("🛒 Online" + (onlineCount > 0 ? " (" + onlineCount + ")" : ""));
                }
                if (kotTab != null) {
                    kotTab.setText("📋 KOT" + (kotCount > 0 ? " (" + kotCount + ")" : ""));
                }
                if (billingTab != null) {
                    billingTab.setText("💰 Bills" + (billingCount > 0 ? " (" + billingCount + ")" : ""));
                }
                if (urgentTab != null) {
                    urgentTab.setText("🚨 Urgent" + (urgentCount > 0 ? " (" + urgentCount + ")" : ""));
                }

                System.out.println("📊 Tab counts updated - Total: " + totalCount +
                                 ", Online: " + onlineCount +
                                 ", KOT: " + kotCount +
                                 ", Bills: " + billingCount +
                                 ", Urgent: " + urgentCount);

            } catch (Exception e) {
                System.err.println("Error updating tab counts: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    private void updateLastUpdateTime() {
        String timeText = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        lastUpdateLabel.setText("Last updated: " + timeText);
    }

    private void startUpdateTimer() {
        updateTimer = new Timer(true);
        updateTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                Platform.runLater(() -> {
                    updateLastUpdateTime();
                    // Only update time, don't reload notifications to prevent loop
                    // loadNotifications(); // REMOVED to prevent notification loop
                });
            }
        }, 30000, 30000); // Update every 30 seconds
    }

    // FXML Event Handlers

    @FXML
    private void markAllAsRead() {
        notificationService.markAllAsRead();
        loadNotifications();
        updateUnreadCount();
    }

    @FXML
    private void clearAllNotifications() {
        Alert confirmation = new Alert(Alert.AlertType.CONFIRMATION);
        confirmation.setTitle("Clear All Notifications");
        confirmation.setHeaderText("Are you sure you want to clear all notifications?");
        confirmation.setContentText("This action cannot be undone.");

        confirmation.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                notificationService.clearAll();
                loadNotifications();
                updateUnreadCount();
            }
        });
    }

    @FXML
    private void refreshNotifications() {
        loadNotifications();
        updateUnreadCount();
        updateLastUpdateTime();
        updateTabCounts(); // Update tab counts when refreshing

        // Simulate new notifications for demo
        if (Math.random() < 0.5) {
            notificationService.notifyOnlineOrder("Swiggy", "SW" + System.currentTimeMillis() % 10000,
                    Math.round((Math.random() * 500 + 200) * 100.0) / 100.0);
        }
    }

    // Filter methods
    @FXML
    private void filterAll() {
        loadNotifications();
        updateTabCounts(); // Update counts when switching tabs
    }

    @FXML
    private void filterOnlineOrders() {
        loadNotifications();
        updateTabCounts(); // Update counts when switching tabs
    }

    @FXML
    private void filterKOT() {
        loadNotifications();
        updateTabCounts(); // Update counts when switching tabs
    }

    @FXML
    private void filterBilling() {
        loadNotifications();
        updateTabCounts(); // Update counts when switching tabs
    }

    @FXML
    private void filterUrgent() {
        loadNotifications();
        updateTabCounts(); // Update counts when switching tabs
    }

    // Quick action methods removed

    // Integration action methods removed



    private void configureScrollPane() {
        if (notificationScrollPane != null) {
            // Use FUNCTIONAL scroll bar that actually scrolls
            notificationScrollPane.getStyleClass().clear();
            notificationScrollPane.getStyleClass().add("notification-scroll-functional");

            // Set scroll bar policies for functional scrolling
            notificationScrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.ALWAYS);
            notificationScrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
            notificationScrollPane.setFitToWidth(true);
            notificationScrollPane.setFitToHeight(false);

            // Use reasonable dimensions that work with JavaFX
            notificationScrollPane.setPrefHeight(400); // Reasonable viewport height
            notificationScrollPane.setMinHeight(400);

            // Ensure container has proper height for scrolling
            if (notificationContainer != null) {
                // Calculate height based on notification count - ensure scrollable content
                int notificationCount = notificationContainer.getChildren().size();
                double calculatedHeight = Math.max(800, notificationCount * 80 + 200); // 80px per notification +
                                                                                       // padding

                notificationContainer.setMinHeight(calculatedHeight);
                notificationContainer.setPrefHeight(Region.USE_COMPUTED_SIZE);

                double visibilityRatio = 400.0 / calculatedHeight * 100;
                System.out.println("Notification container height set to: " + calculatedHeight + " for "
                        + notificationCount + " notifications");
                System.out.println("Viewport: 400px, Content: " + calculatedHeight + "px, Visibility ratio: "
                        + String.format("%.1f", visibilityRatio) + "%");
            }

            System.out.println("Notification scroll pane configured with FUNCTIONAL scroll bar that actually scrolls");
        }
    }

    private void refreshScrollPane() {
        if (notificationScrollPane != null) {
            // Use the ScrollPaneManager for refresh
            ScrollPaneManager.getInstance().refreshAllScrollPanes();
            ScrollPaneUtil.scrollToTop(notificationScrollPane);
            System.out.println("Notification scroll pane refreshed using ScrollPaneManager");
        }
    }

    private void startScrollBarCheck() {
        // DISABLED: This timer was causing performance issues and notification loops
        // The scroll bar policy is already set in setupCustomScrollPane()
        // No need for periodic checks that can cause UI freezing
        System.out.println("ScrollBar check timer disabled to prevent notification loops");
    }

    private void addDemoNotifications() {
        // Add multiple demo notifications to test scroll bar
        notificationService.notifyOnlineOrder("Swiggy", "SW12345", 450.00);
        notificationService.notifyOnlineOrder("Zomato", "ZM67890", 320.50);
        notificationService.notifyKOTAlert("5", 18);
        notificationService.notifyBillingRequest("3", 850.00);
        notificationService.notifyOnlineOrder("Uber Eats", "UE11111", 275.00);
        notificationService.notifyKOTAlert("8", 22);
        notificationService.notifyOnlineOrder("Swiggy", "SW22222", 520.75);
        notificationService.notifyBillingRequest("12", 1200.00);
        notificationService.notifyKOTAlert("2", 16);
        notificationService.notifyOnlineOrder("Zomato", "ZM33333", 380.25);
        notificationService.notifyBillingRequest("7", 650.50);
        notificationService.notifyKOTAlert("15", 25);
        notificationService.notifyOnlineOrder("Swiggy", "SW44444", 425.00);
        notificationService.notifyBillingRequest("9", 750.75);
        notificationService.notifyKOTAlert("4", 19);
        notificationService.notifyLowStock("Paneer", 3);
        notificationService.notifyLowStock("Chicken", 5);
        notificationService.notifyKitchenUpdate("ORD123", "Ready to serve");
        notificationService.notifyPaymentReceived("UPI", 450.00, "TXN789");
        notificationService.notifySystemAlert("Backup Complete", "Daily backup completed successfully",
                com.restaurant.model.Notification.Priority.LOW);
    }

    public void shutdown() {
        if (updateTimer != null) {
            updateTimer.cancel();
        }
    }
}
