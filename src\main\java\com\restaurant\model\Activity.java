package com.restaurant.model;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Model class representing an activity record in the restaurant system
 */
public class Activity {
    private int id;
    private String type;
    private String description;
    private String user;
    private LocalDateTime timestamp;
    
    // Formatters for display
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
    
    // Constructors
    public Activity() {
        this.timestamp = LocalDateTime.now();
    }
    
    public Activity(String type, String description, String user) {
        this();
        this.type = type;
        this.description = description;
        this.user = user;
    }
    
    public Activity(int id, String type, String description, String user, LocalDateTime timestamp) {
        this.id = id;
        this.type = type;
        this.description = description;
        this.user = user;
        this.timestamp = timestamp;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getUser() {
        return user;
    }
    
    public void setUser(String user) {
        this.user = user;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    // Display methods for UI
    public String getTime() {
        return timestamp != null ? timestamp.format(TIME_FORMATTER) : "";
    }
    
    public String getDateTime() {
        return timestamp != null ? timestamp.format(DATETIME_FORMATTER) : "";
    }
    
    public String getTypeIcon() {
        switch (type.toLowerCase()) {
            case "order":
                return "📋";
            case "payment":
                return "💰";
            case "menu":
                return "🍽️";
            case "user":
                return "👤";
            case "inventory":
                return "📦";
            case "table":
                return "🪑";
            case "system":
                return "⚙️";
            case "kitchen":
                return "👨‍🍳";
            default:
                return "📝";
        }
    }
    
    public String getDisplayType() {
        return getTypeIcon() + " " + type;
    }
    
    public String getShortDescription() {
        if (description.length() > 50) {
            return description.substring(0, 47) + "...";
        }
        return description;
    }
    
    public String getPriorityLevel() {
        switch (type.toLowerCase()) {
            case "system":
            case "payment":
                return "HIGH";
            case "order":
            case "kitchen":
                return "MEDIUM";
            default:
                return "LOW";
        }
    }
    
    public boolean isRecent() {
        return timestamp.isAfter(LocalDateTime.now().minusHours(1));
    }
    
    public boolean isToday() {
        return timestamp.toLocalDate().equals(LocalDateTime.now().toLocalDate());
    }
    
    @Override
    public String toString() {
        return String.format("[%s] %s: %s by %s", 
                getTime(), type, description, user);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        Activity activity = (Activity) obj;
        return id == activity.id;
    }
    
    @Override
    public int hashCode() {
        return Integer.hashCode(id);
    }
    
    // Static factory methods for common activities
    public static Activity createOrderActivity(String description, String user) {
        return new Activity("Order", description, user);
    }
    
    public static Activity createPaymentActivity(String description, String user) {
        return new Activity("Payment", description, user);
    }
    
    public static Activity createMenuActivity(String description, String user) {
        return new Activity("Menu", description, user);
    }
    
    public static Activity createUserActivity(String description, String user) {
        return new Activity("User", description, user);
    }
    
    public static Activity createInventoryActivity(String description, String user) {
        return new Activity("Inventory", description, user);
    }
    
    public static Activity createTableActivity(String description, String user) {
        return new Activity("Table", description, user);
    }
    
    public static Activity createSystemActivity(String description) {
        return new Activity("System", description, "System");
    }
    
    public static Activity createKitchenActivity(String description, String user) {
        return new Activity("Kitchen", description, user);
    }
}
