package com.restaurant.controller;

import com.restaurant.model.Recipe;
import com.restaurant.model.RecipeIngredient;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.*;

import java.net.URL;
import java.util.*;

/**
 * Controller for Recipe Management with Multi-Stage Recipe Conversion
 */
public class RecipeManagementController implements Initializable {

    @FXML private Button backButton;
    @FXML private Button addMargheritaBtn;
    @FXML private Button addTomatoBtn;
    @FXML private Button convertTomatoBtn;
    @FXML private VBox margheritaIngredientsContainer;
    @FXML private VBox tomatoIngredientsContainer;
    @FXML private VBox multiStageContainer;

    // Recipe data
    private Recipe margheritaPizza;
    private Recipe tomatoSauce;
    private List<Recipe> multiStageRecipes;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        initializeRecipes();
        setupInitialIngredients();
        setupMultiStageRecipes();
    }

    private void initializeRecipes() {
        // Initialize Margherita Pizza recipe
        margheritaPizza = new Recipe("Margherita Pizza", "Classic Italian pizza with tomato sauce, mozzarella, and basil");
        margheritaPizza.setCategory("Main Course");
        margheritaPizza.setServings(4);
        margheritaPizza.setPrepTimeMinutes(30);
        margheritaPizza.setCookTimeMinutes(15);

        // Initialize Tomato Sauce recipe
        tomatoSauce = new Recipe("Tomato Sauce", "Fresh tomato sauce for pizza and pasta");
        tomatoSauce.setCategory("Sauce");
        tomatoSauce.setServings(8);
        tomatoSauce.setPrepTimeMinutes(10);
        tomatoSauce.setCookTimeMinutes(20);

        // Initialize multi-stage recipes list
        multiStageRecipes = new ArrayList<>();
    }

    private void setupInitialIngredients() {
        // Margherita Pizza ingredients
        margheritaPizza.addIngredient(new RecipeIngredient("Tomato Sauce", 525, "gms"));
        margheritaPizza.addIngredient(new RecipeIngredient("Pizza Base", 255, "gms"));
        margheritaPizza.addIngredient(new RecipeIngredient("Mozzarella", 350, "gms"));

        // Tomato Sauce ingredients
        tomatoSauce.addIngredient(new RecipeIngredient("Tomatoes", 475, "gms"));
        tomatoSauce.addIngredient(new RecipeIngredient("Garlic", 25, "gms"));
        tomatoSauce.addIngredient(new RecipeIngredient("Sugar", 14, "gms"));

        // Display ingredients
        displayMargheritaIngredients();
        displayTomatoIngredients();
    }

    private void setupMultiStageRecipes() {
        // Add recipes to multi-stage conversion
        margheritaPizza.setAutoConvert(true);
        tomatoSauce.setAutoConvert(true);
        
        multiStageRecipes.add(margheritaPizza);
        multiStageRecipes.add(tomatoSauce);
        
        displayMultiStageRecipes();
    }

    private void displayMargheritaIngredients() {
        margheritaIngredientsContainer.getChildren().clear();
        
        for (RecipeIngredient ingredient : margheritaPizza.getIngredients()) {
            HBox ingredientRow = createIngredientRow(ingredient, true);
            margheritaIngredientsContainer.getChildren().add(ingredientRow);
        }
    }

    private void displayTomatoIngredients() {
        tomatoIngredientsContainer.getChildren().clear();
        
        for (RecipeIngredient ingredient : tomatoSauce.getIngredients()) {
            HBox ingredientRow = createIngredientRow(ingredient, false);
            tomatoIngredientsContainer.getChildren().add(ingredientRow);
        }
    }

    private HBox createIngredientRow(RecipeIngredient ingredient, boolean isReadOnly) {
        HBox row = new HBox(10);
        row.setAlignment(Pos.CENTER_LEFT);
        row.getStyleClass().add("ingredient-row");
        row.setPadding(new Insets(8, 0, 8, 0));

        // Material name
        Label materialLabel = new Label(ingredient.getMaterialName());
        materialLabel.getStyleClass().add("ingredient-material");
        materialLabel.setPrefWidth(120);
        HBox.setHgrow(materialLabel, Priority.ALWAYS);

        // Quantity
        Label quantityLabel = new Label(ingredient.getFormattedQuantity());
        quantityLabel.getStyleClass().add("ingredient-quantity");
        quantityLabel.setPrefWidth(60);
        quantityLabel.setAlignment(Pos.CENTER_RIGHT);

        // Unit
        Label unitLabel = new Label(ingredient.getUnit());
        unitLabel.getStyleClass().add("ingredient-unit");
        unitLabel.setPrefWidth(50);

        row.getChildren().addAll(materialLabel, quantityLabel, unitLabel);

        return row;
    }

    private void displayMultiStageRecipes() {
        multiStageContainer.getChildren().clear();
        
        for (Recipe recipe : multiStageRecipes) {
            HBox recipeRow = createMultiStageRow(recipe);
            multiStageContainer.getChildren().add(recipeRow);
        }
    }

    private HBox createMultiStageRow(Recipe recipe) {
        HBox row = new HBox(15);
        row.setAlignment(Pos.CENTER_LEFT);
        row.getStyleClass().add("multi-stage-row");
        row.setPadding(new Insets(12, 0, 12, 0));

        // Conversion name
        Label nameLabel = new Label(recipe.getName());
        nameLabel.getStyleClass().add("multi-stage-name");
        HBox.setHgrow(nameLabel, Priority.ALWAYS);

        // Auto convert checkbox
        CheckBox autoConvertCheck = new CheckBox();
        autoConvertCheck.setSelected(recipe.isAutoConvert());
        autoConvertCheck.getStyleClass().add("auto-convert-checkbox");
        autoConvertCheck.setOnAction(e -> {
            recipe.setAutoConvert(autoConvertCheck.isSelected());
            System.out.println("Auto convert for " + recipe.getName() + ": " + recipe.isAutoConvert());
        });

        // Edit button
        Button editButton = new Button("Edit");
        editButton.getStyleClass().add("edit-button");
        editButton.setOnAction(e -> editRecipe(recipe));

        row.getChildren().addAll(nameLabel, autoConvertCheck, editButton);

        return row;
    }

    // Event handlers
    @FXML
    private void addNewMargheritaIngredient() {
        showAddIngredientDialog(margheritaPizza, "Margherita Pizza");
    }

    @FXML
    private void addNewTomatoIngredient() {
        showAddIngredientDialog(tomatoSauce, "Tomato Sauce");
    }

    @FXML
    private void convertTomatoSauce() {
        showAlert("Convert Tomato Sauce", 
                 "Converting Tomato Sauce recipe...\n\n" +
                 "This will process the tomato sauce ingredients and make it available for use in other recipes.\n\n" +
                 "Conversion completed successfully!");
    }

    private void editRecipe(Recipe recipe) {
        showAlert("Edit Recipe", "Opening recipe editor for: " + recipe.getName());
    }

    private void showAddIngredientDialog(Recipe recipe, String recipeName) {
        Dialog<RecipeIngredient> dialog = new Dialog<>();
        dialog.setTitle("Add New Ingredient");
        dialog.setHeaderText("Add ingredient to " + recipeName);

        // Create form fields
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        TextField materialField = new TextField();
        materialField.setPromptText("Material name");
        
        TextField quantityField = new TextField();
        quantityField.setPromptText("Quantity");
        
        ComboBox<String> unitCombo = new ComboBox<>();
        unitCombo.getItems().addAll("gms", "kg", "ml", "ltr", "pcs", "cups", "tbsp", "tsp");
        unitCombo.setValue("gms");

        grid.add(new Label("Material:"), 0, 0);
        grid.add(materialField, 1, 0);
        grid.add(new Label("Quantity:"), 0, 1);
        grid.add(quantityField, 1, 1);
        grid.add(new Label("Unit:"), 0, 2);
        grid.add(unitCombo, 1, 2);

        dialog.getDialogPane().setContent(grid);

        ButtonType addButtonType = new ButtonType("Add", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(addButtonType, ButtonType.CANCEL);

        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == addButtonType) {
                try {
                    String material = materialField.getText().trim();
                    double quantity = Double.parseDouble(quantityField.getText().trim());
                    String unit = unitCombo.getValue();
                    
                    if (!material.isEmpty() && quantity > 0) {
                        return new RecipeIngredient(material, quantity, unit);
                    }
                } catch (NumberFormatException e) {
                    showAlert("Error", "Please enter a valid quantity number.");
                }
            }
            return null;
        });

        Optional<RecipeIngredient> result = dialog.showAndWait();
        result.ifPresent(ingredient -> {
            recipe.addIngredient(ingredient);
            
            // Refresh the appropriate display
            if (recipe == margheritaPizza) {
                displayMargheritaIngredients();
            } else if (recipe == tomatoSauce) {
                displayTomatoIngredients();
            }
            
            System.out.println("Added ingredient: " + ingredient.getDisplayText() + " to " + recipe.getName());
        });
    }

    @FXML
    private void goBack() {
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/Dashboard.fxml"));
            javafx.scene.Parent root = loader.load();

            javafx.stage.Stage stage = (javafx.stage.Stage) backButton.getScene().getWindow();
            stage.getScene().setRoot(root);

            System.out.println("Navigated back to dashboard from Recipe Management");

        } catch (Exception e) {
            System.err.println("Error navigating back to dashboard: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
