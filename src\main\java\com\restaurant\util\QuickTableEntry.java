package com.restaurant.util;

import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.input.KeyCode;
import javafx.scene.layout.VBox;
import javafx.scene.layout.HBox;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.stage.StageStyle;

/**
 * Quick table number entry utility for fast table navigation
 */
public class QuickTableEntry {
    
    private static Stage currentDialog;
    private static TextField tableNumberField;
    private static Runnable onTableSelected;
    
    /**
     * Show quick table entry dialog
     * @param onTableSelectedCallback Callback function that receives the selected table number
     */
    public static void showTableEntry(java.util.function.Consumer<Integer> onTableSelectedCallback) {
        // Close existing dialog if open
        if (currentDialog != null && currentDialog.isShowing()) {
            currentDialog.close();
        }
        
        Platform.runLater(() -> {
            try {
                createTableEntryDialog(onTableSelectedCallback);
            } catch (Exception e) {
                System.err.println("Error showing table entry dialog: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    /**
     * Create the table entry dialog
     */
    private static void createTableEntryDialog(java.util.function.Consumer<Integer> onTableSelectedCallback) {
        currentDialog = new Stage();
        currentDialog.initModality(Modality.APPLICATION_MODAL);
        currentDialog.initStyle(StageStyle.UNDECORATED);
        currentDialog.setTitle("Quick Table Entry");
        currentDialog.setResizable(false);
        
        // Main container
        VBox mainContainer = new VBox(20);
        mainContainer.setAlignment(Pos.CENTER);
        mainContainer.setPadding(new Insets(30));
        mainContainer.setStyle(
            "-fx-background-color: linear-gradient(to bottom, #667eea 0%, #764ba2 100%); " +
            "-fx-background-radius: 15; " +
            "-fx-border-color: #4a5568; " +
            "-fx-border-width: 2; " +
            "-fx-border-radius: 15; " +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 15, 0, 0, 5);"
        );
        
        // Title
        Label titleLabel = new Label("🍽️ Enter Table Number");
        titleLabel.setFont(Font.font("System", FontWeight.BOLD, 20));
        titleLabel.setStyle("-fx-text-fill: white;");
        
        // Instructions
        Label instructionLabel = new Label("Type the table number (e.g., 1, 10, 22) and press Enter to continue.\nPress Escape to cancel.");
        instructionLabel.setFont(Font.font("System", 12));
        instructionLabel.setStyle("-fx-text-fill: #e2e8f0; -fx-text-alignment: center;");
        instructionLabel.setWrapText(true);
        
        // Table number input
        tableNumberField = new TextField();
        tableNumberField.setPromptText("Table number...");
        tableNumberField.setPrefWidth(200);
        tableNumberField.setPrefHeight(40);
        tableNumberField.setFont(Font.font("System", FontWeight.BOLD, 16));
        tableNumberField.setStyle(
            "-fx-background-color: white; " +
            "-fx-background-radius: 8; " +
            "-fx-border-color: #cbd5e0; " +
            "-fx-border-width: 2; " +
            "-fx-border-radius: 8; " +
            "-fx-padding: 8 12; " +
            "-fx-text-fill: #2d3748;"
        );
        
        // Focus styling
        tableNumberField.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal) {
                tableNumberField.setStyle(
                    "-fx-background-color: white; " +
                    "-fx-background-radius: 8; " +
                    "-fx-border-color: #667eea; " +
                    "-fx-border-width: 2; " +
                    "-fx-border-radius: 8; " +
                    "-fx-padding: 8 12; " +
                    "-fx-text-fill: #2d3748;"
                );
            } else {
                tableNumberField.setStyle(
                    "-fx-background-color: white; " +
                    "-fx-background-radius: 8; " +
                    "-fx-border-color: #cbd5e0; " +
                    "-fx-border-width: 2; " +
                    "-fx-border-radius: 8; " +
                    "-fx-padding: 8 12; " +
                    "-fx-text-fill: #2d3748;"
                );
            }
        });
        
        // Only allow numeric input
        tableNumberField.textProperty().addListener((observable, oldValue, newValue) -> {
            if (!newValue.matches("\\d*")) {
                tableNumberField.setText(newValue.replaceAll("[^\\d]", ""));
            }
        });
        
        // Buttons
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER);
        
        Button enterButton = new Button("✅ Go to Table");
        enterButton.setPrefWidth(120);
        enterButton.setPrefHeight(35);
        enterButton.setFont(Font.font("System", FontWeight.BOLD, 12));
        enterButton.setStyle(
            "-fx-background-color: #48bb78; " +
            "-fx-text-fill: white; " +
            "-fx-background-radius: 6; " +
            "-fx-border-radius: 6;"
        );
        
        Button cancelButton = new Button("❌ Cancel");
        cancelButton.setPrefWidth(120);
        cancelButton.setPrefHeight(35);
        cancelButton.setFont(Font.font("System", FontWeight.BOLD, 12));
        cancelButton.setStyle(
            "-fx-background-color: #f56565; " +
            "-fx-text-fill: white; " +
            "-fx-background-radius: 6; " +
            "-fx-border-radius: 6;"
        );
        
        buttonBox.getChildren().addAll(enterButton, cancelButton);
        
        // Quick number buttons (1-9)
        Label quickLabel = new Label("Quick Access:");
        quickLabel.setFont(Font.font("System", FontWeight.BOLD, 12));
        quickLabel.setStyle("-fx-text-fill: #e2e8f0;");
        
        HBox quickButtonsRow1 = new HBox(8);
        quickButtonsRow1.setAlignment(Pos.CENTER);
        
        HBox quickButtonsRow2 = new HBox(8);
        quickButtonsRow2.setAlignment(Pos.CENTER);
        
        HBox quickButtonsRow3 = new HBox(8);
        quickButtonsRow3.setAlignment(Pos.CENTER);
        
        // Create quick access buttons 1-9
        for (int i = 1; i <= 9; i++) {
            Button quickBtn = createQuickButton(i, onTableSelectedCallback);
            if (i <= 3) {
                quickButtonsRow1.getChildren().add(quickBtn);
            } else if (i <= 6) {
                quickButtonsRow2.getChildren().add(quickBtn);
            } else {
                quickButtonsRow3.getChildren().add(quickBtn);
            }
        }
        
        VBox quickButtonsContainer = new VBox(8);
        quickButtonsContainer.setAlignment(Pos.CENTER);
        quickButtonsContainer.getChildren().addAll(quickLabel, quickButtonsRow1, quickButtonsRow2, quickButtonsRow3);
        
        mainContainer.getChildren().addAll(
            titleLabel, 
            instructionLabel, 
            tableNumberField, 
            buttonBox,
            new Separator(),
            quickButtonsContainer
        );
        
        // Event handlers
        enterButton.setOnAction(e -> handleTableEntry(onTableSelectedCallback));
        cancelButton.setOnAction(e -> currentDialog.close());
        
        // Keyboard handlers
        Scene scene = new Scene(mainContainer);
        scene.setOnKeyPressed(event -> {
            switch (event.getCode()) {
                case ENTER:
                    handleTableEntry(onTableSelectedCallback);
                    break;
                case ESCAPE:
                    currentDialog.close();
                    break;
                case DIGIT1: case NUMPAD1:
                    selectTable(1, onTableSelectedCallback);
                    break;
                case DIGIT2: case NUMPAD2:
                    selectTable(2, onTableSelectedCallback);
                    break;
                case DIGIT3: case NUMPAD3:
                    selectTable(3, onTableSelectedCallback);
                    break;
                case DIGIT4: case NUMPAD4:
                    selectTable(4, onTableSelectedCallback);
                    break;
                case DIGIT5: case NUMPAD5:
                    selectTable(5, onTableSelectedCallback);
                    break;
                case DIGIT6: case NUMPAD6:
                    selectTable(6, onTableSelectedCallback);
                    break;
                case DIGIT7: case NUMPAD7:
                    selectTable(7, onTableSelectedCallback);
                    break;
                case DIGIT8: case NUMPAD8:
                    selectTable(8, onTableSelectedCallback);
                    break;
                case DIGIT9: case NUMPAD9:
                    selectTable(9, onTableSelectedCallback);
                    break;
                case DIGIT0: case NUMPAD0:
                    selectTable(10, onTableSelectedCallback);
                    break;
            }
        });
        
        currentDialog.setScene(scene);
        
        // Center on screen
        currentDialog.centerOnScreen();
        
        // Focus on text field
        Platform.runLater(() -> tableNumberField.requestFocus());
        
        currentDialog.show();
    }
    
    /**
     * Create quick access button for table numbers 1-9
     */
    private static Button createQuickButton(int tableNumber, java.util.function.Consumer<Integer> callback) {
        Button btn = new Button(String.valueOf(tableNumber));
        btn.setPrefSize(40, 40);
        btn.setFont(Font.font("System", FontWeight.BOLD, 14));
        btn.setStyle(
            "-fx-background-color: rgba(255,255,255,0.2); " +
            "-fx-text-fill: white; " +
            "-fx-background-radius: 6; " +
            "-fx-border-color: rgba(255,255,255,0.3); " +
            "-fx-border-width: 1; " +
            "-fx-border-radius: 6;"
        );
        
        btn.setOnMouseEntered(e -> btn.setStyle(
            "-fx-background-color: rgba(255,255,255,0.3); " +
            "-fx-text-fill: white; " +
            "-fx-background-radius: 6; " +
            "-fx-border-color: rgba(255,255,255,0.5); " +
            "-fx-border-width: 1; " +
            "-fx-border-radius: 6;"
        ));
        
        btn.setOnMouseExited(e -> btn.setStyle(
            "-fx-background-color: rgba(255,255,255,0.2); " +
            "-fx-text-fill: white; " +
            "-fx-background-radius: 6; " +
            "-fx-border-color: rgba(255,255,255,0.3); " +
            "-fx-border-width: 1; " +
            "-fx-border-radius: 6;"
        ));
        
        btn.setOnAction(e -> selectTable(tableNumber, callback));
        
        return btn;
    }
    
    /**
     * Handle table entry from text field
     */
    private static void handleTableEntry(java.util.function.Consumer<Integer> callback) {
        String input = tableNumberField.getText().trim();
        
        if (input.isEmpty()) {
            // Shake animation for empty input
            shakeTextField();
            return;
        }
        
        try {
            int tableNumber = Integer.parseInt(input);
            
            if (tableNumber <= 0) {
                showError("Table number must be greater than 0");
                return;
            }
            
            if (tableNumber > 999) {
                showError("Table number too large (max 999)");
                return;
            }
            
            selectTable(tableNumber, callback);
            
        } catch (NumberFormatException e) {
            showError("Please enter a valid table number");
        }
    }
    
    /**
     * Select table and close dialog
     */
    private static void selectTable(int tableNumber, java.util.function.Consumer<Integer> callback) {
        currentDialog.close();
        
        Platform.runLater(() -> {
            try {
                callback.accept(tableNumber);
            } catch (Exception e) {
                System.err.println("Error in table selection callback: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    /**
     * Show error message
     */
    private static void showError(String message) {
        // Change text field border to red temporarily
        tableNumberField.setStyle(
            "-fx-background-color: white; " +
            "-fx-background-radius: 8; " +
            "-fx-border-color: #f56565; " +
            "-fx-border-width: 2; " +
            "-fx-border-radius: 8; " +
            "-fx-padding: 8 12; " +
            "-fx-text-fill: #2d3748;"
        );
        
        // Reset after 2 seconds
        Platform.runLater(() -> {
            new Thread(() -> {
                try {
                    Thread.sleep(2000);
                    Platform.runLater(() -> {
                        if (tableNumberField != null) {
                            tableNumberField.setStyle(
                                "-fx-background-color: white; " +
                                "-fx-background-radius: 8; " +
                                "-fx-border-color: #cbd5e0; " +
                                "-fx-border-width: 2; " +
                                "-fx-border-radius: 8; " +
                                "-fx-padding: 8 12; " +
                                "-fx-text-fill: #2d3748;"
                            );
                        }
                    });
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }).start();
        });
        
        shakeTextField();
    }
    
    /**
     * Shake animation for text field
     */
    private static void shakeTextField() {
        if (tableNumberField == null) return;
        
        // Simple shake effect by changing translateX
        Platform.runLater(() -> {
            double originalX = tableNumberField.getTranslateX();
            
            new Thread(() -> {
                try {
                    for (int i = 0; i < 3; i++) {
                        Platform.runLater(() -> tableNumberField.setTranslateX(originalX + 5));
                        Thread.sleep(50);
                        Platform.runLater(() -> tableNumberField.setTranslateX(originalX - 5));
                        Thread.sleep(50);
                    }
                    Platform.runLater(() -> tableNumberField.setTranslateX(originalX));
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }).start();
        });
    }
}
