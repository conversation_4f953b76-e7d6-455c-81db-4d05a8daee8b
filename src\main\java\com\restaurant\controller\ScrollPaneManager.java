package com.restaurant.controller;

import com.restaurant.util.ScrollPaneUtil;
import javafx.application.Platform;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.control.ScrollPane;
import javafx.scene.layout.Region;

import java.util.ArrayList;
import java.util.List;

/**
 * Manager class to handle all ScrollPane configurations across the application
 */
public class ScrollPaneManager {
    
    private static ScrollPaneManager instance;
    private List<ScrollPane> managedScrollPanes;
    
    private ScrollPaneManager() {
        managedScrollPanes = new ArrayList<>();
    }
    
    public static ScrollPaneManager getInstance() {
        if (instance == null) {
            instance = new ScrollPaneManager();
        }
        return instance;
    }
    
    /**
     * Configure all scroll panes in a parent node
     */
    public void configureAllScrollPanes(Parent parent) {
        if (parent == null) return;
        
        List<ScrollPane> scrollPanes = findAllScrollPanes(parent);
        for (ScrollPane scrollPane : scrollPanes) {
            configureScrollPane(scrollPane);
        }
        
        System.out.println("Configured " + scrollPanes.size() + " scroll panes");
    }
    
    /**
     * Configure a single scroll pane and add it to managed list
     */
    public void configureScrollPane(ScrollPane scrollPane) {
        if (scrollPane == null) return;
        
        // Use the universal utility
        ScrollPaneUtil.configureScrollPane(scrollPane);
        
        // Add to managed list if not already present
        if (!managedScrollPanes.contains(scrollPane)) {
            managedScrollPanes.add(scrollPane);
        }
        
        // Force immediate layout
        Platform.runLater(() -> {
            forceScrollPaneRefresh(scrollPane);
        });
    }
    
    /**
     * Refresh all managed scroll panes
     */
    public void refreshAllScrollPanes() {
        for (ScrollPane scrollPane : managedScrollPanes) {
            if (scrollPane != null) {
                forceScrollPaneRefresh(scrollPane);
            }
        }
        System.out.println("Refreshed " + managedScrollPanes.size() + " managed scroll panes");
    }
    
    /**
     * Force refresh a specific scroll pane
     */
    private void forceScrollPaneRefresh(ScrollPane scrollPane) {
        Platform.runLater(() -> {
            // Force scroll bar policy
            scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.ALWAYS);
            scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
            
            // Force layout recalculation
            scrollPane.requestLayout();
            scrollPane.applyCss();
            
            // Configure content
            Node content = scrollPane.getContent();
            if (content instanceof Region) {
                Region contentRegion = (Region) content;
                
                // Ensure proper sizing
                contentRegion.autosize();
                contentRegion.requestLayout();
                
                // Force minimum height for scrolling
                double viewportHeight = scrollPane.getViewportBounds().getHeight();
                if (viewportHeight > 0) {
                    double minHeight = Math.max(viewportHeight + 200, 600);
                    contentRegion.setMinHeight(minHeight);
                }
            }
            
            // Reset scroll position
            scrollPane.setVvalue(0.0);
        });
    }
    
    /**
     * Find all ScrollPane nodes in a parent
     */
    private List<ScrollPane> findAllScrollPanes(Parent parent) {
        List<ScrollPane> scrollPanes = new ArrayList<>();
        findScrollPanesRecursive(parent, scrollPanes);
        return scrollPanes;
    }
    
    /**
     * Recursively find ScrollPane nodes
     */
    private void findScrollPanesRecursive(Parent parent, List<ScrollPane> scrollPanes) {
        for (Node child : parent.getChildrenUnmodifiable()) {
            if (child instanceof ScrollPane) {
                scrollPanes.add((ScrollPane) child);
            } else if (child instanceof Parent) {
                findScrollPanesRecursive((Parent) child, scrollPanes);
            }
        }
    }
    
    /**
     * Configure scroll pane with specific minimum height
     */
    public void configureScrollPaneWithMinHeight(ScrollPane scrollPane, double minHeight) {
        configureScrollPane(scrollPane);
        
        if (scrollPane.getContent() instanceof Region) {
            Region content = (Region) scrollPane.getContent();
            content.setMinHeight(minHeight);
        }
    }
    
    /**
     * Remove a scroll pane from management
     */
    public void removeScrollPane(ScrollPane scrollPane) {
        managedScrollPanes.remove(scrollPane);
    }
    
    /**
     * Clear all managed scroll panes
     */
    public void clearManagedScrollPanes() {
        managedScrollPanes.clear();
    }
    
    /**
     * Get count of managed scroll panes
     */
    public int getManagedScrollPaneCount() {
        return managedScrollPanes.size();
    }
}
