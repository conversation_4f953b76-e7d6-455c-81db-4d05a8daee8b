package com.restaurant.controller;

import com.restaurant.model.User;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.geometry.Pos;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;
import javafx.scene.layout.BorderPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import com.restaurant.util.ModalManager;
import javafx.scene.control.ComboBox;
import javafx.scene.control.PasswordField;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import java.io.IOException;
import java.util.Optional;

public class UserManagementController {
    
    @FXML private TextField searchField;
    @FXML private ComboBox<String> roleFilterCombo;
    @FXML private ComboBox<String> statusFilterCombo;
    @FXML private TableView<UserRecord> usersTable;
    @FXML private TableColumn<UserRecord, Integer> userIdColumn;
    @FXML private TableColumn<UserRecord, String> usernameColumn;
    @FXML private TableColumn<UserRecord, String> fullNameColumn;
    @FXML private TableColumn<UserRecord, String> roleColumn;
    @FXML private TableColumn<UserRecord, String> statusColumn;
    @FXML private TableColumn<UserRecord, String> lastLoginColumn;
    @FXML private TableColumn<UserRecord, String> createdDateColumn;
    @FXML private TableColumn<UserRecord, String> actionsColumn;
    
    // User Form Dialog
    @FXML private StackPane userFormDialog;
    @FXML private Label dialogTitle;
    @FXML private TextField usernameField;
    @FXML private TextField fullNameField;
    @FXML private ComboBox<String> roleCombo;
    @FXML private ComboBox<String> statusCombo;
    @FXML private PasswordField passwordField;
    @FXML private PasswordField confirmPasswordField;
    @FXML private TextField emailField;
    @FXML private TextField phoneField;
    
    private ObservableList<UserRecord> usersList = FXCollections.observableArrayList();
    private ObservableList<UserRecord> allUsers = FXCollections.observableArrayList(); // Store original users list for filtering
    private UserRecord editingUser = null;
    
    @FXML
    private void initialize() {
        setupTableColumns();
        loadSampleUsers();
        setupFilters();
        setupModalKeyboardHandling();
        setupComboBoxes();
    }
    
    private void setupTableColumns() {
        userIdColumn.setCellValueFactory(new PropertyValueFactory<>("id"));
        usernameColumn.setCellValueFactory(new PropertyValueFactory<>("username"));
        fullNameColumn.setCellValueFactory(new PropertyValueFactory<>("fullName"));
        roleColumn.setCellValueFactory(new PropertyValueFactory<>("role"));
        statusColumn.setCellValueFactory(new PropertyValueFactory<>("status"));
        lastLoginColumn.setCellValueFactory(new PropertyValueFactory<>("lastLogin"));
        createdDateColumn.setCellValueFactory(new PropertyValueFactory<>("createdDate"));

        // Configure actions column
        actionsColumn.setResizable(false);
        actionsColumn.setSortable(false);

        // Setup actions column with buttons
        actionsColumn.setCellFactory(column -> {
            System.out.println("Creating new table cell for actions column");
            return new TableCell<UserRecord, String>() {
                private final Button editBtn = new Button("Edit");
                private final Button deleteBtn = new Button("Delete");
                private final HBox buttonContainer = new HBox(5);

                {
                    System.out.println("Initializing table cell buttons");

                    // Set button tooltips
                    editBtn.setTooltip(new Tooltip("Edit User"));
                    deleteBtn.setTooltip(new Tooltip("Delete User"));

                    // Set button actions
                    editBtn.setOnAction(e -> {
                        UserRecord user = getTableRow().getItem();
                        System.out.println("=== EDIT BUTTON CLICKED ===");
                        System.out.println("Edit button clicked for user: " + (user != null ? user.getUsername() : "null"));
                        if (user != null) {
                            editUser(user);
                        } else {
                            showAlert("Error", "No user selected");
                        }
                    });
                    deleteBtn.setOnAction(e -> {
                        UserRecord user = getTableRow().getItem();
                        System.out.println("=== DELETE BUTTON CLICKED ===");
                        System.out.println("Delete button clicked for user: " + (user != null ? user.getUsername() : "null"));
                        if (user != null) {
                            deleteUser(user);
                        } else {
                            showAlert("Error", "No user selected");
                        }
                    });

                    System.out.println("Button actions set successfully");

                    // Style buttons - compact size
                    editBtn.getStyleClass().addAll("table-action-button", "edit-button");
                    deleteBtn.getStyleClass().addAll("table-action-button", "delete-button");

                    // Set compact button sizes
                    editBtn.setMinSize(50, 26);
                    editBtn.setPrefSize(50, 26);
                    editBtn.setMaxSize(50, 26);

                    deleteBtn.setMinSize(50, 26);
                    deleteBtn.setPrefSize(50, 26);
                    deleteBtn.setMaxSize(50, 26);

                    // Configure container with spacing
                    buttonContainer.setAlignment(Pos.CENTER);
                    buttonContainer.setMaxWidth(110);
                    buttonContainer.setPrefWidth(110);
                    buttonContainer.getChildren().addAll(editBtn, deleteBtn);

                    System.out.println("Button container configured with " + buttonContainer.getChildren().size() + " buttons");
                }
            
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || getTableRow() == null || getTableRow().getItem() == null) {
                    setGraphic(null);
                    System.out.println("Table cell is empty, hiding buttons");
                } else {
                    setGraphic(buttonContainer);
                    System.out.println("Table cell updated with buttons for user: " + getTableRow().getItem().getUsername());
                }
            }
        };
        });
        
        usersTable.setItems(usersList);
    }
    
    private void setupFilters() {
        // Populate filter ComboBoxes
        roleFilterCombo.setItems(FXCollections.observableArrayList(
            "All Roles", "ADMIN", "CASHIER", "WAITER"
        ));
        statusFilterCombo.setItems(FXCollections.observableArrayList(
            "All Status", "Active", "Inactive"
        ));

        // Populate form ComboBoxes
        roleCombo.setItems(FXCollections.observableArrayList(
            "ADMIN", "CASHIER", "WAITER"
        ));
        statusCombo.setItems(FXCollections.observableArrayList(
            "Active", "Inactive"
        ));

        // Set default values
        roleFilterCombo.setValue("All Roles");
        statusFilterCombo.setValue("All Status");

        // Add real-time search functionality
        System.out.println("Adding real-time search listeners...");
        searchField.textProperty().addListener((obs, oldVal, newVal) -> {
            System.out.println("Search field changed: '" + newVal + "'");
            if (newVal != null && !newVal.equals(oldVal)) {
                // Apply local filtering to current table data (no database calls)
                applyLocalSearch(newVal.trim(), roleFilterCombo.getValue(), statusFilterCombo.getValue());
            }
        });

        roleFilterCombo.valueProperty().addListener((obs, oldVal, newVal) -> {
            System.out.println("Role filter changed: '" + newVal + "'");
            applyLocalSearch(searchField.getText(), newVal, statusFilterCombo.getValue());
        });

        statusFilterCombo.valueProperty().addListener((obs, oldVal, newVal) -> {
            System.out.println("Status filter changed: '" + newVal + "'");
            applyLocalSearch(searchField.getText(), roleFilterCombo.getValue(), newVal);
        });

        System.out.println("Real-time search listeners added successfully");
    }
    
    private void loadSampleUsers() {
        usersList.clear();
        usersList.addAll(
            new UserRecord(1, "admin", "System Administrator", "ADMIN", "Active", "2025-07-06 18:30", "2025-01-01"),
            new UserRecord(2, "staff", "John Doe", "STAFF", "Active", "2025-07-06 17:45", "2025-01-15"),
            new UserRecord(3, "cashier1", "Jane Smith", "CASHIER", "Active", "2025-07-06 16:20", "2025-02-01"),
            new UserRecord(4, "waiter1", "Mike Johnson", "WAITER", "Active", "2025-07-06 18:00", "2025-02-15"),
            new UserRecord(5, "waiter2", "Sarah Wilson", "WAITER", "Inactive", "2025-07-05 14:30", "2025-03-01")
        );

        // Update the search backup list
        allUsers.clear();
        allUsers.addAll(usersList);
        System.out.println("Sample users loaded. Total users: " + allUsers.size());
    }
    
    @FXML
    private void addNewUser() {
        System.out.println("Add User button clicked - showing modal");
        showAddUserModal();
    }

    private void showAddUserModal() {
        try {
            // Create modal form content
            VBox modalContent = createAddUserForm();

            // Show modal using ModalManager
            Stage primaryStage = (Stage) usersTable.getScene().getWindow();
            ModalManager.getInstance().setPrimaryStage(primaryStage);
            ModalManager.getInstance().createModal("Add New User", modalContent, 600, 500);

            System.out.println("Add User modal displayed successfully");

        } catch (Exception e) {
            System.err.println("Error showing Add User modal: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Error showing Add User modal: " + e.getMessage());
        }
    }

    private VBox createAddUserForm() {
        VBox form = new VBox();
        form.getStyleClass().add("modal-form");
        form.setSpacing(15);

        // Username field
        HBox usernameRow = new HBox();
        usernameRow.getStyleClass().add("form-row");
        Label usernameLabel = new Label("Username:");
        usernameLabel.getStyleClass().add("form-label");
        TextField usernameField = new TextField();
        usernameField.getStyleClass().add("form-field");
        usernameField.setPromptText("Enter username");
        usernameRow.getChildren().addAll(usernameLabel, usernameField);

        // Full Name field
        HBox nameRow = new HBox();
        nameRow.getStyleClass().add("form-row");
        Label nameLabel = new Label("Full Name:");
        nameLabel.getStyleClass().add("form-label");
        TextField nameField = new TextField();
        nameField.getStyleClass().add("form-field");
        nameField.setPromptText("Enter full name");
        nameRow.getChildren().addAll(nameLabel, nameField);

        // Role field
        HBox roleRow = new HBox();
        roleRow.getStyleClass().add("form-row");
        Label roleLabel = new Label("Role:");
        roleLabel.getStyleClass().add("form-label");
        ComboBox<String> roleCombo = new ComboBox<>();
        roleCombo.getStyleClass().add("form-field");
        roleCombo.getItems().addAll("ADMIN", "MANAGER", "WAITER", "CHEF", "CASHIER");
        roleCombo.setValue("WAITER");
        roleRow.getChildren().addAll(roleLabel, roleCombo);

        // Password field
        HBox passwordRow = new HBox();
        passwordRow.getStyleClass().add("form-row");
        Label passwordLabel = new Label("Password:");
        passwordLabel.getStyleClass().add("form-label");
        PasswordField passwordField = new PasswordField();
        passwordField.getStyleClass().add("form-field");
        passwordField.setPromptText("Enter password");
        passwordRow.getChildren().addAll(passwordLabel, passwordField);

        // Status field
        HBox statusRow = new HBox();
        statusRow.getStyleClass().add("form-row");
        Label statusLabel = new Label("Status:");
        statusLabel.getStyleClass().add("form-label");
        ComboBox<String> statusCombo = new ComboBox<>();
        statusCombo.getStyleClass().add("form-field");
        statusCombo.getItems().addAll("Active", "Inactive");
        statusCombo.setValue("Active");
        statusRow.getChildren().addAll(statusLabel, statusCombo);

        // Buttons
        HBox buttonRow = new HBox();
        buttonRow.getStyleClass().add("modal-buttons");
        Button saveButton = new Button("Save User");
        saveButton.getStyleClass().addAll("button", "save-button");
        Button cancelButton = new Button("Cancel");
        cancelButton.getStyleClass().addAll("button", "cancel-button");

        // Button actions
        saveButton.setOnAction(e -> {
            if (validateModalForm(usernameField, nameField, passwordField)) {
                saveNewUser(usernameField.getText(), nameField.getText(),
                          roleCombo.getValue(), passwordField.getText(), statusCombo.getValue());
            }
        });

        cancelButton.setOnAction(e -> {
            // Close the modal
            Stage modal = (Stage) cancelButton.getScene().getWindow();
            ModalManager.getInstance().closeModal(modal);
        });

        buttonRow.getChildren().addAll(saveButton, cancelButton);

        form.getChildren().addAll(usernameRow, nameRow, roleRow, passwordRow, statusRow, buttonRow);

        return form;
    }

    private boolean validateModalForm(TextField usernameField, TextField nameField, PasswordField passwordField) {
        if (usernameField.getText().trim().isEmpty()) {
            showAlert("Validation Error", "Username is required");
            return false;
        }
        if (nameField.getText().trim().isEmpty()) {
            showAlert("Validation Error", "Full name is required");
            return false;
        }
        if (passwordField.getText().trim().isEmpty()) {
            showAlert("Validation Error", "Password is required");
            return false;
        }
        return true;
    }

    private void saveNewUser(String username, String fullName, String role, String password, String status) {
        try {
            int newId = usersList.size() + 1;
            UserRecord newUser = new UserRecord(
                newId,
                username,
                fullName,
                role,
                status,
                "Never",
                "2025-07-06"
            );
            usersList.add(newUser);
            allUsers.add(newUser); // Also add to the search backup list

            // Close modal and show success
            showAlert("Success", "User added successfully!");

            // Refresh table with current filters
            applyLocalSearch(searchField.getText(), roleFilterCombo.getValue(), statusFilterCombo.getValue());

            System.out.println("New user added: " + username);

        } catch (Exception e) {
            System.err.println("Error saving user: " + e.getMessage());
            showAlert("Error", "Error saving user: " + e.getMessage());
        }
    }

    @FXML
    private void manageStaffRecord() {
        try {
            Parent staffRecordView = FXMLLoader.load(getClass().getResource("/fxml/StaffRecord.fxml"));
            Stage stage = (Stage) usersTable.getScene().getWindow();
            stage.getScene().setRoot(staffRecordView);
        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Could not load Staff Record Management");
        }
    }
    
    @FXML
    private void refreshUserList() {
        loadSampleUsers();
        applyLocalSearch(searchField.getText(), roleFilterCombo.getValue(), statusFilterCombo.getValue());
    }
    
    private void applyLocalSearch(String searchText, String roleFilter, String statusFilter) {
        try {
            System.out.println("Applying local search for: '" + searchText + "', role: '" + roleFilter + "', status: '" + statusFilter + "'");

            // Start with all users
            ObservableList<UserRecord> filteredUsers = FXCollections.observableArrayList();

            for (UserRecord user : allUsers) {
                boolean matches = true;

                // Apply search text filter
                if (searchText != null && !searchText.trim().isEmpty()) {
                    String searchLower = searchText.toLowerCase().trim();
                    boolean textMatches = false;

                    // Search in User ID
                    if (String.valueOf(user.getId()).toLowerCase().contains(searchLower)) {
                        textMatches = true;
                        System.out.println("Match found in User ID: " + user.getId());
                    }

                    // Search in Username
                    if (!textMatches && user.getUsername() != null &&
                        user.getUsername().toLowerCase().contains(searchLower)) {
                        textMatches = true;
                        System.out.println("Match found in Username: " + user.getUsername());
                    }

                    // Search in Full Name
                    if (!textMatches && user.getFullName() != null &&
                        user.getFullName().toLowerCase().contains(searchLower)) {
                        textMatches = true;
                        System.out.println("Match found in Full Name: " + user.getFullName());
                    }

                    // Search in Role
                    if (!textMatches && user.getRole() != null &&
                        user.getRole().toLowerCase().contains(searchLower)) {
                        textMatches = true;
                        System.out.println("Match found in Role: " + user.getRole());
                    }

                    // Search in Status
                    if (!textMatches && user.getStatus() != null &&
                        user.getStatus().toLowerCase().contains(searchLower)) {
                        textMatches = true;
                        System.out.println("Match found in Status: " + user.getStatus());
                    }

                    matches = textMatches;
                }

                // Apply role filter
                if (matches && roleFilter != null && !roleFilter.equals("All Roles")) {
                    if (!roleFilter.equals(user.getRole())) {
                        matches = false;
                    }
                }

                // Apply status filter
                if (matches && statusFilter != null && !statusFilter.equals("All Status")) {
                    if (!statusFilter.equals(user.getStatus())) {
                        matches = false;
                    }
                }

                if (matches) {
                    filteredUsers.add(user);
                }
            }

            // Update the table with filtered results
            usersTable.setItems(filteredUsers);
            System.out.println("Search completed. Found " + filteredUsers.size() + " matching users out of " + allUsers.size() + " total users");

        } catch (Exception e) {
            System.err.println("Error in applyLocalSearch: " + e.getMessage());
            e.printStackTrace();
            // On error, show all users
            usersTable.setItems(allUsers);
        }
    }

    private void applyFilters() {
        // Legacy method - now calls the new search method
        applyLocalSearch(searchField.getText(), roleFilterCombo.getValue(), statusFilterCombo.getValue());
    }
    
    private void editUser(UserRecord user) {
        if (user == null) {
            System.out.println("ERROR: editUser called with null user");
            return;
        }

        System.out.println("editUser called for user: " + user.getUsername());
        editingUser = user;

        try {
            // Create a simple edit dialog using TextInputDialog for testing
            Alert editAlert = new Alert(Alert.AlertType.CONFIRMATION);
            editAlert.setTitle("Edit User");
            editAlert.setHeaderText("Edit User: " + user.getUsername());

            // Create form fields
            TextField fullNameField = new TextField(user.getFullName());
            ComboBox<String> roleCombo = new ComboBox<>();
            roleCombo.getItems().addAll("ADMIN", "CASHIER", "WAITER");
            roleCombo.setValue(user.getRole());

            ComboBox<String> statusCombo = new ComboBox<>();
            statusCombo.getItems().addAll("Active", "Inactive");
            statusCombo.setValue(user.getStatus());

            VBox content = new VBox(10);
            content.getChildren().addAll(
                new Label("Full Name:"), fullNameField,
                new Label("Role:"), roleCombo,
                new Label("Status:"), statusCombo
            );

            editAlert.getDialogPane().setContent(content);

            Optional<ButtonType> result = editAlert.showAndWait();
            if (result.isPresent() && result.get() == ButtonType.OK) {
                // Update user
                user.setFullName(fullNameField.getText());
                user.setRole(roleCombo.getValue());
                user.setStatus(statusCombo.getValue());

                // Refresh table
                applyLocalSearch(searchField.getText(), roleFilterCombo.getValue(), statusFilterCombo.getValue());
                showAlert("Success", "User updated successfully!");
                System.out.println("User updated: " + user.getUsername());
            }

        } catch (Exception e) {
            System.err.println("Error in editUser: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Error opening edit dialog: " + e.getMessage());
        }
    }
    
    private void deleteUser(UserRecord user) {
        if (user == null) return;
        
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Delete User");
        alert.setHeaderText("Are you sure you want to delete this user?");
        alert.setContentText("User: " + user.getFullName() + " (" + user.getUsername() + ")");
        
        alert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                usersList.remove(user);
                allUsers.remove(user); // Also remove from the search backup list
                // Refresh table with current filters
                applyLocalSearch(searchField.getText(), roleFilterCombo.getValue(), statusFilterCombo.getValue());
                showAlert("Success", "User deleted successfully!");
                System.out.println("User deleted: " + user.getUsername());
            }
        });
    }
    
    private void resetPassword(UserRecord user) {
        if (user == null) return;
        
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Reset Password");
        alert.setHeaderText("Reset password for user: " + user.getFullName());
        alert.setContentText("This will reset the password to a default value. Continue?");
        
        alert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                showAlert("Success", "Password reset successfully! New password: user123");
            }
        });
    }
    
    private void setupModalKeyboardHandling() {
        // Add keyboard event handler for ESC key to close modal
        userFormDialog.setOnKeyPressed(event -> {
            if (event.getCode() == KeyCode.ESCAPE) {
                closeUserDialog();
                event.consume();
            }
        });

        // Make modal focusable to receive key events
        userFormDialog.setFocusTraversable(true);
    }

    private void setupComboBoxes() {
        // Setup role combo box
        roleCombo.setItems(FXCollections.observableArrayList(
            "ADMIN", "MANAGER", "STAFF", "CASHIER", "WAITER"
        ));

        // Setup status combo box
        statusCombo.setItems(FXCollections.observableArrayList(
            "Active", "Inactive"
        ));
        statusCombo.setValue("Active"); // Default value
    }

    private void showUserDialog() {
        try {
            System.out.println("showUserDialog called");

            if (userFormDialog == null) {
                System.err.println("ERROR: userFormDialog is null!");
                showAlert("Error", "User dialog not initialized");
                return;
            }

            System.out.println("Setting dialog visible and managed...");
            // Show the modal
            userFormDialog.setVisible(true);
            userFormDialog.setManaged(true);

            // Apply proper modal styling
            userFormDialog.setStyle("");

            System.out.println("Requesting focus...");
            // Request focus to enable keyboard handling
            userFormDialog.requestFocus();

            // TEMPORARY: Force dialog to front
            userFormDialog.toFront();
            System.out.println("TEMPORARY: Forced dialog to front");

            System.out.println("Preventing background interaction...");
            // Prevent background scrolling and interaction
            preventBackgroundInteraction(true);

            System.out.println("Setting up backdrop click handling...");
            // Setup backdrop click handling
            setupBackdropClickHandling();

            System.out.println("Applying responsive sizing...");
            // Apply responsive sizing based on screen size
            applyResponsiveSizing();

            System.out.println("User dialog should now be visible and focused");
        } catch (Exception e) {
            System.err.println("Error in showUserDialog: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Error showing user dialog: " + e.getMessage());
        }
    }

    @FXML
    private void closeUserDialog() {
        userFormDialog.setVisible(false);
        userFormDialog.setManaged(false);
        clearForm();

        // Re-enable background interaction
        preventBackgroundInteraction(false);

        // Clear event handlers to prevent memory leaks
        userFormDialog.setOnMouseClicked(null);
    }

    private void preventBackgroundInteraction(boolean prevent) {
        // Get the main content area (parent of the modal)
        if (userFormDialog.getParent() != null) {
            userFormDialog.getParent().setMouseTransparent(prevent);

            // Disable scrolling on background content
            if (prevent) {
                userFormDialog.getParent().setOnScroll(event -> event.consume());
            } else {
                userFormDialog.getParent().setOnScroll(null);
            }
        }
    }

    private void setupBackdropClickHandling() {
        userFormDialog.setOnMouseClicked(event -> {
            // Only close if clicking directly on the backdrop or modal overlay
            String targetClass = event.getTarget().getClass().getSimpleName();
            if (event.getTarget() == userFormDialog ||
                targetClass.equals("Region") ||
                targetClass.equals("StackPane")) {
                closeUserDialog();
                event.consume();
            }
        });
    }

    private void applyResponsiveSizing() {
        // Get screen dimensions
        double screenHeight = userFormDialog.getScene().getWindow().getHeight();
        double screenWidth = userFormDialog.getScene().getWindow().getWidth();

        // Apply small screen styles if needed
        if (screenHeight < 600 || screenWidth < 800) {
            if (!userFormDialog.getStyleClass().contains("small-screen")) {
                userFormDialog.getStyleClass().add("small-screen");
            }
        } else {
            userFormDialog.getStyleClass().remove("small-screen");
        }
    }
    
    @FXML
    private void saveUser() {
        System.out.println("=== SAVE USER BUTTON CLICKED ===");
        System.out.println("Form validation starting...");

        if (!validateForm()) {
            System.out.println("Form validation failed");
            return;
        }

        System.out.println("Form validation passed");

        if (editingUser == null) {
            // Add new user
            System.out.println("Adding new user...");
            int newId = usersList.size() + 1;
            UserRecord newUser = new UserRecord(
                newId,
                usernameField.getText(),
                fullNameField.getText(),
                roleCombo.getValue(),
                statusCombo.getValue(),
                "Never",
                "2025-07-06"
            );
            usersList.add(newUser);
            allUsers.add(newUser); // Also add to search backup list
            System.out.println("New user added: " + newUser.getUsername());
            showAlert("Success", "User added successfully!");
        } else {
            // Update existing user
            System.out.println("Updating existing user: " + editingUser.getUsername());
            System.out.println("Old values - Full Name: " + editingUser.getFullName() + ", Role: " + editingUser.getRole() + ", Status: " + editingUser.getStatus());

            editingUser.setFullName(fullNameField.getText());
            editingUser.setRole(roleCombo.getValue());
            editingUser.setStatus(statusCombo.getValue());

            System.out.println("New values - Full Name: " + editingUser.getFullName() + ", Role: " + editingUser.getRole() + ", Status: " + editingUser.getStatus());

            // Refresh table with current filters to show updated data
            applyLocalSearch(searchField.getText(), roleFilterCombo.getValue(), statusFilterCombo.getValue());
            showAlert("Success", "User updated successfully!");
            System.out.println("User updated successfully: " + editingUser.getUsername());
        }

        System.out.println("Closing user dialog...");
        closeUserDialog();
        System.out.println("Save operation completed");
    }
    
    private boolean validateForm() {
        if (usernameField.getText().trim().isEmpty()) {
            showAlert("Validation Error", "Username is required!");
            return false;
        }
        if (fullNameField.getText().trim().isEmpty()) {
            showAlert("Validation Error", "Full name is required!");
            return false;
        }
        if (roleCombo.getValue() == null) {
            showAlert("Validation Error", "Please select a role!");
            return false;
        }
        if (editingUser == null && passwordField.getText().trim().isEmpty()) {
            showAlert("Validation Error", "Password is required for new users!");
            return false;
        }
        if (!passwordField.getText().equals(confirmPasswordField.getText())) {
            showAlert("Validation Error", "Passwords do not match!");
            return false;
        }
        return true;
    }
    
    private void clearForm() {
        usernameField.clear();
        fullNameField.clear();
        roleCombo.setValue(null);
        statusCombo.setValue("Active");
        passwordField.clear();
        confirmPasswordField.clear();
        emailField.clear();
        phoneField.clear();
    }
    
    private void populateForm(UserRecord user) {
        try {
            System.out.println("populateForm: Starting to populate form for user: " + user.getUsername());

            System.out.println("populateForm: Setting username field...");
            usernameField.setText(user.getUsername());

            System.out.println("populateForm: Setting full name field...");
            fullNameField.setText(user.getFullName());

            System.out.println("populateForm: Setting role combo...");
            roleCombo.setValue(user.getRole());

            System.out.println("populateForm: Setting status combo...");
            statusCombo.setValue(user.getStatus());

            System.out.println("populateForm: Clearing password fields...");
            // Don't populate password fields for security
            passwordField.clear();
            confirmPasswordField.clear();

            System.out.println("populateForm: Clearing email and phone fields...");
            emailField.clear(); // Would come from database
            phoneField.clear(); // Would come from database

            System.out.println("populateForm: Form population completed successfully");
        } catch (Exception e) {
            System.err.println("ERROR in populateForm: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    @FXML
    private void goBack() {
        try {
            // Navigate back to dashboard
            Parent dashboardView = FXMLLoader.load(getClass().getResource("/fxml/Dashboard.fxml"));
            Stage stage = (Stage) searchField.getScene().getWindow();
            stage.getScene().setRoot(dashboardView);
        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Could not return to dashboard");
        }
    }

    // Inner class for user records
    public static class UserRecord {
        private int id;
        private String username;
        private String fullName;
        private String role;
        private String status;
        private String lastLogin;
        private String createdDate;
        
        public UserRecord(int id, String username, String fullName, String role, String status, String lastLogin, String createdDate) {
            this.id = id;
            this.username = username;
            this.fullName = fullName;
            this.role = role;
            this.status = status;
            this.lastLogin = lastLogin;
            this.createdDate = createdDate;
        }
        
        // Getters and setters
        public int getId() { return id; }
        public void setId(int id) { this.id = id; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getFullName() { return fullName; }
        public void setFullName(String fullName) { this.fullName = fullName; }
        
        public String getRole() { return role; }
        public void setRole(String role) { this.role = role; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public String getLastLogin() { return lastLogin; }
        public void setLastLogin(String lastLogin) { this.lastLogin = lastLogin; }
        
        public String getCreatedDate() { return createdDate; }
        public void setCreatedDate(String createdDate) { this.createdDate = createdDate; }
    }
}
