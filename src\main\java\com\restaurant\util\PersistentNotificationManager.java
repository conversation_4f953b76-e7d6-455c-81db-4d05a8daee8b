package com.restaurant.util;

import com.restaurant.model.OnlineOrder;
import javafx.application.Platform;
import javafx.scene.control.Alert;
import javafx.scene.control.ButtonType;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * Manages persistent notifications for unaccepted orders
 * Provides continuous ringing until orders are accepted
 */
public class PersistentNotificationManager {
    
    private static PersistentNotificationManager instance;
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    private final ConcurrentHashMap<String, ScheduledFuture<?>> activeAlerts = new ConcurrentHashMap<>();
    private final NotificationManager notificationManager;
    
    // Ring settings
    private static final int RING_INTERVAL_SECONDS = 10; // Ring every 10 seconds
    private static final int URGENT_RING_INTERVAL_SECONDS = 5; // Urgent ring every 5 seconds
    private static final int MAX_RING_DURATION_MINUTES = 30; // Stop after 30 minutes
    
    private PersistentNotificationManager() {
        this.notificationManager = NotificationManager.getInstance();
    }
    
    public static PersistentNotificationManager getInstance() {
        if (instance == null) {
            instance = new PersistentNotificationManager();
        }
        return instance;
    }
    
    /**
     * Start continuous ringing for a new unaccepted order
     */
    public void startOrderAlert(OnlineOrder order) {
        String orderId = order.getOrderId();
        
        // Don't start if already ringing
        if (activeAlerts.containsKey(orderId)) {
            System.out.println("Alert already active for order: " + orderId);
            return;
        }
        
        System.out.println("Starting persistent alert for NEW order: " + orderId + " (" + order.getPlatform() + ")");
        
        // Initial immediate notification with platform-specific sound
        if (order.getPlatform().name().equals("SWIGGY")) {
            notificationManager.showNotification(
                NotificationManager.NotificationType.SWIGGY_ORDER,
                "🟠 NEW SWIGGY ORDER - URGENT!",
                "Swiggy Order " + orderId + " needs IMMEDIATE ACCEPTANCE!\nCustomer: " + order.getCustomerName()
            );
            // Start continuous MP3 ringing for Swiggy
            MP3AudioPlayer.getInstance().startSwiggyRinging(orderId);
        } else if (order.getPlatform().name().equals("ZOMATO")) {
            notificationManager.showNotification(
                NotificationManager.NotificationType.ZOMATO_ORDER,
                "🔴 NEW ZOMATO ORDER - URGENT!",
                "Zomato Order " + orderId + " needs IMMEDIATE ACCEPTANCE!\nCustomer: " + order.getCustomerName()
            );
            // Start continuous MP3 ringing for Zomato
            MP3AudioPlayer.getInstance().startZomatoRinging(orderId);
        } else {
            notificationManager.showNotification(
                NotificationManager.NotificationType.URGENT,
                "🚨 NEW " + order.getPlatform() + " ORDER",
                "Order " + orderId + " needs IMMEDIATE ACCEPTANCE!\nCustomer: " + order.getCustomerName()
            );
        }
        
        // Start continuous visual alerts (MP3 audio is already looping)
        ScheduledFuture<?> alertTask = scheduler.scheduleAtFixedRate(() -> {
            try {
                // Show visual alert with platform-specific styling
                Platform.runLater(() -> {
                    if (order.getPlatform().name().equals("SWIGGY")) {
                        notificationManager.showNotification(
                            NotificationManager.NotificationType.SWIGGY_ORDER,
                            "🟠 SWIGGY ORDER RINGING!",
                            "Swiggy Order " + orderId + " still waiting for acceptance!\n" +
                            "Customer: " + order.getCustomerName() + "\nAmount: ₹" + order.getTotalAmount()
                        );
                    } else if (order.getPlatform().name().equals("ZOMATO")) {
                        notificationManager.showNotification(
                            NotificationManager.NotificationType.ZOMATO_ORDER,
                            "🔴 ZOMATO ORDER RINGING!",
                            "Zomato Order " + orderId + " still waiting for acceptance!\n" +
                            "Customer: " + order.getCustomerName() + "\nAmount: ₹" + order.getTotalAmount()
                        );
                    } else {
                        notificationManager.showNotification(
                            NotificationManager.NotificationType.URGENT,
                            "🔔 UNACCEPTED ORDER ALERT",
                            "Order " + orderId + " (" + order.getPlatform() + ") still waiting for acceptance!\n" +
                            "Customer: " + order.getCustomerName() + "\nAmount: ₹" + order.getTotalAmount()
                        );
                    }
                });

                System.out.println("VISUAL ALERT: " + order.getPlatform() + " Order " + orderId + " still unaccepted! (MP3 audio looping)");

            } catch (Exception e) {
                System.err.println("Error in persistent alert for order " + orderId + ": " + e.getMessage());
            }
        }, RING_INTERVAL_SECONDS, RING_INTERVAL_SECONDS, TimeUnit.SECONDS);
        
        // Store the alert task
        activeAlerts.put(orderId, alertTask);
        
        // Auto-stop after maximum duration
        scheduler.schedule(() -> {
            stopOrderAlert(orderId);
            Platform.runLater(() -> {
                notificationManager.showNotification(
                    NotificationManager.NotificationType.WARNING,
                    "⏰ ORDER TIMEOUT",
                    "Order " + orderId + " alert stopped after " + MAX_RING_DURATION_MINUTES + " minutes"
                );
            });
        }, MAX_RING_DURATION_MINUTES, TimeUnit.MINUTES);
        
        // Show acceptance dialog
        showOrderAcceptanceDialog(order);
    }
    
    /**
     * Stop continuous ringing for an order (when accepted)
     */
    public void stopOrderAlert(String orderId) {
        ScheduledFuture<?> alertTask = activeAlerts.remove(orderId);
        if (alertTask != null) {
            alertTask.cancel(false);
            System.out.println("Stopped persistent alert for order: " + orderId);

            // Stop MP3 ringing
            MP3AudioPlayer.getInstance().stopRinging(orderId);

            // Show acceptance confirmation
            Platform.runLater(() -> {
                notificationManager.showNotification(
                    NotificationManager.NotificationType.SUCCESS,
                    "✅ ORDER ACCEPTED",
                    "Order " + orderId + " has been accepted and moved to preparation"
                );
            });
        }
    }
    
    /**
     * Check if an order has active alert
     */
    public boolean hasActiveAlert(String orderId) {
        return activeAlerts.containsKey(orderId);
    }
    
    /**
     * Get count of active alerts
     */
    public int getActiveAlertCount() {
        return activeAlerts.size();
    }
    
    /**
     * Stop all active alerts
     */
    public void stopAllAlerts() {
        System.out.println("Stopping all persistent alerts (" + activeAlerts.size() + " active)");
        activeAlerts.values().forEach(task -> task.cancel(false));
        activeAlerts.clear();

        // Stop all MP3 audio
        MP3AudioPlayer.getInstance().stopAllAudio();
    }
    
    /**
     * Play platform-specific urgent ring sound pattern
     */
    private void playPlatformSpecificRingSound(String platform) {
        try {
            if ("SWIGGY".equals(platform)) {
                playSwiggyRingSound();
            } else if ("ZOMATO".equals(platform)) {
                playZomatoRingSound();
            } else {
                playUrgentRingSound();
            }
        } catch (Exception e) {
            System.err.println("Error playing platform-specific ring sound: " + e.getMessage());
        }
    }

    /**
     * Play Swiggy-specific loud ring sound
     */
    private void playSwiggyRingSound() {
        try {
            System.out.println("🟠 SWIGGY RING: LOUD 4-2 PATTERN!");
            // SWIGGY: 4 rapid beeps, pause, 2 long beeps, pause, repeat
            for (int cycle = 0; cycle < 2; cycle++) {
                // 4 rapid beeps
                for (int i = 0; i < 4; i++) {
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(80);
                }
                Thread.sleep(200);
                // 2 longer beeps
                for (int i = 0; i < 2; i++) {
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(300);
                }
                if (cycle < 1) Thread.sleep(400); // Pause between cycles
            }
        } catch (Exception e) {
            System.err.println("Error playing Swiggy ring sound: " + e.getMessage());
        }
    }

    /**
     * Play Zomato-specific loud ring sound
     */
    private void playZomatoRingSound() {
        try {
            System.out.println("🔴 ZOMATO RING: LOUD TRIPLE-DOUBLE PATTERN!");
            // ZOMATO: 3 sets of double beeps with increasing intensity
            for (int set = 0; set < 3; set++) {
                for (int pair = 0; pair < 2; pair++) {
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(90);
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(150);
                }
                Thread.sleep(250); // Pause between sets
            }
        } catch (Exception e) {
            System.err.println("Error playing Zomato ring sound: " + e.getMessage());
        }
    }

    /**
     * Play generic urgent ring sound pattern
     */
    private void playUrgentRingSound() {
        try {
            // Urgent ring pattern: 3 rapid beeps, pause, 3 rapid beeps
            for (int cycle = 0; cycle < 2; cycle++) {
                for (int i = 0; i < 3; i++) {
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(100); // Very short pause between beeps
                }
                Thread.sleep(300); // Pause between cycles
            }
        } catch (Exception e) {
            System.err.println("Error playing urgent ring sound: " + e.getMessage());
        }
    }
    
    /**
     * Show order acceptance dialog
     */
    private void showOrderAcceptanceDialog(OnlineOrder order) {
        // DISABLED: Order acceptance now handled in notifications panel only
        System.out.println("📱 Order acceptance dialog disabled - use notifications panel");
        System.out.println("🔔 Order " + order.getOrderId() + " (" + order.getPlatform().name() +
                          ") - Accept/Reject in notifications panel");

        // No dialog shown - all order acceptance happens in notifications panel
    }
    
    /**
     * Accept order and move to PREPARING status
     */
    private void acceptOrder(OnlineOrder order) {
        stopOrderAlert(order.getOrderId());
        // This will be handled by the controller to update status to PREPARING
        notificationManager.showNotification(
            NotificationManager.NotificationType.SUCCESS,
            "✅ ORDER ACCEPTED",
            "Order " + order.getOrderId() + " accepted and moved to kitchen preparation"
        );
    }
    
    /**
     * Reject order
     */
    private void rejectOrder(OnlineOrder order) {
        stopOrderAlert(order.getOrderId());
        notificationManager.showNotification(
            NotificationManager.NotificationType.WARNING,
            "❌ ORDER REJECTED",
            "Order " + order.getOrderId() + " has been rejected"
        );
    }
    
    /**
     * Snooze order for 5 minutes
     */
    private void snoozeOrder(OnlineOrder order) {
        stopOrderAlert(order.getOrderId());
        
        // Restart alert after 5 minutes
        scheduler.schedule(() -> {
            if (order.getStatus() == OnlineOrder.OrderStatus.NEW) {
                startOrderAlert(order);
            }
        }, 5, TimeUnit.MINUTES);
        
        notificationManager.showNotification(
            NotificationManager.NotificationType.WARNING,
            "⏰ ORDER SNOOZED",
            "Order " + order.getOrderId() + " alert snoozed for 5 minutes"
        );
    }
    
    /**
     * Get summary of order items
     */
    private String getOrderItemsSummary(OnlineOrder order) {
        if (order.getItems() == null || order.getItems().isEmpty()) {
            return "No items specified";
        }
        
        StringBuilder summary = new StringBuilder();
        order.getItems().forEach(item -> {
            if (summary.length() > 0) summary.append(", ");
            summary.append(item.getItemName()).append(" x").append(item.getQuantity());
        });
        
        return summary.toString();
    }
    
    /**
     * Shutdown the persistent notification manager
     */
    public void shutdown() {
        stopAllAlerts();
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
        }
    }
}
