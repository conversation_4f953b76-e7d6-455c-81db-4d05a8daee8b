package com.restaurant.model;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import com.restaurant.util.MenuShortcuts;

public class MenuDAO {
    
    public static List<MenuItem> getAllMenuItems() {
        List<MenuItem> items = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(
                     "SELECT m.id, m.name, m.price, c.name as category, m.category_id " +
                     "FROM menu_items m JOIN menu_categories c ON m.category_id = c.id")) {
            
            while (rs.next()) {
                items.add(new MenuItem(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getDouble("price"),
                    rs.getString("category"),
                    rs.getInt("category_id")
                ));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return items;
    }
    
    public static List<MenuCategory> getAllCategories() {
        List<MenuCategory> categories = new ArrayList<>();

        try (Connection conn = DatabaseManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT * FROM menu_categories ORDER BY name")) {

            while (rs.next()) {
                categories.add(new MenuCategory(
                    rs.getInt("id"),
                    rs.getString("name")
                ));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return categories;
    }

    public static boolean addMenuItem(String name, double price, int categoryId) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "INSERT INTO menu_items (name, price, category_id) VALUES (?, ?, ?)")) {

            ps.setString(1, name);
            ps.setDouble(2, price);
            ps.setInt(3, categoryId);

            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    public static boolean updateMenuItem(int id, String name, double price, int categoryId) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "UPDATE menu_items SET name = ?, price = ?, category_id = ? WHERE id = ?")) {

            ps.setString(1, name);
            ps.setDouble(2, price);
            ps.setInt(3, categoryId);
            ps.setInt(4, id);

            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    public static boolean deleteMenuItem(int id) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("DELETE FROM menu_items WHERE id = ?")) {

            ps.setInt(1, id);
            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    public static boolean addCategory(String name) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "INSERT INTO menu_categories (name) VALUES (?)")) {

            ps.setString(1, name);
            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    public static boolean deleteCategory(int id) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("DELETE FROM menu_categories WHERE id = ?")) {

            ps.setInt(1, id);
            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    public static List<MenuItem> getMenuItemsByCategory(int categoryId) {
        List<MenuItem> items = new ArrayList<>();

        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT m.id, m.name, m.price, c.name as category, m.category_id " +
                     "FROM menu_items m JOIN menu_categories c ON m.category_id = c.id " +
                     "WHERE m.category_id = ?")) {

            ps.setInt(1, categoryId);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                items.add(new MenuItem(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getDouble("price"),
                    rs.getString("category"),
                    rs.getInt("category_id")
                ));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return items;
    }

    public static List<MenuItem> searchMenuItems(String searchTerm) {
        List<MenuItem> items = new ArrayList<>();

        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT m.id, m.name, m.price, c.name as category, m.category_id " +
                     "FROM menu_items m JOIN menu_categories c ON m.category_id = c.id " +
                     "WHERE m.name LIKE ? ORDER BY m.name")) {

            ps.setString(1, "%" + searchTerm + "%");
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                items.add(new MenuItem(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getDouble("price"),
                    rs.getString("category"),
                    rs.getInt("category_id")
                ));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        // If no results found with direct search, try shortcut-based filtering
        if (items.isEmpty()) {
            // Get all items and filter using shortcut system
            List<MenuItem> allItems = getAllMenuItems();
            for (MenuItem item : allItems) {
                if (MenuShortcuts.matchesItem(item.getName(), searchTerm)) {
                    items.add(item);
                }
            }
        } else {
            // Even if we found direct matches, also include shortcut matches
            List<MenuItem> allItems = getAllMenuItems();
            for (MenuItem item : allItems) {
                if (MenuShortcuts.matchesItem(item.getName(), searchTerm) &&
                    !items.stream().anyMatch(existing -> existing.getId() == item.getId())) {
                    items.add(item);
                }
            }
        }

        return items;
    }
}