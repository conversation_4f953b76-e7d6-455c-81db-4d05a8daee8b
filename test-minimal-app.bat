@echo off
echo TESTING MINIMAL JAVAFX APPLICATION...
echo.

echo This test will determine if the issue is:
echo 1. JavaFX setup problem
echo 2. Complex application components problem
echo 3. System/environment problem
echo.

echo Step 1: Compiling minimal test application...
call mvn clean compile -q

if %ERRORLEVEL% neq 0 (
    echo ❌ COMPILATION FAILED
    echo Running detailed compilation:
    mvn clean compile
    pause
    exit /b 1
)

echo ✅ Compilation successful
echo.

echo Step 2: Testing minimal JavaFX application...
echo.
echo WHAT TO EXPECT:
echo ✅ Should see "MinimalTestApp: Starting main method..."
echo ✅ Should see "MinimalTestApp: Launching JavaFX application..."
echo ✅ Should see "MinimalTestApp: Starting JavaFX application..."
echo ✅ Should see "MinimalTestApp: Showing stage..."
echo ✅ Should see "MinimalTestApp: Stage shown successfully!"
echo ✅ A simple window should appear with a test button
echo.
echo If it gets stuck, note the LAST message you see!
echo.

timeout /t 3

java -Dprism.order=sw ^
     -Dprism.allowhidpi=false ^
     -Djavafx.animation.pulse=60 ^
     -Djava.awt.headless=false ^
     -Xms256m ^
     -Xmx1g ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics ^
     -cp "target/classes" ^
     com.restaurant.MinimalTestApp

echo.
echo MINIMAL TEST ANALYSIS:
echo.

if %ERRORLEVEL% equ 0 (
    echo ✅ MINIMAL APP WORKED!
    echo.
    echo CONCLUSION: JavaFX setup is working fine
    echo ISSUE: Complex components in main application are causing problems
    echo.
    echo NEXT STEPS:
    echo 1. The issue is NOT with JavaFX or system setup
    echo 2. The issue is with complex components in RestaurantApp
    echo 3. We need to simplify the main application step by step
    echo.
    echo COMPONENTS TO INVESTIGATE:
    echo - CSS loading and parsing
    echo - FXML file loading
    echo - Database initialization
    echo - Notification system
    echo - Complex UI components
    echo.
) else (
    echo ❌ MINIMAL APP FAILED!
    echo.
    echo CONCLUSION: Fundamental JavaFX or system issue
    echo ISSUE: Basic JavaFX is not working on this system
    echo.
    echo POSSIBLE CAUSES:
    echo 1. JavaFX modules not properly installed
    echo 2. Graphics driver issues
    echo 3. Java version compatibility problems
    echo 4. System permissions or security software blocking
    echo.
    echo NEXT STEPS:
    echo 1. Check Java and JavaFX versions
    echo 2. Update graphics drivers
    echo 3. Try different Java version
    echo 4. Check antivirus/firewall settings
    echo.
)

echo.
echo WHAT DID YOU SEE?
echo.
echo A. Window appeared and worked: JavaFX is fine, main app has complex issues
echo B. Got stuck at specific message: Note which message was last
echo C. Immediate error/crash: Fundamental JavaFX problem
echo D. Nothing happened: System/environment issue
echo.
pause
