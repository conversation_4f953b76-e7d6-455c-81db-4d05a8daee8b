package com.restaurant.util;

import com.restaurant.model.Attendance;
import com.restaurant.model.AttendanceDAO;
import javafx.stage.FileChooser;
import javafx.stage.Stage;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

public class AttendanceReportGenerator {
    
    public static void generateReport(LocalDate date) throws IOException {
        // Get attendance data for the specified date
        List<Attendance> attendanceList = AttendanceDAO.getAttendanceByDate(date);
        
        // Create file chooser
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Save Attendance Report");
        fileChooser.setInitialFileName("attendance_report_" + date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ".csv");
        fileChooser.getExtensionFilters().add(
            new FileChooser.ExtensionFilter("CSV Files", "*.csv")
        );
        
        // Show save dialog
        Stage stage = new Stage();
        File file = fileChooser.showSaveDialog(stage);
        
        if (file != null) {
            generateCSVReport(attendanceList, file, date);
        }
    }
    
    private static void generateCSVReport(List<Attendance> attendanceList, File file, LocalDate date) throws IOException {
        try (FileWriter writer = new FileWriter(file)) {
            // Write header
            writer.append("Attendance Report for ").append(date.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))).append("\n\n");
            writer.append("Employee ID,Employee Name,Department,Check In,Check Out,Hours Worked,Status\n");
            
            // Write data
            for (Attendance attendance : attendanceList) {
                writer.append(String.valueOf(attendance.getId())).append(",");
                writer.append(attendance.getEmployeeName()).append(",");
                writer.append(attendance.getDepartment()).append(",");
                writer.append(attendance.getCheckInTimeDisplay()).append(",");
                writer.append(attendance.getCheckOutTimeDisplay()).append(",");
                writer.append(attendance.getHoursWorkedDisplay()).append(",");
                writer.append(attendance.getStatus()).append("\n");
            }
            
            // Write summary
            writer.append("\n\nSummary:\n");
            long presentCount = attendanceList.stream().filter(a -> "Present".equals(a.getStatus()) || "Late".equals(a.getStatus()) || "Half Day".equals(a.getStatus())).count();
            long absentCount = attendanceList.stream().filter(a -> "Absent".equals(a.getStatus()) || "Sick Leave".equals(a.getStatus())).count();
            long lateCount = attendanceList.stream().filter(a -> "Late".equals(a.getStatus())).count();
            
            writer.append("Total Present: ").append(String.valueOf(presentCount)).append("\n");
            writer.append("Total Absent: ").append(String.valueOf(absentCount)).append("\n");
            writer.append("Total Late: ").append(String.valueOf(lateCount)).append("\n");
            writer.append("Total Records: ").append(String.valueOf(attendanceList.size())).append("\n");
        }
    }
}
