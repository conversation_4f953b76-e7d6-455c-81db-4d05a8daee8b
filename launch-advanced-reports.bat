@echo off
echo 📊 LAUNCHING ADVANCED REPORTS INTERFACE 📊
echo.

echo 🚀 CREATING LAUNCHER...

echo package com.restaurant.launcher; > src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo. >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo import javafx.application.Application; >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo import javafx.fxml.FXMLLoader; >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo import javafx.scene.Scene; >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo import javafx.stage.Stage; >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo. >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo public class AdvancedReportsLauncher extends Application { >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo. >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo     @Override >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo     public void start(Stage primaryStage) { >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo         try { >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo             System.out.println("🚀 Launching Advanced Reports Interface..."); >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo. >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo             FXMLLoader loader = new FXMLLoader(); >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo             loader.setLocation(getClass().getResource("/com/restaurant/view/advanced-reports.fxml")); >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo. >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo             Scene scene = new Scene(loader.load(), 1400, 900); >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo. >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo             primaryStage.setTitle("📊 Advanced Reports ^& Analytics - Restaurant Management"); >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo             primaryStage.setScene(scene); >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo             primaryStage.setMaximized(true); >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo             primaryStage.show(); >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo. >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo             System.out.println("✅ Advanced Reports Interface launched!"); >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo             System.out.println(); >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo             System.out.println("📊 AVAILABLE TABS:"); >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo             System.out.println("📈 Analytics Dashboard - Real-time metrics and charts"); >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo             System.out.println("📅 Daily Reports - Generate daily performance reports"); >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo             System.out.println("📊 Weekly Reports - Analyze weekly trends and patterns"); >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo             System.out.println("📆 Monthly Reports - Track monthly growth and insights"); >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo. >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo             System.err.println("❌ Error launching Advanced Reports: " + e.getMessage()); >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo             e.printStackTrace(); >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo         } >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo     } >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo. >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo     public static void main(String[] args) { >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo         System.out.println("📊 Starting Advanced Reports System..."); >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo         launch(args); >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo     } >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java
echo } >> src\main\java\com\restaurant\launcher\AdvancedReportsLauncher.java

echo ✅ Launcher created
echo.

echo 🔧 CREATING LAUNCHER DIRECTORY...
mkdir src\main\java\com\restaurant\launcher 2>nul

echo 🔧 COMPILING LAUNCHER...
javac -d target/classes -cp "target/classes;lib/*" src/main/java/com/restaurant/launcher/AdvancedReportsLauncher.java

if %ERRORLEVEL% neq 0 (
    echo ❌ Compilation failed
    pause
    exit /b 1
)

echo ✅ Compilation successful
echo.

echo 🚀 LAUNCHING ADVANCED REPORTS INTERFACE...
echo.
echo This will open a new window with 4 tabs:
echo 📈 Analytics Dashboard - Real-time business metrics
echo 📅 Daily Reports - Generate and view daily reports
echo 📊 Weekly Reports - Analyze weekly trends
echo 📆 Monthly Reports - Track monthly growth
echo.

java -Dprism.order=sw ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2-win.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics,javafx.media ^
     -cp "target/classes;lib/*" ^
     com.restaurant.launcher.AdvancedReportsLauncher

echo.
echo 🎉 ADVANCED REPORTS INTERFACE LAUNCHED!
echo.

pause
