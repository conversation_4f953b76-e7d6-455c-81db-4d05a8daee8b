package com.restaurant.controller;

import com.restaurant.model.*;
import com.restaurant.service.EnhancedReportService;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.chart.*;
import javafx.stage.FileChooser;

import java.io.File;
import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

public class EnhancedReportsController implements Initializable {
    
    // Filter Controls
    @FXML private DatePicker startDatePicker;
    @FXML private DatePicker endDatePicker;
    @FXML private ComboBox<String> reportTypeComboBox;
    @FXML private CheckBox allOrderTypesCheckBox;
    @FXML private CheckBox swiggyCheckBox;
    @FXML private CheckBox zomatoCheckBox;
    @FXML private CheckBox onlineCheckBox;
    @FXML private Button generateReportBtn;
    @FXML private Button exportReportBtn;
    
    // Summary Cards
    @FXML private Label totalOrdersLabel;
    @FXML private Label totalRevenueLabel;
    @FXML private Label avgOrderValueLabel;
    @FXML private Label peakHourLabel;
    @FXML private Label swiggyOrdersLabel;
    @FXML private Label zomatoOrdersLabel;
    @FXML private Label swiggyRevenueLabel;
    @FXML private Label zomatoRevenueLabel;
    
    // Charts
    @FXML private PieChart orderTypePieChart;
    @FXML private LineChart<String, Number> revenueTrendChart;
    @FXML private BarChart<String, Number> platformComparisonChart;
    
    // Data Table
    @FXML private TableView<DailyReport> reportsTable;
    @FXML private TableColumn<DailyReport, String> dateColumn;
    @FXML private TableColumn<DailyReport, Integer> ordersColumn;
    @FXML private TableColumn<DailyReport, Double> revenueColumn;
    @FXML private TableColumn<DailyReport, Integer> swiggyColumn;
    @FXML private TableColumn<DailyReport, Integer> zomatoColumn;
    @FXML private TableColumn<DailyReport, Double> avgValueColumn;
    
    private List<DailyReport> currentReports = new ArrayList<>();
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("EnhancedReportsController: Initializing...");
        
        try {
            setupControls();
            setupTable();
            setupCharts();
            setupEventHandlers();
            setDefaultValues();
            
            System.out.println("EnhancedReportsController: Initialization complete");
        } catch (Exception e) {
            System.err.println("Error initializing EnhancedReportsController: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void setupControls() {
        // Setup report type combo box
        ObservableList<String> reportTypes = FXCollections.observableArrayList(
            "Daily", "Weekly", "Monthly"
        );
        reportTypeComboBox.setItems(reportTypes);
        reportTypeComboBox.setValue("Daily");
        
        // Setup date pickers
        LocalDate today = LocalDate.now();
        endDatePicker.setValue(today);
        startDatePicker.setValue(today.minusDays(30)); // Default to last 30 days
        
        // Setup order type checkboxes
        allOrderTypesCheckBox.setSelected(true);
        swiggyCheckBox.setSelected(true);
        zomatoCheckBox.setSelected(true);
        onlineCheckBox.setSelected(true);
    }
    
    private void setupTable() {
        // Setup table columns
        dateColumn.setCellValueFactory(new PropertyValueFactory<>("reportDate"));
        ordersColumn.setCellValueFactory(new PropertyValueFactory<>("totalOrders"));
        revenueColumn.setCellValueFactory(new PropertyValueFactory<>("totalRevenue"));
        swiggyColumn.setCellValueFactory(new PropertyValueFactory<>("swiggyOrders"));
        zomatoColumn.setCellValueFactory(new PropertyValueFactory<>("zomatoOrders"));
        avgValueColumn.setCellValueFactory(new PropertyValueFactory<>("avgOrderValue"));
        
        // Format revenue columns
        revenueColumn.setCellFactory(column -> new TableCell<DailyReport, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText("₹" + String.format("%.2f", item));
                }
            }
        });
        
        avgValueColumn.setCellFactory(column -> new TableCell<DailyReport, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText("₹" + String.format("%.2f", item));
                }
            }
        });
    }
    
    private void setupCharts() {
        // Setup pie chart
        if (orderTypePieChart != null) {
            orderTypePieChart.setTitle("Order Distribution by Platform");
        }
        
        // Setup line chart
        if (revenueTrendChart != null) {
            CategoryAxis xAxis = (CategoryAxis) revenueTrendChart.getXAxis();
            NumberAxis yAxis = (NumberAxis) revenueTrendChart.getYAxis();
            xAxis.setLabel("Date/Period");
            yAxis.setLabel("Revenue (₹)");
            revenueTrendChart.setTitle("Revenue Trend");
        }
        
        // Setup bar chart
        if (platformComparisonChart != null) {
            CategoryAxis xAxis = (CategoryAxis) platformComparisonChart.getXAxis();
            NumberAxis yAxis = (NumberAxis) platformComparisonChart.getYAxis();
            xAxis.setLabel("Platform");
            yAxis.setLabel("Orders");
            platformComparisonChart.setTitle("Platform Comparison");
        }
    }
    
    private void setupEventHandlers() {
        if (generateReportBtn != null) {
            generateReportBtn.setOnAction(e -> generateReport());
        }
        
        if (exportReportBtn != null) {
            exportReportBtn.setOnAction(e -> exportReport());
        }
        
        // Handle "All Order Types" checkbox
        if (allOrderTypesCheckBox != null) {
            allOrderTypesCheckBox.setOnAction(e -> {
                boolean selected = allOrderTypesCheckBox.isSelected();
                swiggyCheckBox.setSelected(selected);
                zomatoCheckBox.setSelected(selected);
                onlineCheckBox.setSelected(selected);
            });
        }
        
        // Handle individual checkboxes
        if (swiggyCheckBox != null) {
            swiggyCheckBox.setOnAction(e -> updateAllCheckBox());
        }
        if (zomatoCheckBox != null) {
            zomatoCheckBox.setOnAction(e -> updateAllCheckBox());
        }
        if (onlineCheckBox != null) {
            onlineCheckBox.setOnAction(e -> updateAllCheckBox());
        }
    }
    
    private void updateAllCheckBox() {
        boolean allSelected = swiggyCheckBox.isSelected() && zomatoCheckBox.isSelected() && onlineCheckBox.isSelected();
        allOrderTypesCheckBox.setSelected(allSelected);
    }
    
    private void setDefaultValues() {
        // Generate initial report
        generateReport();
    }
    
    @FXML
    private void generateReport() {
        try {
            LocalDate startDate = startDatePicker.getValue();
            LocalDate endDate = endDatePicker.getValue();
            String reportType = reportTypeComboBox.getValue().toLowerCase();
            List<String> orderTypes = getSelectedOrderTypes();
            
            if (startDate == null || endDate == null) {
                showAlert("Error", "Please select both start and end dates.", Alert.AlertType.ERROR);
                return;
            }
            
            if (startDate.isAfter(endDate)) {
                showAlert("Error", "Start date cannot be after end date.", Alert.AlertType.ERROR);
                return;
            }
            
            // Generate filtered reports
            currentReports = EnhancedReportService.generateFilteredReports(startDate, endDate, orderTypes, reportType);
            reportsTable.setItems(FXCollections.observableArrayList(currentReports));
            
            // Update analytics summary
            updateAnalyticsSummary(startDate, endDate, orderTypes);
            
            // Update charts
            updateCharts(startDate, endDate, orderTypes, reportType);
            
            System.out.println("Report generated successfully for " + startDate + " to " + endDate + 
                             " with order types: " + orderTypes + " and report type: " + reportType);
            
        } catch (Exception e) {
            System.err.println("Error generating report: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Failed to generate report: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }
    
    private List<String> getSelectedOrderTypes() {
        List<String> orderTypes = new ArrayList<>();
        
        if (allOrderTypesCheckBox.isSelected()) {
            orderTypes.add("All");
        } else {
            if (swiggyCheckBox.isSelected()) {
                orderTypes.add("Swiggy");
            }
            if (zomatoCheckBox.isSelected()) {
                orderTypes.add("Zomato");
            }
            if (onlineCheckBox.isSelected()) {
                orderTypes.add("Online");
            }
        }
        
        return orderTypes;
    }
    
    private void updateAnalyticsSummary(LocalDate startDate, LocalDate endDate, List<String> orderTypes) {
        try {
            Map<String, Object> analytics = EnhancedReportService.getFilteredAnalyticsSummary(startDate, endDate, orderTypes);
            
            updateLabel(totalOrdersLabel, String.valueOf(analytics.get("totalOrders")));
            updateLabel(totalRevenueLabel, "₹" + String.format("%.2f", (Double) analytics.get("totalRevenue")));
            updateLabel(avgOrderValueLabel, "₹" + String.format("%.2f", (Double) analytics.get("avgOrderValue")));
            updateLabel(peakHourLabel, String.valueOf(analytics.get("peakHour")));
            updateLabel(swiggyOrdersLabel, String.valueOf(analytics.get("swiggyOrders")));
            updateLabel(zomatoOrdersLabel, String.valueOf(analytics.get("zomatoOrders")));
            updateLabel(swiggyRevenueLabel, "₹" + String.format("%.2f", (Double) analytics.get("swiggyRevenue")));
            updateLabel(zomatoRevenueLabel, "₹" + String.format("%.2f", (Double) analytics.get("zomatoRevenue")));
            
        } catch (Exception e) {
            System.err.println("Error updating analytics summary: " + e.getMessage());
        }
    }
    
    private void updateLabel(Label label, String text) {
        if (label != null) {
            label.setText(text);
        }
    }
    
    private void updateCharts(LocalDate startDate, LocalDate endDate, List<String> orderTypes, String reportType) {
        try {
            // Update pie chart
            updateOrderTypePieChart(startDate, endDate);
            
            // Update revenue trend chart
            updateRevenueTrendChart(startDate, endDate, orderTypes, reportType);
            
            // Update platform comparison chart
            updatePlatformComparisonChart(startDate, endDate, orderTypes);
            
        } catch (Exception e) {
            System.err.println("Error updating charts: " + e.getMessage());
        }
    }
    
    private void updateOrderTypePieChart(LocalDate startDate, LocalDate endDate) {
        if (orderTypePieChart != null) {
            try {
                Map<String, Integer> breakdown = EnhancedReportService.getOrderTypeBreakdown(startDate, endDate);
                
                ObservableList<PieChart.Data> pieChartData = FXCollections.observableArrayList();
                for (Map.Entry<String, Integer> entry : breakdown.entrySet()) {
                    pieChartData.add(new PieChart.Data(entry.getKey(), entry.getValue()));
                }
                
                orderTypePieChart.setData(pieChartData);
            } catch (Exception e) {
                System.err.println("Error updating pie chart: " + e.getMessage());
            }
        }
    }
    
    private void updateRevenueTrendChart(LocalDate startDate, LocalDate endDate, List<String> orderTypes, String reportType) {
        if (revenueTrendChart != null) {
            try {
                List<Map<String, Object>> trendData = EnhancedReportService.getRevenueTrendData(
                    startDate, endDate, orderTypes, reportType);
                
                XYChart.Series<String, Number> series = new XYChart.Series<>();
                series.setName("Revenue");
                
                for (Map<String, Object> dataPoint : trendData) {
                    series.getData().add(new XYChart.Data<>(
                        String.valueOf(dataPoint.get("period")),
                        (Number) dataPoint.get("revenue")
                    ));
                }
                
                revenueTrendChart.getData().clear();
                revenueTrendChart.getData().add(series);
            } catch (Exception e) {
                System.err.println("Error updating revenue trend chart: " + e.getMessage());
            }
        }
    }
    
    private void updatePlatformComparisonChart(LocalDate startDate, LocalDate endDate, List<String> orderTypes) {
        if (platformComparisonChart != null) {
            try {
                Map<String, Integer> breakdown = EnhancedReportService.getOrderTypeBreakdown(startDate, endDate);
                
                XYChart.Series<String, Number> series = new XYChart.Series<>();
                series.setName("Orders");
                
                for (Map.Entry<String, Integer> entry : breakdown.entrySet()) {
                    if (orderTypes.contains("All") || orderTypes.contains(entry.getKey())) {
                        series.getData().add(new XYChart.Data<>(entry.getKey(), entry.getValue()));
                    }
                }
                
                platformComparisonChart.getData().clear();
                platformComparisonChart.getData().add(series);
            } catch (Exception e) {
                System.err.println("Error updating platform comparison chart: " + e.getMessage());
            }
        }
    }
    
    @FXML
    private void exportReport() {
        try {
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Export Report");
            fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("CSV Files", "*.csv")
            );
            
            String defaultFileName = "report_" + 
                startDatePicker.getValue().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + "_to_" +
                endDatePicker.getValue().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ".csv";
            fileChooser.setInitialFileName(defaultFileName);
            
            File file = fileChooser.showSaveDialog(generateReportBtn.getScene().getWindow());
            
            if (file != null) {
                boolean success = EnhancedReportService.exportFilteredData(
                    startDatePicker.getValue(),
                    endDatePicker.getValue(),
                    getSelectedOrderTypes(),
                    reportTypeComboBox.getValue().toLowerCase(),
                    file.getAbsolutePath()
                );
                
                if (success) {
                    showAlert("Success", "Report exported successfully to: " + file.getAbsolutePath(), 
                             Alert.AlertType.INFORMATION);
                } else {
                    showAlert("Error", "Failed to export report.", Alert.AlertType.ERROR);
                }
            }
        } catch (Exception e) {
            System.err.println("Error exporting report: " + e.getMessage());
            showAlert("Error", "Failed to export report: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }
    
    private void showAlert(String title, String message, Alert.AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
