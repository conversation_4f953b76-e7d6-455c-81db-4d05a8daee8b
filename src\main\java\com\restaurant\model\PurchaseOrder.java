package com.restaurant.model;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Model class for Purchase Orders
 */
public class PurchaseOrder {
    private int id;
    private String requestNumber;
    private String toLocation;
    private String item;
    private String quantity;
    private String status; // "Saved", "Processed", "Delivered", "Cancelled"
    private LocalDate startDate;
    private LocalDate endDate;
    private LocalDateTime createdAt;
    private LocalDateTime lastUpdated;
    private String createdBy;
    private String notes;
    private double unitPrice;
    private double totalAmount;
    private int supplierId;
    private String supplierName;

    // Constructors
    public PurchaseOrder() {
        this.createdAt = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
    }

    public PurchaseOrder(String requestNumber, String toLocation, String item, String quantity, String status) {
        this();
        this.requestNumber = requestNumber;
        this.toLocation = toLocation;
        this.item = item;
        this.quantity = quantity;
        this.status = status;
    }

    public PurchaseOrder(int id, String requestNumber, String toLocation, String item, String quantity, String status) {
        this(requestNumber, toLocation, item, quantity, status);
        this.id = id;
    }

    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public String getRequestNumber() { return requestNumber; }
    public void setRequestNumber(String requestNumber) { this.requestNumber = requestNumber; }

    public String getToLocation() { return toLocation; }
    public void setToLocation(String toLocation) { this.toLocation = toLocation; }

    public String getItem() { return item; }
    public void setItem(String item) { this.item = item; }

    public String getQuantity() { return quantity; }
    public void setQuantity(String quantity) { this.quantity = quantity; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public LocalDate getStartDate() { return startDate; }
    public void setStartDate(LocalDate startDate) { this.startDate = startDate; }

    public LocalDate getEndDate() { return endDate; }
    public void setEndDate(LocalDate endDate) { this.endDate = endDate; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getLastUpdated() { return lastUpdated; }
    public void setLastUpdated(LocalDateTime lastUpdated) { this.lastUpdated = lastUpdated; }

    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }

    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }

    public double getUnitPrice() { return unitPrice; }
    public void setUnitPrice(double unitPrice) { this.unitPrice = unitPrice; }

    public double getTotalAmount() { return totalAmount; }
    public void setTotalAmount(double totalAmount) { this.totalAmount = totalAmount; }

    public int getSupplierId() { return supplierId; }
    public void setSupplierId(int supplierId) { this.supplierId = supplierId; }

    public String getSupplierName() { return supplierName; }
    public void setSupplierName(String supplierName) { this.supplierName = supplierName; }

    // Utility methods
    public String getStatusColor() {
        switch (status.toLowerCase()) {
            case "saved": return "#ffc107"; // Yellow
            case "processed": return "#007bff"; // Blue
            case "delivered": return "#28a745"; // Green
            case "cancelled": return "#dc3545"; // Red
            default: return "#6c757d"; // Gray
        }
    }

    public boolean isEditable() {
        return "Saved".equalsIgnoreCase(status);
    }

    public boolean isCancellable() {
        return !"Delivered".equalsIgnoreCase(status) && !"Cancelled".equalsIgnoreCase(status);
    }

    @Override
    public String toString() {
        return "PurchaseOrder{" +
                "id=" + id +
                ", requestNumber='" + requestNumber + '\'' +
                ", toLocation='" + toLocation + '\'' +
                ", item='" + item + '\'' +
                ", quantity='" + quantity + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
