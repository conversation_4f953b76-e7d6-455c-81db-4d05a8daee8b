package com.restaurant.model;

import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class ReportDAO {
    private static final String DB_URL = "*************************";
    
    // Daily Reports
    public static boolean saveDailyReport(DailyReport report) {
        String sql = "INSERT OR REPLACE INTO daily_reports " +
            "(report_date, total_orders, total_revenue, swiggy_orders, zomato_orders, " +
            "swiggy_revenue, zomato_revenue, avg_order_value, peak_hour, updated_at) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = DriverManager.getConnection(DB_URL);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, report.getReportDate().toString());
            pstmt.setInt(2, report.getTotalOrders());
            pstmt.setDouble(3, report.getTotalRevenue());
            pstmt.setInt(4, report.getSwiggyOrders());
            pstmt.setInt(5, report.getZomatoOrders());
            pstmt.setDouble(6, report.getSwiggyRevenue());
            pstmt.setDouble(7, report.getZomatoRevenue());
            pstmt.setDouble(8, report.getAvgOrderValue());
            pstmt.setString(9, report.getPeakHour());
            pstmt.setString(10, LocalDateTime.now().toString());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("Error saving daily report: " + e.getMessage());
            return false;
        }
    }
    
    public static DailyReport getDailyReport(LocalDate date) {
        String sql = "SELECT * FROM daily_reports WHERE report_date = ?";
        
        try (Connection conn = DriverManager.getConnection(DB_URL);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, date.toString());
            ResultSet rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return mapDailyReport(rs);
            }
        } catch (SQLException e) {
            System.err.println("Error getting daily report: " + e.getMessage());
        }
        return null;
    }
    
    public static List<DailyReport> getDailyReports(LocalDate startDate, LocalDate endDate) {
        String sql = "SELECT * FROM daily_reports WHERE report_date BETWEEN ? AND ? ORDER BY report_date DESC";
        List<DailyReport> reports = new ArrayList<>();
        
        try (Connection conn = DriverManager.getConnection(DB_URL);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, startDate.toString());
            pstmt.setString(2, endDate.toString());
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                reports.add(mapDailyReport(rs));
            }
        } catch (SQLException e) {
            System.err.println("Error getting daily reports: " + e.getMessage());
        }
        return reports;
    }
    
    // Weekly Reports
    public static boolean saveWeeklyReport(WeeklyReport report) {
        String sql = "INSERT OR REPLACE INTO weekly_reports " +
            "(week_start_date, week_end_date, total_orders, total_revenue, swiggy_orders, " +
            "zomato_orders, swiggy_revenue, zomato_revenue, avg_daily_orders, avg_daily_revenue, best_day) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = DriverManager.getConnection(DB_URL);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, report.getWeekStartDate().toString());
            pstmt.setString(2, report.getWeekEndDate().toString());
            pstmt.setInt(3, report.getTotalOrders());
            pstmt.setDouble(4, report.getTotalRevenue());
            pstmt.setInt(5, report.getSwiggyOrders());
            pstmt.setInt(6, report.getZomatoOrders());
            pstmt.setDouble(7, report.getSwiggyRevenue());
            pstmt.setDouble(8, report.getZomatoRevenue());
            pstmt.setDouble(9, report.getAvgDailyOrders());
            pstmt.setDouble(10, report.getAvgDailyRevenue());
            pstmt.setString(11, report.getBestDay());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("Error saving weekly report: " + e.getMessage());
            return false;
        }
    }
    
    public static List<WeeklyReport> getWeeklyReports(int limit) {
        String sql = "SELECT * FROM weekly_reports ORDER BY week_start_date DESC LIMIT ?";
        List<WeeklyReport> reports = new ArrayList<>();
        
        try (Connection conn = DriverManager.getConnection(DB_URL);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, limit);
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                reports.add(mapWeeklyReport(rs));
            }
        } catch (SQLException e) {
            System.err.println("Error getting weekly reports: " + e.getMessage());
        }
        return reports;
    }
    
    // Monthly Reports
    public static boolean saveMonthlyReport(MonthlyReport report) {
        String sql = "INSERT OR REPLACE INTO monthly_reports " +
            "(report_month, report_year, total_orders, total_revenue, swiggy_orders, " +
            "zomato_orders, swiggy_revenue, zomato_revenue, avg_daily_orders, " +
            "avg_daily_revenue, growth_percentage, best_week) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = DriverManager.getConnection(DB_URL);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, report.getReportMonth());
            pstmt.setInt(2, report.getReportYear());
            pstmt.setInt(3, report.getTotalOrders());
            pstmt.setDouble(4, report.getTotalRevenue());
            pstmt.setInt(5, report.getSwiggyOrders());
            pstmt.setInt(6, report.getZomatoOrders());
            pstmt.setDouble(7, report.getSwiggyRevenue());
            pstmt.setDouble(8, report.getZomatoRevenue());
            pstmt.setDouble(9, report.getAvgDailyOrders());
            pstmt.setDouble(10, report.getAvgDailyRevenue());
            pstmt.setDouble(11, report.getGrowthPercentage());
            pstmt.setString(12, report.getBestWeek());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            System.err.println("Error saving monthly report: " + e.getMessage());
            return false;
        }
    }
    
    public static List<MonthlyReport> getMonthlyReports(int limit) {
        String sql = "SELECT * FROM monthly_reports ORDER BY report_year DESC, report_month DESC LIMIT ?";
        List<MonthlyReport> reports = new ArrayList<>();
        
        try (Connection conn = DriverManager.getConnection(DB_URL);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, limit);
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                reports.add(mapMonthlyReport(rs));
            }
        } catch (SQLException e) {
            System.err.println("Error getting monthly reports: " + e.getMessage());
        }
        return reports;
    }
    
    // Helper methods to map ResultSet to objects
    private static DailyReport mapDailyReport(ResultSet rs) throws SQLException {
        DailyReport report = new DailyReport();
        report.setId(rs.getInt("id"));
        report.setReportDate(LocalDate.parse(rs.getString("report_date")));
        report.setTotalOrders(rs.getInt("total_orders"));
        report.setTotalRevenue(rs.getDouble("total_revenue"));
        report.setSwiggyOrders(rs.getInt("swiggy_orders"));
        report.setZomatoOrders(rs.getInt("zomato_orders"));
        report.setSwiggyRevenue(rs.getDouble("swiggy_revenue"));
        report.setZomatoRevenue(rs.getDouble("zomato_revenue"));
        report.setAvgOrderValue(rs.getDouble("avg_order_value"));
        report.setPeakHour(rs.getString("peak_hour"));
        
        String createdAtStr = rs.getString("created_at");
        if (createdAtStr != null) {
            report.setCreatedAt(LocalDateTime.parse(createdAtStr));
        }
        
        String updatedAtStr = rs.getString("updated_at");
        if (updatedAtStr != null) {
            report.setUpdatedAt(LocalDateTime.parse(updatedAtStr));
        }
        
        return report;
    }
    
    private static WeeklyReport mapWeeklyReport(ResultSet rs) throws SQLException {
        WeeklyReport report = new WeeklyReport();
        report.setId(rs.getInt("id"));
        report.setWeekStartDate(LocalDate.parse(rs.getString("week_start_date")));
        report.setWeekEndDate(LocalDate.parse(rs.getString("week_end_date")));
        report.setTotalOrders(rs.getInt("total_orders"));
        report.setTotalRevenue(rs.getDouble("total_revenue"));
        report.setSwiggyOrders(rs.getInt("swiggy_orders"));
        report.setZomatoOrders(rs.getInt("zomato_orders"));
        report.setSwiggyRevenue(rs.getDouble("swiggy_revenue"));
        report.setZomatoRevenue(rs.getDouble("zomato_revenue"));
        report.setAvgDailyOrders(rs.getDouble("avg_daily_orders"));
        report.setAvgDailyRevenue(rs.getDouble("avg_daily_revenue"));
        report.setBestDay(rs.getString("best_day"));
        
        String createdAtStr = rs.getString("created_at");
        if (createdAtStr != null) {
            report.setCreatedAt(LocalDateTime.parse(createdAtStr));
        }
        
        return report;
    }
    
    private static MonthlyReport mapMonthlyReport(ResultSet rs) throws SQLException {
        MonthlyReport report = new MonthlyReport();
        report.setId(rs.getInt("id"));
        report.setReportMonth(rs.getInt("report_month"));
        report.setReportYear(rs.getInt("report_year"));
        report.setTotalOrders(rs.getInt("total_orders"));
        report.setTotalRevenue(rs.getDouble("total_revenue"));
        report.setSwiggyOrders(rs.getInt("swiggy_orders"));
        report.setZomatoOrders(rs.getInt("zomato_orders"));
        report.setSwiggyRevenue(rs.getDouble("swiggy_revenue"));
        report.setZomatoRevenue(rs.getDouble("zomato_revenue"));
        report.setAvgDailyOrders(rs.getDouble("avg_daily_orders"));
        report.setAvgDailyRevenue(rs.getDouble("avg_daily_revenue"));
        report.setGrowthPercentage(rs.getDouble("growth_percentage"));
        report.setBestWeek(rs.getString("best_week"));
        
        String createdAtStr = rs.getString("created_at");
        if (createdAtStr != null) {
            report.setCreatedAt(LocalDateTime.parse(createdAtStr));
        }
        
        return report;
    }
}
