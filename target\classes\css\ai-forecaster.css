/* AI Forecaster Styles */

/* Header Styles */
.forecaster-header {
    -fx-background-color: linear-gradient(to right, #667eea 0%, #764ba2 100%);
    -fx-text-fill: white;
}

.page-title {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-fill: white;
}

.page-subtitle {
    -fx-font-size: 14px;
    -fx-fill: rgba(255, 255, 255, 0.8);
    -fx-font-style: italic;
}

.header-icon {
    -fx-fill: white;
}

.help-button {
    -fx-background-color: rgba(255, 255, 255, 0.2);
    -fx-text-fill: white;
    -fx-background-radius: 20px;
    -fx-border-radius: 20px;
    -fx-border-color: rgba(255, 255, 255, 0.3);
    -fx-border-width: 1px;
    -fx-padding: 8px 12px;
}

.help-button:hover {
    -fx-background-color: rgba(255, 255, 255, 0.3);
}

/* Content Area */
.forecaster-content {
    -fx-background-color: #f8f9fa;
}

/* Input Section */
.input-section {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 10, 0, 0, 2);
    -fx-padding: 25px;
}

.section-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-fill: #2c3e50;
}

.section-icon {
    -fx-fill: #3498db;
}

.parameter-grid {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 8px;
}

.input-label {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-text-fill: #5a6c7d;
    -fx-padding: 0 0 5px 0;
}

.date-picker, .combo-box {
    -fx-background-color: white;
    -fx-border-color: #e1e8ed;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 8px 12px;
    -fx-font-size: 13px;
}

.date-picker:focused, .combo-box:focused {
    -fx-border-color: #3498db;
    -fx-effect: dropshadow(gaussian, rgba(52, 152, 219, 0.3), 5, 0, 0, 0);
}

/* Buttons */
.primary-button {
    -fx-background-color: linear-gradient(to bottom, #3498db, #2980b9);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 10px 20px;
    -fx-font-size: 13px;
    -fx-cursor: hand;
}

.primary-button:hover {
    -fx-background-color: linear-gradient(to bottom, #2980b9, #21618c);
    -fx-effect: dropshadow(gaussian, rgba(52, 152, 219, 0.4), 8, 0, 0, 2);
}

.secondary-button {
    -fx-background-color: #ecf0f1;
    -fx-text-fill: #5a6c7d;
    -fx-font-weight: bold;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 10px 20px;
    -fx-font-size: 13px;
    -fx-cursor: hand;
}

.secondary-button:hover {
    -fx-background-color: #d5dbdb;
}

/* Metrics Cards */
.metrics-container {
    -fx-padding: 0;
}

.metric-card {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.08), 8, 0, 0, 2);
    -fx-padding: 20px;
    -fx-spacing: 8px;
    -fx-min-width: 200px;
    -fx-pref-width: 200px;
}

.metric-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.15), 12, 0, 0, 4);
    -fx-translate-y: -2px;
}

.metric-label {
    -fx-font-size: 12px;
    -fx-fill: #7f8c8d;
    -fx-font-weight: 500;
}

.metric-value {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-fill: #2c3e50;
}

.metric-change {
    -fx-font-size: 11px;
    -fx-fill: #27ae60;
    -fx-font-weight: 500;
}

.metric-icon-positive {
    -fx-fill: #27ae60;
}

.metric-icon-negative {
    -fx-fill: #e74c3c;
}

.metric-icon-neutral {
    -fx-fill: #3498db;
}

/* Chart Section */
.chart-section {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 10, 0, 0, 2);
    -fx-padding: 25px;
    -fx-spacing: 20px;
}

.forecast-chart {
    -fx-background-color: transparent;
    -fx-padding: 10px;
}

.forecast-chart .chart-plot-background {
    -fx-background-color: #fafbfc;
}

.forecast-chart .chart-vertical-grid-lines {
    -fx-stroke: #e1e8ed;
    -fx-stroke-width: 0.5px;
}

.forecast-chart .chart-horizontal-grid-lines {
    -fx-stroke: #e1e8ed;
    -fx-stroke-width: 0.5px;
}

.forecast-chart .chart-series-line {
    -fx-stroke-width: 3px;
}

.forecast-chart .default-color0.chart-series-line {
    -fx-stroke: #3498db;
}

.forecast-chart .default-color1.chart-series-line {
    -fx-stroke: #e74c3c;
    -fx-stroke-dash-array: 5 5;
}

.forecast-chart .default-color0.chart-line-symbol {
    -fx-background-color: #3498db;
    -fx-background-radius: 4px;
    -fx-padding: 4px;
}

.forecast-chart .default-color1.chart-line-symbol {
    -fx-background-color: #e74c3c;
    -fx-background-radius: 4px;
    -fx-padding: 4px;
}

/* View Toggle Buttons */
.view-toggle {
    -fx-background-color: #ecf0f1;
    -fx-text-fill: #5a6c7d;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-padding: 6px 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 500;
}

.view-toggle:selected {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
}

.view-toggle:hover {
    -fx-background-color: #d5dbdb;
}

.view-toggle:selected:hover {
    -fx-background-color: #2980b9;
}

/* Export Button */
.export-button {
    -fx-background-color: #27ae60;
    -fx-text-fill: white;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 8px 16px;
    -fx-font-size: 12px;
    -fx-font-weight: 500;
}

.export-button:hover {
    -fx-background-color: #229954;
}

/* Chart Legend */
.chart-legend {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 6px;
    -fx-padding: 12px;
}

.legend-color-historical {
    -fx-background-color: #3498db;
    -fx-min-width: 16px;
    -fx-min-height: 3px;
    -fx-background-radius: 2px;
}

.legend-color-forecast {
    -fx-background-color: #e74c3c;
    -fx-min-width: 16px;
    -fx-min-height: 3px;
    -fx-background-radius: 2px;
}

.legend-color-confidence {
    -fx-background-color: rgba(231, 76, 60, 0.3);
    -fx-min-width: 16px;
    -fx-min-height: 3px;
    -fx-background-radius: 2px;
}

.legend-text {
    -fx-font-size: 12px;
    -fx-fill: #5a6c7d;
    -fx-font-weight: 500;
}

/* Loading State */
.loading-container {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 10, 0, 0, 2);
    -fx-padding: 40px;
}

.loading-text {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-fill: #2c3e50;
}

.loading-subtext {
    -fx-font-size: 13px;
    -fx-fill: #7f8c8d;
}

/* Empty State */
.empty-state {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 10, 0, 0, 2);
    -fx-padding: 60px 40px;
}

.empty-state-icon {
    -fx-fill: #bdc3c7;
}

.empty-state-title {
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-fill: #2c3e50;
}

.empty-state-subtitle {
    -fx-font-size: 14px;
    -fx-fill: #7f8c8d;
    -fx-text-alignment: center;
    -fx-wrap-text: true;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
    .parameter-grid {
        -fx-hgap: 10px;
        -fx-vgap: 10px;
    }
    
    .metric-card {
        -fx-min-width: 150px;
        -fx-pref-width: 150px;
        -fx-padding: 15px;
    }
    
    .metric-value {
        -fx-font-size: 20px;
    }
    
    .page-title {
        -fx-font-size: 20px;
    }
}

/* Animation classes */
.fade-in {
    -fx-opacity: 0;
}

.slide-up {
    -fx-translate-y: 20px;
    -fx-opacity: 0;
}
