package com.restaurant.controller;

import javafx.animation.KeyFrame;
import javafx.animation.Timeline;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.concurrent.Task;
import javafx.util.Duration;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.chart.CategoryAxis;
import javafx.scene.chart.LineChart;
import javafx.scene.chart.NumberAxis;
import javafx.scene.chart.XYChart;
import javafx.scene.control.*;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.shape.Circle;
import javafx.scene.text.Text;
import javafx.stage.FileChooser;
import javafx.stage.Stage;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

import com.restaurant.model.SalesData;
import com.restaurant.model.ForecastResult;
import com.restaurant.service.AIForecastService;

public class AIForecasterController implements Initializable {

    // Input Controls
    @FXML private DatePicker startDatePicker;
    @FXML private DatePicker endDatePicker;
    @FXML private ComboBox<String> categoryComboBox;
    @FXML private ComboBox<String> forecastPeriodComboBox;
    @FXML private ComboBox<String> salesChannelComboBox;
    @FXML private ComboBox<String> locationComboBox;
    @FXML private Button generateForecastButton;
    @FXML private Button resetButton;
    @FXML private Button helpButton;

    // Embedded Voice Input Controls
    @FXML private VBox embeddedVoiceInput;
    @FXML private Button embeddedMicButton;
    @FXML private Label embeddedMicIcon;
    @FXML private Circle voicePulseRing;
    @FXML private Text voiceStatusText;

    // Metrics Display
    @FXML private VBox summaryContainer;
    @FXML private Label lastUpdatedLabel;
    @FXML private Text predictedSalesText;
    @FXML private Label salesGrowthIcon;
    @FXML private Text salesGrowthText;
    @FXML private Text growthRateText;
    @FXML private Label growthTrendIcon;
    @FXML private Text growthTrendText;
    @FXML private Text topCategoryText;
    @FXML private Text topCategoryPercentText;
    @FXML private Text confidenceText;
    @FXML private Label confidenceIcon;
    @FXML private Text confidenceDescText;

    // Chart Controls
    @FXML private VBox chartContainer;
    @FXML private LineChart<String, Number> forecastChart;
    @FXML private CategoryAxis xAxis;
    @FXML private NumberAxis yAxis;
    private ToggleGroup viewToggleGroup;
    @FXML private ToggleButton dailyViewButton;
    @FXML private ToggleButton weeklyViewButton;
    @FXML private ToggleButton monthlyViewButton;

    // Export Controls
    @FXML private MenuItem exportCsvMenuItem;
    @FXML private MenuItem exportPdfMenuItem;
    @FXML private MenuItem exportImageMenuItem;

    // State Containers
    @FXML private VBox loadingContainer;
    @FXML private VBox emptyStateContainer;
    @FXML private ProgressIndicator loadingIndicator;
    @FXML private Text loadingStatusText;
    @FXML private VBox insightsPanel;
    @FXML private VBox insightsList;

    private AIForecastService forecastService;
    private ForecastResult currentForecast;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        forecastService = new AIForecastService();
        setupInitialState();
        setupEventHandlers();
        setupDefaultValues();
    }

    private void setupInitialState() {
        // Set default date range (last 30 days)
        startDatePicker.setValue(LocalDate.now().minusDays(30));
        endDatePicker.setValue(LocalDate.now());

        // Populate ComboBoxes
        categoryComboBox.getItems().addAll("All Categories", "Appetizers", "Main Course", "Beverages", "Desserts");
        forecastPeriodComboBox.getItems().addAll("7 Days", "14 Days", "30 Days", "90 Days");
        salesChannelComboBox.getItems().addAll("All Channels", "Dine-in", "Takeaway", "Delivery", "Online Orders");
        locationComboBox.getItems().addAll("All Locations", "Main Branch", "Downtown", "Mall Branch");

        // Set default selections
        categoryComboBox.setValue("All Categories");
        forecastPeriodComboBox.setValue("30 Days");
        salesChannelComboBox.setValue("All Channels");
        locationComboBox.setValue("All Locations");

        // Create and setup ToggleGroup
        viewToggleGroup = new ToggleGroup();
        dailyViewButton.setToggleGroup(viewToggleGroup);
        weeklyViewButton.setToggleGroup(viewToggleGroup);
        monthlyViewButton.setToggleGroup(viewToggleGroup);

        // Set default view
        dailyViewButton.setSelected(true);

        // Initially hide results
        summaryContainer.setVisible(false);
        chartContainer.setVisible(false);
        loadingContainer.setVisible(false);
        emptyStateContainer.setVisible(true);
    }

    private void setupEventHandlers() {
        // View toggle handlers
        viewToggleGroup.selectedToggleProperty().addListener((obs, oldToggle, newToggle) -> {
            if (newToggle != null && currentForecast != null) {
                updateChartView();
            }
        });

        // Help button
        helpButton.setOnAction(e -> showHelpDialog());
        
        // Date validation
        startDatePicker.valueProperty().addListener((obs, oldDate, newDate) -> validateDateRange());
        endDatePicker.valueProperty().addListener((obs, oldDate, newDate) -> validateDateRange());
    }

    private void setupDefaultValues() {
        // Set minimum and maximum dates
        startDatePicker.setValue(LocalDate.now().minusYears(1));
        endDatePicker.setValue(LocalDate.now());
        
        // Disable future dates for historical data
        startDatePicker.setDayCellFactory(picker -> new DateCell() {
            @Override
            public void updateItem(LocalDate date, boolean empty) {
                super.updateItem(date, empty);
                setDisable(empty || date.isAfter(LocalDate.now()));
            }
        });
        
        endDatePicker.setDayCellFactory(picker -> new DateCell() {
            @Override
            public void updateItem(LocalDate date, boolean empty) {
                super.updateItem(date, empty);
                setDisable(empty || date.isAfter(LocalDate.now()));
            }
        });
    }

    @FXML
    private void handleGenerateForecast() {
        if (!validateInputs()) {
            return;
        }

        showLoadingState();
        
        // Create forecast parameters
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("startDate", startDatePicker.getValue());
        parameters.put("endDate", endDatePicker.getValue());
        parameters.put("category", categoryComboBox.getValue());
        parameters.put("forecastPeriod", forecastPeriodComboBox.getValue());
        parameters.put("salesChannel", salesChannelComboBox.getValue());
        parameters.put("location", locationComboBox.getValue());

        // Generate forecast in background thread
        Task<ForecastResult> forecastTask = new Task<ForecastResult>() {
            @Override
            protected ForecastResult call() throws Exception {
                // Simulate AI processing time
                Thread.sleep(2000);
                return forecastService.generateForecast(parameters);
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    currentForecast = getValue();
                    displayForecastResults();
                    hideLoadingState();
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    hideLoadingState();
                    showErrorAlert("Forecast Generation Failed", 
                        "Unable to generate forecast. Please try again.");
                });
            }
        };

        new Thread(forecastTask).start();
    }

    @FXML
    private void handleReset() {
        setupInitialState();
        currentForecast = null;
        forecastChart.getData().clear();
    }

    @FXML
    private void handleExportCsv() {
        if (currentForecast == null) {
            showErrorAlert("No Data", "Please generate a forecast first.");
            return;
        }

        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Export Forecast Data");
        fileChooser.getExtensionFilters().add(
            new FileChooser.ExtensionFilter("CSV Files", "*.csv"));
        fileChooser.setInitialFileName("sales_forecast_" + 
            LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ".csv");

        Stage stage = (Stage) generateForecastButton.getScene().getWindow();
        File file = fileChooser.showSaveDialog(stage);

        if (file != null) {
            exportToCsv(file);
        }
    }

    @FXML
    private void handleExportPdf() {
        showInfoAlert("Export PDF", "PDF export functionality will be implemented soon.");
    }

    @FXML
    private void handleExportImage() {
        showInfoAlert("Export Image", "Image export functionality will be implemented soon.");
    }

    private boolean validateInputs() {
        if (startDatePicker.getValue() == null || endDatePicker.getValue() == null) {
            showErrorAlert("Invalid Date Range", "Please select both start and end dates.");
            return false;
        }

        if (startDatePicker.getValue().isAfter(endDatePicker.getValue())) {
            showErrorAlert("Invalid Date Range", "Start date must be before end date.");
            return false;
        }

        if (startDatePicker.getValue().isAfter(LocalDate.now().minusDays(7))) {
            showErrorAlert("Insufficient Data", "Please select a date range with at least 7 days of historical data.");
            return false;
        }

        return true;
    }

    private void validateDateRange() {
        if (startDatePicker.getValue() != null && endDatePicker.getValue() != null) {
            if (startDatePicker.getValue().isAfter(endDatePicker.getValue())) {
                endDatePicker.setValue(startDatePicker.getValue().plusDays(1));
            }
        }
    }

    private void showLoadingState() {
        emptyStateContainer.setVisible(false);
        summaryContainer.setVisible(false);
        chartContainer.setVisible(false);
        loadingContainer.setVisible(true);
        generateForecastButton.setDisable(true);
    }

    private void hideLoadingState() {
        loadingContainer.setVisible(false);
        generateForecastButton.setDisable(false);
    }

    private void displayForecastResults() {
        if (currentForecast == null) return;

        // Update metrics
        predictedSalesText.setText("₹" + String.format("%,.0f", currentForecast.getPredictedSales()));
        salesGrowthText.setText(String.format("%+.1f%%", currentForecast.getGrowthPercentage()));
        growthRateText.setText(String.format("%.1f%%", Math.abs(currentForecast.getGrowthPercentage())));
        growthTrendText.setText(currentForecast.getGrowthPercentage() >= 0 ? "↗ Growing" : "↘ Declining");
        topCategoryText.setText(currentForecast.getTopCategory());
        topCategoryPercentText.setText(String.format("%.1f%% of sales", currentForecast.getTopCategoryPercent()));
        confidenceText.setText(String.format("%.0f%%", currentForecast.getConfidenceLevel()));
        confidenceDescText.setText(getConfidenceDescription(currentForecast.getConfidenceLevel()));

        // Update chart
        updateChart();

        // Show results
        emptyStateContainer.setVisible(false);
        summaryContainer.setVisible(true);
        chartContainer.setVisible(true);
    }

    private void updateChart() {
        forecastChart.getData().clear();
        
        // Historical data series
        XYChart.Series<String, Number> historicalSeries = new XYChart.Series<>();
        historicalSeries.setName("Historical Data");
        
        // Forecast data series
        XYChart.Series<String, Number> forecastSeries = new XYChart.Series<>();
        forecastSeries.setName("Forecast");

        // Add data points based on current view
        String selectedView = getSelectedView();
        List<SalesData> data = currentForecast.getHistoricalData();
        List<SalesData> forecast = currentForecast.getForecastData();

        // Add historical data
        for (SalesData point : data) {
            historicalSeries.getData().add(new XYChart.Data<>(
                formatDateForView(point.getDate(), selectedView), 
                point.getSales()));
        }

        // Add forecast data
        for (SalesData point : forecast) {
            forecastSeries.getData().add(new XYChart.Data<>(
                formatDateForView(point.getDate(), selectedView), 
                point.getSales()));
        }

        forecastChart.getData().addAll(historicalSeries, forecastSeries);
    }

    private void updateChartView() {
        if (currentForecast != null) {
            updateChart();
        }
    }

    private String getSelectedView() {
        Toggle selected = viewToggleGroup.getSelectedToggle();
        if (selected == dailyViewButton) return "Daily";
        if (selected == weeklyViewButton) return "Weekly";
        if (selected == monthlyViewButton) return "Monthly";
        return "Daily";
    }

    private String formatDateForView(LocalDate date, String view) {
        switch (view) {
            case "Weekly":
                return "Week " + date.format(DateTimeFormatter.ofPattern("w"));
            case "Monthly":
                return date.format(DateTimeFormatter.ofPattern("MMM yyyy"));
            default:
                return date.format(DateTimeFormatter.ofPattern("MMM dd"));
        }
    }

    private String getConfidenceDescription(double confidence) {
        if (confidence >= 90) return "Very High";
        if (confidence >= 75) return "High";
        if (confidence >= 60) return "Moderate";
        if (confidence >= 45) return "Low";
        return "Very Low";
    }

    private void exportToCsv(File file) {
        try (FileWriter writer = new FileWriter(file)) {
            writer.write("Date,Type,Sales,Category,Channel,Location\n");
            
            // Write historical data
            for (SalesData data : currentForecast.getHistoricalData()) {
                writer.write(String.format("%s,Historical,%.2f,%s,%s,%s\n",
                    data.getDate().toString(),
                    data.getSales(),
                    categoryComboBox.getValue(),
                    salesChannelComboBox.getValue(),
                    locationComboBox.getValue()));
            }
            
            // Write forecast data
            for (SalesData data : currentForecast.getForecastData()) {
                writer.write(String.format("%s,Forecast,%.2f,%s,%s,%s\n",
                    data.getDate().toString(),
                    data.getSales(),
                    categoryComboBox.getValue(),
                    salesChannelComboBox.getValue(),
                    locationComboBox.getValue()));
            }
            
            showInfoAlert("Export Successful", "Forecast data exported to: " + file.getName());
        } catch (IOException e) {
            showErrorAlert("Export Failed", "Unable to export data: " + e.getMessage());
        }
    }

    private void showHelpDialog() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("AI Forecaster Help");
        alert.setHeaderText("How to use the AI Sales Forecaster");
        alert.setContentText(
            "1. Select a date range for historical data analysis\n" +
            "2. Choose category, sales channel, and location filters\n" +
            "3. Select forecast period (7-90 days)\n" +
            "4. Click 'Generate Forecast' to see AI predictions\n" +
            "5. Use view toggles to switch between daily/weekly/monthly\n" +
            "6. Export results as CSV for further analysis\n\n" +
            "The AI analyzes historical patterns, seasonality, and trends to predict future sales."
        );
        alert.showAndWait();
    }

    private void showErrorAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void showInfoAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    private void handleVoiceInput() {
        Alert voiceAlert = new Alert(Alert.AlertType.INFORMATION);
        voiceAlert.setTitle("🎤 AI Voice Input");
        voiceAlert.setHeaderText("Voice Command Examples");
        voiceAlert.setContentText(
            "Try these voice commands:\n\n" +
            "• \"Show sales forecast for desserts next 30 days\"\n" +
            "• \"Predict main course sales for next week\"\n" +
            "• \"Generate forecast for delivery channel\"\n" +
            "• \"What will be the growth rate next month?\"\n\n" +
            "Voice input feature coming soon!"
        );
        voiceAlert.showAndWait();
    }

    @FXML
    private void handleEmbeddedVoiceInput() {
        // Toggle voice input state
        if (voiceStatusText.getText().equals("Voice Ready")) {
            startEmbeddedVoiceListening();
        } else {
            stopEmbeddedVoiceListening();
        }
    }

    private void startEmbeddedVoiceListening() {
        // Update UI to listening state
        embeddedMicIcon.setText("🔴");
        voiceStatusText.setText("Listening...");
        voicePulseRing.setVisible(true);

        // Simulate voice recognition for forecasting commands
        Task<String> voiceTask = new Task<String>() {
            @Override
            protected String call() throws Exception {
                Thread.sleep(3000); // Simulate listening time

                // Simulate recognized forecasting commands
                String[] forecastCommands = {
                    "forecast sales for next 30 days",
                    "show forecast for main course",
                    "predict growth for next week",
                    "generate forecast for beverages",
                    "forecast for next 7 days"
                };

                return forecastCommands[(int)(Math.random() * forecastCommands.length)];
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    String command = getValue();
                    processVoiceCommand(command);
                    stopEmbeddedVoiceListening();
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    stopEmbeddedVoiceListening();
                    showErrorAlert("Voice Recognition Failed", "Could not understand the command. Please try again.");
                });
            }
        };

        new Thread(voiceTask).start();
    }

    private void stopEmbeddedVoiceListening() {
        embeddedMicIcon.setText("🎤");
        voiceStatusText.setText("Voice Ready");
        voicePulseRing.setVisible(false);
    }

    private void processVoiceCommand(String command) {
        voiceStatusText.setText("Processing: " + command);

        // Parse voice command and set parameters
        if (command.contains("30 days") || command.contains("month")) {
            forecastPeriodComboBox.setValue("30 Days");
        } else if (command.contains("7 days") || command.contains("week")) {
            forecastPeriodComboBox.setValue("7 Days");
        }

        if (command.contains("main course")) {
            categoryComboBox.setValue("Main Course");
        } else if (command.contains("beverages")) {
            categoryComboBox.setValue("Beverages");
        } else if (command.contains("desserts")) {
            categoryComboBox.setValue("Desserts");
        }

        // Auto-trigger forecast generation
        Platform.runLater(() -> {
            handleGenerateForecast();
            voiceStatusText.setText("Forecast generated!");

            // Reset status after 3 seconds
            Timeline resetTimeline = new Timeline(
                new KeyFrame(Duration.seconds(3), e -> voiceStatusText.setText("Voice Ready"))
            );
            resetTimeline.play();
        });
    }

    private void updateDisplayWithEnhancedData() {
        if (currentForecast == null) return;

        // Update timestamp
        // lastUpdatedLabel.setText("Last updated: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm")));

        // Update growth icons based on trend
        double growth = currentForecast.getGrowthPercentage();
        // if (growth > 0) {
        //     salesGrowthIcon.setText("📈");
        //     growthTrendIcon.setText("🚀");
        // } else if (growth < 0) {
        //     salesGrowthIcon.setText("📉");
        //     growthTrendIcon.setText("⚠️");
        // } else {
        //     salesGrowthIcon.setText("➡️");
        //     growthTrendIcon.setText("🔄");
        // }

        // Update confidence icon
        double confidence = currentForecast.getConfidenceLevel();
        // if (confidence >= 80) {
        //     confidenceIcon.setText("🔥");
        // } else if (confidence >= 60) {
        //     confidenceIcon.setText("👍");
        // } else {
        //     confidenceIcon.setText("⚠️");
        // }
    }
}
