<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.OrderManagementController" stylesheets="@../css/application.css">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="module-header">
         <children>
            <Button mnemonicParsing="false" onAction="#goBack" styleClass="back-button" text="← Back" />
            <Label styleClass="module-title" text="📋 Order Management">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Button mnemonicParsing="false" onAction="#refreshOrders" styleClass="refresh-button" text="🔄 Refresh" />
            <Button mnemonicParsing="false" onAction="#createNewOrder" styleClass="primary-button" text="➕ New Order" />
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </HBox>
      
      <!-- Filter and Search Section -->
      <VBox spacing="15.0" styleClass="filter-section">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="15.0">
               <children>
                  <Label text="Search:" />
                  <TextField fx:id="searchField" promptText="Search by order ID, table, or customer..." styleClass="search-field" />
                  <Label text="Status:" />
                  <ComboBox fx:id="statusFilterCombo" promptText="Active Orders" styleClass="filter-combo" />
                  <Label text="Type:" />
                  <ComboBox fx:id="typeFilterCombo" promptText="All Types" styleClass="filter-combo" />
               </children>
            </HBox>
            
            <HBox alignment="CENTER_LEFT" spacing="15.0">
               <children>
                  <Label text="Date Range:" />
                  <DatePicker fx:id="fromDatePicker" promptText="From Date" styleClass="date-picker" />
                  <Label text="to" />
                  <DatePicker fx:id="toDatePicker" promptText="To Date" styleClass="date-picker" />
                  <Button mnemonicParsing="false" onAction="#applyFilters" styleClass="filter-button" text="🔍 Apply Filters" />
                  <Button mnemonicParsing="false" onAction="#clearFilters" styleClass="secondary-button" text="🗑️ Clear" />
                  <Button mnemonicParsing="false" onAction="#refreshOrders" styleClass="refresh-button" text="🔄 Refresh" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="5.0" />
         </padding>
      </VBox>
      
      <!-- Orders Table -->
      <VBox VBox.vgrow="ALWAYS" styleClass="table-section">
         <children>
            <TableView fx:id="ordersTable" styleClass="orders-table">
               <columns>
                  <TableColumn fx:id="orderIdColumn" prefWidth="70.0" minWidth="60.0" text="Order ID" />
                  <TableColumn fx:id="tableNumberColumn" prefWidth="90.0" minWidth="80.0" text="Table" />
                  <TableColumn fx:id="customerNameColumn" prefWidth="140.0" minWidth="120.0" text="Customer" />
                  <TableColumn fx:id="orderTypeColumn" prefWidth="90.0" minWidth="80.0" text="Type" />
                  <TableColumn fx:id="statusColumn" prefWidth="100.0" minWidth="90.0" text="Status" />
                  <TableColumn fx:id="itemsCountColumn" prefWidth="60.0" minWidth="50.0" text="Items" />
                  <TableColumn fx:id="totalAmountColumn" prefWidth="90.0" minWidth="80.0" text="Total" />
                  <TableColumn fx:id="orderTimeColumn" prefWidth="130.0" minWidth="120.0" text="Order Time" />
                  <TableColumn fx:id="actionsColumn" prefWidth="110.0" minWidth="100.0" maxWidth="120.0" resizable="false" text="Actions" />
               </columns>
            </TableView>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
         </padding>
      </VBox>
      
      <!-- Order Details Dialog -->
      <VBox fx:id="orderDetailsDialog" managed="false" visible="false" styleClass="dialog-overlay">
         <children>
            <VBox styleClass="dialog-content-large">
               <children>
                  <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="dialog-header">
                     <children>
                        <Label fx:id="orderDetailsTitle" styleClass="dialog-title" text="Order Details">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        <Region HBox.hgrow="ALWAYS" />
                        <Button mnemonicParsing="false" onAction="#closeOrderDetailsDialog" styleClass="close-button" text="✕" />
                     </children>
                  </HBox>
                  
                  <ScrollPane styleClass="dialog-scroll">
                     <content>
                        <VBox spacing="20.0" styleClass="order-details-content">
                           <children>
                              <!-- Order Info -->
                              <VBox spacing="10.0" styleClass="info-section">
                                 <children>
                                    <Label styleClass="section-title" text="Order Information">
                                       <font>
                                          <Font name="System Bold" size="16.0" />
                                       </font>
                                    </Label>
                                    <GridPane hgap="20.0" vgap="10.0">
                                       <columnConstraints>
                                          <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                                          <ColumnConstraints hgrow="ALWAYS" />
                                          <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                                          <ColumnConstraints hgrow="ALWAYS" />
                                       </columnConstraints>
                                       <children>
                                          <Label text="Order ID:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                                          <Label fx:id="detailOrderId" text="-" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                                          <Label text="Table Number:" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                                          <Label fx:id="detailTableNumber" text="-" GridPane.columnIndex="3" GridPane.rowIndex="0" />
                                          
                                          <Label text="Customer:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                                          <Label fx:id="detailCustomerName" text="-" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                                          <Label text="Order Type:" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                                          <Label fx:id="detailOrderType" text="-" GridPane.columnIndex="3" GridPane.rowIndex="1" />
                                          
                                          <Label text="Status:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                                          <Label fx:id="detailStatus" text="-" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                                          <Label text="Order Time:" GridPane.columnIndex="2" GridPane.rowIndex="2" />
                                          <Label fx:id="detailOrderTime" text="-" GridPane.columnIndex="3" GridPane.rowIndex="2" />
                                       </children>
                                    </GridPane>
                                 </children>
                              </VBox>
                              
                              <!-- Order Items -->
                              <VBox spacing="10.0" styleClass="info-section">
                                 <children>
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                       <children>
                                          <Label styleClass="section-title" text="Order Items">
                                             <font>
                                                <Font name="System Bold" size="16.0" />
                                             </font>
                                          </Label>
                                          <Region HBox.hgrow="ALWAYS" />
                                          <Button fx:id="addItemButton" mnemonicParsing="false" onAction="#showAddItemDialog" styleClass="primary-button" text="➕ Add Item" />
                                       </children>
                                    </HBox>
                                    <TableView fx:id="orderItemsTable" styleClass="order-items-table">
                                       <columns>
                                          <TableColumn fx:id="itemNameColumn" prefWidth="180.0" text="Item Name" />
                                          <TableColumn fx:id="itemQuantityColumn" prefWidth="60.0" text="Qty" />
                                          <TableColumn fx:id="itemPriceColumn" prefWidth="80.0" text="Price" />
                                          <TableColumn fx:id="itemTotalColumn" prefWidth="80.0" text="Total" />
                                          <TableColumn fx:id="itemNotesColumn" prefWidth="120.0" text="Notes" />
                                          <TableColumn fx:id="itemActionsColumn" prefWidth="100.0" text="Actions" />
                                       </columns>
                                    </TableView>
                                 </children>
                              </VBox>
                              
                              <!-- Order Summary -->
                              <VBox spacing="10.0" styleClass="info-section">
                                 <children>
                                    <Label styleClass="section-title" text="Order Summary">
                                       <font>
                                          <Font name="System Bold" size="16.0" />
                                       </font>
                                    </Label>
                                    <GridPane hgap="20.0" vgap="8.0" styleClass="summary-grid">
                                       <columnConstraints>
                                          <ColumnConstraints hgrow="ALWAYS" />
                                          <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                                       </columnConstraints>
                                       <children>
                                          <Label text="Subtotal:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                                          <Label fx:id="detailSubtotal" text="₹0.00" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                                          
                                          <Label text="GST (18%):" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                                          <Label fx:id="detailGST" text="₹0.00" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                                          
                                          <Label text="Service Charge (10%):" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                                          <Label fx:id="detailServiceCharge" text="₹0.00" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                                          
                                          <Separator GridPane.columnIndex="0" GridPane.columnSpan="2" GridPane.rowIndex="3" />
                                          
                                          <Label styleClass="total-label" text="Grand Total:" GridPane.columnIndex="0" GridPane.rowIndex="4">
                                             <font>
                                                <Font name="System Bold" size="14.0" />
                                             </font>
                                          </Label>
                                          <Label fx:id="detailGrandTotal" styleClass="total-amount" text="₹0.00" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                             <font>
                                                <Font name="System Bold" size="14.0" />
                                             </font>
                                          </Label>
                                       </children>
                                    </GridPane>
                                 </children>
                              </VBox>
                           </children>
                        </VBox>
                     </content>
                  </ScrollPane>
                  
                  <HBox alignment="CENTER_RIGHT" spacing="10.0" styleClass="dialog-actions">
                     <children>
                        <Button mnemonicParsing="false" onAction="#printKOT" styleClass="secondary-button" text="🖨️ Print KOT" />
                        <Button mnemonicParsing="false" onAction="#printInvoice" styleClass="secondary-button" text="🧾 Print Invoice" />
                        <Button mnemonicParsing="false" onAction="#updateOrderStatus" styleClass="primary-button" text="📝 Update Status" />
                        <Button mnemonicParsing="false" onAction="#closeOrderDetailsDialog" styleClass="secondary-button" text="Close" />
                     </children>
                  </HBox>
               </children>
            </VBox>
         </children>
      </VBox>

      <!-- Add Item Dialog -->
      <VBox fx:id="addItemDialog" managed="false" visible="false" styleClass="dialog-overlay">
         <children>
            <VBox styleClass="dialog-content">
               <children>
                  <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="dialog-header">
                     <children>
                        <Label styleClass="dialog-title" text="Add Item to Order">
                           <font>
                              <Font name="System Bold" size="16.0" />
                           </font>
                        </Label>
                        <Region HBox.hgrow="ALWAYS" />
                        <Button mnemonicParsing="false" onAction="#closeAddItemDialog" styleClass="close-button" text="✕" />
                     </children>
                  </HBox>

                  <VBox spacing="15.0" styleClass="dialog-body">
                     <children>
                        <VBox spacing="8.0">
                           <children>
                              <Label text="Select Item:" />
                              <ComboBox fx:id="itemSelectionCombo" promptText="Choose an item..." styleClass="item-combo" />
                           </children>
                        </VBox>

                        <HBox spacing="15.0">
                           <children>
                              <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label text="Quantity:" />
                                    <TextField fx:id="itemQuantityField" promptText="1" text="1" styleClass="quantity-field" />
                                 </children>
                              </VBox>

                              <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label text="Unit Price:" />
                                    <TextField fx:id="itemPriceField" promptText="0.00" styleClass="price-field" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>

                        <VBox spacing="8.0">
                           <children>
                              <Label text="Discount (%):" />
                              <TextField fx:id="itemDiscountField" promptText="0" text="0" styleClass="discount-field" />
                           </children>
                        </VBox>

                        <VBox spacing="8.0">
                           <children>
                              <Label text="Notes (Optional):" />
                              <TextArea fx:id="itemNotesField" promptText="Special instructions..." prefRowCount="2" styleClass="notes-field" />
                           </children>
                        </VBox>

                        <HBox spacing="10.0" styleClass="total-display">
                           <children>
                              <Label text="Item Total:" styleClass="total-label" />
                              <Label fx:id="itemTotalLabel" text="₹0.00" styleClass="total-amount" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>

                  <HBox alignment="CENTER_RIGHT" spacing="10.0" styleClass="dialog-actions">
                     <children>
                        <Button mnemonicParsing="false" onAction="#closeAddItemDialog" styleClass="secondary-button" text="Cancel" />
                        <Button fx:id="confirmAddItemButton" mnemonicParsing="false" onAction="#confirmAddItem" styleClass="primary-button" text="Add Item" />
                     </children>
                  </HBox>
               </children>
            </VBox>
         </children>
      </VBox>
   </children>
</VBox>
