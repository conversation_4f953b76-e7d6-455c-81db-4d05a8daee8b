package com.restaurant.util;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;

/**
 * Utility class for menu item shortcuts/abbreviations
 * Provides quick search functionality for all 233 menu items
 */
public class MenuShortcuts {
    
    private static final Map<String, String> SHORTCUT_TO_ITEM = new HashMap<>();
    private static final Map<String, Set<String>> ITEM_TO_SHORTCUTS = new HashMap<>();
    
    static {
        initializeShortcuts();
    }
    
    /**
     * Initialize all shortcuts for 233 menu items
     */
    private static void initializeShortcuts() {
        // Tandoori Roti - 13 items
        addShortcut("Roti", "r", "roti");
        addShortcut("Butter Roti", "br", "broti", "butter roti");
        addShortcut("Naan", "n", "naan");
        addShortcut("Butter Naan", "bn", "bnaan", "butter naan");
        addShortcut("Garlic Naan", "gn", "gnaan", "garlic naan");
        addShortcut("Khulcha", "k", "khulcha");
        addShortcut("Butter Khulcha", "bk", "bkhulcha", "butter khulcha");
        addShortcut("Lacha Paratha", "lp", "lacha", "lparatha");
        addShortcut("Butter Paratha", "bp", "bparatha", "butter paratha");
        addShortcut("Aloo Paratha", "ap", "aparatha", "aloo paratha");
        addShortcut("Gobi Paratha", "gp", "gparatha", "gobi paratha");
        addShortcut("Paneer Paratha", "pp", "pparatha", "paneer paratha");
        addShortcut("Veg Kheema Paratha", "vkp", "vkparatha", "veg kheema");
        
        // Biryani - 14 items
        addShortcut("Steam Basmati Rice (H/F)", "sbr", "steam rice", "basmati");
        addShortcut("Jeera Rice (H/F)", "jr", "jeera rice", "jrice");
        addShortcut("Veg Pulao", "vp", "vpulao", "veg pulao");
        addShortcut("Veg Biryani", "vb", "vbiryani", "veg biryani");
        addShortcut("Egg Biryani", "eb", "ebiryani", "egg biryani");
        addShortcut("Paneer Pulav", "ppl", "ppulav", "paneer pulav");
        addShortcut("Paneer Biryani", "pb", "pbiryani", "paneer biryani");
        addShortcut("Chicken Biryani", "cb", "cbiryani", "chicken biryani");
        addShortcut("Chi. Dum Biryani", "cdb", "cdum", "chicken dum");
        addShortcut("Chicken Hyderabadi Biryani", "chb", "chyd", "chicken hyderabadi");
        addShortcut("Mutton Pulav", "mp", "mpulav", "mutton pulav");
        addShortcut("Mutton Biryani", "mb", "mbiryani", "mutton biryani");
        addShortcut("Mutton Dum Biryani", "mdb", "mdum", "mutton dum");
        addShortcut("Mutton Hyderabadi Biryani", "mhb", "mhyd", "mutton hyderabadi");
        
        // Tandoori (Veg) - 6 items
        addShortcut("Paneer Tikka", "pt", "ptikka", "paneer tikka");
        addShortcut("Paneer Malai Tikka", "pmt", "pmalai", "paneer malai");
        addShortcut("Veg Seek Kabab", "vsk", "vseek", "veg seek");
        addShortcut("Mushroom Tikka", "mt", "mtikka", "mushroom tikka");
        addShortcut("Baby Corn Tikka", "bct", "bcorn", "baby corn");
        addShortcut("Chilly Milly Kabab", "cmk", "cmilly", "chilly milly");
        
        // Tandoori Chicken - 13 items
        addShortcut("Chicken Tikka", "ct", "ctikka", "chicken tikka");
        addShortcut("Chicken Tandoori Half", "cth", "ctandoori", "chicken tandoori");
        addShortcut("Chicken Tandoori Full", "ctf", "ctfull", "chicken full");
        addShortcut("Chicken Pahadi Tandoori Half", "cpth", "cpahadi", "chicken pahadi");
        addShortcut("Chicken Pahadi Tandoori Full", "cptf", "cpahadif", "pahadi full");
        addShortcut("Chicken Lemon Tandoori Half", "clth", "clemon", "lemon tandoori");
        addShortcut("Chicken Lemon Tandoori Full", "cltf", "clemonf", "lemon full");
        addShortcut("Chicken Kalimiri Kabab", "ckk", "ckalimiri", "kalimiri");
        addShortcut("Chicken Banjara Kabab", "cbk", "cbanjara", "banjara");
        addShortcut("Chicken Sholay Kabab", "csk", "csholay", "sholay");
        addShortcut("Chicken Hariyali Kabab", "chk", "chariyali", "hariyali");
        addShortcut("Chicken Tangri Kabab", "ctgk", "ctangri", "tangri");
        addShortcut("Chicken Rajwadi Kabab", "crk", "crajwadi", "rajwadi");
        
        // Papad & Salad - 8 items
        addShortcut("Roasted Papad", "rp", "rpapad", "roasted");
        addShortcut("Fry Papad", "fp", "fpapad", "fry papad");
        addShortcut("Masala Papad", "msp", "mpapad", "masala papad");
        addShortcut("Green Salad", "gs", "gsalad", "green salad");
        addShortcut("Raita", "rt", "raita");
        addShortcut("Boondi Raita", "brt", "braita", "boondi");
        addShortcut("Schzewan Sauce Extra", "sse", "schzewan", "sauce");
        addShortcut("Fry Noodles Extra", "fne", "fnoodles", "extra noodles");
        
        // Mutton Gravy - 8 items
        addShortcut("Mutton Masala", "mm", "mmasala", "mutton masala");
        addShortcut("Mutton Kadhai", "mk", "mkadhai", "mutton kadhai");
        addShortcut("Mutton Kolhapuri", "mkol", "mkolhapuri", "mutton kolhapuri");
        addShortcut("Mutton Hyderabadi", "mh", "mhyderabadi", "mutton hyderabadi");
        addShortcut("Mutton Handi (Half) 6pcs", "mhh", "mhandi", "mutton handi half");
        addShortcut("Mutton Handi (Full) 12pcs", "mhf", "mhandif", "mutton handi full");
        addShortcut("Mutton Do Pyaza", "mdp", "mdopyaza", "mutton do pyaza");
        addShortcut("Mutton Shukar", "ms", "mshukar", "mutton shukar");
        
        // Sea Food - 11 items
        addShortcut("Bangda Fry", "bf", "bangda", "bangda fry");
        addShortcut("Bangda Masala", "bm", "bmasala", "bangda masala");
        addShortcut("Mandeli Oil Fry", "mof", "mandeli", "mandeli fry");
        addShortcut("Surmai Tawa Fry", "stf", "surmai", "surmai fry");
        addShortcut("Surmai Koliwada", "sk", "skoliwada", "surmai koliwada");
        addShortcut("Prawns Tawa Fry", "ptf", "prawns", "prawns fry");
        addShortcut("Prawns Koliwada", "pk", "pkoliwada", "prawns koliwada");
        addShortcut("Surmai Gavan Curry", "sgc", "sgavan", "surmai curry");
        addShortcut("Surmai Masala", "sm", "smasala", "surmai masala");
        addShortcut("Fish Curry", "fc", "fish", "fish curry");
        addShortcut("Prawn Curry", "pc", "prawn", "prawn curry");
        
        // Bulk Order - 10 items
        addShortcut("Paneer Pulav", "ppl", "ppulav", "paneer pulav bulk");
        addShortcut("Veg Biryani", "vb", "vbiryani", "veg biryani bulk");
        addShortcut("Chicken Biryani", "cb", "cbiryani", "chicken biryani bulk");
        addShortcut("Mutton Biryani", "mb", "mbiryani", "mutton biryani bulk");
        addShortcut("Veg Pulav", "vp", "vpulav", "veg pulav bulk");
        addShortcut("Chicken Masala", "cm", "cmasala", "chicken masala bulk");
        addShortcut("Jira Rice", "jr", "jira", "jira rice");
        addShortcut("Steam Rice", "sr", "steam", "steam rice");
        addShortcut("Chicken Malai Tikka", "cmt", "cmalai", "chicken malai");
        addShortcut("Chicken Seekh Kabab", "csk", "cseekh", "chicken seekh");
        
        // Soup (Veg) - 5 items
        addShortcut("Manchow Soup", "mcs", "manchow", "manchow soup");
        addShortcut("Schezwan Soup", "szs", "schezwan soup", "szwan soup");
        addShortcut("Noodles Soup", "ns", "noodle soup", "noodles soup");
        addShortcut("Clear Soup", "cs", "clear", "clear soup");
        addShortcut("Hot N Sour Soup", "hss", "hot sour", "hot n sour");
        
        // Soup (Non-Veg) - 6 items
        addShortcut("Chicken Manchow Soup", "cms", "cmanchow", "chicken manchow");
        addShortcut("Chicken Hot N Sour Soup", "chss", "chot sour", "chicken hot sour");
        addShortcut("Chicken Lung Fung Soup", "clfs", "clung fung", "chicken lung");
        addShortcut("Chicken Schezwan Soup", "css", "cschezwan", "chicken schezwan soup");
        addShortcut("Chicken Noodles Soup", "cns", "cnoodles", "chicken noodles soup");
        addShortcut("Chicken Clear Soup", "ccs", "cclear", "chicken clear");
        
        // Noodles (Veg) - 9 items
        addShortcut("Veg Hakka Noodles", "vhn", "vhakka", "veg hakka");
        addShortcut("Veg Schezwan Noodles", "vsn", "vschezwan", "veg schezwan");
        addShortcut("Veg Singapore Noodles", "vsgn", "vsingapore", "veg singapore");
        addShortcut("Veg Hong Kong Noodles", "vhkn", "vhong kong", "veg hong kong");
        addShortcut("Veg Mushroom Noodles", "vmn", "vmushroom", "veg mushroom");
        addShortcut("Veg Manchurian Noodles", "vmcn", "vmanchurian", "veg manchurian");
        addShortcut("Veg Sherpa Noodles", "vspn", "vsherpa", "veg sherpa");
        addShortcut("Veg Triple Sch. Noodles", "vtsn", "vtriple", "veg triple");
        addShortcut("Veg Chilly Garlic Noodles", "vcgn", "vchilly garlic", "veg chilly");
        
        // Noodles (Non-Veg) - 12 items
        addShortcut("Egg Hakka Noodles", "ehn", "ehakka", "egg hakka");
        addShortcut("Egg Schezwan Noodles", "esn", "eschezwan", "egg schezwan");
        addShortcut("Chicken Hakka Noodles", "chn", "chakka", "chicken hakka");
        addShortcut("Chi. Schezwan Noodles", "csn", "cschezwan", "chicken schezwan");
        addShortcut("Chi. Singapore Noodles", "csgn", "csingapore", "chicken singapore");
        addShortcut("Chi. Hong Kong Noodles", "chkn", "chong kong", "chicken hong kong");
        addShortcut("Chi. Mushroom Noodles", "cmn", "cmushroom", "chicken mushroom");
        addShortcut("Chi. Triple Schezwan Noodles", "ctsn", "ctriple", "chicken triple");
        addShortcut("Chi. Sherpa Noodles", "cspn", "csherpa", "chicken sherpa");
        addShortcut("Chi. Thousand Noodles", "cthn", "cthousand", "chicken thousand");
        addShortcut("Chi. Chilly Basil Noodles", "ccbn", "cchilly basil", "chicken chilly basil");
        addShortcut("Chicken Chilly Garlic Noodles", "ccgn", "cchilly garlic", "chicken chilly garlic");
        
        // Rice (Veg) - 12 items
        addShortcut("Veg Fry Rice", "vfr", "vfried", "veg fried");
        addShortcut("Veg Schezwan Rice", "vsr", "vschezwan rice", "veg schezwan");
        addShortcut("Veg Singapore Rice", "vsgr", "vsingapore rice", "veg singapore");
        addShortcut("Veg Hong Kong Rice", "vhkr", "vhong kong rice", "veg hong kong");
        addShortcut("Veg Schezwan Combination Rice", "vscr", "vschezwan combo", "veg combo");
        addShortcut("Veg Manchurian Rice", "vmr", "vmanchurian rice", "veg manchurian");
        addShortcut("Veg Triple Schoz. Rice", "vtsr", "vtriple rice", "veg triple");
        addShortcut("Paneer Fry Rice", "pfr", "pfried", "paneer fried");
        addShortcut("Paneer Schezwan Rice", "psr", "pschezwan", "paneer schezwan");
        addShortcut("Veg Sherpa Rice", "vspr", "vsherpa rice", "veg sherpa");
        addShortcut("Veg Thousand Rice", "vtr", "vthousand", "veg thousand");
        addShortcut("Veg Chilly Basil Rice", "vcbr", "vchilly basil", "veg chilly");
        
        // Rice (Non-Veg) - 16 items
        addShortcut("Chi. Schezwan Rice", "csr", "cschezwan rice", "chicken schezwan");
        addShortcut("Chi. Singapore Rice", "csgr", "csingapore rice", "chicken singapore");
        addShortcut("Chi. Hong Kong Rice", "chkr", "chong kong rice", "chicken hong kong");
        addShortcut("Chi. Sez. Combination Rice", "cscr", "csez combo", "chicken combo");
        addShortcut("Chi. Burn Garlic Rice", "cbgr", "cburn garlic", "chicken burn");
        addShortcut("Chi. Chilly Garlic Rice", "ccgr", "cchilly garlic", "chicken chilly");
        addShortcut("Chi. Manchurian Rice", "cmr", "cmanchurian rice", "chicken manchurian");
        addShortcut("Chi. Triple Schoz. Rice", "ctsr", "ctriple rice", "chicken triple");
        addShortcut("Chi. Sherpa Rice", "cspr", "csherpa rice", "chicken sherpa");
        addShortcut("Chi. Thousand Rice", "ctr", "cthousand rice", "chicken thousand");
        addShortcut("Chi. Jadoo Rice", "cjr", "cjadoo", "chicken jadoo");
        addShortcut("Chi. Ginger Garlic Rice", "cggr", "cginger garlic", "chicken ginger");
        addShortcut("Chi. Chilly Basil Rice", "ccbr", "cchilly basil", "chicken chilly basil");
        addShortcut("Egg Fry Rice", "efr", "efried", "egg fried");
        addShortcut("Egg Schezwan Rice", "esr", "eschezwan rice", "egg schezwan");
        addShortcut("Chi. Fry Rice", "cfr", "cfried", "chicken fried");

        // Chinese Gravy - 9 items
        addShortcut("Manchurian Gravy / Chilly", "mg", "manchurian gravy", "manchurian chilly");
        addShortcut("Schezwan Gravy", "sg", "schezwan gravy", "szwan gravy");
        addShortcut("Chilly Gravy", "cg", "chilly gravy", "chilly");
        addShortcut("Kum Pav Gravy", "kpg", "kum pav", "kumpav");
        addShortcut("Hot Garlic Gravy", "hgg", "hot garlic", "hgarlic");
        addShortcut("Oyster Gravy", "og", "oyster", "oyster gravy");
        addShortcut("Paneer Sch. Gravy", "psg", "paneer schezwan", "pschezwan");
        addShortcut("Paneer Manch. Gravy", "pmg", "paneer manchurian", "pmanchurian");
        addShortcut("Paneer Chilly Gravy", "pcg", "paneer chilly", "pchilly");

        // Indian & Punjabi (Veg) - 28 items
        addShortcut("Dal Fry", "df", "dal", "dal fry");
        addShortcut("Dal Tadka", "dt", "dtadka", "dal tadka");
        addShortcut("Dal Palak", "dp", "dpalak", "dal palak");
        addShortcut("Dal Khichadi (1000ML)", "dk", "dkhichadi", "dal khichadi");
        addShortcut("Palak Khichadi (1000ML)", "pk", "pkhichadi", "palak khichadi");
        addShortcut("Dal Khichadi Tadka (1000ML)", "dkt", "dktadka", "dal khichadi tadka");
        addShortcut("Palak Khichadi Tadka (1000ML)", "pkt", "pktadka", "palak khichadi tadka");
        addShortcut("Mix Veg", "mv", "mix", "mix veg");
        addShortcut("Veg Kadhai", "vk", "vkadhai", "veg kadhai");
        addShortcut("Veg Kolhapuri", "vkol", "vkolhapuri", "veg kolhapuri");
        addShortcut("Veg Tawa", "vt", "vtawa", "veg tawa");
        addShortcut("Veg Lajawab", "vl", "vlajawab", "veg lajawab");
        addShortcut("Veg Chilly Milly", "vcm", "vchilly milly", "veg chilly milly");
        addShortcut("Aloo Mutter", "am", "aloo", "aloo mutter");
        addShortcut("Veg Handi (Half/Full)", "vh", "vhandi", "veg handi");
        addShortcut("Paneer Masala", "pm", "pmasala", "paneer masala");
        addShortcut("Paneer Mutter Masala", "pmm", "pmutter", "paneer mutter");
        addShortcut("Paneer Butter Masala", "pbm", "pbutter", "paneer butter");
        addShortcut("Paneer Kadhai", "pk", "pkadhai", "paneer kadhai");
        addShortcut("Paneer Bhurji Masala", "pbhm", "pbhurji", "paneer bhurji");
        addShortcut("Paneer Mutter", "pm", "pmutter", "paneer mutter");
        addShortcut("Palak Paneer", "pp", "ppalak", "palak paneer");
        addShortcut("Mushroom Masala", "mm", "mmasala", "mushroom masala");
        addShortcut("Mushroom Tikka Masala", "mtm", "mtikka masala", "mushroom tikka");
        addShortcut("Lasuni Palak", "lp", "lasuni", "lasuni palak");
        addShortcut("Veg Maratha", "vm", "vmaratha", "veg maratha");
        addShortcut("Sev Bhaji", "sb", "sev", "sev bhaji");
        addShortcut("Masala Fry Masala", "mfm", "masala fry", "mfry");

        // Chicken Gravy - 19 items
        addShortcut("Chicken Masala", "cm", "cmasala", "chicken masala");
        addShortcut("Chicken Curry", "cc", "ccurry", "chicken curry");
        addShortcut("Chicken Kadhai", "ck", "ckadhai", "chicken kadhai");
        addShortcut("Chicken Bhurjani", "cb", "cbhurjani", "chicken bhurjani");
        addShortcut("Chicken Tawa Masala", "ctm", "ctawa", "chicken tawa");
        addShortcut("Chicken Gayti Masala", "cgm", "cgayti", "chicken gayti");
        addShortcut("Chicken Tikka Masala", "ctm", "ctikka masala", "chicken tikka");
        addShortcut("Chicken Maratha", "cm", "cmaratha", "chicken maratha");
        addShortcut("Chicken Lasuni Masala", "clm", "clasuni", "chicken lasuni");
        addShortcut("Chicken Japeta (H/F)", "cj", "cjapeta", "chicken japeta");
        addShortcut("Butter Chicken (H/F)", "bc", "butter chicken", "bchicken");
        addShortcut("Chicken Malvani Masala", "cmm", "cmalvani", "chicken malvani");
        addShortcut("Chicken Tikka Lemon Masala", "ctlm", "ctikka lemon", "chicken lemon");
        addShortcut("Chicken Hyderabadi Masala", "chm", "chyderabadi", "chicken hyderabadi");
        addShortcut("Chicken Mughlai", "cmg", "cmughlai", "chicken mughlai");
        addShortcut("Chicken Pahadi Masala", "cpm", "cpahadi masala", "chicken pahadi");
        addShortcut("Chicken Handi (Half) 6pcs", "chh", "chandi", "chicken handi half");
        addShortcut("Chicken Handi (Full) 12pcs", "chf", "chandif", "chicken handi full");
        addShortcut("Chicken Do Pyaza", "cdp", "cdo pyaza", "chicken do pyaza");

        // Starters (Veg) - 14 items
        addShortcut("Veg Manchurian / Chilly", "vmc", "vmanchurian", "veg manchurian");
        addShortcut("Veg Chinese Bhel", "vcb", "vchinese", "veg chinese");
        addShortcut("Mushroom Chilly/ Manchurian", "mcm", "mchilly", "mushroom chilly");
        addShortcut("Paneer Chilly/ Manchurian", "pcm", "pchilly", "paneer chilly");
        addShortcut("Paneer Crispy", "pc", "pcrispy", "paneer crispy");
        addShortcut("Paneer Singapur", "ps", "psingapur", "paneer singapur");
        addShortcut("Veg Crispy", "vc", "vcrispy", "veg crispy");
        addShortcut("Crispy Chilly Potato", "ccp", "crispy potato", "cchilly potato");
        addShortcut("Honey Chilly Potato", "hcp", "honey potato", "hchilly potato");
        addShortcut("Paneer Shangai Wok", "psw", "pshangai", "paneer shangai");
        addShortcut("Paneer Schezwan Wok", "psw", "pschezwan wok", "paneer schezwan");
        addShortcut("Paneer Chilly Basil Wok", "pcbw", "pchilly basil", "paneer chilly basil");
        addShortcut("Paneer Honey Chilly Wok", "phcw", "phoney chilly", "paneer honey");
        addShortcut("Paneer Kum Pav Wok", "pkpw", "pkum pav", "paneer kum pav");

        // Starters (Non-Veg) - 14 items
        addShortcut("Chi. Chinese Bhel", "ccb", "cchinese", "chicken chinese");
        addShortcut("Chi. Chilly/Manchurian", "ccm", "cchilly", "chicken chilly");
        addShortcut("Chi. Schezwan", "cs", "cschezwan", "chicken schezwan");
        addShortcut("Chi. Chilly Garlic Wok", "ccgw", "cchilly garlic", "chicken chilly garlic");
        addShortcut("Chi. Kum Pav Wok", "ckpw", "ckum pav", "chicken kum pav");
        addShortcut("Chi. Crispy", "cc", "ccrispy", "chicken crispy");
        addShortcut("Chi. Singapur", "cs", "csingapur", "chicken singapur");
        addShortcut("Chi. Lamba", "cl", "clamba", "chicken lamba");
        addShortcut("Chi. Oyster Sauces", "cos", "coyster", "chicken oyster");
        addShortcut("Chi. Black Paper Wok", "cbpw", "cblack paper", "chicken black paper");
        addShortcut("Chi. Lollypop 8 Pc.", "clp", "clollypop", "chicken lollypop");
        addShortcut("Chi. Lollypop Schzwn/Hnypp", "clsh", "clollypop schezwan", "lollypop honey");
        addShortcut("Chi. Honey Chilly", "chc", "choney", "chicken honey");
        addShortcut("Chi. Chilly Basil Wok", "ccbw", "cchilly basil", "chicken chilly basil");

        // Egg Dishes - 6 items
        addShortcut("Boiled Egg", "be", "boiled", "boiled egg");
        addShortcut("Egg Omlete", "eo", "omlete", "egg omlete");
        addShortcut("Egg Bhurji", "eb", "ebhurji", "egg bhurji");
        addShortcut("Egg Masala", "em", "emasala", "egg masala");
        addShortcut("Egg Curry", "ec", "ecurry", "egg curry");
        addShortcut("Anda Ghotala", "ag", "anda", "anda ghotala");
    }
    
    /**
     * Add shortcut mappings for a menu item
     */
    private static void addShortcut(String itemName, String... shortcuts) {
        Set<String> shortcutSet = new HashSet<>();
        for (String shortcut : shortcuts) {
            SHORTCUT_TO_ITEM.put(shortcut.toLowerCase(), itemName);
            shortcutSet.add(shortcut.toLowerCase());
        }
        ITEM_TO_SHORTCUTS.put(itemName, shortcutSet);
    }
    
    /**
     * Get menu item name from shortcut
     */
    public static String getItemFromShortcut(String shortcut) {
        return SHORTCUT_TO_ITEM.get(shortcut.toLowerCase());
    }
    
    /**
     * Get all shortcuts for a menu item
     */
    public static Set<String> getShortcutsForItem(String itemName) {
        return ITEM_TO_SHORTCUTS.getOrDefault(itemName, new HashSet<>());
    }
    
    /**
     * Check if text matches any shortcut or item name
     */
    public static boolean matchesItem(String itemName, String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            return true;
        }
        
        String searchLower = searchText.toLowerCase().trim();
        
        // Check if item name contains search text
        if (itemName.toLowerCase().contains(searchLower)) {
            return true;
        }
        
        // Check if search text is a shortcut for this item
        String itemFromShortcut = getItemFromShortcut(searchLower);
        if (itemFromShortcut != null && itemFromShortcut.equals(itemName)) {
            return true;
        }
        
        // Check if any shortcut for this item contains search text
        Set<String> shortcuts = getShortcutsForItem(itemName);
        for (String shortcut : shortcuts) {
            if (shortcut.contains(searchLower)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get all shortcuts as a formatted string for display
     */
    public static String getShortcutsDisplay(String itemName) {
        Set<String> shortcuts = getShortcutsForItem(itemName);
        if (shortcuts.isEmpty()) {
            return "";
        }
        return String.join(", ", shortcuts);
    }
}
