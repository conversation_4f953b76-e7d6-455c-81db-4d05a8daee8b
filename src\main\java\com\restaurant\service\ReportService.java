package com.restaurant.service;

import com.restaurant.model.*;
import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

public class ReportService {
    private static final String DB_URL = "*************************";
    
    // Generate Daily Report
    public static DailyReport generateDailyReport(LocalDate date) {
        DailyReport report = new DailyReport(date);
        
        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            // Get daily statistics
            String sql = "SELECT " +
                "COUNT(*) as total_orders, " +
                "COALESCE(SUM(total_amount), 0) as total_revenue, " +
                "COUNT(CASE WHEN platform = 'Swiggy' THEN 1 END) as swiggy_orders, " +
                "COUNT(CASE WHEN platform = 'Zomato' THEN 1 END) as zomato_orders, " +
                "COALESCE(SUM(CASE WHEN platform = 'Swiggy' THEN total_amount ELSE 0 END), 0) as swiggy_revenue, " +
                "COALESCE(SUM(CASE WHEN platform = 'Zomato' THEN total_amount ELSE 0 END), 0) as zomato_revenue " +
                "FROM online_orders " +
                "WHERE DATE(order_time) = ?";
            
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, date.toString());
                ResultSet rs = pstmt.executeQuery();
                
                if (rs.next()) {
                    report.setTotalOrders(rs.getInt("total_orders"));
                    report.setTotalRevenue(rs.getDouble("total_revenue"));
                    report.setSwiggyOrders(rs.getInt("swiggy_orders"));
                    report.setZomatoOrders(rs.getInt("zomato_orders"));
                    report.setSwiggyRevenue(rs.getDouble("swiggy_revenue"));
                    report.setZomatoRevenue(rs.getDouble("zomato_revenue"));
                    
                    // Calculate average order value
                    if (report.getTotalOrders() > 0) {
                        report.setAvgOrderValue(report.getTotalRevenue() / report.getTotalOrders());
                    }
                }
            }
            
            // Find peak hour
            String peakHourSql = "SELECT strftime('%H', order_time) as hour, COUNT(*) as order_count " +
                "FROM online_orders " +
                "WHERE DATE(order_time) = ? " +
                "GROUP BY strftime('%H', order_time) " +
                "ORDER BY order_count DESC " +
                "LIMIT 1";
            
            try (PreparedStatement pstmt = conn.prepareStatement(peakHourSql)) {
                pstmt.setString(1, date.toString());
                ResultSet rs = pstmt.executeQuery();
                
                if (rs.next()) {
                    String hour = rs.getString("hour");
                    int count = rs.getInt("order_count");
                    report.setPeakHour(hour + ":00 (" + count + " orders)");
                }
            }
            
        } catch (SQLException e) {
            System.err.println("Error generating daily report: " + e.getMessage());
        }
        
        return report;
    }
    
    // Generate Weekly Report
    public static WeeklyReport generateWeeklyReport(LocalDate weekStart, LocalDate weekEnd) {
        WeeklyReport report = new WeeklyReport(weekStart, weekEnd);
        
        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            // Get weekly statistics
            String sql = "SELECT " +
                "COUNT(*) as total_orders, " +
                "COALESCE(SUM(total_amount), 0) as total_revenue, " +
                "COUNT(CASE WHEN platform = 'Swiggy' THEN 1 END) as swiggy_orders, " +
                "COUNT(CASE WHEN platform = 'Zomato' THEN 1 END) as zomato_orders, " +
                "COALESCE(SUM(CASE WHEN platform = 'Swiggy' THEN total_amount ELSE 0 END), 0) as swiggy_revenue, " +
                "COALESCE(SUM(CASE WHEN platform = 'Zomato' THEN total_amount ELSE 0 END), 0) as zomato_revenue " +
                "FROM online_orders " +
                "WHERE DATE(order_time) BETWEEN ? AND ?";
            
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, weekStart.toString());
                pstmt.setString(2, weekEnd.toString());
                ResultSet rs = pstmt.executeQuery();
                
                if (rs.next()) {
                    report.setTotalOrders(rs.getInt("total_orders"));
                    report.setTotalRevenue(rs.getDouble("total_revenue"));
                    report.setSwiggyOrders(rs.getInt("swiggy_orders"));
                    report.setZomatoOrders(rs.getInt("zomato_orders"));
                    report.setSwiggyRevenue(rs.getDouble("swiggy_revenue"));
                    report.setZomatoRevenue(rs.getDouble("zomato_revenue"));
                    
                    // Calculate averages
                    long daysBetween = ChronoUnit.DAYS.between(weekStart, weekEnd) + 1;
                    report.setAvgDailyOrders(report.getTotalOrders() / (double) daysBetween);
                    report.setAvgDailyRevenue(report.getTotalRevenue() / daysBetween);
                }
            }
            
            // Find best day
            String bestDaySql = "SELECT DATE(order_time) as order_date, " +
                "COUNT(*) as daily_orders, " +
                "SUM(total_amount) as daily_revenue " +
                "FROM online_orders " +
                "WHERE DATE(order_time) BETWEEN ? AND ? " +
                "GROUP BY DATE(order_time) " +
                "ORDER BY daily_revenue DESC " +
                "LIMIT 1";
            
            try (PreparedStatement pstmt = conn.prepareStatement(bestDaySql)) {
                pstmt.setString(1, weekStart.toString());
                pstmt.setString(2, weekEnd.toString());
                ResultSet rs = pstmt.executeQuery();
                
                if (rs.next()) {
                    String bestDate = rs.getString("order_date");
                    double revenue = rs.getDouble("daily_revenue");
                    report.setBestDay(bestDate + " (₹" + String.format("%.2f", revenue) + ")");
                }
            }
            
        } catch (SQLException e) {
            System.err.println("Error generating weekly report: " + e.getMessage());
        }
        
        return report;
    }
    
    // Generate Monthly Report
    public static MonthlyReport generateMonthlyReport(int month, int year) {
        MonthlyReport report = new MonthlyReport(month, year);
        
        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            // Get monthly statistics
            String sql = "SELECT " +
                "COUNT(*) as total_orders, " +
                "COALESCE(SUM(total_amount), 0) as total_revenue, " +
                "COUNT(CASE WHEN platform = 'Swiggy' THEN 1 END) as swiggy_orders, " +
                "COUNT(CASE WHEN platform = 'Zomato' THEN 1 END) as zomato_orders, " +
                "COALESCE(SUM(CASE WHEN platform = 'Swiggy' THEN total_amount ELSE 0 END), 0) as swiggy_revenue, " +
                "COALESCE(SUM(CASE WHEN platform = 'Zomato' THEN total_amount ELSE 0 END), 0) as zomato_revenue " +
                "FROM online_orders " +
                "WHERE strftime('%m', order_time) = ? AND strftime('%Y', order_time) = ?";
            
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, String.format("%02d", month));
                pstmt.setString(2, String.valueOf(year));
                ResultSet rs = pstmt.executeQuery();
                
                if (rs.next()) {
                    report.setTotalOrders(rs.getInt("total_orders"));
                    report.setTotalRevenue(rs.getDouble("total_revenue"));
                    report.setSwiggyOrders(rs.getInt("swiggy_orders"));
                    report.setZomatoOrders(rs.getInt("zomato_orders"));
                    report.setSwiggyRevenue(rs.getDouble("swiggy_revenue"));
                    report.setZomatoRevenue(rs.getDouble("zomato_revenue"));
                    
                    // Calculate daily averages (assuming 30 days per month)
                    report.setAvgDailyOrders(report.getTotalOrders() / 30.0);
                    report.setAvgDailyRevenue(report.getTotalRevenue() / 30.0);
                }
            }
            
            // Calculate growth percentage compared to previous month
            int prevMonth = month == 1 ? 12 : month - 1;
            int prevYear = month == 1 ? year - 1 : year;
            
            String growthSql = "SELECT COALESCE(SUM(total_amount), 0) as prev_revenue " +
                "FROM online_orders " +
                "WHERE strftime('%m', order_time) = ? AND strftime('%Y', order_time) = ?";
            
            try (PreparedStatement pstmt = conn.prepareStatement(growthSql)) {
                pstmt.setString(1, String.format("%02d", prevMonth));
                pstmt.setString(2, String.valueOf(prevYear));
                ResultSet rs = pstmt.executeQuery();
                
                if (rs.next()) {
                    double prevRevenue = rs.getDouble("prev_revenue");
                    if (prevRevenue > 0) {
                        double growth = ((report.getTotalRevenue() - prevRevenue) / prevRevenue) * 100;
                        report.setGrowthPercentage(growth);
                    }
                }
            }
            
        } catch (SQLException e) {
            System.err.println("Error generating monthly report: " + e.getMessage());
        }
        
        return report;
    }
    
    // Auto-generate and save daily report
    public static boolean generateAndSaveDailyReport(LocalDate date) {
        DailyReport report = generateDailyReport(date);
        return ReportDAO.saveDailyReport(report);
    }
    
    // Auto-generate and save weekly report
    public static boolean generateAndSaveWeeklyReport(LocalDate weekStart, LocalDate weekEnd) {
        WeeklyReport report = generateWeeklyReport(weekStart, weekEnd);
        return ReportDAO.saveWeeklyReport(report);
    }
    
    // Auto-generate and save monthly report
    public static boolean generateAndSaveMonthlyReport(int month, int year) {
        MonthlyReport report = generateMonthlyReport(month, year);
        return ReportDAO.saveMonthlyReport(report);
    }
    
    // Get analytics summary
    public static Map<String, Object> getAnalyticsSummary() {
        Map<String, Object> analytics = new HashMap<>();
        
        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            // Today's stats
            LocalDate today = LocalDate.now();
            DailyReport todayReport = generateDailyReport(today);
            analytics.put("todayOrders", todayReport.getTotalOrders());
            analytics.put("todayRevenue", todayReport.getTotalRevenue());
            
            // This week's stats
            LocalDate weekStart = today.minusDays(today.getDayOfWeek().getValue() - 1);
            LocalDate weekEnd = weekStart.plusDays(6);
            WeeklyReport weekReport = generateWeeklyReport(weekStart, weekEnd);
            analytics.put("weekOrders", weekReport.getTotalOrders());
            analytics.put("weekRevenue", weekReport.getTotalRevenue());
            
            // This month's stats
            MonthlyReport monthReport = generateMonthlyReport(today.getMonthValue(), today.getYear());
            analytics.put("monthOrders", monthReport.getTotalOrders());
            analytics.put("monthRevenue", monthReport.getTotalRevenue());
            analytics.put("monthGrowth", monthReport.getGrowthPercentage());
            
            // Platform comparison
            analytics.put("swiggyPercentage", todayReport.getSwiggyPercentage());
            analytics.put("zomatoPercentage", todayReport.getZomatoPercentage());
            
        } catch (Exception e) {
            System.err.println("Error getting analytics summary: " + e.getMessage());
        }
        
        return analytics;
    }
}
