package com.restaurant.model;

import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

public class OrderDAO {
    
    public static int saveOrder(Order order) {
        Connection conn = null;
        try {
            conn = DatabaseManager.getConnection();
            conn.setAutoCommit(false);
            
            // Insert order
            PreparedStatement ps = conn.prepareStatement(
                    "INSERT INTO orders (table_number, is_takeaway, status, total_amount) VALUES (?, ?, ?, ?)",
                    Statement.RETURN_GENERATED_KEYS);
            
            ps.setObject(1, order.getTableNumber());
            ps.setBoolean(2, order.isTakeaway());
            ps.setString(3, order.getStatus());
            ps.setDouble(4, order.calculateTotal());
            
            ps.executeUpdate();
            
            // Get generated order ID
            ResultSet rs = ps.getGeneratedKeys();
            if (rs.next()) {
                int orderId = rs.getInt(1);
                order.setId(orderId);
                
                // Insert order items
                ps = conn.prepareStatement(
                        "INSERT INTO order_items (order_id, item_id, quantity, price) VALUES (?, ?, ?, ?)");

                if (order.getItems() != null && !order.getItems().isEmpty()) {
                    for (OrderItem item : order.getItems()) {
                        if (item != null && item.getMenuItem() != null) {
                            ps.setInt(1, orderId);
                            ps.setInt(2, item.getMenuItem().getId());
                            ps.setInt(3, item.getQuantity());
                            ps.setDouble(4, item.getPrice());
                            ps.addBatch();
                        }
                    }
                }
                
                ps.executeBatch();
                conn.commit();
                
                return orderId;
            }
            
            conn.rollback();
            return -1;
            
        } catch (SQLException e) {
            e.printStackTrace();
            try {
                if (conn != null) conn.rollback();
            } catch (SQLException ex) {
                ex.printStackTrace();
            }
            return -1;
        } finally {
            try {
                if (conn != null) {
                    conn.setAutoCommit(true);
                    conn.close();
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }
    
    public static Order getOrderById(int orderId) {
        Order order = null;
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT * FROM orders WHERE id = ?")) {
            
            ps.setInt(1, orderId);
            ResultSet rs = ps.executeQuery();
            
            if (rs.next()) {
                Integer tableNumber = rs.getInt("table_number");
                if (rs.wasNull()) {
                    tableNumber = null;
                }

                order = new Order(
                    rs.getInt("id"),
                    tableNumber,
                    rs.getBoolean("is_takeaway"),
                    rs.getString("status"),
                    rs.getTimestamp("timestamp").toLocalDateTime()
                );
                
                // Load order items
                loadOrderItems(order);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return order;
    }
    
    private static void loadOrderItems(Order order) {
        try {
            System.out.println("loadOrderItems() - Loading items for order ID: " + order.getId());

            Connection conn = DatabaseManager.getConnection();
            System.out.println("loadOrderItems() - Got database connection for order " + order.getId());

            PreparedStatement ps = conn.prepareStatement(
                "SELECT oi.*, mi.name, mi.category_id, c.name as category " +
                "FROM order_items oi " +
                "JOIN menu_items mi ON oi.item_id = mi.id " +
                "JOIN menu_categories c ON mi.category_id = c.id " +
                "WHERE oi.order_id = ?");

            System.out.println("loadOrderItems() - Prepared statement created for order " + order.getId());

            ps.setInt(1, order.getId());
            System.out.println("loadOrderItems() - Executing query for order " + order.getId());

            ResultSet rs = ps.executeQuery();
            System.out.println("loadOrderItems() - Query executed for order " + order.getId() + ", processing results...");

            int itemCount = 0;
            while (rs.next()) {
                itemCount++;
                System.out.println("loadOrderItems() - Processing item " + itemCount + " for order " + order.getId());

                try {
                    MenuItem menuItem = new MenuItem(
                        rs.getInt("item_id"),
                        rs.getString("name"),
                        rs.getDouble("price"),
                        rs.getString("category"),
                        rs.getInt("category_id")
                    );

                    OrderItem orderItem = new OrderItem(
                        rs.getInt("id"),
                        menuItem,
                        rs.getInt("quantity"),
                        rs.getDouble("price")
                    );

                    order.getItems().add(orderItem);
                    System.out.println("loadOrderItems() - Added item " + menuItem.getName() + " to order " + order.getId());

                } catch (Exception itemError) {
                    System.err.println("loadOrderItems() - Error processing item " + itemCount + " for order " + order.getId() + ": " + itemError.getMessage());
                    itemError.printStackTrace();
                    // Continue with next item
                }
            }

            System.out.println("loadOrderItems() - Finished loading " + itemCount + " items for order " + order.getId());

            // Close resources
            rs.close();
            ps.close();
            // Note: Don't close connection here as it's managed by try-with-resources in calling method

        } catch (SQLException e) {
            System.err.println("loadOrderItems() - SQL error for order " + order.getId() + ": " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("loadOrderItems() - Unexpected error for order " + order.getId() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static List<Order> getRecentOrders(int limit) {
        List<Order> orders = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT * FROM orders ORDER BY timestamp DESC LIMIT ?")) {
            
            ps.setInt(1, limit);
            ResultSet rs = ps.executeQuery();
            
            while (rs.next()) {
                Order order = new Order(
                    rs.getInt("id"),
                    rs.getInt("table_number"),
                    rs.getBoolean("is_takeaway"),
                    rs.getString("status"),
                    rs.getTimestamp("timestamp").toLocalDateTime()
                );
                
                orders.add(order);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return orders;
    }
    
    public static boolean updateOrderStatus(int orderId, String status) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "UPDATE orders SET status = ? WHERE id = ?")) {

            ps.setString(1, status);
            ps.setInt(2, orderId);

            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Get all active orders for the current business day that are not yet billed
     * (status: PENDING, WORKING, READY) - Simplified version to avoid hanging
     */
    public static List<Order> getTodayActiveOrders() {
        List<Order> orders = new ArrayList<>();

        System.out.println("OrderDAO.getTodayActiveOrders() - Starting database query...");

        try {
            System.out.println("OrderDAO.getTodayActiveOrders() - Creating direct database connection...");

            // Create a direct connection without going through DatabaseManager to avoid initialization hang
            String dbUrl = "*************************";
            Connection conn = DriverManager.getConnection(dbUrl);
            System.out.println("OrderDAO.getTodayActiveOrders() - Direct connection created successfully");

            // Check if orders table exists first
            System.out.println("OrderDAO.getTodayActiveOrders() - Checking if orders table exists...");
            DatabaseMetaData meta = conn.getMetaData();
            ResultSet tables = meta.getTables(null, null, "orders", null);

            if (!tables.next()) {
                System.out.println("OrderDAO.getTodayActiveOrders() - Orders table does not exist, returning empty list");
                conn.close();
                return orders; // Return empty list if table doesn't exist
            }
            System.out.println("OrderDAO.getTodayActiveOrders() - Orders table exists, proceeding with query");

            // Use simple today's date query instead of complex business day logic
            PreparedStatement ps = conn.prepareStatement(
                "SELECT * FROM orders " +
                "WHERE DATE(timestamp) = DATE('now') " +
                "AND status IN ('PENDING', 'WORKING', 'READY', 'CONFIRMED') " +
                "ORDER BY table_number, timestamp");

            System.out.println("OrderDAO.getTodayActiveOrders() - Prepared statement created");

            ResultSet rs = ps.executeQuery();
            System.out.println("OrderDAO.getTodayActiveOrders() - Query executed, processing results...");

            int orderCount = 0;
            while (rs.next()) {
                orderCount++;
                System.out.println("OrderDAO.getTodayActiveOrders() - Processing order " + orderCount);

                try {
                    // Handle nullable table_number properly
                    Integer tableNumber = rs.getObject("table_number", Integer.class);

                    Order order = new Order(
                        rs.getInt("id"),
                        tableNumber,
                        rs.getBoolean("is_takeaway"),
                        rs.getString("status"),
                        rs.getTimestamp("timestamp").toLocalDateTime()
                    );

                    System.out.println("OrderDAO.getTodayActiveOrders() - Loading items for order " + order.getId());
                    // Load order items for each order using the same connection
                    loadOrderItemsDirect(order, conn);
                    orders.add(order);
                    System.out.println("OrderDAO.getTodayActiveOrders() - Order " + order.getId() + " added successfully");

                } catch (Exception orderError) {
                    System.err.println("OrderDAO.getTodayActiveOrders() - Error processing order: " + orderError.getMessage());
                    orderError.printStackTrace();
                    // Continue with next order instead of failing completely
                }
            }

            System.out.println("OrderDAO.getTodayActiveOrders() - Finished processing. Total orders: " + orders.size());

            // Close resources
            rs.close();
            ps.close();
            conn.close();

        } catch (SQLException e) {
            System.err.println("OrderDAO.getTodayActiveOrders() - Database error: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("OrderDAO.getTodayActiveOrders() - Unexpected error: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("OrderDAO.getTodayActiveOrders() - Returning " + orders.size() + " orders");
        return orders;
    }

    /**
     * Load order items using a direct connection (to avoid DatabaseManager hanging)
     */
    private static void loadOrderItemsDirect(Order order, Connection conn) {
        try {
            System.out.println("loadOrderItemsDirect() - Loading items for order ID: " + order.getId());

            // Check if required tables exist first
            DatabaseMetaData meta = conn.getMetaData();
            ResultSet orderItemsTable = meta.getTables(null, null, "order_items", null);
            ResultSet menuItemsTable = meta.getTables(null, null, "menu_items", null);
            ResultSet categoriesTable = meta.getTables(null, null, "menu_categories", null);

            if (!orderItemsTable.next()) {
                System.out.println("loadOrderItemsDirect() - order_items table does not exist, skipping");
                return;
            }
            if (!menuItemsTable.next()) {
                System.out.println("loadOrderItemsDirect() - menu_items table does not exist, skipping");
                return;
            }
            if (!categoriesTable.next()) {
                System.out.println("loadOrderItemsDirect() - menu_categories table does not exist, skipping");
                return;
            }

            System.out.println("loadOrderItemsDirect() - All required tables exist, proceeding with query");

            PreparedStatement ps = conn.prepareStatement(
                "SELECT oi.*, mi.name, mi.category_id, c.name as category " +
                "FROM order_items oi " +
                "JOIN menu_items mi ON oi.item_id = mi.id " +
                "JOIN menu_categories c ON mi.category_id = c.id " +
                "WHERE oi.order_id = ?");

            System.out.println("loadOrderItemsDirect() - Prepared statement created for order " + order.getId());

            ps.setInt(1, order.getId());
            System.out.println("loadOrderItemsDirect() - Executing query for order " + order.getId());

            ResultSet rs = ps.executeQuery();
            System.out.println("loadOrderItemsDirect() - Query executed for order " + order.getId() + ", processing results...");

            int itemCount = 0;
            while (rs.next()) {
                itemCount++;
                System.out.println("loadOrderItemsDirect() - Processing item " + itemCount + " for order " + order.getId());

                try {
                    MenuItem menuItem = new MenuItem(
                        rs.getInt("item_id"),
                        rs.getString("name"),
                        rs.getDouble("price"),
                        rs.getString("category"),
                        rs.getInt("category_id")
                    );

                    OrderItem orderItem = new OrderItem(
                        rs.getInt("id"),
                        menuItem,
                        rs.getInt("quantity"),
                        rs.getDouble("price")
                    );

                    order.getItems().add(orderItem);
                    System.out.println("loadOrderItemsDirect() - Added item " + menuItem.getName() + " to order " + order.getId());

                } catch (Exception itemError) {
                    System.err.println("loadOrderItemsDirect() - Error processing item " + itemCount + " for order " + order.getId() + ": " + itemError.getMessage());
                    itemError.printStackTrace();
                    // Continue with next item
                }
            }

            System.out.println("loadOrderItemsDirect() - Finished loading " + itemCount + " items for order " + order.getId());

            // Close resources
            rs.close();
            ps.close();

        } catch (SQLException e) {
            System.err.println("loadOrderItemsDirect() - SQL error for order " + order.getId() + ": " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("loadOrderItemsDirect() - Unexpected error for order " + order.getId() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Get all active orders for a specific table for the current day
     */
    public static List<Order> getTodayActiveOrdersForTable(int tableNumber) {
        List<Order> orders = new ArrayList<>();

        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT * FROM orders " +
                     "WHERE DATE(timestamp) = DATE('now') " +
                     "AND table_number = ? " +
                     "AND status IN ('PENDING', 'WORKING', 'READY', 'CONFIRMED') " +
                     "ORDER BY timestamp")) {

            ps.setInt(1, tableNumber);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                Order order = new Order(
                    rs.getInt("id"),
                    rs.getObject("table_number", Integer.class),
                    rs.getBoolean("is_takeaway"),
                    rs.getString("status"),
                    rs.getTimestamp("timestamp").toLocalDateTime()
                );

                // Load order items for each order
                loadOrderItems(order);
                orders.add(order);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return orders;
    }

    /**
     * Get the most recent active order for a specific table (for order entry)
     */
    public static Order getActiveOrderByTable(int tableNumber) {
        Order order = null;

        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT * FROM orders " +
                     "WHERE DATE(timestamp) = DATE('now') " +
                     "AND table_number = ? " +
                     "AND status IN ('PENDING', 'WORKING', 'READY', 'CONFIRMED', 'KOT_PRINTED') " +
                     "ORDER BY timestamp DESC " +
                     "LIMIT 1")) {

            ps.setInt(1, tableNumber);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                // Handle table_number properly for SQLite
                Integer tableNum = null;
                int tableNumberValue = rs.getInt("table_number");
                if (!rs.wasNull()) {
                    tableNum = tableNumberValue;
                }

                order = new Order(
                    rs.getInt("id"),
                    tableNum,
                    rs.getBoolean("is_takeaway"),
                    rs.getString("status"),
                    rs.getTimestamp("timestamp").toLocalDateTime()
                );

                // Load order items for this order
                loadOrderItems(order);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return order;
    }

    /**
     * Get all orders for a specific date with optional status filter
     */
    public static List<Order> getOrdersByDate(LocalDateTime date, String statusFilter) {
        List<Order> orders = new ArrayList<>();

        String sql = "SELECT * FROM orders WHERE DATE(timestamp) = DATE(?) ";
        if (statusFilter != null && !statusFilter.equals("ALL")) {
            sql += "AND status = ? ";
        }
        sql += "ORDER BY table_number, timestamp";

        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {

            ps.setTimestamp(1, Timestamp.valueOf(date));
            if (statusFilter != null && !statusFilter.equals("ALL")) {
                ps.setString(2, statusFilter);
            }

            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                Order order = new Order(
                    rs.getInt("id"),
                    rs.getInt("table_number"),
                    rs.getBoolean("is_takeaway"),
                    rs.getString("status"),
                    rs.getTimestamp("timestamp").toLocalDateTime()
                );

                // Load order items for each order
                loadOrderItems(order);
                orders.add(order);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return orders;
    }

    /**
     * Get table status information for all tables
     */
    public static List<TableStatus> getAllTableStatuses() {
        List<TableStatus> tableStatuses = new ArrayList<>();

        // Initialize all tables as blank (1-20 for now)
        for (int i = 1; i <= 20; i++) {
            tableStatuses.add(new TableStatus(i));
        }

        // Get current business day
        LocalDate businessDay = BusinessConfig.getBusinessDay(LocalDateTime.now());
        LocalTime dayEndTime = BusinessConfig.getDayEndTime();

        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT o.table_number, o.status, o.total_amount, o.timestamp, o.id " +
                     "FROM orders o " +
                     "WHERE ((DATE(o.timestamp) = ? AND TIME(o.timestamp) >= ?) " +
                     "OR (DATE(o.timestamp) = ? AND TIME(o.timestamp) < ?)) " +
                     "AND o.table_number IS NOT NULL " +
                     "AND o.status IN ('PENDING', 'WORKING', 'READY', 'CONFIRMED', 'KOT_PRINTED') " +
                     "ORDER BY o.table_number, o.timestamp DESC")) {

            // Set parameters for business day calculation
            ps.setString(1, businessDay.toString());
            ps.setString(2, dayEndTime.toString());
            ps.setString(3, businessDay.plusDays(1).toString());
            ps.setString(4, dayEndTime.toString());

            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                int tableNumber = rs.getInt("table_number");
                String status = rs.getString("status");
                double totalAmount = rs.getDouble("total_amount");
                LocalDateTime orderTime = rs.getTimestamp("timestamp").toLocalDateTime();
                int orderId = rs.getInt("id");

                // Find the table status and update it
                for (TableStatus tableStatus : tableStatuses) {
                    if (tableStatus.getTableNumber() == tableNumber && !tableStatus.hasActiveOrder()) {
                        tableStatus.setStatus(status);
                        tableStatus.setTotalAmount(totalAmount);
                        tableStatus.setOrderTime(orderTime);
                        tableStatus.setOrderId(orderId);
                        tableStatus.setHasActiveOrder(true);
                        break;
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return tableStatuses;
    }

    /**
     * Get table status for a specific table
     */
    public static TableStatus getTableStatus(int tableNumber) {
        TableStatus tableStatus = new TableStatus(tableNumber);

        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT o.status, o.total_amount, o.timestamp, o.id " +
                     "FROM orders o " +
                     "WHERE DATE(o.timestamp) = DATE('now') " +
                     "AND o.table_number = ? " +
                     "AND o.status IN ('PENDING', 'WORKING', 'READY', 'CONFIRMED', 'KOT_PRINTED') " +
                     "ORDER BY o.timestamp DESC " +
                     "LIMIT 1")) {

            ps.setInt(1, tableNumber);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                String status = rs.getString("status");
                double totalAmount = rs.getDouble("total_amount");
                LocalDateTime orderTime = rs.getTimestamp("timestamp").toLocalDateTime();
                int orderId = rs.getInt("id");

                tableStatus.setStatus(status);
                tableStatus.setTotalAmount(totalAmount);
                tableStatus.setOrderTime(orderTime);
                tableStatus.setOrderId(orderId);
                tableStatus.setHasActiveOrder(true);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return tableStatus;
    }

    /**
     * Search orders by multiple criteria with case-insensitive partial matching
     */
    public static List<Order> searchOrders(String searchText, String status, String orderType,
                                         LocalDate fromDate, LocalDate toDate) {
        List<Order> orders = new ArrayList<>();
        StringBuilder query = new StringBuilder("SELECT * FROM orders WHERE 1=1 ");
        List<Object> parameters = new ArrayList<>();

        // Add search text filter (search in order ID, table number, customer info)
        if (searchText != null && !searchText.trim().isEmpty()) {
            query.append("AND (CAST(id AS TEXT) LIKE ? OR CAST(table_number AS TEXT) LIKE ? OR LOWER(status) LIKE ?) ");
            String searchPattern = "%" + searchText.toLowerCase() + "%";
            parameters.add(searchPattern);
            parameters.add(searchPattern);
            parameters.add(searchPattern);
        }

        // Add status filter
        if (status != null && !status.equals("All Status") && !status.equals("Active Orders")) {
            query.append("AND status = ? ");
            parameters.add(status);
        } else if ("Active Orders".equals(status)) {
            query.append("AND status IN ('PENDING', 'CONFIRMED', 'PREPARING', 'READY') ");
        }

        // Add order type filter
        if (orderType != null && !orderType.equals("All Types")) {
            if ("Dine In".equals(orderType)) {
                query.append("AND is_takeaway = 0 ");
            } else if ("Takeaway".equals(orderType) || "Delivery".equals(orderType)) {
                query.append("AND is_takeaway = 1 ");
            }
        }

        // Add date range filter
        if (fromDate != null) {
            query.append("AND DATE(timestamp) >= ? ");
            parameters.add(fromDate.toString());
        }
        if (toDate != null) {
            query.append("AND DATE(timestamp) <= ? ");
            parameters.add(toDate.toString());
        }

        query.append("ORDER BY timestamp DESC");

        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(query.toString())) {

            // Set parameters
            for (int i = 0; i < parameters.size(); i++) {
                ps.setObject(i + 1, parameters.get(i));
            }

            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                Order order = new Order(
                    rs.getInt("id"),
                    rs.getObject("table_number", Integer.class),
                    rs.getBoolean("is_takeaway"),
                    rs.getString("status"),
                    rs.getTimestamp("timestamp").toLocalDateTime()
                );

                // Load order items for each order
                loadOrderItems(order);
                orders.add(order);
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }

        return orders;
    }

    /**
     * Get order with full details including items
     */
    public static Order getOrderWithDetails(int orderId) {
        Order order = getOrderById(orderId);
        if (order != null) {
            // Load order items using existing method
            loadOrderItems(order);
        }
        return order;
    }
}