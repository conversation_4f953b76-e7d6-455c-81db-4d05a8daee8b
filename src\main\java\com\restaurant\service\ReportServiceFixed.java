package com.restaurant.service; 
 
import com.restaurant.model.*; 
import java.sql.*; 
import java.time.LocalDate; 
import java.time.LocalDateTime; 
import java.time.temporal.ChronoUnit; 
import java.util.*; 
 
public class ReportServiceFixed { 
    private static final String DB_URL = "*************************"; 
 
    // Generate Daily Report 
    public static DailyReport generateDailyReport(LocalDate date) { 
        DailyReport report = new DailyReport(date); 
 
        try (Connection conn = DriverManager.getConnection(DB_URL)) { 
            // Get daily statistics 
            String sql = "SELECT COUNT(*) as total_orders, " + 
                "COALESCE(SUM(total_amount), 0) as total_revenue, " + 
                "COUNT(CASE WHEN platform = 'Swiggy' THEN 1 END) as swiggy_orders, " + 
                "COUNT(CASE WHEN platform = 'Zomato' THEN 1 END) as zomato_orders, " + 
                "COALESCE(SUM(CASE WHEN platform = 'Swiggy' THEN total_amount ELSE 0 END), 0) as swiggy_revenue, " + 
                "COALESCE(SUM(CASE WHEN platform = 'Zomato' THEN total_amount ELSE 0 END), 0) as zomato_revenue " + 
                "FROM online_orders WHERE DATE(order_time) = ?"; 
 
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) { 
                pstmt.setString(1, date.toString()); 
                ResultSet rs = pstmt.executeQuery(); 
 
                if (rs.next()) { 
                    report.setTotalOrders(rs.getInt("total_orders")); 
                    report.setTotalRevenue(rs.getDouble("total_revenue")); 
                    report.setSwiggyOrders(rs.getInt("swiggy_orders")); 
                    report.setZomatoOrders(rs.getInt("zomato_orders")); 
                    report.setSwiggyRevenue(rs.getDouble("swiggy_revenue")); 
                    report.setZomatoRevenue(rs.getDouble("zomato_revenue")); 
 
                    // Calculate average order value 
                    if (report.getTotalOrders() > 0) { 
                        report.setAvgOrderValue(report.getTotalRevenue() / report.getTotalOrders()); 
                    } 
                } 
            } 
 
            // Find peak hour 
            String peakHourSql = "SELECT strftime('%H', order_time) as hour, COUNT(*) as order_count " + 
                "FROM online_orders WHERE DATE(order_time) = ? " + 
                "GROUP BY strftime('%H', order_time) ORDER BY order_count DESC LIMIT 1"; 
 
            try (PreparedStatement pstmt = conn.prepareStatement(peakHourSql)) { 
                pstmt.setString(1, date.toString()); 
                ResultSet rs = pstmt.executeQuery(); 
 
                if (rs.next()) { 
                    String hour = rs.getString("hour"); 
                    int count = rs.getInt("order_count"); 
                    report.setPeakHour(hour + ":00 (" + count + " orders)"); 
                } 
            } 
 
        } catch (SQLException e) { 
            System.err.println("Error generating daily report: " + e.getMessage()); 
        } 
 
        return report; 
    } 
 
    // Auto-generate and save daily report 
    public static boolean generateAndSaveDailyReport(LocalDate date) { 
        DailyReport report = generateDailyReport(date); 
        return ReportDAO.saveDailyReport(report); 
    } 
 
    // Get analytics summary 
    public static Map<String, Object> getAnalyticsSummary() { 
        Map<String, Object> analytics = new HashMap<>(); 
 
        try (Connection conn = DriverManager.getConnection(DB_URL)) { 
            // Today's stats 
            LocalDate today = LocalDate.now(); 
            DailyReport todayReport = generateDailyReport(today); 
            analytics.put("todayOrders", todayReport.getTotalOrders()); 
            analytics.put("todayRevenue", todayReport.getTotalRevenue()); 
            analytics.put("swiggyPercentage", todayReport.getSwiggyPercentage()); 
            analytics.put("zomatoPercentage", todayReport.getZomatoPercentage()); 
 
            // Default values for week and month 
            analytics.put("weekOrders", 0); 
            analytics.put("weekRevenue", 0.0); 
            analytics.put("monthOrders", 0); 
            analytics.put("monthRevenue", 0.0); 
            analytics.put("monthGrowth", 0.0); 
 
        } catch (Exception e) { 
            System.err.println("Error getting analytics summary: " + e.getMessage()); 
        } 
 
        return analytics; 
    } 
} 
