<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.CustomerHistoryController">
   <Button fx:id="backButton" onAction="#goBack" text="Back to CRM" />
   <Label text="Customer History" />
   <Label text="Name: Arpit Shah" />
   <Label fx:id="customerNameLabel" text="Arpit Shah" />
   <Label fx:id="customerMobileLabel" text="91 XXXXX XXXXX" />
   <Label fx:id="maxOrderLabel" text="2403.00" />
   <Label fx:id="averageBillLabel" text="1282.33" />
   <Label fx:id="comingSinceLabel" text="28-11-2022" />
   <Label fx:id="visitsLabel" text="3 Times" />
   <VBox fx:id="orderHistoryContainer" />
   <Button fx:id="exportBtn" onAction="#exportHistory" text="Export" />
   <Button fx:id="printBtn" onAction="#printHistory" text="Print" />
   <Button fx:id="sendEmailBtn" onAction="#sendEmailReport" text="Email" />
</VBox>
