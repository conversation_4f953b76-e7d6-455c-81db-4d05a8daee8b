@echo off
echo TESTING FINISH LIST WITH SAMPLE DATA...
echo.

echo 🎉 FINISH LIST NOW INCLUDES SAMPLE DATA FOR EASY UNDERSTANDING! 🎉
echo.

echo SAMPLE DATA INCLUDED:
echo ✅ 6 realistic Swig<PERSON> & <PERSON><PERSON>to orders
echo ✅ Orders in different stages (Preparing/Ready/Pricing/Completed)
echo ✅ Real customer names, addresses, and phone numbers
echo ✅ Authentic Indian food items and prices
echo ✅ Special instructions and delivery details
echo ✅ Mixed platforms (3 Swiggy + 3 Zomato orders)
echo.

echo SAMPLE ORDERS YOU'LL SEE:
echo.
echo 🔄 PREPARING ORDERS:
echo   [<PERSON>wiggy] SW1001 - <PERSON><PERSON> - ₹485
echo   Items: Chicken Biryani x2, <PERSON>ar<PERSON> Naan x3, Raita
echo   Special: Extra spicy, no onions
echo.
echo   [Zomato] ZM2004 - Sneha Reddy - ₹275  
echo   Items: Veg Hakka Noodles, Manchurian Dry
echo   Special: Jain food - no onion, garlic, potato
echo.
echo ✅ READY ORDERS:
echo   [<PERSON><PERSON><PERSON>] ZM2002 - <PERSON><PERSON> - ₹320
echo   Items: <PERSON><PERSON>, <PERSON>
echo   Special: Less oil, medium spice
echo.
echo   [<PERSON>wiggy] SW1005 - <PERSON><PERSON><PERSON> - ₹420
echo   Items: Chicken Tikka Masala, Butter Naan x2, Mixed Veg
echo   Special: Extra sauce packets, no plastic cutlery
echo.
echo 💰 PRICING ORDERS:
echo   [Swiggy] SW1003 - Amit Patel - ₹650
echo   Items: Mutton Curry, Jeera Rice x2, Tandoori Roti x4, Gulab Jamun
echo   Special: Pack separately, include extra chutneys
echo.
echo ✔️ COMPLETED ORDERS:
echo   [Zomato] ZM2006 - Anita Gupta - ₹195
echo   Items: Masala Dosa x2, Filter Coffee
echo   Special: Call before delivery
echo.

echo STATISTICS YOU'LL SEE:
echo 📊 Preparing: 2  Ready: 2  Pricing: 1  Completed: 1  Total: 6
echo.

echo Starting application with sample data...
echo.

echo LOGIN INSTRUCTIONS:
echo 1. Username: admin
echo 2. Password: admin123
echo 3. Role: ADMIN (select from dropdown)
echo 4. Click "Login to Dashboard →"
echo 5. Click "🍽️ Finish List" in navigation
echo.

echo WHAT YOU'LL SEE IMMEDIATELY:
echo ✅ 6 sample orders already loaded
echo ✅ Live statistics showing order counts
echo ✅ Professional order cards with all details
echo ✅ Platform badges (Swiggy orange, Zomato red)
echo ✅ Status indicators with colors
echo ✅ Customer information and delivery addresses
echo ✅ Item lists with quantities and prices
echo ✅ Special instructions for each order
echo.

echo FEATURES TO TEST:
echo 🔄 STATUS UPDATES: Use dropdowns to change order stages
echo 🔍 FILTERING: Filter by status (All/Preparing/Ready/Pricing/Completed)
echo 🏢 PLATFORM FILTER: Show only Swiggy or Zomato orders
echo 📊 LIVE STATS: Watch counts update as you change statuses
echo ⚡ BULK ACTIONS: Mark all preparing orders as ready
echo ➕ ADD MORE: Create additional test orders
echo 🗑️ CLEANUP: Clear completed orders
echo.

java -Dprism.order=sw ^
     -Dprism.allowhidpi=false ^
     -Djavafx.animation.pulse=60 ^
     -Djava.awt.headless=false ^
     -Dsqlite.purejava=false ^
     -Xms512m ^
     -Xmx2g ^
     -XX:+UseG1GC ^
     -XX:MaxGCPauseMillis=200 ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics ^
     -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\pdfbox\2.0.27\pdfbox-2.0.27.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\fontbox\2.0.27\fontbox-2.0.27.jar;%USERPROFILE%\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar" ^
     com.restaurant.RestaurantApp

echo.
echo SAMPLE DATA TESTING RESULTS:
echo.

if %ERRORLEVEL% equ 0 (
    echo ✅ APPLICATION WITH SAMPLE DATA WORKED SUCCESSFULLY!
    echo.
    echo 🎉 SAMPLE DATA VERIFICATION CHECKLIST: 🎉
    echo.
    echo ✅ Did you see 6 orders immediately after opening Finish List?
    echo ✅ Did you see statistics: Preparing: 2, Ready: 2, Pricing: 1, Completed: 1?
    echo ✅ Did you see Swiggy orders with orange badges?
    echo ✅ Did you see Zomato orders with red badges?
    echo ✅ Did order cards show customer names and addresses?
    echo ✅ Did you see item lists with quantities and prices?
    echo ✅ Did special instructions appear for each order?
    echo ✅ Could you update order statuses using dropdowns?
    echo ✅ Did filtering by status work (All/Preparing/Ready/Pricing)?
    echo ✅ Did filtering by platform work (All/Swiggy/Zomato)?
    echo ✅ Did statistics update when you changed order statuses?
    echo ✅ Did "Mark All Ready" work for bulk updates?
    echo ✅ Could you add more test orders using "Add Test Order"?
    echo.
    echo If ALL above are YES, then sample data is working perfectly!
    echo.
    echo 🎉 FINISH LIST WITH SAMPLE DATA SUCCESSFULLY IMPLEMENTED! 🎉
    echo.
    echo SAMPLE DATA BENEFITS:
    echo ✅ Immediate understanding of the workflow
    echo ✅ No need to create test orders first
    echo ✅ Realistic restaurant order scenarios
    echo ✅ Demonstrates all order stages
    echo ✅ Shows both Swiggy and Zomato orders
    echo ✅ Includes authentic Indian food items
    echo ✅ Professional customer information
    echo ✅ Ready for production use
    echo.
    echo WORKFLOW DEMONSTRATION:
    echo 🔄 Preparing → Kitchen is cooking the food
    echo ✅ Ready → Food is prepared, ready for packaging
    echo 💰 Pricing → Final pricing and delivery preparation
    echo ✔️ Completed → Handed over to delivery partner
    echo.
) else (
    echo ❌ APPLICATION HAD ISSUES
    echo.
    echo Error code: %ERRORLEVEL%
    echo.
    echo If sample data is not showing, possible issues:
    echo 1. Login failed - use admin/admin123 with ADMIN role
    echo 2. Finish List button not found - check navigation sidebar
    echo 3. Orders not loading - check console for error messages
    echo 4. Database issues - try deleting restaurant.db and restart
    echo.
    echo TROUBLESHOOTING:
    echo - Ensure successful admin login
    echo - Look for "🍽️ Finish List" in navigation
    echo - Check console output for loading messages
    echo - Verify all 6 sample orders appear
    echo.
)

echo.
echo SAMPLE DATA TECHNICAL DETAILS:
echo.
echo 🔧 AUTOMATIC LOADING:
echo   - Sample data loads when no database orders exist
echo   - 6 realistic orders with complete information
echo   - Mixed platforms and order stages
echo   - Authentic Indian restaurant items
echo.
echo 🔧 ORDER DETAILS:
echo   - Customer names, phones, addresses
echo   - Item lists with quantities and prices
echo   - Special cooking instructions
echo   - Estimated preparation times
echo   - Platform-specific order IDs
echo.
echo 🔧 WORKFLOW STAGES:
echo   - 2 orders in Preparing stage
echo   - 2 orders in Ready stage  
echo   - 1 order in Pricing stage
echo   - 1 order in Completed stage
echo.
echo Your Finish List now has comprehensive sample data for easy understanding!
echo Perfect for demonstrating the Swiggy & Zomato order tracking workflow!
echo.
pause
