package com.restaurant.controller;

import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Pos;
import javafx.scene.Parent;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.input.KeyCode;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import java.net.URL;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;

public class AttendanceManagementController implements Initializable {
    
    // FXML Components
    @FXML private DatePicker attendanceDatePicker;
    @FXML private ComboBox<String> departmentFilterCombo;
    @FXML private ComboBox<String> statusFilterCombo;
    @FXML private Label presentCountLabel, absentCountLabel, lateCountLabel, totalStaffLabel;
    @FXML private TableView<AttendanceRecord> attendanceTable;
    @FXML private TableColumn<AttendanceRecord, Integer> staffIdColumn;
    @FXML private TableColumn<AttendanceRecord, String> staffNameColumn;
    @FXML private TableColumn<AttendanceRecord, String> departmentColumn;
    @FXML private TableColumn<AttendanceRecord, String> checkInColumn;
    @FXML private TableColumn<AttendanceRecord, String> checkOutColumn;
    @FXML private TableColumn<AttendanceRecord, String> hoursWorkedColumn;
    @FXML private TableColumn<AttendanceRecord, String> statusColumn;
    @FXML private TableColumn<AttendanceRecord, String> actionsColumn;
    
    // Dialog Components
    @FXML private VBox attendanceDialog;
    @FXML private Label dialogTitle;
    @FXML private ComboBox<String> staffCombo;
    @FXML private DatePicker markDatePicker;
    @FXML private TextField checkInTimeField, checkOutTimeField;
    @FXML private ComboBox<String> attendanceStatusCombo;
    @FXML private TextArea notesArea;
    
    // Data
    private ObservableList<AttendanceRecord> attendanceRecords = FXCollections.observableArrayList();
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        setupDatePicker();
        setupComboBoxes();
        setupTable();
        setupSampleData();
        setupEscapeKeyHandler();
        updateSummaryCards();
    }
    
    private void setupDatePicker() {
        attendanceDatePicker.setValue(LocalDate.now());
        markDatePicker.setValue(LocalDate.now());
    }
    
    private void setupComboBoxes() {
        // Department filter
        departmentFilterCombo.setItems(FXCollections.observableArrayList(
            "All Departments", "Kitchen", "Service", "Management", "Cleaning"
        ));
        departmentFilterCombo.setValue("All Departments");
        
        // Status filter
        statusFilterCombo.setItems(FXCollections.observableArrayList(
            "All Status", "Present", "Absent", "Late", "Half Day"
        ));
        statusFilterCombo.setValue("All Status");
        
        // Staff combo for marking attendance - load from database
        loadEmployeeNames();
        
        // Attendance status combo
        attendanceStatusCombo.setItems(FXCollections.observableArrayList(
            "Present", "Absent", "Late", "Half Day", "Sick Leave", "Personal Leave"
        ));
        attendanceStatusCombo.setValue("Present");
    }
    
    private void loadEmployeeNames() {
        try {
            List<String> employeeNames = com.restaurant.model.EmployeeDAO.getEmployeeNames();
            staffCombo.setItems(FXCollections.observableArrayList(employeeNames));
        } catch (Exception e) {
            // Fallback to sample data if database fails
            staffCombo.setItems(FXCollections.observableArrayList(
                "John Doe", "Jane Smith", "Mike Johnson", "Sarah Wilson", "David Brown",
                "Lisa Garcia", "Tom Anderson", "Emily Davis", "Chris Martinez", "Anna Taylor"
            ));
        }
    }

    private void setupTable() {
        staffIdColumn.setCellValueFactory(new PropertyValueFactory<>("staffId"));
        staffNameColumn.setCellValueFactory(new PropertyValueFactory<>("staffName"));
        departmentColumn.setCellValueFactory(new PropertyValueFactory<>("department"));
        checkInColumn.setCellValueFactory(new PropertyValueFactory<>("checkIn"));
        checkOutColumn.setCellValueFactory(new PropertyValueFactory<>("checkOut"));
        hoursWorkedColumn.setCellValueFactory(new PropertyValueFactory<>("hoursWorked"));
        statusColumn.setCellValueFactory(new PropertyValueFactory<>("status"));
        
        // Setup actions column
        actionsColumn.setCellFactory(column -> new TableCell<AttendanceRecord, String>() {
            private final Button editButton = new Button("Edit");
            private final Button deleteButton = new Button("Delete");
            private final HBox buttonsBox = new HBox(5, editButton, deleteButton);
            
            {
                editButton.getStyleClass().add("edit-button");
                deleteButton.getStyleClass().add("delete-button");
                buttonsBox.setAlignment(Pos.CENTER);
                
                editButton.setOnAction(event -> {
                    AttendanceRecord record = getTableView().getItems().get(getIndex());
                    editAttendance(record);
                });
                
                deleteButton.setOnAction(event -> {
                    AttendanceRecord record = getTableView().getItems().get(getIndex());
                    deleteAttendance(record);
                });
            }
            
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                setGraphic(empty ? null : buttonsBox);
            }
        });
        
        attendanceTable.setItems(attendanceRecords);
    }
    
    private void setupSampleData() {
        // Load attendance records from database
        loadAttendanceRecordsFromDatabase();
    }

    private void loadAttendanceRecordsFromDatabase() {
        attendanceRecords.clear();

        // Load today's attendance records and convert to AttendanceRecord format
        List<com.restaurant.model.Attendance> dbRecords = com.restaurant.model.AttendanceDAO.getTodayAttendance();

        for (com.restaurant.model.Attendance attendance : dbRecords) {
            AttendanceRecord record = new AttendanceRecord(
                attendance.getId(),
                attendance.getEmployeeName(),
                attendance.getDepartment(),
                attendance.getCheckInTimeDisplay(),
                attendance.getCheckOutTimeDisplay(),
                attendance.getHoursWorkedDisplay(),
                attendance.getStatus()
            );
            attendanceRecords.add(record);
        }

        // If no records exist, create sample data for demonstration
        if (attendanceRecords.isEmpty()) {
            createSampleAttendanceData();
        }
    }

    private void createSampleAttendanceData() {
        attendanceRecords.addAll(FXCollections.observableArrayList(
            new AttendanceRecord(1, "John Doe", "Kitchen", "09:00", "17:00", "8.0", "Present"),
            new AttendanceRecord(2, "Jane Smith", "Service", "09:15", "17:00", "7.75", "Late"),
            new AttendanceRecord(3, "Mike Johnson", "Kitchen", "09:00", "17:00", "8.0", "Present"),
            new AttendanceRecord(4, "Sarah Wilson", "Management", "09:00", "18:00", "9.0", "Present"),
            new AttendanceRecord(5, "David Brown", "Service", "-", "-", "0.0", "Absent"),
            new AttendanceRecord(6, "Lisa Garcia", "Kitchen", "09:30", "17:00", "7.5", "Late"),
            new AttendanceRecord(7, "Tom Anderson", "Cleaning", "08:00", "16:00", "8.0", "Present"),
            new AttendanceRecord(8, "Emily Davis", "Service", "09:00", "13:00", "4.0", "Half Day"),
            new AttendanceRecord(9, "Chris Martinez", "Kitchen", "09:00", "17:00", "8.0", "Present"),
            new AttendanceRecord(10, "Anna Taylor", "Service", "-", "-", "0.0", "Sick Leave")
        ));
    }
    
    private void setupEscapeKeyHandler() {
        Platform.runLater(() -> {
            if (attendanceTable != null && attendanceTable.getScene() != null) {
                attendanceTable.getScene().setOnKeyPressed(event -> {
                    if (event.getCode() == KeyCode.ESCAPE) {
                        if (attendanceDialog.isVisible()) {
                            closeAttendanceDialog();
                        } else {
                            goBack();
                        }
                    }
                });
            }
        });
    }
    
    private void updateSummaryCards() {
        int present = 0, absent = 0, late = 0, total = attendanceRecords.size();
        
        for (AttendanceRecord record : attendanceRecords) {
            switch (record.getStatus()) {
                case "Present":
                    present++;
                    break;
                case "Absent":
                case "Sick Leave":
                    absent++;
                    break;
                case "Late":
                    late++;
                    present++; // Late is still present
                    break;
                case "Half Day":
                    present++;
                    break;
            }
        }
        
        presentCountLabel.setText(String.valueOf(present));
        absentCountLabel.setText(String.valueOf(absent));
        lateCountLabel.setText(String.valueOf(late));
        totalStaffLabel.setText(String.valueOf(total));
    }
    
    @FXML
    private void markAttendance() {
        clearAttendanceForm();
        dialogTitle.setText("Mark Attendance");
        showAttendanceDialog();
    }
    
    @FXML
    private void generateReport() {
        try {
            // Get selected date
            LocalDate selectedDate = attendanceDatePicker.getValue();
            if (selectedDate == null) {
                selectedDate = LocalDate.now();
            }

            // Generate attendance report
            com.restaurant.util.AttendanceReportGenerator.generateReport(selectedDate);
            showAlert("Success", "Attendance report generated successfully!");

        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Failed to generate report: " + e.getMessage());
        }
    }
    
    @FXML
    private void refreshAttendance() {
        // Refresh the attendance data based on selected filters
        updateSummaryCards();
        showAlert("Refreshed", "Attendance data has been refreshed for " + attendanceDatePicker.getValue());
    }
    
    private void showAttendanceDialog() {
        attendanceDialog.setVisible(true);
        attendanceDialog.setManaged(true);
    }
    
    @FXML
    private void closeAttendanceDialog() {
        attendanceDialog.setVisible(false);
        attendanceDialog.setManaged(false);
    }
    
    @FXML
    private void saveAttendance() {
        if (validateAttendanceForm()) {
            // Create new attendance record
            int newId = attendanceRecords.size() + 1;
            String staffName = staffCombo.getValue();
            String department = getDepartmentForStaff(staffName);
            String checkIn = checkInTimeField.getText().trim();
            String checkOut = checkOutTimeField.getText().trim();
            String status = attendanceStatusCombo.getValue();
            String hoursWorked = calculateHours(checkIn, checkOut);
            
            AttendanceRecord newRecord = new AttendanceRecord(newId, staffName, department, 
                checkIn.isEmpty() ? "-" : checkIn, 
                checkOut.isEmpty() ? "-" : checkOut, 
                hoursWorked, status);
            
            attendanceRecords.add(newRecord);
            updateSummaryCards();
            closeAttendanceDialog();
            showAlert("Success", "Attendance marked successfully for " + staffName);
        }
    }
    
    private boolean validateAttendanceForm() {
        if (staffCombo.getValue() == null || staffCombo.getValue().isEmpty()) {
            showAlert("Validation Error", "Please select a staff member");
            return false;
        }
        if (markDatePicker.getValue() == null) {
            showAlert("Validation Error", "Please select a date");
            return false;
        }
        if (attendanceStatusCombo.getValue() == null) {
            showAlert("Validation Error", "Please select attendance status");
            return false;
        }
        return true;
    }
    
    private String getDepartmentForStaff(String staffName) {
        // Simple mapping - in real app, this would come from database
        if (staffName.contains("John") || staffName.contains("Mike") || staffName.contains("Lisa") || staffName.contains("Chris")) {
            return "Kitchen";
        } else if (staffName.contains("Jane") || staffName.contains("David") || staffName.contains("Emily") || staffName.contains("Anna")) {
            return "Service";
        } else if (staffName.contains("Sarah")) {
            return "Management";
        } else {
            return "Cleaning";
        }
    }
    
    private String calculateHours(String checkIn, String checkOut) {
        if (checkIn.isEmpty() || checkOut.isEmpty()) {
            return "0.0";
        }
        
        try {
            LocalTime inTime = LocalTime.parse(checkIn);
            LocalTime outTime = LocalTime.parse(checkOut);
            long minutes = java.time.Duration.between(inTime, outTime).toMinutes();
            double hours = minutes / 60.0;
            return String.format("%.1f", hours);
        } catch (Exception e) {
            return "0.0";
        }
    }
    
    private void clearAttendanceForm() {
        staffCombo.setValue(null);
        markDatePicker.setValue(LocalDate.now());
        checkInTimeField.clear();
        checkOutTimeField.clear();
        attendanceStatusCombo.setValue("Present");
        notesArea.clear();
    }
    
    private void editAttendance(AttendanceRecord record) {
        // Populate form with existing data
        staffCombo.setValue(record.getStaffName());
        checkInTimeField.setText(record.getCheckIn().equals("-") ? "" : record.getCheckIn());
        checkOutTimeField.setText(record.getCheckOut().equals("-") ? "" : record.getCheckOut());
        attendanceStatusCombo.setValue(record.getStatus());
        
        dialogTitle.setText("Edit Attendance - " + record.getStaffName());
        showAttendanceDialog();
    }
    
    private void deleteAttendance(AttendanceRecord record) {
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("Delete Attendance");
        confirmAlert.setHeaderText(null);
        confirmAlert.setContentText("Are you sure you want to delete attendance record for " + record.getStaffName() + "?");
        
        confirmAlert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                attendanceRecords.remove(record);
                updateSummaryCards();
                showAlert("Deleted", "Attendance record deleted successfully");
            }
        });
    }
    
    @FXML
    private void goBack() {
        try {
            Parent userManagementView = FXMLLoader.load(getClass().getResource("/fxml/UserManagement.fxml"));
            Stage stage = (Stage) attendanceTable.getScene().getWindow();
            stage.getScene().setRoot(userManagementView);
        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Could not return to User Management");
        }
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    // Data class for attendance records
    public static class AttendanceRecord {
        private int staffId;
        private String staffName;
        private String department;
        private String checkIn;
        private String checkOut;
        private String hoursWorked;
        private String status;
        
        public AttendanceRecord(int staffId, String staffName, String department, 
                              String checkIn, String checkOut, String hoursWorked, String status) {
            this.staffId = staffId;
            this.staffName = staffName;
            this.department = department;
            this.checkIn = checkIn;
            this.checkOut = checkOut;
            this.hoursWorked = hoursWorked;
            this.status = status;
        }
        
        // Getters
        public int getStaffId() { return staffId; }
        public String getStaffName() { return staffName; }
        public String getDepartment() { return department; }
        public String getCheckIn() { return checkIn; }
        public String getCheckOut() { return checkOut; }
        public String getHoursWorked() { return hoursWorked; }
        public String getStatus() { return status; }
        
        // Setters
        public void setStaffId(int staffId) { this.staffId = staffId; }
        public void setStaffName(String staffName) { this.staffName = staffName; }
        public void setDepartment(String department) { this.department = department; }
        public void setCheckIn(String checkIn) { this.checkIn = checkIn; }
        public void setCheckOut(String checkOut) { this.checkOut = checkOut; }
        public void setHoursWorked(String hoursWorked) { this.hoursWorked = hoursWorked; }
        public void setStatus(String status) { this.status = status; }
    }
}
