<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/17.0.2-ea" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.MenuManagementController" stylesheets="@../css/application.css">
   <top>
      <!-- Header Section -->
      <VBox styleClass="menu-header">
         <children>
            <!-- Top Navigation -->
            <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="top-nav">
               <children>
                  <Button fx:id="backButton" mnemonicParsing="false" onAction="#goBack" styleClass="back-button" text="← Back" />
                  <Label styleClass="restaurant-name" text="Restaurant Management">
                     <font>
                        <Font name="System Bold" size="18.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button mnemonicParsing="false" onAction="#openCategoryItemManager" styleClass="secondary-button" text="📋 Category Manager" />
                  <Label fx:id="menuInfoLabel" styleClass="table-info" text="Menu Management">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
               </children>
               <padding>
                  <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
               </padding>
            </HBox>

            <!-- Order Type Tabs -->
            <HBox styleClass="order-tabs">
               <children>
                  <Button fx:id="allItemsTab" mnemonicParsing="false" onAction="#showAllItems" styleClass="tab-button,tab-active" text="All Items" />
                  <Button fx:id="beveragesTab" mnemonicParsing="false" onAction="#showBeverages" styleClass="tab-button" text="Beverages" />
                  <Button fx:id="burgersTab" mnemonicParsing="false" onAction="#showBurgers" styleClass="tab-button" text="Burgers" />
                  <Button fx:id="chickenTab" mnemonicParsing="false" onAction="#showChicken" styleClass="tab-button" text="Chicken" />
                  <Button fx:id="chineseTab" mnemonicParsing="false" onAction="#showChinese" styleClass="tab-button" text="Chinese" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="20.0" right="20.0" top="10.0" />
               </padding>
            </HBox>

            <!-- Search Section -->
            <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="search-section">
               <children>
                  <TextField fx:id="searchField" onKeyReleased="#searchItems" promptText="Search menu items..." styleClass="search-field" HBox.hgrow="ALWAYS" />
                  <Button mnemonicParsing="false" onAction="#searchItems" styleClass="search-button" text="Search" />
                  <Button mnemonicParsing="false" onAction="#addNewItem" styleClass="add-button" text="+ Add Item" />
               </children>
               <padding>
                  <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
               </padding>
            </HBox>
         </children>
      </VBox>
   </top>

   <center>
      <!-- Main Content Area -->
      <HBox>
         <children>
            <!-- Categories Panel -->
            <VBox prefWidth="200.0" styleClass="categories-panel">
               <children>
                  <Label styleClass="categories-title" text="Categories">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <ListView fx:id="categoriesList" styleClass="categories-list" VBox.vgrow="ALWAYS" />
               </children>
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>
            </VBox>

            <!-- Menu Items Grid -->
            <ScrollPane fitToWidth="true" styleClass="universal-scroll-pane" hbarPolicy="NEVER" vbarPolicy="ALWAYS" HBox.hgrow="ALWAYS">
               <content>
                  <VBox spacing="20.0">
                     <children>
                        <GridPane fx:id="menuGrid" hgap="20.0" styleClass="menu-grid" vgap="20.0" />
                     </children>
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                  </VBox>
               </content>
            </ScrollPane>

            <!-- Management Panel -->
            <VBox prefWidth="350.0" styleClass="order-panel">
               <children>
                  <!-- Header -->
                  <VBox styleClass="order-header">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label styleClass="order-title" text="Menu Management">
                                 <font>
                                    <Font name="System Bold" size="16.0" />
                                 </font>
                              </Label>
                              <Region HBox.hgrow="ALWAYS" />
                              <Label fx:id="itemCountLabel" styleClass="item-count" text="0 Items" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>

                  <!-- Item Form -->
                  <ScrollPane fitToWidth="true" styleClass="universal-scroll-pane" hbarPolicy="NEVER" vbarPolicy="ALWAYS" VBox.vgrow="ALWAYS">
                     <content>
                        <VBox fx:id="itemFormContainer" spacing="15.0" styleClass="order-items">
                           <children>
                              <Label styleClass="form-section-title" text="Item Details">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>

                              <VBox spacing="8.0">
                                 <children>
                                    <Label text="Item Name:" />
                                    <TextField fx:id="itemNameField" promptText="Enter item name" styleClass="form-field" />
                                 </children>
                              </VBox>

                              <VBox spacing="8.0">
                                 <children>
                                    <Label text="Price (₹):" />
                                    <TextField fx:id="itemPriceField" promptText="Enter price" styleClass="form-field" />
                                 </children>
                              </VBox>

                              <VBox spacing="8.0">
                                 <children>
                                    <Label text="Category:" />
                                    <ComboBox fx:id="itemCategoryComboBox" prefWidth="200.0" promptText="Select category" styleClass="form-field" />
                                 </children>
                              </VBox>

                              <VBox spacing="8.0">
                                 <children>
                                    <Label text="Description:" />
                                    <TextArea fx:id="itemDescriptionArea" prefHeight="80.0" promptText="Enter description" styleClass="form-field" />
                                 </children>
                              </VBox>

                              <CheckBox fx:id="itemAvailableCheckBox" selected="true" styleClass="form-checkbox" text="Item is available" />
                           </children>
                        </VBox>
                     </content>
                  </ScrollPane>

                  <!-- Summary Section -->
                  <VBox styleClass="order-summary">
                     <children>
                        <HBox alignment="CENTER_LEFT">
                           <children>
                              <Label styleClass="total-label" text="Total Items:" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Label fx:id="totalItemsLabel" styleClass="total-amount" text="0" />
                           </children>
                        </HBox>
                        <HBox alignment="CENTER_LEFT">
                           <children>
                              <Label styleClass="total-label" text="Available:" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Label fx:id="availableItemsLabel" styleClass="total-amount" text="0" />
                           </children>
                        </HBox>
                        <HBox alignment="CENTER_LEFT">
                           <children>
                              <Label styleClass="total-label" text="Categories:" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Label fx:id="categoriesCountLabel" styleClass="total-amount" text="0" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>

                  <!-- Action Buttons - Improved Alignment -->
                  <VBox styleClass="action-buttons-container" spacing="15.0" alignment="CENTER">
                     <children>
                        <Button fx:id="saveItemButton" mnemonicParsing="false" onAction="#saveItem" styleClass="primary-action-button" text="💾 Save Item" prefWidth="220.0" maxWidth="220.0" minWidth="220.0" prefHeight="45.0" />
                        <Button fx:id="updateItemButton" mnemonicParsing="false" onAction="#updateItem" styleClass="secondary-action-button" text="✏️ Update Item" visible="false" prefWidth="220.0" maxWidth="220.0" minWidth="220.0" prefHeight="45.0" />
                        <Button fx:id="clearFormButton" mnemonicParsing="false" onAction="#clearForm" styleClass="tertiary-action-button" text="🔄 Clear Form" prefWidth="220.0" maxWidth="220.0" minWidth="220.0" prefHeight="45.0" />
                     </children>
                     <padding>
                        <Insets bottom="25.0" left="15.0" right="15.0" top="25.0" />
                     </padding>
                  </VBox>
               </children>
            </VBox>
         </children>
      </HBox>
   </center>
</BorderPane>
