/* ===== FINISH LIST PANEL STYLES ===== */

/* Header Section */
.header-section {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1px 0;
}

.page-title {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.filter-label {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-font-weight: bold;
}

.status-filter, .platform-filter {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-font-size: 12px;
}

.refresh-button {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 8px 16px;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
}

.refresh-button:hover {
    -fx-background-color: #218838;
}

/* Statistics Section */
.stats-section {
    -fx-background-color: #ffffff;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1px 0;
}

.stat-card {
    -fx-background-color: white;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 20px;
    -fx-spacing: 5px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 8, 0, 0, 2);
    -fx-min-width: 120px;
}

.stat-card.preparing {
    -fx-border-color: #ff9800;
    -fx-border-width: 2px;
}

.stat-card.ready {
    -fx-border-color: #4caf50;
    -fx-border-width: 2px;
}

.stat-card.pricing {
    -fx-border-color: #2196f3;
    -fx-border-width: 2px;
}

.stat-card.total {
    -fx-border-color: #9c27b0;
    -fx-border-width: 2px;
}

.stat-number {
    -fx-font-size: 32px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.stat-label {
    -fx-font-size: 14px;
    -fx-text-fill: #6c757d;
    -fx-font-weight: bold;
}

/* Orders Container */
.orders-scroll-pane {
    -fx-background-color: #f8f9fa;
}

.orders-container {
    -fx-background-color: #f8f9fa;
}

/* Action Section */
.action-section {
    -fx-background-color: #ffffff;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px 0 0 0;
}

.test-button {
    -fx-background-color: #17a2b8;
    -fx-text-fill: white;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 10px 20px;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
}

.test-button:hover {
    -fx-background-color: #138496;
}

.bulk-action-button {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 10px 20px;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
}

.bulk-action-button:hover {
    -fx-background-color: #218838;
}

.clear-button {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 10px 20px;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
}

.clear-button:hover {
    -fx-background-color: #c82333;
}

/* Order Card Animations */
.order-card {
    -fx-background-color: white;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 15px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);
    -fx-cursor: hand;
}

.order-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 8, 0, 0, 4);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

/* Platform Badges */
.platform-swiggy {
    -fx-background-color: #fc8019;
    -fx-text-fill: white;
    -fx-padding: 4px 8px;
    -fx-background-radius: 12px;
    -fx-font-weight: bold;
    -fx-font-size: 12px;
}

.platform-zomato {
    -fx-background-color: #e23744;
    -fx-text-fill: white;
    -fx-padding: 4px 8px;
    -fx-background-radius: 12px;
    -fx-font-weight: bold;
    -fx-font-size: 12px;
}

/* Status Badges */
.status-preparing {
    -fx-background-color: #ff9800;
    -fx-text-fill: white;
    -fx-padding: 4px 8px;
    -fx-background-radius: 6px;
    -fx-font-size: 11px;
}

.status-ready {
    -fx-background-color: #4caf50;
    -fx-text-fill: white;
    -fx-padding: 4px 8px;
    -fx-background-radius: 6px;
    -fx-font-size: 11px;
}

.status-pricing {
    -fx-background-color: #2196f3;
    -fx-text-fill: white;
    -fx-padding: 4px 8px;
    -fx-background-radius: 6px;
    -fx-font-size: 11px;
}

.status-completed {
    -fx-background-color: #9e9e9e;
    -fx-text-fill: white;
    -fx-padding: 4px 8px;
    -fx-background-radius: 6px;
    -fx-font-size: 11px;
}

/* Status Action Buttons */
.status-button {
    -fx-font-size: 10px;
    -fx-padding: 3px 8px;
    -fx-background-radius: 4px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
    -fx-border-width: 0;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 2, 0, 0, 1);
}

.status-button:hover {
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 3, 0, 0, 2);
}

.status-button:pressed {
    -fx-scale-x: 0.95;
    -fx-scale-y: 0.95;
}

.status-button-preparing {
    -fx-background-color: #ff9800;
    -fx-text-fill: white;
}

.status-button-preparing:hover {
    -fx-background-color: #f57c00;
}

.status-button-ready {
    -fx-background-color: #4caf50;
    -fx-text-fill: white;
}

.status-button-ready:hover {
    -fx-background-color: #388e3c;
}

.status-button-pricing {
    -fx-background-color: #2196f3;
    -fx-text-fill: white;
}

.status-button-pricing:hover {
    -fx-background-color: #1976d2;
}

.status-button-completed {
    -fx-background-color: #9e9e9e;
    -fx-text-fill: white;
}

.status-button-completed:hover {
    -fx-background-color: #757575;
}

.status-button-new {
    -fx-background-color: #f44336;
    -fx-text-fill: white;
}

.status-button-new:hover {
    -fx-background-color: #d32f2f;
}
