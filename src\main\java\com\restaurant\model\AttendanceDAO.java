package com.restaurant.model;

import java.sql.*;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class AttendanceDAO {
    
    /**
     * Get all attendance records
     */
    public static List<Attendance> getAllAttendanceRecords() {
        List<Attendance> records = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(
                     "SELECT a.*, e.name as employee_name, e.department " +
                     "FROM attendance a " +
                     "JOIN employees e ON a.employee_id = e.id " +
                     "ORDER BY a.attendance_date DESC, e.name")) {
            
            while (rs.next()) {
                Attendance attendance = createAttendanceFromResultSet(rs);
                records.add(attendance);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return records;
    }
    
    /**
     * Get attendance records for a specific date
     */
    public static List<Attendance> getAttendanceByDate(LocalDate date) {
        List<Attendance> records = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT a.*, e.name as employee_name, e.department " +
                     "FROM attendance a " +
                     "JOIN employees e ON a.employee_id = e.id " +
                     "WHERE a.attendance_date = ? " +
                     "ORDER BY e.name")) {
            
            ps.setDate(1, Date.valueOf(date));
            ResultSet rs = ps.executeQuery();
            
            while (rs.next()) {
                Attendance attendance = createAttendanceFromResultSet(rs);
                records.add(attendance);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return records;
    }
    
    /**
     * Get attendance records for a specific employee
     */
    public static List<Attendance> getAttendanceByEmployee(int employeeId) {
        List<Attendance> records = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT a.*, e.name as employee_name, e.department " +
                     "FROM attendance a " +
                     "JOIN employees e ON a.employee_id = e.id " +
                     "WHERE a.employee_id = ? " +
                     "ORDER BY a.attendance_date DESC")) {
            
            ps.setInt(1, employeeId);
            ResultSet rs = ps.executeQuery();
            
            while (rs.next()) {
                Attendance attendance = createAttendanceFromResultSet(rs);
                records.add(attendance);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return records;
    }
    
    /**
     * Get attendance record for specific employee and date
     */
    public static Attendance getAttendanceByEmployeeAndDate(int employeeId, LocalDate date) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT a.*, e.name as employee_name, e.department " +
                     "FROM attendance a " +
                     "JOIN employees e ON a.employee_id = e.id " +
                     "WHERE a.employee_id = ? AND a.attendance_date = ?")) {
            
            ps.setInt(1, employeeId);
            ps.setDate(2, Date.valueOf(date));
            ResultSet rs = ps.executeQuery();
            
            if (rs.next()) {
                return createAttendanceFromResultSet(rs);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * Add new attendance record
     */
    public static boolean addAttendance(Attendance attendance) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "INSERT INTO attendance (employee_id, attendance_date, check_in_time, check_out_time, " +
                     "status, hours_worked, notes, created_at, last_updated) " +
                     "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)", Statement.RETURN_GENERATED_KEYS)) {
            
            ps.setInt(1, attendance.getEmployeeId());
            ps.setDate(2, Date.valueOf(attendance.getAttendanceDate()));
            ps.setTime(3, attendance.getCheckInTime() != null ? Time.valueOf(attendance.getCheckInTime()) : null);
            ps.setTime(4, attendance.getCheckOutTime() != null ? Time.valueOf(attendance.getCheckOutTime()) : null);
            ps.setString(5, attendance.getStatus());
            ps.setDouble(6, attendance.getHoursWorked());
            ps.setString(7, attendance.getNotes());
            ps.setTimestamp(8, Timestamp.valueOf(LocalDateTime.now()));
            ps.setTimestamp(9, Timestamp.valueOf(LocalDateTime.now()));
            
            int result = ps.executeUpdate();
            
            if (result > 0) {
                ResultSet rs = ps.getGeneratedKeys();
                if (rs.next()) {
                    attendance.setId(rs.getInt(1));
                }
                return true;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Update attendance record
     */
    public static boolean updateAttendance(Attendance attendance) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "UPDATE attendance SET employee_id = ?, attendance_date = ?, check_in_time = ?, check_out_time = ?, " +
                     "status = ?, hours_worked = ?, notes = ?, last_updated = ? WHERE id = ?")) {
            
            ps.setInt(1, attendance.getEmployeeId());
            ps.setDate(2, Date.valueOf(attendance.getAttendanceDate()));
            ps.setTime(3, attendance.getCheckInTime() != null ? Time.valueOf(attendance.getCheckInTime()) : null);
            ps.setTime(4, attendance.getCheckOutTime() != null ? Time.valueOf(attendance.getCheckOutTime()) : null);
            ps.setString(5, attendance.getStatus());
            ps.setDouble(6, attendance.getHoursWorked());
            ps.setString(7, attendance.getNotes());
            ps.setTimestamp(8, Timestamp.valueOf(LocalDateTime.now()));
            ps.setInt(9, attendance.getId());
            
            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Delete attendance record
     */
    public static boolean deleteAttendance(int id) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("DELETE FROM attendance WHERE id = ?")) {
            
            ps.setInt(1, id);
            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Get attendance records for date range
     */
    public static List<Attendance> getAttendanceByDateRange(LocalDate fromDate, LocalDate toDate) {
        List<Attendance> records = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT a.*, e.name as employee_name, e.department " +
                     "FROM attendance a " +
                     "JOIN employees e ON a.employee_id = e.id " +
                     "WHERE a.attendance_date BETWEEN ? AND ? " +
                     "ORDER BY a.attendance_date DESC, e.name")) {
            
            ps.setDate(1, Date.valueOf(fromDate));
            ps.setDate(2, Date.valueOf(toDate));
            ResultSet rs = ps.executeQuery();
            
            while (rs.next()) {
                Attendance attendance = createAttendanceFromResultSet(rs);
                records.add(attendance);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return records;
    }
    
    /**
     * Get today's attendance records
     */
    public static List<Attendance> getTodayAttendance() {
        return getAttendanceByDate(LocalDate.now());
    }
    
    /**
     * Mark employee as present
     */
    public static boolean markPresent(int employeeId, LocalDate date, LocalTime checkIn, LocalTime checkOut) {
        Attendance existing = getAttendanceByEmployeeAndDate(employeeId, date);
        
        if (existing != null) {
            existing.markPresent(checkIn, checkOut);
            return updateAttendance(existing);
        } else {
            Employee employee = EmployeeDAO.getEmployeeById(employeeId);
            if (employee != null) {
                Attendance attendance = new Attendance(employeeId, employee.getName(), employee.getDepartment(), date);
                attendance.markPresent(checkIn, checkOut);
                return addAttendance(attendance);
            }
        }
        return false;
    }
    
    /**
     * Mark employee as absent
     */
    public static boolean markAbsent(int employeeId, LocalDate date) {
        Attendance existing = getAttendanceByEmployeeAndDate(employeeId, date);
        
        if (existing != null) {
            existing.markAbsent();
            return updateAttendance(existing);
        } else {
            Employee employee = EmployeeDAO.getEmployeeById(employeeId);
            if (employee != null) {
                Attendance attendance = new Attendance(employeeId, employee.getName(), employee.getDepartment(), date);
                attendance.markAbsent();
                return addAttendance(attendance);
            }
        }
        return false;
    }
    
    /**
     * Helper method to create Attendance object from ResultSet
     */
    private static Attendance createAttendanceFromResultSet(ResultSet rs) throws SQLException {
        Attendance attendance = new Attendance(
            rs.getInt("id"),
            rs.getInt("employee_id"),
            rs.getString("employee_name"),
            rs.getString("department"),
            rs.getDate("attendance_date").toLocalDate(),
            rs.getTime("check_in_time") != null ? rs.getTime("check_in_time").toLocalTime() : null,
            rs.getTime("check_out_time") != null ? rs.getTime("check_out_time").toLocalTime() : null,
            rs.getString("status")
        );
        
        attendance.setHoursWorked(rs.getDouble("hours_worked"));
        attendance.setNotes(rs.getString("notes"));
        attendance.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
        attendance.setLastUpdated(rs.getTimestamp("last_updated").toLocalDateTime());
        
        return attendance;
    }
}
