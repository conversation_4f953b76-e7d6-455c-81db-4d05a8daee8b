package com.restaurant.util;

import javafx.scene.media.Media;
import javafx.scene.media.MediaPlayer;
import javafx.util.Duration;

import java.io.File;
import java.net.URI;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;

/**
 * MP3 Audio Player for Swiggy and Zomato notification sounds
 * Handles continuous looping and platform-specific audio files
 */
public class MP3AudioPlayer {
    
    private static MP3AudioPlayer instance;
    private final ConcurrentHashMap<String, MediaPlayer> activePlayers = new ConcurrentHashMap<>();
    private boolean audioEnabled = true;
    
    // Audio file paths
    private static final String SOUNDS_DIR = "sounds/";
    private static final String SWIGGY_AUDIO = "swiggy-notification.mp3";
    private static final String ZOMATO_AUDIO = "zomato-notification.mp3";
    private static final String WOK_KA_TADKA_AUDIO = "wok-ka-tadka-notification.mp3";
    private static final String URGENT_AUDIO = "mixkit-urgent-simple-tone-loop-2976.mp3";
    private static final String DEFAULT_AUDIO = "mixkit-urgent-simple-tone-loop-2976.mp3";
    
    private MP3AudioPlayer() {}
    
    public static MP3AudioPlayer getInstance() {
        if (instance == null) {
            instance = new MP3AudioPlayer();
        }
        return instance;
    }
    
    /**
     * Play Swiggy notification sound (single play)
     */
    public void playSwiggyNotification() {
        if (!audioEnabled) return;
        
        CompletableFuture.runAsync(() -> {
            try {
                String audioFile = findAudioFile(SWIGGY_AUDIO, DEFAULT_AUDIO);
                playAudioFile(audioFile, false, "swiggy-notification");
                System.out.println("🟠 Playing Swiggy notification sound: " + audioFile);
            } catch (Exception e) {
                System.err.println("Error playing Swiggy notification: " + e.getMessage());
                fallbackToSystemBeep("swiggy");
            }
        });
    }
    
    /**
     * Play Zomato notification sound (single play)
     */
    public void playZomatoNotification() {
        if (!audioEnabled) return;

        CompletableFuture.runAsync(() -> {
            try {
                String audioFile = findAudioFile(ZOMATO_AUDIO, DEFAULT_AUDIO);
                playAudioFile(audioFile, false, "zomato-notification");
                System.out.println("🔴 Playing Zomato notification sound: " + audioFile);
            } catch (Exception e) {
                System.err.println("Error playing Zomato notification: " + e.getMessage());
                fallbackToSystemBeep("zomato");
            }
        });
    }

    /**
     * Play single Wok Ka Tadka notification sound
     */
    public void playWokKaTadkaNotification() {
        if (!audioEnabled) return;

        CompletableFuture.runAsync(() -> {
            try {
                String audioFile = findAudioFile(WOK_KA_TADKA_AUDIO, DEFAULT_AUDIO);
                playAudioFile(audioFile, false, "wok-ka-tadka-single");
                System.out.println("🟢 Playing Wok Ka Tadka notification sound: " + audioFile);
            } catch (Exception e) {
                System.err.println("Error playing Wok Ka Tadka notification: " + e.getMessage());
                fallbackToSystemBeep("wok-ka-tadka");
            }
        });
    }
    
    /**
     * Start continuous Swiggy ringing (loops until stopped)
     */
    public void startSwiggyRinging(String orderId) {
        if (!audioEnabled) return;
        
        CompletableFuture.runAsync(() -> {
            try {
                String audioFile = findAudioFile(SWIGGY_AUDIO, DEFAULT_AUDIO);
                playAudioFile(audioFile, true, "swiggy-ring-" + orderId);
                System.out.println("🟠 Starting Swiggy continuous ringing for order: " + orderId);
            } catch (Exception e) {
                System.err.println("Error starting Swiggy ringing: " + e.getMessage());
                fallbackToSystemBeep("swiggy");
            }
        });
    }
    
    /**
     * Start continuous Zomato ringing (loops until stopped)
     */
    public void startZomatoRinging(String orderId) {
        if (!audioEnabled) return;

        CompletableFuture.runAsync(() -> {
            try {
                String audioFile = findAudioFile(ZOMATO_AUDIO, DEFAULT_AUDIO);
                playAudioFile(audioFile, true, "zomato-ring-" + orderId);
                System.out.println("🔴 Starting Zomato continuous ringing for order: " + orderId);
            } catch (Exception e) {
                System.err.println("Error starting Zomato ringing: " + e.getMessage());
                fallbackToSystemBeep("zomato");
            }
        });
    }

    /**
     * Start continuous Wok Ka Tadka ringing (loops until stopped)
     */
    public void startWokKaTadkaRinging(String orderId) {
        if (!audioEnabled) return;

        CompletableFuture.runAsync(() -> {
            try {
                String audioFile = findAudioFile(WOK_KA_TADKA_AUDIO, DEFAULT_AUDIO);
                playAudioFile(audioFile, true, "wok-ka-tadka-ring-" + orderId);
                System.out.println("🟢 Starting Wok Ka Tadka continuous ringing for order: " + orderId);
            } catch (Exception e) {
                System.err.println("Error starting Wok Ka Tadka ringing: " + e.getMessage());
                fallbackToSystemBeep("wok-ka-tadka");
            }
        });
    }
    
    /**
     * Stop continuous ringing for a specific order
     */
    public void stopRinging(String orderId) {
        String swiggyKey = "swiggy-ring-" + orderId;
        String zomatoKey = "zomato-ring-" + orderId;
        
        stopAudio(swiggyKey);
        stopAudio(zomatoKey);
        
        System.out.println("🔇 Stopped ringing for order: " + orderId);
    }
    
    /**
     * Stop all active audio
     */
    public void stopAllAudio() {
        System.out.println("🔇 Stopping all active audio (" + activePlayers.size() + " players)");
        activePlayers.values().forEach(player -> {
            try {
                player.stop();
                player.dispose();
            } catch (Exception e) {
                System.err.println("Error stopping audio player: " + e.getMessage());
            }
        });
        activePlayers.clear();
    }
    
    /**
     * Play audio file with optional looping
     */
    private void playAudioFile(String fileName, boolean loop, String playerId) {
        try {
            // Stop any existing player with the same ID
            stopAudio(playerId);
            
            // Create file path
            File audioFile = new File(SOUNDS_DIR + fileName);
            if (!audioFile.exists()) {
                throw new Exception("Audio file not found: " + audioFile.getAbsolutePath());
            }
            
            // Create media and player
            URI audioUri = audioFile.toURI();
            Media media = new Media(audioUri.toString());
            MediaPlayer player = new MediaPlayer(media);
            
            // Configure player
            player.setVolume(1.0); // Maximum volume
            
            if (loop) {
                player.setCycleCount(MediaPlayer.INDEFINITE);
            }
            
            // Set up event handlers
            player.setOnReady(() -> {
                System.out.println("Audio ready: " + fileName + " (Duration: " + 
                    player.getMedia().getDuration().toSeconds() + "s)");
                player.play();
            });
            
            player.setOnError(() -> {
                System.err.println("Audio error: " + player.getError().getMessage());
                fallbackToSystemBeep(playerId.contains("swiggy") ? "swiggy" : "zomato");
            });
            
            player.setOnEndOfMedia(() -> {
                if (!loop) {
                    stopAudio(playerId);
                }
            });
            
            // Store player for management
            activePlayers.put(playerId, player);
            
        } catch (Exception e) {
            System.err.println("Error playing audio file " + fileName + ": " + e.getMessage());
            throw new RuntimeException(e);
        }
    }
    
    /**
     * Stop specific audio player
     */
    private void stopAudio(String playerId) {
        MediaPlayer player = activePlayers.remove(playerId);
        if (player != null) {
            try {
                player.stop();
                player.dispose();
            } catch (Exception e) {
                System.err.println("Error stopping audio player " + playerId + ": " + e.getMessage());
            }
        }
    }
    
    /**
     * Find audio file, fallback to default if not found
     */
    private String findAudioFile(String preferredFile, String fallbackFile) {
        File preferred = new File(SOUNDS_DIR + preferredFile);
        if (preferred.exists()) {
            return preferredFile;
        }
        
        File fallback = new File(SOUNDS_DIR + fallbackFile);
        if (fallback.exists()) {
            System.out.println("Using fallback audio: " + fallbackFile + " (preferred not found: " + preferredFile + ")");
            return fallbackFile;
        }
        
        throw new RuntimeException("No audio files found! Checked: " + preferredFile + ", " + fallbackFile);
    }
    
    /**
     * Fallback to system beep if MP3 playback fails
     */
    private void fallbackToSystemBeep(String platform) {
        try {
            if ("swiggy".equals(platform)) {
                // Swiggy fallback: 4 rapid + 2 long beeps
                for (int i = 0; i < 4; i++) {
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(80);
                }
                Thread.sleep(300);
                for (int i = 0; i < 2; i++) {
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(400);
                }
            } else if ("zomato".equals(platform)) {
                // Zomato fallback: 3 sets of double beeps
                for (int set = 0; set < 3; set++) {
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(100);
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(250);
                }
            } else if ("wok-ka-tadka".equals(platform)) {
                // Wok Ka Tadka fallback: 5 ascending beeps (distinctive pattern)
                for (int i = 0; i < 5; i++) {
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(120 + (i * 20)); // Ascending timing
                }
            } else {
                // Generic fallback
                java.awt.Toolkit.getDefaultToolkit().beep();
            }
        } catch (Exception e) {
            System.err.println("Error in fallback system beep: " + e.getMessage());
        }
    }
    
    /**
     * Get count of active audio players
     */
    public int getActivePlayerCount() {
        return activePlayers.size();
    }
    
    /**
     * Check if audio is enabled
     */
    public boolean isAudioEnabled() {
        return audioEnabled;
    }
    
    /**
     * Enable/disable audio
     */
    public void setAudioEnabled(boolean enabled) {
        this.audioEnabled = enabled;
        if (!enabled) {
            stopAllAudio();
        }
        System.out.println("MP3 Audio " + (enabled ? "enabled" : "disabled"));
    }
}
