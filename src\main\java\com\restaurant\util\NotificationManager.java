package com.restaurant.util;

import javafx.animation.FadeTransition;
import javafx.animation.PauseTransition;
import javafx.application.Platform;
import javafx.geometry.Pos;
import javafx.scene.control.Label;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.stage.Popup;
import javafx.stage.Stage;
import javafx.util.Duration;

import java.io.File;
import java.util.concurrent.CompletableFuture;

/**
 * Notification Manager for Finish List Panel
 * Handles audio alerts and visual notifications for order status changes
 */
public class NotificationManager {
    
    private static NotificationManager instance;
    private Stage primaryStage;
    private boolean audioEnabled = true;
    private boolean notificationsEnabled = true;
    
    // Notification types
    public enum NotificationType {
        NEW_ORDER("🔔 New Order", "#2196f3", "notification_new_order.wav"),
        SWIGGY_ORDER("🟠 Swiggy Order", "#ff6600", "notification_swiggy_order.wav"),
        ZOMATO_ORDER("🔴 Zomato Order", "#e23744", "notification_zomato_order.wav"),
        STATUS_CHANGE("✅ Status Updated", "#4caf50", "notification_status_change.wav"),
        ORDER_READY("🍽️ Order Ready", "#ff9800", "notification_order_ready.wav"),
        ORDER_COMPLETED("✔️ Order Completed", "#9e9e9e", "notification_completed.wav"),
        URGENT("🚨 Urgent", "#f44336", "notification_urgent.wav"),
        SUCCESS("✅ Success", "#4caf50", "notification_success.wav"),
        WARNING("⚠️ Warning", "#ff9800", "notification_warning.wav"),
        ERROR("❌ Error", "#f44336", "notification_error.wav");
        
        private final String displayText;
        private final String color;
        private final String audioFile;
        
        NotificationType(String displayText, String color, String audioFile) {
            this.displayText = displayText;
            this.color = color;
            this.audioFile = audioFile;
        }
        
        public String getDisplayText() { return displayText; }
        public String getColor() { return color; }
        public String getAudioFile() { return audioFile; }
    }
    
    private NotificationManager() {}
    
    public static NotificationManager getInstance() {
        if (instance == null) {
            instance = new NotificationManager();
        }
        return instance;
    }
    
    public void initialize(Stage primaryStage) {
        this.primaryStage = primaryStage;
        System.out.println("NotificationManager initialized with primary stage");
    }
    
    /**
     * Show notification with audio alert
     */
    public void showNotification(NotificationType type, String title, String message) {
        if (!notificationsEnabled) return;
        
        Platform.runLater(() -> {
            try {
                // Play audio alert
                if (audioEnabled) {
                    playNotificationSound(type);
                }
                
                // Show visual notification
                showVisualNotification(type, title, message);
                
                // Log notification
                System.out.println("NOTIFICATION: " + type.getDisplayText() + " - " + title + ": " + message);
                
            } catch (Exception e) {
                System.err.println("Error showing notification: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    /**
     * Quick notification methods for common scenarios
     */
    public void notifyNewOrder(String orderId, String platform) {
        if ("SWIGGY".equalsIgnoreCase(platform)) {
            notifySwiggyOrder(orderId);
        } else if ("ZOMATO".equalsIgnoreCase(platform)) {
            notifyZomatoOrder(orderId);
        } else {
            showNotification(NotificationType.NEW_ORDER,
                            "New " + platform + " Order",
                            "Order " + orderId + " received and ready for preparation");
        }
    }

    /**
     * Swiggy-specific notification with distinct sound
     */
    public void notifySwiggyOrder(String orderId) {
        showNotification(NotificationType.SWIGGY_ORDER,
                        "🟠 NEW SWIGGY ORDER",
                        "Swiggy Order " + orderId + " received! IMMEDIATE ATTENTION REQUIRED!");
    }

    /**
     * Zomato-specific notification with distinct sound
     */
    public void notifyZomatoOrder(String orderId) {
        showNotification(NotificationType.ZOMATO_ORDER,
                        "🔴 NEW ZOMATO ORDER",
                        "Zomato Order " + orderId + " received! IMMEDIATE ATTENTION REQUIRED!");
    }
    
    public void notifyStatusChange(String orderId, String oldStatus, String newStatus) {
        showNotification(NotificationType.STATUS_CHANGE,
                        "Status Updated",
                        "Order " + orderId + " changed from " + oldStatus + " to " + newStatus);
    }
    
    public void notifyOrderReady(String orderId, String customerName) {
        showNotification(NotificationType.ORDER_READY,
                        "Order Ready for Delivery",
                        "Order " + orderId + " for " + customerName + " is ready for pickup");
    }
    
    public void notifyOrderCompleted(String orderId) {
        showNotification(NotificationType.ORDER_COMPLETED,
                        "Order Completed",
                        "Order " + orderId + " has been handed over to delivery partner");
    }
    
    public void notifyUrgent(String title, String message) {
        showNotification(NotificationType.URGENT, title, message);
    }
    
    public void notifySuccess(String title, String message) {
        showNotification(NotificationType.SUCCESS, title, message);
    }
    
    public void notifyWarning(String title, String message) {
        showNotification(NotificationType.WARNING, title, message);
    }
    
    public void notifyError(String title, String message) {
        showNotification(NotificationType.ERROR, title, message);
    }
    
    /**
     * Play notification sound using MP3 files or system beep fallback
     */
    private void playNotificationSound(NotificationType type) {
        CompletableFuture.runAsync(() -> {
            try {
                MP3AudioPlayer audioPlayer = MP3AudioPlayer.getInstance();

                // Use MP3 audio files for platform-specific notifications
                switch (type) {
                    case SWIGGY_ORDER:
                        audioPlayer.playSwiggyNotification();
                        break;
                    case ZOMATO_ORDER:
                        audioPlayer.playZomatoNotification();
                        break;
                    default:
                        // Use system beep for other notification types
                        playSystemBeep(type);
                        break;
                }

            } catch (Exception e) {
                System.err.println("Error playing notification sound: " + e.getMessage());
                // Fallback to system beep
                playSystemBeep(type);
            }
        });
    }
    
    /**
     * Play system beep with different patterns for different notification types
     */
    private void playSystemBeep(NotificationType type) {
        try {
            switch (type) {
                case SWIGGY_ORDER:
                    // SWIGGY: Loud distinctive pattern - 4 rapid beeps, pause, 2 long beeps
                    System.out.println("🟠 PLAYING SWIGGY ORDER SOUND - LOUD!");
                    for (int i = 0; i < 4; i++) {
                        java.awt.Toolkit.getDefaultToolkit().beep();
                        Thread.sleep(80);  // Very rapid
                    }
                    Thread.sleep(300);  // Pause
                    for (int i = 0; i < 2; i++) {
                        java.awt.Toolkit.getDefaultToolkit().beep();
                        Thread.sleep(400);  // Longer beeps
                    }
                    break;

                case ZOMATO_ORDER:
                    // ZOMATO: Loud distinctive pattern - 3 sets of double beeps
                    System.out.println("🔴 PLAYING ZOMATO ORDER SOUND - LOUD!");
                    for (int set = 0; set < 3; set++) {
                        java.awt.Toolkit.getDefaultToolkit().beep();
                        Thread.sleep(100);
                        java.awt.Toolkit.getDefaultToolkit().beep();
                        Thread.sleep(250);  // Pause between sets
                    }
                    break;

                case NEW_ORDER:
                    // Double beep for generic new orders
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(200);
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    break;

                case ORDER_READY:
                    // Triple beep for ready orders
                    for (int i = 0; i < 3; i++) {
                        java.awt.Toolkit.getDefaultToolkit().beep();
                        Thread.sleep(150);
                    }
                    break;

                case URGENT:
                    // Rapid beeps for urgent notifications
                    for (int i = 0; i < 5; i++) {
                        java.awt.Toolkit.getDefaultToolkit().beep();
                        Thread.sleep(100);
                    }
                    break;

                case ERROR:
                    // Long beep for errors
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(500);
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    break;

                default:
                    // Single beep for other notifications
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    break;
            }
        } catch (Exception e) {
            System.err.println("Error playing system beep: " + e.getMessage());
        }
    }
    
    /**
     * Play custom audio file (for when you add your own audio files)
     */
    private void playAudioFile(File audioFile) {
        try {
            // This is where you can add custom audio file playback
            // For now, we'll use system beep as placeholder
            System.out.println("Would play audio file: " + audioFile.getName());
            java.awt.Toolkit.getDefaultToolkit().beep();
        } catch (Exception e) {
            System.err.println("Error playing audio file: " + e.getMessage());
        }
    }
    
    /**
     * Show visual notification popup
     */
    private void showVisualNotification(NotificationType type, String title, String message) {
        if (primaryStage == null) return;
        
        try {
            // Create notification popup
            Popup popup = new Popup();
            popup.setAutoHide(true);
            popup.setHideOnEscape(true);
            
            // Create notification content
            VBox notificationBox = new VBox(5);
            notificationBox.setAlignment(Pos.CENTER);
            notificationBox.setStyle(String.format(
                "-fx-background-color: %s; " +
                "-fx-background-radius: 8px; " +
                "-fx-padding: 15px; " +
                "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 10, 0, 0, 3); " +
                "-fx-border-color: white; " +
                "-fx-border-width: 2px; " +
                "-fx-border-radius: 8px;",
                type.getColor()
            ));
            
            // Title label
            Label titleLabel = new Label(type.getDisplayText() + " " + title);
            titleLabel.setStyle("-fx-text-fill: white; -fx-font-weight: bold; -fx-font-size: 14px;");
            
            // Message label
            Label messageLabel = new Label(message);
            messageLabel.setStyle("-fx-text-fill: white; -fx-font-size: 12px; -fx-wrap-text: true;");
            messageLabel.setMaxWidth(300);
            
            notificationBox.getChildren().addAll(titleLabel, messageLabel);
            
            // Wrap in StackPane for better positioning
            StackPane wrapper = new StackPane(notificationBox);
            popup.getContent().add(wrapper);
            
            // Position popup at top-right of screen
            double x = primaryStage.getX() + primaryStage.getWidth() - 350;
            double y = primaryStage.getY() + 50;
            
            // Show popup
            popup.show(primaryStage, x, y);
            
            // Auto-hide after 4 seconds with fade animation
            PauseTransition delay = new PauseTransition(Duration.seconds(4));
            delay.setOnFinished(e -> {
                FadeTransition fade = new FadeTransition(Duration.seconds(0.5), wrapper);
                fade.setFromValue(1.0);
                fade.setToValue(0.0);
                fade.setOnFinished(f -> popup.hide());
                fade.play();
            });
            delay.play();
            
        } catch (Exception e) {
            System.err.println("Error showing visual notification: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    // Settings methods
    public void setAudioEnabled(boolean enabled) {
        this.audioEnabled = enabled;
        System.out.println("Audio notifications " + (enabled ? "enabled" : "disabled"));
    }
    
    public void setNotificationsEnabled(boolean enabled) {
        this.notificationsEnabled = enabled;
        System.out.println("Visual notifications " + (enabled ? "enabled" : "disabled"));
    }
    
    public boolean isAudioEnabled() { return audioEnabled; }
    public boolean isNotificationsEnabled() { return notificationsEnabled; }
}
