package com.restaurant.controller;

import com.restaurant.model.TableStatus;
import com.restaurant.util.NavigationUtil;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.input.MouseEvent;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;

import java.net.URL;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class TableViewController implements Initializable {

    @FXML private GridPane tablesGrid;
    @FXML private Button refreshBtn;
    @FXML private ToggleButton deliveryToggle;
    @FXML private ToggleButton pickUpToggle;
    @FXML private Button addTableBtn;
    @FXML private Button tableReservationBtn;
    @FXML private Button contestationsBtn;
    @FXML private ComboBox<String> fromTableCombo;
    @FXML private ComboBox<String> toTableCombo;
    @FXML private Button moveKOTBtn;
    @FXML private ComboBox<String> floorCombo;

    private Map<Integer, TableStatus> tableStatuses = new ConcurrentHashMap<>();
    private Map<Integer, VBox> tableNodes = new ConcurrentHashMap<>();
    private int maxTables = 20; // Default number of tables
    private String currentFloor = "Ground Floor";
    private boolean isDeliveryMode = false;
    private boolean isPickUpMode = false;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("TableViewController: Initializing...");
        
        setupFloorSelection();
        setupMoveKOTControls();
        setupToggleButtons();
        initializeTables();
        loadTableStatuses();
        refreshTables();
        
        System.out.println("TableViewController: Initialization complete");
    }

    private void setupFloorSelection() {
        ObservableList<String> floors = FXCollections.observableArrayList(
            "Ground Floor", "First Floor", "Second Floor", "Terrace"
        );
        floorCombo.setItems(floors);
        floorCombo.setValue(currentFloor);
        floorCombo.setOnAction(e -> switchFloor());
    }

    private void setupMoveKOTControls() {
        // Populate table combos
        ObservableList<String> tableNumbers = FXCollections.observableArrayList();
        for (int i = 1; i <= maxTables; i++) {
            tableNumbers.add("Table " + i);
        }
        fromTableCombo.setItems(tableNumbers);
        toTableCombo.setItems(tableNumbers);
    }

    private void setupToggleButtons() {
        // Create toggle group for delivery/pickup
        ToggleGroup modeGroup = new ToggleGroup();
        deliveryToggle.setToggleGroup(modeGroup);
        pickUpToggle.setToggleGroup(modeGroup);
        
        // Set default state
        deliveryToggle.setSelected(false);
        pickUpToggle.setSelected(false);
    }

    private void initializeTables() {
        System.out.println("TableViewController: Initializing " + maxTables + " tables");
        
        // Clear existing tables
        tablesGrid.getChildren().clear();
        tableNodes.clear();
        
        // Create table grid (5 columns)
        int columns = 5;
        int row = 0;
        int col = 0;
        
        for (int i = 1; i <= maxTables; i++) {
            VBox tableNode = createTableNode(i);
            tableNodes.put(i, tableNode);
            
            tablesGrid.add(tableNode, col, row);
            
            col++;
            if (col >= columns) {
                col = 0;
                row++;
            }
        }
        
        System.out.println("TableViewController: Created " + tableNodes.size() + " table nodes");
    }

    private VBox createTableNode(int tableNumber) {
        VBox tableBox = new VBox();
        tableBox.setAlignment(Pos.CENTER);
        tableBox.setSpacing(5.0);
        tableBox.setPrefWidth(120.0);
        tableBox.setPrefHeight(100.0);
        tableBox.setPadding(new Insets(10));
        tableBox.getStyleClass().addAll("table-node", "blank-table");
        
        // Table number label
        Label numberLabel = new Label(String.valueOf(tableNumber));
        numberLabel.getStyleClass().add("table-number");
        
        // Status/timing label
        Label statusLabel = new Label("");
        statusLabel.getStyleClass().add("table-status");
        statusLabel.setWrapText(true);
        statusLabel.setMaxWidth(100);
        
        // Amount label
        Label amountLabel = new Label("");
        amountLabel.getStyleClass().add("table-amount");
        
        tableBox.getChildren().addAll(numberLabel, statusLabel, amountLabel);
        
        // Add click handler
        tableBox.setOnMouseClicked(e -> handleTableClick(tableNumber, e));
        
        return tableBox;
    }

    private void loadTableStatuses() {
        System.out.println("TableViewController: Loading table statuses...");
        
        // Initialize all tables as blank
        for (int i = 1; i <= maxTables; i++) {
            tableStatuses.put(i, new TableStatus(i));
        }
        
        // Load some sample data
        loadSampleTableData();
        
        System.out.println("TableViewController: Loaded " + tableStatuses.size() + " table statuses");
    }

    private void loadSampleTableData() {
        // Table 3 - Running with timing
        TableStatus table3 = tableStatuses.get(3);
        table3.setStatus("RUNNING");
        table3.setTotalAmount(399.00);
        table3.setOrderTime(LocalDateTime.now().minusMinutes(10));
        table3.setHasActiveOrder(true);
        table3.setOrderId(12345);
        
        // Table 6 - Running KOT
        TableStatus table6 = tableStatuses.get(6);
        table6.setStatus("RUNNING_KOT");
        table6.setTotalAmount(150.00);
        table6.setOrderTime(LocalDateTime.now().minusMinutes(2));
        table6.setHasActiveOrder(true);
        table6.setOrderId(12346);
    }

    @FXML
    private void refreshTables() {
        System.out.println("TableViewController: Refreshing tables...");
        
        Platform.runLater(() -> {
            for (Map.Entry<Integer, TableStatus> entry : tableStatuses.entrySet()) {
                int tableNumber = entry.getKey();
                TableStatus status = entry.getValue();
                VBox tableNode = tableNodes.get(tableNumber);
                
                if (tableNode != null) {
                    updateTableDisplay(tableNode, status);
                }
            }
        });
        
        System.out.println("TableViewController: Tables refreshed");
    }

    private void updateTableDisplay(VBox tableNode, TableStatus status) {
        // Clear existing style classes
        tableNode.getStyleClass().removeIf(style -> 
            style.contains("table") && !style.equals("table-node"));
        
        // Add status-specific style class
        tableNode.getStyleClass().add(status.getStyleClass());
        
        // Update labels
        Label statusLabel = (Label) tableNode.getChildren().get(1);
        Label amountLabel = (Label) tableNode.getChildren().get(2);
        
        // Update status text
        if (status.shouldShowTiming()) {
            String timing = status.getFormattedTiming();
            statusLabel.setText(timing);
        } else {
            statusLabel.setText("");
        }
        
        // Update amount
        amountLabel.setText(status.getFormattedAmount());
    }

    private void handleTableClick(int tableNumber, MouseEvent event) {
        System.out.println("TableViewController: Table " + tableNumber + " clicked");
        
        TableStatus status = tableStatuses.get(tableNumber);
        if (status == null) {
            return;
        }
        
        if (event.getClickCount() == 2) {
            // Double click - open order entry
            openOrderEntry(tableNumber);
        } else {
            // Single click - show context menu or table details
            showTableContextMenu(tableNumber, event);
        }
    }

    private void openOrderEntry(int tableNumber) {
        System.out.println("TableViewController: Opening order entry for table " + tableNumber);
        
        try {
            // Navigate to order entry with table pre-selected
            Map<String, Object> params = new HashMap<>();
            params.put("tableNumber", tableNumber);
            params.put("returnTo", "TableView");
            
            NavigationUtil.navigateToWithParams("/fxml/OrderEntry.fxml", params);
        } catch (Exception e) {
            System.err.println("Error opening order entry: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Failed to open order entry: " + e.getMessage());
        }
    }

    private void showTableContextMenu(int tableNumber, MouseEvent event) {
        TableStatus status = tableStatuses.get(tableNumber);

        ContextMenu contextMenu = new ContextMenu();

        // View Details
        MenuItem viewDetails = new MenuItem("View Details");
        viewDetails.setOnAction(e -> showTableDetails(tableNumber));
        contextMenu.getItems().add(viewDetails);

        if (status.hasActiveOrder()) {
            // Add Items
            MenuItem addItems = new MenuItem("Add Items");
            addItems.setOnAction(e -> openOrderEntry(tableNumber));
            contextMenu.getItems().add(addItems);

            // Print KOT
            MenuItem printKOT = new MenuItem("Print KOT");
            printKOT.setOnAction(e -> printKOT(tableNumber));
            contextMenu.getItems().add(printKOT);

            // Generate Bill
            MenuItem generateBill = new MenuItem("Generate Bill");
            generateBill.setOnAction(e -> generateBill(tableNumber));
            contextMenu.getItems().add(generateBill);

            contextMenu.getItems().add(new SeparatorMenuItem());

            // Mark as Paid
            MenuItem markPaid = new MenuItem("Mark as Paid");
            markPaid.setOnAction(e -> markTablePaid(tableNumber));
            contextMenu.getItems().add(markPaid);
        } else {
            // New Order
            MenuItem newOrder = new MenuItem("New Order");
            newOrder.setOnAction(e -> openOrderEntry(tableNumber));
            contextMenu.getItems().add(newOrder);

            // Reserve Table
            MenuItem reserve = new MenuItem("Reserve Table");
            reserve.setOnAction(e -> reserveTable(tableNumber));
            contextMenu.getItems().add(reserve);
        }

        contextMenu.show(tableNodes.get(tableNumber), event.getScreenX(), event.getScreenY());
    }

    private void showTableDetails(int tableNumber) {
        System.out.println("TableViewController: Showing details for table " + tableNumber);

        TableStatus status = tableStatuses.get(tableNumber);
        StringBuilder details = new StringBuilder();

        details.append("Table ").append(tableNumber).append("\n\n");
        details.append("Status: ").append(status.getDisplayStatus()).append("\n");

        if (status.hasActiveOrder()) {
            details.append("Order ID: ").append(status.getOrderId()).append("\n");
            details.append("Order Time: ").append(formatDateTime(status.getOrderTime())).append("\n");
            details.append("Duration: ").append(status.getFormattedTiming()).append("\n");
            details.append("Amount: ").append(status.getFormattedAmount()).append("\n");
        }

        showAlert("Table Details", details.toString());
    }

    private void printKOT(int tableNumber) {
        System.out.println("TableViewController: Printing KOT for table " + tableNumber);

        TableStatus status = tableStatuses.get(tableNumber);
        status.setStatus("RUNNING_KOT");
        refreshTables();

        showAlert("KOT Printed", "Kitchen Order Ticket printed for Table " + tableNumber);
    }

    private void generateBill(int tableNumber) {
        System.out.println("TableViewController: Generating bill for table " + tableNumber);

        TableStatus status = tableStatuses.get(tableNumber);
        status.setStatus("PRINTED");
        refreshTables();

        showAlert("Bill Generated", "Bill generated for Table " + tableNumber + "\nTotal: " + status.getFormattedAmount());
    }

    private void markTablePaid(int tableNumber) {
        System.out.println("TableViewController: Marking table " + tableNumber + " as paid");

        TableStatus status = tableStatuses.get(tableNumber);
        status.setStatus("PAID");
        refreshTables();

        // After a short delay, reset the table to blank
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                Platform.runLater(() -> {
                    status.setStatus("BLANK");
                    status.setHasActiveOrder(false);
                    status.setTotalAmount(0.0);
                    refreshTables();
                });
            }
        }, 3000); // 3 seconds delay

        showAlert("Table Paid", "Table " + tableNumber + " marked as paid\nTotal: " + status.getFormattedAmount());
    }

    private void reserveTable(int tableNumber) {
        System.out.println("TableViewController: Reserving table " + tableNumber);

        // Show reservation dialog
        Dialog<ButtonType> dialog = new Dialog<>();
        dialog.setTitle("Reserve Table");
        dialog.setHeaderText("Reserve Table " + tableNumber);

        // Create form content
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        TextField nameField = new TextField();
        nameField.setPromptText("Customer Name");
        TextField phoneField = new TextField();
        phoneField.setPromptText("Phone Number");
        DatePicker datePicker = new DatePicker();
        ComboBox<String> timeCombo = new ComboBox<>();
        timeCombo.getItems().addAll("12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM",
                                   "7:00 PM", "7:30 PM", "8:00 PM", "8:30 PM", "9:00 PM");
        timeCombo.setValue("8:00 PM");
        Spinner<Integer> guestsSpinner = new Spinner<>(1, 20, 4);

        grid.add(new Label("Name:"), 0, 0);
        grid.add(nameField, 1, 0);
        grid.add(new Label("Phone:"), 0, 1);
        grid.add(phoneField, 1, 1);
        grid.add(new Label("Date:"), 0, 2);
        grid.add(datePicker, 1, 2);
        grid.add(new Label("Time:"), 0, 3);
        grid.add(timeCombo, 1, 3);
        grid.add(new Label("Guests:"), 0, 4);
        grid.add(guestsSpinner, 1, 4);

        dialog.getDialogPane().setContent(grid);

        // Add buttons
        dialog.getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);

        // Show dialog and process result
        Optional<ButtonType> result = dialog.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            // Process reservation
            showAlert("Table Reserved", "Table " + tableNumber + " has been reserved.\n\n" +
                     "Name: " + nameField.getText() + "\n" +
                     "Phone: " + phoneField.getText() + "\n" +
                     "Date: " + (datePicker.getValue() != null ? datePicker.getValue().toString() : "Today") + "\n" +
                     "Time: " + timeCombo.getValue() + "\n" +
                     "Guests: " + guestsSpinner.getValue());
        }
    }

    private String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "N/A";
        }
        return dateTime.format(DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm"));
    }

    private void showAlert(String title, String content) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(content);
        alert.showAndWait();
    }

    @FXML
    private void goBack() {
        System.out.println("TableViewController: Navigating back to dashboard");
        try {
            com.restaurant.util.NavigationUtil.navigateTo("/fxml/Dashboard.fxml");
        } catch (Exception e) {
            System.err.println("Error navigating back: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @FXML
    private void toggleDeliveryMode() {
        try {
            isDeliveryMode = deliveryToggle.isSelected();
            if (isDeliveryMode) {
                pickUpToggle.setSelected(false);
                isPickUpMode = false;

                // Show delivery orders interface
                showDeliveryOrders();

                // Update UI to show delivery mode
                updateModeDisplay("DELIVERY");
            } else {
                // Return to normal table view
                updateModeDisplay("DINE_IN");
                refreshTables();
            }
            System.out.println("TableViewController: Delivery mode " + (isDeliveryMode ? "enabled" : "disabled"));
        } catch (Exception e) {
            System.err.println("Error toggling delivery mode: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @FXML
    private void togglePickUpMode() {
        try {
            isPickUpMode = pickUpToggle.isSelected();
            if (isPickUpMode) {
                deliveryToggle.setSelected(false);
                isDeliveryMode = false;

                // Show pickup orders interface
                showPickUpOrders();

                // Update UI to show pickup mode
                updateModeDisplay("PICKUP");
            } else {
                // Return to normal table view
                updateModeDisplay("DINE_IN");
                refreshTables();
            }
            System.out.println("TableViewController: Pick Up mode " + (isPickUpMode ? "enabled" : "disabled"));
        } catch (Exception e) {
            System.err.println("Error toggling pickup mode: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @FXML
    private void addNewTable() {
        try {
            // Create dialog for adding new table
            TextInputDialog dialog = new TextInputDialog();
            dialog.setTitle("Add New Table");
            dialog.setHeaderText("Add a new table to the restaurant");
            dialog.setContentText("Enter table number:");

            // Set dialog styling
            dialog.getDialogPane().getStylesheets().add(
                getClass().getResource("/css/application.css").toExternalForm()
            );

            Optional<String> result = dialog.showAndWait();

            if (result.isPresent() && !result.get().trim().isEmpty()) {
                String input = result.get().trim();

                try {
                    int newTableNumber = Integer.parseInt(input);

                    // Validate table number
                    if (newTableNumber <= 0) {
                        showAlert("Invalid Input", "Table number must be a positive number!");
                        return;
                    }

                    if (tableStatuses.containsKey(newTableNumber)) {
                        showAlert("Table Exists", "Table " + newTableNumber + " already exists!");
                        return;
                    }

                    // Add new table
                    addTable(newTableNumber);

                    showAlert("Success", "Table " + newTableNumber + " has been added successfully!");

                } catch (NumberFormatException e) {
                    showAlert("Invalid Input", "Please enter a valid table number!");
                }
            }

        } catch (Exception e) {
            System.err.println("Error adding new table: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Failed to add new table: " + e.getMessage());
        }
    }

    /**
     * Show delivery orders interface
     */
    private void showDeliveryOrders() {
        try {
            System.out.println("TableViewController: Showing delivery orders");

            // Clear current table grid
            tablesGrid.getChildren().clear();

            // Create delivery orders display
            VBox deliveryContainer = new VBox(15);
            deliveryContainer.setStyle("-fx-padding: 20; -fx-alignment: center;");

            // Title
            Label titleLabel = new Label("🚚 DELIVERY ORDERS");
            titleLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

            // Sample delivery orders
            VBox ordersContainer = new VBox(10);
            ordersContainer.setStyle("-fx-alignment: center;");

            // Create sample delivery order cards
            for (int i = 1; i <= 5; i++) {
                VBox orderCard = createDeliveryOrderCard(i);
                ordersContainer.getChildren().add(orderCard);
            }

            ScrollPane scrollPane = new ScrollPane(ordersContainer);
            scrollPane.setFitToWidth(true);
            scrollPane.setStyle("-fx-background-color: transparent;");

            deliveryContainer.getChildren().addAll(titleLabel, scrollPane);

            // Add to grid (span entire grid)
            tablesGrid.add(deliveryContainer, 0, 0, 5, 4);

        } catch (Exception e) {
            System.err.println("Error showing delivery orders: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Show pickup orders interface
     */
    private void showPickUpOrders() {
        try {
            System.out.println("TableViewController: Showing pickup orders");

            // Clear current table grid
            tablesGrid.getChildren().clear();

            // Create pickup orders display
            VBox pickupContainer = new VBox(15);
            pickupContainer.setStyle("-fx-padding: 20; -fx-alignment: center;");

            // Title
            Label titleLabel = new Label("🥡 PICKUP ORDERS");
            titleLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

            // Sample pickup orders
            VBox ordersContainer = new VBox(10);
            ordersContainer.setStyle("-fx-alignment: center;");

            // Create sample pickup order cards
            for (int i = 1; i <= 3; i++) {
                VBox orderCard = createPickupOrderCard(i);
                ordersContainer.getChildren().add(orderCard);
            }

            ScrollPane scrollPane = new ScrollPane(ordersContainer);
            scrollPane.setFitToWidth(true);
            scrollPane.setStyle("-fx-background-color: transparent;");

            pickupContainer.getChildren().addAll(titleLabel, scrollPane);

            // Add to grid (span entire grid)
            tablesGrid.add(pickupContainer, 0, 0, 5, 4);

        } catch (Exception e) {
            System.err.println("Error showing pickup orders: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Update mode display based on current mode
     */
    private void updateModeDisplay(String mode) {
        try {
            System.out.println("TableViewController: Updating mode display to " + mode);

            // Update toggle button styles based on mode
            switch (mode) {
                case "DELIVERY":
                    deliveryToggle.getStyleClass().removeAll("delivery-toggle");
                    deliveryToggle.getStyleClass().add("delivery-toggle-active");
                    pickUpToggle.getStyleClass().removeAll("pickup-toggle-active");
                    pickUpToggle.getStyleClass().add("pickup-toggle");
                    break;

                case "PICKUP":
                    pickUpToggle.getStyleClass().removeAll("pickup-toggle");
                    pickUpToggle.getStyleClass().add("pickup-toggle-active");
                    deliveryToggle.getStyleClass().removeAll("delivery-toggle-active");
                    deliveryToggle.getStyleClass().add("delivery-toggle");
                    break;

                case "DINE_IN":
                default:
                    deliveryToggle.getStyleClass().removeAll("delivery-toggle-active");
                    deliveryToggle.getStyleClass().add("delivery-toggle");
                    pickUpToggle.getStyleClass().removeAll("pickup-toggle-active");
                    pickUpToggle.getStyleClass().add("pickup-toggle");
                    break;
            }

        } catch (Exception e) {
            System.err.println("Error updating mode display: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Create delivery order card
     */
    private VBox createDeliveryOrderCard(int orderNumber) {
        VBox card = new VBox(8);
        card.setStyle("-fx-background-color: white; -fx-border-color: #3498db; -fx-border-width: 2; " +
                     "-fx-border-radius: 8; -fx-background-radius: 8; -fx-padding: 15; -fx-max-width: 400;");

        // Order header
        HBox header = new HBox();
        header.setAlignment(Pos.CENTER_LEFT);

        Label orderLabel = new Label("🚚 Delivery Order #D" + String.format("%03d", orderNumber));
        orderLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        Label statusLabel = new Label("PREPARING");
        statusLabel.setStyle("-fx-background-color: #f39c12; -fx-text-fill: white; -fx-padding: 4 8; " +
                           "-fx-background-radius: 12; -fx-font-size: 10px; -fx-font-weight: bold;");

        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);

        header.getChildren().addAll(orderLabel, spacer, statusLabel);

        // Customer info
        Label customerLabel = new Label("Customer: John Doe");
        customerLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #34495e;");

        Label addressLabel = new Label("📍 123 Main St, City");
        addressLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #7f8c8d;");

        Label phoneLabel = new Label("📞 ******-567-8900");
        phoneLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #7f8c8d;");

        // Order details
        Label itemsLabel = new Label("Items: Burger, Fries, Coke");
        itemsLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #34495e;");

        Label amountLabel = new Label("Amount: ₹" + (250 + orderNumber * 50));
        amountLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #27ae60;");

        // Time info
        Label timeLabel = new Label("⏰ Ordered: " + java.time.LocalTime.now().minusMinutes(orderNumber * 5).format(
            java.time.format.DateTimeFormatter.ofPattern("HH:mm")));
        timeLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #7f8c8d;");

        // Action buttons
        HBox buttonBox = new HBox(8);
        buttonBox.setAlignment(Pos.CENTER);

        Button readyBtn = new Button("Mark Ready");
        readyBtn.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-padding: 6 12; " +
                         "-fx-background-radius: 4; -fx-font-size: 10px;");

        Button dispatchBtn = new Button("Dispatch");
        dispatchBtn.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-padding: 6 12; " +
                           "-fx-background-radius: 4; -fx-font-size: 10px;");

        buttonBox.getChildren().addAll(readyBtn, dispatchBtn);

        card.getChildren().addAll(header, customerLabel, addressLabel, phoneLabel,
                                 new Separator(), itemsLabel, amountLabel, timeLabel, buttonBox);

        return card;
    }

    /**
     * Create pickup order card
     */
    private VBox createPickupOrderCard(int orderNumber) {
        VBox card = new VBox(8);
        card.setStyle("-fx-background-color: white; -fx-border-color: #e67e22; -fx-border-width: 2; " +
                     "-fx-border-radius: 8; -fx-background-radius: 8; -fx-padding: 15; -fx-max-width: 400;");

        // Order header
        HBox header = new HBox();
        header.setAlignment(Pos.CENTER_LEFT);

        Label orderLabel = new Label("🥡 Pickup Order #P" + String.format("%03d", orderNumber));
        orderLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        Label statusLabel = new Label("READY");
        statusLabel.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-padding: 4 8; " +
                           "-fx-background-radius: 12; -fx-font-size: 10px; -fx-font-weight: bold;");

        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);

        header.getChildren().addAll(orderLabel, spacer, statusLabel);

        // Customer info
        Label customerLabel = new Label("Customer: Jane Smith");
        customerLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #34495e;");

        Label phoneLabel = new Label("📞 ******-567-8901");
        phoneLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #7f8c8d;");

        // Order details
        Label itemsLabel = new Label("Items: Pizza, Salad, Juice");
        itemsLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #34495e;");

        Label amountLabel = new Label("Amount: ₹" + (180 + orderNumber * 40));
        amountLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #27ae60;");

        // Time info
        Label timeLabel = new Label("⏰ Ready since: " + java.time.LocalTime.now().minusMinutes(orderNumber * 3).format(
            java.time.format.DateTimeFormatter.ofPattern("HH:mm")));
        timeLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #7f8c8d;");

        // Action buttons
        HBox buttonBox = new HBox(8);
        buttonBox.setAlignment(Pos.CENTER);

        Button notifyBtn = new Button("Notify Customer");
        notifyBtn.setStyle("-fx-background-color: #f39c12; -fx-text-fill: white; -fx-padding: 6 12; " +
                          "-fx-background-radius: 4; -fx-font-size: 10px;");

        Button completeBtn = new Button("Complete");
        completeBtn.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-padding: 6 12; " +
                           "-fx-background-radius: 4; -fx-font-size: 10px;");

        buttonBox.getChildren().addAll(notifyBtn, completeBtn);

        card.getChildren().addAll(header, customerLabel, phoneLabel,
                                 new Separator(), itemsLabel, amountLabel, timeLabel, buttonBox);

        return card;
    }

    /**
     * Add a new table to the system
     */
    private void addTable(int tableNumber) {
        try {
            // Create table status
            TableStatus newStatus = new TableStatus(tableNumber);
            tableStatuses.put(tableNumber, newStatus);

            // Update max tables if necessary
            maxTables = Math.max(maxTables, tableNumber);

            // Reinitialize tables to include the new one
            initializeTables();
            refreshTables();

            System.out.println("TableViewController: Added table " + tableNumber);

        } catch (Exception e) {
            System.err.println("Error adding table " + tableNumber + ": " + e.getMessage());
            e.printStackTrace();
        }
    }

    @FXML
    private void openTableReservation() {
        System.out.println("TableViewController: Opening table reservation");

        // Show table reservation dialog
        Dialog<ButtonType> dialog = new Dialog<>();
        dialog.setTitle("Table Reservations");
        dialog.setHeaderText("Manage Table Reservations");

        // Create content
        VBox content = new VBox(10);
        content.setPadding(new Insets(20));

        // Add reservation list (sample data)
        TableView<Map<String, String>> reservationsTable = new TableView<>();
        reservationsTable.setPrefHeight(300);

        TableColumn<Map<String, String>, String> nameCol = new TableColumn<>("Name");
        nameCol.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().get("name")));

        TableColumn<Map<String, String>, String> tableCol = new TableColumn<>("Table");
        tableCol.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().get("table")));

        TableColumn<Map<String, String>, String> dateCol = new TableColumn<>("Date");
        dateCol.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().get("date")));

        TableColumn<Map<String, String>, String> timeCol = new TableColumn<>("Time");
        timeCol.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().get("time")));

        TableColumn<Map<String, String>, String> guestsCol = new TableColumn<>("Guests");
        guestsCol.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().get("guests")));

        reservationsTable.getColumns().addAll(nameCol, tableCol, dateCol, timeCol, guestsCol);

        // Add sample data
        ObservableList<Map<String, String>> reservations = FXCollections.observableArrayList();

        Map<String, String> res1 = new HashMap<>();
        res1.put("name", "John Doe");
        res1.put("table", "3");
        res1.put("date", "15-07-2025");
        res1.put("time", "8:00 PM");
        res1.put("guests", "4");

        Map<String, String> res2 = new HashMap<>();
        res2.put("name", "Jane Smith");
        res2.put("table", "6");
        res2.put("date", "15-07-2025");
        res2.put("time", "7:30 PM");
        res2.put("guests", "2");

        reservations.addAll(res1, res2);
        reservationsTable.setItems(reservations);

        content.getChildren().add(reservationsTable);

        // Add buttons for new reservation, edit, delete
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button newReservationBtn = new Button("New Reservation");
        Button editReservationBtn = new Button("Edit");
        Button deleteReservationBtn = new Button("Delete");

        buttonBox.getChildren().addAll(newReservationBtn, editReservationBtn, deleteReservationBtn);
        content.getChildren().add(buttonBox);

        dialog.getDialogPane().setContent(content);
        dialog.getDialogPane().getButtonTypes().add(ButtonType.CLOSE);

        dialog.showAndWait();
    }

    @FXML
    private void openContestations() {
        System.out.println("TableViewController: Opening contestations");

        // Show contestations dialog
        Dialog<ButtonType> dialog = new Dialog<>();
        dialog.setTitle("Contestations");
        dialog.setHeaderText("Manage Contestations");

        // Create content
        VBox content = new VBox(10);
        content.setPadding(new Insets(20));

        // Add contestation list (sample data)
        TableView<Map<String, String>> contestationsTable = new TableView<>();
        contestationsTable.setPrefHeight(300);

        TableColumn<Map<String, String>, String> orderCol = new TableColumn<>("Order ID");
        orderCol.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().get("orderId")));

        TableColumn<Map<String, String>, String> tableCol = new TableColumn<>("Table");
        tableCol.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().get("table")));

        TableColumn<Map<String, String>, String> dateCol = new TableColumn<>("Date");
        dateCol.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().get("date")));

        TableColumn<Map<String, String>, String> amountCol = new TableColumn<>("Amount");
        amountCol.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().get("amount")));

        TableColumn<Map<String, String>, String> statusCol = new TableColumn<>("Status");
        statusCol.setCellValueFactory(data -> new javafx.beans.property.SimpleStringProperty(data.getValue().get("status")));

        contestationsTable.getColumns().addAll(orderCol, tableCol, dateCol, amountCol, statusCol);

        // Add sample data
        ObservableList<Map<String, String>> contestations = FXCollections.observableArrayList();

        Map<String, String> con1 = new HashMap<>();
        con1.put("orderId", "12345");
        con1.put("table", "3");
        con1.put("date", "15-07-2025");
        con1.put("amount", "₹399.00");
        con1.put("status", "Pending");

        Map<String, String> con2 = new HashMap<>();
        con2.put("orderId", "12340");
        con2.put("table", "5");
        con2.put("date", "14-07-2025");
        con2.put("amount", "₹250.50");
        con2.put("status", "Resolved");

        contestations.addAll(con1, con2);
        contestationsTable.setItems(contestations);

        content.getChildren().add(contestationsTable);

        // Add buttons for new contestation, resolve, delete
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER_RIGHT);

        Button newContestationBtn = new Button("New Contestation");
        Button resolveBtn = new Button("Resolve");
        Button deleteBtn = new Button("Delete");

        buttonBox.getChildren().addAll(newContestationBtn, resolveBtn, deleteBtn);
        content.getChildren().add(buttonBox);

        dialog.getDialogPane().setContent(content);
        dialog.getDialogPane().getButtonTypes().add(ButtonType.CLOSE);

        dialog.showAndWait();
    }

    @FXML
    private void moveKOT() {
        String fromTable = fromTableCombo.getValue();
        String toTable = toTableCombo.getValue();

        if (fromTable == null || toTable == null) {
            showAlert("Error", "Please select both source and destination tables!");
            return;
        }

        if (fromTable.equals(toTable)) {
            showAlert("Error", "Source and destination tables cannot be the same!");
            return;
        }

        // Extract table numbers
        int fromTableNum = Integer.parseInt(fromTable.replace("Table ", ""));
        int toTableNum = Integer.parseInt(toTable.replace("Table ", ""));

        TableStatus fromStatus = tableStatuses.get(fromTableNum);
        TableStatus toStatus = tableStatuses.get(toTableNum);

        if (!fromStatus.hasActiveOrder()) {
            showAlert("Error", "Source table has no active order to move!");
            return;
        }

        if (toStatus.hasActiveOrder()) {
            // Confirm merging orders
            Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("Confirm Merge");
            confirmAlert.setHeaderText("Destination table already has an active order");
            confirmAlert.setContentText("Do you want to merge the orders?");

            Optional<ButtonType> result = confirmAlert.showAndWait();
            if (result.isPresent() && result.get() == ButtonType.OK) {
                // Merge orders
                double newTotal = fromStatus.getTotalAmount() + toStatus.getTotalAmount();
                toStatus.setTotalAmount(newTotal);

                // Clear source table
                fromStatus.setStatus("BLANK");
                fromStatus.setHasActiveOrder(false);
                fromStatus.setTotalAmount(0.0);

                refreshTables();

                showAlert("Orders Merged", "Orders from Table " + fromTableNum +
                         " have been merged with Table " + toTableNum + "\n\nNew Total: ₹" +
                         String.format("%.2f", newTotal));
            }
        } else {
            // Move order to empty table
            toStatus.setStatus(fromStatus.getStatus());
            toStatus.setHasActiveOrder(true);
            toStatus.setTotalAmount(fromStatus.getTotalAmount());
            toStatus.setOrderTime(fromStatus.getOrderTime());
            toStatus.setOrderId(fromStatus.getOrderId());

            // Clear source table
            fromStatus.setStatus("BLANK");
            fromStatus.setHasActiveOrder(false);
            fromStatus.setTotalAmount(0.0);

            refreshTables();

            showAlert("Order Moved", "Order from Table " + fromTableNum +
                     " has been moved to Table " + toTableNum);
        }
    }

    private void switchFloor() {
        currentFloor = floorCombo.getValue();
        System.out.println("TableViewController: Switched to " + currentFloor);

        // In a real implementation, this would load tables for the selected floor
        // For now, just refresh the current tables
        refreshTables();
    }
}
