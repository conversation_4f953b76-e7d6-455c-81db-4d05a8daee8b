<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.CampaignCreatorController">
   <top>
      <VBox styleClass="header-section">
         <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="page-header">
            <Button fx:id="backButton" onAction="#goBack" styleClass="back-button" text="Back" />
            <Label styleClass="page-title" text="Campaign Creator" />
            <Label styleClass="page-subtitle" text="Create targeted marketing campaigns" />
         </HBox>
      </VBox>
   </top>
   
   <center>
      <ScrollPane fitToWidth="true" styleClass="content-scroll">
         <VBox spacing="25.0" styleClass="main-content">
            <padding>
               <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
            </padding>
            
            <HBox spacing="25.0" styleClass="campaign-layout">
               <!-- Left Panel - Campaign Type -->
               <VBox spacing="0.0" styleClass="campaign-type-panel">
                  <Label styleClass="panel-title" text="Campaign Type" />
                  
                  <VBox spacing="0.0" styleClass="campaign-type-list">
                     <Button fx:id="scheduleBtn" onAction="#selectSchedule" styleClass="campaign-type-item" text="Schedule" />
                     <Button fx:id="onBillPrintBtn" onAction="#selectOnBillPrint" styleClass="campaign-type-item" text="On Bill Print" />
                     <Button fx:id="birthdayBtn" onAction="#selectBirthday" styleClass="campaign-type-item selected" text="Birthday" />
                     <Button fx:id="anniversaryBtn" onAction="#selectAnniversary" styleClass="campaign-type-item" text="Anniversary" />
                  </VBox>
               </VBox>
               
               <!-- Right Panel - Campaign Preview -->
               <VBox spacing="15.0" styleClass="campaign-preview-panel" HBox.hgrow="ALWAYS">
                  <!-- Email Icon and Message -->
                  <VBox spacing="15.0" styleClass="message-preview">
                     <HBox alignment="CENTER_LEFT" spacing="10.0">
                        <Label styleClass="email-icon" text="Email" />
                     </HBox>
                     
                     <VBox spacing="10.0" styleClass="message-content">
                        <Label fx:id="messageTitle" styleClass="message-title" text="Wishing you a very Happy Birthday" />
                        
                        <VBox spacing="8.0">
                           <Label fx:id="messageBody" styleClass="message-body" text="Visit any of the CDI outlets and show this message to enjoy 30 percent off on your bill" />
                        </VBox>
                        
                        <Label fx:id="messageSender" styleClass="message-sender" text="Sent by CDICAFE" />
                     </VBox>
                  </VBox>
                  
                  <!-- Campaign Configuration -->
                  <VBox spacing="15.0" styleClass="campaign-config">
                     <Label styleClass="config-title" text="Campaign Settings" />
                     
                     <GridPane hgap="15.0" vgap="12.0" styleClass="config-grid">
                        <Label text="Campaign Name:" styleClass="config-label" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <TextField fx:id="campaignNameField" promptText="Enter campaign name" styleClass="config-field" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="Discount:" styleClass="config-label" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <TextField fx:id="discountField" promptText="30" styleClass="discount-field" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label text="Valid Days:" styleClass="config-label" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <TextField fx:id="validDaysField" promptText="7" styleClass="config-field" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        
                        <Label text="Message Type:" styleClass="config-label" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <ComboBox fx:id="messageTypeCombo" promptText="Select type" styleClass="config-combo" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                     </GridPane>
                  </VBox>
               </VBox>
            </HBox>
            
            <!-- Customer Selection Section -->
            <VBox spacing="15.0" styleClass="customer-selection-section">
               <Label styleClass="section-title" text="Select Customer/Recipients" />
               
               <!-- Search and Filter -->
               <HBox spacing="15.0" alignment="CENTER_LEFT" styleClass="selection-controls">
                  <TextField fx:id="customerSearchField" promptText="Search customers..." styleClass="customer-search-field" />
                  <ComboBox fx:id="segmentFilterCombo" promptText="Filter by segment" styleClass="segment-filter-combo" />
                  <Button fx:id="selectAllBtn" onAction="#selectAllCustomers" styleClass="select-all-button" text="Select All" />
                  <Button fx:id="clearAllBtn" onAction="#clearAllCustomers" styleClass="clear-all-button" text="Clear All" />
               </HBox>
               
               <!-- Customer List -->
               <VBox styleClass="customer-selection-table">
                  <!-- Table Header -->
                  <HBox styleClass="customer-selection-header">
                     <CheckBox fx:id="selectAllCheckbox" onAction="#toggleSelectAll" styleClass="header-checkbox" />
                     <Label styleClass="header-label phone-header" text="Phone" />
                     <Label styleClass="header-label name-header" text="Name" />
                     <Label styleClass="header-label segment-header" text="Segment" />
                     <Label styleClass="header-label birthday-header" text="Birthday" />
                  </HBox>
                  
                  <!-- Customer List Container -->
                  <ScrollPane styleClass="customer-list-scroll" maxHeight="300">
                     <VBox fx:id="customerListContainer" styleClass="customer-list-content">
                        <!-- Customer rows will be added dynamically -->
                     </VBox>
                  </ScrollPane>
               </VBox>
               
               <!-- Selected Count -->
               <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="selection-summary">
                  <Label fx:id="selectedCountLabel" styleClass="selected-count" text="0 customers selected" />
               </HBox>
            </VBox>
            
            <!-- Action Buttons -->
            <HBox spacing="15.0" alignment="CENTER_RIGHT" styleClass="action-buttons-section">
               <Button fx:id="previewBtn" onAction="#previewCampaign" styleClass="preview-button" text="Preview Campaign" />
               <Button fx:id="saveDraftBtn" onAction="#saveDraft" styleClass="save-draft-button" text="Save Draft" />
               <Button fx:id="sendCampaignBtn" onAction="#sendCampaign" styleClass="send-campaign-button" text="Send Campaign" />
            </HBox>
         </VBox>
      </ScrollPane>
   </center>
</BorderPane>
