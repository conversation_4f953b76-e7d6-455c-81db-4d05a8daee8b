package com.restaurant.util;

import javafx.application.Platform;
import javafx.concurrent.Task;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Utility class for performance optimization and preventing UI freezing
 * Provides methods for async operations, debouncing, and memory management
 */
public class PerformanceOptimizer {
    
    // Thread pools for different types of operations
    private static final ExecutorService DATABASE_EXECUTOR = Executors.newFixedThreadPool(3);
    private static final ExecutorService UI_EXECUTOR = Executors.newFixedThreadPool(2);
    private static final ScheduledExecutorService SCHEDULER = Executors.newScheduledThreadPool(2);
    
    // Debouncing mechanism
    private static final Map<String, ScheduledExecutorService> DEBOUNCE_TIMERS = new ConcurrentHashMap<>();
    
    /**
     * Execute a database operation asynchronously
     */
    public static <T> CompletableFuture<T> executeAsync(Supplier<T> operation) {
        return CompletableFuture.supplyAsync(operation, DATABASE_EXECUTOR);
    }
    
    /**
     * Execute a UI operation asynchronously
     */
    public static <T> CompletableFuture<T> executeUIAsync(Supplier<T> operation) {
        return CompletableFuture.supplyAsync(operation, UI_EXECUTOR);
    }
    
    /**
     * Execute an operation on the JavaFX Application Thread
     */
    public static void runOnUIThread(Runnable operation) {
        if (Platform.isFxApplicationThread()) {
            operation.run();
        } else {
            Platform.runLater(operation);
        }
    }
    
    /**
     * Debounce a function call - prevents rapid successive calls
     */
    public static void debounce(String key, Runnable operation, long delayMs) {
        // Cancel any existing timer for this key
        ScheduledExecutorService existingTimer = DEBOUNCE_TIMERS.get(key);
        if (existingTimer != null) {
            existingTimer.shutdownNow();
        }
        
        // Create new timer
        ScheduledExecutorService timer = Executors.newSingleThreadScheduledExecutor();
        DEBOUNCE_TIMERS.put(key, timer);
        
        timer.schedule(() -> {
            try {
                operation.run();
            } finally {
                DEBOUNCE_TIMERS.remove(key);
                timer.shutdown();
            }
        }, delayMs, TimeUnit.MILLISECONDS);
    }
    
    /**
     * Create a JavaFX Task for background operations
     */
    public static <T> Task<T> createTask(Supplier<T> operation) {
        return new Task<T>() {
            @Override
            protected T call() throws Exception {
                return operation.get();
            }
        };
    }
    
    /**
     * Create a JavaFX Task with progress reporting
     */
    public static <T> Task<T> createProgressTask(ProgressOperation<T> operation) {
        return new Task<T>() {
            @Override
            protected T call() throws Exception {
                return operation.execute(this::updateProgress);
            }
        };
    }
    
    /**
     * Execute operation with timeout
     */
    public static <T> CompletableFuture<T> executeWithTimeout(Supplier<T> operation, long timeoutMs) {
        CompletableFuture<T> future = CompletableFuture.supplyAsync(operation, DATABASE_EXECUTOR);
        
        // Set timeout
        SCHEDULER.schedule(() -> {
            if (!future.isDone()) {
                future.cancel(true);
            }
        }, timeoutMs, TimeUnit.MILLISECONDS);
        
        return future;
    }
    
    /**
     * Batch operations to reduce database calls
     */
    public static <T> CompletableFuture<T> batchOperation(Supplier<T> operation, String batchKey, long batchDelayMs) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Small delay to allow batching
                Thread.sleep(batchDelayMs);
                return operation.get();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException(e);
            }
        }, DATABASE_EXECUTOR);
    }
    
    /**
     * Memory optimization - force garbage collection (use sparingly)
     */
    public static void optimizeMemory() {
        System.gc();
        System.runFinalization();
    }
    
    /**
     * Get memory usage information
     */
    public static MemoryInfo getMemoryInfo() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        return new MemoryInfo(totalMemory, freeMemory, usedMemory, maxMemory);
    }
    
    /**
     * Check if memory usage is high
     */
    public static boolean isMemoryUsageHigh() {
        MemoryInfo info = getMemoryInfo();
        double usagePercentage = (double) info.usedMemory / info.maxMemory;
        return usagePercentage > 0.8; // 80% threshold
    }
    
    /**
     * Shutdown all executors (call on application exit)
     */
    public static void shutdown() {
        DATABASE_EXECUTOR.shutdown();
        UI_EXECUTOR.shutdown();
        SCHEDULER.shutdown();
        
        // Cancel all debounce timers
        DEBOUNCE_TIMERS.values().forEach(timer -> timer.shutdownNow());
        DEBOUNCE_TIMERS.clear();
        
        try {
            if (!DATABASE_EXECUTOR.awaitTermination(5, TimeUnit.SECONDS)) {
                DATABASE_EXECUTOR.shutdownNow();
            }
            if (!UI_EXECUTOR.awaitTermination(5, TimeUnit.SECONDS)) {
                UI_EXECUTOR.shutdownNow();
            }
            if (!SCHEDULER.awaitTermination(5, TimeUnit.SECONDS)) {
                SCHEDULER.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * Interface for operations that report progress
     */
    @FunctionalInterface
    public interface ProgressOperation<T> {
        T execute(ProgressReporter reporter) throws Exception;
    }
    
    /**
     * Interface for progress reporting
     */
    @FunctionalInterface
    public interface ProgressReporter {
        void updateProgress(double workDone, double max);
    }
    
    /**
     * Memory information holder
     */
    public static class MemoryInfo {
        public final long totalMemory;
        public final long freeMemory;
        public final long usedMemory;
        public final long maxMemory;
        
        public MemoryInfo(long totalMemory, long freeMemory, long usedMemory, long maxMemory) {
            this.totalMemory = totalMemory;
            this.freeMemory = freeMemory;
            this.usedMemory = usedMemory;
            this.maxMemory = maxMemory;
        }
        
        public double getUsagePercentage() {
            return (double) usedMemory / maxMemory * 100;
        }
        
        @Override
        public String toString() {
            return String.format("Memory: Used=%dMB, Free=%dMB, Total=%dMB, Max=%dMB (%.1f%% used)",
                    usedMemory / 1024 / 1024,
                    freeMemory / 1024 / 1024,
                    totalMemory / 1024 / 1024,
                    maxMemory / 1024 / 1024,
                    getUsagePercentage());
        }
    }
    
    /**
     * Performance monitoring
     */
    public static void logPerformanceMetrics(String operation, long startTime) {
        long duration = System.currentTimeMillis() - startTime;
        MemoryInfo memInfo = getMemoryInfo();
        
        System.out.printf("[PERF] %s: %dms, %s%n", operation, duration, memInfo);
        
        if (duration > 1000) {
            System.out.printf("[WARN] Slow operation detected: %s took %dms%n", operation, duration);
        }
        
        if (isMemoryUsageHigh()) {
            System.out.printf("[WARN] High memory usage: %.1f%%%n", memInfo.getUsagePercentage());
        }
    }
}
