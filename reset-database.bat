@echo off
echo RESETTING DATABASE TO FIX LOGIN ISSUE...
echo.

echo PROBLEM: Database connection issues preventing admin login
echo SOLUTION: Delete and recreate database with fresh admin user
echo.

echo Step 1: Deleting existing database file...
if exist restaurant.db (
    del restaurant.db
    echo ✅ Deleted restaurant.db
) else (
    echo ℹ️ No existing database found
)

echo.
echo Step 2: Starting application to recreate database...
echo.

echo The application will now start and automatically:
echo 1. Create a fresh restaurant.db file
echo 2. Initialize all tables
echo 3. Create admin user with admin/admin123
echo 4. Create staff user with staff/staff123
echo.

echo ADMIN LOGIN CREDENTIALS:
echo Username: admin
echo Password: admin123
echo Role: ADMIN (select this from dropdown)
echo.

echo BACKUP CREDENTIALS:
echo Username: staff  
echo Password: staff123
echo Role: STAFF (select this from dropdown)
echo.

echo Starting application now...
echo.

java -Dprism.order=sw ^
     -Dprism.allowhidpi=false ^
     -Djavafx.animation.pulse=60 ^
     -Djava.awt.headless=false ^
     -Dsqlite.purejava=false ^
     -Xms512m ^
     -Xmx2g ^
     -XX:+UseG1GC ^
     -XX:MaxGCPauseMillis=200 ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics ^
     -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\pdfbox\2.0.27\pdfbox-2.0.27.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\fontbox\2.0.27\fontbox-2.0.27.jar;%USERPROFILE%\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar" ^
     com.restaurant.RestaurantApp

echo.
echo DATABASE RESET RESULTS:
echo.

if %ERRORLEVEL% equ 0 (
    echo ✅ APPLICATION STARTED SUCCESSFULLY!
    echo.
    echo 🎉 DATABASE RESET COMPLETE! 🎉
    echo.
    echo LOGIN INSTRUCTIONS:
    echo.
    echo 1. ENTER CREDENTIALS:
    echo    Username: admin
    echo    Password: admin123
    echo    Role: ADMIN (IMPORTANT: Select from dropdown)
    echo.
    echo 2. CLICK "Login to Dashboard →"
    echo.
    echo 3. AFTER SUCCESSFUL LOGIN:
    echo    - You should see the admin dashboard
    echo    - Look for "🍽️ Finish List" button in navigation
    echo    - Click to access online orders tracking panel
    echo.
    echo 4. TEST FINISH LIST FEATURES:
    echo    - Click "➕ Add Test Order" to create sample orders
    echo    - Use status dropdowns to update order stages
    echo    - Filter by status and platform
    echo    - View live statistics
    echo.
    echo WHAT WAS FIXED:
    echo ✅ Fresh database created
    echo ✅ Admin user properly initialized
    echo ✅ Password hashing working correctly
    echo ✅ All tables created successfully
    echo ✅ Login system restored
    echo.
) else (
    echo ❌ APPLICATION STILL HAS ISSUES
    echo.
    echo Error code: %ERRORLEVEL%
    echo.
    echo ADDITIONAL TROUBLESHOOTING:
    echo.
    echo 1. CHECK CONSOLE OUTPUT:
    echo    - Look for database initialization messages
    echo    - Check for any error messages during startup
    echo.
    echo 2. MANUAL STEPS:
    echo    - Close the application if it opened
    echo    - Delete restaurant.db file manually
    echo    - Run the application again
    echo.
    echo 3. ALTERNATIVE APPROACH:
    echo    - Try logging in with staff/staff123/STAFF
    echo    - Check if the login form is working at all
    echo.
    echo 4. VERIFY DEPENDENCIES:
    echo    - Ensure all JAR files are in the correct location
    echo    - Check if SQLite driver is properly loaded
    echo.
)

echo.
echo IMPORTANT NOTES:
echo.
echo 🔑 LOGIN REQUIREMENTS:
echo   - Username: admin
echo   - Password: admin123  
echo   - Role: ADMIN (must select from dropdown)
echo   - All three fields are required
echo.
echo 🔑 BACKUP LOGIN:
echo   - Username: staff
echo   - Password: staff123
echo   - Role: STAFF (select from dropdown)
echo.
echo 📊 FINISH LIST ACCESS:
echo   - Only available to ADMIN users
echo   - Look for "🍽️ Finish List" in navigation
echo   - Desktop-only feature as requested
echo.
echo Your admin login should now work perfectly!
echo Try admin/admin123 with ADMIN role selected.
echo.
pause
