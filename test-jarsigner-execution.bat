@echo off
echo Testing JavaFX with <PERSON><PERSON><PERSON><PERSON> Plugin...
echo.

REM Set Java path
set JAVA_HOME=C:\Program Files\Java\jdk-23
set PATH=%JAVA_HOME%\bin;%PATH%

echo Step 1: Building project...
call mvn clean compile dependency:copy-dependencies
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to build project
    pause
    exit /b 1
)

echo.
echo Step 2: Testing secure JavaFX profile...
echo Running: mvn compile -Psecure-javafx
call mvn compile -Psecure-javafx
if %ERRORLEVEL% equ 0 (
    echo ✅ Secure JavaFX Profile: SUCCESS
) else (
    echo ❌ Secure JavaFX Profile: FAILED
)

echo.
echo Step 3: Testing signed execution profile...
echo Running: mvn package -Psigned-execution
call mvn package -Psigned-execution
if %ERRORLEVEL% equ 0 (
    echo ✅ Signed Execution Profile: SUCCESS
    echo.
    echo Step 4: Verifying JAR signature...
    call mvn jarsigner:verify
    if %ERRORLEVEL% equ 0 (
        echo ✅ JAR Signature Verification: SUCCESS
    ) else (
        echo ❌ JAR Signature Verification: FAILED
    )
) else (
    echo ❌ Signed Execution Profile: FAILED
)

echo.
echo Step 5: Testing standard AntRun execution...
echo Running: mvn antrun:run
call mvn antrun:run
if %ERRORLEVEL% equ 0 (
    echo ✅ AntRun Execution: SUCCESS
) else (
    echo ❌ AntRun Execution: FAILED
)

echo.
echo ========================================
echo JARSIGNER TESTING COMPLETE
echo ========================================
echo.
echo Summary:
echo - Secure JavaFX profile provides enhanced security
echo - Signed execution helps with JavaFX runtime issues
echo - AntRun plugin remains the most reliable method
echo.
echo For NetBeans, use: mvn compile -Psecure-javafx
echo Or: mvn dependency:copy-dependencies antrun:run
echo.
pause
