package com.restaurant.model;

/**
 * Model class for individual items in online orders
 */
public class OnlineOrderItem {
    
    private int id;
    private int onlineOrderId;
    private String itemName;
    private int quantity;
    private double unitPrice;
    private double totalPrice;
    private String specialInstructions;
    private String category;
    
    // Constructors
    public OnlineOrderItem() {}
    
    public OnlineOrderItem(int id, int onlineOrderId, String itemName, int quantity, 
                          double unitPrice, String category) {
        this.id = id;
        this.onlineOrderId = onlineOrderId;
        this.itemName = itemName;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        this.totalPrice = quantity * unitPrice;
        this.category = category;
    }
    
    public OnlineOrderItem(String itemName, int quantity, double unitPrice, String category) {
        this.itemName = itemName;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        this.totalPrice = quantity * unitPrice;
        this.category = category;
    }
    
    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public int getOnlineOrderId() { return onlineOrderId; }
    public void setOnlineOrderId(int onlineOrderId) { this.onlineOrderId = onlineOrderId; }
    
    public String getItemName() { return itemName; }
    public void setItemName(String itemName) { this.itemName = itemName; }
    
    public int getQuantity() { return quantity; }
    public void setQuantity(int quantity) { 
        this.quantity = quantity;
        this.totalPrice = quantity * unitPrice;
    }
    
    public double getUnitPrice() { return unitPrice; }
    public void setUnitPrice(double unitPrice) { 
        this.unitPrice = unitPrice;
        this.totalPrice = quantity * unitPrice;
    }
    
    public double getTotalPrice() { return totalPrice; }
    public void setTotalPrice(double totalPrice) { this.totalPrice = totalPrice; }
    
    public String getSpecialInstructions() { return specialInstructions; }
    public void setSpecialInstructions(String specialInstructions) { this.specialInstructions = specialInstructions; }
    
    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }
    
    // Utility methods
    public String getFormattedUnitPrice() {
        return String.format("₹%.2f", unitPrice);
    }
    
    public String getFormattedTotalPrice() {
        return String.format("₹%.2f", totalPrice);
    }
    
    public String getDisplayText() {
        return String.format("%s x%d - %s", itemName, quantity, getFormattedTotalPrice());
    }
    
    @Override
    public String toString() {
        return String.format("OnlineOrderItem{id=%d, itemName='%s', quantity=%d, unitPrice=%.2f, totalPrice=%.2f}", 
                           id, itemName, quantity, unitPrice, totalPrice);
    }
}
