@echo off
echo 🔍 QUICK DATABASE CHECK...
echo.

echo The database file exists and is 55MB - that's quite large!
echo This suggests the database has data, but we need to check if admin user exists.
echo.

echo Let's try a different approach - recreate the admin user directly...
echo.

echo STEP 1: Delete the large database (might be corrupted)...
if exist restaurant.db (
    del restaurant.db
    echo ✅ Deleted large database file
) else (
    echo ℹ️ No database to delete
)

echo.
echo STEP 2: Create fresh, clean database...

echo package com.restaurant.util; > src\main\java\com\restaurant\util\QuickAdminCreator.java
echo. >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo import java.sql.*; >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo. >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo public class QuickAdminCreator { >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo     public static void main(String[] args) { >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo         System.out.println("🔧 Creating fresh database with admin user..."); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo. >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo         try { >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             Class.forName("org.sqlite.JDBC"); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             System.out.println("✅ SQLite driver loaded"); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             System.err.println("❌ Driver error: " + e.getMessage()); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             return; >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo         } >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo. >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo         String url = "*************************"; >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo. >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo         try (Connection conn = DriverManager.getConnection(url)) { >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             System.out.println("✅ Database connected"); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo. >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             // Create users table >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             String createTable = "CREATE TABLE users (" + >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo                 "id INTEGER PRIMARY KEY AUTOINCREMENT, " + >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo                 "username TEXT UNIQUE NOT NULL, " + >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo                 "password TEXT NOT NULL, " + >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo                 "role TEXT NOT NULL)"; >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo. >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             conn.createStatement().execute(createTable); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             System.out.println("✅ Users table created"); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo. >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             // Insert admin with BCrypt hash for 'admin123' >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             String insert = "INSERT INTO users (username, password, role) VALUES (?, ?, ?)"; >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             PreparedStatement ps = conn.prepareStatement(insert); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             ps.setString(1, "admin"); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             ps.setString(2, "$2a$10$N9qo8uLOickgx2ZMRZoMye.Uo0aBOBjXoXebXeQABkc4ILoLGxjvO"); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             ps.setString(3, "ADMIN"); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             ps.executeUpdate(); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             ps.close(); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             System.out.println("✅ Admin user created"); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo. >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             // Verify >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             ResultSet rs = conn.createStatement().executeQuery("SELECT * FROM users"); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             while (rs.next()) { >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo                 System.out.println("User: " + rs.getString("username") + " | Role: " + rs.getString("role")); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             } >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             rs.close(); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo. >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             System.out.println("🎉 SUCCESS! Admin user ready for login."); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo. >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             System.err.println("❌ Error: " + e.getMessage()); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo             e.printStackTrace(); >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo         } >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo     } >> src\main\java\com\restaurant\util\QuickAdminCreator.java
echo } >> src\main\java\com\restaurant\util\QuickAdminCreator.java

echo ✅ QuickAdminCreator created
echo.

echo STEP 3: Compile and run...
javac -cp "%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar" -d target/classes src/main/java/com/restaurant/util/QuickAdminCreator.java

if %ERRORLEVEL% neq 0 (
    echo ❌ Compilation failed, trying Maven...
    mvn compile -q
)

echo.
echo STEP 4: Creating admin user...
java -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar" com.restaurant.util.QuickAdminCreator

echo.
echo STEP 5: Check new database size...
if exist restaurant.db (
    dir restaurant.db
    echo.
    echo ✅ New database created - should be much smaller than 55MB
) else (
    echo ❌ Database creation failed
    pause
    exit /b 1
)

echo.
echo 🔑 LOGIN CREDENTIALS:
echo Username: admin
echo Password: admin123
echo Role: ADMIN
echo.

echo STEP 6: Starting application...
echo.

java -Dprism.order=sw ^
     -Dprism.allowhidpi=false ^
     -Djavafx.animation.pulse=60 ^
     -Djava.awt.headless=false ^
     -Dsqlite.purejava=false ^
     -Xms512m ^
     -Xmx2g ^
     -XX:+UseG1GC ^
     -XX:MaxGCPauseMillis=200 ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics,javafx.media ^
     -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\pdfbox\2.0.27\pdfbox-2.0.27.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\fontbox\2.0.27\fontbox-2.0.27.jar;%USERPROFILE%\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2.jar" ^
     com.restaurant.RestaurantApp

echo.
echo 🎉 QUICK DATABASE FIX COMPLETE!
echo.
echo The 55MB database was likely corrupted or had issues.
echo We've created a fresh, clean database with just the admin user.
echo.
echo LOGIN WITH:
echo Username: admin
echo Password: admin123
echo Role: ADMIN (select from dropdown)
echo.
echo After login, click "🍽️ Finish List" to test MP3 audio!
echo.

pause
