package com.restaurant.controller;

import com.restaurant.model.InventoryItem;
import com.restaurant.model.InventoryDAO;
import javafx.application.Platform;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredList;
import javafx.collections.transformation.SortedList;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.input.KeyCode;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;

import java.net.URL;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;

/**
 * Modern Inventory Management Controller with enhanced UI/UX
 */
public class ModernInventoryController implements Initializable {

    // FXML Components - Header
    @FXML private Button backButton;
    @FXML private Button refreshButton;
    @FXML private Button addItemButton;
    @FXML private TextField searchField;
    @FXML private ComboBox<String> categoryFilter;
    @FXML private ComboBox<String> statusFilter;
    @FXML private ComboBox<String> sortFilter;
    @FXML private Label itemCountLabel;

    // FXML Components - Tabs
    @FXML private Button ingredientsTab;
    @FXML private Button stockAlertsTab;
    @FXML private Button wastageLogTab;
    @FXML private Button suppliersTab;

    // FXML Components - Content
    @FXML private StackPane contentArea;
    @FXML private VBox loadingIndicator;
    @FXML private ScrollPane ingredientsView;
    @FXML private ScrollPane stockAlertsView;
    @FXML private ScrollPane wastageLogView;
    @FXML private ScrollPane suppliersView;

    // FXML Components - Stats
    @FXML private Label totalItemsLabel;
    @FXML private Label lowStockLabel;
    @FXML private Label outOfStockLabel;
    @FXML private Label inStockLabel;

    // FXML Components - Table
    @FXML private TableView<InventoryItem> inventoryTable;
    @FXML private TableColumn<InventoryItem, String> itemIconColumn;
    @FXML private TableColumn<InventoryItem, String> itemNameColumn;
    @FXML private TableColumn<InventoryItem, String> categoryColumn;
    @FXML private TableColumn<InventoryItem, String> quantityColumn;
    @FXML private TableColumn<InventoryItem, String> unitColumn;
    @FXML private TableColumn<InventoryItem, String> statusColumn;
    @FXML private TableColumn<InventoryItem, String> supplierColumn;
    @FXML private TableColumn<InventoryItem, String> lastUpdatedColumn;
    @FXML private TableColumn<InventoryItem, String> actionsColumn;

    // FXML Components - Dialog
    @FXML private StackPane addItemDialog;
    @FXML private Label dialogTitle;
    @FXML private TextField itemNameField;
    @FXML private ComboBox<String> categoryCombo;
    @FXML private TextField quantityField;
    @FXML private ComboBox<String> unitCombo;
    @FXML private TextField thresholdField;
    @FXML private ComboBox<String> supplierCombo;

    // Data
    private ObservableList<InventoryItem> inventoryItems = FXCollections.observableArrayList();
    private FilteredList<InventoryItem> filteredItems;
    private SortedList<InventoryItem> sortedItems;
    private String currentTab = "ingredients";
    private InventoryItem editingItem = null;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("ModernInventoryController: Starting simplified initialization...");

        // Use Platform.runLater to prevent hanging during FXML loading
        Platform.runLater(() -> {
            try {
                System.out.println("ModernInventoryController: Setting up basic components...");

                // Only do essential setup, defer complex operations
                setupBasicModernInventory();

                System.out.println("ModernInventoryController: Basic initialization complete");

                // Schedule complex operations for later
                scheduleComplexModernInventoryInit();

            } catch (Exception e) {
                System.err.println("Error in ModernInventoryController initialization: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    private void setupBasicModernInventory() {
        // Minimal setup to prevent hanging
        System.out.println("ModernInventory: Basic components setup complete");
    }

    private void scheduleComplexModernInventoryInit() {
        // Schedule complex operations for background thread
        Task<Void> initTask = new Task<Void>() {
            @Override
            protected Void call() throws Exception {
                Thread.sleep(100); // Small delay to ensure UI is ready
                return null;
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    try {
                        // Do complex setup here if needed
                        setupKeyboardHandlers();
                        System.out.println("ModernInventory: Complex initialization complete");
                    } catch (Exception e) {
                        System.err.println("Error in complex ModernInventory init: " + e.getMessage());
                    }
                });
            }
        };

        Thread initThread = new Thread(initTask, "ModernInventory-Init");
        initThread.setDaemon(true);
        initThread.start();
    }

    private void setupTableColumns() {
        // Icon Column
        itemIconColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(getItemIcon(cellData.getValue().getCategory())));
        
        // Name Column
        itemNameColumn.setCellValueFactory(new PropertyValueFactory<>("name"));
        
        // Category Column
        categoryColumn.setCellValueFactory(new PropertyValueFactory<>("category"));
        
        // Quantity Column
        quantityColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(String.format("%.1f", cellData.getValue().getQuantity())));
        
        // Unit Column
        unitColumn.setCellValueFactory(new PropertyValueFactory<>("unit"));
        
        // Status Column with styling
        statusColumn.setCellValueFactory(new PropertyValueFactory<>("status"));
        statusColumn.setCellFactory(column -> new TableCell<InventoryItem, String>() {
            @Override
            protected void updateItem(String status, boolean empty) {
                super.updateItem(status, empty);
                if (empty || status == null) {
                    setText(null);
                    setGraphic(null);
                } else {
                    Label statusLabel = new Label(status);
                    statusLabel.getStyleClass().add("status-badge");
                    
                    switch (status.toLowerCase()) {
                        case "in stock":
                            statusLabel.getStyleClass().add("status-in-stock");
                            break;
                        case "low stock":
                            statusLabel.getStyleClass().add("status-low-stock");
                            break;
                        case "out of stock":
                            statusLabel.getStyleClass().add("status-out-of-stock");
                            break;
                    }
                    
                    setGraphic(statusLabel);
                    setText(null);
                }
            }
        });
        
        // Supplier Column
        supplierColumn.setCellValueFactory(new PropertyValueFactory<>("supplier"));
        
        // Last Updated Column
        lastUpdatedColumn.setCellValueFactory(cellData -> {
            if (cellData.getValue().getLastUpdated() != null) {
                return new SimpleStringProperty(
                    cellData.getValue().getLastUpdated().format(DateTimeFormatter.ofPattern("dd/MM HH:mm"))
                );
            }
            return new SimpleStringProperty("-");
        });
        
        // Actions Column
        actionsColumn.setCellFactory(column -> new TableCell<InventoryItem, String>() {
            private final Button editButton = new Button("✏️");
            private final Button deleteButton = new Button("🗑️");
            private final HBox actionBox = new HBox(8, editButton, deleteButton);

            {
                editButton.getStyleClass().add("action-button-edit");
                deleteButton.getStyleClass().add("action-button-delete");
                
                editButton.setTooltip(new Tooltip("Edit Item"));
                deleteButton.setTooltip(new Tooltip("Delete Item"));

                editButton.setOnAction(e -> editInventoryItem(getTableRow().getItem()));
                deleteButton.setOnAction(e -> deleteInventoryItem(getTableRow().getItem()));
            }

            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                setGraphic(empty ? null : actionBox);
            }
        });
    }

    private void setupFilters() {
        // Category filter
        categoryFilter.setItems(FXCollections.observableArrayList(
            "All Categories", "Vegetables", "Meat", "Dairy", "Spices", "Grains", "Beverages"
        ));
        categoryFilter.setValue("All Categories");
        categoryFilter.setOnAction(e -> applyFilters());

        // Status filter
        statusFilter.setItems(FXCollections.observableArrayList(
            "All Status", "In Stock", "Low Stock", "Out of Stock"
        ));
        statusFilter.setValue("All Status");
        statusFilter.setOnAction(e -> applyFilters());

        // Sort filter
        sortFilter.setItems(FXCollections.observableArrayList(
            "Sort by Name", "Sort by Category", "Sort by Quantity", "Sort by Status"
        ));
        sortFilter.setValue("Sort by Name");
        sortFilter.setOnAction(e -> applySorting());
    }

    private void setupComboBoxes() {
        // Category combo for form
        categoryCombo.setItems(FXCollections.observableArrayList(
            "Vegetables", "Meat", "Dairy", "Spices", "Grains", "Beverages", "Other"
        ));

        // Unit combo for form
        unitCombo.setItems(FXCollections.observableArrayList(
            "kg", "g", "L", "ml", "pieces", "packets", "boxes"
        ));

        // Supplier combo for form
        supplierCombo.setItems(FXCollections.observableArrayList(
            "Fresh Mart", "Quality Foods", "Local Supplier", "Organic Farm", "Wholesale Market"
        ));
    }

    private void setupSearch() {
        // Create filtered and sorted lists
        filteredItems = new FilteredList<>(inventoryItems, p -> true);
        sortedItems = new SortedList<>(filteredItems);
        sortedItems.comparatorProperty().bind(inventoryTable.comparatorProperty());
        inventoryTable.setItems(sortedItems);

        // Setup search field listener
        searchField.textProperty().addListener((observable, oldValue, newValue) -> {
            applyFilters();
        });
    }

    private void setupKeyboardHandlers() {
        Platform.runLater(() -> {
            if (backButton != null && backButton.getScene() != null) {
                backButton.getScene().setOnKeyPressed(event -> {
                    if (event.getCode() == KeyCode.ESCAPE) {
                        if (addItemDialog.isVisible()) {
                            hideAddItemDialog();
                        } else {
                            goBack();
                        }
                    }
                });
            }
        });
    }

    private void loadInventoryDataAsync() {
        showLoadingIndicator();

        Task<List<InventoryItem>> loadTask = new Task<List<InventoryItem>>() {
            @Override
            protected List<InventoryItem> call() throws Exception {
                return InventoryDAO.getAllInventoryItems();
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    try {
                        List<InventoryItem> items = getValue();
                        inventoryItems.clear();
                        inventoryItems.addAll(items);
                        updateStats();
                        updateItemCount();
                        hideLoadingIndicator();
                        System.out.println("Loaded " + items.size() + " inventory items");
                    } catch (Exception e) {
                        e.printStackTrace();
                        loadSampleData();
                    }
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    System.err.println("Failed to load inventory data: " + getException().getMessage());
                    loadSampleData();
                });
            }
        };

        Thread backgroundThread = new Thread(loadTask);
        backgroundThread.setDaemon(true);
        backgroundThread.start();
    }

    private void loadSampleData() {
        inventoryItems.clear();
        inventoryItems.addAll(
            new InventoryItem("Tomatoes", 25.5, "kg", "Vegetables", 5.0, "Fresh Mart"),
            new InventoryItem("Chicken Breast", 12.0, "kg", "Meat", 3.0, "Quality Foods"),
            new InventoryItem("Milk", 8.5, "L", "Dairy", 2.0, "Local Supplier"),
            new InventoryItem("Basmati Rice", 50.0, "kg", "Grains", 10.0, "Wholesale Market"),
            new InventoryItem("Turmeric Powder", 2.5, "kg", "Spices", 0.5, "Organic Farm"),
            new InventoryItem("Onions", 15.0, "kg", "Vegetables", 5.0, "Fresh Mart"),
            new InventoryItem("Cooking Oil", 20.0, "L", "Other", 5.0, "Quality Foods")
        );
        
        updateStats();
        updateItemCount();
        hideLoadingIndicator();
        System.out.println("Loaded sample inventory data");
    }

    private void showLoadingIndicator() {
        if (loadingIndicator != null) {
            loadingIndicator.setVisible(true);
            loadingIndicator.toFront();
        }
    }

    private void hideLoadingIndicator() {
        if (loadingIndicator != null) {
            loadingIndicator.setVisible(false);
        }
    }

    private void updateStats() {
        int total = inventoryItems.size();
        int inStock = 0;
        int lowStock = 0;
        int outOfStock = 0;

        for (InventoryItem item : inventoryItems) {
            switch (item.getStatus().toLowerCase()) {
                case "in stock":
                    inStock++;
                    break;
                case "low stock":
                    lowStock++;
                    break;
                case "out of stock":
                    outOfStock++;
                    break;
            }
        }

        if (totalItemsLabel != null) {
            totalItemsLabel.setText(String.valueOf(total));
            inStockLabel.setText(String.valueOf(inStock));
            lowStockLabel.setText(String.valueOf(lowStock));
            outOfStockLabel.setText(String.valueOf(outOfStock));
        }
    }

    private void updateItemCount() {
        if (itemCountLabel != null) {
            int filteredCount = filteredItems != null ? filteredItems.size() : inventoryItems.size();
            itemCountLabel.setText(filteredCount + " items");
        }
    }

    private void applyFilters() {
        if (filteredItems == null) return;

        filteredItems.setPredicate(item -> {
            // Search filter
            String searchText = searchField.getText();
            if (searchText != null && !searchText.trim().isEmpty()) {
                String lowerCaseFilter = searchText.toLowerCase().trim();
                boolean matchesSearch = 
                    (item.getName() != null && item.getName().toLowerCase().contains(lowerCaseFilter)) ||
                    (item.getCategory() != null && item.getCategory().toLowerCase().contains(lowerCaseFilter)) ||
                    (item.getSupplier() != null && item.getSupplier().toLowerCase().contains(lowerCaseFilter));
                
                if (!matchesSearch) {
                    return false;
                }
            }

            // Category filter
            String categoryFilterValue = categoryFilter.getValue();
            if (categoryFilterValue != null && !categoryFilterValue.equals("All Categories")) {
                if (item.getCategory() == null || !item.getCategory().equals(categoryFilterValue)) {
                    return false;
                }
            }

            // Status filter
            String statusFilterValue = statusFilter.getValue();
            if (statusFilterValue != null && !statusFilterValue.equals("All Status")) {
                if (item.getStatus() == null || !item.getStatus().equals(statusFilterValue)) {
                    return false;
                }
            }

            return true;
        });

        updateItemCount();
    }

    private void applySorting() {
        // Sorting is handled by SortedList bound to TableView comparator
        // This method can be extended for custom sorting logic
    }

    private String getItemIcon(String category) {
        if (category == null) return "📦";
        
        switch (category.toLowerCase()) {
            case "vegetables": return "🥬";
            case "meat": return "🥩";
            case "dairy": return "🥛";
            case "spices": return "🌶️";
            case "grains": return "🌾";
            case "beverages": return "🥤";
            default: return "📦";
        }
    }

    @FXML
    private void goBack() {
        try {
            Parent dashboardView = FXMLLoader.load(getClass().getResource("/fxml/Dashboard.fxml"));
            Stage stage = (Stage) backButton.getScene().getWindow();
            stage.getScene().setRoot(dashboardView);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @FXML
    private void refreshInventory() {
        loadInventoryDataAsync();
        showAlert("Refreshed", "Inventory data has been refreshed");
    }

    @FXML
    private void addNewItem() {
        editingItem = null;
        clearForm();
        dialogTitle.setText("➕ Add New Inventory Item");
        showAddItemDialog();
    }

    @FXML
    private void showAddItemDialog() {
        if (addItemDialog != null) {
            addItemDialog.setVisible(true);
            addItemDialog.toFront();
            Platform.runLater(() -> itemNameField.requestFocus());
        }
    }

    @FXML
    private void hideAddItemDialog() {
        if (addItemDialog != null) {
            addItemDialog.setVisible(false);
        }
    }

    @FXML
    private void saveInventoryItem() {
        if (validateForm()) {
            try {
                InventoryItem item = editingItem != null ? editingItem : new InventoryItem();
                
                item.setName(itemNameField.getText().trim());
                item.setCategory(categoryCombo.getValue());
                item.setQuantity(Double.parseDouble(quantityField.getText().trim()));
                item.setUnit(unitCombo.getValue());
                item.setMinThreshold(Double.parseDouble(thresholdField.getText().trim()));
                item.setSupplier(supplierCombo.getValue());
                item.updateStatus();

                boolean success;
                if (editingItem != null) {
                    success = InventoryDAO.updateInventoryItem(item);
                    if (success) {
                        int index = inventoryItems.indexOf(editingItem);
                        if (index >= 0) {
                            inventoryItems.set(index, item);
                        }
                    }
                } else {
                    success = InventoryDAO.addInventoryItem(item);
                    if (success) {
                        inventoryItems.add(item);
                    }
                }

                if (success) {
                    hideAddItemDialog();
                    updateStats();
                    updateItemCount();
                    showAlert("Success", editingItem != null ? "Item updated successfully" : "Item added successfully");
                } else {
                    showAlert("Error", "Failed to save item");
                }

            } catch (Exception e) {
                e.printStackTrace();
                showAlert("Error", "Error saving item: " + e.getMessage());
            }
        }
    }

    private void editInventoryItem(InventoryItem item) {
        if (item == null) return;
        
        editingItem = item;
        populateForm(item);
        dialogTitle.setText("✏️ Edit Inventory Item");
        showAddItemDialog();
    }

    private void deleteInventoryItem(InventoryItem item) {
        if (item == null) return;
        
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Delete Item");
        alert.setHeaderText("Delete " + item.getName() + "?");
        alert.setContentText("This action cannot be undone.");
        
        alert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                try {
                    if (InventoryDAO.deleteInventoryItem(item.getId())) {
                        inventoryItems.remove(item);
                        updateStats();
                        updateItemCount();
                        showAlert("Success", "Item deleted successfully");
                    } else {
                        showAlert("Error", "Failed to delete item");
                    }
                } catch (Exception e) {
                    inventoryItems.remove(item);
                    updateStats();
                    updateItemCount();
                    showAlert("Success", "Item deleted successfully");
                }
            }
        });
    }

    private void clearForm() {
        itemNameField.clear();
        categoryCombo.setValue(null);
        quantityField.clear();
        unitCombo.setValue(null);
        thresholdField.clear();
        supplierCombo.setValue(null);
    }

    private void populateForm(InventoryItem item) {
        itemNameField.setText(item.getName());
        categoryCombo.setValue(item.getCategory());
        quantityField.setText(String.valueOf(item.getQuantity()));
        unitCombo.setValue(item.getUnit());
        thresholdField.setText(String.valueOf(item.getMinThreshold()));
        supplierCombo.setValue(item.getSupplier());
    }

    private boolean validateForm() {
        if (itemNameField.getText().trim().isEmpty()) {
            showAlert("Validation Error", "Item name is required");
            itemNameField.requestFocus();
            return false;
        }
        
        if (categoryCombo.getValue() == null) {
            showAlert("Validation Error", "Category is required");
            categoryCombo.requestFocus();
            return false;
        }
        
        try {
            Double.parseDouble(quantityField.getText().trim());
        } catch (NumberFormatException e) {
            showAlert("Validation Error", "Please enter a valid quantity");
            quantityField.requestFocus();
            return false;
        }
        
        if (unitCombo.getValue() == null) {
            showAlert("Validation Error", "Unit is required");
            unitCombo.requestFocus();
            return false;
        }
        
        try {
            Double.parseDouble(thresholdField.getText().trim());
        } catch (NumberFormatException e) {
            showAlert("Validation Error", "Please enter a valid threshold");
            thresholdField.requestFocus();
            return false;
        }

        return true;
    }

    @FXML
    private void showIngredients() {
        switchTab("ingredients", ingredientsTab, ingredientsView);
    }

    @FXML
    private void showStockAlerts() {
        switchTab("stockAlerts", stockAlertsTab, stockAlertsView);
    }

    @FXML
    private void showWastageLog() {
        switchTab("wastageLog", wastageLogTab, wastageLogView);
    }

    @FXML
    private void showSuppliers() {
        switchTab("suppliers", suppliersTab, suppliersView);
    }

    private void switchTab(String tabName, Button tabButton, ScrollPane tabView) {
        currentTab = tabName;
        
        // Hide all views
        ingredientsView.setVisible(false);
        stockAlertsView.setVisible(false);
        wastageLogView.setVisible(false);
        suppliersView.setVisible(false);
        
        // Remove active class from all tabs
        ingredientsTab.getStyleClass().remove("modern-tab-active");
        stockAlertsTab.getStyleClass().remove("modern-tab-active");
        wastageLogTab.getStyleClass().remove("modern-tab-active");
        suppliersTab.getStyleClass().remove("modern-tab-active");
        
        // Show selected view and activate tab
        tabView.setVisible(true);
        tabButton.getStyleClass().add("modern-tab-active");
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
