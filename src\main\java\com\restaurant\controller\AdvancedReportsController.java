package com.restaurant.controller;

import com.restaurant.model.*;
import com.restaurant.service.ReportService;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.VBox;
import javafx.scene.chart.*;
import javafx.geometry.Insets;

import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;

public class AdvancedReportsController implements Initializable {
    
    @FXML private TabPane reportsTabPane;
    @FXML private VBox analyticsContainer;
    
    // Daily Reports Tab
    @FXML private DatePicker dailyDatePicker;
    @FXML private Button generateDailyReportBtn;
    @FXML private TableView<DailyReport> dailyReportsTable;
    @FXML private TableColumn<DailyReport, String> dailyDateColumn;
    @FXML private TableColumn<DailyReport, Integer> dailyOrdersColumn;
    @FXML private TableColumn<DailyReport, Double> dailyRevenueColumn;
    @FXML private TableColumn<DailyReport, Integer> dailySwiggyColumn;
    @FXML private TableColumn<DailyReport, Integer> dailyZomatoColumn;
    @FXML private TableColumn<DailyReport, String> dailyPeakHourColumn;
    
    // Weekly Reports Tab
    @FXML private DatePicker weeklyStartDatePicker;
    @FXML private DatePicker weeklyEndDatePicker;
    @FXML private Button generateWeeklyReportBtn;
    @FXML private TableView<WeeklyReport> weeklyReportsTable;
    @FXML private TableColumn<WeeklyReport, String> weeklyPeriodColumn;
    @FXML private TableColumn<WeeklyReport, Integer> weeklyOrdersColumn;
    @FXML private TableColumn<WeeklyReport, Double> weeklyRevenueColumn;
    @FXML private TableColumn<WeeklyReport, Double> weeklyAvgOrdersColumn;
    @FXML private TableColumn<WeeklyReport, String> weeklyBestDayColumn;
    
    // Monthly Reports Tab
    @FXML private ComboBox<Integer> monthComboBox;
    @FXML private ComboBox<Integer> yearComboBox;
    @FXML private Button generateMonthlyReportBtn;
    @FXML private TableView<MonthlyReport> monthlyReportsTable;
    @FXML private TableColumn<MonthlyReport, String> monthlyPeriodColumn;
    @FXML private TableColumn<MonthlyReport, Integer> monthlyOrdersColumn;
    @FXML private TableColumn<MonthlyReport, Double> monthlyRevenueColumn;
    @FXML private TableColumn<MonthlyReport, Double> monthlyGrowthColumn;
    @FXML private TableColumn<MonthlyReport, String> monthlyBestWeekColumn;
    
    // Analytics Tab
    @FXML private Label todayOrdersLabel;
    @FXML private Label todayRevenueLabel;
    @FXML private Label weekOrdersLabel;
    @FXML private Label weekRevenueLabel;
    @FXML private Label monthOrdersLabel;
    @FXML private Label monthRevenueLabel;
    @FXML private Label monthGrowthLabel;
    @FXML private PieChart platformChart;
    @FXML private LineChart<String, Number> revenueChart;
    
    // Navigation
    @FXML private Button homeBtn;
    @FXML private Button notificationsBtn;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("AdvancedReportsController: Initializing...");
        
        try {
            setupDatePickers();
            setupComboBoxes();
            setupTables();
            setupCharts();
            setupEventHandlers();
            setupNavigation();
            loadInitialData();
            
            System.out.println("AdvancedReportsController: Initialization complete");
        } catch (Exception e) {
            System.err.println("Error initializing AdvancedReportsController: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void setupDatePickers() {
        // Set default dates
        dailyDatePicker.setValue(LocalDate.now());
        
        LocalDate today = LocalDate.now();
        LocalDate weekStart = today.minusDays(today.getDayOfWeek().getValue() - 1);
        weeklyStartDatePicker.setValue(weekStart);
        weeklyEndDatePicker.setValue(weekStart.plusDays(6));
    }
    
    private void setupComboBoxes() {
        // Setup month combo box
        ObservableList<Integer> months = FXCollections.observableArrayList();
        for (int i = 1; i <= 12; i++) {
            months.add(i);
        }
        monthComboBox.setItems(months);
        monthComboBox.setValue(LocalDate.now().getMonthValue());
        
        // Setup year combo box
        ObservableList<Integer> years = FXCollections.observableArrayList();
        int currentYear = LocalDate.now().getYear();
        for (int i = currentYear - 5; i <= currentYear + 1; i++) {
            years.add(i);
        }
        yearComboBox.setItems(years);
        yearComboBox.setValue(currentYear);
    }
    
    private void setupTables() {
        // Daily Reports Table
        dailyDateColumn.setCellValueFactory(new PropertyValueFactory<>("reportDate"));
        dailyOrdersColumn.setCellValueFactory(new PropertyValueFactory<>("totalOrders"));
        dailyRevenueColumn.setCellValueFactory(new PropertyValueFactory<>("totalRevenue"));
        dailySwiggyColumn.setCellValueFactory(new PropertyValueFactory<>("swiggyOrders"));
        dailyZomatoColumn.setCellValueFactory(new PropertyValueFactory<>("zomatoOrders"));
        dailyPeakHourColumn.setCellValueFactory(new PropertyValueFactory<>("peakHour"));
        
        // Format revenue columns
        formatRevenueColumn(dailyRevenueColumn);
        formatRevenueColumn(weeklyRevenueColumn);
        formatRevenueColumn(monthlyRevenueColumn);
        
        // Weekly Reports Table
        weeklyPeriodColumn.setCellValueFactory(new PropertyValueFactory<>("weekPeriod"));
        weeklyOrdersColumn.setCellValueFactory(new PropertyValueFactory<>("totalOrders"));
        weeklyRevenueColumn.setCellValueFactory(new PropertyValueFactory<>("totalRevenue"));
        weeklyAvgOrdersColumn.setCellValueFactory(new PropertyValueFactory<>("avgDailyOrders"));
        weeklyBestDayColumn.setCellValueFactory(new PropertyValueFactory<>("bestDay"));
        
        // Monthly Reports Table
        monthlyPeriodColumn.setCellValueFactory(new PropertyValueFactory<>("monthYear"));
        monthlyOrdersColumn.setCellValueFactory(new PropertyValueFactory<>("totalOrders"));
        monthlyRevenueColumn.setCellValueFactory(new PropertyValueFactory<>("totalRevenue"));
        monthlyGrowthColumn.setCellValueFactory(new PropertyValueFactory<>("growthPercentage"));
        monthlyBestWeekColumn.setCellValueFactory(new PropertyValueFactory<>("bestWeek"));
        
        // Format growth column
        monthlyGrowthColumn.setCellFactory(column -> new TableCell<MonthlyReport, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    String text = String.format("%.1f%%", item);
                    setText(text);
                    // Color code growth
                    if (item > 0) {
                        setStyle("-fx-text-fill: green; -fx-font-weight: bold;");
                    } else if (item < 0) {
                        setStyle("-fx-text-fill: red; -fx-font-weight: bold;");
                    } else {
                        setStyle("-fx-text-fill: black;");
                    }
                }
            }
        });
    }
    
    private <T> void formatRevenueColumn(TableColumn<T, Double> column) {
        column.setCellFactory(col -> new TableCell<T, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText("₹" + String.format("%.2f", item));
                }
            }
        });
    }
    
    private void setupCharts() {
        if (platformChart != null) {
            platformChart.setTitle("Platform Distribution (Today)");
        }
        
        if (revenueChart != null) {
            CategoryAxis xAxis = (CategoryAxis) revenueChart.getXAxis();
            NumberAxis yAxis = (NumberAxis) revenueChart.getYAxis();
            xAxis.setLabel("Date");
            yAxis.setLabel("Revenue (₹)");
            revenueChart.setTitle("Revenue Trend (Last 7 Days)");
        }
    }
    
    private void setupEventHandlers() {
        if (generateDailyReportBtn != null) {
            generateDailyReportBtn.setOnAction(e -> generateDailyReport());
        }
        if (generateWeeklyReportBtn != null) {
            generateWeeklyReportBtn.setOnAction(e -> generateWeeklyReport());
        }
        if (generateMonthlyReportBtn != null) {
            generateMonthlyReportBtn.setOnAction(e -> generateMonthlyReport());
        }
    }
    
    private void setupNavigation() {
        if (homeBtn != null) {
            homeBtn.setOnAction(e -> navigateToHome());
        }
        if (notificationsBtn != null) {
            notificationsBtn.setOnAction(e -> navigateToNotifications());
        }
    }
    
    private void loadInitialData() {
        loadDailyReports();
        loadWeeklyReports();
        loadMonthlyReports();
        loadAnalytics();
    }
    
    @FXML
    private void generateDailyReport() {
        LocalDate selectedDate = dailyDatePicker.getValue();
        if (selectedDate != null) {
            boolean success = ReportService.generateAndSaveDailyReport(selectedDate);
            if (success) {
                showAlert("Success", "Daily report generated successfully!", Alert.AlertType.INFORMATION);
                loadDailyReports();
                loadAnalytics();
            } else {
                showAlert("Error", "Failed to generate daily report.", Alert.AlertType.ERROR);
            }
        }
    }
    
    @FXML
    private void generateWeeklyReport() {
        LocalDate startDate = weeklyStartDatePicker.getValue();
        LocalDate endDate = weeklyEndDatePicker.getValue();
        
        if (startDate != null && endDate != null && !startDate.isAfter(endDate)) {
            boolean success = ReportService.generateAndSaveWeeklyReport(startDate, endDate);
            if (success) {
                showAlert("Success", "Weekly report generated successfully!", Alert.AlertType.INFORMATION);
                loadWeeklyReports();
                loadAnalytics();
            } else {
                showAlert("Error", "Failed to generate weekly report.", Alert.AlertType.ERROR);
            }
        } else {
            showAlert("Error", "Please select valid start and end dates.", Alert.AlertType.ERROR);
        }
    }
    
    @FXML
    private void generateMonthlyReport() {
        Integer month = monthComboBox.getValue();
        Integer year = yearComboBox.getValue();
        
        if (month != null && year != null) {
            boolean success = ReportService.generateAndSaveMonthlyReport(month, year);
            if (success) {
                showAlert("Success", "Monthly report generated successfully!", Alert.AlertType.INFORMATION);
                loadMonthlyReports();
                loadAnalytics();
            } else {
                showAlert("Error", "Failed to generate monthly report.", Alert.AlertType.ERROR);
            }
        }
    }
    
    private void loadDailyReports() {
        try {
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = endDate.minusDays(30); // Last 30 days
            List<DailyReport> reports = ReportDAO.getDailyReports(startDate, endDate);
            if (dailyReportsTable != null) {
                dailyReportsTable.setItems(FXCollections.observableArrayList(reports));
            }
        } catch (Exception e) {
            System.err.println("Error loading daily reports: " + e.getMessage());
        }
    }
    
    private void loadWeeklyReports() {
        try {
            List<WeeklyReport> reports = ReportDAO.getWeeklyReports(10); // Last 10 weeks
            if (weeklyReportsTable != null) {
                weeklyReportsTable.setItems(FXCollections.observableArrayList(reports));
            }
        } catch (Exception e) {
            System.err.println("Error loading weekly reports: " + e.getMessage());
        }
    }
    
    private void loadMonthlyReports() {
        try {
            List<MonthlyReport> reports = ReportDAO.getMonthlyReports(12); // Last 12 months
            if (monthlyReportsTable != null) {
                monthlyReportsTable.setItems(FXCollections.observableArrayList(reports));
            }
        } catch (Exception e) {
            System.err.println("Error loading monthly reports: " + e.getMessage());
        }
    }
    
    private void loadAnalytics() {
        try {
            Map<String, Object> analytics = ReportService.getAnalyticsSummary();
            
            // Update summary labels
            updateLabel(todayOrdersLabel, String.valueOf(analytics.get("todayOrders")));
            updateLabel(todayRevenueLabel, "₹" + String.format("%.2f", (Double) analytics.get("todayRevenue")));
            updateLabel(weekOrdersLabel, String.valueOf(analytics.get("weekOrders")));
            updateLabel(weekRevenueLabel, "₹" + String.format("%.2f", (Double) analytics.get("weekRevenue")));
            updateLabel(monthOrdersLabel, String.valueOf(analytics.get("monthOrders")));
            updateLabel(monthRevenueLabel, "₹" + String.format("%.2f", (Double) analytics.get("monthRevenue")));
            
            Double growth = (Double) analytics.get("monthGrowth");
            if (monthGrowthLabel != null) {
                monthGrowthLabel.setText(String.format("%.1f%%", growth));
                if (growth > 0) {
                    monthGrowthLabel.setStyle("-fx-text-fill: green; -fx-font-weight: bold;");
                } else if (growth < 0) {
                    monthGrowthLabel.setStyle("-fx-text-fill: red; -fx-font-weight: bold;");
                }
            }
            
            // Update charts
            updatePlatformChart(analytics);
            updateRevenueChart();
            
        } catch (Exception e) {
            System.err.println("Error loading analytics: " + e.getMessage());
        }
    }
    
    private void updateLabel(Label label, String text) {
        if (label != null) {
            label.setText(text);
        }
    }
    
    private void updatePlatformChart(Map<String, Object> analytics) {
        if (platformChart != null) {
            try {
                ObservableList<PieChart.Data> pieChartData = FXCollections.observableArrayList(
                    new PieChart.Data("Swiggy", (Double) analytics.get("swiggyPercentage")),
                    new PieChart.Data("Zomato", (Double) analytics.get("zomatoPercentage"))
                );
                platformChart.setData(pieChartData);
            } catch (Exception e) {
                System.err.println("Error updating platform chart: " + e.getMessage());
            }
        }
    }
    
    private void updateRevenueChart() {
        if (revenueChart != null) {
            try {
                XYChart.Series<String, Number> series = new XYChart.Series<>();
                series.setName("Daily Revenue");
                
                LocalDate today = LocalDate.now();
                for (int i = 6; i >= 0; i--) {
                    LocalDate date = today.minusDays(i);
                    DailyReport report = ReportService.generateDailyReport(date);
                    series.getData().add(new XYChart.Data<>(
                        date.format(DateTimeFormatter.ofPattern("MM/dd")), 
                        report.getTotalRevenue()
                    ));
                }
                
                revenueChart.getData().clear();
                revenueChart.getData().add(series);
            } catch (Exception e) {
                System.err.println("Error updating revenue chart: " + e.getMessage());
            }
        }
    }
    
    private void navigateToHome() {
        // Implementation for navigation to home
        System.out.println("Navigating to home...");
    }
    
    private void navigateToNotifications() {
        // Implementation for navigation to notifications
        System.out.println("Navigating to notifications...");
    }
    
    private void showAlert(String title, String message, Alert.AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
