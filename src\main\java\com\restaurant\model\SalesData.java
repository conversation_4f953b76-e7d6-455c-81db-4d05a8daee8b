package com.restaurant.model;

import java.time.LocalDate;

/**
 * Model class representing sales data for a specific date
 */
public class SalesData {
    private LocalDate date;
    private double sales;
    private String category;
    private String channel;
    private String location;
    private int orderCount;
    private double averageOrderValue;

    // Constructors
    public SalesData() {}

    public SalesData(LocalDate date, double sales) {
        this.date = date;
        this.sales = sales;
    }

    public SalesData(LocalDate date, double sales, String category, String channel, String location) {
        this.date = date;
        this.sales = sales;
        this.category = category;
        this.channel = channel;
        this.location = location;
    }

    // Getters and Setters
    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public double getSales() {
        return sales;
    }

    public void setSales(double sales) {
        this.sales = sales;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public int getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(int orderCount) {
        this.orderCount = orderCount;
    }

    public double getAverageOrderValue() {
        return averageOrderValue;
    }

    public void setAverageOrderValue(double averageOrderValue) {
        this.averageOrderValue = averageOrderValue;
    }

    @Override
    public String toString() {
        return "SalesData{" +
                "date=" + date +
                ", sales=" + sales +
                ", category='" + category + '\'' +
                ", channel='" + channel + '\'' +
                ", location='" + location + '\'' +
                ", orderCount=" + orderCount +
                ", averageOrderValue=" + averageOrderValue +
                '}';
    }
}
