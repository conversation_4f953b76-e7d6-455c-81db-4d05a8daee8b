package com.restaurant.model;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.sql.*;

/**
 * Business Configuration Manager
 * Handles day-end time, business hours, and operational settings
 */
public class BusinessConfig {
    
    // Default configuration values
    private static final LocalTime DEFAULT_DAY_END_TIME = LocalTime.of(2, 0); // 2:00 AM
    private static final LocalTime DEFAULT_BUSINESS_START = LocalTime.of(18, 0); // 6:00 PM
    private static final LocalTime DEFAULT_BUSINESS_END = LocalTime.of(2, 0); // 2:00 AM next day
    private static final int DEFAULT_KOT_HOLD_WARNING_MINUTES = 15;
    
    // Configuration keys
    public static final String DAY_END_TIME = "day_end_time";
    public static final String BUSINESS_START_TIME = "business_start_time";
    public static final String BUSINESS_END_TIME = "business_end_time";
    public static final String HOLD_KOT_BEFORE_BILLING = "hold_kot_before_billing";
    public static final String RESTRICT_BILLING_WITHOUT_KOT = "restrict_billing_without_kot";
    public static final String KOT_HOLD_WARNING_MINUTES = "kot_hold_warning_minutes";
    public static final String CUSTOM_BILL_FOOTER = "custom_bill_footer";
    public static final String ENABLE_KOT_NOTIFICATIONS = "enable_kot_notifications";
    
    /**
     * Get the day-end time (default: 2:00 AM)
     */
    public static LocalTime getDayEndTime() {
        String timeStr = getConfigValue(DAY_END_TIME);
        if (timeStr != null) {
            try {
                return LocalTime.parse(timeStr);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return DEFAULT_DAY_END_TIME;
    }
    
    /**
     * Set the day-end time
     */
    public static void setDayEndTime(LocalTime time) {
        setConfigValue(DAY_END_TIME, time.toString());
    }
    
    /**
     * Get business day for a given timestamp
     * Orders between midnight and day-end time belong to previous day
     */
    public static LocalDate getBusinessDay(LocalDateTime timestamp) {
        LocalTime dayEndTime = getDayEndTime();
        LocalTime orderTime = timestamp.toLocalTime();
        LocalDate orderDate = timestamp.toLocalDate();
        
        // If order time is between midnight and day-end time, it belongs to previous day
        if (orderTime.isBefore(dayEndTime)) {
            return orderDate.minusDays(1);
        }
        
        return orderDate;
    }
    
    /**
     * Get business start time
     */
    public static LocalTime getBusinessStartTime() {
        String timeStr = getConfigValue(BUSINESS_START_TIME);
        if (timeStr != null) {
            try {
                return LocalTime.parse(timeStr);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return DEFAULT_BUSINESS_START;
    }
    
    /**
     * Get business end time
     */
    public static LocalTime getBusinessEndTime() {
        String timeStr = getConfigValue(BUSINESS_END_TIME);
        if (timeStr != null) {
            try {
                return LocalTime.parse(timeStr);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return DEFAULT_BUSINESS_END;
    }
    
    /**
     * Check if KOT should be held before billing
     */
    public static boolean shouldHoldKOTBeforeBilling() {
        return getBooleanConfig(HOLD_KOT_BEFORE_BILLING, false);
    }
    
    /**
     * Check if billing should be restricted without KOT
     */
    public static boolean shouldRestrictBillingWithoutKOT() {
        return getBooleanConfig(RESTRICT_BILLING_WITHOUT_KOT, true);
    }
    
    /**
     * Get KOT hold warning time in minutes
     */
    public static int getKOTHoldWarningMinutes() {
        String value = getConfigValue(KOT_HOLD_WARNING_MINUTES);
        if (value != null) {
            try {
                return Integer.parseInt(value);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return DEFAULT_KOT_HOLD_WARNING_MINUTES;
    }
    
    /**
     * Get custom bill footer text
     */
    public static String getCustomBillFooter() {
        return getConfigValue(CUSTOM_BILL_FOOTER);
    }
    
    /**
     * Check if KOT notifications are enabled
     */
    public static boolean areKOTNotificationsEnabled() {
        return getBooleanConfig(ENABLE_KOT_NOTIFICATIONS, true);
    }
    
    /**
     * Set business configuration value
     */
    public static void setConfigValue(String key, String value) {
        try (Connection conn = DatabaseManager.getConnection()) {
            // First try to update existing config
            PreparedStatement updatePs = conn.prepareStatement(
                "UPDATE business_config SET config_value = ?, updated_at = CURRENT_TIMESTAMP WHERE config_key = ?");
            updatePs.setString(1, value);
            updatePs.setString(2, key);
            
            int rowsUpdated = updatePs.executeUpdate();
            
            // If no rows updated, insert new config
            if (rowsUpdated == 0) {
                PreparedStatement insertPs = conn.prepareStatement(
                    "INSERT INTO business_config (config_key, config_value, created_at, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)");
                insertPs.setString(1, key);
                insertPs.setString(2, value);
                insertPs.executeUpdate();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
    
    /**
     * Get business configuration value
     */
    public static String getConfigValue(String key) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                 "SELECT config_value FROM business_config WHERE config_key = ?")) {
            
            ps.setString(1, key);
            ResultSet rs = ps.executeQuery();
            
            if (rs.next()) {
                return rs.getString("config_value");
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * Get boolean configuration value
     */
    public static boolean getBooleanConfig(String key, boolean defaultValue) {
        String value = getConfigValue(key);
        if (value != null) {
            return Boolean.parseBoolean(value);
        }
        return defaultValue;
    }
    
    /**
     * Set boolean configuration value
     */
    public static void setBooleanConfig(String key, boolean value) {
        setConfigValue(key, String.valueOf(value));
    }
    
    /**
     * Initialize default business configuration
     */
    public static void initializeDefaultConfig() {
        // Set default values if not already configured
        if (getConfigValue(DAY_END_TIME) == null) {
            setDayEndTime(DEFAULT_DAY_END_TIME);
        }
        if (getConfigValue(BUSINESS_START_TIME) == null) {
            setConfigValue(BUSINESS_START_TIME, DEFAULT_BUSINESS_START.toString());
        }
        if (getConfigValue(BUSINESS_END_TIME) == null) {
            setConfigValue(BUSINESS_END_TIME, DEFAULT_BUSINESS_END.toString());
        }
        if (getConfigValue(KOT_HOLD_WARNING_MINUTES) == null) {
            setConfigValue(KOT_HOLD_WARNING_MINUTES, String.valueOf(DEFAULT_KOT_HOLD_WARNING_MINUTES));
        }
        if (getConfigValue(CUSTOM_BILL_FOOTER) == null) {
            setConfigValue(CUSTOM_BILL_FOOTER, "Thank you for dining with us! Visit again soon.");
        }
        if (getConfigValue(ENABLE_KOT_NOTIFICATIONS) == null) {
            setBooleanConfig(ENABLE_KOT_NOTIFICATIONS, true);
        }
        if (getConfigValue(RESTRICT_BILLING_WITHOUT_KOT) == null) {
            setBooleanConfig(RESTRICT_BILLING_WITHOUT_KOT, true);
        }
    }
}
