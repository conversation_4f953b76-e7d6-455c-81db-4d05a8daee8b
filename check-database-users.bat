@echo off
echo 🔍 CHECKING DATABASE USERS...
echo.

echo This will show you what users exist in the database
echo and test the authentication system.
echo.

echo STEP 1: Compiling the project...
mvn compile -q

if %ERRORLEVEL% neq 0 (
    echo ❌ Compilation failed. Please check for errors.
    pause
    exit /b 1
)

echo ✅ Compilation successful
echo.

echo STEP 2: Creating database checker utility...

echo package com.restaurant.util; > src\main\java\com\restaurant\util\DatabaseChecker.java
echo. >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo import com.restaurant.model.UserDAO; >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo import com.restaurant.model.User; >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo import java.sql.*; >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo. >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo public class DatabaseChecker { >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo     public static void main(String[] args) { >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo         System.out.println("🔍 Checking database users..."); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo         System.out.println(); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo. >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo         try { >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo             // Check if database file exists >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo             java.io.File dbFile = new java.io.File("restaurant.db"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo             if (!dbFile.exists()) { >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 System.out.println("❌ Database file does not exist: restaurant.db"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 System.out.println("   Run fix-admin-login-issue.bat to create it"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 return; >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo             } >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo. >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo             System.out.println("✅ Database file exists: " + dbFile.getAbsolutePath()); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo             System.out.println("   Size: " + dbFile.length() + " bytes"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo             System.out.println(); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo. >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo             // Connect to database and list users >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo             String url = "*************************"; >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo             try (Connection conn = DriverManager.getConnection(url)) { >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 System.out.println("✅ Database connection successful"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo. >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 // Check if users table exists >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 DatabaseMetaData meta = conn.getMetaData(); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 ResultSet tables = meta.getTables(null, null, "users", null); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 if (!tables.next()) { >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     System.out.println("❌ Users table does not exist"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     System.out.println("   Run fix-admin-login-issue.bat to create it"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     return; >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 } >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 tables.close(); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo. >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 System.out.println("✅ Users table exists"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 System.out.println(); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo. >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 // List all users >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 String sql = "SELECT id, username, role FROM users"; >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 try (Statement stmt = conn.createStatement(); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                      ResultSet rs = stmt.executeQuery(sql)) { >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo. >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     System.out.println("📋 USERS IN DATABASE:"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     System.out.println("ID | Username | Role"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     System.out.println("---|----------|-----"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo. >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     boolean hasUsers = false; >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     while (rs.next()) { >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                         hasUsers = true; >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                         System.out.printf("%2d | %-8s | %s%n", >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                             rs.getInt("id"), >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                             rs.getString("username"), >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                             rs.getString("role")); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     } >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo. >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     if (!hasUsers) { >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                         System.out.println("❌ No users found in database"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                         System.out.println("   Run fix-admin-login-issue.bat to create admin user"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     } >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 } >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo. >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 System.out.println(); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo. >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 // Test admin authentication >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 System.out.println("🧪 TESTING ADMIN AUTHENTICATION:"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 User admin = UserDAO.authenticate("admin", "admin123"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 if (admin != null) { >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     System.out.println("✅ Admin authentication SUCCESSFUL"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     System.out.println("   Username: " + admin.getUsername()); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     System.out.println("   Role: " + admin.getRole()); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     System.out.println("   ID: " + admin.getId()); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 } else { >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     System.out.println("❌ Admin authentication FAILED"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     System.out.println("   Username 'admin' with password 'admin123' not found"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 } >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo. >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 // Test staff authentication >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 System.out.println(); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 System.out.println("🧪 TESTING STAFF AUTHENTICATION:"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 User staff = UserDAO.authenticate("staff", "staff123"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 if (staff != null) { >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     System.out.println("✅ Staff authentication SUCCESSFUL"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     System.out.println("   Username: " + staff.getUsername()); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     System.out.println("   Role: " + staff.getRole()); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     System.out.println("   ID: " + staff.getId()); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 } else { >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     System.out.println("❌ Staff authentication FAILED"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                     System.out.println("   Username 'staff' with password 'staff123' not found"); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo                 } >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo. >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo             } >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo. >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo             System.err.println("❌ Error checking database: " + e.getMessage()); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo             e.printStackTrace(); >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo         } >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo     } >> src\main\java\com\restaurant\util\DatabaseChecker.java
echo } >> src\main\java\com\restaurant\util\DatabaseChecker.java

echo ✅ DatabaseChecker class created
echo.

echo STEP 3: Recompiling with new checker...
mvn compile -q

echo STEP 4: Running database check...
echo.

java -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar" com.restaurant.util.DatabaseChecker

echo.
echo 📋 SUMMARY:
echo.
echo If you see "Admin authentication SUCCESSFUL" above, then:
echo ✅ Database is working correctly
echo ✅ Admin user exists with correct password
echo ✅ Login should work with: admin/admin123/ADMIN
echo.
echo If you see "Admin authentication FAILED" above, then:
echo ❌ Admin user doesn't exist or password is wrong
echo 🔧 Run: fix-admin-login-issue.bat to fix it
echo.
echo CORRECT LOGIN PROCESS:
echo 1. Username: admin (lowercase)
echo 2. Password: admin123 (exactly as shown)
echo 3. Role: ADMIN (select from dropdown)
echo 4. Click "Login to Dashboard →"
echo.

pause
