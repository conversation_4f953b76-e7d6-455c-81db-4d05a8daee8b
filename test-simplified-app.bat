@echo off
echo TESTING SIMPLIFIED RESTAURANT APP...
echo.

echo This simplified version removes all complex components:
echo ✅ No FXML loading
echo ✅ No CSS loading
echo ✅ No database initialization
echo ✅ No notification system
echo ✅ Simple JavaFX UI only
echo.

echo Starting simplified RestaurantApp...
echo.

java -Dprism.order=sw ^
     -Dprism.allowhidpi=false ^
     -Djavafx.animation.pulse=60 ^
     -Djava.awt.headless=false ^
     -Xms256m ^
     -Xmx1g ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics ^
     -cp "target/classes" ^
     com.restaurant.RestaurantApp

echo.
echo SIMPLIFIED APP TEST RESULTS:
echo.

if %ERRORLEVEL% equ 0 (
    echo ✅ SIMPLIFIED APP WORKED!
    echo.
    echo CONCLUSION: The issue is with complex components
    echo.
    echo PROBLEMATIC COMPONENTS IDENTIFIED:
    echo - FXML loading system
    echo - CSS parsing and loading
    echo - Database initialization
    echo - Notification system timers
    echo - Complex UI components
    echo.
    echo NEXT STEPS:
    echo 1. Gradually add back components one by one
    echo 2. Test after each addition to find the exact culprit
    echo 3. Fix or simplify the problematic component
    echo.
) else (
    echo ❌ SIMPLIFIED APP STILL FAILED!
    echo.
    echo CONCLUSION: Fundamental JavaFX issue
    echo.
    echo This means the problem is deeper than application components.
    echo Possible issues:
    echo 1. JavaFX runtime environment
    echo 2. Graphics drivers
    echo 3. System permissions
    echo 4. Java version compatibility
    echo.
)

echo.
pause
