package com.restaurant.model;

import java.time.LocalDateTime;

public class Supplier {
    private int id;
    private String name;
    private String contactPerson;
    private String phone;
    private String email;
    private String address;
    private String category; // "Vegetables", "Meat", "Dairy", "Grains", etc.
    private String status; // "Active", "Inactive"
    private LocalDateTime createdAt;
    private LocalDateTime lastUpdated;
    private String notes;
    
    // Constructors
    public Supplier() {
        this.createdAt = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
        this.status = "Active";
    }
    
    public Supplier(int id, String name, String contactPerson, String phone, String email, String address) {
        this.id = id;
        this.name = name;
        this.contactPerson = contactPerson;
        this.phone = phone;
        this.email = email;
        this.address = address;
        this.createdAt = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
        this.status = "Active";
    }
    
    public Supplier(String name, String contactPerson, String phone, String email, String address, String category) {
        this.name = name;
        this.contactPerson = contactPerson;
        this.phone = phone;
        this.email = email;
        this.address = address;
        this.category = category;
        this.createdAt = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
        this.status = "Active";
    }
    
    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { 
        this.name = name;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public String getContactPerson() { return contactPerson; }
    public void setContactPerson(String contactPerson) { 
        this.contactPerson = contactPerson;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { 
        this.phone = phone;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { 
        this.email = email;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public String getAddress() { return address; }
    public void setAddress(String address) { 
        this.address = address;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public String getCategory() { return category; }
    public void setCategory(String category) { 
        this.category = category;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { 
        this.status = status;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getLastUpdated() { return lastUpdated; }
    public void setLastUpdated(LocalDateTime lastUpdated) { this.lastUpdated = lastUpdated; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { 
        this.notes = notes;
        this.lastUpdated = LocalDateTime.now();
    }
    
    // Helper methods
    public String getDisplayName() {
        return name + (contactPerson != null && !contactPerson.isEmpty() ? " (" + contactPerson + ")" : "");
    }
    
    public String getContactInfo() {
        StringBuilder contact = new StringBuilder();
        if (phone != null && !phone.isEmpty()) {
            contact.append("📞 ").append(phone);
        }
        if (email != null && !email.isEmpty()) {
            if (contact.length() > 0) contact.append(" | ");
            contact.append("📧 ").append(email);
        }
        return contact.toString();
    }
    
    public boolean isActive() {
        return "Active".equals(status);
    }
    
    public String getStatusIcon() {
        return isActive() ? "🟢" : "🔴";
    }
    
    public String getCategoryIcon() {
        if (category == null) return "📦";
        
        switch (category.toLowerCase()) {
            case "vegetables": return "🥬";
            case "fruits": return "🍎";
            case "meat": return "🥩";
            case "dairy": return "🥛";
            case "grains": return "🌾";
            case "spices": return "🌶️";
            case "beverages": return "🥤";
            case "cleaning": return "🧽";
            default: return "📦";
        }
    }
    
    @Override
    public String toString() {
        return name;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Supplier supplier = (Supplier) obj;
        return id == supplier.id;
    }
    
    @Override
    public int hashCode() {
        return Integer.hashCode(id);
    }
}
