package com.restaurant.util;

import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.Parent;
import javafx.stage.Stage;

/**
 * Launcher for Platform Configuration interface
 * This can be used to test the platform configuration independently
 */
public class PlatformConfigurationLauncher extends Application {

    @Override
    public void start(Stage primaryStage) {
        try {
            // Load the FXML file
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/PlatformConfiguration.fxml"));
            Parent root = loader.load();
            
            // Create scene
            Scene scene = new Scene(root, 900, 700);
            
            // Set up stage
            primaryStage.setTitle("Platform Configuration - Restaurant Management");
            primaryStage.setScene(scene);
            primaryStage.setMinWidth(800);
            primaryStage.setMinHeight(600);
            primaryStage.show();
            
            System.out.println("Platform Configuration interface launched successfully");
            
        } catch (Exception e) {
            System.err.println("Error launching Platform Configuration: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        launch(args);
    }
}
