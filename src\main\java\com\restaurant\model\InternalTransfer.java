package com.restaurant.model;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Model class for Internal Transfers between locations
 */
public class InternalTransfer {
    private int id;
    private String requestNumber;
    private String fromLocation;
    private String toLocation;
    private String item;
    private String quantity;
    private String status; // "Saved", "Processed", "In Transit", "Delivered", "Cancelled"
    private LocalDate startDate;
    private LocalDate endDate;
    private LocalDateTime createdAt;
    private LocalDateTime lastUpdated;
    private String createdBy;
    private String approvedBy;
    private String notes;
    private String transferReason;
    private LocalDateTime transferDate;
    private LocalDateTime deliveryDate;

    // Constructors
    public InternalTransfer() {
        this.createdAt = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
    }

    public InternalTransfer(String requestNumber, String fromLocation, String toLocation, String item, String quantity, String status) {
        this();
        this.requestNumber = requestNumber;
        this.fromLocation = fromLocation;
        this.toLocation = toLocation;
        this.item = item;
        this.quantity = quantity;
        this.status = status;
    }

    public InternalTransfer(int id, String requestNumber, String fromLocation, String toLocation, String item, String quantity, String status) {
        this(requestNumber, fromLocation, toLocation, item, quantity, status);
        this.id = id;
    }

    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public String getRequestNumber() { return requestNumber; }
    public void setRequestNumber(String requestNumber) { this.requestNumber = requestNumber; }

    public String getFromLocation() { return fromLocation; }
    public void setFromLocation(String fromLocation) { this.fromLocation = fromLocation; }

    public String getToLocation() { return toLocation; }
    public void setToLocation(String toLocation) { this.toLocation = toLocation; }

    public String getItem() { return item; }
    public void setItem(String item) { this.item = item; }

    public String getQuantity() { return quantity; }
    public void setQuantity(String quantity) { this.quantity = quantity; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public LocalDate getStartDate() { return startDate; }
    public void setStartDate(LocalDate startDate) { this.startDate = startDate; }

    public LocalDate getEndDate() { return endDate; }
    public void setEndDate(LocalDate endDate) { this.endDate = endDate; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getLastUpdated() { return lastUpdated; }
    public void setLastUpdated(LocalDateTime lastUpdated) { this.lastUpdated = lastUpdated; }

    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }

    public String getApprovedBy() { return approvedBy; }
    public void setApprovedBy(String approvedBy) { this.approvedBy = approvedBy; }

    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }

    public String getTransferReason() { return transferReason; }
    public void setTransferReason(String transferReason) { this.transferReason = transferReason; }

    public LocalDateTime getTransferDate() { return transferDate; }
    public void setTransferDate(LocalDateTime transferDate) { this.transferDate = transferDate; }

    public LocalDateTime getDeliveryDate() { return deliveryDate; }
    public void setDeliveryDate(LocalDateTime deliveryDate) { this.deliveryDate = deliveryDate; }

    // Utility methods
    public String getStatusColor() {
        switch (status.toLowerCase()) {
            case "saved": return "#ffc107"; // Yellow
            case "processed": return "#007bff"; // Blue
            case "in transit": return "#17a2b8"; // Teal
            case "delivered": return "#28a745"; // Green
            case "cancelled": return "#dc3545"; // Red
            default: return "#6c757d"; // Gray
        }
    }

    public boolean isEditable() {
        return "Saved".equalsIgnoreCase(status);
    }

    public boolean isCancellable() {
        return !"Delivered".equalsIgnoreCase(status) && !"Cancelled".equalsIgnoreCase(status);
    }

    public String getTransferDirection() {
        return fromLocation + " → " + toLocation;
    }

    @Override
    public String toString() {
        return "InternalTransfer{" +
                "id=" + id +
                ", requestNumber='" + requestNumber + '\'' +
                ", fromLocation='" + fromLocation + '\'' +
                ", toLocation='" + toLocation + '\'' +
                ", item='" + item + '\'' +
                ", quantity='" + quantity + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
