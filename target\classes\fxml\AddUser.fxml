<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.AddUserController">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="page-header">
         <children>
            <Button mnemonicParsing="false" onAction="#goBack" styleClass="back-button" text="Back">
               <font>
                  <Font size="14.0" />
               </font>
            </Button>
            <Label styleClass="page-title" text="Add New User">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
         </children>
         <padding>
            <Insets bottom="20.0" left="30.0" right="30.0" top="20.0" />
         </padding>
      </HBox>

      <!-- Main Content Area -->
      <ScrollPane fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED" VBox.vgrow="ALWAYS">
         <content>
            <VBox spacing="30.0" styleClass="add-user-container">
               <children>
                  <!-- User Information Card -->
                  <VBox spacing="25.0" styleClass="form-card">
                     <children>
                        <Label styleClass="section-title" text="User Information">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>

                        <!-- Username Field -->
                        <VBox spacing="8.0">
                           <children>
                              <Label styleClass="field-label" text="Username *" />
                              <TextField fx:id="usernameField" promptText="Enter username" styleClass="input-field" />
                           </children>
                        </VBox>

                        <!-- Full Name Field -->
                        <VBox spacing="8.0">
                           <children>
                              <Label styleClass="field-label" text="Full Name *" />
                              <TextField fx:id="fullNameField" promptText="Enter full name" styleClass="input-field" />
                           </children>
                        </VBox>

                        <!-- Email Field -->
                        <VBox spacing="8.0">
                           <children>
                              <Label styleClass="field-label" text="Email Address" />
                              <TextField fx:id="emailField" promptText="Enter email address" styleClass="input-field" />
                           </children>
                        </VBox>

                        <!-- Phone Field -->
                        <VBox spacing="8.0">
                           <children>
                              <Label styleClass="field-label" text="Phone Number" />
                              <TextField fx:id="phoneField" promptText="Enter phone number" styleClass="input-field" />
                           </children>
                        </VBox>

                        <!-- Role Field -->
                        <VBox spacing="8.0">
                           <children>
                              <Label styleClass="field-label" text="Role *" />
                              <ComboBox fx:id="roleCombo" promptText="Select user role" styleClass="input-field" maxWidth="Infinity" />
                           </children>
                        </VBox>

                        <!-- Status Field -->
                        <VBox spacing="8.0">
                           <children>
                              <Label styleClass="field-label" text="Status" />
                              <ComboBox fx:id="statusCombo" styleClass="input-field" maxWidth="Infinity" />
                           </children>
                        </VBox>

                        <!-- Password Field -->
                        <VBox spacing="8.0">
                           <children>
                              <Label styleClass="field-label" text="Password *" />
                              <PasswordField fx:id="passwordField" promptText="Enter password" styleClass="input-field" />
                           </children>
                        </VBox>

                        <!-- Confirm Password Field -->
                        <VBox spacing="8.0">
                           <children>
                              <Label styleClass="field-label" text="Confirm Password *" />
                              <PasswordField fx:id="confirmPasswordField" promptText="Confirm password" styleClass="input-field" />
                           </children>
                        </VBox>
                     </children>
                  </VBox>

                  <!-- Action Buttons -->
                  <HBox alignment="CENTER_RIGHT" spacing="15.0" styleClass="action-buttons">
                     <children>
                        <Button mnemonicParsing="false" onAction="#goBack" styleClass="secondary-button" text="Cancel">
                           <font>
                              <Font size="14.0" />
                           </font>
                        </Button>
                        <Button mnemonicParsing="false" onAction="#saveUser" styleClass="primary-button" text="Create User">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Button>
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="40.0" left="30.0" right="30.0" top="20.0" />
               </padding>
            </VBox>
         </content>
      </ScrollPane>
   </children>
</VBox>
