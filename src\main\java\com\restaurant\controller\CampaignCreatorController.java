package com.restaurant.controller;

import com.restaurant.model.Customer;
import com.restaurant.model.MarketingCampaign;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.*;

import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Controller for Campaign Creator
 */
public class CampaignCreatorController implements Initializable {

    // FXML Controls
    @FXML private Button backButton;
    @FXML private Button scheduleBtn;
    @FXML private Button onBillPrintBtn;
    @FXML private Button birthdayBtn;
    @FXML private Button anniversaryBtn;
    @FXML private Label messageTitle;
    @FXML private Label messageBody;
    @FXML private Label messageSender;
    @FXML private TextField campaignNameField;
    @FXML private TextField discountField;
    @FXML private TextField validDaysField;
    @FXML private ComboBox<String> messageTypeCombo;
    @FXML private TextField customerSearchField;
    @FXML private ComboBox<String> segmentFilterCombo;
    @FXML private Button selectAllBtn;
    @FXML private Button clearAllBtn;
    @FXML private CheckBox selectAllCheckbox;
    @FXML private VBox customerListContainer;
    @FXML private Label selectedCountLabel;
    @FXML private Button previewBtn;
    @FXML private Button saveDraftBtn;
    @FXML private Button sendCampaignBtn;

    // Data
    private List<Customer> customers;
    private List<Customer> selectedCustomers;
    private String selectedCampaignType = "Birthday";
    private Map<String, String> campaignTemplates;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        initializeData();
        setupControls();
        setupEventHandlers();
        selectBirthday(); // Default selection
        displayCustomers();
        updateSelectedCount();
    }

    private void initializeData() {
        // Initialize customers with birthday data
        customers = new ArrayList<>();
        
        Customer customer1 = new Customer(1, "Arpit Shah", "+91 98765 43210", "<EMAIL>");
        customer1.setAddress("123 MG Road, Bangalore");
        customer1.addOrder(1200.0);
        customer1.addOrder(850.0);
        // Set birthday (for demo, using random dates)
        customer1.setNotes("Birthday: 15-03-1990");
        customers.add(customer1);

        Customer customer2 = new Customer(2, "Harpreet Singh", "+91 98765 43211", "<EMAIL>");
        customer2.setAddress("456 Brigade Road, Bangalore");
        customer2.addOrder(2200.0);
        customer2.addOrder(1800.0);
        customer2.setNotes("Birthday: 22-07-1985");
        customers.add(customer2);

        Customer customer3 = new Customer(3, "Priya Sharma", "+91 98765 43212", "<EMAIL>");
        customer3.setAddress("789 Commercial Street, Bangalore");
        customer3.addOrder(950.0);
        customer3.setNotes("Birthday: 08-12-1992");
        customers.add(customer3);

        Customer customer4 = new Customer(4, "Rajesh Kumar", "+91 98765 43213", "<EMAIL>");
        customer4.setAddress("321 Koramangala, Bangalore");
        customer4.addOrder(1500.0);
        customer4.setNotes("Birthday: 30-09-1988");
        customers.add(customer4);

        Customer customer5 = new Customer(5, "Sneha Reddy", "+91 98765 43214", "<EMAIL>");
        customer5.setAddress("654 Indiranagar, Bangalore");
        customer5.addOrder(3200.0);
        customer5.addOrder(2800.0);
        customer5.setNotes("Birthday: 14-05-1991");
        customers.add(customer5);

        selectedCustomers = new ArrayList<>();

        // Initialize campaign templates
        campaignTemplates = new HashMap<>();
        campaignTemplates.put("Birthday", "Wishing you a very Happy Birthday\n\nVisit any of the CDI outlets and show this message to enjoy {discount}% off on your bill");
        campaignTemplates.put("Anniversary", "Happy Anniversary!\n\nCelebrate your special day with us. Enjoy {discount}% off on your bill");
        campaignTemplates.put("Schedule", "Special Offer Just for You!\n\nDon't miss out on our exclusive {discount}% discount");
        campaignTemplates.put("On Bill Print", "Thank you for dining with us!\n\nShow this message on your next visit for {discount}% off");
    }

    private void setupControls() {
        // Setup message type combo
        messageTypeCombo.getItems().addAll("SMS", "WhatsApp", "Email", "Push Notification");
        messageTypeCombo.setValue("SMS");

        // Setup segment filter
        segmentFilterCombo.getItems().addAll("All", "New Customer", "Regular", "High Spender", "Lapsed Customer");
        segmentFilterCombo.setValue("All");

        // Set default values
        campaignNameField.setText("Birthday Special Campaign");
        discountField.setText("30");
        validDaysField.setText("7");
    }

    private void setupEventHandlers() {
        // Search functionality
        customerSearchField.textProperty().addListener((obs, oldVal, newVal) -> {
            displayCustomers();
        });

        // Segment filter
        segmentFilterCombo.setOnAction(e -> displayCustomers());

        // Discount field listener to update message
        discountField.textProperty().addListener((obs, oldVal, newVal) -> {
            updateMessagePreview();
        });

        // Campaign name field listener
        campaignNameField.textProperty().addListener((obs, oldVal, newVal) -> {
            updateMessagePreview();
        });
    }

    // Campaign Type Selection Methods
    @FXML
    private void selectSchedule() {
        selectCampaignType("Schedule", scheduleBtn);
    }

    @FXML
    private void selectOnBillPrint() {
        selectCampaignType("On Bill Print", onBillPrintBtn);
    }

    @FXML
    private void selectBirthday() {
        selectCampaignType("Birthday", birthdayBtn);
    }

    @FXML
    private void selectAnniversary() {
        selectCampaignType("Anniversary", anniversaryBtn);
    }

    private void selectCampaignType(String type, Button selectedButton) {
        selectedCampaignType = type;
        
        // Update button styles
        scheduleBtn.getStyleClass().remove("selected");
        onBillPrintBtn.getStyleClass().remove("selected");
        birthdayBtn.getStyleClass().remove("selected");
        anniversaryBtn.getStyleClass().remove("selected");
        
        selectedButton.getStyleClass().add("selected");
        
        // Update campaign name
        campaignNameField.setText(type + " Special Campaign");
        
        // Update message preview
        updateMessagePreview();
    }

    private void updateMessagePreview() {
        String template = campaignTemplates.get(selectedCampaignType);
        String discount = discountField.getText().isEmpty() ? "30" : discountField.getText();
        
        if (template != null) {
            String message = template.replace("{discount}", discount);
            String[] parts = message.split("\n\n", 2);
            
            messageTitle.setText(parts[0]);
            if (parts.length > 1) {
                messageBody.setText(parts[1]);
            }
        }
        
        messageSender.setText("Sent by " + (campaignNameField.getText().isEmpty() ? "RESTAURANT" : "RESTAURANT"));
    }

    private void displayCustomers() {
        customerListContainer.getChildren().clear();
        
        String searchText = customerSearchField.getText().toLowerCase();
        String segmentFilter = segmentFilterCombo.getValue();
        
        List<Customer> filteredCustomers = customers.stream()
            .filter(customer -> {
                // Search filter
                boolean matchesSearch = searchText.isEmpty() || 
                    customer.getName().toLowerCase().contains(searchText) ||
                    customer.getPhone().contains(searchText);
                
                // Segment filter
                boolean matchesSegment = "All".equals(segmentFilter) || 
                    customer.getSegment().equals(segmentFilter);
                
                return matchesSearch && matchesSegment;
            })
            .collect(Collectors.toList());

        for (Customer customer : filteredCustomers) {
            HBox customerRow = createCustomerRow(customer);
            customerListContainer.getChildren().add(customerRow);
        }
    }

    private HBox createCustomerRow(Customer customer) {
        HBox row = new HBox();
        row.setAlignment(Pos.CENTER_LEFT);
        row.getStyleClass().add("customer-selection-row");
        row.setPadding(new Insets(12, 15, 12, 15));

        // Checkbox
        CheckBox checkbox = new CheckBox();
        checkbox.getStyleClass().add("customer-checkbox");
        checkbox.setSelected(selectedCustomers.contains(customer));
        checkbox.setOnAction(e -> {
            if (checkbox.isSelected()) {
                if (!selectedCustomers.contains(customer)) {
                    selectedCustomers.add(customer);
                }
            } else {
                selectedCustomers.remove(customer);
            }
            updateSelectedCount();
            updateSelectAllCheckbox();
        });

        // Phone
        Label phoneLabel = new Label(customer.getPhone());
        phoneLabel.getStyleClass().add("customer-phone-label");
        phoneLabel.setPrefWidth(150);

        // Name
        Label nameLabel = new Label(customer.getName());
        nameLabel.getStyleClass().add("customer-name-label");
        nameLabel.setPrefWidth(150);

        // Segment
        Label segmentLabel = new Label(customer.getSegment());
        segmentLabel.getStyleClass().addAll("segment-badge-small", customer.getSegment().toLowerCase().replace(" ", "-"));
        segmentLabel.setPrefWidth(120);

        // Birthday (extracted from notes for demo)
        String birthday = extractBirthday(customer.getNotes());
        Label birthdayLabel = new Label(birthday);
        birthdayLabel.getStyleClass().add("customer-birthday-label");
        birthdayLabel.setPrefWidth(100);

        row.getChildren().addAll(checkbox, phoneLabel, nameLabel, segmentLabel, birthdayLabel);

        return row;
    }

    private String extractBirthday(String notes) {
        if (notes != null && notes.contains("Birthday:")) {
            String[] parts = notes.split("Birthday:");
            if (parts.length > 1) {
                return parts[1].trim();
            }
        }
        return "Not set";
    }

    @FXML
    private void selectAllCustomers() {
        selectedCustomers.clear();
        selectedCustomers.addAll(customers);
        selectAllCheckbox.setSelected(true);
        displayCustomers();
        updateSelectedCount();
    }

    @FXML
    private void clearAllCustomers() {
        selectedCustomers.clear();
        selectAllCheckbox.setSelected(false);
        displayCustomers();
        updateSelectedCount();
    }

    @FXML
    private void toggleSelectAll() {
        if (selectAllCheckbox.isSelected()) {
            selectAllCustomers();
        } else {
            clearAllCustomers();
        }
    }

    private void updateSelectedCount() {
        int count = selectedCustomers.size();
        selectedCountLabel.setText(count + " customer" + (count != 1 ? "s" : "") + " selected");
    }

    private void updateSelectAllCheckbox() {
        selectAllCheckbox.setSelected(selectedCustomers.size() == customers.size());
    }

    @FXML
    private void previewCampaign() {
        if (selectedCustomers.isEmpty()) {
            showAlert("No Recipients", "Please select at least one customer to preview the campaign.");
            return;
        }

        StringBuilder preview = new StringBuilder();
        preview.append("Campaign Preview:\n\n");
        preview.append("Type: ").append(selectedCampaignType).append("\n");
        preview.append("Name: ").append(campaignNameField.getText()).append("\n");
        preview.append("Message Type: ").append(messageTypeCombo.getValue()).append("\n");
        preview.append("Discount: ").append(discountField.getText()).append("%\n");
        preview.append("Valid Days: ").append(validDaysField.getText()).append("\n");
        preview.append("Recipients: ").append(selectedCustomers.size()).append(" customers\n\n");

        preview.append("Message:\n");
        preview.append(messageTitle.getText()).append("\n\n");
        preview.append(messageBody.getText()).append("\n\n");
        preview.append(messageSender.getText()).append("\n\n");

        preview.append("Recipients:\n");
        for (Customer customer : selectedCustomers.stream().limit(5).collect(Collectors.toList())) {
            preview.append("• ").append(customer.getName()).append(" (").append(customer.getPhone()).append(")\n");
        }
        if (selectedCustomers.size() > 5) {
            preview.append("... and ").append(selectedCustomers.size() - 5).append(" more");
        }

        showAlert("Campaign Preview", preview.toString());
    }

    @FXML
    private void saveDraft() {
        if (validateCampaign()) {
            MarketingCampaign campaign = createCampaignFromForm();
            campaign.setStatus("Draft");

            showAlert("Draft Saved", "Campaign draft has been saved successfully!\n\n" +
                "Campaign: " + campaign.getName() + "\n" +
                "Type: " + selectedCampaignType + "\n" +
                "Recipients: " + selectedCustomers.size() + " customers");
        }
    }

    @FXML
    private void sendCampaign() {
        if (validateCampaign()) {
            if (selectedCustomers.isEmpty()) {
                showAlert("No Recipients", "Please select at least one customer to send the campaign.");
                return;
            }

            MarketingCampaign campaign = createCampaignFromForm();
            campaign.setStatus("Active");
            campaign.setTargetCustomers(selectedCustomers.size());
            campaign.setSentCount(selectedCustomers.size());
            campaign.setDeliveredCount((int) (selectedCustomers.size() * 0.95)); // 95% delivery rate

            StringBuilder result = new StringBuilder();
            result.append("Campaign sent successfully!\n\n");
            result.append("Campaign: ").append(campaign.getName()).append("\n");
            result.append("Type: ").append(selectedCampaignType).append("\n");
            result.append("Message Type: ").append(messageTypeCombo.getValue()).append("\n");
            result.append("Recipients: ").append(selectedCustomers.size()).append(" customers\n");
            result.append("Delivery Rate: 95%\n\n");
            result.append("The campaign has been sent to all selected customers!");

            showAlert("Campaign Sent", result.toString());

            // Reset form after sending
            resetForm();
        }
    }

    private boolean validateCampaign() {
        if (campaignNameField.getText().trim().isEmpty()) {
            showAlert("Validation Error", "Please enter a campaign name.");
            return false;
        }

        if (discountField.getText().trim().isEmpty()) {
            showAlert("Validation Error", "Please enter a discount percentage.");
            return false;
        }

        try {
            double discount = Double.parseDouble(discountField.getText().trim());
            if (discount < 0 || discount > 100) {
                showAlert("Validation Error", "Discount must be between 0 and 100.");
                return false;
            }
        } catch (NumberFormatException e) {
            showAlert("Validation Error", "Please enter a valid discount percentage.");
            return false;
        }

        if (messageTypeCombo.getValue() == null) {
            showAlert("Validation Error", "Please select a message type.");
            return false;
        }

        return true;
    }

    private MarketingCampaign createCampaignFromForm() {
        MarketingCampaign campaign = new MarketingCampaign(
            campaignNameField.getText().trim(),
            messageTypeCombo.getValue(),
            "Custom",
            messageTitle.getText() + "\n\n" + messageBody.getText()
        );

        campaign.setDiscountPercentage(Double.parseDouble(discountField.getText().trim()));
        campaign.setStartDate(LocalDateTime.now());
        campaign.setEndDate(LocalDateTime.now().plusDays(Integer.parseInt(validDaysField.getText().trim())));
        campaign.setCreatedBy("Admin");

        return campaign;
    }

    private void resetForm() {
        campaignNameField.setText(selectedCampaignType + " Special Campaign");
        discountField.setText("30");
        validDaysField.setText("7");
        messageTypeCombo.setValue("SMS");
        selectedCustomers.clear();
        selectAllCheckbox.setSelected(false);
        displayCustomers();
        updateSelectedCount();
    }

    @FXML
    private void goBack() {
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/CustomerCRM.fxml"));
            javafx.scene.Parent root = loader.load();

            javafx.stage.Stage stage = (javafx.stage.Stage) backButton.getScene().getWindow();
            stage.getScene().setRoot(root);

            System.out.println("Navigated back to Customer CRM from Campaign Creator");

        } catch (Exception e) {
            System.err.println("Error navigating back to Customer CRM: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
