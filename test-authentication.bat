@echo off
echo 🔍 TESTING AUTHENTICATION DIRECTLY...
echo.

echo This will test the exact authentication process the application uses.
echo.

echo STEP 1: Creating authentication tester...

echo package com.restaurant.util; > src\main\java\com\restaurant\util\AuthTester.java
echo. >> src\main\java\com\restaurant\util\AuthTester.java
echo import java.sql.*; >> src\main\java\com\restaurant\util\AuthTester.java
echo import org.mindrot.jbcrypt.BCrypt; >> src\main\java\com\restaurant\util\AuthTester.java
echo. >> src\main\java\com\restaurant\util\AuthTester.java
echo public class AuthTester { >> src\main\java\com\restaurant\util\AuthTester.java
echo     public static void main(String[] args) { >> src\main\java\com\restaurant\util\AuthTester.java
echo         System.out.println("🔍 Testing authentication process..."); >> src\main\java\com\restaurant\util\AuthTester.java
echo         System.out.println(); >> src\main\java\com\restaurant\util\AuthTester.java
echo. >> src\main\java\com\restaurant\util\AuthTester.java
echo         // Test credentials >> src\main\java\com\restaurant\util\AuthTester.java
echo         String testUsername = "admin"; >> src\main\java\com\restaurant\util\AuthTester.java
echo         String testPassword = "admin123"; >> src\main\java\com\restaurant\util\AuthTester.java
echo. >> src\main\java\com\restaurant\util\AuthTester.java
echo         try { >> src\main\java\com\restaurant\util\AuthTester.java
echo             Class.forName("org.sqlite.JDBC"); >> src\main\java\com\restaurant\util\AuthTester.java
echo             System.out.println("✅ SQLite driver loaded"); >> src\main\java\com\restaurant\util\AuthTester.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\util\AuthTester.java
echo             System.err.println("❌ Driver error: " + e.getMessage()); >> src\main\java\com\restaurant\util\AuthTester.java
echo             return; >> src\main\java\com\restaurant\util\AuthTester.java
echo         } >> src\main\java\com\restaurant\util\AuthTester.java
echo. >> src\main\java\com\restaurant\util\AuthTester.java
echo         String url = "*************************"; >> src\main\java\com\restaurant\util\AuthTester.java
echo. >> src\main\java\com\restaurant\util\AuthTester.java
echo         try (Connection conn = DriverManager.getConnection(url)) { >> src\main\java\com\restaurant\util\AuthTester.java
echo             System.out.println("✅ Database connected"); >> src\main\java\com\restaurant\util\AuthTester.java
echo. >> src\main\java\com\restaurant\util\AuthTester.java
echo             // First, show all users >> src\main\java\com\restaurant\util\AuthTester.java
echo             System.out.println(); >> src\main\java\com\restaurant\util\AuthTester.java
echo             System.out.println("📋 ALL USERS IN DATABASE:"); >> src\main\java\com\restaurant\util\AuthTester.java
echo             String selectAll = "SELECT id, username, role, password FROM users"; >> src\main\java\com\restaurant\util\AuthTester.java
echo             try (Statement stmt = conn.createStatement(); >> src\main\java\com\restaurant\util\AuthTester.java
echo                  ResultSet rs = stmt.executeQuery(selectAll)) { >> src\main\java\com\restaurant\util\AuthTester.java
echo                 while (rs.next()) { >> src\main\java\com\restaurant\util\AuthTester.java
echo                     System.out.println("ID: " + rs.getInt("id")); >> src\main\java\com\restaurant\util\AuthTester.java
echo                     System.out.println("Username: '" + rs.getString("username") + "'"); >> src\main\java\com\restaurant\util\AuthTester.java
echo                     System.out.println("Role: '" + rs.getString("role") + "'"); >> src\main\java\com\restaurant\util\AuthTester.java
echo                     System.out.println("Password hash: " + rs.getString("password").substring(0, 20) + "..."); >> src\main\java\com\restaurant\util\AuthTester.java
echo                     System.out.println("---"); >> src\main\java\com\restaurant\util\AuthTester.java
echo                 } >> src\main\java\com\restaurant\util\AuthTester.java
echo             } >> src\main\java\com\restaurant\util\AuthTester.java
echo. >> src\main\java\com\restaurant\util\AuthTester.java
echo             // Now test authentication >> src\main\java\com\restaurant\util\AuthTester.java
echo             System.out.println(); >> src\main\java\com\restaurant\util\AuthTester.java
echo             System.out.println("🧪 TESTING AUTHENTICATION:"); >> src\main\java\com\restaurant\util\AuthTester.java
echo             System.out.println("Testing username: '" + testUsername + "'"); >> src\main\java\com\restaurant\util\AuthTester.java
echo             System.out.println("Testing password: '" + testPassword + "'"); >> src\main\java\com\restaurant\util\AuthTester.java
echo             System.out.println(); >> src\main\java\com\restaurant\util\AuthTester.java
echo. >> src\main\java\com\restaurant\util\AuthTester.java
echo             // Step 1: Find user by username >> src\main\java\com\restaurant\util\AuthTester.java
echo             String findUser = "SELECT id, username, password, role FROM users WHERE username = ?"; >> src\main\java\com\restaurant\util\AuthTester.java
echo             try (PreparedStatement pstmt = conn.prepareStatement(findUser)) { >> src\main\java\com\restaurant\util\AuthTester.java
echo                 pstmt.setString(1, testUsername); >> src\main\java\com\restaurant\util\AuthTester.java
echo                 ResultSet rs = pstmt.executeQuery(); >> src\main\java\com\restaurant\util\AuthTester.java
echo. >> src\main\java\com\restaurant\util\AuthTester.java
echo                 if (rs.next()) { >> src\main\java\com\restaurant\util\AuthTester.java
echo                     System.out.println("✅ User found in database"); >> src\main\java\com\restaurant\util\AuthTester.java
echo                     System.out.println("   Database username: '" + rs.getString("username") + "'"); >> src\main\java\com\restaurant\util\AuthTester.java
echo                     System.out.println("   Database role: '" + rs.getString("role") + "'"); >> src\main\java\com\restaurant\util\AuthTester.java
echo. >> src\main\java\com\restaurant\util\AuthTester.java
echo                     // Step 2: Check password >> src\main\java\com\restaurant\util\AuthTester.java
echo                     String storedHash = rs.getString("password"); >> src\main\java\com\restaurant\util\AuthTester.java
echo                     System.out.println("   Stored hash: " + storedHash.substring(0, 20) + "..."); >> src\main\java\com\restaurant\util\AuthTester.java
echo. >> src\main\java\com\restaurant\util\AuthTester.java
echo                     // Test BCrypt password verification >> src\main\java\com\restaurant\util\AuthTester.java
echo                     boolean passwordMatch = BCrypt.checkpw(testPassword, storedHash); >> src\main\java\com\restaurant\util\AuthTester.java
echo                     System.out.println("   Password check result: " + passwordMatch); >> src\main\java\com\restaurant\util\AuthTester.java
echo. >> src\main\java\com\restaurant\util\AuthTester.java
echo                     if (passwordMatch) { >> src\main\java\com\restaurant\util\AuthTester.java
echo                         System.out.println("✅ AUTHENTICATION SUCCESSFUL!"); >> src\main\java\com\restaurant\util\AuthTester.java
echo                         System.out.println("   User ID: " + rs.getInt("id")); >> src\main\java\com\restaurant\util\AuthTester.java
echo                         System.out.println("   Username: " + rs.getString("username")); >> src\main\java\com\restaurant\util\AuthTester.java
echo                         System.out.println("   Role: " + rs.getString("role")); >> src\main\java\com\restaurant\util\AuthTester.java
echo                     } else { >> src\main\java\com\restaurant\util\AuthTester.java
echo                         System.out.println("❌ PASSWORD VERIFICATION FAILED!"); >> src\main\java\com\restaurant\util\AuthTester.java
echo                         System.out.println("   The password 'admin123' does not match the stored hash"); >> src\main\java\com\restaurant\util\AuthTester.java
echo. >> src\main\java\com\restaurant\util\AuthTester.java
echo                         // Test creating a new hash >> src\main\java\com\restaurant\util\AuthTester.java
echo                         String newHash = BCrypt.hashpw(testPassword, BCrypt.gensalt()); >> src\main\java\com\restaurant\util\AuthTester.java
echo                         System.out.println("   New hash for 'admin123': " + newHash.substring(0, 20) + "..."); >> src\main\java\com\restaurant\util\AuthTester.java
echo                     } >> src\main\java\com\restaurant\util\AuthTester.java
echo. >> src\main\java\com\restaurant\util\AuthTester.java
echo                 } else { >> src\main\java\com\restaurant\util\AuthTester.java
echo                     System.out.println("❌ USER NOT FOUND!"); >> src\main\java\com\restaurant\util\AuthTester.java
echo                     System.out.println("   No user with username '" + testUsername + "' exists"); >> src\main\java\com\restaurant\util\AuthTester.java
echo                 } >> src\main\java\com\restaurant\util\AuthTester.java
echo             } >> src\main\java\com\restaurant\util\AuthTester.java
echo. >> src\main\java\com\restaurant\util\AuthTester.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\util\AuthTester.java
echo             System.err.println("❌ Error: " + e.getMessage()); >> src\main\java\com\restaurant\util\AuthTester.java
echo             e.printStackTrace(); >> src\main\java\com\restaurant\util\AuthTester.java
echo         } >> src\main\java\com\restaurant\util\AuthTester.java
echo     } >> src\main\java\com\restaurant\util\AuthTester.java
echo } >> src\main\java\com\restaurant\util\AuthTester.java

echo ✅ AuthTester created
echo.

echo STEP 2: Compiling authentication tester...
mvn compile -q

if %ERRORLEVEL% neq 0 (
    echo ❌ Compilation failed
    pause
    exit /b 1
)

echo STEP 3: Running authentication test...
echo.

java -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar" com.restaurant.util.AuthTester

echo.
echo 📋 AUTHENTICATION TEST COMPLETE!
echo.
echo Based on the results above:
echo.
echo ✅ If "AUTHENTICATION SUCCESSFUL" - then the issue is in the application login code
echo ❌ If "PASSWORD VERIFICATION FAILED" - then the password hash is wrong
echo ❌ If "USER NOT FOUND" - then the database doesn't have the admin user
echo.
echo NEXT STEPS:
echo 1. If authentication works here but not in app - check application login code
echo 2. If password fails - we need to update the password hash
echo 3. If user not found - we need to recreate the admin user
echo.

pause
