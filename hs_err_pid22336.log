#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x0000000070c17ed0, pid=22336, tid=2388
#
# JRE version: Java(TM) SE Runtime Environment (23.0.1+11) (build 23.0.1+11-39)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (23.0.1+11-39, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# Problematic frame:
# C  [sqlite-********-30354b97-6afc-4739-932c-9afb3ea625c0-sqlitejdbc.dll+0x57ed0]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://bugreport.java.com/bugreport/crash.jsp
# The crash happened outside the Java Virtual Machine in native code.
# See problematic frame for where to report the bug.
#

---------------  S U M M A R Y ------------

Command Line: --module-path=C:\Users\<USER>\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics com.restaurant.RestaurantApp

Host: AMD Ryzen 7 5800H with Radeon Graphics         , 16 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
Time: Thu Jul 10 13:27:38 2025 India Standard Time elapsed time: 12.962817 seconds (0d 0h 0m 12s)

---------------  T H R E A D  ---------------

Current thread (0x000001dd11d39930):  JavaThread "Thread-10" daemon [_thread_in_native, id=2388, stack(0x00000095bb900000,0x00000095bba00000) (1024K)]

Stack: [0x00000095bb900000,0x00000095bba00000],  sp=0x00000095bb9ff090,  free space=1020k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  [sqlite-********-30354b97-6afc-4739-932c-9afb3ea625c0-sqlitejdbc.dll+0x57ed0]  (no source info available)
C  0x000001dcb5d4dec0  (no source info available)

The last pc belongs to native method entry point (kind = native_synchronized) (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  org.sqlite.core.NativeDB.reset(J)I+0
j  org.sqlite.jdbc3.JDBC3PreparedStatement.executeQuery()Ljava/sql/ResultSet;+39
j  com.restaurant.model.ActivityDAO.getRecentActivities(I)Ljava/util/List;+30
j  com.restaurant.controller.DashboardController$2.call()Ljava/util/List;+8
j  com.restaurant.controller.DashboardController$2.call()Ljava/lang/Object;+1
j  javafx.concurrent.Task$TaskCallable.call()Ljava/lang/Object;+25 javafx.graphics
j  java.util.concurrent.FutureTask.run()V+39 java.base@23.0.1
j  java.lang.Thread.runWith(Ljava/lang/Object;Ljava/lang/Runnable;)V+5 java.base@23.0.1
j  java.lang.Thread.run()V+19 java.base@23.0.1
v  ~StubRoutines::call_stub 0x000001dcb5d40fcd

siginfo: EXCEPTION_ACCESS_VIOLATION (0xc0000005), reading address 0x000001dd0d020fb8


Registers:
RAX=0x0000000000000000, RBX=0x000001dd0d020fb8, RCX=0x000001dd0d020fb8, RDX=0x00000095bb9ff1a0
RSP=0x00000095bb9ff090, RBP=0x00000095bb9ff170, RSI=0x000001dd07006860, RDI=0x0000000000110836
R8 =0x000001dd0d020fb8, R9 =0x00007ffabb4a0300, R10=0x000001dcb5d4de91, R11=0x0000000000000121
R12=0x0000000000000000, R13=0x000001dd0800baf8, R14=0x00000095bb9ff1a0, R15=0x000001dd11d39930
RIP=0x0000000070c17ed0, EFLAGS=0x0000000000010206


Register to memory mapping:

RAX=0x0 is null
RBX=0x000001dd0d020fb8 is an unknown value
RCX=0x000001dd0d020fb8 is an unknown value
RDX=0x00000095bb9ff1a0 is pointing into the stack for thread: 0x000001dd11d39930
RSP=0x00000095bb9ff090 is pointing into the stack for thread: 0x000001dd11d39930
RBP=0x00000095bb9ff170 is pointing into the stack for thread: 0x000001dd11d39930
RSI=0x000001dd07006860 is pointing into metadata
RDI=0x0000000000110836 is an unknown value
R8 =0x000001dd0d020fb8 is an unknown value
R9 =0x00007ffabb4a0300 KERNEL32.DLL
R10=0x000001dcb5d4de91 is at code_begin+1329 in an Interpreter codelet
native method entry point (kind = native_synchronized)  [0x000001dcb5d4d960, 0x000001dcb5d4e5f0]  3216 bytes
R11=0x0000000000000121 is an unknown value
R12=0x0 is null
R13={method} {0x000001dd0800bb00} 'reset' '(J)I' in 'org/sqlite/core/NativeDB'
R14=0x00000095bb9ff1a0 is pointing into the stack for thread: 0x000001dd11d39930
R15=0x000001dd11d39930 is a thread

Top of Stack: (sp=0x00000095bb9ff090)
0x00000095bb9ff090:   000001dd00000002 00000095bb9ff170
0x00000095bb9ff0a0:   0000000000000000 000001dd0800baf8
0x00000095bb9ff0b0:   000000070d931b30 00007ffa04113d54
0x00000095bb9ff0c0:   000001dd07006860 000001dd0800baf8
0x00000095bb9ff0d0:   000001dd07006860 000001dcb5d4dec0
0x00000095bb9ff0e0:   0000000000110836 000001dcb5d4dc28
0x00000095bb9ff0f0:   0000000000000628 0000000000000000
0x00000095bb9ff100:   00000095bb9ff130 00000095bb9ff180
0x00000095bb9ff110:   000001dcb5d4dbd0 0000000000000000
0x00000095bb9ff120:   000000070d931b30 fffffffffffffff5
0x00000095bb9ff130:   000001dd0800baf8 0000000000000006
0x00000095bb9ff140:   000001dd08060b58 0000000000000000
0x00000095bb9ff150:   0000000718242be8 000001dd0800baf8
0x00000095bb9ff160:   0000000000000000 00000095bb9ff190
0x00000095bb9ff170:   00000095bb9ff1f0 000001dcb5d48848
0x00000095bb9ff180:   0000000000000000 000001dcb5d4a5d5
0x00000095bb9ff190:   000001dd0d020fb8 0000000000000000
0x00000095bb9ff1a0:   000000070d931b30 fffffffffffffff7
0x00000095bb9ff1b0:   000001dd08092287 0000000000000004
0x00000095bb9ff1c0:   000001dd08096a70 0000000000000000
0x00000095bb9ff1d0:   0000000718238a28 000001dd08092320
0x00000095bb9ff1e0:   fffffffffffffff4 00000095bb9ff210
0x00000095bb9ff1f0:   00000095bb9ff260 000001dcb5d48da0
0x00000095bb9ff200:   0000000000000000 0000000000000000
0x00000095bb9ff210:   000000070d502e40 fffffffffffffff7
0x00000095bb9ff220:   000001dd07ff163e 0000000000000007
0x00000095bb9ff230:   000001dd08241ba8 0000000000000000
0x00000095bb9ff240:   000000070d92a8f8 000001dd08241478
0x00000095bb9ff250:   fffffffffffffff6 00000095bb9ff298
0x00000095bb9ff260:   00000095bb9ff2e8 000001dcb5d4889a
0x00000095bb9ff270:   0000000000000000 0000000000000000
0x00000095bb9ff280:   000000070d502e40 000000070d931600 

Instructions: (pc=0x0000000070c17ed0)
0x0000000070c17dd0:   89 44 24 20 48 8b 13 e8 2c af fb ff 48 8b 43 10
0x0000000070c17de0:   48 89 43 28 eb 17 48 8b 4c 24 40 49 89 c0 48 89
0x0000000070c17df0:   da 48 81 c1 cf 01 00 00 e8 89 b0 fb ff 45 31 ed
0x0000000070c17e00:   48 8b 43 18 45 89 2c 24 48 89 45 28 48 81 c4 28
0x0000000070c17e10:   01 00 00 5b 5e 5f 5d 41 5c 41 5d 41 5e 41 5f c3
0x0000000070c17e20:   48 83 ec 38 48 8d 05 6d 8c ff ff 48 89 44 24 20
0x0000000070c17e30:   e8 17 f3 ff ff 48 83 c4 38 c3 56 53 48 83 ec 28
0x0000000070c17e40:   48 8b 31 48 89 cb 48 8b 49 10 48 85 c9 74 05 e8
0x0000000070c17e50:   78 cc ff ff 48 8b 93 58 01 00 00 48 89 f1 e8 63
0x0000000070c17e60:   53 fc ff 48 8b 93 60 01 00 00 48 85 d2 74 15 48
0x0000000070c17e70:   8b 42 28 48 89 f1 48 89 83 60 01 00 00 e8 1e 44
0x0000000070c17e80:   fc ff eb df 48 8b 93 68 01 00 00 48 89 f1 e8 b6
0x0000000070c17e90:   54 fc ff 48 8b 53 08 48 89 f1 e8 a8 51 fb ff 48
0x0000000070c17ea0:   8b 93 a8 01 00 00 48 89 f1 e8 5f 53 fb ff 48 89
0x0000000070c17eb0:   d9 48 83 c4 28 5b 5e e9 27 42 fc ff 56 53 48 83
0x0000000070c17ec0:   ec 38 31 c0 48 85 c9 48 89 cb 0f 84 84 00 00 00
0x0000000070c17ed0:   48 8b 31 48 8b 4e 18 e8 3d c1 fa ff 48 83 bb b8
0x0000000070c17ee0:   00 00 00 00 7e 0b 48 89 da 48 89 f1 e8 15 4f fb
0x0000000070c17ef0:   ff 48 89 d9 e8 0f cb ff ff 48 89 f1 89 c2 c7 43
0x0000000070c17f00:   24 a3 0d f2 2d c7 43 34 ff ff ff ff c7 43 38 00
0x0000000070c17f10:   00 00 00 c6 83 c2 00 00 00 02 c7 43 3c 00 00 00
0x0000000070c17f20:   00 c7 43 30 01 00 00 00 c6 83 c3 00 00 00 ff c7
0x0000000070c17f30:   43 40 00 00 00 00 48 c7 43 50 00 00 00 00 e8 6c
0x0000000070c17f40:   a4 fb ff 48 8b 4e 18 89 44 24 2c e8 e5 c0 fa ff
0x0000000070c17f50:   8b 44 24 2c 48 83 c4 38 5b 5e c3 55 57 56 53 48
0x0000000070c17f60:   83 ec 28 48 8b 31 48 89 cf 48 8b 89 a0 00 00 00
0x0000000070c17f70:   e8 fa 4f fb ff 48 8b 8f b0 00 00 00 e8 ee 4f fb
0x0000000070c17f80:   ff 48 8b 5f 38 48 85 db 74 36 83 7f 20 05 b8 02
0x0000000070c17f90:   00 00 00 75 09 31 c0 83 7f 24 00 0f 95 c0 48 8b
0x0000000070c17fa0:   56 28 48 89 d9 48 8d 2c c2 48 83 7d 28 00 75 0b
0x0000000070c17fb0:   e8 07 ff ff ff 48 89 5d 28 eb 05 e8 47 cb ff ff
0x0000000070c17fc0:   48 8b 5f 48 48 85 db 74 10 48 8b 0b e8 36 cb ff 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x000001dd00000002 is an unknown value
stack at sp + 1 slots: 0x00000095bb9ff170 is pointing into the stack for thread: 0x000001dd11d39930
stack at sp + 2 slots: 0x0 is null
stack at sp + 3 slots: {method} {0x000001dd0800bb00} 'reset' '(J)I' in 'org/sqlite/core/NativeDB'
stack at sp + 4 slots: 0x000000070d931b30 is an oop: org.sqlite.core.NativeDB 
{0x000000070d931b30} - klass: 'org/sqlite/core/NativeDB'
 - ---- fields (total size 10 words):
 - private final 'url' 'Ljava/lang/String;' @12  "*************************"{0x00000007182436c0} (0xe30486d8)
 - 'begin' 'J' @16  0 (0x0000000000000000)
 - 'commit' 'J' @24  0 (0x0000000000000000)
 - private final 'fileName' 'Ljava/lang/String;' @32  "E:\restaurant-desktop\restaurant.db"{0x000000070d931a60} (0xe1b2634c)
 - private final 'config' 'Lorg/sqlite/SQLiteConfig;' @36  a 'org/sqlite/SQLiteConfig'{0x000000070d931770} (0xe1b262ee)
 - private final 'closed' 'Ljava/util/concurrent/atomic/AtomicBoolean;' @40  a 'java/util/concurrent/atomic/AtomicBoolean'{0x000000070d931b80} (0xe1b26370)
 - private final 'stmts' 'Ljava/util/Map;' @44  a 'java/util/HashMap'{0x000000070d931b90} (0xe1b26372)
 - private final 'updateListeners' 'Ljava/util/Set;' @48  a 'java/util/HashSet'{0x000000070d931bc0} (0xe1b26378)
 - private final 'commitListeners' 'Ljava/util/Set;' @52  a 'java/util/HashSet'{0x000000070d931c00} (0xe1b26380)
 - 'pointer' 'J' @56  0 (0x0000000000000000)
 - private final 'udfdatalist' 'J' @64  0 (0x0000000000000000)
 - private final 'colldatalist' 'J' @72  0 (0x0000000000000000)
stack at sp + 5 slots: 0x00007ffa04113d54 jvm.dll
stack at sp + 6 slots: 0x000001dd07006860 is pointing into metadata
stack at sp + 7 slots: {method} {0x000001dd0800bb00} 'reset' '(J)I' in 'org/sqlite/core/NativeDB'

Lock stack of current Java thread (top to bottom):

native method entry point (kind = native_synchronized)  [0x000001dcb5d4d960, 0x000001dcb5d4e5f0]  3216 bytes
[MachCode]
  0x000001dcb5d4d960: 488b 4b08 | 0fb7 492e | 584c 8d74 | ccf8 6800 | 0000 0068 | 0000 0000 | 5055 488b | ec41 5568 
  0x000001dcb5d4d980: 0000 0000 | 4c8b 6b08 | 4d8d 6d38 | 5348 8b53 | 0848 8b52 | 0848 8b52 | 1848 8b52 | 7048 8b12 
  0x000001dcb5d4d9a0: 5248 8b53 | 1048 85d2 | 0f84 0700 | 0000 4881 | c210 0100 | 0052 488b | 5308 488b | 5208 488b 
  0x000001dcb5d4d9c0: 5210 5249 | 8bc6 482b | c548 c1e8 | 0350 6800 | 0000 0068 | f7ff ffff | 41c6 8771 | 0400 0001 
  0x000001dcb5d4d9e0: 488b 4310 | 4885 c074 | 208b 88cc | 0000 0083 | c102 8988 | cc00 0000 | 2388 e000 | 0000 0f84 
  0x000001dcb5d4da00: 390b 0000 | e9cf 0000 | 0048 8b43 | 1848 85c0 | 0f85 b000 | 0000 e805 | 0000 00e9 | 9900 0000 
  0x000001dcb5d4da20: 488b d348 | 8d44 2408 | 4c89 6dc0 | 498b cfc5 | f877 4989 | afb0 0300 | 0049 8987 | a003 0000 
  0x000001dcb5d4da40: 4883 ec20 | 40f6 c40f | 0f84 1900 | 0000 4883 | ec08 48b8 | 9026 1104 | fa7f 0000 | ffd0 4883 
  0x000001dcb5d4da60: c408 e90c | 0000 0048 | b890 2611 | 04fa 7f00 | 00ff d048 | 83c4 2049 | c787 a003 | 0000 0000 
  0x000001dcb5d4da80: 0000 49c7 | 87b0 0300 | 0000 0000 | 0049 c787 | a803 0000 | 0000 0000 | c5f8 7749 | 837f 0800 
  0x000001dcb5d4daa0: 0f84 0500 | 0000 e915 | 34ff ff4c | 8b6d c04c | 8b75 c84e | 8d74 f500 | c348 8b43 | 1848 85c0 
  0x000001dcb5d4dac0: 0f84 1200 | 0000 8b48 | 0883 c102 | 8948 0823 | 481c 0f84 | 650a 0000 | 493b a7e8 | 0400 000f 
  0x000001dcb5d4dae0: 8748 0000 | 0089 8424 | 00f0 ffff | 8984 2400 | e0ff ff89 | 8424 00d0 | ffff 8984 | 2400 c0ff 
  0x000001dcb5d4db00: ff89 8424 | 00b0 ffff | 8984 2400 | a0ff ff89 | 8424 0090 | ffff 8984 | 2400 80ff | ff49 3ba7 
  0x000001dcb5d4db20: e004 0000 | 7607 4989 | a7e8 0400 | 0041 c687 | 7104 0000 | 008b 4328 | a808 498b | 060f 8413 
  0x000001dcb5d4db40: 0000 0048 | 8b43 0848 | 8b40 0848 | 8b40 1848 | 8b40 7048 | 8b00 4883 | ec10 4883 | 6db8 0248 
  0x000001dcb5d4db60: 8944 2408 | 488b d44c | 8b4a 0849 | 8b01 418b | 9f18 0600 | 0081 fb68 | 0600 000f | 8d4a 0000 
  0x000001dcb5d4db80: 004d 3b4c | 1ff8 0f84 | 2500 0000 | a802 0f85 | 3700 0000 | 488b d848 | 83e3 fe48 | 83c8 01f0 
  0x000001dcb5d4dba0: 490f b119 | 0f85 2100 | 0000 418b | 9f18 0600 | 004d 890c | 1f83 c308 | 4189 9f18 | 0600 0049 
  0x000001dcb5d4dbc0: ff87 5005 | 0000 e9a3 | 0000 00e8 | 0500 0000 | e999 0000 | 0049 8bd1 | 488d 4424 | 084c 896d 
  0x000001dcb5d4dbe0: c049 8bcf | c5f8 7749 | 89af b003 | 0000 4989 | 87a0 0300 | 0048 83ec | 2040 f6c4 | 0f0f 8419 
  0x000001dcb5d4dc00: 0000 0048 | 83ec 0848 | b8f0 3c11 | 04fa 7f00 | 00ff d048 | 83c4 08e9 | 0c00 0000 | 48b8 f03c 
  0x000001dcb5d4dc20: 1104 fa7f | 0000 ffd0 | 4883 c420 | 49c7 87a0 | 0300 0000 | 0000 0049 | c787 b003 | 0000 0000 
  0x000001dcb5d4dc40: 0000 49c7 | 87a8 0300 | 0000 0000 | 00c5 f877 | 4983 7f08 | 000f 8405 | 0000 00e9 | 6032 ffff 
  0x000001dcb5d4dc60: 4c8b 6dc0 | 4c8b 75c8 | 4e8d 74f5 | 00c3 49ba | 41ad 9804 | fa7f 0000 | 4180 3a00 | 0f84 3e00 
  0x000001dcb5d4dc80: 0000 488b | 55e8 498b | cf48 83ec | 2040 f6c4 | 0f0f 8419 | 0000 0048 | 83ec 0848 | b810 ee48 
  0x000001dcb5d4dca0: 04fa 7f00 | 00ff d048 | 83c4 08e9 | 0c00 0000 | 48b8 10ee | 4804 fa7f | 0000 ffd0 | 4883 c420 
  0x000001dcb5d4dcc0: 488b 5de8 | 4c8b 5b08 | 450f b75b | 2e41 c1e3 | 0349 2be3 | 4883 ec20 | 4883 e4f0 | 4c8b 5b60 
  0x000001dcb5d4dce0: 4d85 db0f | 85ab 0000 | 00e8 0500 | 0000 e999 | 0000 0048 | 8bd3 488d | 4424 084c | 896d c049 
  0x000001dcb5d4dd00: 8bcf c5f8 | 7749 89af | b003 0000 | 4989 87a0 | 0300 0048 | 83ec 2040 | f6c4 0f0f | 8419 0000 
  0x000001dcb5d4dd20: 0048 83ec | 0848 b8f0 | 4d11 04fa | 7f00 00ff | d048 83c4 | 08e9 0c00 | 0000 48b8 | f04d 1104 
  0x000001dcb5d4dd40: fa7f 0000 | ffd0 4883 | c420 49c7 | 87a0 0300 | 0000 0000 | 0049 c787 | b003 0000 | 0000 0000 
  0x000001dcb5d4dd60: 49c7 87a8 | 0300 0000 | 0000 00c5 | f877 4983 | 7f08 000f | 8405 0000 | 00e9 4231 | ffff 4c8b 
  0x000001dcb5d4dd80: 6dc0 4c8b | 75c8 4e8d | 74f5 00c3 | 488b 5de8 | 4c8b 5b60 | 41ff d348 | 8b5d e848 | 8945 1844 
  0x000001dcb5d4dda0: 8b5b 2841 | f6c3 080f | 841b 0000 | 004c 8b5b | 084d 8b5b | 084d 8b5b | 184d 8b5b | 704d 8b1b 
  0x000001dcb5d4ddc0: 4c89 5d10 | 488d 5510 | 488b 4358 | 49ba 603c | 4904 fa7f | 0000 493b | c20f 85ab | 0000 00e8 
  0x000001dcb5d4dde0: 0500 0000 | e999 0000 | 0048 8bd3 | 488d 4424 | 084c 896d | c049 8bcf | c5f8 7749 | 89af b003 
  0x000001dcb5d4de00: 0000 4989 | 87a0 0300 | 0048 83ec | 2040 f6c4 | 0f0f 8419 | 0000 0048 | 83ec 0848 | b8f0 4d11 
  0x000001dcb5d4de20: 04fa 7f00 | 00ff d048 | 83c4 08e9 | 0c00 0000 | 48b8 f04d | 1104 fa7f | 0000 ffd0 | 4883 c420 
  0x000001dcb5d4de40: 49c7 87a0 | 0300 0000 | 0000 0049 | c787 b003 | 0000 0000 | 0000 49c7 | 87a8 0300 | 0000 0000 
  0x000001dcb5d4de60: 00c5 f877 | 4983 7f08 | 000f 8405 | 0000 00e9 | 4c30 ffff | 4c8b 6dc0 | 4c8b 75c8 | 4e8d 74f5 
  0x000001dcb5d4de80: 00c3 488b | 5de8 488b | 4358 498d | 8fc0 0300 | 00c5 f877 | 4989 afb0 | 0300 0049 | ba91 ded4 
  0x000001dcb5d4dea0: b5dc 0100 | 004d 8997 | a803 0000 | 4989 a7a0 | 0300 0041 | c787 4404 | 0000 0400 | 0000 ffd0 
  0x000001dcb5d4dec0: c5f8 7748 | 83ec 10c5 | fb11 0424 | 4883 ec10 | 4889 0424 | 48c7 4424 | 0800 0000 | 0041 c787 
  0x000001dcb5d4dee0: 4404 0000 | 0500 0000 | f083 4424 | c000 493b | af48 0400 | 000f 870e | 0000 0041 | 83bf 4004 
  0x000001dcb5d4df00: 0000 000f | 8420 0000 | 0049 8bcf | 4c8b e448 | 83ec 2048 | 83e4 f048 | b890 a912 | 04fa 7f00 
  0x000001dcb5d4df20: 00ff d049 | 8be4 4d33 | e441 c787 | 4404 0000 | 0800 0000 | 49c7 87a0 | 0300 0000 | 0000 0049 
  0x000001dcb5d4df40: c787 b003 | 0000 0000 | 0000 49c7 | 87a8 0300 | 0000 0000 | 00c5 f877 | 4d8b 9f30 | 0400 0041 
  0x000001dcb5d4df60: c783 0001 | 0000 0000 | 0000 49bb | daa5 d4b5 | dc01 0000 | 4c3b 5d18 | 0f85 2402 | 0000 488b 
  0x000001dcb5d4df80: 0424 4883 | c410 4885 | c00f 84fe | 0100 00a8 | 030f 8508 | 0000 0048 | 8b00 e9ee | 0100 00a8 
  0x000001dcb5d4dfa0: 010f 8509 | 0000 0048 | 8b40 fee9 | dd01 0000 | 488b 40ff | 4180 7f38 | 000f 84ce | 0100 0048 
  0x000001dcb5d4dfc0: 83f8 000f | 84c4 0100 | 004d 8b5f | 2849 83fb | 000f 8414 | 0000 0049 | 83eb 084d | 895f 284d 
  0x000001dcb5d4dfe0: 035f 3049 | 8903 e9a2 | 0100 0048 | 81ec d000 | 0000 4889 | 0424 4889 | 4c24 0848 | 8954 2410 
  0x000001dcb5d4e000: 4889 7424 | 1848 897c | 2420 4c89 | 4424 284c | 894c 2430 | 4c89 5424 | 384c 895c | 2440 c5fb 
  0x000001dcb5d4e020: 1144 2450 | c5fb 114c | 2458 c5fb | 1154 2460 | c5fb 115c | 2468 c5fb | 1164 2470 | c5fb 116c 
  0x000001dcb5d4e040: 2478 c5fb | 11b4 2480 | 0000 00c5 | fb11 bc24 | 8800 0000 | c57b 1184 | 2490 0000 | 00c5 7b11 
  0x000001dcb5d4e060: 8c24 9800 | 0000 c57b | 1194 24a0 | 0000 00c5 | 7b11 9c24 | a800 0000 | c57b 11a4 | 24b0 0000 
  0x000001dcb5d4e080: 00c5 7b11 | ac24 b800 | 0000 c57b | 11b4 24c0 | 0000 00c5 | 7b11 bc24 | c800 0000 | 498b d748 
  0x000001dcb5d4e0a0: 8bc8 4883 | ec20 40f6 | c40f 0f84 | 1900 0000 | 4883 ec08 | 48b8 e007 | 0604 fa7f | 0000 ffd0 
  0x000001dcb5d4e0c0: 4883 c408 | e90c 0000 | 0048 b8e0 | 0706 04fa | 7f00 00ff | d048 83c4 | 20c5 7b10 | bc24 c800 
  0x000001dcb5d4e0e0: 0000 c57b | 10b4 24c0 | 0000 00c5 | 7b10 ac24 | b800 0000 | c57b 10a4 | 24b0 0000 | 00c5 7b10 
  0x000001dcb5d4e100: 9c24 a800 | 0000 c57b | 1094 24a0 | 0000 00c5 | 7b10 8c24 | 9800 0000 | c57b 1084 | 2490 0000 
  0x000001dcb5d4e120: 00c5 fb10 | bc24 8800 | 0000 c5fb | 10b4 2480 | 0000 00c5 | fb10 6c24 | 78c5 fb10 | 6424 70c5 
  0x000001dcb5d4e140: fb10 5c24 | 68c5 fb10 | 5424 60c5 | fb10 4c24 | 58c5 fb10 | 4424 504c | 8b5c 2440 | 4c8b 5424 
  0x000001dcb5d4e160: 384c 8b4c | 2430 4c8b | 4424 2848 | 8b7c 2420 | 488b 7424 | 1848 8b54 | 2410 488b | 4c24 0848 
  0x000001dcb5d4e180: 8b04 2448 | 81c4 d000 | 0000 c5f8 | 7748 8945 | 1048 83ec | 1048 8904 | 2448 c744 | 2408 0000 
  0x000001dcb5d4e1a0: 0000 4183 | bfc8 0400 | 0002 0f85 | bf00 0000 | 4881 ec80 | 0000 0048 | 8944 2478 | 4889 4c24 
  0x000001dcb5d4e1c0: 7048 8954 | 2468 4889 | 5c24 6048 | 896c 2450 | 4889 7424 | 4848 897c | 2440 4c89 | 4424 384c 
  0x000001dcb5d4e1e0: 894c 2430 | 4c89 5424 | 284c 895c | 2420 4c89 | 6424 184c | 896c 2410 | 4c89 7424 | 084c 893c 
  0x000001dcb5d4e200: 244c 8be4 | 4883 ec20 | 4883 e4f0 | 48b8 c022 | 4904 fa7f | 0000 ffd0 | 498b e44c | 8b3c 244c 
  0x000001dcb5d4e220: 8b74 2408 | 4c8b 6c24 | 104c 8b64 | 2418 4c8b | 5c24 204c | 8b54 2428 | 4c8b 4c24 | 304c 8b44 
  0x000001dcb5d4e240: 2438 488b | 7c24 4048 | 8b74 2448 | 488b 6c24 | 5048 8b5c | 2460 488b | 5424 6848 | 8b4c 2470 
  0x000001dcb5d4e260: 488b 4424 | 7848 81c4 | 8000 0000 | 4d33 e448 | 8b5d e84c | 8b6b 084d | 8d6d 3849 | 837f 0800 
  0x000001dcb5d4e280: 0f84 bb00 | 0000 e805 | 0000 00e9 | 9600 0000 | 488d 4424 | 084c 896d | c049 8bcf | c5f8 7749 
  0x000001dcb5d4e2a0: 89af b003 | 0000 4989 | 87a0 0300 | 0048 83ec | 2040 f6c4 | 0f0f 8419 | 0000 0048 | 83ec 0848 
  0x000001dcb5d4e2c0: b800 6c11 | 04fa 7f00 | 00ff d048 | 83c4 08e9 | 0c00 0000 | 48b8 006c | 1104 fa7f | 0000 ffd0 
  0x000001dcb5d4e2e0: 4883 c420 | 49c7 87a0 | 0300 0000 | 0000 0049 | c787 b003 | 0000 0000 | 0000 49c7 | 87a8 0300 
  0x000001dcb5d4e300: 0000 0000 | 00c5 f877 | 4983 7f08 | 000f 8405 | 0000 00e9 | a82b ffff | 4c8b 6dc0 | 4c8b 75c8 
  0x000001dcb5d4e320: 4e8d 74f5 | 00c3 48b9 | 40d5 7004 | fa7f 0000 | 4883 e4f0 | 48b8 704a | 3704 fa7f | 0000 ffd0 
  0x000001dcb5d4e340: f444 8b5b | 2841 f6c3 | 200f 8479 | 0100 0048 | 8d55 a84c | 8b5a 084d | 85db 0f85 | bb00 0000 
  0x000001dcb5d4e360: e805 0000 | 00e9 9600 | 0000 488d | 4424 084c | 896d c049 | 8bcf c5f8 | 7749 89af | b003 0000 
  0x000001dcb5d4e380: 4989 87a0 | 0300 0048 | 83ec 2040 | f6c4 0f0f | 8419 0000 | 0048 83ec | 0848 b830 | 6b11 04fa 
  0x000001dcb5d4e3a0: 7f00 00ff | d048 83c4 | 08e9 0c00 | 0000 48b8 | 306b 1104 | fa7f 0000 | ffd0 4883 | c420 49c7 
  0x000001dcb5d4e3c0: 87a0 0300 | 0000 0000 | 0049 c787 | b003 0000 | 0000 0000 | 49c7 87a8 | 0300 0000 | 0000 00c5 
  0x000001dcb5d4e3e0: f877 4983 | 7f08 000f | 8405 0000 | 00e9 ce2a | ffff 4c8b | 6dc0 4c8b | 75c8 4e8d | 74f5 00c3 
  0x000001dcb5d4e400: 48b9 40d5 | 7004 fa7f | 0000 4883 | e4f0 48b8 | 704a 3704 | fa7f 0000 | ffd0 f44c | 896d c04c 
  0x000001dcb5d4e420: 8b4a 0848 | c742 0800 | 0000 0045 | 8b87 1806 | 0000 4f3b | 4c07 f80f | 8549 0000 | 0041 83af 
  0x000001dcb5d4e440: 1806 0000 | 084f 3b4c | 07f0 0f84 | 2a00 0000 | 498b 01a8 | 020f 8512 | 0000 004c | 8bc0 4983 
  0x000001dcb5d4e460: c801 f04d | 0fb1 010f | 840d 0000 | 0041 8387 | 1806 0000 | 08e9 0c00 | 0000 49ff | 8f50 0500 
  0x000001dcb5d4e480: 00e9 3e00 | 0000 4c89 | 4a08 488b | ca48 83ec | 2040 f6c4 | 0f0f 8419 | 0000 0048 | 83ec 0848 
  0x000001dcb5d4e4a0: b8f0 3d11 | 04fa 7f00 | 00ff d048 | 83c4 08e9 | 0c00 0000 | 48b8 f03d | 1104 fa7f | 0000 ffd0 
  0x000001dcb5d4e4c0: 4883 c420 | 4c8b 6dc0 | 49ba 41ad | 9804 fa7f | 0000 4180 | 3a00 0f84 | 3e00 0000 | 488b 55e8 
  0x000001dcb5d4e4e0: 498b cf48 | 83ec 2040 | f6c4 0f0f | 8419 0000 | 0048 83ec | 0848 b810 | ee48 04fa | 7f00 00ff 
  0x000001dcb5d4e500: d048 83c4 | 08e9 0c00 | 0000 48b8 | 10ee 4804 | fa7f 0000 | ffd0 4883 | c420 488b | 0424 4883 
  0x000001dcb5d4e520: c410 c5fb | 1004 2448 | 83c4 104c | 8b5d 1841 | ffd3 4c8b | 5df8 c95f | 498b e3ff | e7ba 0000 
  0x000001dcb5d4e540: 0000 e805 | 0000 00e9 | 9600 0000 | 488d 4424 | 084c 896d | c049 8bcf | c5f8 7749 | 89af b003 
  0x000001dcb5d4e560: 0000 4989 | 87a0 0300 | 0048 83ec | 2040 f6c4 | 0f0f 8419 | 0000 0048 | 83ec 0848 | b8a0 3111 
  0x000001dcb5d4e580: 04fa 7f00 | 00ff d048 | 83c4 08e9 | 0c00 0000 | 48b8 a031 | 1104 fa7f | 0000 ffd0 | 4883 c420 
  0x000001dcb5d4e5a0: 49c7 87a0 | 0300 0000 | 0000 0049 | c787 b003 | 0000 0000 | 0000 49c7 | 87a8 0300 | 0000 0000 
  0x000001dcb5d4e5c0: 00c5 f877 | 4983 7f08 | 000f 8405 | 0000 00e9 | ec28 ffff | 4c8b 6dc0 | 4c8b 75c8 | 4e8d 74f5 
  0x000001dcb5d4e5e0: 00c3 488b | 5de8 e9ed | f4ff ff0f | 1f44 0000 
[/MachCode]

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001dd11d74e30, length=31, elements={
0x000001dca3dc83b0, 0x000001dcc5df1b10, 0x000001dcc5df2570, 0x000001dcc5df55d0,
0x000001dcc5df7e10, 0x000001dcc5df6a60, 0x000001dcc5df84a0, 0x000001dd0b0203c0,
0x000001dd0b020e70, 0x000001dcc5e1e4b0, 0x000001dcc5df91c0, 0x000001dd0b19aed0,
0x000001dd0b229230, 0x000001dcc5df70f0, 0x000001dcc5df8b30, 0x000001dcc5df7780,
0x000001dcc5df9850, 0x000001dcc5df63d0, 0x000001dd0bfc8790, 0x000001dd0bfc8100,
0x000001dd0bfcc2a0, 0x000001dd0c214920, 0x000001dd0bfccfc0, 0x000001dd0bfcd650,
0x000001dd0bfcaef0, 0x000001dd0bfcea00, 0x000001dd11d39930, 0x000001dd11e65450,
0x000001dd11e65b10, 0x000001dd15719580, 0x000001dd15716ca0
}

Java Threads: ( => current thread )
  0x000001dca3dc83b0 JavaThread "main"                              [_thread_blocked, id=20748, stack(0x00000095b7700000,0x00000095b7800000) (1024K)]
  0x000001dcc5df1b10 JavaThread "Reference Handler"          daemon [_thread_blocked, id=20592, stack(0x00000095b7f00000,0x00000095b8000000) (1024K)]
  0x000001dcc5df2570 JavaThread "Finalizer"                  daemon [_thread_blocked, id=3128, stack(0x00000095b8000000,0x00000095b8100000) (1024K)]
  0x000001dcc5df55d0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=23728, stack(0x00000095b8100000,0x00000095b8200000) (1024K)]
  0x000001dcc5df7e10 JavaThread "Attach Listener"            daemon [_thread_blocked, id=29656, stack(0x00000095b8200000,0x00000095b8300000) (1024K)]
  0x000001dcc5df6a60 JavaThread "Service Thread"             daemon [_thread_blocked, id=26552, stack(0x00000095b8300000,0x00000095b8400000) (1024K)]
  0x000001dcc5df84a0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=12152, stack(0x00000095b8400000,0x00000095b8500000) (1024K)]
  0x000001dd0b0203c0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=27244, stack(0x00000095b8500000,0x00000095b8600000) (1024K)]
  0x000001dd0b020e70 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=26576, stack(0x00000095b8600000,0x00000095b8700000) (1024K)]
  0x000001dcc5e1e4b0 JavaThread "C1 CompilerThread1"         daemon [_thread_in_native, id=9428, stack(0x00000095b8700000,0x00000095b8800000) (1024K)]
  0x000001dcc5df91c0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=8068, stack(0x00000095b8800000,0x00000095b8900000) (1024K)]
  0x000001dd0b19aed0 JavaThread "C1 CompilerThread2"         daemon [_thread_blocked, id=26060, stack(0x00000095b8900000,0x00000095b8a00000) (1024K)]
  0x000001dd0b229230 JavaThread "C1 CompilerThread3"         daemon [_thread_blocked, id=18872, stack(0x00000095b8b00000,0x00000095b8c00000) (1024K)]
  0x000001dcc5df70f0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=24324, stack(0x00000095b8e00000,0x00000095b8f00000) (1024K)]
  0x000001dcc5df8b30 JavaThread "QuantumRenderer-0"          daemon [_thread_in_Java, id=10208, stack(0x00000095b8f00000,0x00000095b9000000) (1024K)]
  0x000001dcc5df7780 JavaThread "InvokeLaterDispatcher"      daemon [_thread_blocked, id=29136, stack(0x00000095b9200000,0x00000095b9300000) (1024K)]
  0x000001dcc5df9850 JavaThread "JavaFX Application Thread"         [_thread_blocked, id=26052, stack(0x00000095b9300000,0x00000095b9400000) (1024K)]
  0x000001dcc5df63d0 JavaThread "JavaFX-Launcher"                   [_thread_blocked, id=22280, stack(0x00000095b9f00000,0x00000095ba000000) (1024K)]
  0x000001dd0bfc8790 JavaThread "Thread-2"                   daemon [_thread_in_native, id=25404, stack(0x00000095b9e00000,0x00000095b9f00000) (1024K)]
  0x000001dd0bfc8100 JavaThread "Prism Font Disposer"        daemon [_thread_blocked, id=11880, stack(0x00000095baf00000,0x00000095bb000000) (1024K)]
  0x000001dd0bfcc2a0 JavaThread "Cleaner-0"                  daemon [_thread_blocked, id=20156, stack(0x00000095bb100000,0x00000095bb200000) (1024K)]
  0x000001dd0c214920 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=20504, stack(0x00000095b8d00000,0x00000095b8e00000) (1024K)]
  0x000001dd0bfccfc0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=16524, stack(0x00000095bb200000,0x00000095bb300000) (1024K)]
  0x000001dd0bfcd650 JavaThread "pool-2-thread-2"                   [_thread_blocked, id=16584, stack(0x00000095bb300000,0x00000095bb400000) (1024K)]
  0x000001dd0bfcaef0 JavaThread "Timer-0"                    daemon [_thread_blocked, id=21856, stack(0x00000095bb400000,0x00000095bb500000) (1024K)]
  0x000001dd0bfcea00 JavaThread "Timer-1"                           [_thread_blocked, id=18184, stack(0x00000095bb500000,0x00000095bb600000) (1024K)]
=>0x000001dd11d39930 JavaThread "Thread-10"                  daemon [_thread_in_native, id=2388, stack(0x00000095bb900000,0x00000095bba00000) (1024K)]
  0x000001dd11e65450 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=28116, stack(0x00000095bb600000,0x00000095bb700000) (1024K)]
  0x000001dd11e65b10 JavaThread "C2 CompilerThread3"         daemon [_thread_blocked, id=14164, stack(0x00000095bb700000,0x00000095bb800000) (1024K)]
  0x000001dd15719580 JavaThread "C2 CompilerThread4"         daemon [_thread_in_native, id=27288, stack(0x00000095ba600000,0x00000095ba700000) (1024K)]
  0x000001dd15716ca0 JavaThread "C2 CompilerThread5"         daemon [_thread_in_native, id=22500, stack(0x00000095bb800000,0x00000095bb900000) (1024K)]
Total: 31

Other Threads:
  0x000001dcc5dd9910 VMThread "VM Thread"                           [id=12244, stack(0x00000095b7e00000,0x00000095b7f00000) (1024K)]
  0x000001dcc5dc3e30 WatcherThread "VM Periodic Task Thread"        [id=7680, stack(0x00000095b7d00000,0x00000095b7e00000) (1024K)]
  0x000001dca6222c00 WorkerThread "GC Thread#0"                     [id=2780, stack(0x00000095b7800000,0x00000095b7900000) (1024K)]
  0x000001dd0bf92eb0 WorkerThread "GC Thread#1"                     [id=3644, stack(0x00000095ba000000,0x00000095ba100000) (1024K)]
  0x000001dd0bf93260 WorkerThread "GC Thread#2"                     [id=18044, stack(0x00000095ba100000,0x00000095ba200000) (1024K)]
  0x000001dd0bf93610 WorkerThread "GC Thread#3"                     [id=27856, stack(0x00000095ba200000,0x00000095ba300000) (1024K)]
  0x000001dd0bf939c0 WorkerThread "GC Thread#4"                     [id=12996, stack(0x00000095ba300000,0x00000095ba400000) (1024K)]
  0x000001dd0bf93d70 WorkerThread "GC Thread#5"                     [id=23568, stack(0x00000095ba400000,0x00000095ba500000) (1024K)]
  0x000001dd0c35d0e0 WorkerThread "GC Thread#6"                     [id=27084, stack(0x00000095ba700000,0x00000095ba800000) (1024K)]
  0x000001dd0c35d490 WorkerThread "GC Thread#7"                     [id=3484, stack(0x00000095ba800000,0x00000095ba900000) (1024K)]
  0x000001dd0c35c220 WorkerThread "GC Thread#8"                     [id=19004, stack(0x00000095ba900000,0x00000095baa00000) (1024K)]
  0x000001dd0c35d840 WorkerThread "GC Thread#9"                     [id=28912, stack(0x00000095baa00000,0x00000095bab00000) (1024K)]
  0x000001dd0c35be70 WorkerThread "GC Thread#10"                    [id=23396, stack(0x00000095bab00000,0x00000095bac00000) (1024K)]
  0x000001dd0c17c2c0 WorkerThread "GC Thread#11"                    [id=21224, stack(0x00000095bac00000,0x00000095bad00000) (1024K)]
  0x000001dd0c17a8f0 WorkerThread "GC Thread#12"                    [id=4540, stack(0x00000095bad00000,0x00000095bae00000) (1024K)]
  0x000001dca6239270 ConcurrentGCThread "G1 Main Marker"            [id=25868, stack(0x00000095b7900000,0x00000095b7a00000) (1024K)]
  0x000001dca6239d80 WorkerThread "G1 Conc#0"                       [id=29164, stack(0x00000095b7a00000,0x00000095b7b00000) (1024K)]
  0x000001dd0c17bb60 WorkerThread "G1 Conc#1"                       [id=24708, stack(0x00000095ba500000,0x00000095ba600000) (1024K)]
  0x000001dd0c179680 WorkerThread "G1 Conc#2"                       [id=17536, stack(0x00000095bba00000,0x00000095bbb00000) (1024K)]
  0x000001dcc5c901c0 ConcurrentGCThread "G1 Refine#0"               [id=27632, stack(0x00000095b7b00000,0x00000095b7c00000) (1024K)]
  0x000001dcc5c91af0 ConcurrentGCThread "G1 Service"                [id=29132, stack(0x00000095b7c00000,0x00000095b7d00000) (1024K)]
Total: 21

Threads with active compile tasks:
C2 CompilerThread4  13199 5709  s!   4       com.sun.scenario.effect.impl.ImagePool::checkOut (447 bytes)
C2 CompilerThread5  13199 5578   !   4       javafx.scene.CssStyleHelper::transitionToState (1031 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x000000070a800000, size: 3928 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000001dcc6000000-0x000001dcc6d70000-0x000001dcc6d70000), size 14090240, SharedBaseAddress: 0x000001dcc6000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001dcc7000000-0x000001dd07000000, reserved size: 1073741824
Narrow klass base: 0x000001dcc6000000, Narrow klass shift: 0, Narrow klass range: 0x41000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096
 CPUs: 16 total, 16 available
 Memory: 15709M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 246M
 Heap Max Capacity: 3928M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total reserved 4022272K, committed 61440K, used 39813K [0x000000070a800000, 0x0000000800000000)
  region size 2048K, 16 young (32768K), 4 survivors (8192K)
 Metaspace       used 22380K, committed 22848K, reserved 1114112K
  class space    used 3169K, committed 3392K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x000000070a800000, 0x000000070aa00000, 0x000000070aa00000|100%| O|  |TAMS 0x000000070a800000| PB 0x000000070a800000| Untracked |  0
|   1|0x000000070aa00000, 0x000000070ac00000, 0x000000070ac00000|100%| O|  |TAMS 0x000000070aa00000| PB 0x000000070aa00000| Untracked |  0
|   2|0x000000070ac00000, 0x000000070ae00000, 0x000000070ae00000|100%|HS|  |TAMS 0x000000070ac00000| PB 0x000000070ac00000| Complete |  0
|   3|0x000000070ae00000, 0x000000070b000000, 0x000000070b000000|100%| O|  |TAMS 0x000000070ae00000| PB 0x000000070ae00000| Untracked |  0
|   4|0x000000070b000000, 0x000000070b189d88, 0x000000070b200000| 76%| O|  |TAMS 0x000000070b000000| PB 0x000000070b000000| Untracked |  0
|   5|0x000000070b200000, 0x000000070b200000, 0x000000070b400000|  0%| F|  |TAMS 0x000000070b200000| PB 0x000000070b200000| Untracked |  0
|   6|0x000000070b400000, 0x000000070b400000, 0x000000070b600000|  0%| F|  |TAMS 0x000000070b400000| PB 0x000000070b400000| Untracked |  0
|   7|0x000000070b600000, 0x000000070b600000, 0x000000070b800000|  0%| F|  |TAMS 0x000000070b600000| PB 0x000000070b600000| Untracked |  0
|   8|0x000000070b800000, 0x000000070b800000, 0x000000070ba00000|  0%| F|  |TAMS 0x000000070b800000| PB 0x000000070b800000| Untracked |  0
|   9|0x000000070ba00000, 0x000000070ba00000, 0x000000070bc00000|  0%| F|  |TAMS 0x000000070ba00000| PB 0x000000070ba00000| Untracked |  0
|  10|0x000000070bc00000, 0x000000070bc00000, 0x000000070be00000|  0%| F|  |TAMS 0x000000070bc00000| PB 0x000000070bc00000| Untracked |  0
|  11|0x000000070be00000, 0x000000070be00000, 0x000000070c000000|  0%| F|  |TAMS 0x000000070be00000| PB 0x000000070be00000| Untracked |  0
|  12|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000| PB 0x000000070c000000| Untracked |  0
|  13|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000| PB 0x000000070c200000| Untracked |  0
|  14|0x000000070c400000, 0x000000070c55c6a0, 0x000000070c600000| 68%| E|  |TAMS 0x000000070c400000| PB 0x000000070c400000| Complete |  0
|  15|0x000000070c600000, 0x000000070c800000, 0x000000070c800000|100%| E|CS|TAMS 0x000000070c600000| PB 0x000000070c600000| Complete |  0
|  16|0x000000070c800000, 0x000000070ca00000, 0x000000070ca00000|100%| E|CS|TAMS 0x000000070c800000| PB 0x000000070c800000| Complete |  0
|  17|0x000000070ca00000, 0x000000070cc00000, 0x000000070cc00000|100%| E|CS|TAMS 0x000000070ca00000| PB 0x000000070ca00000| Complete |  0
|  18|0x000000070cc00000, 0x000000070ce00000, 0x000000070ce00000|100%| E|CS|TAMS 0x000000070cc00000| PB 0x000000070cc00000| Complete |  0
|  19|0x000000070ce00000, 0x000000070d000000, 0x000000070d000000|100%| E|CS|TAMS 0x000000070ce00000| PB 0x000000070ce00000| Complete |  0
|  20|0x000000070d000000, 0x000000070d200000, 0x000000070d200000|100%| E|CS|TAMS 0x000000070d000000| PB 0x000000070d000000| Complete |  0
|  21|0x000000070d200000, 0x000000070d400000, 0x000000070d400000|100%| E|CS|TAMS 0x000000070d200000| PB 0x000000070d200000| Complete |  0
|  22|0x000000070d400000, 0x000000070d600000, 0x000000070d600000|100%| E|CS|TAMS 0x000000070d400000| PB 0x000000070d400000| Complete |  0
|  23|0x000000070d600000, 0x000000070d800000, 0x000000070d800000|100%| E|CS|TAMS 0x000000070d600000| PB 0x000000070d600000| Complete |  0
|  24|0x000000070d800000, 0x000000070da00000, 0x000000070da00000|100%| E|CS|TAMS 0x000000070d800000| PB 0x000000070d800000| Complete |  0
| 108|0x0000000718000000, 0x0000000718157758, 0x0000000718200000| 67%| S|CS|TAMS 0x0000000718000000| PB 0x0000000718000000| Complete |  0
| 109|0x0000000718200000, 0x0000000718400000, 0x0000000718400000|100%| S|CS|TAMS 0x0000000718200000| PB 0x0000000718200000| Complete |  0
| 110|0x0000000718400000, 0x0000000718600000, 0x0000000718600000|100%| S|CS|TAMS 0x0000000718400000| PB 0x0000000718400000| Complete |  0
| 111|0x0000000718600000, 0x0000000718800000, 0x0000000718800000|100%| S|CS|TAMS 0x0000000718600000| PB 0x0000000718600000| Complete |  0
| 122|0x0000000719c00000, 0x0000000719e00000, 0x0000000719e00000|100%| E|CS|TAMS 0x0000000719c00000| PB 0x0000000719c00000| Complete |  0

Card table byte_map: [0x000001dcbe920000,0x000001dcbf0d0000] _byte_map_base: 0x000001dcbb0cc000

Marking Bits: (CMBitMap*) 0x000001dca6224730
 Bits: [0x000001dcbf0d0000, 0x000001dcc2e30000)

Polling page: 0x000001dca3d80000

Metaspace:

Usage:
  Non-class:     18.77 MB used.
      Class:      3.10 MB used.
       Both:     21.86 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      19.00 MB ( 30%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       3.31 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      22.31 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  12.86 MB
       Class:  12.61 MB
        Both:  25.47 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 35.12 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 3.
num_arena_births: 408.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 357.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 3.
num_chunks_taken_from_freelist: 1182.
num_chunk_merges: 3.
num_chunk_splits: 792.
num_chunks_enlarged: 555.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119232Kb used=1931Kb max_used=1931Kb free=117300Kb
 bounds [0x000001dcb6480000, 0x000001dcb66f0000, 0x000001dcbd8f0000]
CodeHeap 'profiled nmethods': size=119104Kb used=9697Kb max_used=9697Kb free=109406Kb
 bounds [0x000001dcae8f0000, 0x000001dcaf270000, 0x000001dcb5d40000]
CodeHeap 'non-nmethods': size=7424Kb used=3524Kb max_used=3565Kb free=3899Kb
 bounds [0x000001dcb5d40000, 0x000001dcb60c0000, 0x000001dcb6480000]
CodeCache: size=245760Kb, used=15152Kb, max_used=15193Kb, free=230605Kb
 total_blobs=6648, nmethods=5780, adapters=770, full_count=0
Compilation: enabled, stopped_count=0, restarted_count=0

Compilation events (20 events):
Event: 12.936 Thread 0x000001dcc5e1e4b0 5050       3       javafx.css.SizeUnits::pixelSize (18 bytes)
Event: 12.936 Thread 0x000001dd0b020e70 5051       3       javafx.css.SizeUnits::round (38 bytes)
Event: 12.936 Thread 0x000001dd0b229230 5052       3       com.sun.javafx.font.LogicalFont::getStrike (11 bytes)
Event: 12.937 Thread 0x000001dcc5e1e4b0 nmethod 5050 0x000001dcaf0e5f08 code [0x000001dcaf0e6040, 0x000001dcaf0e6200]
Event: 12.937 Thread 0x000001dcc5e1e4b0 5053       3       com.sun.javafx.font.LogicalFont::getDefaultAAMode (10 bytes)
Event: 12.937 Thread 0x000001dd0b020e70 nmethod 5051 0x000001dcaf0e6288 code [0x000001dcaf0e63e0, 0x000001dcaf0e65f0]
Event: 12.937 Thread 0x000001dd0b19aed0 nmethod 5049 0x000001dcaf0e6608 code [0x000001dcaf0e6740, 0x000001dcaf0e69a0]
Event: 12.937 Thread 0x000001dcc5e1e4b0 nmethod 5053 0x000001dcaf0e6a08 code [0x000001dcaf0e6b40, 0x000001dcaf0e6d48]
Event: 12.937 Thread 0x000001dd0b229230 nmethod 5052 0x000001dcaf0e6d88 code [0x000001dcaf0e6ee0, 0x000001dcaf0e71c8]
Event: 12.937 Thread 0x000001dcc5e1e4b0 5054       3       java.util.AbstractCollection::toArray (144 bytes)
Event: 12.937 Thread 0x000001dd0b020e70 5055       3       javafx.beans.property.ObjectPropertyBase::bind (71 bytes)
Event: 12.937 Thread 0x000001dd0b229230 5056       3       javafx.beans.property.ObjectPropertyBase::unbind (39 bytes)
Event: 12.937 Thread 0x000001dcc5e1e4b0 nmethod 5054 0x000001dcaf0e7208 code [0x000001dcaf0e7460, 0x000001dcaf0e85d8]
Event: 12.937 Thread 0x000001dd0b19aed0 5057       3       javafx.beans.property.ObjectPropertyBase$Listener::<init> (17 bytes)
Event: 12.938 Thread 0x000001dcc5e1e4b0 5058       3       javafx.beans.property.StringPropertyBase::<init> (30 bytes)
Event: 12.938 Thread 0x000001dd0b229230 nmethod 5056 0x000001dcaf0e8688 code [0x000001dcaf0e87c0, 0x000001dcaf0e8b48]
Event: 12.938 Thread 0x000001dd0b19aed0 nmethod 5057 0x000001dcaf0e8b88 code [0x000001dcaf0e8ce0, 0x000001dcaf0e9188]
Event: 12.938 Thread 0x000001dd0b020e70 nmethod 5055 0x000001dcaf0e9208 code [0x000001dcaf0e93e0, 0x000001dcaf0e9ee8]
Event: 12.938 Thread 0x000001dcc5e1e4b0 nmethod 5058 0x000001dcaf0e9f88 code [0x000001dcaf0ea0c0, 0x000001dcaf0ea440]
Event: 12.938 Thread 0x000001dcc5e1e4b0 5060       3       javafx.scene.Node::wouldCreateCycle (76 bytes)

GC Heap History (8 events):
Event: 0.492 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total reserved 4022272K, committed 251904K, used 22528K [0x000000070a800000, 0x0000000800000000)
  region size 2048K, 12 young (24576K), 0 survivors (0K)
 Metaspace       used 3930K, committed 4032K, reserved 1114112K
  class space    used 464K, committed 512K, reserved 1048576K
}
Event: 0.494 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total reserved 4022272K, committed 251904K, used 2602K [0x000000070a800000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 3930K, committed 4032K, reserved 1114112K
  class space    used 464K, committed 512K, reserved 1048576K
}
Event: 0.856 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total reserved 4022272K, committed 251904K, used 27178K [0x000000070a800000, 0x0000000800000000)
  region size 2048K, 15 young (30720K), 2 survivors (4096K)
 Metaspace       used 10989K, committed 11328K, reserved 1114112K
  class space    used 1494K, committed 1664K, reserved 1048576K
}
Event: 0.860 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total reserved 4022272K, committed 251904K, used 6643K [0x000000070a800000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 10989K, committed 11328K, reserved 1114112K
  class space    used 1494K, committed 1664K, reserved 1048576K
}
Event: 11.531 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total reserved 4022272K, committed 251904K, used 55795K [0x000000070a800000, 0x0000000800000000)
  region size 2048K, 26 young (53248K), 2 survivors (4096K)
 Metaspace       used 18028K, committed 18368K, reserved 1114112K
  class space    used 2625K, committed 2816K, reserved 1048576K
}
Event: 11.535 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total reserved 4022272K, committed 251904K, used 14012K [0x000000070a800000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 18028K, committed 18368K, reserved 1114112K
  class space    used 2625K, committed 2816K, reserved 1048576K
}
Event: 12.853 GC heap before
{Heap before GC invocations=3 (full 0):
 garbage-first heap   total reserved 4022272K, committed 251904K, used 34492K [0x000000070a800000, 0x0000000800000000)
  region size 2048K, 15 young (30720K), 4 survivors (8192K)
 Metaspace       used 21151K, committed 21504K, reserved 1114112K
  class space    used 3000K, committed 3200K, reserved 1048576K
}
Event: 12.858 GC heap after
{Heap after GC invocations=4 (full 0):
 garbage-first heap   total reserved 4022272K, committed 251904K, used 17285K [0x000000070a800000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 21151K, committed 21504K, reserved 1114112K
  class space    used 3000K, committed 3200K, reserved 1048576K
}

Dll operation events (20 events):
Event: 0.293 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-locale-l1-1-0.dll
Event: 0.296 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-math-l1-1-0.dll
Event: 0.299 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-multibyte-l1-1-0.dll
Event: 0.303 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-private-l1-1-0.dll
Event: 0.306 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-process-l1-1-0.dll
Event: 0.310 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-runtime-l1-1-0.dll
Event: 0.313 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-stdio-l1-1-0.dll
Event: 0.316 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-string-l1-1-0.dll
Event: 0.320 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-time-l1-1-0.dll
Event: 0.323 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-utility-l1-1-0.dll
Event: 0.335 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\ucrtbase.dll
Event: 0.338 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\vcruntime140.dll
Event: 0.341 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\vcruntime140_1.dll
Event: 0.348 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\msvcp140.dll
Event: 0.377 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\prism_d3d.dll
Event: 0.422 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\glass.dll
Event: 0.498 Loaded shared library C:\Program Files\Java\jdk-23\bin\jimage.dll
Event: 0.612 Loaded shared library C:\Users\<USER>\.openjfx\cache\17.0.2-ea\javafx_font.dll
Event: 12.613 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\sqlite-********-30354b97-6afc-4739-932c-9afb3ea625c0-sqlitejdbc.dll
Event: 12.624 Loaded shared library C:\Program Files\Java\jdk-23\bin\verify.dll

Deoptimization events (20 events):
Event: 12.900 Thread 0x000001dcc5df9850 DEOPT PACKING pc=0x000001dcb65d1718 sp=0x00000095b93fbb40
Event: 12.900 Thread 0x000001dcc5df9850 DEOPT UNPACKING pc=0x000001dcb5d94402 sp=0x00000095b93fb9f0 mode 2
Event: 12.934 Thread 0x000001dcc5df9850 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000001dcb65cc470 relative=0x0000000000004f70
Event: 12.934 Thread 0x000001dcc5df9850 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000001dcb65cc470 method=javafx.scene.CssStyleHelper.getStyle(Ljavafx/css/Styleable;Ljava/lang/String;Lcom/sun/javafx/css/StyleMap;Ljava/util/Set;)Lcom/sun/javafx/css/CascadingStyle; @ 94 
Event: 12.934 Thread 0x000001dcc5df9850 DEOPT PACKING pc=0x000001dcb65cc470 sp=0x00000095b93fa470
Event: 12.934 Thread 0x000001dcc5df9850 DEOPT UNPACKING pc=0x000001dcb5d94402 sp=0x00000095b93fa3a0 mode 2
Event: 12.936 Thread 0x000001dcc5df9850 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000001dcb65cc470 relative=0x0000000000004f70
Event: 12.936 Thread 0x000001dcc5df9850 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000001dcb65cc470 method=javafx.scene.CssStyleHelper.getStyle(Ljavafx/css/Styleable;Ljava/lang/String;Lcom/sun/javafx/css/StyleMap;Ljava/util/Set;)Lcom/sun/javafx/css/CascadingStyle; @ 94 
Event: 12.936 Thread 0x000001dcc5df9850 DEOPT PACKING pc=0x000001dcb65cc470 sp=0x00000095b93fa470
Event: 12.936 Thread 0x000001dcc5df9850 DEOPT UNPACKING pc=0x000001dcb5d94402 sp=0x00000095b93fa3a0 mode 2
Event: 12.937 Thread 0x000001dcc5df9850 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000001dcb65cc470 relative=0x0000000000004f70
Event: 12.937 Thread 0x000001dcc5df9850 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000001dcb65cc470 method=javafx.scene.CssStyleHelper.getStyle(Ljavafx/css/Styleable;Ljava/lang/String;Lcom/sun/javafx/css/StyleMap;Ljava/util/Set;)Lcom/sun/javafx/css/CascadingStyle; @ 94 
Event: 12.937 Thread 0x000001dcc5df9850 DEOPT PACKING pc=0x000001dcb65cc470 sp=0x00000095b93fa470
Event: 12.937 Thread 0x000001dcc5df9850 DEOPT UNPACKING pc=0x000001dcb5d94402 sp=0x00000095b93fa3a0 mode 2
Event: 12.937 Thread 0x000001dcc5df9850 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000001dcb65cc470 relative=0x0000000000004f70
Event: 12.937 Thread 0x000001dcc5df9850 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000001dcb65cc470 method=javafx.scene.CssStyleHelper.getStyle(Ljavafx/css/Styleable;Ljava/lang/String;Lcom/sun/javafx/css/StyleMap;Ljava/util/Set;)Lcom/sun/javafx/css/CascadingStyle; @ 94 
Event: 12.937 Thread 0x000001dcc5df9850 DEOPT PACKING pc=0x000001dcb65cc470 sp=0x00000095b93fa470
Event: 12.937 Thread 0x000001dcc5df9850 DEOPT UNPACKING pc=0x000001dcb5d94402 sp=0x00000095b93fa3a0 mode 2
Event: 12.938 Thread 0x000001dcc5df9850 DEOPT PACKING pc=0x000001dcaeea49b0 sp=0x00000095b93fa4a0
Event: 12.938 Thread 0x000001dcc5df9850 DEOPT UNPACKING pc=0x000001dcb5d94b22 sp=0x00000095b93f9990 mode 0

Classes loaded (20 events):
Event: 12.808 Loading class java/util/concurrent/Delayed
Event: 12.808 Loading class java/util/concurrent/Delayed done
Event: 12.808 Loading class java/util/concurrent/ScheduledFuture done
Event: 12.808 Loading class java/util/concurrent/RunnableScheduledFuture done
Event: 12.809 Loading class jdk/internal/math/FormattedFPDecimal
Event: 12.809 Loading class jdk/internal/math/FormattedFPDecimal done
Event: 12.811 Loading class java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask
Event: 12.811 Loading class java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask done
Event: 12.814 Loading class java/util/TaskQueue
Event: 12.814 Loading class java/util/TaskQueue done
Event: 12.814 Loading class java/util/TimerThread
Event: 12.814 Loading class java/util/TimerThread done
Event: 12.814 Loading class java/util/Timer$ThreadReaper
Event: 12.814 Loading class java/util/Timer$ThreadReaper done
Event: 12.816 Loading class java/util/stream/ReduceOps$5
Event: 12.817 Loading class java/util/stream/ReduceOps$5 done
Event: 12.817 Loading class java/util/stream/ReduceOps$CountingSink$OfRef
Event: 12.817 Loading class java/util/stream/ReduceOps$CountingSink
Event: 12.817 Loading class java/util/stream/ReduceOps$CountingSink done
Event: 12.817 Loading class java/util/stream/ReduceOps$CountingSink$OfRef done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 12.105 Thread 0x000001dcc5df9850 Exception <a 'java/lang/ClassCastException'{0x00000007198388d8}: class java.lang.String cannot be cast to class javafx.css.Size (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.Size is in module javafx.graphics of loader 'app')> (0x00000007198388d8) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 12.105 Thread 0x000001dcc5df9850 Exception <a 'java/lang/ClassCastException'{0x000000071984d640}: class java.lang.String cannot be cast to class javafx.css.Size (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.Size is in module javafx.graphics of loader 'app')> (0x000000071984d640) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 12.552 Thread 0x000001dcc5df9850 Exception <a 'java/lang/NoSuchMethodError'{0x0000000719782118}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int, int, int)'> (0x0000000719782118) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 12.569 Thread 0x000001dcc5df9850 Exception <a 'java/lang/NoSuchMethodError'{0x000000071944e7d0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, int, int, int, int)'> (0x000000071944e7d0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 12.815 Thread 0x000001dcc5df9850 Exception <a 'java/lang/NoSuchMethodError'{0x0000000718a38f58}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, double, int)'> (0x0000000718a38f58) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 12.823 Thread 0x000001dcc5df9850 Exception <a 'java/lang/NoSuchMethodError'{0x0000000718a755e8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x0000000718a755e8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 12.844 Thread 0x000001dcc5df9850 Exception <a 'java/lang/ClassCastException'{0x00000007188d0b98}: class java.lang.String cannot be cast to class javafx.css.ParsedValue (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.ParsedValue is in module javafx.graphics of loader 'app')> (0x00000007188d0b98) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 12.847 Thread 0x000001dcc5df9850 Exception <a 'java/lang/ClassCastException'{0x00000007188ed738}: class java.lang.String cannot be cast to class javafx.css.ParsedValue (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.ParsedValue is in module javafx.graphics of loader 'app')> (0x00000007188ed738) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 12.848 Thread 0x000001dcc5df9850 Exception <a 'java/lang/ClassCastException'{0x0000000718905588}: class java.lang.String cannot be cast to class javafx.css.ParsedValue (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.ParsedValue is in module javafx.graphics of loader 'app')> (0x0000000718905588) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 12.850 Thread 0x000001dcc5df9850 Exception <a 'java/lang/ClassCastException'{0x0000000718922020}: class java.lang.String cannot be cast to class javafx.css.ParsedValue (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.ParsedValue is in module javafx.graphics of loader 'app')> (0x0000000718922020) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 12.851 Thread 0x000001dcc5df9850 Exception <a 'java/lang/ClassCastException'{0x0000000718937f70}: class java.lang.String cannot be cast to class javafx.scene.paint.Paint (java.lang.String is in module java.base of loader 'bootstrap'; javafx.scene.paint.Paint is in module javafx.graphics of loader 'app')> (0x0000000718937f70) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 12.893 Thread 0x000001dcc5df9850 Exception <a 'java/lang/ClassCastException'{0x000000070d96d280}: class java.lang.String cannot be cast to class javafx.scene.paint.Paint (java.lang.String is in module java.base of loader 'bootstrap'; javafx.scene.paint.Paint is in module javafx.graphics of loader 'app')> (0x000000070d96d280) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 12.894 Thread 0x000001dcc5df9850 Exception <a 'java/lang/ClassCastException'{0x000000070d97af18}: class java.lang.String cannot be cast to class javafx.css.Size (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.Size is in module javafx.graphics of loader 'app')> (0x000000070d97af18) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 12.895 Thread 0x000001dcc5df9850 Exception <a 'java/lang/ClassCastException'{0x000000070d990038}: class java.lang.String cannot be cast to class javafx.css.Size (java.lang.String is in module java.base of loader 'bootstrap'; javafx.css.Size is in module javafx.graphics of loader 'app')> (0x000000070d990038) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 12.920 Thread 0x000001dcc5df9850 Exception <a 'java/lang/ClassCastException'{0x000000070d7bc300}: class java.lang.String cannot be cast to class javafx.scene.paint.Paint (java.lang.String is in module java.base of loader 'bootstrap'; javafx.scene.paint.Paint is in module javafx.graphics of loader 'app')> (0x000000070d7bc300) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 12.933 Thread 0x000001dcc5df9850 Exception <a 'java/lang/ClassCastException'{0x000000070d44aba8}: class java.lang.String cannot be cast to class javafx.scene.paint.Paint (java.lang.String is in module java.base of loader 'bootstrap'; javafx.scene.paint.Paint is in module javafx.graphics of loader 'app')> (0x000000070d44aba8) 
thrown [s\open\src\hotspot\share\interpreter\interpreterRuntime.cpp, line 438]
Event: 12.934 Thread 0x000001dcc5df9850 Implicit null exception at 0x000001dcb65c8769 to 0x000001dcb65cc434
Event: 12.936 Thread 0x000001dcc5df9850 Implicit null exception at 0x000001dcb65c8769 to 0x000001dcb65cc434
Event: 12.937 Thread 0x000001dcc5df9850 Implicit null exception at 0x000001dcb65c8769 to 0x000001dcb65cc434
Event: 12.937 Thread 0x000001dcc5df9850 Implicit null exception at 0x000001dcb65c8769 to 0x000001dcb65cc434

VM Operations (20 events):
Event: 11.531 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 11.535 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 12.516 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 12.516 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 12.524 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 12.524 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 12.531 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 12.531 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 12.556 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 12.556 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 12.813 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 12.813 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 12.817 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 12.817 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 12.853 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold)
Event: 12.858 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold) done
Event: 12.863 Executing VM operation: G1PauseRemark
Event: 12.865 Executing VM operation: G1PauseRemark done
Event: 12.866 Executing VM operation: G1PauseCleanup
Event: 12.866 Executing VM operation: G1PauseCleanup done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 11.553 Thread 0x000001dcc5e1e4b0 Thread added: 0x000001dd121a09e0
Event: 11.763 Thread 0x000001dd121a09e0 Thread exited: 0x000001dd121a09e0
Event: 11.767 Thread 0x000001dd0c4b5160 Thread exited: 0x000001dd0c4b5160
Event: 11.818 Thread 0x000001dd0b020e70 Thread added: 0x000001dd0c4b5160
Event: 12.025 Thread 0x000001dd0c4b5160 Thread exited: 0x000001dd0c4b5160
Event: 12.543 Thread 0x000001dd0b229230 Thread added: 0x000001dd0c214920
Event: 12.778 Thread 0x000001dcc5df9850 Thread added: 0x000001dd0bfcc930
Event: 12.780 Thread 0x000001dcc5df9850 Thread added: 0x000001dd0bfce370
Event: 12.811 Thread 0x000001dcc5df9850 Thread added: 0x000001dd0bfccfc0
Event: 12.812 Thread 0x000001dcc5df9850 Thread added: 0x000001dd0bfcd650
Event: 12.814 Thread 0x000001dcc5df9850 Thread added: 0x000001dd0bfcaef0
Event: 12.820 Thread 0x000001dcc5df9850 Thread added: 0x000001dd0bfcea00
Event: 12.825 Thread 0x000001dcc5df9850 Thread added: 0x000001dd0bfcdce0
Event: 12.825 Thread 0x000001dcc5df9850 Thread added: 0x000001dd0bfcb580
Event: 12.826 Thread 0x000001dcc5df9850 Thread added: 0x000001dd11d392a0
Event: 12.826 Thread 0x000001dcc5df9850 Thread added: 0x000001dd11d39930
Event: 12.830 Thread 0x000001dd0bfcc930 Thread exited: 0x000001dd0bfcc930
Event: 12.877 Thread 0x000001dd11d392a0 Thread exited: 0x000001dd11d392a0
Event: 12.877 Thread 0x000001dd0bfcdce0 Thread exited: 0x000001dd0bfcdce0
Event: 12.927 Thread 0x000001dd0bfcb580 Thread exited: 0x000001dd0bfcb580


Dynamic libraries:
0x00007ff602520000 - 0x00007ff602530000 	C:\Program Files\Java\jdk-23\bin\java.exe
0x00007ffabc4c0000 - 0x00007ffabc728000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffabb3e0000 - 0x00007ffabb4a9000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffab9e70000 - 0x00007ffaba25d000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffab9b10000 - 0x00007ffab9c5b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffa9c710000 - 0x00007ffa9c72b000 	C:\Program Files\Java\jdk-23\bin\VCRUNTIME140.dll
0x00007ffa95e20000 - 0x00007ffa95e37000 	C:\Program Files\Java\jdk-23\bin\jli.dll
0x00007ffabbc00000 - 0x00007ffabbdcc000 	C:\WINDOWS\System32\USER32.dll
0x00007ffab98f0000 - 0x00007ffab9917000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa9d050000 - 0x00007ffa9d2ea000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3\COMCTL32.dll
0x00007ffaba260000 - 0x00007ffaba28b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffabbe90000 - 0x00007ffabbf39000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffab9920000 - 0x00007ffab9a57000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffab9a60000 - 0x00007ffab9b03000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffabbf40000 - 0x00007ffabbf6f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffaa1070000 - 0x00007ffaa107c000 	C:\Program Files\Java\jdk-23\bin\vcruntime140_1.dll
0x00007ffa893e0000 - 0x00007ffa8946e000 	C:\Program Files\Java\jdk-23\bin\msvcp140.dll
0x00007ffa03d40000 - 0x00007ffa04a8f000 	C:\Program Files\Java\jdk-23\bin\server\jvm.dll
0x00007ffabbdd0000 - 0x00007ffabbe84000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffabbb50000 - 0x00007ffabbbf6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffabb2c0000 - 0x00007ffabb3d8000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffaba290000 - 0x00007ffaba304000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffab9460000 - 0x00007ffab94be000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffab28d0000 - 0x00007ffab2905000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffab2910000 - 0x00007ffab291b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffab9440000 - 0x00007ffab9454000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffab83c0000 - 0x00007ffab83db000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffa9e820000 - 0x00007ffa9e82a000 	C:\Program Files\Java\jdk-23\bin\jimage.dll
0x00007ffaabc50000 - 0x00007ffaabe91000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffabbf80000 - 0x00007ffabc306000 	C:\WINDOWS\System32\combase.dll
0x00007ffabc390000 - 0x00007ffabc470000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffa99340000 - 0x00007ffa99383000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffab96d0000 - 0x00007ffab9769000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffa95e00000 - 0x00007ffa95e1e000 	C:\Program Files\Java\jdk-23\bin\java.dll
0x00007ffabb8d0000 - 0x00007ffabba6e000 	C:\WINDOWS\System32\ole32.dll
0x00007ffaba550000 - 0x00007ffabac9a000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffab9cf0000 - 0x00007ffab9e64000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffab7230000 - 0x00007ffab7a8b000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffabb1c0000 - 0x00007ffabb2b5000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffabb860000 - 0x00007ffabb8ca000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffab9520000 - 0x00007ffab954f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffa89300000 - 0x00007ffa893d7000 	C:\Program Files\Java\jdk-23\bin\jsvml.dll
0x00007ffa9d8b0000 - 0x00007ffa9d8c0000 	C:\Program Files\Java\jdk-23\bin\net.dll
0x00007ffab8940000 - 0x00007ffab89aa000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa8ad40000 - 0x00007ffa8ad56000 	C:\Program Files\Java\jdk-23\bin\nio.dll
0x00007ffa89a80000 - 0x00007ffa89a97000 	C:\Program Files\Java\jdk-23\bin\zip.dll
0x000001dca4010000 - 0x000001dca4013000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-console-l1-1-0.dll
0x000001dca4020000 - 0x000001dca4023000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-console-l1-2-0.dll
0x000001dca4030000 - 0x000001dca4033000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-datetime-l1-1-0.dll
0x000001dca4040000 - 0x000001dca4043000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-debug-l1-1-0.dll
0x000001dca4050000 - 0x000001dca4053000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-errorhandling-l1-1-0.dll
0x000001dca4060000 - 0x000001dca4064000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-file-l1-1-0.dll
0x000001dca4070000 - 0x000001dca4073000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-file-l1-2-0.dll
0x000001dca4080000 - 0x000001dca4083000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-file-l2-1-0.dll
0x000001dca5950000 - 0x000001dca5953000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-handle-l1-1-0.dll
0x000001dca5960000 - 0x000001dca5963000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-heap-l1-1-0.dll
0x000001dcc5f80000 - 0x000001dcc5f83000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-interlocked-l1-1-0.dll
0x000001dcc5f90000 - 0x000001dcc5f93000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-libraryloader-l1-1-0.dll
0x000001dcc5fa0000 - 0x000001dcc5fa3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-localization-l1-2-0.dll
0x000001dcc5fb0000 - 0x000001dcc5fb3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-memory-l1-1-0.dll
0x000001dcc5fc0000 - 0x000001dcc5fc3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-namedpipe-l1-1-0.dll
0x000001dcc5fd0000 - 0x000001dcc5fd3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-processenvironment-l1-1-0.dll
0x000001dcc5fe0000 - 0x000001dcc5fe3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-processthreads-l1-1-0.dll
0x000001dcc5ff0000 - 0x000001dcc5ff3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-processthreads-l1-1-1.dll
0x000001dd0bc00000 - 0x000001dd0bc03000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-profile-l1-1-0.dll
0x000001dd0bc10000 - 0x000001dd0bc13000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-rtlsupport-l1-1-0.dll
0x000001dd0bc20000 - 0x000001dd0bc23000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-string-l1-1-0.dll
0x000001dd0bc30000 - 0x000001dd0bc33000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-synch-l1-1-0.dll
0x000001dd0bc40000 - 0x000001dd0bc43000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-synch-l1-2-0.dll
0x000001dd0bc50000 - 0x000001dd0bc53000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-sysinfo-l1-1-0.dll
0x000001dd0bc60000 - 0x000001dd0bc63000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-timezone-l1-1-0.dll
0x000001dd0bc70000 - 0x000001dd0bc73000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-core-util-l1-1-0.dll
0x000001dd0bc80000 - 0x000001dd0bc83000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-conio-l1-1-0.dll
0x000001dd0bc90000 - 0x000001dd0bc94000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-convert-l1-1-0.dll
0x000001dd0bca0000 - 0x000001dd0bca3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-environment-l1-1-0.dll
0x000001dd0bcb0000 - 0x000001dd0bcb3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-filesystem-l1-1-0.dll
0x000001dd0bcc0000 - 0x000001dd0bcc3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-heap-l1-1-0.dll
0x000001dd0bcd0000 - 0x000001dd0bcd3000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-locale-l1-1-0.dll
0x000001dd0bce0000 - 0x000001dd0bce5000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-math-l1-1-0.dll
0x000001dd0bcf0000 - 0x000001dd0bcf5000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-multibyte-l1-1-0.dll
0x000001dd0bd00000 - 0x000001dd0bd10000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-private-l1-1-0.dll
0x000001dd0bd10000 - 0x000001dd0bd13000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-process-l1-1-0.dll
0x000001dd0bd20000 - 0x000001dd0bd24000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-runtime-l1-1-0.dll
0x000001dd0bd30000 - 0x000001dd0bd34000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-stdio-l1-1-0.dll
0x000001dd0bd40000 - 0x000001dd0bd44000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-string-l1-1-0.dll
0x000001dd0cd20000 - 0x000001dd0cd23000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-time-l1-1-0.dll
0x000001dd0cd30000 - 0x000001dd0cd33000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\api-ms-win-crt-utility-l1-1-0.dll
0x00007ffa891d0000 - 0x00007ffa892cc000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\ucrtbase.dll
0x00007ffa891b0000 - 0x00007ffa891ca000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\vcruntime140.dll
0x00007ffa9c800000 - 0x00007ffa9c80c000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\vcruntime140_1.dll
0x00007ffa89120000 - 0x00007ffa891ad000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\msvcp140.dll
0x00007ffa890f0000 - 0x00007ffa8911a000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\prism_d3d.dll
0x00007ffa837c0000 - 0x00007ffa8397a000 	C:\WINDOWS\system32\d3d9.dll
0x00007ffab6b40000 - 0x00007ffab6b76000 	C:\WINDOWS\SYSTEM32\dwmapi.dll
0x00007ffab6980000 - 0x00007ffab69cd000 	C:\WINDOWS\SYSTEM32\dxcore.dll
0x00007ffab6770000 - 0x00007ffab681f000 	C:\WINDOWS\system32\uxtheme.dll
0x00007ffab2920000 - 0x00007ffab2adb000 	C:\WINDOWS\System32\DriverStore\FileRepository\u0407098.inf_amd64_05392d9068e0824e\B406567\aticfx64.dll
0x00007ffa9af40000 - 0x00007ffa9af74000 	C:\WINDOWS\System32\DriverStore\FileRepository\u0407098.inf_amd64_05392d9068e0824e\B406567\atiu9p64.dll
0x00007ffa6fae0000 - 0x00007ffa70827000 	C:\WINDOWS\System32\DriverStore\FileRepository\u0407098.inf_amd64_05392d9068e0824e\B406567\atiumd64.dll
0x00000000724a0000 - 0x00000000733b3000 	C:\WINDOWS\System32\DriverStore\FileRepository\u0407098.inf_amd64_05392d9068e0824e\B406567\atiumd6a.dll
0x00007ffabad30000 - 0x00007ffabb1b6000 	C:\WINDOWS\System32\SETUPAPI.dll
0x00007ffab08d0000 - 0x00007ffab0905000 	C:\WINDOWS\SYSTEM32\amdihk64.dll
0x00007ffab6910000 - 0x00007ffab6974000 	C:\WINDOWS\SYSTEM32\directxdatabasehelper.dll
0x00007ffa890a0000 - 0x00007ffa890e2000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\glass.dll
0x00007ffaba3e0000 - 0x00007ffaba4d8000 	C:\WINDOWS\System32\COMDLG32.dll
0x00007ffabb6f0000 - 0x00007ffabb850000 	C:\WINDOWS\System32\MSCTF.dll
0x00007ffa88b70000 - 0x00007ffa88b84000 	C:\Users\<USER>\.openjfx\cache\17.0.2-ea\javafx_font.dll
0x00007ffab4e50000 - 0x00007ffab50bb000 	C:\WINDOWS\SYSTEM32\dwrite.dll
0x00007ffabbaa0000 - 0x00007ffabbb48000 	C:\WINDOWS\System32\clbcatq.dll
0x00007ffab4c10000 - 0x00007ffab4e4a000 	C:\WINDOWS\SYSTEM32\WindowsCodecs.dll
0x00007ffa9c6b0000 - 0x00007ffa9c70a000 	C:\WINDOWS\system32\dataexchange.dll
0x00007ffab0580000 - 0x00007ffab07b8000 	C:\WINDOWS\system32\twinapi.appcore.dll
0x00007ffa88b40000 - 0x00007ffa88b6c000 	C:\Program Files\Common Files\Microsoft Shared\Ink\rtscom.dll
0x00007ffa9e560000 - 0x00007ffa9e6b0000 	C:\WINDOWS\SYSTEM32\textinputframework.dll
0x00007ffab6010000 - 0x00007ffab6135000 	C:\WINDOWS\SYSTEM32\CoreMessaging.dll
0x00007ffab2cd0000 - 0x00007ffab2fb3000 	C:\WINDOWS\SYSTEM32\CoreUIComponents.dll
0x00007ffab8bf0000 - 0x00007ffab8bfc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.DLL
0x00007ffa8b700000 - 0x00007ffa8bb33000 	C:\WINDOWS\SYSTEM32\UIAutomationCore.DLL
0x00007ffa8f2a0000 - 0x00007ffa8f35b000 	C:\Windows\System32\OneCoreCommonProxyStub.dll
0x00007ffab8c00000 - 0x00007ffab8c1b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffab8320000 - 0x00007ffab835a000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffab89e0000 - 0x00007ffab8a0b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffab94f0000 - 0x00007ffab9516000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffab7e60000 - 0x00007ffab7e93000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffaba3d0000 - 0x00007ffaba3da000 	C:\WINDOWS\System32\NSI.dll
0x0000000070bc0000 - 0x0000000070c9c000 	C:\Users\<USER>\AppData\Local\Temp\sqlite-********-30354b97-6afc-4739-932c-9afb3ea625c0-sqlitejdbc.dll
0x00007ffa9cee0000 - 0x00007ffa9cef0000 	C:\Program Files\Java\jdk-23\bin\verify.dll
0x00007ffa8c9d0000 - 0x00007ffa8ca83000 	C:\WINDOWS\SYSTEM32\TextShaping.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-23\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4484_none_3e0e6d4ce32ef3b3;C:\Program Files\Java\jdk-23\bin\server;C:\Users\<USER>\.openjfx\cache\17.0.2-ea;C:\WINDOWS\System32\DriverStore\FileRepository\u0407098.inf_amd64_05392d9068e0824e\B406567;C:\Program Files\Common Files\Microsoft Shared\Ink;C:\Users\<USER>\AppData\Local\Temp

VM Arguments:
jvm_args: --module-path=C:\Users\<USER>\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;C:\Users\<USER>\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics 
java_command: com.restaurant.RestaurantApp
java_class_path (initial): E:\restaurant-desktop\target\classes;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\pdfbox\fontbox\2.0.27\fontbox-2.0.27.jar;C:\Users\<USER>\.m2\repository\org\apache\pdfbox\pdfbox\2.0.27\pdfbox-2.0.27.jar;C:\Users\<USER>\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar;C:\Users\<USER>\.m2\repository\org\xerial\sqlite-jdbc\********\sqlite-jdbc-********.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
   size_t InitialHeapSize                          = 257949696                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MarkStackSizeMax                         = 536870912                                 {product} {ergonomic}
   size_t MaxHeapSize                              = 4118806528                                {product} {ergonomic}
   size_t MaxNewSize                               = 2470445056                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602176                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122093568                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 121962496                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4118806528                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-23
PATH=c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Python313\Scripts\;C:\Python313\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files\Python311\Scripts\;C:\Program Files\Python311\;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\Git\cmd;C:\Program Files\MySQL\MySQL Server 8.0\bin;C:\Program Files\MongoDB\Server\8.0\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\Downloads\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;C:\Users\<USER>\GlassFish_Server\bin;C:\Program Files\Java\jdk-23\bin;C:\Program Files\Java\jdk-23\bin;\bin;C:\Program Files\Apache Software Foundation\Tomcat 10.1_Tomcat.10\bin;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\PostgreSQL\17\bin;C:\Program Files\MySQL\MySQL Shell 8.0\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
USERNAME=91837
LANG=en_US.UTF-8
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 80 Stepping 0, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4484)
OS uptime: 0 days 16:36 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 25 model 80 stepping 0 microcode 0xa500011, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, rdpid, fsrm, f16c, cet_ss
Processor Information for processor 0
  Max Mhz: 3201, Current Mhz: 2555, Mhz Limit: 3201
Processor Information for processor 1
  Max Mhz: 3201, Current Mhz: 3201, Mhz Limit: 3201
Processor Information for processor 2
  Max Mhz: 3201, Current Mhz: 3201, Mhz Limit: 3201
Processor Information for processor 3
  Max Mhz: 3201, Current Mhz: 2555, Mhz Limit: 3201
Processor Information for processor 4
  Max Mhz: 3201, Current Mhz: 3201, Mhz Limit: 3201
Processor Information for processor 5
  Max Mhz: 3201, Current Mhz: 3201, Mhz Limit: 3201
Processor Information for processor 6
  Max Mhz: 3201, Current Mhz: 3201, Mhz Limit: 3201
Processor Information for processor 7
  Max Mhz: 3201, Current Mhz: 3201, Mhz Limit: 3201
Processor Information for processor 8
  Max Mhz: 3201, Current Mhz: 3201, Mhz Limit: 3201
Processor Information for processor 9
  Max Mhz: 3201, Current Mhz: 2555, Mhz Limit: 3201
Processor Information for processor 10
  Max Mhz: 3201, Current Mhz: 2555, Mhz Limit: 3201
Processor Information for processor 11
  Max Mhz: 3201, Current Mhz: 3201, Mhz Limit: 3201
Processor Information for processor 12
  Max Mhz: 3201, Current Mhz: 3201, Mhz Limit: 3201
Processor Information for processor 13
  Max Mhz: 3201, Current Mhz: 3201, Mhz Limit: 3201
Processor Information for processor 14
  Max Mhz: 3201, Current Mhz: 3201, Mhz Limit: 3201
Processor Information for processor 15
  Max Mhz: 3201, Current Mhz: 2555, Mhz Limit: 3201

Memory: 4k page, system-wide physical 15709M (2770M free)
TotalPageFile size 25323M (AvailPageFile size 4410M)
current process WorkingSet (physical memory assigned to process): 203M, peak: 211M
current process commit charge ("private bytes"): 320M, peak: 484M

vm_info: Java HotSpot(TM) 64-Bit Server VM (23.0.1+11-39) for windows-amd64 JRE (23.0.1+11-39), built on 2024-09-30T07:20:43Z with MS VC++ 17.6 (VS2022)

END.
