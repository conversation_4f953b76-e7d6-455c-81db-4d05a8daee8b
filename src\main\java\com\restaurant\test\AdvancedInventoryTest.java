package com.restaurant.test;

import com.restaurant.model.*;
import java.time.LocalDate;
import java.util.List;

/**
 * Test class for Advanced Inventory Management functionality
 */
public class AdvancedInventoryTest {

    public static void main(String[] args) {
        System.out.println("=== Advanced Inventory Management Test ===");

        // Initialize database by getting a connection (this automatically initializes)
        try {
            DatabaseManager.getConnection().close();
            System.out.println("Database initialized successfully");
        } catch (Exception e) {
            System.err.println("Error initializing database: " + e.getMessage());
            return;
        }

        // Test Purchase Orders
        testPurchaseOrders();

        // Test Internal Transfers
        testInternalTransfers();

        System.out.println("=== Test Completed ===");
    }
    
    private static void testPurchaseOrders() {
        System.out.println("\n--- Testing Purchase Orders ---");
        
        try {
            // Create sample purchase order
            PurchaseOrder order = new PurchaseOrder();
            order.setRequestNumber(PurchaseOrderDAO.generateNextRequestNumber());
            order.setToLocation("Vastrapur");
            order.setItem("Test Mango");
            order.setQuantity("10 kg");
            order.setStatus("Saved");
            order.setStartDate(LocalDate.now());
            order.setEndDate(LocalDate.now().plusDays(7));
            order.setCreatedBy("Test User");
            order.setNotes("Test purchase order");
            order.setUnitPrice(50.0);
            order.setTotalAmount(500.0);
            
            // Add to database
            boolean added = PurchaseOrderDAO.addPurchaseOrder(order);
            System.out.println("Purchase order added: " + added);
            System.out.println("Order ID: " + order.getId());
            System.out.println("Request Number: " + order.getRequestNumber());
            
            // Retrieve all purchase orders
            List<PurchaseOrder> orders = PurchaseOrderDAO.getAllPurchaseOrders();
            System.out.println("Total purchase orders: " + orders.size());
            
            // Test status filtering
            List<PurchaseOrder> savedOrders = PurchaseOrderDAO.getPurchaseOrdersByStatus("Saved");
            System.out.println("Saved orders: " + savedOrders.size());
            
            // Update order status
            if (!orders.isEmpty()) {
                PurchaseOrder firstOrder = orders.get(0);
                firstOrder.setStatus("Processed");
                boolean updated = PurchaseOrderDAO.updatePurchaseOrder(firstOrder);
                System.out.println("Order updated: " + updated);
            }
            
        } catch (Exception e) {
            System.err.println("Error testing purchase orders: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testInternalTransfers() {
        System.out.println("\n--- Testing Internal Transfers ---");
        
        try {
            // Create sample internal transfer
            InternalTransfer transfer = new InternalTransfer();
            transfer.setRequestNumber(InternalTransferDAO.generateNextRequestNumber());
            transfer.setFromLocation("Central Kitchen");
            transfer.setToLocation("Vastrapur");
            transfer.setItem("Test Carrot");
            transfer.setQuantity("6 kg");
            transfer.setStatus("Saved");
            transfer.setStartDate(LocalDate.now());
            transfer.setEndDate(LocalDate.now().plusDays(3));
            transfer.setCreatedBy("Test User");
            transfer.setNotes("Test internal transfer");
            transfer.setTransferReason("Stock replenishment");
            
            // Add to database
            boolean added = InternalTransferDAO.addInternalTransfer(transfer);
            System.out.println("Internal transfer added: " + added);
            System.out.println("Transfer ID: " + transfer.getId());
            System.out.println("Request Number: " + transfer.getRequestNumber());
            
            // Retrieve all internal transfers
            List<InternalTransfer> transfers = InternalTransferDAO.getAllInternalTransfers();
            System.out.println("Total internal transfers: " + transfers.size());
            
            // Test status filtering
            List<InternalTransfer> savedTransfers = InternalTransferDAO.getInternalTransfersByStatus("Saved");
            System.out.println("Saved transfers: " + savedTransfers.size());
            
            // Test location filtering
            List<InternalTransfer> fromCentralKitchen = InternalTransferDAO.getInternalTransfersByLocation("Central Kitchen", true);
            System.out.println("Transfers from Central Kitchen: " + fromCentralKitchen.size());
            
            // Update transfer status
            if (!transfers.isEmpty()) {
                InternalTransfer firstTransfer = transfers.get(0);
                firstTransfer.setStatus("Processed");
                boolean updated = InternalTransferDAO.updateInternalTransfer(firstTransfer);
                System.out.println("Transfer updated: " + updated);
            }
            
        } catch (Exception e) {
            System.err.println("Error testing internal transfers: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
