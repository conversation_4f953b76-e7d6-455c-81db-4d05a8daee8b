package com.restaurant.model;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class DailyReport {
    private int id;
    private LocalDate reportDate;
    private int totalOrders;
    private double totalRevenue;
    private int swiggyOrders;
    private int zomatoOrders;
    private double swiggyRevenue;
    private double zomatoRevenue;
    private double avgOrderValue;
    private String peakHour;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Constructors
    public DailyReport() {}
    
    public DailyReport(LocalDate reportDate) {
        this.reportDate = reportDate;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public LocalDate getReportDate() { return reportDate; }
    public void setReportDate(LocalDate reportDate) { this.reportDate = reportDate; }
    
    public int getTotalOrders() { return totalOrders; }
    public void setTotalOrders(int totalOrders) { this.totalOrders = totalOrders; }
    
    public double getTotalRevenue() { return totalRevenue; }
    public void setTotalRevenue(double totalRevenue) { this.totalRevenue = totalRevenue; }
    
    public int getSwiggyOrders() { return swiggyOrders; }
    public void setSwiggyOrders(int swiggyOrders) { this.swiggyOrders = swiggyOrders; }
    
    public int getZomatoOrders() { return zomatoOrders; }
    public void setZomatoOrders(int zomatoOrders) { this.zomatoOrders = zomatoOrders; }
    
    public double getSwiggyRevenue() { return swiggyRevenue; }
    public void setSwiggyRevenue(double swiggyRevenue) { this.swiggyRevenue = swiggyRevenue; }
    
    public double getZomatoRevenue() { return zomatoRevenue; }
    public void setZomatoRevenue(double zomatoRevenue) { this.zomatoRevenue = zomatoRevenue; }
    
    public double getAvgOrderValue() { return avgOrderValue; }
    public void setAvgOrderValue(double avgOrderValue) { this.avgOrderValue = avgOrderValue; }
    
    public String getPeakHour() { return peakHour; }
    public void setPeakHour(String peakHour) { this.peakHour = peakHour; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    // Utility methods
    public double getSwiggyPercentage() {
        return totalOrders > 0 ? (swiggyOrders * 100.0) / totalOrders : 0.0;
    }
    
    public double getZomatoPercentage() {
        return totalOrders > 0 ? (zomatoOrders * 100.0) / totalOrders : 0.0;
    }
    
    public double getSwiggyRevenuePercentage() {
        return totalRevenue > 0 ? (swiggyRevenue * 100.0) / totalRevenue : 0.0;
    }
    
    public double getZomatoRevenuePercentage() {
        return totalRevenue > 0 ? (zomatoRevenue * 100.0) / totalRevenue : 0.0;
    }
    
    public String getDominantPlatform() {
        if (swiggyOrders > zomatoOrders) {
            return "Swiggy";
        } else if (zomatoOrders > swiggyOrders) {
            return "Zomato";
        } else {
            return "Equal";
        }
    }
    
    @Override
    public String toString() {
        return String.format("DailyReport{date=%s, orders=%d, revenue=%.2f, swiggy=%d, zomato=%d}", 
                           reportDate, totalOrders, totalRevenue, swiggyOrders, zomatoOrders);
    }
}
