@echo off
echo 🎵 SIMPLE SWIGGY & ZOMATO BEEP PATTERN TEST 🎵
echo.

echo This will test the distinctive beep patterns for Swiggy and Zomato
echo using system beeps (no MP3 needed).
echo.

echo 🔧 CREATING SIMPLE BEEP TESTER...

echo package com.restaurant.util; > src\main\java\com\restaurant\util\SimpleBeepTester.java
echo. >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo public class SimpleBeepTester { >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo     public static void main(String[] args) { >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println("🎵 Testing Swiggy and Zomato beep patterns..."); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println(); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo. >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         try { >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo             testSwiggyPattern(); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo             Thread.sleep(2000); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo             testZomatoPattern(); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo             e.printStackTrace(); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         } >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo     } >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo. >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo     private static void testSwiggyPattern() throws InterruptedException { >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println("🟠 SWIGGY ORDER SOUND - 4 Rapid + 2 Long Beeps"); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println("Pattern: BEEP-BEEP-BEEP-BEEP (pause) BEEP-BEEP (long)"); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println("Playing in 3 seconds..."); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         Thread.sleep(3000); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo. >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         // 4 rapid beeps >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println("4 rapid beeps:"); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         for (int i = 0; i ^< 4; i++) { >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo             System.out.println("  Rapid BEEP " + (i + 1)); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo             java.awt.Toolkit.getDefaultToolkit().beep(); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo             Thread.sleep(100); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         } >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo. >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println("Pause..."); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         Thread.sleep(400); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo. >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         // 2 long beeps >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println("2 long beeps:"); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         for (int i = 0; i ^< 2; i++) { >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo             System.out.println("  Long BEEP " + (i + 1)); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo             java.awt.Toolkit.getDefaultToolkit().beep(); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo             Thread.sleep(500); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         } >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo. >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println("✅ Swiggy pattern complete!"); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println(); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo     } >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo. >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo     private static void testZomatoPattern() throws InterruptedException { >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println("🔴 ZOMATO ORDER SOUND - 3 Sets of Double Beeps"); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println("Pattern: BEEP-BEEP (pause) BEEP-BEEP (pause) BEEP-BEEP"); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println("Playing in 3 seconds..."); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         Thread.sleep(3000); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo. >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         // 3 sets of double beeps >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         for (int set = 0; set ^< 3; set++) { >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo             System.out.println("Set " + (set + 1) + ":"); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo. >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo             // Double beep >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo             System.out.println("  BEEP 1"); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo             java.awt.Toolkit.getDefaultToolkit().beep(); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo             Thread.sleep(120); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo. >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo             System.out.println("  BEEP 2"); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo             java.awt.Toolkit.getDefaultToolkit().beep(); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo             Thread.sleep(300); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         } >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo. >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println("✅ Zomato pattern complete!"); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println(); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println("🎉 ALL SOUND PATTERNS TESTED!"); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println(); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println("SUMMARY:"); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println("🟠 Swiggy: 4 rapid beeps + 2 long beeps (urgent pattern)"); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println("🔴 Zomato: 3 sets of double beeps (distinctive pattern)"); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println(); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo         System.out.println("These patterns help staff instantly identify which platform has new orders!"); >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo     } >> src\main\java\com\restaurant\util\SimpleBeepTester.java
echo } >> src\main\java\com\restaurant\util\SimpleBeepTester.java

echo ✅ SimpleBeepTester created
echo.

echo 🔧 COMPILING BEEP TESTER...
javac -d target/classes src/main/java/com/restaurant/util/SimpleBeepTester.java

if %ERRORLEVEL% neq 0 (
    echo ❌ Compilation failed
    pause
    exit /b 1
)

echo ✅ Compilation successful
echo.

echo 🎵 STARTING BEEP PATTERN TEST...
echo.
echo Make sure your system volume is turned on!
echo.
echo You will hear:
echo 1. 🟠 Swiggy pattern: 4 rapid beeps + 2 long beeps
echo 2. 🔴 Zomato pattern: 3 sets of double beeps
echo.

java -cp target/classes com.restaurant.util.SimpleBeepTester

echo.
echo 🎉 BEEP PATTERN TEST COMPLETE!
echo.
echo WHAT YOU HEARD:
echo.
echo 🟠 SWIGGY PATTERN:
echo    - 4 rapid beeps (BEEP-BEEP-BEEP-BEEP)
echo    - Short pause
echo    - 2 long beeps (BEEP-BEEP with longer intervals)
echo    - This pattern is urgent and attention-grabbing
echo.
echo 🔴 ZOMATO PATTERN:
echo    - 3 sets of double beeps
echo    - BEEP-BEEP (pause) BEEP-BEEP (pause) BEEP-BEEP
echo    - This pattern is distinctive and recognizable
echo.
echo 🎯 BENEFITS:
echo ✅ Staff can instantly identify Swiggy vs Zomato orders by sound alone
echo ✅ No need to look at screen to know which platform
echo ✅ Different urgency levels for different platforms
echo ✅ Works even in noisy kitchen environments
echo ✅ Professional restaurant-grade notification system
echo.
echo 🔊 MP3 AUDIO UPGRADE:
echo To use custom MP3 files instead of system beeps:
echo 1. Add swiggy-notification.mp3 to sounds/ folder
echo 2. Add zomato-notification.mp3 to sounds/ folder
echo 3. The system will automatically use MP3 files when available
echo 4. Falls back to these beep patterns if MP3 files are missing
echo.
echo Your notification system now has distinctive sounds for each platform!
echo.

pause
