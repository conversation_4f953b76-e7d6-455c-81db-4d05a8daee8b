package com.restaurant.model;

import java.time.LocalDateTime;

public class InventoryItem {
    private int id;
    private String name;
    private double quantity;
    private String unit;
    private String status; // "In Stock", "Low Stock", "Out of Stock"
    private LocalDateTime lastUpdated;
    private String category;
    private double minThreshold;
    private String supplier;
    
    // Constructors
    public InventoryItem() {}
    
    public InventoryItem(int id, String name, double quantity, String unit, String status) {
        this.id = id;
        this.name = name;
        this.quantity = quantity;
        this.unit = unit;
        this.status = status;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public InventoryItem(String name, double quantity, String unit, String category, double minThreshold, String supplier) {
        this.name = name;
        this.quantity = quantity;
        this.unit = unit;
        this.category = category;
        this.minThreshold = minThreshold;
        this.supplier = supplier;
        this.lastUpdated = LocalDateTime.now();
        updateStatus();
    }
    
    // Update status based on quantity and threshold
    public void updateStatus() {
        if (quantity <= 0) {
            this.status = "Out of Stock";
        } else if (quantity <= minThreshold) {
            this.status = "Low Stock";
        } else {
            this.status = "In Stock";
        }
    }
    
    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public double getQuantity() { return quantity; }
    public void setQuantity(double quantity) { 
        this.quantity = quantity;
        updateStatus();
        this.lastUpdated = LocalDateTime.now();
    }
    
    public String getUnit() { return unit; }
    public void setUnit(String unit) { this.unit = unit; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public LocalDateTime getLastUpdated() { return lastUpdated; }
    public void setLastUpdated(LocalDateTime lastUpdated) { this.lastUpdated = lastUpdated; }
    
    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }
    
    public double getMinThreshold() { return minThreshold; }
    public void setMinThreshold(double minThreshold) { 
        this.minThreshold = minThreshold;
        updateStatus();
    }
    
    public String getSupplier() { return supplier; }
    public void setSupplier(String supplier) { this.supplier = supplier; }
    
    // Helper methods
    public String getQuantityDisplay() {
        return String.format("%.1f %s available", quantity, unit);
    }
    
    public String getTimeAgo() {
        // Simple time ago calculation
        LocalDateTime now = LocalDateTime.now();
        long hours = java.time.Duration.between(lastUpdated, now).toHours();
        
        if (hours < 1) {
            return "Just now";
        } else if (hours < 24) {
            return hours + " hour" + (hours > 1 ? "s" : "") + " ago";
        } else {
            long days = hours / 24;
            return days + " day" + (days > 1 ? "s" : "") + " ago";
        }
    }
    
    @Override
    public String toString() {
        return "InventoryItem{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", quantity=" + quantity +
                ", unit='" + unit + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
