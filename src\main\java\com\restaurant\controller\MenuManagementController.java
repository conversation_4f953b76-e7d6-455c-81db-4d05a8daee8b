package com.restaurant.controller;

import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Parent;
import javafx.scene.control.*;
import javafx.scene.input.KeyCode;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.text.Font;
import javafx.stage.Stage;

import com.restaurant.controller.ScrollPaneManager;
import com.restaurant.util.UniversalNavigationManager;
import java.net.URL;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.Collectors;
import com.restaurant.util.MenuShortcuts;

public class MenuManagementController implements Initializable {
    
    // FXML Components
    @FXML private Button backButton;
    @FXML private Label menuInfoLabel;
    @FXML private Button allItemsTab, beveragesTab, burgersTab, chickenTab, chineseTab;
    @FXML private TextField searchField;
    @FXML private ListView<String> categoriesList;
    @FXML private GridPane menuGrid;
    @FXML private Label itemCountLabel;
    
    // Form Components
    @FXML private VBox itemFormContainer;
    @FXML private TextField itemNameField;
    @FXML private TextField itemPriceField;
    @FXML private ComboBox<String> itemCategoryComboBox;
    @FXML private TextArea itemDescriptionArea;
    @FXML private CheckBox itemAvailableCheckBox;
    @FXML private Label totalItemsLabel, availableItemsLabel, categoriesCountLabel;
    @FXML private Button saveItemButton, updateItemButton, clearFormButton;
    
    // Data
    private ObservableList<MenuItem> menuItems = FXCollections.observableArrayList();
    private String currentCategory = "All Items";
    private MenuItem editingItem = null;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        setupSampleData();
        setupCategories();
        setupComboBoxes();
        setupEscapeKeyHandler();
        displayMenuItems();
        updateSummaryLabels();

        // Configure all scroll panes for proper movement
        Platform.runLater(() -> {
            ScrollPaneManager.getInstance().configureAllScrollPanes(menuGrid.getScene().getRoot());
            System.out.println("MenuManagement scroll panes configured");

            // Setup responsive layout listener
            setupResponsiveLayout();
        });

        // Register with universal navigation manager
        UniversalNavigationManager.getInstance().setCurrentController("MenuManagementController");
    }
    
    private void setupSampleData() {
        // Clear existing items and add new menu items from the restaurant menu
        menuItems.clear();

        // Tandoori Roti
        menuItems.addAll(List.of(
            new MenuItem(1, "Roti", 20.00, "Tandoori Roti", "Traditional Indian bread", true),
            new MenuItem(2, "Butter Roti", 25.00, "Tandoori Roti", "Butter-topped roti", true),
            new MenuItem(3, "Naan", 35.00, "Tandoori Roti", "Soft leavened bread", true),
            new MenuItem(4, "Butter Naan", 40.00, "Tandoori Roti", "Butter-topped naan", true),
            new MenuItem(5, "Garlic Naan", 70.00, "Tandoori Roti", "Garlic-flavored naan", true),
            new MenuItem(6, "Khulcha", 40.00, "Tandoori Roti", "Stuffed bread", true),
            new MenuItem(7, "Butter Khulcha", 45.00, "Tandoori Roti", "Butter-topped khulcha", true),
            new MenuItem(8, "Lacha Paratha", 45.00, "Tandoori Roti", "Layered paratha", true),
            new MenuItem(9, "Butter Paratha", 45.00, "Tandoori Roti", "Butter-topped paratha", true),
            new MenuItem(10, "Aloo Paratha", 70.00, "Tandoori Roti", "Potato-stuffed paratha", true),
            new MenuItem(11, "Chi. Kheema Paratha", 120.00, "Tandoori Roti", "Chicken mince paratha", true),
            new MenuItem(12, "Paneer Paratha", 120.00, "Tandoori Roti", "Paneer-stuffed paratha", true),
            new MenuItem(13, "Veg Kheema Paratha", 120.00, "Tandoori Roti", "Vegetable mince paratha", true)
        ));

        // Biryani (Veg/Non Veg)
        menuItems.addAll(List.of(
            new MenuItem(14, "Steam Basmati Rice (H/F)", 60.00, "Biryani", "Steamed basmati rice", true),
            new MenuItem(15, "Jeera Rice (H/F)", 70.00, "Biryani", "Cumin-flavored rice", true),
            new MenuItem(16, "Veg Pulao", 170.00, "Biryani", "Vegetable pulao", true),
            new MenuItem(17, "Veg Biryani", 180.00, "Biryani", "Vegetable biryani", true),
            new MenuItem(18, "Egg Biryani", 190.00, "Biryani", "Egg biryani", true),
            new MenuItem(19, "Paneer Pulav", 180.00, "Biryani", "Paneer pulao", true),
            new MenuItem(20, "Paneer Biryani", 190.00, "Biryani", "Paneer biryani", true),
            new MenuItem(21, "Chicken Biryani", 220.00, "Biryani", "Chicken biryani", true),
            new MenuItem(22, "Chi. Dum Biryani", 230.00, "Biryani", "Chicken dum biryani", true),
            new MenuItem(23, "Chicken Hyderabadi Biryani", 230.00, "Biryani", "Hyderabadi style chicken biryani", true),
            new MenuItem(24, "Chicken Tikka Biryani", 250.00, "Biryani", "Chicken tikka biryani", true),
            new MenuItem(25, "Mutton Biryani", 280.00, "Biryani", "Mutton biryani", true),
            new MenuItem(26, "Mutton Dum Biryani", 300.00, "Biryani", "Mutton dum biryani", true),
            new MenuItem(27, "Mutton Hyderabadi Biryani", 300.00, "Biryani", "Hyderabadi style mutton biryani", true)
        ));

        // Tandoori (Veg)
        menuItems.addAll(List.of(
            new MenuItem(28, "Paneer Tikka", 200.00, "Tandoori", "Grilled paneer cubes", true),
            new MenuItem(29, "Paneer Malai Tikka", 220.00, "Tandoori", "Creamy paneer tikka", true),
            new MenuItem(30, "Veg Seek Kabab", 190.00, "Tandoori", "Vegetable seekh kabab", true),
            new MenuItem(31, "Mushroom Tikka", 200.00, "Tandoori", "Grilled mushroom", true),
            new MenuItem(32, "Baby Corn Tikka", 190.00, "Tandoori", "Grilled baby corn", true),
            new MenuItem(33, "Chilly Milly Kabab", 200.00, "Tandoori", "Spicy mixed vegetable kabab", true)
        ));

        // Tandoori Chicken
        menuItems.addAll(List.of(
            new MenuItem(34, "Chicken Tikka", 210.00, "Tandoori", "Grilled chicken pieces", true),
            new MenuItem(35, "Chicken Tandoori Half", 210.00, "Tandoori", "Half tandoori chicken", true),
            new MenuItem(36, "Chicken Tandoori Full", 400.00, "Tandoori", "Full tandoori chicken", true),
            new MenuItem(37, "Chicken Pahadi Tandoori Half", 220.00, "Tandoori", "Half pahadi tandoori chicken", true),
            new MenuItem(38, "Chicken Pahadi Tandoori Full", 410.00, "Tandoori", "Full pahadi tandoori chicken", true),
            new MenuItem(39, "Chicken Lemon Tandoori Half", 240.00, "Tandoori", "Half lemon tandoori chicken", true),
            new MenuItem(40, "Chicken Lemon Tandoori Full", 420.00, "Tandoori", "Full lemon tandoori chicken", true),
            new MenuItem(41, "Chicken Kalimiri Kabab", 260.00, "Tandoori", "Black pepper chicken kabab", true),
            new MenuItem(42, "Chicken Banjara Kabab", 260.00, "Tandoori", "Banjara style chicken kabab", true),
            new MenuItem(43, "Chicken Sholay Kabab", 260.00, "Tandoori", "Sholay style chicken kabab", true),
            new MenuItem(44, "Chicken Sikkh Kabab", 280.00, "Tandoori", "Chicken seekh kabab", true),
            new MenuItem(45, "Chicken Tangri Kabab", 200.00, "Tandoori", "Chicken drumstick kabab", true),
            new MenuItem(46, "Chicken Rajwadi Kabab", 300.00, "Tandoori", "Royal style chicken kabab", true)
        ));

        // Papad & Salad
        menuItems.addAll(List.of(
            new MenuItem(47, "Roasted Papad", 20.00, "Papad & Salad", "Roasted papad", true),
            new MenuItem(48, "Fry Papad", 25.00, "Papad & Salad", "Fried papad", true),
            new MenuItem(49, "Masala Papad", 50.00, "Papad & Salad", "Spiced papad", true),
            new MenuItem(50, "Green Salad", 90.00, "Papad & Salad", "Fresh green salad", true),
            new MenuItem(51, "Raita", 100.00, "Papad & Salad", "Yogurt-based side dish", true),
            new MenuItem(52, "Boondi Raita", 130.00, "Papad & Salad", "Boondi raita", true),
            new MenuItem(53, "Schzewan Sauce Extra", 20.00, "Papad & Salad", "Extra schzewan sauce", true),
            new MenuItem(54, "Fry Noodles Extra", 20.00, "Papad & Salad", "Extra fried noodles", true)
        ));

        // Mutton Gravy
        menuItems.addAll(List.of(
            new MenuItem(55, "Mutton Masala", 330.00, "Mutton Gravy", "Spiced mutton curry", true),
            new MenuItem(56, "Mutton Kadhai", 330.00, "Mutton Gravy", "Kadhai-style mutton", true),
            new MenuItem(57, "Mutton Kolhapuri", 330.00, "Mutton Gravy", "Kolhapuri-style mutton", true),
            new MenuItem(58, "Mutton Hyderabadi", 330.00, "Mutton Gravy", "Hyderabadi-style mutton", true),
            new MenuItem(59, "Mutton Handi (Half) 6pcs", 470.00, "Mutton Gravy", "Mutton handi half portion", true),
            new MenuItem(60, "Mutton Handi (Full) 12pcs", 750.00, "Mutton Gravy", "Mutton handi full portion", true),
            new MenuItem(61, "Mutton Do Pyaza", 330.00, "Mutton Gravy", "Mutton with onions", true),
            new MenuItem(62, "Mutton Shukar", 350.00, "Mutton Gravy", "Dry mutton preparation", true)
        ));

        // Sea Food
        menuItems.addAll(List.of(
            new MenuItem(63, "Bangda Fry", 130.00, "Sea Food", "Fried mackerel", true),
            new MenuItem(64, "Bangda Masala", 180.00, "Sea Food", "Spiced mackerel curry", true),
            new MenuItem(65, "Mandeli Oil Fry", 150.00, "Sea Food", "Oil-fried mandeli fish", true),
            new MenuItem(66, "Surmai Tawa Fry", 195.00, "Sea Food", "Tawa-fried surmai", true),
            new MenuItem(67, "Surmai Koliwada", 195.00, "Sea Food", "Koliwada-style surmai", true),
            new MenuItem(68, "Prawns Tawa Fry", 255.00, "Sea Food", "Tawa-fried prawns", true),
            new MenuItem(69, "Prawns Koliwada", 250.00, "Sea Food", "Koliwada-style prawns", true),
            new MenuItem(70, "Surmai Gavan Curry", 195.00, "Sea Food", "Surmai in coconut curry", true),
            new MenuItem(71, "Surmai Masala", 195.00, "Sea Food", "Spiced surmai curry", true)
        ));

        // Bulk Order (Per KG)
        menuItems.addAll(List.of(
            new MenuItem(72, "Paneer Pulav", 800.00, "Bulk Order", "Paneer pulao per kg", true),
            new MenuItem(73, "Veg Biryani", 800.00, "Bulk Order", "Vegetable biryani per kg", true),
            new MenuItem(74, "Chicken Biryani", 1000.00, "Bulk Order", "Chicken biryani per kg", true),
            new MenuItem(75, "Mutton Biryani", 1300.00, "Bulk Order", "Mutton biryani per kg", true),
            new MenuItem(76, "Veg Pulav", 700.00, "Bulk Order", "Vegetable pulao per kg", true),
            new MenuItem(77, "Chicken Masala", 900.00, "Bulk Order", "Chicken masala per kg", true),
            new MenuItem(78, "Jira Rice", 650.00, "Bulk Order", "Jeera rice per kg", true),
            new MenuItem(79, "Steam Rice", 650.00, "Bulk Order", "Steamed rice per kg", true),

            // Additional items from the menu to reach 83 total
            new MenuItem(80, "Chicken Malai Tikka", 240.00, "Tandoori", "Creamy chicken tikka", true),
            new MenuItem(81, "Chicken Seekh Kabab", 280.00, "Tandoori", "Chicken seekh kabab", true),
            new MenuItem(82, "Fish Curry", 220.00, "Sea Food", "Traditional fish curry", true),
            new MenuItem(83, "Prawn Curry", 280.00, "Sea Food", "Spiced prawn curry", true)
        ));

        // Soup (Veg)
        menuItems.addAll(List.of(
            new MenuItem(84, "Manchow Soup", 110.00, "Soup (Veg)", "Spicy vegetable soup", true),
            new MenuItem(85, "Schezwan Soup", 110.00, "Soup (Veg)", "Schezwan flavored soup", true),
            new MenuItem(86, "Noodles Soup", 110.00, "Soup (Veg)", "Noodles in soup", true),
            new MenuItem(87, "Clear Soup", 110.00, "Soup (Veg)", "Light clear soup", true),
            new MenuItem(88, "Hot N Sour Soup", 110.00, "Soup (Veg)", "Hot and sour soup", true)
        ));

        // Soup (Non-Veg)
        menuItems.addAll(List.of(
            new MenuItem(89, "Chicken Manchow Soup", 120.00, "Soup (Non-Veg)", "Chicken manchow soup", true),
            new MenuItem(90, "Chicken Hot N Sour Soup", 120.00, "Soup (Non-Veg)", "Chicken hot and sour soup", true),
            new MenuItem(91, "Chicken Lung Fung Soup", 120.00, "Soup (Non-Veg)", "Chicken lung fung soup", true),
            new MenuItem(92, "Chicken Schezwan Soup", 120.00, "Soup (Non-Veg)", "Chicken schezwan soup", true),
            new MenuItem(93, "Chicken Noodles Soup", 120.00, "Soup (Non-Veg)", "Chicken noodles soup", true),
            new MenuItem(94, "Chicken Clear Soup", 120.00, "Soup (Non-Veg)", "Chicken clear soup", true)
        ));

        // Noodles (Veg)
        menuItems.addAll(List.of(
            new MenuItem(95, "Veg Hakka Noodles", 160.00, "Noodles (Veg)", "Vegetable hakka noodles", true),
            new MenuItem(96, "Veg Schezwan Noodles", 170.00, "Noodles (Veg)", "Schezwan vegetable noodles", true),
            new MenuItem(97, "Veg Singapore Noodles", 190.00, "Noodles (Veg)", "Singapore style veg noodles", true),
            new MenuItem(98, "Veg Hong Kong Noodles", 190.00, "Noodles (Veg)", "Hong Kong style veg noodles", true),
            new MenuItem(99, "Veg Mushroom Noodles", 180.00, "Noodles (Veg)", "Mushroom vegetable noodles", true),
            new MenuItem(100, "Veg Manchurian Noodles", 190.00, "Noodles (Veg)", "Manchurian vegetable noodles", true),
            new MenuItem(101, "Veg Sherpa Noodles", 220.00, "Noodles (Veg)", "Sherpa style veg noodles", true),
            new MenuItem(102, "Veg Triple Sch. Noodles", 220.00, "Noodles (Veg)", "Triple schezwan veg noodles", true),
            new MenuItem(103, "Veg Chilly Garlic Noodles", 220.00, "Noodles (Veg)", "Chilly garlic veg noodles", true)
        ));

        // Noodles (Non-Veg)
        menuItems.addAll(List.of(
            new MenuItem(104, "Egg Hakka Noodles", 160.00, "Noodles (Non-Veg)", "Egg hakka noodles", true),
            new MenuItem(105, "Egg Schezwan Noodles", 170.00, "Noodles (Non-Veg)", "Egg schezwan noodles", true),
            new MenuItem(106, "Chicken Hakka Noodles", 180.00, "Noodles (Non-Veg)", "Chicken hakka noodles", true),
            new MenuItem(107, "Chi. Schezwan Noodles", 190.00, "Noodles (Non-Veg)", "Chicken schezwan noodles", true),
            new MenuItem(108, "Chi. Singapore Noodles", 200.00, "Noodles (Non-Veg)", "Chicken singapore noodles", true),
            new MenuItem(109, "Chi. Hong Kong Noodles", 200.00, "Noodles (Non-Veg)", "Chicken hong kong noodles", true),
            new MenuItem(110, "Chi. Mushroom Noodles", 200.00, "Noodles (Non-Veg)", "Chicken mushroom noodles", true),
            new MenuItem(111, "Chi. Triple Schezwan Noodles", 250.00, "Noodles (Non-Veg)", "Chicken triple schezwan noodles", true),
            new MenuItem(112, "Chi. Sherpa Noodles", 250.00, "Noodles (Non-Veg)", "Chicken sherpa noodles", true),
            new MenuItem(113, "Chi. Thousand Noodles", 280.00, "Noodles (Non-Veg)", "Chicken thousand noodles", true),
            new MenuItem(114, "Chi. Chilly Basil Noodles", 250.00, "Noodles (Non-Veg)", "Chicken chilly basil noodles", true),
            new MenuItem(115, "Chicken Chilly Garlic Noodles", 250.00, "Noodles (Non-Veg)", "Chicken chilly garlic noodles", true)
        ));

        // Rice (Veg)
        menuItems.addAll(List.of(
            new MenuItem(116, "Veg Fry Rice", 170.00, "Rice (Veg)", "Vegetable fried rice", true),
            new MenuItem(117, "Veg Schezwan Rice", 180.00, "Rice (Veg)", "Schezwan vegetable rice", true),
            new MenuItem(118, "Veg Singapore Rice", 190.00, "Rice (Veg)", "Singapore style veg rice", true),
            new MenuItem(119, "Veg Hong Kong Rice", 190.00, "Rice (Veg)", "Hong Kong style veg rice", true),
            new MenuItem(120, "Veg Schezwan Combination Rice", 190.00, "Rice (Veg)", "Schezwan combination veg rice", true),
            new MenuItem(121, "Veg Manchurian Rice", 210.00, "Rice (Veg)", "Manchurian vegetable rice", true),
            new MenuItem(122, "Veg Triple Schoz. Rice", 210.00, "Rice (Veg)", "Triple schezwan veg rice", true),
            new MenuItem(123, "Paneer Fry Rice", 200.00, "Rice (Veg)", "Paneer fried rice", true),
            new MenuItem(124, "Paneer Schezwan Rice", 200.00, "Rice (Veg)", "Paneer schezwan rice", true),
            new MenuItem(125, "Veg Sherpa Rice", 230.00, "Rice (Veg)", "Sherpa style veg rice", true),
            new MenuItem(126, "Veg Thousand Rice", 230.00, "Rice (Veg)", "Thousand vegetable rice", true),
            new MenuItem(127, "Veg Chilly Basil Rice", 200.00, "Rice (Veg)", "Chilly basil veg rice", true)
        ));

        // Rice (Non-Veg)
        menuItems.addAll(List.of(
            new MenuItem(128, "Chi. Schezwan Rice", 190.00, "Rice (Non-Veg)", "Chicken schezwan rice", true),
            new MenuItem(129, "Chi. Singapore Rice", 200.00, "Rice (Non-Veg)", "Chicken singapore rice", true),
            new MenuItem(130, "Chi. Hong Kong Rice", 200.00, "Rice (Non-Veg)", "Chicken hong kong rice", true),
            new MenuItem(131, "Chi. Sez. Combination Rice", 210.00, "Rice (Non-Veg)", "Chicken schezwan combination rice", true),
            new MenuItem(132, "Chi. Burn Garlic Rice", 210.00, "Rice (Non-Veg)", "Chicken burn garlic rice", true),
            new MenuItem(133, "Chi. Chilly Garlic Rice", 210.00, "Rice (Non-Veg)", "Chicken chilly garlic rice", true),
            new MenuItem(134, "Chi. Manchurian Rice", 250.00, "Rice (Non-Veg)", "Chicken manchurian rice", true),
            new MenuItem(135, "Chi. Triple Schoz. Rice", 250.00, "Rice (Non-Veg)", "Chicken triple schezwan rice", true),
            new MenuItem(136, "Chi. Sherpa Rice", 260.00, "Rice (Non-Veg)", "Chicken sherpa rice", true),
            new MenuItem(137, "Chi. Thousand Rice", 300.00, "Rice (Non-Veg)", "Chicken thousand rice", true),
            new MenuItem(138, "Chi. Jadoo Rice", 280.00, "Rice (Non-Veg)", "Chicken jadoo rice", true),
            new MenuItem(139, "Chi. Ginger Garlic Rice", 210.00, "Rice (Non-Veg)", "Chicken ginger garlic rice", true),
            new MenuItem(140, "Chi. Chilly Basil Rice", 220.00, "Rice (Non-Veg)", "Chicken chilly basil rice", true),
            new MenuItem(141, "Egg Fry Rice", 170.00, "Rice (Non-Veg)", "Egg fried rice", true),
            new MenuItem(142, "Egg Schezwan Rice", 180.00, "Rice (Non-Veg)", "Egg schezwan rice", true),
            new MenuItem(143, "Chi. Fry Rice", 180.00, "Rice (Non-Veg)", "Chicken fried rice", true)
        ));

        // Chinese Gravy
        menuItems.addAll(List.of(
            new MenuItem(144, "Manchurian Gravy / Chilly", 180.00, "Chinese Gravy", "Manchurian in gravy", true),
            new MenuItem(145, "Schezwan Gravy", 180.00, "Chinese Gravy", "Schezwan gravy", true),
            new MenuItem(146, "Chilly Gravy", 180.00, "Chinese Gravy", "Chilly gravy", true),
            new MenuItem(147, "Kum Pav Gravy", 180.00, "Chinese Gravy", "Kum pav gravy", true),
            new MenuItem(148, "Hot Garlic Gravy", 190.00, "Chinese Gravy", "Hot garlic gravy", true),
            new MenuItem(149, "Oyster Gravy", 190.00, "Chinese Gravy", "Oyster sauce gravy", true),
            new MenuItem(150, "Paneer Sch. Gravy", 190.00, "Chinese Gravy", "Paneer schezwan gravy", true),
            new MenuItem(151, "Paneer Manch. Gravy", 190.00, "Chinese Gravy", "Paneer manchurian gravy", true),
            new MenuItem(152, "Paneer Chilly Gravy", 190.00, "Chinese Gravy", "Paneer chilly gravy", true)
        ));

        // Indian & Punjabi (Veg)
        menuItems.addAll(List.of(
            new MenuItem(153, "Dal Fry", 130.00, "Indian & Punjabi (Veg)", "Dal fry", true),
            new MenuItem(154, "Dal Tadka", 150.00, "Indian & Punjabi (Veg)", "Dal tadka", true),
            new MenuItem(155, "Dal Palak", 150.00, "Indian & Punjabi (Veg)", "Dal palak", true),
            new MenuItem(156, "Dal Khichadi (1000ML)", 220.00, "Indian & Punjabi (Veg)", "Dal khichadi", true),
            new MenuItem(157, "Palak Khichadi (1000ML)", 240.00, "Indian & Punjabi (Veg)", "Palak khichadi", true),
            new MenuItem(158, "Dal Khichadi Tadka (1000ML)", 240.00, "Indian & Punjabi (Veg)", "Dal khichadi tadka", true),
            new MenuItem(159, "Palak Khichadi Tadka (1000ML)", 240.00, "Indian & Punjabi (Veg)", "Palak khichadi tadka", true),
            new MenuItem(160, "Mix Veg", 180.00, "Indian & Punjabi (Veg)", "Mixed vegetables", true),
            new MenuItem(161, "Veg Kadhai", 190.00, "Indian & Punjabi (Veg)", "Vegetable kadhai", true),
            new MenuItem(162, "Veg Kolhapuri", 190.00, "Indian & Punjabi (Veg)", "Vegetable kolhapuri", true),
            new MenuItem(163, "Veg Tawa", 190.00, "Indian & Punjabi (Veg)", "Vegetable tawa", true),
            new MenuItem(164, "Veg Lajawab", 190.00, "Indian & Punjabi (Veg)", "Vegetable lajawab", true),
            new MenuItem(165, "Veg Chilly Milly", 220.00, "Indian & Punjabi (Veg)", "Vegetable chilly milly", true),
            new MenuItem(166, "Aloo Mutter", 170.00, "Indian & Punjabi (Veg)", "Aloo mutter", true),
            new MenuItem(167, "Veg Handi (Half/Full)", 210.00, "Indian & Punjabi (Veg)", "Vegetable handi", true),
            new MenuItem(168, "Paneer Masala", 200.00, "Indian & Punjabi (Veg)", "Paneer masala", true),
            new MenuItem(169, "Paneer Mutter Masala", 200.00, "Indian & Punjabi (Veg)", "Paneer mutter masala", true),
            new MenuItem(170, "Paneer Butter Masala", 220.00, "Indian & Punjabi (Veg)", "Paneer butter masala", true),
            new MenuItem(171, "Paneer Kadhai", 200.00, "Indian & Punjabi (Veg)", "Paneer kadhai", true),
            new MenuItem(172, "Paneer Bhurji Masala", 220.00, "Indian & Punjabi (Veg)", "Paneer bhurji masala", true),
            new MenuItem(173, "Paneer Mutter", 200.00, "Indian & Punjabi (Veg)", "Paneer mutter", true),
            new MenuItem(174, "Palak Paneer", 200.00, "Indian & Punjabi (Veg)", "Palak paneer", true),
            new MenuItem(175, "Mushroom Masala", 210.00, "Indian & Punjabi (Veg)", "Mushroom masala", true),
            new MenuItem(176, "Mushroom Tikka Masala", 230.00, "Indian & Punjabi (Veg)", "Mushroom tikka masala", true),
            new MenuItem(177, "Lasuni Palak", 190.00, "Indian & Punjabi (Veg)", "Lasuni palak", true),
            new MenuItem(178, "Veg Maratha", 250.00, "Indian & Punjabi (Veg)", "Vegetable maratha", true),
            new MenuItem(179, "Sev Bhaji", 180.00, "Indian & Punjabi (Veg)", "Sev bhaji", true),
            new MenuItem(180, "Masala Fry Masala", 200.00, "Indian & Punjabi (Veg)", "Masala fry masala", true)
        ));

        // Chicken Gravy
        menuItems.addAll(List.of(
            new MenuItem(181, "Chicken Masala", 210.00, "Chicken Gravy", "Chicken masala", true),
            new MenuItem(182, "Chicken Curry", 210.00, "Chicken Gravy", "Chicken curry", true),
            new MenuItem(183, "Chicken Kadhai", 240.00, "Chicken Gravy", "Chicken kadhai", true),
            new MenuItem(184, "Chicken Bhurjani", 240.00, "Chicken Gravy", "Chicken bhurjani", true),
            new MenuItem(185, "Chicken Tawa Masala", 260.00, "Chicken Gravy", "Chicken tawa masala", true),
            new MenuItem(186, "Chicken Gayti Masala", 260.00, "Chicken Gravy", "Chicken gayti masala", true),
            new MenuItem(187, "Chicken Tikka Masala", 260.00, "Chicken Gravy", "Chicken tikka masala", true),
            new MenuItem(188, "Chicken Maratha", 280.00, "Chicken Gravy", "Chicken maratha", true),
            new MenuItem(189, "Chicken Lasuni Masala", 260.00, "Chicken Gravy", "Chicken lasuni masala", true),
            new MenuItem(190, "Chicken Japeta (H/F)", 310.00, "Chicken Gravy", "Chicken japeta", true),
            new MenuItem(191, "Butter Chicken (H/F)", 290.00, "Chicken Gravy", "Butter chicken", true),
            new MenuItem(192, "Chicken Malvani Masala", 260.00, "Chicken Gravy", "Chicken malvani masala", true),
            new MenuItem(193, "Chicken Tikka Lemon Masala", 260.00, "Chicken Gravy", "Chicken tikka lemon masala", true),
            new MenuItem(194, "Chicken Hyderabadi Masala", 260.00, "Chicken Gravy", "Chicken hyderabadi masala", true),
            new MenuItem(195, "Chicken Mughlai", 260.00, "Chicken Gravy", "Chicken mughlai", true),
            new MenuItem(196, "Chicken Pahadi Masala", 260.00, "Chicken Gravy", "Chicken pahadi masala", true),
            new MenuItem(197, "Chicken Handi (Half) 6pcs", 260.00, "Chicken Gravy", "Chicken handi half", true),
            new MenuItem(198, "Chicken Handi (Full) 12pcs", 480.00, "Chicken Gravy", "Chicken handi full", true),
            new MenuItem(199, "Chicken Do Pyaza", 260.00, "Chicken Gravy", "Chicken do pyaza", true)
        ));

        // Starters (Veg)
        menuItems.addAll(List.of(
            new MenuItem(200, "Veg Manchurian / Chilly", 190.00, "Starters (Veg)", "Veg manchurian or chilly", true),
            new MenuItem(201, "Veg Chinese Bhel", 190.00, "Starters (Veg)", "Veg chinese bhel", true),
            new MenuItem(202, "Mushroom Chilly/ Manchurian", 200.00, "Starters (Veg)", "Mushroom chilly or manchurian", true),
            new MenuItem(203, "Paneer Chilly/ Manchurian", 220.00, "Starters (Veg)", "Paneer chilly or manchurian", true),
            new MenuItem(204, "Paneer Crispy", 220.00, "Starters (Veg)", "Paneer crispy", true),
            new MenuItem(205, "Paneer Singapur", 220.00, "Starters (Veg)", "Paneer singapore", true),
            new MenuItem(206, "Veg Crispy", 200.00, "Starters (Veg)", "Veg crispy", true),
            new MenuItem(207, "Crispy Chilly Potato", 220.00, "Starters (Veg)", "Crispy chilly potato", true),
            new MenuItem(208, "Honey Chilly Potato", 220.00, "Starters (Veg)", "Honey chilly potato", true),
            new MenuItem(209, "Paneer Shangai Wok", 250.00, "Starters (Veg)", "Paneer shanghai wok", true),
            new MenuItem(210, "Paneer Schezwan Wok", 250.00, "Starters (Veg)", "Paneer schezwan wok", true),
            new MenuItem(211, "Paneer Chilly Basil Wok", 260.00, "Starters (Veg)", "Paneer chilly basil wok", true),
            new MenuItem(212, "Paneer Honey Chilly Wok", 260.00, "Starters (Veg)", "Paneer honey chilly wok", true),
            new MenuItem(213, "Paneer Kum Pav Wok", 260.00, "Starters (Veg)", "Paneer kum pav wok", true)
        ));

        // Starters (Non-Veg)
        menuItems.addAll(List.of(
            new MenuItem(214, "Chi. Chinese Bhel", 180.00, "Starters (Non-Veg)", "Chicken chinese bhel", true),
            new MenuItem(215, "Chi. Chilly/Manchurian", 220.00, "Starters (Non-Veg)", "Chicken chilly or manchurian", true),
            new MenuItem(216, "Chi. Schezwan", 220.00, "Starters (Non-Veg)", "Chicken schezwan", true),
            new MenuItem(217, "Chi. Chilly Garlic Wok", 230.00, "Starters (Non-Veg)", "Chicken chilly garlic wok", true),
            new MenuItem(218, "Chi. Kum Pav Wok", 260.00, "Starters (Non-Veg)", "Chicken kum pav wok", true),
            new MenuItem(219, "Chi. Crispy", 250.00, "Starters (Non-Veg)", "Chicken crispy", true),
            new MenuItem(220, "Chi. Singapur", 250.00, "Starters (Non-Veg)", "Chicken singapore", true),
            new MenuItem(221, "Chi. Lamba", 250.00, "Starters (Non-Veg)", "Chicken lamba", true),
            new MenuItem(222, "Chi. Oyster Sauces", 250.00, "Starters (Non-Veg)", "Chicken oyster sauces", true),
            new MenuItem(223, "Chi. Black Paper Wok", 250.00, "Starters (Non-Veg)", "Chicken black pepper wok", true),
            new MenuItem(224, "Chi. Lollypop 8 Pc.", 230.00, "Starters (Non-Veg)", "Chicken lollypop 8 pieces", true),
            new MenuItem(225, "Chi. Lollypop Schzwn/Hnypp", 300.00, "Starters (Non-Veg)", "Chicken lollypop schezwan/honey pepper", true),
            new MenuItem(226, "Chi. Honey Chilly", 270.00, "Starters (Non-Veg)", "Chicken honey chilly", true),
            new MenuItem(227, "Chi. Chilly Basil Wok", 270.00, "Starters (Non-Veg)", "Chicken chilly basil wok", true)
        ));

        // Egg Dishes
        menuItems.addAll(List.of(
            new MenuItem(228, "Boiled Egg", 40.00, "Egg Dishes", "Boiled egg", true),
            new MenuItem(229, "Egg Omlete", 50.00, "Egg Dishes", "Egg omelet", true),
            new MenuItem(230, "Egg Bhurji", 110.00, "Egg Dishes", "Egg bhurji", true),
            new MenuItem(231, "Egg Masala", 170.00, "Egg Dishes", "Egg masala", true),
            new MenuItem(232, "Egg Curry", 170.00, "Egg Dishes", "Egg curry", true),
            new MenuItem(233, "Anda Ghotala", 180.00, "Egg Dishes", "Anda ghotala", true)
        ));
    }
    
    private void setupCategories() {
        // Populate categories list
        ObservableList<String> categories = FXCollections.observableArrayList(
            "All Items", "Tandoori Roti", "Biryani", "Tandoori", "Papad & Salad",
            "Mutton Gravy", "Sea Food", "Bulk Order", "Soup (Veg)", "Soup (Non-Veg)",
            "Noodles (Veg)", "Noodles (Non-Veg)", "Rice (Veg)", "Rice (Non-Veg)",
            "Indian & Punjabi (Veg)", "Chinese Gravy", "Chicken Gravy", "Starters (Veg)",
            "Starters (Non-Veg)", "Egg Dishes"
        );
        categoriesList.setItems(categories);
        
        categoriesList.getSelectionModel().selectedItemProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal != null) {
                currentCategory = newVal;
                filterMenuByCategory(newVal);
            }
        });
        
        // Select first category by default
        Platform.runLater(() -> {
            if (!categoriesList.getItems().isEmpty()) {
                categoriesList.getSelectionModel().selectFirst();
            }
        });
    }
    
    private void setupComboBoxes() {
        // Setup category combo box for form
        itemCategoryComboBox.setItems(FXCollections.observableArrayList(
            "Tandoori Roti", "Biryani", "Tandoori", "Papad & Salad", "Mutton Gravy",
            "Sea Food", "Bulk Order", "Soup (Veg)", "Soup (Non-Veg)", "Noodles (Veg)",
            "Noodles (Non-Veg)", "Rice (Veg)", "Rice (Non-Veg)", "Indian & Punjabi (Veg)",
            "Chinese Gravy", "Chicken Gravy", "Starters (Veg)", "Starters (Non-Veg)", "Egg Dishes"
        ));
    }
    
    private void setupEscapeKeyHandler() {
        Platform.runLater(() -> {
            if (backButton != null && backButton.getScene() != null) {
                backButton.getScene().setOnKeyPressed(event -> {
                    if (event.getCode() == KeyCode.ESCAPE) {
                        // Use universal navigation for ESC handling
                        UniversalNavigationManager.getInstance().handleEscapeKey();
                    }
                });
            }
        });
    }
    
    private void filterMenuByCategory(String category) {
        if ("All Items".equals(category)) {
            displayMenuItems(menuItems);
        } else {
            List<MenuItem> filtered = menuItems.stream()
                .filter(item -> category.equals(item.getCategory()))
                .collect(Collectors.toList());
            displayMenuItems(filtered);
        }
    }
    
    private void displayMenuItems() {
        displayMenuItems(menuItems);
    }
    
    private void displayMenuItems(List<MenuItem> items) {
        menuGrid.getChildren().clear();

        String searchText = searchField.getText().toLowerCase().trim();
        List<MenuItem> filteredItems = items.stream()
            .filter(item -> searchText.isEmpty() ||
                   MenuShortcuts.matchesItem(item.getName(), searchText))
            .collect(Collectors.toList());

        // Calculate number of columns based on available space
        int maxColumns = calculateOptimalColumns();

        // Apply responsive CSS classes based on available space
        applyResponsiveStyles();

        int col = 0, row = 0;
        for (MenuItem item : filteredItems) {
            VBox itemCard = createMenuItemCard(item);
            menuGrid.add(itemCard, col, row);

            col++;
            if (col >= maxColumns) {
                col = 0;
                row++;
            }
        }

        updateSummaryLabels();
    }

    /**
     * Setup responsive layout listener to adjust columns when window is resized
     */
    private void setupResponsiveLayout() {
        // Add listener for when scene becomes available
        menuGrid.sceneProperty().addListener((obs, oldScene, newScene) -> {
            if (newScene != null) {
                setupWindowResizeListener(newScene);
            }
        });

        // If scene is already available, setup listener immediately
        if (menuGrid.getScene() != null) {
            setupWindowResizeListener(menuGrid.getScene());
        }
    }

    private void setupWindowResizeListener(javafx.scene.Scene scene) {
        if (scene.getWindow() != null) {
            scene.getWindow().widthProperty().addListener((obs, oldVal, newVal) -> {
                Platform.runLater(() -> {
                    System.out.println("Window resized to width: " + newVal);
                    displayMenuItems();
                });
            });
        }
    }

    /**
     * Calculate optimal number of columns based on available space
     */
    private int calculateOptimalColumns() {
        try {
            // Get the scroll pane that contains the menu grid
            ScrollPane scrollPane = (ScrollPane) menuGrid.getParent().getParent();
            double availableWidth = scrollPane.getWidth();

            // If width is not available yet, try to get it from the scene
            if (availableWidth <= 0 && menuGrid.getScene() != null) {
                // Calculate available width considering the layout structure
                double sceneWidth = menuGrid.getScene().getWidth();
                double categoriesPanelWidth = 200; // Fixed width from FXML
                double managementPanelWidth = 350; // Fixed width from FXML
                availableWidth = sceneWidth - categoriesPanelWidth - managementPanelWidth;
            }

            // If still no width available, use a reasonable default
            if (availableWidth <= 0) {
                return 3; // Default to 3 columns when width is unknown
            }

            // Account for padding and spacing - responsive based on available width
            double padding = 40; // 20px padding on each side
            double itemWidth, spacing;

            // Responsive sizing based on available width
            if (availableWidth > 1200) {
                itemWidth = 180;
                spacing = 20;
            } else if (availableWidth > 900) {
                itemWidth = 160;
                spacing = 15;
            } else if (availableWidth > 600) {
                itemWidth = 140;
                spacing = 12;
            } else {
                itemWidth = 120;
                spacing = 10;
            }

            // Calculate how many items can fit
            double usableWidth = availableWidth - padding;
            int maxColumns = (int) Math.floor((usableWidth + spacing) / (itemWidth + spacing));

            // Ensure we have at least 1 column and at most 5 columns
            maxColumns = Math.max(1, Math.min(5, maxColumns));

            System.out.println("Available width: " + availableWidth + ", Calculated columns: " + maxColumns);
            return maxColumns;

        } catch (Exception e) {
            System.err.println("Error calculating optimal columns: " + e.getMessage());
            return 3; // Fallback to 3 columns
        }
    }

    /**
     * Apply responsive CSS classes based on available space
     */
    private void applyResponsiveStyles() {
        try {
            // Get the scroll pane that contains the menu grid
            ScrollPane scrollPane = (ScrollPane) menuGrid.getParent().getParent();
            double availableWidth = scrollPane.getWidth();

            // If width is not available yet, try to get it from the scene
            if (availableWidth <= 0 && menuGrid.getScene() != null) {
                double sceneWidth = menuGrid.getScene().getWidth();
                double categoriesPanelWidth = 200;
                double managementPanelWidth = 350;
                availableWidth = sceneWidth - categoriesPanelWidth - managementPanelWidth;
            }

            // Remove existing responsive classes
            menuGrid.getStyleClass().removeAll("compact", "small");

            // Apply appropriate CSS classes based on available width
            if (availableWidth > 0) {
                if (availableWidth <= 900) {
                    menuGrid.getStyleClass().add("small");
                    System.out.println("Applied 'small' style for width: " + availableWidth);
                } else if (availableWidth <= 1200) {
                    menuGrid.getStyleClass().add("compact");
                    System.out.println("Applied 'compact' style for width: " + availableWidth);
                } else {
                    System.out.println("Applied default style for width: " + availableWidth);
                }
            }

        } catch (Exception e) {
            System.err.println("Error applying responsive styles: " + e.getMessage());
        }
    }

    /**
     * Apply responsive CSS classes to individual menu item cards
     */
    private void applyCardResponsiveStyles(VBox card) {
        try {
            // Get the scroll pane that contains the menu grid
            ScrollPane scrollPane = (ScrollPane) menuGrid.getParent().getParent();
            double availableWidth = scrollPane.getWidth();

            // If width is not available yet, try to get it from the scene
            if (availableWidth <= 0 && menuGrid.getScene() != null) {
                double sceneWidth = menuGrid.getScene().getWidth();
                double categoriesPanelWidth = 200;
                double managementPanelWidth = 350;
                availableWidth = sceneWidth - categoriesPanelWidth - managementPanelWidth;
            }

            // Remove existing responsive classes
            card.getStyleClass().removeAll("compact", "small");

            // Apply appropriate CSS classes based on available width
            if (availableWidth > 0) {
                if (availableWidth <= 900) {
                    card.getStyleClass().add("small");
                } else if (availableWidth <= 1200) {
                    card.getStyleClass().add("compact");
                }
            }

        } catch (Exception e) {
            System.err.println("Error applying card responsive styles: " + e.getMessage());
        }
    }

    private VBox createMenuItemCard(MenuItem item) {
        VBox card = new VBox();
        card.getStyleClass().add("menu-item-card");

        // Apply responsive styles to individual cards
        applyCardResponsiveStyles(card);

        card.setSpacing(8);
        card.setPadding(new Insets(15));
        
        Label nameLabel = new Label(item.getName());
        nameLabel.getStyleClass().add("item-name");
        nameLabel.setFont(Font.font("System", 12));
        nameLabel.setWrapText(true);
        
        Label priceLabel = new Label("₹" + String.format("%.2f", item.getPrice()));
        priceLabel.getStyleClass().add("item-price");
        
        Label categoryLabel = new Label(item.getCategory());
        categoryLabel.getStyleClass().add("item-category");
        categoryLabel.setStyle("-fx-font-size: 10; -fx-text-fill: #6c757d;");
        
        Label statusLabel = new Label(item.isAvailable() ? "Available" : "Unavailable");
        statusLabel.getStyleClass().add(item.isAvailable() ? "status-available" : "status-unavailable");
        statusLabel.setStyle("-fx-font-size: 10; -fx-padding: 2 6 2 6; -fx-background-radius: 10;");
        
        HBox buttonBox = new HBox(5);
        buttonBox.setAlignment(Pos.CENTER);
        
        Button editButton = new Button("Edit");
        editButton.getStyleClass().add("edit-button");
        editButton.setOnAction(e -> editMenuItem(item));
        
        Button deleteButton = new Button("Delete");
        deleteButton.getStyleClass().add("delete-button");
        deleteButton.setOnAction(e -> deleteMenuItem(item));
        
        buttonBox.getChildren().addAll(editButton, deleteButton);
        
        card.getChildren().addAll(nameLabel, priceLabel, categoryLabel, statusLabel, buttonBox);
        return card;
    }
    
    private void editMenuItem(MenuItem item) {
        editingItem = item;
        itemNameField.setText(item.getName());
        itemPriceField.setText(String.valueOf(item.getPrice()));
        itemCategoryComboBox.setValue(item.getCategory());
        itemDescriptionArea.setText(item.getDescription());
        itemAvailableCheckBox.setSelected(item.isAvailable());
        
        saveItemButton.setVisible(false);
        updateItemButton.setVisible(true);
    }
    
    private void deleteMenuItem(MenuItem item) {
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("Delete Menu Item");
        confirmAlert.setHeaderText(null);
        confirmAlert.setContentText("Are you sure you want to delete " + item.getName() + "?");
        
        confirmAlert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                menuItems.remove(item);
                displayMenuItems();
                showAlert("Deleted", "Menu item deleted successfully");
            }
        });
    }
    
    private void updateSummaryLabels() {
        int total = menuItems.size();
        int available = (int) menuItems.stream().filter(MenuItem::isAvailable).count();
        int categories = (int) menuItems.stream().map(MenuItem::getCategory).distinct().count();
        
        totalItemsLabel.setText(String.valueOf(total));
        availableItemsLabel.setText(String.valueOf(available));
        categoriesCountLabel.setText(String.valueOf(categories));
        itemCountLabel.setText(total + " Items");
    }
    
    // Tab Selection Methods
    @FXML
    private void showAllItems() {
        setActiveTab(allItemsTab);
        currentCategory = "All Items";
        displayMenuItems();
    }
    
    @FXML
    private void showBeverages() {
        setActiveTab(beveragesTab);
        currentCategory = "Beverages";
        filterMenuByCategory("Beverages");
    }
    
    @FXML
    private void showBurgers() {
        setActiveTab(burgersTab);
        currentCategory = "Burgers";
        filterMenuByCategory("Burgers");
    }
    
    @FXML
    private void showChicken() {
        setActiveTab(chickenTab);
        currentCategory = "Chicken";
        filterMenuByCategory("Chicken");
    }
    
    @FXML
    private void showChinese() {
        setActiveTab(chineseTab);
        currentCategory = "Chinese Snacks";
        filterMenuByCategory("Chinese Snacks");
    }
    
    private void setActiveTab(Button activeTab) {
        // Remove active class from all tabs
        allItemsTab.getStyleClass().remove("tab-active");
        beveragesTab.getStyleClass().remove("tab-active");
        burgersTab.getStyleClass().remove("tab-active");
        chickenTab.getStyleClass().remove("tab-active");
        chineseTab.getStyleClass().remove("tab-active");
        
        // Add active class to selected tab
        activeTab.getStyleClass().add("tab-active");
    }
    
    @FXML
    private void searchItems() {
        displayMenuItems();
    }

    @FXML
    private void addNewItem() {
        clearForm();
    }
    
    @FXML
    private void saveItem() {
        if (validateForm()) {
            int newId = menuItems.size() + 1;
            MenuItem newItem = new MenuItem(
                newId,
                itemNameField.getText().trim(),
                Double.parseDouble(itemPriceField.getText().trim()),
                itemCategoryComboBox.getValue(),
                itemDescriptionArea.getText().trim(),
                itemAvailableCheckBox.isSelected()
            );
            
            menuItems.add(newItem);
            displayMenuItems();
            clearForm();
            showAlert("Success", "Menu item added successfully!");
        }
    }
    
    @FXML
    private void updateItem() {
        if (editingItem != null && validateForm()) {
            editingItem.setName(itemNameField.getText().trim());
            editingItem.setPrice(Double.parseDouble(itemPriceField.getText().trim()));
            editingItem.setCategory(itemCategoryComboBox.getValue());
            editingItem.setDescription(itemDescriptionArea.getText().trim());
            editingItem.setAvailable(itemAvailableCheckBox.isSelected());
            
            displayMenuItems();
            clearForm();
            showAlert("Success", "Menu item updated successfully!");
        }
    }
    
    @FXML
    private void clearForm() {
        editingItem = null;
        itemNameField.clear();
        itemPriceField.clear();
        itemCategoryComboBox.setValue(null);
        itemDescriptionArea.clear();
        itemAvailableCheckBox.setSelected(true);
        
        saveItemButton.setVisible(true);
        updateItemButton.setVisible(false);
    }
    
    private boolean validateForm() {
        if (itemNameField.getText().trim().isEmpty()) {
            showAlert("Validation Error", "Please enter item name");
            return false;
        }
        if (itemPriceField.getText().trim().isEmpty()) {
            showAlert("Validation Error", "Please enter item price");
            return false;
        }
        try {
            Double.parseDouble(itemPriceField.getText().trim());
        } catch (NumberFormatException e) {
            showAlert("Validation Error", "Please enter a valid price");
            return false;
        }
        if (itemCategoryComboBox.getValue() == null) {
            showAlert("Validation Error", "Please select a category");
            return false;
        }
        return true;
    }
    
    @FXML
    private void goBack() {
        try {
            // Use universal navigation manager for robust back navigation
            UniversalNavigationManager.getInstance().goBack();
        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Could not go back: " + e.getMessage());
        }
    }
    
    @FXML
    private void openCategoryItemManager() {
        try {
            // Load the Category Item Manager interface
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/CategoryItemManager.fxml"));
            javafx.scene.Parent root = loader.load();

            // Create new stage for category item manager
            javafx.stage.Stage managerStage = new javafx.stage.Stage();
            managerStage.setTitle("Category Item Manager");
            managerStage.setScene(new javafx.scene.Scene(root, 1000, 700));
            managerStage.setMinWidth(900);
            managerStage.setMinHeight(600);

            // Set as modal window
            managerStage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            managerStage.initOwner(searchField.getScene().getWindow());

            managerStage.show();

            System.out.println("Category Item Manager opened successfully");

        } catch (Exception e) {
            System.err.println("Error opening Category Item Manager: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Failed to open Category Item Manager: " + e.getMessage());
        }
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    // Data class for menu items
    public static class MenuItem {
        private int id;
        private String name;
        private double price;
        private String category;
        private String description;
        private boolean available;

        public MenuItem(int id, String name, double price, String category, String description, boolean available) {
            this.id = id;
            this.name = name;
            this.price = price;
            this.category = category;
            this.description = description;
            this.available = available;
        }

        // Getters and Setters
        public int getId() { return id; }
        public void setId(int id) { this.id = id; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public double getPrice() { return price; }
        public void setPrice(double price) { this.price = price; }

        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public boolean isAvailable() { return available; }
        public void setAvailable(boolean available) { this.available = available; }
    }
}
