<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.shape.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.GlobalVoiceController">
   <!-- Global AI Voice Assistant Interface -->
   <VBox fx:id="globalVoiceContainer" styleClass="global-voice-container" spacing="16.0">
      <children>
         <!-- Header Section -->
         <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="global-voice-header">
            <children>
               <!-- AI Assistant Avatar -->
               <StackPane styleClass="ai-avatar-container">
                  <children>
                     <Circle radius="25.0" styleClass="ai-avatar-background" />
                     <Label text="🤖" styleClass="ai-avatar-icon" />
                     <Circle fx:id="aiPulseRing" radius="30.0" styleClass="ai-pulse-ring" visible="false" />
                  </children>
               </StackPane>
               
               <!-- Assistant Info -->
               <VBox spacing="4.0" HBox.hgrow="ALWAYS">
                  <children>
                     <HBox alignment="CENTER_LEFT" spacing="8.0">
                        <children>
                           <Text text="AI Restaurant Assistant" styleClass="ai-assistant-title" />
                           <Label fx:id="contextBadge" text="Dashboard" styleClass="context-badge" />
                        </children>
                     </HBox>
                     <Text fx:id="assistantStatus" styleClass="ai-assistant-status" text="Ready to help with restaurant operations" />
                  </children>
               </VBox>
               
               <!-- Voice Controls -->
               <HBox spacing="8.0" alignment="CENTER_RIGHT">
                  <children>
                     <!-- Main Voice Button -->
                     <StackPane styleClass="main-voice-button-container">
                        <children>
                           <Circle fx:id="mainPulseRing" radius="35.0" styleClass="main-pulse-ring" visible="false" />
                           <Button fx:id="mainVoiceButton" onAction="#handleMainVoiceInput" styleClass="main-voice-button">
                              <graphic>
                                 <Label fx:id="mainVoiceIcon" text="🎤" styleClass="main-voice-icon" />
                              </graphic>
                              <tooltip>
                                 <Tooltip fx:id="mainVoiceTooltip" text="Click to speak or say 'Hey Restaurant'" />
                              </tooltip>
                           </Button>
                        </children>
                     </StackPane>
                     
                     <!-- Settings Menu -->
                     <MenuButton fx:id="voiceSettingsMenu" styleClass="voice-settings-menu" text="⚙️">
                        <items>
                           <CheckMenuItem fx:id="wakeWordEnabled" text="Enable 'Hey Restaurant' wake word" selected="true" />
                           <CheckMenuItem fx:id="continuousListening" text="Continuous listening mode" selected="false" />
                           <CheckMenuItem fx:id="voiceFeedback" text="Voice feedback responses" selected="true" />
                           <SeparatorMenuItem />
                           <MenuItem fx:id="voiceTraining" text="Voice training" onAction="#openVoiceTraining" />
                           <MenuItem fx:id="commandHistory" text="Command history" onAction="#showCommandHistory" />
                           <SeparatorMenuItem />
                           <MenuItem fx:id="voiceHelp" text="Voice commands help" onAction="#showVoiceHelp" />
                        </items>
                     </MenuButton>
                  </children>
               </HBox>
            </children>
         </HBox>
         
         <!-- Voice Input Area -->
         <VBox fx:id="voiceInputArea" styleClass="voice-input-area" visible="false" spacing="12.0">
            <children>
               <!-- Live Transcription -->
               <VBox spacing="8.0">
                  <children>
                     <HBox alignment="CENTER_LEFT" spacing="8.0">
                        <children>
                           <Label text="🎙️" styleClass="transcription-icon" />
                           <Text text="Live Transcription" styleClass="transcription-label" />
                           <Region HBox.hgrow="ALWAYS" />
                           <Label fx:id="confidenceIndicator" text="85%" styleClass="confidence-indicator" />
                        </children>
                     </HBox>
                     
                     <ScrollPane styleClass="transcription-scroll" fitToWidth="true" prefHeight="60.0">
                        <content>
                           <TextArea fx:id="liveTranscription" styleClass="live-transcription" 
                                    promptText="Your speech will appear here in real-time..." 
                                    wrapText="true" editable="true" prefRowCount="2" />
                        </content>
                     </ScrollPane>
                  </children>
               </VBox>
               
               <!-- Intent Recognition Display -->
               <HBox fx:id="intentDisplay" alignment="CENTER_LEFT" spacing="12.0" styleClass="intent-display" visible="false">
                  <children>
                     <Label fx:id="intentIcon" text="🧠" styleClass="intent-icon" />
                     <VBox spacing="4.0" HBox.hgrow="ALWAYS">
                        <children>
                           <HBox alignment="CENTER_LEFT" spacing="8.0">
                              <children>
                                 <Text fx:id="intentCategory" styleClass="intent-category" text="AI Forecasting" />
                                 <Label fx:id="intentConfidence" text="95%" styleClass="intent-confidence" />
                              </children>
                           </HBox>
                           <Text fx:id="intentDescription" styleClass="intent-description" text="Generate sales forecast for next 30 days" />
                        </children>
                     </VBox>
                     <Button fx:id="executeIntentButton" text="▶️ Execute" styleClass="execute-intent-button" onAction="#executeRecognizedIntent" />
                  </children>
               </HBox>
               
               <!-- Action Buttons -->
               <HBox spacing="10.0" alignment="CENTER_LEFT">
                  <children>
                     <Button fx:id="confirmVoiceButton" text="✅ Confirm &amp; Execute" styleClass="confirm-voice-button"
                            onAction="#confirmAndExecute" disable="true" />
                     <Button fx:id="editCommandButton" text="✏️ Edit Command" styleClass="edit-command-button" 
                            onAction="#editCommand" />
                     <Button fx:id="tryAgainVoiceButton" text="🔄 Try Again" styleClass="try-again-voice-button" 
                            onAction="#tryAgainVoice" />
                     <Button fx:id="cancelVoiceButton" text="❌ Cancel" styleClass="cancel-voice-button" 
                            onAction="#cancelVoiceInput" />
                  </children>
               </HBox>
            </children>
         </VBox>
         
         <!-- Contextual Suggestions -->
         <VBox fx:id="suggestionsSection" styleClass="suggestions-section" spacing="10.0">
            <children>
               <HBox alignment="CENTER_LEFT" spacing="8.0">
                  <children>
                     <Label text="💡" styleClass="suggestions-icon" />
                     <Text text="Suggested Commands" styleClass="suggestions-title" />
                     <Region HBox.hgrow="ALWAYS" />
                     <Button fx:id="refreshSuggestions" text="🔄" styleClass="refresh-suggestions-button" 
                            onAction="#refreshContextualSuggestions" />
                  </children>
               </HBox>
               
               <FlowPane fx:id="contextualSuggestions" hgap="8.0" vgap="6.0" styleClass="contextual-suggestions">
                  <!-- Dynamic suggestion buttons will be added here -->
               </FlowPane>
            </children>
         </VBox>
         
         <!-- Command Execution Result -->
         <VBox fx:id="executionResult" styleClass="execution-result" visible="false" spacing="8.0">
            <children>
               <HBox alignment="CENTER_LEFT" spacing="10.0">
                  <children>
                     <Label fx:id="executionIcon" text="✅" styleClass="execution-icon" />
                     <VBox spacing="4.0" HBox.hgrow="ALWAYS">
                        <children>
                           <Text fx:id="executionTitle" styleClass="execution-title" text="Command Executed Successfully" />
                           <Text fx:id="executionDescription" styleClass="execution-description" text="Sales forecast generated for next 30 days" />
                        </children>
                     </VBox>
                     <Button fx:id="viewResultButton" text="👁️ View Result" styleClass="view-result-button" onAction="#viewExecutionResult" />
                  </children>
               </HBox>
            </children>
         </VBox>
         
         <!-- Error Display -->
         <VBox fx:id="errorDisplay" styleClass="error-display" visible="false" spacing="8.0">
            <children>
               <HBox alignment="CENTER_LEFT" spacing="10.0">
                  <children>
                     <Label fx:id="errorIcon" text="❌" styleClass="error-icon" />
                     <VBox spacing="4.0" HBox.hgrow="ALWAYS">
                        <children>
                           <Text fx:id="errorTitle" styleClass="error-title" text="Command Failed" />
                           <Text fx:id="errorDescription" styleClass="error-description" text="Could not understand the voice command. Please try again." />
                        </children>
                     </VBox>
                     <Button fx:id="retryErrorButton" text="🔄 Retry" styleClass="retry-error-button" onAction="#retryFailedCommand" />
                  </children>
               </HBox>
            </children>
         </VBox>
         
         <!-- Quick Actions Bar -->
         <HBox fx:id="quickActionsBar" alignment="CENTER" spacing="12.0" styleClass="quick-actions-bar">
            <children>
               <Button fx:id="quickForecast" text="📊 Forecast" styleClass="quick-action-button" 
                      onAction="#quickForecastAction" userData="forecast sales for next 30 days" />
               <Button fx:id="quickSales" text="💰 Today's Sales" styleClass="quick-action-button" 
                      onAction="#quickSalesAction" userData="show today's sales" />
               <Button fx:id="quickTables" text="🪑 Tables" styleClass="quick-action-button" 
                      onAction="#quickTablesAction" userData="go to table management" />
               <Button fx:id="quickMenu" text="🔍 Search Menu" styleClass="quick-action-button" 
                      onAction="#quickMenuAction" userData="search menu items" />
               <Button fx:id="quickStock" text="📦 Stock" styleClass="quick-action-button" 
                      onAction="#quickStockAction" userData="show low stock items" />
            </children>
         </HBox>
         
         <!-- Voice Activity Indicator -->
         <HBox fx:id="voiceActivityIndicator" alignment="CENTER" spacing="8.0" styleClass="voice-activity-indicator" visible="false">
            <children>
               <Circle fx:id="activityDot1" radius="4.0" styleClass="activity-dot" />
               <Circle fx:id="activityDot2" radius="4.0" styleClass="activity-dot" />
               <Circle fx:id="activityDot3" radius="4.0" styleClass="activity-dot" />
               <Text text="Listening for 'Hey Restaurant'..." styleClass="activity-text" />
            </children>
         </HBox>
      </children>
   </VBox>
</VBox>
