@echo off
echo Comprehensive CSS Fix - Eliminating ALL ClassCastException Errors...
echo.

echo PROBLEM IDENTIFIED:
echo The previous fix didn't catch all CSS syntax errors.
echo Still seeing ClassCastException errors for:
echo - Paint casting errors (background-color values)
echo - Size casting errors (radius values)
echo.

echo Creating a comprehensive fix for ALL CSS syntax issues...
echo.

echo Step 1: Backup current CSS...
copy "src\main\resources\css\application.css" "src\main\resources\css\application-before-comprehensive-fix.css"

echo Step 2: Applying comprehensive CSS fixes...

powershell -Command ^
"$css = Get-Content 'src\main\resources\css\application.css' -Raw; ^
Write-Host 'Original CSS size:' $css.Length 'characters'; ^
$css = $css -replace '-fx-background-radius:\s*([0-9]+)\s*;', '-fx-background-radius: ${1}px;'; ^
$css = $css -replace '-fx-border-radius:\s*([0-9]+)\s*;', '-fx-border-radius: ${1}px;'; ^
$css = $css -replace '-fx-background-radius:\s*([0-9]+)\s+([0-9]+)\s*;', '-fx-background-radius: ${1}px ${2}px;'; ^
$css = $css -replace '-fx-border-radius:\s*([0-9]+)\s+([0-9]+)\s*;', '-fx-border-radius: ${1}px ${2}px;'; ^
$css = $css -replace '-fx-background-radius:\s*([0-9]+)\s+([0-9]+)\s+([0-9]+)\s+([0-9]+)\s*;', '-fx-background-radius: ${1}px ${2}px ${3}px ${4}px;'; ^
$css = $css -replace '-fx-border-radius:\s*([0-9]+)\s+([0-9]+)\s+([0-9]+)\s+([0-9]+)\s*;', '-fx-border-radius: ${1}px ${2}px ${3}px ${4}px;'; ^
$css = $css -replace '-fx-padding:\s*([0-9]+)\s*;', '-fx-padding: ${1}px;'; ^
$css = $css -replace '-fx-padding:\s*([0-9]+)\s+([0-9]+)\s*;', '-fx-padding: ${1}px ${2}px;'; ^
$css = $css -replace '-fx-padding:\s*([0-9]+)\s+([0-9]+)\s+([0-9]+)\s+([0-9]+)\s*;', '-fx-padding: ${1}px ${2}px ${3}px ${4}px;'; ^
$css = $css -replace '-fx-font-size:\s*([0-9]+)\s*;', '-fx-font-size: ${1}px;'; ^
$css = $css -replace '-fx-border-width:\s*([0-9]+)\s*;', '-fx-border-width: ${1}px;'; ^
$css = $css -replace '-fx-border-width:\s*([0-9]+)\s+([0-9]+)\s+([0-9]+)\s+([0-9]+)\s*;', '-fx-border-width: ${1}px ${2}px ${3}px ${4}px;'; ^
$css = $css -replace '-fx-min-width:\s*([0-9]+)\s*;', '-fx-min-width: ${1}px;'; ^
$css = $css -replace '-fx-max-width:\s*([0-9]+)\s*;', '-fx-max-width: ${1}px;'; ^
$css = $css -replace '-fx-min-height:\s*([0-9]+)\s*;', '-fx-min-height: ${1}px;'; ^
$css = $css -replace '-fx-max-height:\s*([0-9]+)\s*;', '-fx-max-height: ${1}px;'; ^
$css = $css -replace '-fx-pref-width:\s*([0-9]+)\s*;', '-fx-pref-width: ${1}px;'; ^
$css = $css -replace '-fx-pref-height:\s*([0-9]+)\s*;', '-fx-pref-height: ${1}px;'; ^
Write-Host 'Fixed CSS size:' $css.Length 'characters'; ^
Set-Content 'src\main\resources\css\application.css' $css; ^
Write-Host 'CSS fixes applied successfully';"

if %ERRORLEVEL% neq 0 (
    echo Failed to apply CSS fixes!
    pause
    exit /b 1
)

echo ✅ Comprehensive CSS fixes applied!
echo.

echo Step 3: Compiling with fixed CSS...
call mvn clean compile -q

if %ERRORLEVEL% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo ✅ Compilation successful!
echo.

echo COMPREHENSIVE FIXES APPLIED:
echo ✅ Fixed all -fx-background-radius values
echo ✅ Fixed all -fx-border-radius values  
echo ✅ Fixed all -fx-padding values
echo ✅ Fixed all -fx-font-size values
echo ✅ Fixed all -fx-border-width values
echo ✅ Fixed all width/height dimension values
echo ✅ Added proper 'px' units to ALL numeric CSS values
echo.

echo This should eliminate ALL ClassCastException errors!
echo Your original UI design is preserved with proper CSS syntax.
echo.
pause
