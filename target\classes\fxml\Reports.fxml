<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<ScrollPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.ReportsController"
            stylesheets="@../css/application.css" styleClass="reports-scroll-pane" fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED">
   <content>
      <VBox styleClass="reports-container" spacing="0.0">
         <children>
            <!-- Header Section with Proper Grid Layout -->
            <VBox styleClass="reports-header-section">
               <children>
                  <!-- Title Row -->
                  <HBox styleClass="reports-title-row" alignment="CENTER_LEFT">
                     <children>
                        <Label styleClass="page-title" text="Reports &amp; Analytics">
                           <font>
                              <Font name="System Bold" size="28.0" />
                           </font>
                        </Label>
                        <Region HBox.hgrow="ALWAYS" />
                        <!-- Navigation Icons -->
                        <HBox styleClass="nav-icons-container" spacing="10.0" alignment="CENTER_RIGHT">
                           <children>
                              <Button fx:id="homeBtn" mnemonicParsing="false" onAction="#goToHome" styleClass="nav-icon-button" text="🏠">
                                 <tooltip>
                                    <Tooltip text="Go to Dashboard Home" />
                                 </tooltip>
                              </Button>
                              <Button fx:id="notificationsBtn" mnemonicParsing="false" onAction="#showNotifications" styleClass="nav-icon-button" text="🔔">
                                 <tooltip>
                                    <Tooltip text="View Notifications" />
                                 </tooltip>
                              </Button>
                           </children>
                        </HBox>
                     </children>
                  </HBox>

                  <!-- Filters and Controls Row -->
                  <HBox styleClass="reports-filters-row" alignment="CENTER_LEFT" spacing="15.0">
                     <children>
                        <Label styleClass="filter-label" text="Date Range:" />
                        <DatePicker fx:id="fromDatePicker" promptText="From Date" styleClass="date-picker-field" />
                        <Label styleClass="filter-separator" text="to" />
                        <DatePicker fx:id="toDatePicker" promptText="To Date" styleClass="date-picker-field" />
                        <Region HBox.hgrow="ALWAYS" />
                        <HBox styleClass="action-buttons-container" spacing="10.0" alignment="CENTER_RIGHT">
                           <children>
                              <Button mnemonicParsing="false" onAction="#generateReports" styleClass="primary-action-button" text="📊 Generate Reports" />
                              <Button mnemonicParsing="false" onAction="#exportSummaryReport" styleClass="secondary-action-button" text="📄 Export Summary" />
                           </children>
                        </HBox>
                     </children>
                  </HBox>
               </children>
            </VBox>

            <!-- Summary Cards Section with Grid Layout -->
            <VBox styleClass="summary-section">
               <children>
                  <Label styleClass="section-header" text="Performance Overview">
                     <font>
                        <Font name="System Bold" size="20.0" />
                     </font>
                  </Label>

                  <!-- Summary Cards Grid -->
                  <GridPane styleClass="summary-cards-grid" hgap="20.0" vgap="20.0">
                     <columnConstraints>
                        <ColumnConstraints hgrow="ALWAYS" minWidth="200.0" prefWidth="300.0" />
                        <ColumnConstraints hgrow="ALWAYS" minWidth="200.0" prefWidth="300.0" />
                        <ColumnConstraints hgrow="ALWAYS" minWidth="200.0" prefWidth="300.0" />
                        <ColumnConstraints hgrow="ALWAYS" minWidth="200.0" prefWidth="300.0" />
                     </columnConstraints>
                     <rowConstraints>
                        <RowConstraints minHeight="120.0" prefHeight="140.0" vgrow="SOMETIMES" />
                     </rowConstraints>
                     <children>
                        <!-- Total Sales Card -->
                        <VBox styleClass="summary-card" alignment="CENTER" spacing="12.0" GridPane.columnIndex="0" GridPane.rowIndex="0">
                           <children>
                              <Label styleClass="card-icon" text="💰" />
                              <Label styleClass="card-title" text="Total Sales">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label fx:id="totalSalesLabel" styleClass="card-value" text="₹0.00">
                                 <font>
                                    <Font name="System Bold" size="24.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>

                        <!-- Total Orders Card -->
                        <VBox styleClass="summary-card" alignment="CENTER" spacing="12.0" GridPane.columnIndex="1" GridPane.rowIndex="0">
                           <children>
                              <Label styleClass="card-icon" text="📋" />
                              <Label styleClass="card-title" text="Total Orders">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label fx:id="totalOrdersLabel" styleClass="card-value" text="0">
                                 <font>
                                    <Font name="System Bold" size="24.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>

                        <!-- Average Order Card -->
                        <VBox styleClass="summary-card" alignment="CENTER" spacing="12.0" GridPane.columnIndex="2" GridPane.rowIndex="0">
                           <children>
                              <Label styleClass="card-icon" text="📊" />
                              <Label styleClass="card-title" text="Average Order">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label fx:id="averageOrderLabel" styleClass="card-value" text="₹0.00">
                                 <font>
                                    <Font name="System Bold" size="24.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>

                        <!-- Peak Hours Card -->
                        <VBox styleClass="summary-card" alignment="CENTER" spacing="12.0" GridPane.columnIndex="3" GridPane.rowIndex="0">
                           <children>
                              <Label styleClass="card-icon" text="⏰" />
                              <Label styleClass="card-title" text="Peak Hours">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Label fx:id="peakHoursLabel" styleClass="card-value" text="12-2 PM">
                                 <font>
                                    <Font name="System Bold" size="18.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>
                     </children>
                  </GridPane>
               </children>
            </VBox>

            <!-- Reports Grid Section -->
            <VBox styleClass="reports-grid-section">
               <children>
                  <Label styleClass="section-header" text="Detailed Reports">
                     <font>
                        <Font name="System Bold" size="20.0" />
                     </font>
                  </Label>

                  <!-- Reports Grid -->
                  <GridPane styleClass="reports-grid" hgap="25.0" vgap="25.0">
                     <columnConstraints>
                        <ColumnConstraints hgrow="ALWAYS" minWidth="400.0" prefWidth="500.0" />
                        <ColumnConstraints hgrow="ALWAYS" minWidth="400.0" prefWidth="500.0" />
                     </columnConstraints>
                     <rowConstraints>
                        <RowConstraints minHeight="350.0" prefHeight="400.0" vgrow="ALWAYS" />
                        <RowConstraints minHeight="350.0" prefHeight="400.0" vgrow="ALWAYS" />
                     </rowConstraints>
                     <children>
                        <!-- Daily Sales Report -->
                        <VBox styleClass="report-card" spacing="15.0" GridPane.columnIndex="0" GridPane.rowIndex="0">
                           <children>
                              <HBox styleClass="report-card-header" alignment="CENTER_LEFT" spacing="10.0">
                                 <children>
                                    <Label styleClass="report-icon" text="📈" />
                                    <Label styleClass="report-title" text="Daily Sales Report">
                                       <font>
                                          <Font name="System Bold" size="16.0" />
                                       </font>
                                    </Label>
                                    <Region HBox.hgrow="ALWAYS" />
                                    <Button mnemonicParsing="false" onAction="#exportDailySales" styleClass="export-button-small" text="📄" />
                                 </children>
                              </HBox>

                              <ScrollPane styleClass="table-scroll-pane" fitToWidth="true" VBox.vgrow="ALWAYS">
                                 <content>
                                    <TableView fx:id="dailySalesTable" styleClass="report-table">
                                       <columns>
                                          <TableColumn fx:id="dateColumn" prefWidth="120.0" text="Date" />
                                          <TableColumn fx:id="ordersColumn" prefWidth="80.0" text="Orders" />
                                          <TableColumn fx:id="salesColumn" prefWidth="100.0" text="Sales" />
                                          <TableColumn fx:id="avgOrderColumn" prefWidth="100.0" text="Avg Order" />
                                       </columns>
                                    </TableView>
                                 </content>
                              </ScrollPane>

                              <HBox styleClass="report-card-footer" alignment="CENTER_RIGHT" spacing="10.0">
                                 <children>
                                    <Button mnemonicParsing="false" onAction="#exportDailySales" styleClass="export-button" text="Export to PDF" />
                                 </children>
                              </HBox>
                           </children>
                        </VBox>

                        <!-- Popular Items Report -->
                        <VBox styleClass="report-card" spacing="15.0" GridPane.columnIndex="1" GridPane.rowIndex="0">
                           <children>
                              <HBox styleClass="report-card-header" alignment="CENTER_LEFT" spacing="10.0">
                                 <children>
                                    <Label styleClass="report-icon" text="🏆" />
                                    <Label styleClass="report-title" text="Popular Items">
                                       <font>
                                          <Font name="System Bold" size="16.0" />
                                       </font>
                                    </Label>
                                    <Region HBox.hgrow="ALWAYS" />
                                    <Button mnemonicParsing="false" onAction="#exportPopularItems" styleClass="export-button-small" text="📄" />
                                 </children>
                              </HBox>

                              <ScrollPane styleClass="table-scroll-pane" fitToWidth="true" VBox.vgrow="ALWAYS">
                                 <content>
                                    <TableView fx:id="popularItemsTable" styleClass="report-table">
                                       <columns>
                                          <TableColumn fx:id="itemNameColumn" prefWidth="180.0" text="Item Name" />
                                          <TableColumn fx:id="quantitySoldColumn" prefWidth="100.0" text="Qty Sold" />
                                          <TableColumn fx:id="revenueColumn" prefWidth="120.0" text="Revenue" />
                                       </columns>
                                    </TableView>
                                 </content>
                              </ScrollPane>

                              <HBox styleClass="report-card-footer" alignment="CENTER_RIGHT" spacing="10.0">
                                 <children>
                                    <Button mnemonicParsing="false" onAction="#exportPopularItems" styleClass="export-button" text="Export to PDF" />
                                 </children>
                              </HBox>
                           </children>
                        </VBox>

                        <!-- Monthly Trends Report -->
                        <VBox styleClass="report-card" spacing="15.0" GridPane.columnIndex="0" GridPane.rowIndex="1">
                           <children>
                              <HBox styleClass="report-card-header" alignment="CENTER_LEFT" spacing="10.0">
                                 <children>
                                    <Label styleClass="report-icon" text="📅" />
                                    <Label styleClass="report-title" text="Monthly Trends">
                                       <font>
                                          <Font name="System Bold" size="16.0" />
                                       </font>
                                    </Label>
                                    <Region HBox.hgrow="ALWAYS" />
                                    <Button mnemonicParsing="false" onAction="#exportMonthlyTrends" styleClass="export-button-small" text="📄" />
                                 </children>
                              </HBox>

                              <ScrollPane styleClass="table-scroll-pane" fitToWidth="true" VBox.vgrow="ALWAYS">
                                 <content>
                                    <TableView fx:id="monthlyTrendsTable" styleClass="report-table">
                                       <columns>
                                          <TableColumn fx:id="monthColumn" prefWidth="100.0" text="Month" />
                                          <TableColumn fx:id="monthOrdersColumn" prefWidth="80.0" text="Orders" />
                                          <TableColumn fx:id="monthSalesColumn" prefWidth="120.0" text="Sales" />
                                          <TableColumn fx:id="monthGrowthColumn" prefWidth="100.0" text="Growth %" />
                                       </columns>
                                    </TableView>
                                 </content>
                              </ScrollPane>

                              <HBox styleClass="report-card-footer" alignment="CENTER_RIGHT" spacing="10.0">
                                 <children>
                                    <Button mnemonicParsing="false" onAction="#exportMonthlyTrends" styleClass="export-button" text="Export to PDF" />
                                 </children>
                              </HBox>
                           </children>
                        </VBox>

                        <!-- Recent Orders Report -->
                        <VBox styleClass="report-card" spacing="15.0" GridPane.columnIndex="1" GridPane.rowIndex="1">
                           <children>
                              <HBox styleClass="report-card-header" alignment="CENTER_LEFT" spacing="10.0">
                                 <children>
                                    <Label styleClass="report-icon" text="🕒" />
                                    <Label styleClass="report-title" text="Recent Orders">
                                       <font>
                                          <Font name="System Bold" size="16.0" />
                                       </font>
                                    </Label>
                                    <Region HBox.hgrow="ALWAYS" />
                                    <Button mnemonicParsing="false" onAction="#refreshRecentOrders" styleClass="refresh-button-small" text="🔄" />
                                 </children>
                              </HBox>

                              <ScrollPane styleClass="table-scroll-pane" fitToWidth="true" VBox.vgrow="ALWAYS">
                                 <content>
                                    <TableView fx:id="recentOrdersTable" styleClass="report-table">
                                       <columns>
                                          <TableColumn fx:id="orderIdColumn" prefWidth="80.0" text="Order ID" />
                                          <TableColumn fx:id="orderDateColumn" prefWidth="120.0" text="Date/Time" />
                                          <TableColumn fx:id="tableNumberColumn" prefWidth="80.0" text="Table" />
                                          <TableColumn fx:id="orderStatusColumn" prefWidth="100.0" text="Status" />
                                          <TableColumn fx:id="orderTotalColumn" prefWidth="100.0" text="Total" />
                                       </columns>
                                    </TableView>
                                 </content>
                              </ScrollPane>

                              <HBox styleClass="report-card-footer" alignment="CENTER_RIGHT" spacing="10.0">
                                 <children>
                                    <Button mnemonicParsing="false" onAction="#refreshRecentOrders" styleClass="refresh-button" text="Refresh Data" />
                                 </children>
                              </HBox>
                           </children>
                        </VBox>
                     </children>
                  </GridPane>
               </children>
            </VBox>
         </children>
      </VBox>
   </content>
</ScrollPane>
