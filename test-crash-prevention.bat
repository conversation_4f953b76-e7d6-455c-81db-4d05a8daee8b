@echo off
echo Testing Application Crash Prevention...
echo.

echo This script tests the crash prevention mechanisms added to prevent
echo the application from converting to JAR file icon after button clicks.
echo.

echo Compiling project with crash prevention improvements...
call mvn clean compile -q

if %ERRORLEVEL% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo Compilation successful!
echo.

echo Starting application with enhanced crash prevention...
echo.
echo CRASH PREVENTION FEATURES ADDED:
echo - Global uncaught exception handlers
echo - Safe button click execution
echo - Enhanced error recovery mechanisms
echo - Thread-safe UI operations
echo - Critical error dialogs instead of crashes
echo.
echo TESTING INSTRUCTIONS:
echo 1. Try clicking buttons multiple times rapidly
echo 2. Try clicking different buttons in sequence
echo 3. Watch for "CRITICAL ERROR" messages in console
echo 4. Look for error dialogs instead of application crashes
echo 5. Application should stay running even if errors occur
echo.
echo If the application converts to JAR icon, check console for:
echo - "CRITICAL: Uncaught exception" messages
echo - JavaFX thread errors
echo - Database connection issues
echo - FXML loading failures
echo.

java -Dprism.order=sw ^
     -Dprism.allowhidpi=false ^
     -Djavafx.animation.pulse=60 ^
     -Djava.awt.headless=false ^
     -Dsqlite.purejava=false ^
     -Xms512m ^
     -Xmx2g ^
     -XX:+UseG1GC ^
     -XX:MaxGCPauseMillis=200 ^
     -XX:+PrintGCDetails ^
     -XX:+PrintGCTimeStamps ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics ^
     -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\pdfbox\2.0.27\pdfbox-2.0.27.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\fontbox\2.0.27\fontbox-2.0.27.jar;%USERPROFILE%\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar" ^
     com.restaurant.RestaurantApp

echo.
echo Application closed.
echo.
echo CRASH PREVENTION ANALYSIS:
echo.
if %ERRORLEVEL% equ 0 (
    echo ✅ Application exited normally - No crash detected
) else (
    echo ❌ Application exited with error code: %ERRORLEVEL%
    echo Check console output above for crash details
)
echo.
echo If the application converted to JAR icon during testing:
echo 1. Check for "CRITICAL: Uncaught exception" messages above
echo 2. Look for JavaFX thread violations
echo 3. Check for database connection errors
echo 4. Verify FXML file loading issues
echo 5. Look for memory-related errors
echo.
echo The crash prevention should have shown error dialogs instead of crashing.
echo If crashes still occur, additional debugging may be needed.
echo.
pause
