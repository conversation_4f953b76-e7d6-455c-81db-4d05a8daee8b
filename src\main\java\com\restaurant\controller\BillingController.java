package com.restaurant.controller;

import com.restaurant.model.*;
import com.restaurant.util.PrintService;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;

import java.net.URL;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;

public class BillingController implements Initializable {
    
    @FXML private TableView<Order> ordersTable;
    @FXML private TableColumn<Order, Integer> orderIdColumn;
    @FXML private TableColumn<Order, String> orderDateColumn;
    @FXML private TableColumn<Order, String> tableColumn;
    @FXML private TableColumn<Order, String> statusColumn;
    @FXML private TableColumn<Order, Double> totalColumn;
    
    @FXML private TableView<OrderItem> orderItemsTable;
    @FXML private TableColumn<OrderItem, String> itemNameColumn;
    @FXML private TableColumn<OrderItem, Integer> quantityColumn;
    @FXML private TableColumn<OrderItem, Double> priceColumn;
    @FXML private TableColumn<OrderItem, Double> itemTotalColumn;
    
    @FXML private Label orderIdLabel;
    @FXML private Label orderDateLabel;
    @FXML private Label tableNumberLabel;
    @FXML private Label subtotalLabel;
    @FXML private Label gstLabel;
    @FXML private Label serviceChargeLabel;
    @FXML private Label grandTotalLabel;
    
    @FXML private TextField discountField;
    @FXML private ComboBox<String> paymentMethodComboBox;
    @FXML private Button generateBillBtn;
    @FXML private Button markPaidBtn;
    
    private ObservableList<Order> orders;
    private Order selectedOrder;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        setupTableColumns();
        setupPaymentMethods();
        loadOrders();
        setupEventHandlers();
    }
    
    private void setupTableColumns() {
        // Orders table
        orderIdColumn.setCellValueFactory(new PropertyValueFactory<>("id"));
        orderDateColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))));
        tableColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().isTakeaway() ? "Takeaway" : "Table " + cellData.getValue().getTableNumber()));
        statusColumn.setCellValueFactory(new PropertyValueFactory<>("status"));
        totalColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleDoubleProperty(cellData.getValue().calculateGrandTotal()).asObject());
        
        // Order items table
        itemNameColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(cellData.getValue().getMenuItem().getName()));
        quantityColumn.setCellValueFactory(new PropertyValueFactory<>("quantity"));
        priceColumn.setCellValueFactory(new PropertyValueFactory<>("price"));
        itemTotalColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleDoubleProperty(cellData.getValue().getTotalPrice()).asObject());
        
        // Format currency columns
        totalColumn.setCellFactory(column -> new TableCell<Order, Double>() {
            @Override
            protected void updateItem(Double total, boolean empty) {
                super.updateItem(total, empty);
                if (empty || total == null) {
                    setText(null);
                } else {
                    setText(String.format("$%.2f", total));
                }
            }
        });
        
        priceColumn.setCellFactory(column -> new TableCell<OrderItem, Double>() {
            @Override
            protected void updateItem(Double price, boolean empty) {
                super.updateItem(price, empty);
                if (empty || price == null) {
                    setText(null);
                } else {
                    setText(String.format("$%.2f", price));
                }
            }
        });
        
        itemTotalColumn.setCellFactory(column -> new TableCell<OrderItem, Double>() {
            @Override
            protected void updateItem(Double total, boolean empty) {
                super.updateItem(total, empty);
                if (empty || total == null) {
                    setText(null);
                } else {
                    setText(String.format("$%.2f", total));
                }
            }
        });
    }
    
    private void setupPaymentMethods() {
        paymentMethodComboBox.setItems(FXCollections.observableArrayList(
            "Cash", "Credit Card", "Debit Card", "Digital Wallet", "UPI"
        ));
        paymentMethodComboBox.setValue("Cash");
    }
    
    private void loadOrders() {
        List<Order> orderList = OrderDAO.getRecentOrders(50); // Get recent 50 orders
        orders = FXCollections.observableArrayList(orderList);
        ordersTable.setItems(orders);
    }
    
    private void setupEventHandlers() {
        ordersTable.getSelectionModel().selectedItemProperty().addListener(
            (obs, oldSelection, newSelection) -> {
                if (newSelection != null) {
                    loadOrderDetails(newSelection);
                }
            });
    }
    
    private void loadOrderDetails(Order order) {
        selectedOrder = OrderDAO.getOrderById(order.getId()); // Load full order with items
        
        if (selectedOrder != null) {
            // Update order info
            orderIdLabel.setText(String.valueOf(selectedOrder.getId()));
            orderDateLabel.setText(selectedOrder.getTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            tableNumberLabel.setText(selectedOrder.isTakeaway() ? "Takeaway" : "Table " + selectedOrder.getTableNumber());
            
            // Load order items
            orderItemsTable.setItems(FXCollections.observableArrayList(selectedOrder.getItems()));
            
            // Update totals
            updateTotals();
            
            // Enable/disable buttons based on order status
            boolean isPaid = "PAID".equals(selectedOrder.getStatus());
            generateBillBtn.setDisable(false);
            markPaidBtn.setDisable(isPaid);
        }
    }
    
    private void updateTotals() {
        if (selectedOrder == null) return;
        
        double subtotal = selectedOrder.calculateSubtotal();
        double gst = selectedOrder.calculateGST();
        double serviceCharge = selectedOrder.calculateServiceCharge();
        
        // Apply discount if any
        double discount = 0;
        try {
            String discountText = discountField.getText().trim();
            if (!discountText.isEmpty()) {
                discount = Double.parseDouble(discountText);
                if (discount > 100) discount = 100; // Max 100% discount
                if (discount < 0) discount = 0; // No negative discount
            }
        } catch (NumberFormatException e) {
            discount = 0;
        }
        
        double discountAmount = subtotal * (discount / 100);
        double discountedSubtotal = subtotal - discountAmount;
        double finalGst = discountedSubtotal * 0.18;
        double finalServiceCharge = discountedSubtotal * 0.10;
        double grandTotal = discountedSubtotal + finalGst + finalServiceCharge;
        
        subtotalLabel.setText(String.format("$%.2f", subtotal));
        gstLabel.setText(String.format("$%.2f", finalGst));
        serviceChargeLabel.setText(String.format("$%.2f", finalServiceCharge));
        grandTotalLabel.setText(String.format("$%.2f", grandTotal));
    }
    
    @FXML
    private void applyDiscount() {
        updateTotals();
    }
    
    @FXML
    private void generateBill() {
        if (selectedOrder == null) {
            showAlert("No Order Selected", "Please select an order to generate bill.");
            return;
        }
        
        try {
            PrintService.generateBill(selectedOrder);
            showAlert("Success", "Bill generated successfully!");
        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Failed to generate bill: " + e.getMessage());
        }
    }
    
    @FXML
    private void markAsPaid() {
        if (selectedOrder == null) {
            showAlert("No Order Selected", "Please select an order to mark as paid.");
            return;
        }
        
        Alert confirmation = new Alert(Alert.AlertType.CONFIRMATION);
        confirmation.setTitle("Mark as Paid");
        confirmation.setHeaderText("Mark Order as Paid");
        confirmation.setContentText("Are you sure you want to mark Order #" + selectedOrder.getId() + " as paid?");
        
        if (confirmation.showAndWait().orElse(ButtonType.CANCEL) == ButtonType.OK) {
            if (OrderDAO.updateOrderStatus(selectedOrder.getId(), "PAID")) {
                selectedOrder.setStatus("PAID");
                loadOrders(); // Refresh the orders table
                markPaidBtn.setDisable(true);
                showAlert("Success", "Order marked as paid successfully!");
            } else {
                showAlert("Error", "Failed to update order status.");
            }
        }
    }
    
    @FXML
    private void refreshOrders() {
        loadOrders();
        clearOrderDetails();
    }
    
    private void clearOrderDetails() {
        selectedOrder = null;
        orderIdLabel.setText("-");
        orderDateLabel.setText("-");
        tableNumberLabel.setText("-");
        orderItemsTable.setItems(FXCollections.observableArrayList());
        subtotalLabel.setText("$0.00");
        gstLabel.setText("$0.00");
        serviceChargeLabel.setText("$0.00");
        grandTotalLabel.setText("$0.00");
        discountField.clear();
        generateBillBtn.setDisable(true);
        markPaidBtn.setDisable(true);
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
