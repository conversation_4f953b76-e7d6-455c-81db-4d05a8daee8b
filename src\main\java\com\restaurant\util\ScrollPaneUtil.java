package com.restaurant.util;

import javafx.application.Platform;
import javafx.scene.control.ScrollPane;
import javafx.scene.Node;
import javafx.scene.layout.Region;

/**
 * Simple utility class for configuring ScrollPane components
 */
public class ScrollPaneUtil {

    private static final double KEYBOARD_SCROLL_SPEED = 0.1;
    
    /**
     * Configure a ScrollPane with universal scroll settings for proper movement
     */
    public static void configureScrollPane(ScrollPane scrollPane) {
        if (scrollPane == null) return;

        // Force scroll pane to use proper policies
        scrollPane.setFitToWidth(true);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.ALWAYS);
        scrollPane.setPannable(false);
        scrollPane.setVvalue(0.0);

        // Disable default scroll behavior to prevent conflicts
        scrollPane.skinProperty().addListener((obs, oldSkin, newSkin) -> {
            if (newSkin != null) {
                // Force scroll bar to always be visible
                Platform.runLater(() -> {
                    scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.ALWAYS);
                });
            }
        });

        // Enhanced scroll event handler with better sensitivity
        scrollPane.setOnScroll(event -> {
            double deltaY = event.getDeltaY();
            if (Math.abs(deltaY) > 0) {
                // Calculate scroll amount based on content size
                Node content = scrollPane.getContent();
                double contentHeight = content != null ? content.getBoundsInLocal().getHeight() : 1000;
                double viewportHeight = scrollPane.getViewportBounds().getHeight();

                // Dynamic scroll speed based on content ratio
                double scrollRatio = Math.max(0.01, Math.min(0.1, 40.0 / contentHeight));
                double scrollAmount = deltaY > 0 ? -scrollRatio : scrollRatio;

                double currentValue = scrollPane.getVvalue();
                double newValue = Math.max(0.0, Math.min(1.0, currentValue + scrollAmount));

                scrollPane.setVvalue(newValue);
                event.consume();
            }
        });

        // Enhanced keyboard navigation
        scrollPane.setOnKeyPressed(event -> {
            double currentValue = scrollPane.getVvalue();
            double newValue = currentValue;

            switch (event.getCode()) {
                case UP:
                    newValue = Math.max(0.0, currentValue - KEYBOARD_SCROLL_SPEED);
                    break;
                case DOWN:
                    newValue = Math.min(1.0, currentValue + KEYBOARD_SCROLL_SPEED);
                    break;
                case PAGE_UP:
                    newValue = Math.max(0.0, currentValue - 0.3);
                    break;
                case PAGE_DOWN:
                    newValue = Math.min(1.0, currentValue + 0.3);
                    break;
                case HOME:
                    newValue = 0.0;
                    break;
                case END:
                    newValue = 1.0;
                    break;
            }

            if (newValue != currentValue) {
                scrollPane.setVvalue(newValue);
                event.consume();
            }
        });

        // Make scroll pane focusable
        scrollPane.setFocusTraversable(true);

        // Configure content sizing
        configureContentSizing(scrollPane);

        // Force initial layout
        Platform.runLater(() -> {
            forceScrollPaneLayout(scrollPane);
        });
    }
    
    /**
     * Configure content sizing for proper scrolling
     */
    private static void configureContentSizing(ScrollPane scrollPane) {
        Node content = scrollPane.getContent();
        if (content instanceof Region) {
            Region contentRegion = (Region) content;

            // Bind content width to scroll pane width minus scroll bar
            contentRegion.prefWidthProperty().bind(
                scrollPane.widthProperty().subtract(25)
            );
            contentRegion.maxWidthProperty().bind(
                scrollPane.widthProperty().subtract(25)
            );

            // Force content to be taller than viewport to enable scrolling
            Platform.runLater(() -> {
                double viewportHeight = scrollPane.getViewportBounds().getHeight();
                if (viewportHeight > 0) {
                    // Ensure content is at least 1.5x the viewport height for proper scrolling
                    double minContentHeight = Math.max(viewportHeight * 1.5, 800);
                    contentRegion.setMinHeight(minContentHeight);

                    // Set preferred height to allow natural growth
                    contentRegion.setPrefHeight(Region.USE_COMPUTED_SIZE);
                }
            });

            // Add listener for content height changes
            contentRegion.heightProperty().addListener((obs, oldVal, newVal) -> {
                Platform.runLater(() -> {
                    refreshScrollPaneSimple(scrollPane);
                });
            });
        }
    }

    /**
     * Simple scroll pane refresh
     */
    private static void refreshScrollPaneSimple(ScrollPane scrollPane) {
        if (scrollPane == null) return;

        Platform.runLater(() -> {
            scrollPane.requestLayout();
            scrollPane.applyCss();
        });
    }

    /**
     * Simple scroll pane layout
     */
    private static void forceScrollPaneLayout(ScrollPane scrollPane) {
        if (scrollPane == null) return;

        // Force scroll bar to always be visible
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.ALWAYS);

        // Apply CSS class for styling
        if (!scrollPane.getStyleClass().contains("universal-scroll-pane")) {
            scrollPane.getStyleClass().add("universal-scroll-pane");
        }

        // Force layout updates
        scrollPane.requestLayout();
        scrollPane.applyCss();
    }

    /**
     * Refresh scroll pane to recalculate content bounds and ensure proper scrolling
     */
    public static void refreshScrollPane(ScrollPane scrollPane) {
        if (scrollPane == null) return;

        Platform.runLater(() -> {
            forceScrollPaneLayout(scrollPane);
        });
    }
    
    /**
     * Configure scroll pane with specific minimum content height
     */
    public static void configureScrollPaneWithMinHeight(ScrollPane scrollPane, double minHeight) {
        configureScrollPane(scrollPane);
        
        Node content = scrollPane.getContent();
        if (content instanceof Region) {
            Region contentRegion = (Region) content;
            contentRegion.setMinHeight(minHeight);
        }
    }
    
    /**
     * Scroll to top of the scroll pane
     */
    public static void scrollToTop(ScrollPane scrollPane) {
        if (scrollPane != null) {
            Platform.runLater(() -> scrollPane.setVvalue(0.0));
        }
    }
    
    /**
     * Scroll to bottom of the scroll pane
     */
    public static void scrollToBottom(ScrollPane scrollPane) {
        if (scrollPane != null) {
            Platform.runLater(() -> scrollPane.setVvalue(1.0));
        }
    }
    
    /**
     * Scroll to specific percentage (0.0 to 1.0)
     */
    public static void scrollToPosition(ScrollPane scrollPane, double position) {
        if (scrollPane != null) {
            double clampedPosition = Math.max(0.0, Math.min(1.0, position));
            Platform.runLater(() -> scrollPane.setVvalue(clampedPosition));
        }
    }
}
