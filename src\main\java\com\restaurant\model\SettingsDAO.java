package com.restaurant.model;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

public class SettingsDAO {
    
    /**
     * Get all settings as a map
     */
    public static Map<String, String> getAllSettings() {
        Map<String, String> settings = new HashMap<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT setting_key, setting_value FROM settings")) {
            
            while (rs.next()) {
                settings.put(rs.getString("setting_key"), rs.getString("setting_value"));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return settings;
    }
    
    /**
     * Get a specific setting value
     */
    public static String getSetting(String key) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("SELECT setting_value FROM settings WHERE setting_key = ?")) {
            
            ps.setString(1, key);
            ResultSet rs = ps.executeQuery();
            
            if (rs.next()) {
                return rs.getString("setting_value");
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * Set a setting value
     */
    public static boolean setSetting(String key, String value) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "INSERT OR REPLACE INTO settings (setting_key, setting_value, last_updated) VALUES (?, ?, ?)")) {
            
            ps.setString(1, key);
            ps.setString(2, value);
            ps.setTimestamp(3, Timestamp.valueOf(LocalDateTime.now()));
            
            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Set multiple settings at once
     */
    public static boolean setSettings(Map<String, String> settings) {
        try (Connection conn = DatabaseManager.getConnection()) {
            conn.setAutoCommit(false);
            
            try (PreparedStatement ps = conn.prepareStatement(
                    "INSERT OR REPLACE INTO settings (setting_key, setting_value, last_updated) VALUES (?, ?, ?)")) {
                
                for (Map.Entry<String, String> entry : settings.entrySet()) {
                    ps.setString(1, entry.getKey());
                    ps.setString(2, entry.getValue());
                    ps.setTimestamp(3, Timestamp.valueOf(LocalDateTime.now()));
                    ps.addBatch();
                }
                
                ps.executeBatch();
                conn.commit();
                return true;
            } catch (SQLException e) {
                conn.rollback();
                throw e;
            } finally {
                conn.setAutoCommit(true);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Delete a setting
     */
    public static boolean deleteSetting(String key) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("DELETE FROM settings WHERE setting_key = ?")) {
            
            ps.setString(1, key);
            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    // Restaurant Information Settings
    public static final String RESTAURANT_NAME = "restaurant.name";
    public static final String RESTAURANT_PHONE = "restaurant.phone";
    public static final String RESTAURANT_EMAIL = "restaurant.email";
    public static final String RESTAURANT_ADDRESS = "restaurant.address";
    public static final String RESTAURANT_WEBSITE = "restaurant.website";
    public static final String RESTAURANT_LOGO_PATH = "restaurant.logo_path";
    
    // Printer Settings
    public static final String INVOICE_PRINTER = "printer.invoice";
    public static final String KOT_PRINTER = "printer.kot";
    public static final String AUTO_PRINT_KOT = "printer.auto_print_kot";
    public static final String AUTO_PRINT_INVOICE = "printer.auto_print_invoice";
    public static final String PRINT_CUSTOMER_COPY = "printer.customer_copy";
    public static final String PRINT_KITCHEN_COPY = "printer.kitchen_copy";
    
    // System Settings
    public static final String STARTUP_LOGIN = "system.startup_login";
    public static final String REMEMBER_LAST_USER = "system.remember_last_user";
    public static final String AUTO_SAVE = "system.auto_save";
    public static final String SOUND_NOTIFICATIONS = "system.sound_notifications";
    public static final String SESSION_TIMEOUT = "system.session_timeout";
    public static final String THEME = "system.theme";
    public static final String LANGUAGE = "system.language";
    
    // Business Settings
    public static final String ENABLE_GST = "business.enable_gst";
    public static final String GST_RATE = "business.gst_rate";
    public static final String ENABLE_SERVICE_CHARGE = "business.enable_service_charge";
    public static final String SERVICE_CHARGE_RATE = "business.service_charge_rate";
    public static final String CURRENCY_SYMBOL = "business.currency_symbol";
    public static final String DECIMAL_PLACES = "business.decimal_places";
    
    // Convenience methods for restaurant info
    public static String getRestaurantName() {
        return getSetting(RESTAURANT_NAME);
    }
    
    public static boolean setRestaurantName(String name) {
        return setSetting(RESTAURANT_NAME, name);
    }
    
    public static String getRestaurantPhone() {
        return getSetting(RESTAURANT_PHONE);
    }
    
    public static boolean setRestaurantPhone(String phone) {
        return setSetting(RESTAURANT_PHONE, phone);
    }
    
    public static String getRestaurantEmail() {
        return getSetting(RESTAURANT_EMAIL);
    }
    
    public static boolean setRestaurantEmail(String email) {
        return setSetting(RESTAURANT_EMAIL, email);
    }
    
    public static String getRestaurantAddress() {
        return getSetting(RESTAURANT_ADDRESS);
    }
    
    public static boolean setRestaurantAddress(String address) {
        return setSetting(RESTAURANT_ADDRESS, address);
    }
    
    public static String getRestaurantWebsite() {
        return getSetting(RESTAURANT_WEBSITE);
    }
    
    public static boolean setRestaurantWebsite(String website) {
        return setSetting(RESTAURANT_WEBSITE, website);
    }
    
    // Convenience methods for printer settings
    public static String getInvoicePrinter() {
        return getSetting(INVOICE_PRINTER);
    }
    
    public static boolean setInvoicePrinter(String printer) {
        return setSetting(INVOICE_PRINTER, printer);
    }
    
    public static String getKOTPrinter() {
        return getSetting(KOT_PRINTER);
    }
    
    public static boolean setKOTPrinter(String printer) {
        return setSetting(KOT_PRINTER, printer);
    }
    
    public static boolean isAutoPrintKOT() {
        String value = getSetting(AUTO_PRINT_KOT);
        return "true".equals(value);
    }
    
    public static boolean setAutoPrintKOT(boolean enabled) {
        return setSetting(AUTO_PRINT_KOT, String.valueOf(enabled));
    }
    
    public static boolean isAutoPrintInvoice() {
        String value = getSetting(AUTO_PRINT_INVOICE);
        return "true".equals(value);
    }
    
    public static boolean setAutoPrintInvoice(boolean enabled) {
        return setSetting(AUTO_PRINT_INVOICE, String.valueOf(enabled));
    }
    
    // Convenience methods for system settings
    public static boolean isStartupLogin() {
        String value = getSetting(STARTUP_LOGIN);
        return "true".equals(value);
    }
    
    public static boolean setStartupLogin(boolean enabled) {
        return setSetting(STARTUP_LOGIN, String.valueOf(enabled));
    }
    
    public static boolean isRememberLastUser() {
        String value = getSetting(REMEMBER_LAST_USER);
        return "true".equals(value);
    }
    
    public static boolean setRememberLastUser(boolean enabled) {
        return setSetting(REMEMBER_LAST_USER, String.valueOf(enabled));
    }
    
    public static boolean isAutoSave() {
        String value = getSetting(AUTO_SAVE);
        return "true".equals(value);
    }
    
    public static boolean setAutoSave(boolean enabled) {
        return setSetting(AUTO_SAVE, String.valueOf(enabled));
    }
    
    public static boolean isSoundNotifications() {
        String value = getSetting(SOUND_NOTIFICATIONS);
        return "true".equals(value);
    }
    
    public static boolean setSoundNotifications(boolean enabled) {
        return setSetting(SOUND_NOTIFICATIONS, String.valueOf(enabled));
    }
    
    public static String getTheme() {
        String theme = getSetting(THEME);
        return theme != null ? theme : "Light";
    }
    
    public static boolean setTheme(String theme) {
        return setSetting(THEME, theme);
    }
    
    // Convenience methods for business settings
    public static boolean isGSTEnabled() {
        String value = getSetting(ENABLE_GST);
        return "true".equals(value);
    }
    
    public static boolean setGSTEnabled(boolean enabled) {
        return setSetting(ENABLE_GST, String.valueOf(enabled));
    }
    
    public static double getGSTRate() {
        String value = getSetting(GST_RATE);
        try {
            return value != null ? Double.parseDouble(value) : 18.0;
        } catch (NumberFormatException e) {
            return 18.0;
        }
    }
    
    public static boolean setGSTRate(double rate) {
        return setSetting(GST_RATE, String.valueOf(rate));
    }
    
    public static boolean isServiceChargeEnabled() {
        String value = getSetting(ENABLE_SERVICE_CHARGE);
        return "true".equals(value);
    }
    
    public static boolean setServiceChargeEnabled(boolean enabled) {
        return setSetting(ENABLE_SERVICE_CHARGE, String.valueOf(enabled));
    }
    
    public static double getServiceChargeRate() {
        String value = getSetting(SERVICE_CHARGE_RATE);
        try {
            return value != null ? Double.parseDouble(value) : 10.0;
        } catch (NumberFormatException e) {
            return 10.0;
        }
    }
    
    public static boolean setServiceChargeRate(double rate) {
        return setSetting(SERVICE_CHARGE_RATE, String.valueOf(rate));
    }
    
    /**
     * Initialize default settings
     */
    public static void initializeDefaultSettings() {
        Map<String, String> defaults = new HashMap<>();
        
        // Restaurant info defaults
        defaults.put(RESTAURANT_NAME, "WOK KA TADKA");
        defaults.put(RESTAURANT_PHONE, "+91 98765 43210");
        defaults.put(RESTAURANT_EMAIL, "<EMAIL>");
        defaults.put(RESTAURANT_ADDRESS, "123 Food Street, Flavor City, FC 12345");
        defaults.put(RESTAURANT_WEBSITE, "www.wokkatadka.com");
        
        // Printer defaults
        defaults.put(INVOICE_PRINTER, "Default Printer");
        defaults.put(KOT_PRINTER, "Kitchen Printer");
        defaults.put(AUTO_PRINT_KOT, "true");
        defaults.put(AUTO_PRINT_INVOICE, "false");
        defaults.put(PRINT_CUSTOMER_COPY, "true");
        defaults.put(PRINT_KITCHEN_COPY, "true");
        
        // System defaults
        defaults.put(STARTUP_LOGIN, "true");
        defaults.put(REMEMBER_LAST_USER, "false");
        defaults.put(AUTO_SAVE, "true");
        defaults.put(SOUND_NOTIFICATIONS, "true");
        defaults.put(SESSION_TIMEOUT, "30.0");
        defaults.put(THEME, "Light");
        defaults.put(LANGUAGE, "English");
        
        // Business defaults
        defaults.put(ENABLE_GST, "true");
        defaults.put(GST_RATE, "18.0");
        defaults.put(ENABLE_SERVICE_CHARGE, "true");
        defaults.put(SERVICE_CHARGE_RATE, "10.0");
        defaults.put(CURRENCY_SYMBOL, "₹");
        defaults.put(DECIMAL_PLACES, "2");
        
        // Use direct connection to avoid circular dependency during initialization
        try (Connection conn = java.sql.DriverManager.getConnection("*************************")) {
            for (Map.Entry<String, String> entry : defaults.entrySet()) {
                // Check if setting exists
                try (PreparedStatement checkPs = conn.prepareStatement("SELECT setting_value FROM settings WHERE setting_key = ?")) {
                    checkPs.setString(1, entry.getKey());
                    ResultSet rs = checkPs.executeQuery();

                    if (!rs.next()) {
                        // Setting doesn't exist, insert it
                        try (PreparedStatement insertPs = conn.prepareStatement(
                                "INSERT INTO settings (setting_key, setting_value, last_updated) VALUES (?, ?, ?)")) {
                            insertPs.setString(1, entry.getKey());
                            insertPs.setString(2, entry.getValue());
                            insertPs.setTimestamp(3, Timestamp.valueOf(LocalDateTime.now()));
                            insertPs.executeUpdate();
                        }
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
}
