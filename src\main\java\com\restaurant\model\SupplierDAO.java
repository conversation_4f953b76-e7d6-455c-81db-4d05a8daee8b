package com.restaurant.model;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class SupplierDAO {
    
    /**
     * Get all suppliers
     */
    public static List<Supplier> getAllSuppliers() {
        List<Supplier> suppliers = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT * FROM suppliers ORDER BY name")) {
            
            while (rs.next()) {
                Supplier supplier = new Supplier(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getString("contact_person"),
                    rs.getString("phone"),
                    rs.getString("email"),
                    rs.getString("address")
                );
                supplier.setCategory(rs.getString("category"));
                supplier.setStatus(rs.getString("status"));
                supplier.setNotes(rs.getString("notes"));
                supplier.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
                supplier.setLastUpdated(rs.getTimestamp("last_updated").toLocalDateTime());
                suppliers.add(supplier);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return suppliers;
    }
    
    /**
     * Get supplier by ID
     */
    public static Supplier getSupplierById(int id) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("SELECT * FROM suppliers WHERE id = ?")) {
            
            ps.setInt(1, id);
            ResultSet rs = ps.executeQuery();
            
            if (rs.next()) {
                Supplier supplier = new Supplier(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getString("contact_person"),
                    rs.getString("phone"),
                    rs.getString("email"),
                    rs.getString("address")
                );
                supplier.setCategory(rs.getString("category"));
                supplier.setStatus(rs.getString("status"));
                supplier.setNotes(rs.getString("notes"));
                supplier.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
                supplier.setLastUpdated(rs.getTimestamp("last_updated").toLocalDateTime());
                return supplier;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * Add new supplier
     */
    public static boolean addSupplier(Supplier supplier) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "INSERT INTO suppliers (name, contact_person, phone, email, address, category, status, notes, created_at, last_updated) " +
                     "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", Statement.RETURN_GENERATED_KEYS)) {
            
            ps.setString(1, supplier.getName());
            ps.setString(2, supplier.getContactPerson());
            ps.setString(3, supplier.getPhone());
            ps.setString(4, supplier.getEmail());
            ps.setString(5, supplier.getAddress());
            ps.setString(6, supplier.getCategory());
            ps.setString(7, supplier.getStatus());
            ps.setString(8, supplier.getNotes());
            ps.setTimestamp(9, Timestamp.valueOf(LocalDateTime.now()));
            ps.setTimestamp(10, Timestamp.valueOf(LocalDateTime.now()));
            
            int result = ps.executeUpdate();
            
            if (result > 0) {
                ResultSet rs = ps.getGeneratedKeys();
                if (rs.next()) {
                    supplier.setId(rs.getInt(1));
                }
                return true;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Update supplier
     */
    public static boolean updateSupplier(Supplier supplier) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "UPDATE suppliers SET name = ?, contact_person = ?, phone = ?, email = ?, address = ?, " +
                     "category = ?, status = ?, notes = ?, last_updated = ? WHERE id = ?")) {
            
            ps.setString(1, supplier.getName());
            ps.setString(2, supplier.getContactPerson());
            ps.setString(3, supplier.getPhone());
            ps.setString(4, supplier.getEmail());
            ps.setString(5, supplier.getAddress());
            ps.setString(6, supplier.getCategory());
            ps.setString(7, supplier.getStatus());
            ps.setString(8, supplier.getNotes());
            ps.setTimestamp(9, Timestamp.valueOf(LocalDateTime.now()));
            ps.setInt(10, supplier.getId());
            
            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Delete supplier
     */
    public static boolean deleteSupplier(int id) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("DELETE FROM suppliers WHERE id = ?")) {
            
            ps.setInt(1, id);
            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * Get active suppliers
     */
    public static List<Supplier> getActiveSuppliers() {
        List<Supplier> suppliers = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("SELECT * FROM suppliers WHERE status = 'Active' ORDER BY name")) {
            
            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                Supplier supplier = new Supplier(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getString("contact_person"),
                    rs.getString("phone"),
                    rs.getString("email"),
                    rs.getString("address")
                );
                supplier.setCategory(rs.getString("category"));
                supplier.setStatus(rs.getString("status"));
                supplier.setNotes(rs.getString("notes"));
                supplier.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
                supplier.setLastUpdated(rs.getTimestamp("last_updated").toLocalDateTime());
                suppliers.add(supplier);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return suppliers;
    }
    
    /**
     * Search suppliers by name or category
     */
    public static List<Supplier> searchSuppliers(String searchTerm) {
        List<Supplier> suppliers = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT * FROM suppliers WHERE name LIKE ? OR category LIKE ? OR contact_person LIKE ? ORDER BY name")) {
            
            String searchPattern = "%" + searchTerm + "%";
            ps.setString(1, searchPattern);
            ps.setString(2, searchPattern);
            ps.setString(3, searchPattern);
            
            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                Supplier supplier = new Supplier(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getString("contact_person"),
                    rs.getString("phone"),
                    rs.getString("email"),
                    rs.getString("address")
                );
                supplier.setCategory(rs.getString("category"));
                supplier.setStatus(rs.getString("status"));
                supplier.setNotes(rs.getString("notes"));
                supplier.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
                supplier.setLastUpdated(rs.getTimestamp("last_updated").toLocalDateTime());
                suppliers.add(supplier);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return suppliers;
    }
    
    /**
     * Get suppliers by category
     */
    public static List<Supplier> getSuppliersByCategory(String category) {
        List<Supplier> suppliers = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("SELECT * FROM suppliers WHERE category = ? ORDER BY name")) {
            
            ps.setString(1, category);
            ResultSet rs = ps.executeQuery();
            
            while (rs.next()) {
                Supplier supplier = new Supplier(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getString("contact_person"),
                    rs.getString("phone"),
                    rs.getString("email"),
                    rs.getString("address")
                );
                supplier.setCategory(rs.getString("category"));
                supplier.setStatus(rs.getString("status"));
                supplier.setNotes(rs.getString("notes"));
                supplier.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
                supplier.setLastUpdated(rs.getTimestamp("last_updated").toLocalDateTime());
                suppliers.add(supplier);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return suppliers;
    }
    
    /**
     * Get supplier names for dropdown
     */
    public static List<String> getSupplierNames() {
        List<String> names = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("SELECT name FROM suppliers WHERE status = 'Active' ORDER BY name")) {
            
            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                names.add(rs.getString("name"));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return names;
    }
}
