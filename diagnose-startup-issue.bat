@echo off
echo DIAGNOSING STARTUP ISSUE - Finding Exact Problem...
echo.

echo This script will help identify exactly where the application gets stuck
echo.

echo Step 1: Testing basic Java and JavaFX setup...
echo.

echo Testing Java version:
java -version

echo.
echo Testing JavaFX module availability:
java --list-modules | findstr javafx

echo.
echo Step 2: Testing basic compilation...
call mvn clean compile -q

if %ERRORLEVEL% neq 0 (
    echo ❌ COMPILATION FAILED - This is the root issue
    echo Running detailed compilation to see errors:
    mvn clean compile
    pause
    exit /b 1
)

echo ✅ Compilation successful
echo.

echo Step 3: Testing database connection...
java -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\********\sqlite-jdbc-********.jar" com.restaurant.model.TestDatabaseConnection

echo.
echo Step 4: Testing application startup with maximum logging...
echo.
echo Starting application with detailed logging to see where it gets stuck...
echo WATCH THE OUTPUT CAREFULLY - Note the last message before it hangs
echo.

timeout /t 3

java -Dprism.order=sw ^
     -Dprism.allowhidpi=false ^
     -Djavafx.animation.pulse=60 ^
     -Djava.awt.headless=false ^
     -Dsqlite.purejava=false ^
     -Dprism.verbose=true ^
     -Djavafx.verbose=true ^
     -Djava.util.logging.level=ALL ^
     -Xms256m ^
     -Xmx1g ^
     -XX:+UseG1GC ^
     -XX:MaxGCPauseMillis=200 ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics ^
     -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\********\sqlite-jdbc-********.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\pdfbox\2.0.27\pdfbox-2.0.27.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\fontbox\2.0.27\fontbox-2.0.27.jar;%USERPROFILE%\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar" ^
     com.restaurant.RestaurantApp

echo.
echo DIAGNOSTIC COMPLETE
echo.
echo ANALYSIS:
echo.
echo Please review the output above and identify:
echo.
echo 1. LAST SUCCESSFUL MESSAGE before hanging:
echo    - Look for the last line that printed successfully
echo    - This tells us exactly where the application gets stuck
echo.
echo 2. COMMON STUCK POINTS:
echo    - "Loading login..." - Issue with login FXML
echo    - "Initializing database..." - Database connection problem
echo    - "Loading CSS..." - CSS parsing issue
echo    - "Setting up notifications..." - Notification system problem
echo    - "Configuring scroll panes..." - UI component issue
echo.
echo 3. ERROR MESSAGES:
echo    - Look for any Exception or Error messages
echo    - Note any ClassCastException or other errors
echo.
echo Based on where it gets stuck, we can create a targeted fix.
echo.
echo NEXT STEPS:
echo.
echo If stuck at login loading: Run emergency-safe-mode.bat
echo If stuck at database: Check database file permissions
echo If stuck at CSS: Use minimal CSS approach
echo If stuck at notifications: Disable notification system
echo.
pause
