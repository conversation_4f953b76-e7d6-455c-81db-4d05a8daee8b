package com.restaurant.util; 
 
import java.sql.*; 
 
public class CorrectDatabaseCreator { 
    public static void main(String[] args) { 
        System.out.println("🔧 Creating database with CORRECT column names..."); 
 
        try { 
            Class.forName("org.sqlite.JDBC"); 
            System.out.println("✅ SQLite driver loaded"); 
        } catch (Exception e) { 
            System.err.println("❌ Driver error: " + e.getMessage()); 
            return; 
        } 
 
        String url = "*************************"; 
 
        try (Connection conn = DriverManager.getConnection(url)) { 
            System.out.println("✅ Database connected"); 
 
            // Create users table with CORRECT column name: password_hash 
            String createTable = "CREATE TABLE users (" + 
                "id INTEGER PRIMARY KEY AUTOINCREMENT, " + 
                "username TEXT UNIQUE NOT NULL, " + 
                "password_hash TEXT NOT NULL, " + 
                "role TEXT NOT NULL)"; 
 
            conn.createStatement().execute(createTable); 
            System.out.println("✅ Users table created with password_hash column"); 
 
            // Insert admin with BCrypt hash for 'admin123' 
            String insert = "INSERT INTO users (username, password_hash, role) VALUES (?, ?, ?)"; 
            PreparedStatement ps = conn.prepareStatement(insert); 
            ps.setString(1, "admin"); 
            ps.setString(2, "$2a$10$N9qo8uLOickgx2ZMRZoMye.Uo0aBOBjXoXebXeQABkc4ILoLGxjvO"); 
            ps.setString(3, "ADMIN"); 
            ps.executeUpdate(); 
            ps.close(); 
            System.out.println("✅ Admin user created with correct column"); 
 
            // Insert staff user 
            ps = conn.prepareStatement(insert); 
            ps.setString(1, "staff"); 
            ps.setString(2, "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi."); 
            ps.setString(3, "STAFF"); 
            ps.executeUpdate(); 
            ps.close(); 
            System.out.println("✅ Staff user created"); 
 
            // Verify with correct column name 
            System.out.println(); 
            System.out.println("📋 USERS WITH CORRECT COLUMNS:"); 
            ResultSet rs = conn.createStatement().executeQuery("SELECT id, username, role, password_hash FROM users"); 
            while (rs.next()) { 
                System.out.println("ID: " + rs.getInt("id") + " | Username: " + rs.getString("username") + " | Role: " + rs.getString("role")); 
                System.out.println("Password hash: " + rs.getString("password_hash").substring(0, 20) + "..."); 
                System.out.println("---"); 
            } 
            rs.close(); 
 
            System.out.println("🎉 SUCCESS! Database created with correct column names."); 
            System.out.println("UserDAO.authenticate should now work properly!"); 
 
        } catch (Exception e) { 
            System.err.println("❌ Error: " + e.getMessage()); 
            e.printStackTrace(); 
        } 
    } 
} 
