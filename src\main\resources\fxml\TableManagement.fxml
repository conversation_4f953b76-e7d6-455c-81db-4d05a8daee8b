<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.TableManagementController" stylesheets="@../css/application.css">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="module-header">
         <children>
            <Button mnemonicParsing="false" onAction="#goBack" styleClass="back-button" text="←" />
            <VBox spacing="5.0">
               <children>
                  <Label styleClass="module-title" text="Table Management">
                     <font>
                        <Font name="System Bold" size="24.0" />
                     </font>
                  </Label>
                  <Label styleClass="module-subtitle" text="Select a table to take order" />
               </children>
            </VBox>
            <Region HBox.hgrow="ALWAYS" />
            <!-- Search Field with Autocomplete -->
            <VBox spacing="0.0">
               <children>
                  <HBox alignment="CENTER_LEFT" spacing="10.0">
                     <children>
                        <Label text="🔍" styleClass="search-icon" />
                        <TextField fx:id="searchField" promptText="Type to search dishes..." styleClass="search-field" prefWidth="250.0" />
                     </children>
                  </HBox>
                  <!-- Autocomplete Suggestions Dropdown -->
                  <VBox fx:id="autocompleteDropdown" styleClass="autocomplete-dropdown" visible="false" managed="false" maxHeight="200.0">
                     <children>
                        <ScrollPane fx:id="autocompleteScrollPane" styleClass="autocomplete-scroll" fitToWidth="true" maxHeight="200.0">
                           <content>
                              <VBox fx:id="autocompleteContainer" styleClass="autocomplete-container" spacing="2.0" />
                           </content>
                        </ScrollPane>
                     </children>
                  </VBox>
               </children>
            </VBox>
            <Button mnemonicParsing="false" onAction="#refreshTables" styleClass="refresh-button" text="🔄 Refresh" />
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </HBox>

      <!-- Search Results Area (Initially Hidden) -->
      <VBox fx:id="searchResultsArea" styleClass="search-results-area" visible="false" managed="false" spacing="10.0">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <children>
                  <Label text="🍽️ Search Results:" styleClass="search-results-title" />
                  <Button fx:id="clearSearchButton" onAction="#clearSearch" text="✕ Clear" styleClass="clear-search-button" />
               </children>
            </HBox>
            <ScrollPane fx:id="searchResultsScrollPane" styleClass="search-results-scroll" prefHeight="200.0" fitToWidth="true">
               <content>
                  <VBox fx:id="searchResultsContainer" styleClass="search-results-container" spacing="10.0" />
               </content>
            </ScrollPane>
         </children>
         <padding>
            <Insets bottom="10.0" left="20.0" right="20.0" top="10.0" />
         </padding>
      </VBox>

      <!-- Status Legend -->
      <HBox alignment="CENTER_LEFT" spacing="30.0" styleClass="status-legend">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="8.0">
               <children>
                  <Label styleClass="status-indicator-free" text="●" />
                  <Label text="Free" />
               </children>
            </HBox>
            <HBox alignment="CENTER_LEFT" spacing="8.0">
               <children>
                  <Label styleClass="status-indicator-occupied" text="●" />
                  <Label text="Occupied" />
               </children>
            </HBox>
            <HBox alignment="CENTER_LEFT" spacing="8.0">
               <children>
                  <Label styleClass="status-indicator-preparing" text="●" />
                  <Label text="Preparing" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="10.0" left="20.0" right="20.0" top="10.0" />
         </padding>
      </HBox>
      
      <!-- Tables Grid -->
      <ScrollPane styleClass="tables-scroll" VBox.vgrow="ALWAYS">
         <content>
            <GridPane fx:id="tablesGrid" styleClass="tables-grid" hgap="30.0" vgap="30.0" alignment="CENTER">
               <columnConstraints>
                  <ColumnConstraints hgrow="SOMETIMES" minWidth="250.0" prefWidth="300.0" maxWidth="350.0" />
                  <ColumnConstraints hgrow="SOMETIMES" minWidth="250.0" prefWidth="300.0" maxWidth="350.0" />
                  <ColumnConstraints hgrow="SOMETIMES" minWidth="250.0" prefWidth="300.0" maxWidth="350.0" />
               </columnConstraints>
               <rowConstraints>
                  <RowConstraints minHeight="180.0" prefHeight="200.0" maxHeight="220.0" vgrow="SOMETIMES" />
                  <RowConstraints minHeight="180.0" prefHeight="200.0" maxHeight="220.0" vgrow="SOMETIMES" />
                  <RowConstraints minHeight="180.0" prefHeight="200.0" maxHeight="220.0" vgrow="SOMETIMES" />
               </rowConstraints>
               <children>
                  <!-- Table 1 - Free -->
                  <VBox fx:id="table1" styleClass="table-card, table-free" GridPane.columnIndex="0" GridPane.rowIndex="0">
                     <children>
                        <Label styleClass="table-number" text="Table 1">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        <HBox alignment="CENTER" spacing="5.0">
                           <children>
                              <Label text="👥" />
                              <Label text="4 seats" />
                           </children>
                        </HBox>
                        <Label styleClass="table-status" text="Available" />
                        <Region VBox.vgrow="ALWAYS" />
                        <HBox alignment="CENTER" spacing="8.0" styleClass="table-actions">
                           <children>
                              <Button onAction="#viewTableDetails" styleClass="table-action-btn, eye-button" text="👁️" userData="1">
                                 <tooltip>
                                    <Tooltip text="View Details" />
                                 </tooltip>
                              </Button>
                              <Button onAction="#selectTable" styleClass="table-action-btn, select-button" text="Select" userData="1">
                                 <tooltip>
                                    <Tooltip text="Select Table" />
                                 </tooltip>
                              </Button>
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 2 - Occupied -->
                  <VBox fx:id="table2" styleClass="table-card, table-occupied" GridPane.columnIndex="1" GridPane.rowIndex="0">
                     <children>
                        <Label styleClass="table-number" text="Table 2">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        <HBox alignment="CENTER" spacing="5.0">
                           <children>
                              <Label text="👥" />
                              <Label text="4 seats" />
                           </children>
                        </HBox>
                        <Label styleClass="order-badge" text="Order #1001" />
                        <Label styleClass="table-status" text="Order Pending" />
                        <Region VBox.vgrow="ALWAYS" />
                        <HBox alignment="CENTER" spacing="8.0" styleClass="table-actions">
                           <children>
                              <Button onAction="#viewTableDetails" styleClass="table-action-btn, eye-button" text="👁️" userData="2">
                                 <tooltip>
                                    <Tooltip text="View Order Details" />
                                 </tooltip>
                              </Button>
                              <Button onAction="#addItemsToTable" styleClass="table-action-btn, add-button" text="+" userData="2">
                                 <tooltip>
                                    <Tooltip text="Add Items" />
                                 </tooltip>
                              </Button>
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 3 - Available -->
                  <VBox fx:id="table3" styleClass="table-card, table-free" GridPane.columnIndex="2" GridPane.rowIndex="0">
                     <children>
                        <Label styleClass="table-number" text="Table 3">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        <HBox alignment="CENTER" spacing="5.0">
                           <children>
                              <Label text="👥" />
                              <Label text="4 seats" />
                           </children>
                        </HBox>
                        <Label styleClass="table-status" text="Available" />
                        <Region VBox.vgrow="ALWAYS" />
                        <HBox alignment="CENTER" spacing="8.0" styleClass="table-actions">
                           <children>
                              <Button onAction="#viewTableDetails" styleClass="table-action-btn, eye-button" text="👁️" userData="3">
                                 <tooltip>
                                    <Tooltip text="View Details" />
                                 </tooltip>
                              </Button>
                              <Button onAction="#selectTable" styleClass="table-action-btn, select-button" text="Select" userData="3">
                                 <tooltip>
                                    <Tooltip text="Select Table" />
                                 </tooltip>
                              </Button>
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 4 - Ready -->
                  <VBox fx:id="table4" styleClass="table-card, table-preparing" GridPane.columnIndex="0" GridPane.rowIndex="1">
                     <children>
                        <Label styleClass="table-number" text="Table 4">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        <HBox alignment="CENTER" spacing="5.0">
                           <children>
                              <Label text="👥" />
                              <Label text="4 seats" />
                           </children>
                        </HBox>
                        <Label styleClass="order-badge" text="Order #1002" />
                        <Label styleClass="table-status" text="Ready to Serve" />
                        <Region VBox.vgrow="ALWAYS" />
                        <HBox alignment="CENTER" spacing="8.0" styleClass="table-actions">
                           <children>
                              <Button onAction="#viewTableDetails" styleClass="table-action-btn, eye-button" text="👁️" userData="4">
                                 <tooltip>
                                    <Tooltip text="View Order Details" />
                                 </tooltip>
                              </Button>
                              <Button onAction="#addItemsToTable" styleClass="table-action-btn, add-button" text="+" userData="4">
                                 <tooltip>
                                    <Tooltip text="Add Items" />
                                 </tooltip>
                              </Button>
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 5 - Occupied -->
                  <VBox fx:id="table5" onMouseClicked="#selectTable" styleClass="table-card, table-occupied" GridPane.columnIndex="1" GridPane.rowIndex="1">
                     <children>
                        <Label styleClass="table-number" text="Table 5">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        <HBox alignment="CENTER" spacing="5.0">
                           <children>
                              <Label text="👥" />
                              <Label text="2 seats" />
                           </children>
                        </HBox>
                        <Label styleClass="order-badge" text="Order #125" />
                        <Label styleClass="table-status" text="Occupied" />
                     </children>
                  </VBox>
                  
                  <!-- Table 6 - Preparing -->
                  <VBox fx:id="table6" onMouseClicked="#selectTable" styleClass="table-card, table-preparing" GridPane.columnIndex="2" GridPane.rowIndex="1">
                     <children>
                        <Label styleClass="table-number" text="Table 6">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        <HBox alignment="CENTER" spacing="5.0">
                           <children>
                              <Label text="👥" />
                              <Label text="4 seats" />
                           </children>
                        </HBox>
                        <Label styleClass="order-badge" text="Order #126" />
                        <Label styleClass="table-status" text="Preparing" />
                     </children>
                  </VBox>
                  
                  <!-- Table 7 - Free -->
                  <VBox fx:id="table7" onMouseClicked="#selectTable" styleClass="table-card, table-free" GridPane.columnIndex="0" GridPane.rowIndex="2">
                     <children>
                        <Label styleClass="table-number" text="Table 7">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        <HBox alignment="CENTER" spacing="5.0">
                           <children>
                              <Label text="👥" />
                              <Label text="8 seats" />
                           </children>
                        </HBox>
                        <Label styleClass="table-status" text="Free" />
                     </children>
                  </VBox>
                  
                  <!-- Table 8 - Occupied -->
                  <VBox fx:id="table8" onMouseClicked="#selectTable" styleClass="table-card, table-occupied" GridPane.columnIndex="1" GridPane.rowIndex="2">
                     <children>
                        <Label styleClass="table-number" text="Table 8">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        <HBox alignment="CENTER" spacing="5.0">
                           <children>
                              <Label text="👥" />
                              <Label text="4 seats" />
                           </children>
                        </HBox>
                        <Label styleClass="order-badge" text="Order #127" />
                        <Label styleClass="table-status" text="Occupied" />
                     </children>
                  </VBox>
                  
                  <!-- Table 9 - Free -->
                  <VBox fx:id="table9" onMouseClicked="#selectTable" styleClass="table-card, table-free" GridPane.columnIndex="2" GridPane.rowIndex="2">
                     <children>
                        <Label styleClass="table-number" text="Table 9">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        <HBox alignment="CENTER" spacing="5.0">
                           <children>
                              <Label text="👥" />
                              <Label text="2 seats" />
                           </children>
                        </HBox>
                        <Label styleClass="table-status" text="Free" />
                     </children>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="40.0" left="40.0" right="40.0" top="40.0" />
               </padding>
            </GridPane>
         </content>
      </ScrollPane>
   </children>
</VBox>
