<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.PurchaseOrderRequestController">
   <top>
      <VBox styleClass="header-section">
         <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="page-header">
            <Button fx:id="backButton" onAction="#goBack" styleClass="back-button" text="← Back" />
            <Label styleClass="page-title" text="📋 Purchase Order Request" />
            <Label styleClass="page-subtitle" text="Request items from suppliers" />
         </HBox>
      </VBox>
   </top>
   
   <center>
      <ScrollPane fitToWidth="true" styleClass="content-scroll">
         <VBox spacing="25.0" styleClass="main-content">
            <padding>
               <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
            </padding>
            
            <!-- Purchase Order Request Form -->
            <VBox styleClass="purchase-request-form">
               <!-- Supplier Selection -->
               <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="supplier-header">
                  <Label styleClass="supplier-label" text="From:" />
                  <ComboBox fx:id="supplierComboBox" styleClass="supplier-combo" promptText="Select Supplier" />
               </HBox>
               
               <!-- Item Request Form -->
               <VBox spacing="20.0" styleClass="item-request-section">
                  <!-- Form Fields -->
                  <GridPane hgap="15.0" vgap="15.0" styleClass="request-form-grid">
                     <!-- Item Name -->
                     <Label text="Item Name" styleClass="form-label" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                     <ComboBox fx:id="itemComboBox" styleClass="item-combo" promptText="Select Item" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                     
                     <!-- Quantity -->
                     <Label text="Qty" styleClass="form-label" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                     <TextField fx:id="quantityField" styleClass="quantity-field" promptText="Enter quantity" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                     
                     <!-- Unit -->
                     <Label text="Unit" styleClass="form-label" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                     <ComboBox fx:id="unitComboBox" styleClass="unit-combo" promptText="Unit" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                  </GridPane>
                  
                  <!-- Current Stock Display -->
                  <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="current-stock-section">
                     <Label fx:id="currentStockLabel" styleClass="current-stock-label" text="Current Stock: --" />
                  </HBox>
                  
                  <!-- Action Buttons -->
                  <HBox spacing="15.0" alignment="CENTER_LEFT" styleClass="action-buttons-section">
                     <Button fx:id="saveButton" onAction="#saveRequest" styleClass="save-button" text="Save" />
                     <Button fx:id="sendRequestButton" onAction="#sendRequest" styleClass="send-request-button" text="Send request" />
                  </HBox>
               </VBox>
            </VBox>
            
            <!-- Purchase Orders List -->
            <VBox spacing="15.0" styleClass="purchase-orders-section">
               <Label styleClass="section-title" text="Purchase Orders" />
               
               <!-- Orders Table -->
               <VBox styleClass="orders-table">
                  <!-- Table Header -->
                  <HBox styleClass="orders-table-header">
                     <Label styleClass="table-header-cell" text="From" HBox.hgrow="ALWAYS" />
                     <Label styleClass="table-header-cell" text="PO No." />
                     <Label styleClass="table-header-cell" text="Total" />
                     <Label styleClass="table-header-cell" text="Status" />
                  </HBox>
                  
                  <!-- Table Content -->
                  <VBox fx:id="ordersContainer" styleClass="orders-table-content">
                     <!-- Purchase order rows will be added dynamically -->
                  </VBox>
               </VBox>
            </VBox>
         </VBox>
      </ScrollPane>
   </center>
</BorderPane>
