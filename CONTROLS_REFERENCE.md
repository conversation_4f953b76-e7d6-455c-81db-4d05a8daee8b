# 🎮 Restaurant Management System - Controls Reference

## 📋 **Table of Contents**
1. [Login Page Controls](#login-page-controls)
2. [Universal Navigation Controls](#universal-navigation-controls)
3. [Dashboard Shortcuts](#dashboard-shortcuts)
4. [Table Management Controls](#table-management-controls)
5. [Menu Selection Controls](#menu-selection-controls)
6. [Billing & KOT Controls](#billing--kot-controls)
7. [General Application Controls](#general-application-controls)

---

## 🔐 **Login Page Controls**

### **Progressive Navigation**
| Key | Action | Description |
|-----|--------|-------------|
| `Enter` (Username field) | Move to Password | Focus shifts to password field |
| `Enter` (Password field) | Move to Role | Focus shifts to role selection + opens dropdown |
| `Enter` (Role field) | Submit Login | Submits the login form |
| `Tab` | Next Field | Standard tab navigation between fields |
| `Shift + Tab` | Previous Field | Reverse tab navigation |

### **Mouse Controls**
- **Click Login Button** - Submit login form
- **Click Role Dropdown** - Open role selection menu

---

## 🧭 **Universal Navigation Controls**

### **ESC Key Navigation**
| Key Combination | Action | Description |
|-----------------|--------|-------------|
| `ESC` (Single press) | Normal Back | Go back one step in navigation history |
| `ESC ESC` (Double press) | Quick Dashboard | Return directly to dashboard (500ms window) |

### **Back Button Navigation**
- **All Back Buttons** - Use robust navigation system
- **Automatic Fallback** - If navigation fails, returns to dashboard
- **History Tracking** - Maintains navigation history for smart back navigation

---

## 🏠 **Dashboard Shortcuts**

### **Quick Actions**
| Shortcut | Action | Description |
|----------|--------|-------------|
| `Ctrl + K` | Billing & KOT | Open billing and KOT management |
| `Ctrl + S` | Save Order | Save current order |
| `Ctrl + H` | Hold KOT | Hold current KOT |
| `Ctrl + F` | Search | Open search functionality |
| `Ctrl + P` | Print KOT | Print kitchen order ticket |
| `Ctrl + D` | Order Discount | Apply discount to order |
| `Ctrl + A` | Settings/Admin | Open settings or admin panel |
| `Ctrl + L` | Log Out | Log out of the system |
| `Ctrl + M` | Menu Management | Open menu management |
| `Ctrl + U` | User Management | Open user management |
| `Ctrl + T` | See Tables | Open table management view |
| `Ctrl + R` | Reprint Last Bill | Reprint the last bill |

### **Quick Payment Shortcuts**
| Shortcut | Action | Description |
|----------|--------|-------------|
| `Ctrl + 1` | Quick Cash Payment | Process cash payment |
| `Ctrl + 2` | Quick Card Payment | Process card payment |
| `Ctrl + 3` | Quick UPI Payment | Process UPI payment |

---

## 🍽️ **Table Management Controls**

### **Table Selection**
| Key | Action | Description |
|-----|--------|-------------|
| `1-9` | Select Table | Direct table selection by number |
| `Numpad 1-9` | Select Table | Numpad table selection |
| `Enter` | Confirm Selection | Confirm table selection and proceed |
| `ESC` | Clear/Back | Clear selection or go back |
| `Backspace` | Clear Input | Clear current table number input |

### **Table Actions**
| Key Combination | Action | Description |
|-----------------|--------|-------------|
| `Ctrl + S` | Search Tables | Open table search mode |
| `Ctrl + V` | View Table Details | View selected table details |
| `Ctrl + A` | Add Items | Add items to selected table |

### **Search Mode**
- **Type to Search** - Filter tables by number or status
- **Enter** - Select first visible table from search results
- **ESC** - Exit search mode

---

## 🍜 **Menu Selection Controls**

### **Navigation**
| Key | Action | Description |
|-----|--------|-------------|
| `ESC` | Back/Cancel | Go back or cancel current action |
| `Enter` | Confirm/Finish | Confirm selection or finish order |
| `Ctrl + Enter` | Force Confirm | Force confirm current action |
| `Ctrl + K` | Billing & KOT | Open table-specific billing (high priority) |

### **Menu Categories**
- **Click Tabs** - Switch between Dine-In, Takeaway, Delivery
- **Scroll** - Navigate through menu items
- **Click Items** - Add items to order

---

## 💰 **Billing & KOT Controls**

### **Key Navigation**
| Key | Action | Description |
|-----|--------|-------------|
| `ESC` | Back Navigation | Use universal navigation (single/double ESC) |
| `Backspace` | Remove/Edit | Remove items or edit quantities |
| `Enter` | Confirm Action | Confirm current billing action |
| `Ctrl + Enter` | Force Process | Force process current transaction |

### **Table Selection in Billing**
| Key | Action | Description |
|-----|--------|-------------|
| `1-9` | Select Table | Quick table selection |
| `Numpad 1-9` | Select Table | Numpad table selection |
| `Ctrl + T` | Table List | Show table selection list |

---

## ⚙️ **General Application Controls**

### **Universal Shortcuts**
| Shortcut | Action | Description |
|----------|--------|-------------|
| `F1` | Help | Show help information |
| `F5` | Refresh | Refresh current view |
| `F11` | Fullscreen | Toggle fullscreen mode |
| `Alt + F4` | Exit | Close application |

### **Text Input Controls**
| Key | Action | Description |
|-----|--------|-------------|
| `Ctrl + A` | Select All | Select all text in input field |
| `Ctrl + C` | Copy | Copy selected text |
| `Ctrl + V` | Paste | Paste text from clipboard |
| `Ctrl + X` | Cut | Cut selected text |
| `Ctrl + Z` | Undo | Undo last action |

### **Scroll Controls**
| Action | Description |
|--------|-------------|
| **Mouse Wheel** | Scroll up/down in lists and menus |
| **Page Up/Down** | Navigate through long lists |
| **Home/End** | Go to beginning/end of lists |

---

## 🎯 **Special Features**

### **Smart Navigation**
- **Navigation History** - System remembers where you came from
- **Intelligent Fallback** - Always provides a way back to dashboard
- **Context-Aware ESC** - ESC behavior adapts to current screen

### **Accessibility**
- **Full Keyboard Navigation** - Complete system usable without mouse
- **Progressive Tab Order** - Logical tab sequence through all controls
- **Visual Focus Indicators** - Clear indication of current focus
- **Screen Reader Support** - Compatible with accessibility tools

### **Error Recovery**
- **Robust Navigation** - Multiple fallback methods prevent navigation failures
- **Graceful Degradation** - System continues working even if some features fail
- **User-Friendly Alerts** - Clear error messages with suggested actions

---

## 📱 **Touch/Mobile Controls**

### **Touch Gestures**
- **Tap** - Select items, buttons, and controls
- **Swipe** - Navigate between screens (where supported)
- **Pinch to Zoom** - Zoom in/out on content (where supported)
- **Long Press** - Context menus and additional options

---

## 🔧 **Advanced Controls**

### **Power User Shortcuts**
| Shortcut | Action | Description |
|----------|--------|-------------|
| `Ctrl + Shift + D` | Debug Mode | Enable debug information |
| `Ctrl + Shift + R` | Force Refresh | Force refresh all data |
| `Ctrl + Shift + C` | Clear Cache | Clear application cache |

### **Admin Controls**
| Shortcut | Action | Description |
|----------|--------|-------------|
| `Ctrl + Alt + A` | Admin Panel | Quick access to admin functions |
| `Ctrl + Alt + U` | User Management | Quick user management access |
| `Ctrl + Alt + S` | System Settings | Quick system settings access |

---

## 💡 **Tips for Efficient Use**

1. **Use Double ESC** for quick dashboard return from anywhere
2. **Memorize Ctrl+K** for instant billing access
3. **Use number keys** for quick table selection
4. **Progressive Enter** on login page for fastest login
5. **ESC always works** - your universal "go back" key
6. **Tab navigation** works throughout the entire system
7. **Context matters** - same keys may do different things in different screens

---

## 🆘 **Emergency Controls**

| Situation | Control | Action |
|-----------|---------|--------|
| **Lost/Stuck** | `ESC ESC` | Return to dashboard |
| **Can't Navigate** | `Alt + F4` | Close and restart application |
| **System Frozen** | `Ctrl + Alt + Del` | Windows task manager |
| **Need Help** | `F1` | Show help information |

---

*This control reference covers all implemented keyboard shortcuts and navigation controls in the Restaurant Management System. The system is designed to be fully accessible via keyboard while maintaining full mouse/touch support.*
