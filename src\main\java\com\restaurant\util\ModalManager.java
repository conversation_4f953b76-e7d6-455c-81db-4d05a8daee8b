package com.restaurant.util;

import javafx.application.Platform;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.ScrollPane;
import javafx.scene.layout.Region;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.stage.StageStyle;

import java.util.ArrayList;
import java.util.List;

/**
 * Comprehensive Modal Manager for proper modal behavior and scrolling
 */
public class ModalManager {
    
    private static ModalManager instance;
    private List<Stage> openModals;
    private Stage primaryStage;
    
    private ModalManager() {
        openModals = new ArrayList<>();
    }
    
    public static ModalManager getInstance() {
        if (instance == null) {
            instance = new ModalManager();
        }
        return instance;
    }
    
    public void setPrimaryStage(Stage primaryStage) {
        this.primaryStage = primaryStage;
    }
    
    /**
     * Create and show a modal with proper scrolling and sizing
     */
    public Stage createModal(String title, Parent content, double width, double height) {
        Stage modal = new Stage();
        modal.initModality(Modality.APPLICATION_MODAL);
        modal.initStyle(StageStyle.DECORATED);
        modal.setTitle(title);
        
        if (primaryStage != null) {
            modal.initOwner(primaryStage);
        }
        
        // Create modal container with proper scrolling
        StackPane modalContainer = createModalContainer(content, width, height);
        
        Scene scene = new Scene(modalContainer, width, height);
        scene.getStylesheets().add(getClass().getResource("/css/application.css").toExternalForm());
        
        modal.setScene(scene);
        modal.setResizable(true);
        modal.setMinWidth(400);
        modal.setMinHeight(300);
        
        // Center modal on screen
        modal.centerOnScreen();
        
        // Lock main page scrolling when modal opens
        lockMainPageScrolling();
        
        // Handle modal close events
        modal.setOnCloseRequest(event -> {
            closeModal(modal);
        });
        
        modal.setOnHidden(event -> {
            closeModal(modal);
        });
        
        // Add to open modals list
        openModals.add(modal);
        
        modal.show();
        
        System.out.println("Modal created and shown: " + title);
        return modal;
    }
    
    /**
     * Create modal container with proper scrolling
     */
    private StackPane createModalContainer(Parent content, double width, double height) {
        StackPane container = new StackPane();
        container.getStyleClass().add("modal-container");
        
        // Create scroll pane for modal content
        ScrollPane scrollPane = new ScrollPane();
        scrollPane.getStyleClass().add("modal-scroll-pane");
        scrollPane.setFitToWidth(true);
        scrollPane.setFitToHeight(false);
        scrollPane.setHbarPolicy(ScrollPane.ScrollBarPolicy.NEVER);
        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setPannable(false);
        
        // Configure content sizing
        if (content instanceof Region) {
            Region contentRegion = (Region) content;
            contentRegion.setMaxWidth(width - 40); // Account for padding
            contentRegion.setPrefWidth(width - 40);
        }
        
        scrollPane.setContent(content);
        
        // Configure scroll pane with our utility
        Platform.runLater(() -> {
            configureModalScrollPane(scrollPane);
        });
        
        container.getChildren().add(scrollPane);
        
        return container;
    }
    
    /**
     * Configure modal scroll pane for proper scrolling
     */
    private void configureModalScrollPane(ScrollPane scrollPane) {
        // Set maximum height to ensure scrolling works
        double maxHeight = 600; // Maximum modal content height
        scrollPane.setMaxHeight(maxHeight);
        scrollPane.setPrefHeight(maxHeight);
        
        // Force scroll bar visibility when needed
        Node content = scrollPane.getContent();
        if (content instanceof Region) {
            Region contentRegion = (Region) content;
            
            // Add listener to show scroll bar when content exceeds height
            contentRegion.heightProperty().addListener((obs, oldVal, newVal) -> {
                if (newVal.doubleValue() > maxHeight - 20) {
                    scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.ALWAYS);
                } else {
                    scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED);
                }
            });
        }
        
        // Configure with our scroll utility
        ScrollPaneUtil.configureScrollPane(scrollPane);
        
        System.out.println("Modal scroll pane configured");
    }
    
    /**
     * Close modal and restore main page scrolling
     */
    public void closeModal(Stage modal) {
        if (openModals.contains(modal)) {
            openModals.remove(modal);
            
            // If no more modals are open, restore main page scrolling
            if (openModals.isEmpty()) {
                restoreMainPageScrolling();
            }
            
            if (modal.isShowing()) {
                modal.close();
            }
            
            System.out.println("Modal closed and scrolling restored");
        }
    }
    
    /**
     * Lock main page scrolling when modal is open
     */
    private void lockMainPageScrolling() {
        if (primaryStage != null && primaryStage.getScene() != null) {
            Parent root = primaryStage.getScene().getRoot();
            lockScrolling(root);
        }
    }
    
    /**
     * Restore main page scrolling when modal is closed
     */
    private void restoreMainPageScrolling() {
        if (primaryStage != null && primaryStage.getScene() != null) {
            Parent root = primaryStage.getScene().getRoot();
            restoreScrolling(root);
        }
    }
    
    /**
     * Recursively lock scrolling on all scroll panes
     */
    private void lockScrolling(Parent parent) {
        for (Node child : parent.getChildrenUnmodifiable()) {
            if (child instanceof ScrollPane) {
                ScrollPane scrollPane = (ScrollPane) child;
                scrollPane.setDisable(true);
            } else if (child instanceof Parent) {
                lockScrolling((Parent) child);
            }
        }
    }
    
    /**
     * Recursively restore scrolling on all scroll panes
     */
    private void restoreScrolling(Parent parent) {
        for (Node child : parent.getChildrenUnmodifiable()) {
            if (child instanceof ScrollPane) {
                ScrollPane scrollPane = (ScrollPane) child;
                scrollPane.setDisable(false);
                // Refresh scroll pane
                Platform.runLater(() -> {
                    ScrollPaneUtil.refreshScrollPane(scrollPane);
                });
            } else if (child instanceof Parent) {
                restoreScrolling((Parent) child);
            }
        }
    }
    
    /**
     * Close all open modals
     */
    public void closeAllModals() {
        List<Stage> modalsToClose = new ArrayList<>(openModals);
        for (Stage modal : modalsToClose) {
            closeModal(modal);
        }
    }
    
    /**
     * Check if any modals are open
     */
    public boolean hasOpenModals() {
        return !openModals.isEmpty();
    }
    
    /**
     * Get count of open modals
     */
    public int getOpenModalCount() {
        return openModals.size();
    }
}
