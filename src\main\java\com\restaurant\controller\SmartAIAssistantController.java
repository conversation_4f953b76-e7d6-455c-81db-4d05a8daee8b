package com.restaurant.controller;

import javafx.animation.*;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.shape.Circle;
import javafx.scene.text.Text;
import javafx.util.Duration;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;

import java.net.URL;
import java.util.*;
import java.time.LocalDateTime;


import com.restaurant.service.SmartAIService;
import com.restaurant.model.AIResponse;
import com.restaurant.model.ChatMessage;

public class SmartAIAssistantController implements Initializable {

    // Header Components
    @FXML private VBox assistantContainer;
    @FXML private Button clearChatButton;
    @FXML private MenuButton settingsMenu;
    @FXML private CheckMenuItem voiceEnabledItem;
    @FXML private CheckMenuItem autoSuggestItem;
    @FXML private CheckMenuItem soundEffectsItem;
    
    // Chat Area
    @FXML private ScrollPane chatScrollPane;
    @FXML private VBox chatMessagesContainer;
    
    // Quick Suggestions
    @FXML private HBox quickSuggestionsBar;
    @FXML private HBox quickSuggestionsContainer;
    
    // Input Components
    @FXML private VBox autoSuggestionsContainer;
    @FXML private ListView<String> autoSuggestionsList;
    @FXML private TextField messageInput;
    @FXML private Circle voicePulse;
    @FXML private Button voiceButton;
    @FXML private Label voiceIcon;
    @FXML private Tooltip voiceTooltip;
    @FXML private Button sendButton;
    
    // Input Status
    @FXML private HBox inputStatusBar;
    @FXML private Label inputStatusIcon;
    @FXML private Text inputStatusText;
    @FXML private Button cancelInputButton;
    


    // Services and State
    private SmartAIService aiService;
    private List<ChatMessage> chatHistory;
    private boolean isDarkTheme = false;
    private boolean isListening = false;
    private boolean isProcessing = false;
    private Timeline typingAnimation;
    private Timeline voicePulseAnimation;
    private String lastUserMessage = "";
    private AIResponse lastResponse = null;
    
    // Auto-suggestions
    private ObservableList<String> autoSuggestions;
    private List<String> commonCommands;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        aiService = new SmartAIService();
        chatHistory = new ArrayList<>();
        autoSuggestions = FXCollections.observableArrayList();
        
        initializeCommonCommands();
        setupEventHandlers();
        setupAnimations();
        setupAutoSuggestions();
        
        // Initialize status
        System.out.println("Smart AI Assistant initialized successfully");
        
        // Focus on input field
        Platform.runLater(() -> messageInput.requestFocus());
    }

    private void initializeCommonCommands() {
        commonCommands = Arrays.asList(
            // Sales & Forecasting
            "show today's sales summary",
            "forecast sales for next 7 days",
            "forecast sales for next 15 days",
            "forecast sales for next 30 days",
            "what will be the sales trend for biryani next month",
            "show me the growth rate this year",
            "predict revenue for next week",
            "forecast demand for beverages",
            
            // Billing & Orders
            "create an order for table 4",
            "add 3 butter naan and 2 cokes to table 6",
            "show the bill for table 2",
            "place order for table 5",
            "remove item from table 3",
            "update order for table 1",
            "print bill for table 7",
            
            // Inventory & Stock
            "what items are low in stock",
            "update cheese quantity to 5 kg",
            "show stock status for beverages",
            "check inventory levels",
            "add new stock item",
            "update stock for paneer",
            "show expired items",
            
            // Analytics & Reports
            "give me today's sales summary",
            "compare last week vs this week",
            "download the daily sales report",
            "show monthly revenue report",
            "analyze customer preferences",
            "show peak hours analysis",
            "generate profit margin report",
            
            // Menu & Search
            "show all vegetarian dishes under ₹100",
            "search for tandoori starters",
            "which item sells the most",
            "show popular menu items",
            "find spicy dishes",
            "search chicken items",
            "show dessert menu",
            
            // Navigation & Control
            "go to dashboard",
            "open AI forecaster",
            "reset all filters",
            "log out",
            "open table management",
            "show reports section",
            "navigate to billing"
        );
    }

    private void setupEventHandlers() {
        // Message input handlers
        messageInput.textProperty().addListener((obs, oldText, newText) -> {
            sendButton.setDisable(newText.trim().isEmpty());
            
            if (autoSuggestItem.isSelected()) {
                updateAutoSuggestions(newText);
            }
        });

        // Auto-suggestions list handler
        autoSuggestionsList.setItems(autoSuggestions);
        autoSuggestionsList.setOnMouseClicked(event -> {
            if (event.getClickCount() == 2) {
                String selected = autoSuggestionsList.getSelectionModel().getSelectedItem();
                if (selected != null) {
                    messageInput.setText(selected);
                    hideAutoSuggestions();
                    sendMessage();
                }
            }
        });

        // Settings handlers
        voiceEnabledItem.selectedProperty().addListener((obs, oldVal, newVal) -> {
            voiceButton.setDisable(!newVal);
            if (!newVal && isListening) {
                cancelVoiceInput();
            }
        });
    }

    private void setupAnimations() {
        // Typing indicator animation (simplified)
        typingAnimation = new Timeline(
            new KeyFrame(Duration.millis(500), e -> {}),
            new KeyFrame(Duration.millis(1000), e -> {}),
            new KeyFrame(Duration.millis(1500), e -> {})
        );
        typingAnimation.setCycleCount(Timeline.INDEFINITE);

        // Voice pulse animation
        voicePulseAnimation = new Timeline(
            new KeyFrame(Duration.ZERO, 
                new KeyValue(voicePulse.radiusProperty(), 20.0),
                new KeyValue(voicePulse.opacityProperty(), 1.0)),
            new KeyFrame(Duration.seconds(1), 
                new KeyValue(voicePulse.radiusProperty(), 30.0),
                new KeyValue(voicePulse.opacityProperty(), 0.0))
        );
        voicePulseAnimation.setCycleCount(Timeline.INDEFINITE);
    }

    private void setupAutoSuggestions() {
        autoSuggestionsList.setCellFactory(listView -> new ListCell<String>() {
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                    setGraphic(null);
                } else {
                    setText(item);
                    setStyle("-fx-padding: 8px; -fx-font-size: 13px;");
                }
            }
        });
    }

    @FXML
    private void handleKeyPressed(KeyEvent event) {
        if (event.getCode() == KeyCode.ENTER) {
            if (autoSuggestionsContainer.isVisible() && !autoSuggestions.isEmpty()) {
                // Use first suggestion if auto-suggestions are visible
                messageInput.setText(autoSuggestions.get(0));
                hideAutoSuggestions();
            }
            sendMessage();
        } else if (event.getCode() == KeyCode.ESCAPE) {
            hideAutoSuggestions();
        } else if (event.getCode() == KeyCode.DOWN && autoSuggestionsContainer.isVisible()) {
            autoSuggestionsList.requestFocus();
            autoSuggestionsList.getSelectionModel().selectFirst();
        }
    }

    @FXML
    private void sendMessage() {
        String message = messageInput.getText().trim();
        if (message.isEmpty()) return;

        lastUserMessage = message;
        hideAutoSuggestions();
        
        // Add user message to chat
        addUserMessage(message);
        
        // Clear input and disable send button
        messageInput.clear();
        sendButton.setDisable(true);
        
        // Show typing indicator
        showTypingIndicator();
        
        // Process message with AI
        processMessageWithAI(message);
    }

    @FXML
    private void handleVoiceInput() {
        if (!voiceEnabledItem.isSelected()) return;
        
        if (isListening) {
            stopVoiceInput();
        } else {
            startVoiceInput();
        }
    }

    private void startVoiceInput() {
        isListening = true;
        voiceIcon.setText("🔴");
        voiceTooltip.setText("Click to stop listening");
        voicePulse.setVisible(true);
        voicePulseAnimation.play();
        
        inputStatusBar.setVisible(true);
        inputStatusIcon.setText("🎙️");
        inputStatusText.setText("Listening... Speak now");
        
        System.out.println("Listening for your voice command...");
        
        // Simulate voice recognition
        simulateVoiceRecognition();
    }

    private void stopVoiceInput() {
        isListening = false;
        voiceIcon.setText("🎤");
        voiceTooltip.setText("Click to speak (or hold Ctrl+Space)");
        voicePulse.setVisible(false);
        voicePulseAnimation.stop();
        
        inputStatusBar.setVisible(false);
        System.out.println("Processing voice input...");
    }

    @FXML
    private void cancelVoiceInput() {
        stopVoiceInput();
        System.out.println("Voice input cancelled");
    }

    private void simulateVoiceRecognition() {
        Task<String> voiceTask = new Task<String>() {
            @Override
            protected String call() throws Exception {
                // Simulate voice recognition delay
                Thread.sleep(2000 + (int)(Math.random() * 2000));
                
                // Return a sample voice command
                String[] sampleCommands = {
                    "show today's sales summary",
                    "forecast sales for next 15 days",
                    "what items are low in stock",
                    "create an order for table 4",
                    "show all vegetarian dishes under 100 rupees",
                    "compare last week vs this week"
                };
                
                return sampleCommands[(int)(Math.random() * sampleCommands.length)];
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    String recognizedText = getValue();
                    messageInput.setText(recognizedText);
                    stopVoiceInput();
                    
                    // Auto-send the recognized command
                    Platform.runLater(() -> {
                        try {
                            Thread.sleep(500); // Brief pause to show the recognized text
                            sendMessage();
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                    });
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    stopVoiceInput();
                    System.out.println("Voice recognition failed. Please try again.");
                    addAssistantMessage("Sorry, I couldn't understand your voice command. Please try speaking again or type your message.", "error");
                });
            }
        };

        new Thread(voiceTask).start();
    }

    private void processMessageWithAI(String message) {
        isProcessing = true;
        
        Task<AIResponse> aiTask = new Task<AIResponse>() {
            @Override
            protected AIResponse call() throws Exception {
                // Simulate AI processing time
                Thread.sleep(1000 + (int)(Math.random() * 2000));
                
                return aiService.processMessage(message);
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    AIResponse response = getValue();
                    lastResponse = response;
                    
                    hideTypingIndicator();
                    addAssistantResponse(response);
                    
                    isProcessing = false;
                    System.out.println("Ready for your next question");
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    hideTypingIndicator();
                    addAssistantMessage("I'm sorry, I encountered an error processing your request. Please try again.", "error");
                    isProcessing = false;
                    System.out.println("Error occurred. Ready to try again.");
                });
            }
        };

        new Thread(aiTask).start();
    }

    private void addUserMessage(String message) {
        ChatMessage chatMessage = new ChatMessage(message, "user", LocalDateTime.now());
        chatHistory.add(chatMessage);
        
        VBox messageContainer = createUserMessageUI(message);
        chatMessagesContainer.getChildren().add(messageContainer);
        scrollToBottom();
    }

    private void addAssistantMessage(String message, String type) {
        ChatMessage chatMessage = new ChatMessage(message, "assistant", LocalDateTime.now());
        chatHistory.add(chatMessage);
        
        VBox messageContainer = createAssistantMessageUI(message, type);
        chatMessagesContainer.getChildren().add(messageContainer);
        scrollToBottom();
    }

    private void addAssistantResponse(AIResponse response) {
        ChatMessage chatMessage = new ChatMessage(response.getMessage(), "assistant", LocalDateTime.now());
        chatHistory.add(chatMessage);
        
        VBox messageContainer = createAssistantResponseUI(response);
        chatMessagesContainer.getChildren().add(messageContainer);
        scrollToBottom();
    }

    private VBox createUserMessageUI(String message) {
        VBox container = new VBox();
        container.getStyleClass().add("modern-message-container");
        container.setAlignment(javafx.geometry.Pos.CENTER_RIGHT);

        // User message (right-aligned)
        HBox messageRow = new HBox();
        messageRow.setSpacing(12.0);
        messageRow.setAlignment(javafx.geometry.Pos.CENTER_RIGHT);

        // Message bubble
        VBox bubble = new VBox();
        bubble.getStyleClass().add("user-message-bubble");

        Text messageText = new Text(message);
        messageText.getStyleClass().add("user-message-text");
        messageText.setWrappingWidth(400);

        bubble.getChildren().add(messageText);

        // User avatar
        VBox avatarContainer = new VBox();
        avatarContainer.getStyleClass().add("modern-avatar-container");
        avatarContainer.setAlignment(javafx.geometry.Pos.CENTER);

        Label avatar = new Label("👤");
        avatar.getStyleClass().add("modern-message-avatar");
        avatar.setStyle("-fx-background-color: rgba(59, 130, 246, 0.1); -fx-text-fill: #3b82f6;");

        avatarContainer.getChildren().add(avatar);

        messageRow.getChildren().addAll(bubble, avatarContainer);
        container.getChildren().add(messageRow);

        return container;
    }

    private VBox createAssistantMessageUI(String message, String type) {
        VBox container = new VBox();
        container.getStyleClass().add("modern-message-container");

        // Assistant message (left-aligned)
        HBox messageRow = new HBox();
        messageRow.setSpacing(12.0);
        messageRow.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        // AI Avatar
        VBox avatarContainer = new VBox();
        avatarContainer.getStyleClass().add("modern-avatar-container");
        avatarContainer.setAlignment(javafx.geometry.Pos.CENTER);

        Label avatar = new Label("🤖");
        avatar.getStyleClass().add("modern-message-avatar");

        avatarContainer.getChildren().add(avatar);

        // Message bubble
        VBox bubble = new VBox();
        bubble.getStyleClass().add("modern-message-bubble");
        if ("error".equals(type)) {
            bubble.setStyle("-fx-background-color: rgba(248, 113, 113, 0.1); -fx-border-color: rgba(248, 113, 113, 0.3);");
        }

        Text messageText = new Text(message);
        messageText.getStyleClass().add("modern-message-text");
        messageText.setWrappingWidth(450);

        bubble.getChildren().add(messageText);

        messageRow.getChildren().addAll(avatarContainer, bubble);
        container.getChildren().add(messageRow);

        return container;
    }

    private VBox createAssistantResponseUI(AIResponse response) {
        VBox container = createAssistantMessageUI(response.getMessage(), "normal");

        // Add visual elements if present
        if (response.hasVisualData()) {
            VBox visualContainer = new VBox();
            visualContainer.setSpacing(8.0);
            visualContainer.setStyle("-fx-padding: 12px 0 0 0; -fx-border-color: rgba(229, 231, 235, 0.5); -fx-border-width: 1px 0 0 0;");

            // Add charts, tables, or other visual elements
            if (response.getChartData() != null) {
                Label chartPlaceholder = new Label("📊 " + response.getChartData());
                chartPlaceholder.setStyle("-fx-background-color: rgba(59, 130, 246, 0.05); -fx-text-fill: #3b82f6; -fx-background-radius: 8px; -fx-border-radius: 8px; -fx-border-color: rgba(59, 130, 246, 0.2); -fx-border-width: 1px; -fx-padding: 12px; -fx-font-size: 13px;");
                visualContainer.getChildren().add(chartPlaceholder);
            }

            if (response.getTableData() != null) {
                Label tablePlaceholder = new Label("📋 " + response.getTableData());
                tablePlaceholder.setStyle("-fx-background-color: rgba(59, 130, 246, 0.05); -fx-text-fill: #3b82f6; -fx-background-radius: 8px; -fx-border-radius: 8px; -fx-border-color: rgba(59, 130, 246, 0.2); -fx-border-width: 1px; -fx-padding: 12px; -fx-font-size: 13px;");
                visualContainer.getChildren().add(tablePlaceholder);
            }

            // Add action buttons
            if (response.getActionButtons() != null && !response.getActionButtons().isEmpty()) {
                HBox actionButtons = new HBox();
                actionButtons.setSpacing(8.0);
                actionButtons.setStyle("-fx-padding: 8px 0 0 0;");

                for (String action : response.getActionButtons()) {
                    Button actionBtn = new Button(action);
                    actionBtn.setStyle("-fx-background-color: #3b82f6; -fx-text-fill: white; -fx-background-radius: 8px; -fx-border-radius: 8px; -fx-padding: 6px 12px; -fx-font-size: 12px; -fx-cursor: hand;");
                    actionBtn.setOnAction(e -> handleResponseAction(action));
                    actionButtons.getChildren().add(actionBtn);
                }

                visualContainer.getChildren().add(actionButtons);
            }

            // Add visual container to the message bubble
            HBox messageRow = (HBox) container.getChildren().get(0);
            VBox bubble = (VBox) messageRow.getChildren().get(1);
            bubble.getChildren().add(visualContainer);
        }

        return container;
    }

    private void handleResponseAction(String action) {
        // Handle action button clicks from AI responses
        switch (action.toLowerCase()) {
            case "view details":
                showActionDetails();
                break;
            case "download report":
                downloadReport();
                break;
            case "open forecaster":
                openForecaster();
                break;
            case "go to tables":
                navigateToTables();
                break;
            default:
                addAssistantMessage("Executing: " + action, "normal");
        }
    }

    private void updateAutoSuggestions(String input) {
        if (input.length() < 2) {
            hideAutoSuggestions();
            return;
        }
        
        List<String> matches = commonCommands.stream()
            .filter(cmd -> cmd.toLowerCase().contains(input.toLowerCase()))
            .limit(5)
            .collect(java.util.stream.Collectors.toList());
        
        if (matches.isEmpty()) {
            hideAutoSuggestions();
        } else {
            autoSuggestions.setAll(matches);
            autoSuggestionsContainer.setVisible(true);
        }
    }

    private void hideAutoSuggestions() {
        autoSuggestionsContainer.setVisible(false);
    }

    private void showTypingIndicator() {
        typingAnimation.play();
        System.out.println("Thinking...");
    }

    private void hideTypingIndicator() {
        typingAnimation.stop();
    }

    private void scrollToBottom() {
        Platform.runLater(() -> {
            chatScrollPane.setVvalue(1.0);
        });
    }





    @FXML
    private void handleQuickSuggestion(javafx.event.ActionEvent event) {
        Button source = (Button) event.getSource();
        String suggestion = (String) source.getUserData();
        messageInput.setText(suggestion);
        sendMessage();
    }

    @FXML
    private void toggleTheme() {
        isDarkTheme = !isDarkTheme;

        if (isDarkTheme) {
            assistantContainer.getStyleClass().add("dark-theme");
        } else {
            assistantContainer.getStyleClass().remove("dark-theme");
        }
    }

    @FXML
    private void clearChat() {
        chatHistory.clear();
        chatMessagesContainer.getChildren().clear();
        
        // Re-add welcome message
        addAssistantMessage("Chat cleared! How can I help you today?", "normal");
        System.out.println("Ready to help with your restaurant operations");
    }

    @FXML
    private void exportChatHistory() {
        // Implementation for exporting chat history
        addAssistantMessage("Chat history export feature coming soon!", "normal");
    }

    @FXML
    private void showAssistantHelp() {
        String helpMessage = "🤖 **RestaurantBot Help**\n\n" +
            "I can help you with:\n" +
            "• 📊 Sales forecasting and analytics\n" +
            "• 💰 Billing and order management\n" +
            "• 📦 Inventory and stock control\n" +
            "• 📈 Reports and business insights\n" +
            "• 🔍 Menu search and recommendations\n" +
            "• 🧭 Navigation and app control\n\n" +
            "Try natural language like:\n" +
            "• 'Show me today's sales'\n" +
            "• 'What items are running low?'\n" +
            "• 'Forecast next week's revenue'\n" +
            "• 'Add chicken tikka to table 5'";
        
        addAssistantMessage(helpMessage, "normal");
    }

    @FXML
    private void repeatLastAction() {
        if (lastResponse != null && lastResponse.hasActions()) {
            addAssistantMessage("Repeating last action: " + lastResponse.getExecutedAction(), "normal");
        }
    }

    @FXML
    private void undoLastAction() {
        if (lastResponse != null && lastResponse.hasActions()) {
            addAssistantMessage("Undoing last action: " + lastResponse.getExecutedAction(), "normal");
        }
    }

    @FXML
    private void showActionDetails() {
        if (lastResponse != null) {
            addAssistantMessage("Action details: " + lastResponse.getActionDetails(), "normal");
        }
    }

    @FXML
    private void showContextualHelp() {
        addAssistantMessage("Here are some things you can try based on your current context:\n" +
            "• Ask about sales trends\n" +
            "• Check inventory levels\n" +
            "• Manage table orders\n" +
            "• Generate reports", "normal");
    }

    // Helper methods for action execution
    private void downloadReport() {
        addAssistantMessage("📥 Report download initiated. Check your downloads folder.", "normal");
    }

    private void openForecaster() {
        addAssistantMessage("🔮 Opening AI Forecaster module...", "normal");
    }

    private void navigateToTables() {
        addAssistantMessage("🪑 Navigating to table management...", "normal");
    }
}
