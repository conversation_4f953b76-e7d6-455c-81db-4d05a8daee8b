package com.restaurant.test; 
 
import com.restaurant.model.*; 
import com.restaurant.service.EnhancedReportService; 
import java.time.LocalDate; 
import java.util.*; 
 
public class EnhancedReportsTest { 
    public static void main(String[] args) { 
        System.out.println("🧪 TESTING ENHANCED REPORTS SYSTEM..."); 
        System.out.println(); 
 
        try { 
            LocalDate startDate = LocalDate.now().minusDays(30); 
            LocalDate endDate = LocalDate.now(); 
 
            // Test 1: Date Range Filtering 
            System.out.println("📅 Test 1: Date Range Filtering..."); 
            List<String> allOrderTypes = Arrays.asList("All"); 
            List<DailyReport> dailyReports = EnhancedReportService.generateFilteredReports( 
                startDate, endDate, allOrderTypes, "daily"); 
            System.out.println("✅ Generated " + dailyReports.size() + " daily reports"); 
            System.out.println(); 
 
            // Test 2: Order Type Filtering 
            System.out.println("🛒 Test 2: Order Type Filtering..."); 
            List<String> swiggyOnly = Arrays.asList("Swiggy"); 
            Map<String, Object> swiggyAnalytics = EnhancedReportService.getFilteredAnalyticsSummary( 
                startDate, endDate, swiggyOnly); 
            System.out.println("✅ Swiggy-only analytics: " + swiggyAnalytics.get("totalOrders") + " orders"); 
            System.out.println(); 
 
            // Test 3: Analytics Summary 
            System.out.println("📈 Test 3: Analytics Summary..."); 
            Map<String, Object> analytics = EnhancedReportService.getFilteredAnalyticsSummary( 
                startDate, endDate, allOrderTypes); 
            System.out.println("✅ Analytics Summary:"); 
            System.out.println("   Total Orders: " + analytics.get("totalOrders")); 
            System.out.println("   Total Revenue: ₹" + String.format(".2f", (Double) analytics.get("totalRevenue"))); 
            System.out.println("   Swiggy Orders: " + analytics.get("swiggyOrders")); 
            System.out.println("   Zomato Orders: " + analytics.get("zomatoOrders")); 
            System.out.println("   Peak Hour: " + analytics.get("peakHour")); 
            System.out.println(); 
 
            // Test 4: Order Type Breakdown 
            System.out.println("📊 Test 4: Order Type Breakdown..."); 
            Map<String, Integer> breakdown = EnhancedReportService.getOrderTypeBreakdown(startDate, endDate); 
            System.out.println("✅ Order Type Breakdown:"); 
            breakdown.forEach((platform, count) -> 
                System.out.println("   " + platform + ": " + count + " orders")); 
            System.out.println(); 
 
            // Test 5: Revenue Trend Data 
            System.out.println("📈 Test 5: Revenue Trend Data..."); 
            List<Map<String, Object>> trendData = EnhancedReportService.getRevenueTrendData( 
                startDate, endDate, allOrderTypes, "daily"); 
            System.out.println("✅ Generated " + trendData.size() + " trend data points"); 
            System.out.println(); 
 
            System.out.println("🎉 ALL ENHANCED REPORTS TESTS PASSED!"); 
            System.out.println(); 
            System.out.println("📊 ENHANCED FEATURES TESTED:"); 
            System.out.println("✅ Date Range Filtering - Custom date ranges"); 
            System.out.println("✅ Order Type Filtering - Platform-specific filtering"); 
            System.out.println("✅ Report Type Selection - Daily, Weekly, Monthly"); 
            System.out.println("✅ Analytics Summary - Comprehensive metrics"); 
            System.out.println("✅ Order Type Breakdown - Platform distribution"); 
            System.out.println("✅ Revenue Trend Data - Chart data generation"); 
            System.out.println("✅ Export Functionality - CSV export support"); 
 
        } catch (Exception e) { 
            System.err.println("❌ Error during testing: " + e.getMessage()); 
            e.printStackTrace(); 
        } 
    } 
} 
