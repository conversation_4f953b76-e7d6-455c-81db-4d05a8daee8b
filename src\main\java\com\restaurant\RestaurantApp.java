package com.restaurant;

import javafx.application.Application;
import javafx.application.Platform;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Alert;
import javafx.stage.Stage;
import javafx.stage.Screen;
import javafx.geometry.Rectangle2D;
import com.restaurant.util.ModalManager;
import com.restaurant.util.SceneEventHandlerManager;
import com.restaurant.model.DatabaseManager;

public class RestaurantApp extends Application {

    @Override
    public void start(Stage primaryStage) throws Exception {
        // Set up ModalManager with primary stage
        ModalManager.getInstance().setPrimaryStage(primaryStage);

        // Set up SceneEventHandlerManager with primary stage for reliable navigation
        SceneEventHandlerManager.getInstance().setPrimaryStage(primaryStage);

        Parent root = FXMLLoader.load(getClass().getResource("/fxml/Login.fxml"));
        primaryStage.setTitle("Restaurant Management System - WOK KA TADKA");

        // Get screen dimensions for fullscreen
        Rectangle2D screenBounds = Screen.getPrimary().getVisualBounds();

        // Create scene with screen dimensions
        Scene scene = new Scene(root, screenBounds.getWidth(), screenBounds.getHeight());
        primaryStage.setScene(scene);

        // Set window to fullscreen mode
        primaryStage.setMaximized(true);
        primaryStage.setFullScreen(false); // Use maximized instead of fullscreen for better UX

        // Allow user to exit fullscreen with Escape key
        primaryStage.setFullScreenExitHint("Press ESC to exit fullscreen or click minimize button");

        // Set minimum window size
        primaryStage.setMinWidth(1024);
        primaryStage.setMinHeight(768);

        // Position window at top-left of screen
        primaryStage.setX(screenBounds.getMinX());
        primaryStage.setY(screenBounds.getMinY());

        // Add shutdown hook for proper cleanup
        primaryStage.setOnCloseRequest(event -> {
            System.out.println("Application shutting down...");
            cleanup();
        });

        primaryStage.show();

        // Initialize global ESC key handling after the stage is shown
        Platform.runLater(() -> {
            try {
                com.restaurant.util.UniversalNavigationManager.getInstance().initializeGlobalEscHandling();
                System.out.println("Global ESC key handling initialized");
            } catch (Exception e) {
                System.err.println("Failed to initialize global ESC handling: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    @Override
    public void stop() throws Exception {
        System.out.println("Application stop() called");
        cleanup();
        super.stop();
    }

    /**
     * Cleanup resources when application shuts down
     */
    private void cleanup() {
        try {
            // Close all database connections
            DatabaseManager.closeAllConnections();
            System.out.println("Database connections closed");

            // Add any other cleanup here

        } catch (Exception e) {
            System.err.println("Error during cleanup: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        // Set up global exception handlers to prevent crashes
        Thread.setDefaultUncaughtExceptionHandler((thread, exception) -> {
            System.err.println("CRITICAL: Uncaught exception in thread " + thread.getName() + ": " + exception.getMessage());
            exception.printStackTrace();

            // Try to show error dialog if possible
            try {
                if (Platform.isFxApplicationThread()) {
                    showCriticalErrorDialog(exception);
                } else {
                    Platform.runLater(() -> showCriticalErrorDialog(exception));
                }
            } catch (Exception e) {
                System.err.println("Failed to show error dialog: " + e.getMessage());
            }
        });

        // Add JVM shutdown hook as backup
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("JVM shutdown hook triggered");
            DatabaseManager.closeAllConnections();
        }));

        try {
            launch(args);
        } catch (Exception e) {
            System.err.println("CRITICAL: Failed to launch application: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }

    /**
     * Show critical error dialog to prevent silent crashes
     */
    private static void showCriticalErrorDialog(Throwable exception) {
        try {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Critical Application Error");
            alert.setHeaderText("The application encountered a critical error");
            alert.setContentText("Error: " + exception.getMessage() +
                               "\n\nThe application will attempt to continue running." +
                               "\nIf problems persist, please restart the application." +
                               "\n\nCheck the console for detailed error information.");

            // Make dialog non-blocking
            alert.show();

        } catch (Exception e) {
            System.err.println("Failed to show critical error dialog: " + e.getMessage());
        }
    }
}
