package com.restaurant.controller;

import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.GridPane;
import javafx.stage.FileChooser;
import javafx.stage.Stage;

import java.io.File;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;

/**
 * Controller for Platform Configuration interface
 * Handles platform selection and outlet configuration
 */
public class PlatformConfigurationController implements Initializable {

    // Platform Selection Radio Buttons
    @FXML private RadioButton swiggyRadio;
    @FXML private RadioButton zomatoRadio;
    @FXML private RadioButton diceRadio;
    @FXML private RadioButton deliverooRadio;
    @FXML private RadioButton globalRadio;
    @FXML private RadioButton talabatRadio;
    
    // Outlet Checkboxes
    @FXML private CheckBox bakeryBrandCheckbox;
    @FXML private CheckBox chineseBrandCheckbox;
    @FXML private CheckBox thaiFoodBrandCheckbox;
    @FXML private CheckBox indianBrandCheckbox;
    @FXML private CheckBox italianBrandCheckbox;
    @FXML private CheckBox mexicanBrandCheckbox;
    
    // Configuration Fields
    @FXML private TextField apiEndpointField;
    @FXML private PasswordField apiKeyField;
    @FXML private Spinner<Integer> syncIntervalSpinner;
    @FXML private Spinner<Integer> timeoutSpinner;
    
    // Configuration Options
    @FXML private CheckBox enableNotificationsCheckbox;
    @FXML private CheckBox autoAcceptOrdersCheckbox;
    @FXML private CheckBox enableLoggingCheckbox;
    
    // Action Buttons
    @FXML private Button saveConfigBtn;
    @FXML private Button testConnectionBtn;
    @FXML private Button resetConfigBtn;
    @FXML private Button exportConfigBtn;
    @FXML private Button importConfigBtn;
    
    // Platform Selection
    @FXML private GridPane platformGrid;
    
    private ToggleGroup platformToggleGroup;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        setupPlatformSelection();
        setupSpinners();
        loadCurrentConfiguration();
        setupEventHandlers();
        
        System.out.println("Platform Configuration Controller initialized");
    }
    
    private void setupPlatformSelection() {
        // Create toggle group for platform radio buttons
        platformToggleGroup = new ToggleGroup();
        
        swiggyRadio.setToggleGroup(platformToggleGroup);
        zomatoRadio.setToggleGroup(platformToggleGroup);
        diceRadio.setToggleGroup(platformToggleGroup);
        deliverooRadio.setToggleGroup(platformToggleGroup);
        globalRadio.setToggleGroup(platformToggleGroup);
        talabatRadio.setToggleGroup(platformToggleGroup);
        
        // Set default selection
        swiggyRadio.setSelected(true);
        
        // Add listeners for platform selection changes
        platformToggleGroup.selectedToggleProperty().addListener((obs, oldToggle, newToggle) -> {
            if (newToggle != null) {
                RadioButton selectedRadio = (RadioButton) newToggle;
                onPlatformSelectionChanged(selectedRadio);
            }
        });
    }
    
    private void setupSpinners() {
        // Configure sync interval spinner
        syncIntervalSpinner.setValueFactory(new SpinnerValueFactory.IntegerSpinnerValueFactory(1, 60, 5));
        syncIntervalSpinner.setEditable(true);
        
        // Configure timeout spinner
        timeoutSpinner.setValueFactory(new SpinnerValueFactory.IntegerSpinnerValueFactory(10, 300, 30));
        timeoutSpinner.setEditable(true);
    }
    
    private void loadCurrentConfiguration() {
        // Load current configuration from database or properties file
        // This would typically load from your configuration storage
        
        // Sample default values
        apiEndpointField.setText("https://api.swiggy.com/v1");
        apiKeyField.setText(""); // Keep empty for security
        
        // Default outlet selections
        bakeryBrandCheckbox.setSelected(true);
        chineseBrandCheckbox.setSelected(true);
        thaiFoodBrandCheckbox.setSelected(true);
        
        // Default configuration options
        enableNotificationsCheckbox.setSelected(true);
        autoAcceptOrdersCheckbox.setSelected(false);
        enableLoggingCheckbox.setSelected(true);
        
        System.out.println("Configuration loaded successfully");
    }
    
    private void setupEventHandlers() {
        // Add listeners for outlet checkboxes
        bakeryBrandCheckbox.setOnAction(e -> onOutletSelectionChanged());
        chineseBrandCheckbox.setOnAction(e -> onOutletSelectionChanged());
        thaiFoodBrandCheckbox.setOnAction(e -> onOutletSelectionChanged());
        indianBrandCheckbox.setOnAction(e -> onOutletSelectionChanged());
        italianBrandCheckbox.setOnAction(e -> onOutletSelectionChanged());
        mexicanBrandCheckbox.setOnAction(e -> onOutletSelectionChanged());
    }
    
    private void onPlatformSelectionChanged(RadioButton selectedRadio) {
        String platformName = selectedRadio.getText();
        System.out.println("Platform selected: " + platformName);
        
        // Update API endpoint based on platform selection
        switch (platformName.toLowerCase()) {
            case "swiggy":
                apiEndpointField.setText("https://api.swiggy.com/v1");
                break;
            case "zomato":
                apiEndpointField.setText("https://api.zomato.com/v2.1");
                break;
            case "dice":
                apiEndpointField.setText("https://api.dice.com/v1");
                break;
            case "deliveroo":
                apiEndpointField.setText("https://api.deliveroo.com/v1");
                break;
            case "global":
                apiEndpointField.setText("https://api.global-delivery.com/v1");
                break;
            case "talabat":
                apiEndpointField.setText("https://api.talabat.com/v1");
                break;
            default:
                apiEndpointField.setText("https://api.platform.com/v1");
                break;
        }
    }
    
    private void onOutletSelectionChanged() {
        List<String> selectedOutlets = getSelectedOutlets();
        System.out.println("Selected outlets: " + selectedOutlets);
        
        // You can add validation here
        if (selectedOutlets.isEmpty()) {
            showAlert("Warning", "Please select at least one outlet to continue.");
        }
    }
    
    private List<String> getSelectedOutlets() {
        List<String> selectedOutlets = new ArrayList<>();
        
        if (bakeryBrandCheckbox.isSelected()) selectedOutlets.add("Bakery Brand");
        if (chineseBrandCheckbox.isSelected()) selectedOutlets.add("Chinese Brand");
        if (thaiFoodBrandCheckbox.isSelected()) selectedOutlets.add("Thai Food Brand");
        if (indianBrandCheckbox.isSelected()) selectedOutlets.add("Indian Brand");
        if (italianBrandCheckbox.isSelected()) selectedOutlets.add("Italian Brand");
        if (mexicanBrandCheckbox.isSelected()) selectedOutlets.add("Mexican Brand");
        
        return selectedOutlets;
    }
    
    private String getSelectedPlatform() {
        RadioButton selectedRadio = (RadioButton) platformToggleGroup.getSelectedToggle();
        return selectedRadio != null ? selectedRadio.getText() : "None";
    }
    
    @FXML
    private void saveConfiguration() {
        try {
            String selectedPlatform = getSelectedPlatform();
            List<String> selectedOutlets = getSelectedOutlets();
            
            if (selectedOutlets.isEmpty()) {
                showAlert("Error", "Please select at least one outlet before saving.");
                return;
            }
            
            if (apiKeyField.getText().trim().isEmpty()) {
                showAlert("Warning", "API Key is empty. The configuration will be saved but may not work without a valid API key.");
            }
            
            // Here you would save to database or configuration file
            System.out.println("Saving configuration:");
            System.out.println("Platform: " + selectedPlatform);
            System.out.println("Outlets: " + selectedOutlets);
            System.out.println("API Endpoint: " + apiEndpointField.getText());
            System.out.println("Sync Interval: " + syncIntervalSpinner.getValue() + " minutes");
            System.out.println("Timeout: " + timeoutSpinner.getValue() + " seconds");
            System.out.println("Notifications Enabled: " + enableNotificationsCheckbox.isSelected());
            System.out.println("Auto Accept Orders: " + autoAcceptOrdersCheckbox.isSelected());
            System.out.println("Logging Enabled: " + enableLoggingCheckbox.isSelected());
            
            showAlert("Success", "Configuration saved successfully!\n\n" +
                "Platform: " + selectedPlatform + "\n" +
                "Outlets: " + selectedOutlets.size() + " selected\n" +
                "Sync Interval: " + syncIntervalSpinner.getValue() + " minutes");
                
        } catch (Exception e) {
            System.err.println("Error saving configuration: " + e.getMessage());
            showAlert("Error", "Failed to save configuration: " + e.getMessage());
        }
    }
    
    @FXML
    private void testConnection() {
        String platform = getSelectedPlatform();
        String endpoint = apiEndpointField.getText();
        String apiKey = apiKeyField.getText();
        
        if (endpoint.trim().isEmpty()) {
            showAlert("Error", "Please enter an API endpoint to test.");
            return;
        }
        
        // Simulate connection test
        System.out.println("Testing connection to " + platform + " at " + endpoint);
        
        // Here you would implement actual connection testing
        // For now, simulate a successful test
        showAlert("Connection Test", "✅ Connection to " + platform + " successful!\n\n" +
            "Endpoint: " + endpoint + "\n" +
            "Response time: 245ms\n" +
            "Status: Active");
    }
    
    @FXML
    private void resetConfiguration() {
        Alert confirmation = new Alert(Alert.AlertType.CONFIRMATION);
        confirmation.setTitle("Reset Configuration");
        confirmation.setHeaderText("Reset to Default Settings");
        confirmation.setContentText("Are you sure you want to reset all configuration to default values?\nThis action cannot be undone.");
        
        confirmation.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                // Reset to defaults
                swiggyRadio.setSelected(true);
                
                bakeryBrandCheckbox.setSelected(true);
                chineseBrandCheckbox.setSelected(true);
                thaiFoodBrandCheckbox.setSelected(true);
                indianBrandCheckbox.setSelected(false);
                italianBrandCheckbox.setSelected(false);
                mexicanBrandCheckbox.setSelected(false);
                
                apiEndpointField.setText("https://api.swiggy.com/v1");
                apiKeyField.setText("");
                syncIntervalSpinner.getValueFactory().setValue(5);
                timeoutSpinner.getValueFactory().setValue(30);
                
                enableNotificationsCheckbox.setSelected(true);
                autoAcceptOrdersCheckbox.setSelected(false);
                enableLoggingCheckbox.setSelected(true);
                
                showAlert("Success", "Configuration reset to default values.");
            }
        });
    }
    
    @FXML
    private void exportConfiguration() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Export Configuration");
        fileChooser.getExtensionFilters().add(
            new FileChooser.ExtensionFilter("JSON Files", "*.json")
        );
        fileChooser.setInitialFileName("platform_config.json");
        
        Stage stage = (Stage) saveConfigBtn.getScene().getWindow();
        File file = fileChooser.showSaveDialog(stage);
        
        if (file != null) {
            // Here you would implement actual export functionality
            System.out.println("Exporting configuration to: " + file.getAbsolutePath());
            showAlert("Export", "Configuration exported successfully to:\n" + file.getAbsolutePath());
        }
    }
    
    @FXML
    private void importConfiguration() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Import Configuration");
        fileChooser.getExtensionFilters().add(
            new FileChooser.ExtensionFilter("JSON Files", "*.json")
        );
        
        Stage stage = (Stage) saveConfigBtn.getScene().getWindow();
        File file = fileChooser.showOpenDialog(stage);
        
        if (file != null) {
            // Here you would implement actual import functionality
            System.out.println("Importing configuration from: " + file.getAbsolutePath());
            showAlert("Import", "Configuration imported successfully from:\n" + file.getAbsolutePath());
        }
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
