# Comprehensive CSS Fix Script for JavaFX Compatibility
# This script fixes all rgba() colors and other CSS issues

$cssFile = "src\main\resources\css\application.css"

Write-Host "Starting comprehensive CSS fixes..." -ForegroundColor Green

# Create backup
Copy-Item $cssFile "$cssFile.backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
Write-Host "Backup created" -ForegroundColor Yellow

# Read the CSS content
$content = Get-Content $cssFile -Raw

# Common rgba() to hex conversions
$replacements = @{
    'rgba\(0,\s*0,\s*0,\s*0\.1\)' = '#1a1a1a'
    'rgba\(0,\s*0,\s*0,\s*0\.15\)' = '#262626'
    'rgba\(0,\s*0,\s*0,\s*0\.2\)' = '#333333'
    'rgba\(0,\s*0,\s*0,\s*0\.25\)' = '#404040'
    'rgba\(0,\s*0,\s*0,\s*0\.3\)' = '#4d4d4d'
    'rgba\(0,\s*0,\s*0,\s*0\.4\)' = '#666666'
    'rgba\(0,\s*0,\s*0,\s*0\.5\)' = '#808080'
    'rgba\(0,\s*0,\s*0,\s*0\.6\)' = '#999999'
    'rgba\(0,\s*0,\s*0,\s*0\.7\)' = '#b3b3b3'
    'rgba\(0,\s*0,\s*0,\s*0\.8\)' = '#cccccc'
    'rgba\(0,\s*0,\s*0,\s*0\.9\)' = '#e6e6e6'
    'rgba\(255,\s*255,\s*255,\s*0\.1\)' = '#ffffff1a'
    'rgba\(255,\s*255,\s*255,\s*0\.2\)' = '#ffffff33'
    'rgba\(255,\s*255,\s*255,\s*0\.3\)' = '#ffffff4d'
    'rgba\(255,\s*255,\s*255,\s*0\.4\)' = '#ffffff66'
    'rgba\(255,\s*255,\s*255,\s*0\.5\)' = '#ffffff80'
    'rgba\(255,\s*255,\s*255,\s*0\.6\)' = '#ffffff99'
    'rgba\(255,\s*255,\s*255,\s*0\.7\)' = '#ffffffb3'
    'rgba\(255,\s*255,\s*255,\s*0\.8\)' = '#ffffffcc'
    'rgba\(255,\s*255,\s*255,\s*0\.9\)' = '#ffffffe6'
    'rgba\(255,\s*107,\s*53,\s*0\.2\)' = '#ff6b35'
    'rgba\(255,\s*107,\s*53,\s*0\.3\)' = '#ff6b35'
    'rgba\(255,\s*107,\s*53,\s*0\.4\)' = '#ff6b35'
    'rgba\(255,\s*107,\s*53,\s*0\.5\)' = '#ff6b35'
    'rgba\(0,\s*123,\s*255,\s*0\.2\)' = '#007bff'
    'rgba\(0,\s*123,\s*255,\s*0\.25\)' = '#007bff'
    'rgba\(0,\s*123,\s*255,\s*0\.3\)' = '#007bff'
    'rgba\(0,\s*123,\s*255,\s*0\.4\)' = '#007bff'
    'rgba\(0,\s*123,\s*255,\s*0\.5\)' = '#007bff'
    'rgba\(40,\s*167,\s*69,\s*0\.3\)' = '#28a745'
    'rgba\(40,\s*167,\s*69,\s*0\.4\)' = '#28a745'
    'rgba\(40,\s*167,\s*69,\s*0\.5\)' = '#28a745'
    'rgba\(220,\s*53,\s*69,\s*0\.3\)' = '#dc3545'
    'rgba\(220,\s*53,\s*69,\s*0\.5\)' = '#dc3545'
    'rgba\(220,\s*53,\s*69,\s*0\.8\)' = '#dc3545'
    'rgba\(102,\s*126,\s*234,\s*0\.1\)' = '#667eea'
    'rgba\(102,\s*126,\s*234,\s*0\.15\)' = '#667eea'
    'rgba\(102,\s*126,\s*234,\s*0\.2\)' = '#667eea'
    'rgba\(102,\s*126,\s*234,\s*0\.25\)' = '#667eea'
    'rgba\(102,\s*126,\s*234,\s*0\.3\)' = '#667eea'
    'rgba\(102,\s*126,\s*234,\s*0\.4\)' = '#667eea'
    'rgba\(102,\s*126,\s*234,\s*0\.5\)' = '#667eea'
    'rgba\(102,\s*126,\s*234,\s*0\.6\)' = '#667eea'
    'rgba\(102,\s*126,\s*234,\s*0\.8\)' = '#667eea'
    'rgba\(229,\s*231,\s*235,\s*0\.5\)' = '#e5e7eb'
    'rgba\(229,\s*231,\s*235,\s*0\.8\)' = '#e5e7eb'
    'rgba\(229,\s*231,\s*235,\s*1\)' = '#e5e7eb'
    'rgba\(203,\s*213,\s*225,\s*0\.3\)' = '#cbd5e1'
    'rgba\(203,\s*213,\s*225,\s*0\.4\)' = '#cbd5e1'
    'rgba\(203,\s*213,\s*225,\s*0\.5\)' = '#cbd5e1'
    'rgba\(203,\s*213,\s*225,\s*0\.8\)' = '#cbd5e1'
    'rgba\(248,\s*113,\s*113,\s*0\.1\)' = '#f87171'
    'rgba\(248,\s*113,\s*113,\s*0\.2\)' = '#f87171'
    'rgba\(248,\s*113,\s*113,\s*0\.3\)' = '#f87171'
    'rgba\(248,\s*113,\s*113,\s*0\.4\)' = '#f87171'
    'rgba\(100,\s*116,\s*139,\s*0\.1\)' = '#64748b'
    'rgba\(100,\s*116,\s*139,\s*0\.2\)' = '#64748b'
    'rgba\(100,\s*116,\s*139,\s*0\.4\)' = '#64748b'
    'rgba\(148,\s*163,\s*184,\s*0\.1\)' = '#94a3b8'
    'rgba\(148,\s*163,\s*184,\s*0\.2\)' = '#94a3b8'
    'rgba\(148,\s*163,\s*184,\s*0\.4\)' = '#94a3b8'
    'rgba\(59,\s*130,\s*246,\s*0\.1\)' = '#3b82f6'
    'rgba\(16,\s*185,\s*129,\s*0\.4\)' = '#10b981'
    'rgba\(16,\s*185,\s*129,\s*0\.6\)' = '#10b981'
    'rgba\(245,\s*158,\s*11,\s*0\.4\)' = '#f59e0b'
    'rgba\(243,\s*156,\s*18,\s*0\.4\)' = '#f39c12'
    'rgba\(243,\s*156,\s*18,\s*0\.8\)' = '#f39c12'
    'rgba\(231,\s*76,\s*60,\s*0\.4\)' = '#e74c3c'
    'rgba\(231,\s*76,\s*60,\s*0\.8\)' = '#e74c3c'
}

# Apply all replacements
foreach ($pattern in $replacements.Keys) {
    $replacement = $replacements[$pattern]
    $content = $content -replace $pattern, $replacement
    Write-Host "Replaced $pattern with $replacement" -ForegroundColor Cyan
}

# Fix other CSS issues
$content = $content -replace '-fx-radius-lg', '12px'
$content = $content -replace '-fx-primary-500', '#007bff'

# Write the fixed content back
Set-Content -Path $cssFile -Value $content -Encoding UTF8

Write-Host "CSS fixes completed successfully!" -ForegroundColor Green
Write-Host "Starting application..." -ForegroundColor Yellow

# Run the application
mvn javafx:run
