package com.restaurant.util;

import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.Parent;
import javafx.stage.Stage;

/**
 * Launcher for Integrated Platform Manager interface
 * This combines platform selection with category item management
 */
public class IntegratedPlatformManagerLauncher extends Application {

    @Override
    public void start(Stage primaryStage) {
        try {
            // Load the FXML file
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/IntegratedPlatformManager.fxml"));
            Parent root = loader.load();
            
            // Create scene
            Scene scene = new Scene(root, 1100, 800);
            
            // Set up stage
            primaryStage.setTitle("Integrated Platform Manager - Restaurant Management");
            primaryStage.setScene(scene);
            primaryStage.setMinWidth(1000);
            primaryStage.setMinHeight(700);
            primaryStage.show();
            
            System.out.println("Integrated Platform Manager interface launched successfully");
            
        } catch (Exception e) {
            System.err.println("Error launching Integrated Platform Manager: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        launch(args);
    }
}
