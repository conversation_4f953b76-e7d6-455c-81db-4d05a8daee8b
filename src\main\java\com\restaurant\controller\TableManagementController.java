package com.restaurant.controller;

import com.restaurant.model.TableStatus;
import com.restaurant.model.OrderDAO;
import com.restaurant.model.MenuItem;
import com.restaurant.model.MenuDAO;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Parent;
import javafx.scene.Node;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonType;
import javafx.scene.control.Label;
import javafx.scene.control.ScrollPane;
import javafx.scene.control.TextField;
import javafx.event.ActionEvent;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import java.net.URL;
import java.time.LocalDateTime;
import java.util.ArrayList;
import com.restaurant.util.UniversalNavigationManager;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.stream.Collectors;
import com.restaurant.util.MenuShortcuts;

public class TableManagementController implements Initializable {

    @FXML private GridPane tablesGrid;
    @FXML private VBox table1, table2, table3, table4, table5, table6, table7, table8, table9;
    @FXML private TextField searchField;
    @FXML private VBox searchResultsArea;
    @FXML private VBox searchResultsContainer;
    @FXML private Button clearSearchButton;
    @FXML private ScrollPane searchResultsScrollPane;

    // Autocomplete components
    @FXML private VBox autocompleteDropdown;
    @FXML private VBox autocompleteContainer;
    @FXML private ScrollPane autocompleteScrollPane;

    private String selectedTableId = null;
    private Map<Integer, TableStatus> tableStatuses = new HashMap<>();
    private List<VBox> tableCards = new ArrayList<>();
    private List<VBox> allTableCards = new ArrayList<>(); // Store all tables for filtering

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("TableManagementController.initialize() called");

        // Initialize table cards list
        initializeTableCards();

        // Setup search functionality
        setupSearchFunctionality();

        // Load table data asynchronously
        loadTableDataAsync();

        // Setup refresh timer (every 30 seconds)
        setupAutoRefresh();

        System.out.println("TableManagementController initialization complete");
    }

    private void initializeTableCards() {
        tableCards.clear();

        // Add table cards and ensure they're visible
        if (table1 != null) {
            table1.setVisible(true);
            table1.setManaged(true);
            tableCards.add(table1);
        }
        if (table2 != null) {
            table2.setVisible(true);
            table2.setManaged(true);
            tableCards.add(table2);
        }
        if (table3 != null) {
            table3.setVisible(true);
            table3.setManaged(true);
            tableCards.add(table3);
        }
        if (table4 != null) {
            table4.setVisible(true);
            table4.setManaged(true);
            tableCards.add(table4);
        }
        if (table5 != null) {
            table5.setVisible(true);
            table5.setManaged(true);
            tableCards.add(table5);
        }
        if (table6 != null) {
            table6.setVisible(true);
            table6.setManaged(true);
            tableCards.add(table6);
        }
        if (table7 != null) {
            table7.setVisible(true);
            table7.setManaged(true);
            tableCards.add(table7);
        }
        if (table8 != null) {
            table8.setVisible(true);
            table8.setManaged(true);
            tableCards.add(table8);
        }
        if (table9 != null) {
            table9.setVisible(true);
            table9.setManaged(true);
            tableCards.add(table9);
        }

        System.out.println("Initialized " + tableCards.size() + " table cards");

        // Store all tables for search filtering
        allTableCards.clear();
        allTableCards.addAll(tableCards);

        // Debug: Check if GridPane has children
        if (tablesGrid != null) {
            System.out.println("GridPane children count: " + tablesGrid.getChildren().size());
            tablesGrid.setVisible(true);
            tablesGrid.setManaged(true);
        }
    }

    private void setupSearchFunctionality() {
        System.out.println("Setting up autocomplete search functionality...");

        if (searchField != null) {
            // Set up search field prompt text
            searchField.setPromptText("Type to search dishes (e.g., chicken, biryani, burger)...");

            // Add real-time listener for autocomplete as user types
            searchField.textProperty().addListener((observable, oldValue, newValue) -> {
                System.out.println("Search text changed: '" + oldValue + "' -> '" + newValue + "'");
                // Show autocomplete suggestions as user types
                showAutocompleteSuggestions(newValue);
            });

            // Handle Enter key to select first suggestion
            searchField.setOnAction(e -> {
                System.out.println("Enter key pressed in search field");
                selectFirstSuggestion();
            });

            // Handle focus lost to hide suggestions
            searchField.focusedProperty().addListener((observable, oldValue, newValue) -> {
                if (!newValue) {
                    // Hide autocomplete when field loses focus (with small delay)
                    Platform.runLater(() -> {
                        try {
                            Thread.sleep(100); // Small delay to allow click on suggestion
                        } catch (InterruptedException ex) {}
                        hideAutocomplete();
                    });
                }
            });

            System.out.println("Real-time autocomplete functionality setup complete");
        } else {
            System.err.println("ERROR: searchField is null!");
        }

        // Initially hide autocomplete dropdown
        if (autocompleteDropdown != null) {
            autocompleteDropdown.setVisible(false);
            autocompleteDropdown.setManaged(false);
            System.out.println("Autocomplete dropdown initialized and hidden");
        } else {
            System.err.println("ERROR: autocompleteDropdown is null!");
        }

        // Initially hide search results area
        if (searchResultsArea != null) {
            searchResultsArea.setVisible(false);
            searchResultsArea.setManaged(false);
            System.out.println("Search results area initialized and hidden");
        } else {
            System.err.println("ERROR: searchResultsArea is null!");
        }
    }

    private void autoFilterDishes(String searchText) {
        System.out.println("autoFilterDishes called with: '" + searchText + "'");

        // Real-time filtering as user types
        if (searchText == null || searchText.trim().isEmpty()) {
            System.out.println("Search text is empty, clearing search");
            // Hide search results when search is empty
            clearSearch();
            return;
        }

        // Only start filtering after user types at least 2 characters
        if (searchText.trim().length() < 2) {
            System.out.println("Search text too short (" + searchText.trim().length() + " chars), clearing search");
            clearSearch();
            return;
        }

        System.out.println("Auto-filtering dishes for: '" + searchText + "'");

        // Always use sample data for now to ensure it works
        displaySampleSearchResults(searchText);

        // Commented out database search for debugging
        /*
        // Search for menu items using MenuDAO
        try {
            List<MenuItem> foundDishes = MenuDAO.searchMenuItems(searchText.trim());
            displaySearchResults(foundDishes, searchText);
        } catch (Exception e) {
            System.err.println("Error auto-filtering dishes: " + e.getMessage());
            // Fallback to sample data
            displaySampleSearchResults(searchText);
        }
        */
    }

    @FXML
    private void searchDishes() {
        // This method is called by the search button or Enter key
        String searchText = searchField.getText();
        autoFilterDishes(searchText);
    }

    @FXML
    private void clearSearch() {
        System.out.println("clearSearch() called");

        // Clear search field
        if (searchField != null) {
            searchField.clear();
            System.out.println("Search field cleared");
        } else {
            System.err.println("ERROR: searchField is null in clearSearch()");
        }

        // Hide search results
        if (searchResultsArea != null) {
            searchResultsArea.setVisible(false);
            searchResultsArea.setManaged(false);
            System.out.println("Search results area hidden");
        } else {
            System.err.println("ERROR: searchResultsArea is null in clearSearch()");
        }

        // Clear results container
        if (searchResultsContainer != null) {
            searchResultsContainer.getChildren().clear();
            System.out.println("Search results container cleared");
        } else {
            System.err.println("ERROR: searchResultsContainer is null in clearSearch()");
        }

        System.out.println("Search cleared successfully");
    }

    private void showAutocompleteSuggestions(String searchText) {
        System.out.println("showAutocompleteSuggestions called with: '" + searchText + "'");

        if (searchText == null || searchText.trim().isEmpty()) {
            hideAutocomplete();
            return;
        }

        // Only show suggestions after user types at least 1 character
        if (searchText.trim().length() < 1) {
            hideAutocomplete();
            return;
        }

        // Get all available dishes
        List<MenuItem> allDishes = getAllAvailableDishes();

        // Filter dishes that match the search text
        String lowerSearch = searchText.toLowerCase();
        List<MenuItem> matchingDishes = allDishes.stream()
            .filter(dish -> dish.getName().toLowerCase().contains(lowerSearch))
            .limit(8) // Limit to 8 suggestions for better UX
            .collect(Collectors.toList());

        System.out.println("Found " + matchingDishes.size() + " matching dishes");

        if (matchingDishes.isEmpty()) {
            hideAutocomplete();
            return;
        }

        // Clear previous suggestions
        if (autocompleteContainer != null) {
            autocompleteContainer.getChildren().clear();

            // Add matching dishes as clickable suggestions
            for (MenuItem dish : matchingDishes) {
                HBox suggestionItem = createSuggestionItem(dish, searchText);
                autocompleteContainer.getChildren().add(suggestionItem);
            }

            // Show autocomplete dropdown
            if (autocompleteDropdown != null) {
                autocompleteDropdown.setVisible(true);
                autocompleteDropdown.setManaged(true);
                System.out.println("Autocomplete dropdown shown with " + matchingDishes.size() + " suggestions");
            }
        }
    }

    private void hideAutocomplete() {
        System.out.println("hideAutocomplete called");

        if (autocompleteDropdown != null) {
            autocompleteDropdown.setVisible(false);
            autocompleteDropdown.setManaged(false);
            System.out.println("Autocomplete dropdown hidden");
        }

        if (autocompleteContainer != null) {
            autocompleteContainer.getChildren().clear();
            System.out.println("Autocomplete container cleared");
        }
    }

    private void selectFirstSuggestion() {
        System.out.println("selectFirstSuggestion called");

        if (autocompleteContainer != null && !autocompleteContainer.getChildren().isEmpty()) {
            // Get first suggestion and simulate click
            Node firstSuggestion = autocompleteContainer.getChildren().get(0);
            if (firstSuggestion instanceof HBox) {
                HBox suggestionBox = (HBox) firstSuggestion;
                // Trigger the click action
                if (suggestionBox.getUserData() instanceof MenuItem) {
                    MenuItem dish = (MenuItem) suggestionBox.getUserData();
                    selectDish(dish);
                }
            }
        }
    }

    private HBox createSuggestionItem(MenuItem dish, String searchText) {
        HBox suggestionBox = new HBox(10);
        suggestionBox.setAlignment(javafx.geometry.Pos.CENTER_LEFT);
        suggestionBox.setStyle("-fx-background-color: white; -fx-padding: 8px 12px; -fx-border-color: #eee; " +
                              "-fx-border-width: 0 0 1 0; -fx-cursor: hand;");

        // Store dish data for selection
        suggestionBox.setUserData(dish);

        // Dish name with highlighted search text
        Label nameLabel = new Label(highlightSearchText(dish.getName(), searchText));
        nameLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #333;");

        // Price label
        Label priceLabel = new Label("₹" + String.format("%.0f", dish.getPrice()));
        priceLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #666; -fx-font-weight: bold;");

        // Category label
        Label categoryLabel = new Label(dish.getCategory());
        categoryLabel.setStyle("-fx-font-size: 10px; -fx-text-fill: #888; -fx-background-color: #f0f0f0; " +
                              "-fx-padding: 2px 6px; -fx-background-radius: 8px;");

        // Add spacer
        Region spacer = new Region();
        HBox.setHgrow(spacer, javafx.scene.layout.Priority.ALWAYS);

        suggestionBox.getChildren().addAll(nameLabel, spacer, priceLabel, categoryLabel);

        // Add hover effect
        suggestionBox.setOnMouseEntered(e ->
            suggestionBox.setStyle("-fx-background-color: #f8f9fa; -fx-padding: 8px 12px; -fx-border-color: #eee; " +
                                  "-fx-border-width: 0 0 1 0; -fx-cursor: hand;"));

        suggestionBox.setOnMouseExited(e ->
            suggestionBox.setStyle("-fx-background-color: white; -fx-padding: 8px 12px; -fx-border-color: #eee; " +
                                  "-fx-border-width: 0 0 1 0; -fx-cursor: hand;"));

        // Add click handler
        suggestionBox.setOnMouseClicked(e -> {
            System.out.println("Suggestion clicked: " + dish.getName());
            selectDish(dish);
        });

        return suggestionBox;
    }

    private String highlightSearchText(String dishName, String searchText) {
        // Simple highlighting - in a real app you'd use rich text
        return dishName; // For now, just return the dish name as-is
    }

    private void selectDish(MenuItem dish) {
        System.out.println("Dish selected: " + dish.getName());

        // Set the dish name in search field
        if (searchField != null) {
            searchField.setText(dish.getName());
        }

        // Hide autocomplete
        hideAutocomplete();

        // Show dish details or add to order
        showDishDetails(dish);
    }

    private void showDishDetails(MenuItem dish) {
        // Show selected dish details
        showAlert("Dish Selected",
                 "🍽️ " + dish.getName() + "\n" +
                 "💰 ₹" + String.format("%.0f", dish.getPrice()) + "\n" +
                 "📂 " + dish.getCategory() + "\n\n" +
                 "Would you like to add this to an order?");
    }

    private List<MenuItem> getAllAvailableDishes() {
        // Create comprehensive sample menu for autocomplete
        List<MenuItem> allDishes = new ArrayList<>();

        allDishes.addAll(List.of(
            // Tandoori Roti - 13 items
            new MenuItem(1, "Roti", 20.0, "Tandoori Roti", 1),
            new MenuItem(2, "Butter Roti", 25.0, "Tandoori Roti", 2),
            new MenuItem(3, "Naan", 35.0, "Tandoori Roti", 3),
            new MenuItem(4, "Butter Naan", 40.0, "Tandoori Roti", 4),
            new MenuItem(5, "Garlic Naan", 70.0, "Tandoori Roti", 5),
            new MenuItem(6, "Khulcha", 40.0, "Tandoori Roti", 6),
            new MenuItem(7, "Butter Khulcha", 45.0, "Tandoori Roti", 7),
            new MenuItem(8, "Lacha Paratha", 45.0, "Tandoori Roti", 8),
            new MenuItem(9, "Butter Paratha", 45.0, "Tandoori Roti", 9),
            new MenuItem(10, "Aloo Paratha", 70.0, "Tandoori Roti", 10),
            new MenuItem(11, "Chi. Kheema Paratha", 120.0, "Tandoori Roti", 11),
            new MenuItem(12, "Paneer Paratha", 120.0, "Tandoori Roti", 12),
            new MenuItem(13, "Veg Kheema Paratha", 120.0, "Tandoori Roti", 13),

            // Biryani - 14 items
            new MenuItem(14, "Steam Basmati Rice (H/F)", 60.0, "Biryani", 14),
            new MenuItem(15, "Jeera Rice (H/F)", 70.0, "Biryani", 15),
            new MenuItem(16, "Veg Pulao", 170.0, "Biryani", 16),
            new MenuItem(17, "Veg Biryani", 180.0, "Biryani", 17),
            new MenuItem(18, "Egg Biryani", 190.0, "Biryani", 18),
            new MenuItem(19, "Paneer Pulav", 180.0, "Biryani", 19),
            new MenuItem(20, "Paneer Biryani", 190.0, "Biryani", 20),
            new MenuItem(21, "Chicken Biryani", 220.0, "Biryani", 21),
            new MenuItem(22, "Chi. Dum Biryani", 230.0, "Biryani", 22),
            new MenuItem(23, "Chicken Hyderabadi Biryani", 230.0, "Biryani", 23),
            new MenuItem(24, "Chicken Tikka Biryani", 250.0, "Biryani", 24),
            new MenuItem(25, "Mutton Biryani", 280.0, "Biryani", 25),
            new MenuItem(26, "Mutton Dum Biryani", 300.0, "Biryani", 26),
            new MenuItem(27, "Mutton Hyderabadi Biryani", 300.0, "Biryani", 27),

            // Tandoori Veg - 6 items
            new MenuItem(28, "Paneer Tikka", 200.0, "Tandoori", 28),
            new MenuItem(29, "Paneer Malai Tikka", 220.0, "Tandoori", 29),
            new MenuItem(30, "Veg Seek Kabab", 190.0, "Tandoori", 30),
            new MenuItem(31, "Mushroom Tikka", 200.0, "Tandoori", 31),
            new MenuItem(32, "Baby Corn Tikka", 190.0, "Tandoori", 32),
            new MenuItem(33, "Chilly Milly Kabab", 200.0, "Tandoori", 33),

            // Tandoori Chicken - 15 items
            new MenuItem(34, "Chicken Tikka", 210.0, "Tandoori", 34),
            new MenuItem(35, "Chicken Tandoori Half", 210.0, "Tandoori", 35),
            new MenuItem(36, "Chicken Tandoori Full", 400.0, "Tandoori", 36),
            new MenuItem(37, "Chicken Pahadi Tandoori Half", 220.0, "Tandoori", 37),
            new MenuItem(38, "Chicken Pahadi Tandoori Full", 410.0, "Tandoori", 38),
            new MenuItem(39, "Chicken Lemon Tandoori Half", 240.0, "Tandoori", 39),
            new MenuItem(40, "Chicken Lemon Tandoori Full", 420.0, "Tandoori", 40),
            new MenuItem(41, "Chicken Kalimiri Kabab", 260.0, "Tandoori", 41),
            new MenuItem(42, "Chicken Banjara Kabab", 260.0, "Tandoori", 42),
            new MenuItem(43, "Chicken Sholay Kabab", 260.0, "Tandoori", 43),
            new MenuItem(44, "Chicken Sikkh Kabab", 280.0, "Tandoori", 44),
            new MenuItem(45, "Chicken Tangri Kabab", 200.0, "Tandoori", 45),
            new MenuItem(46, "Chicken Rajwadi Kabab", 300.0, "Tandoori", 46),
            new MenuItem(80, "Chicken Malai Tikka", 240.0, "Tandoori", 80),
            new MenuItem(81, "Chicken Seekh Kabab", 280.0, "Tandoori", 81),

            // Papad & Salad - 8 items
            new MenuItem(47, "Roasted Papad", 20.0, "Papad & Salad", 47),
            new MenuItem(48, "Fry Papad", 25.0, "Papad & Salad", 48),
            new MenuItem(49, "Masala Papad", 50.0, "Papad & Salad", 49),
            new MenuItem(50, "Green Salad", 90.0, "Papad & Salad", 50),
            new MenuItem(51, "Raita", 100.0, "Papad & Salad", 51),
            new MenuItem(52, "Boondi Raita", 130.0, "Papad & Salad", 52),
            new MenuItem(53, "Schzewan Sauce Extra", 20.0, "Papad & Salad", 53),
            new MenuItem(54, "Fry Noodles Extra", 20.0, "Papad & Salad", 54),

            // Mutton Gravy - 8 items
            new MenuItem(55, "Mutton Masala", 330.0, "Mutton Gravy", 55),
            new MenuItem(56, "Mutton Kadhai", 330.0, "Mutton Gravy", 56),
            new MenuItem(57, "Mutton Kolhapuri", 330.0, "Mutton Gravy", 57),
            new MenuItem(58, "Mutton Hyderabadi", 330.0, "Mutton Gravy", 58),
            new MenuItem(59, "Mutton Handi (Half) 6pcs", 470.0, "Mutton Gravy", 59),
            new MenuItem(60, "Mutton Handi (Full) 12pcs", 750.0, "Mutton Gravy", 60),
            new MenuItem(61, "Mutton Do Pyaza", 330.0, "Mutton Gravy", 61),
            new MenuItem(62, "Mutton Shukar", 350.0, "Mutton Gravy", 62),

            // Sea Food - 11 items
            new MenuItem(63, "Bangda Fry", 130.0, "Sea Food", 63),
            new MenuItem(64, "Bangda Masala", 180.0, "Sea Food", 64),
            new MenuItem(65, "Mandeli Oil Fry", 150.0, "Sea Food", 65),
            new MenuItem(66, "Surmai Tawa Fry", 195.0, "Sea Food", 66),
            new MenuItem(67, "Surmai Koliwada", 195.0, "Sea Food", 67),
            new MenuItem(68, "Prawns Tawa Fry", 255.0, "Sea Food", 68),
            new MenuItem(69, "Prawns Koliwada", 250.0, "Sea Food", 69),
            new MenuItem(70, "Surmai Gavan Curry", 195.0, "Sea Food", 70),
            new MenuItem(71, "Surmai Masala", 195.0, "Sea Food", 71),
            new MenuItem(82, "Fish Curry", 220.0, "Sea Food", 82),
            new MenuItem(83, "Prawn Curry", 280.0, "Sea Food", 83),

            // Bulk Order - 8 items
            new MenuItem(72, "Paneer Pulav", 800.0, "Bulk Order", 72),
            new MenuItem(73, "Veg Biryani", 800.0, "Bulk Order", 73),
            new MenuItem(74, "Chicken Biryani", 1000.0, "Bulk Order", 74),
            new MenuItem(75, "Mutton Biryani", 1300.0, "Bulk Order", 75),
            new MenuItem(76, "Veg Pulav", 700.0, "Bulk Order", 76),
            new MenuItem(77, "Chicken Masala", 900.0, "Bulk Order", 77),
            new MenuItem(78, "Jira Rice", 650.0, "Bulk Order", 78),
            new MenuItem(79, "Steam Rice", 650.0, "Bulk Order", 79),

            // Soup (Veg) - 5 items
            new MenuItem(80, "Manchow Soup", 110.0, "Soup (Veg)", 80),
            new MenuItem(81, "Schezwan Soup", 110.0, "Soup (Veg)", 81),
            new MenuItem(82, "Noodles Soup", 110.0, "Soup (Veg)", 82),
            new MenuItem(83, "Clear Soup", 110.0, "Soup (Veg)", 83),
            new MenuItem(84, "Hot N Sour Soup", 110.0, "Soup (Veg)", 84),

            // Soup (Non-Veg) - 6 items
            new MenuItem(85, "Chicken Manchow Soup", 120.0, "Soup (Non-Veg)", 85),
            new MenuItem(86, "Chicken Hot N Sour Soup", 120.0, "Soup (Non-Veg)", 86),
            new MenuItem(87, "Chicken Lung Fung Soup", 120.0, "Soup (Non-Veg)", 87),
            new MenuItem(88, "Chicken Schezwan Soup", 120.0, "Soup (Non-Veg)", 88),
            new MenuItem(89, "Chicken Noodles Soup", 120.0, "Soup (Non-Veg)", 89),
            new MenuItem(90, "Chicken Clear Soup", 120.0, "Soup (Non-Veg)", 90),

            // Popular Noodles (Veg) - 5 items
            new MenuItem(91, "Veg Hakka Noodles", 160.0, "Noodles (Veg)", 91),
            new MenuItem(92, "Veg Schezwan Noodles", 170.0, "Noodles (Veg)", 92),
            new MenuItem(93, "Veg Singapore Noodles", 190.0, "Noodles (Veg)", 93),
            new MenuItem(94, "Veg Mushroom Noodles", 180.0, "Noodles (Veg)", 94),
            new MenuItem(95, "Veg Chilly Garlic Noodles", 220.0, "Noodles (Veg)", 95),

            // Popular Noodles (Non-Veg) - 5 items
            new MenuItem(96, "Chicken Hakka Noodles", 180.0, "Noodles (Non-Veg)", 96),
            new MenuItem(97, "Chi. Schezwan Noodles", 190.0, "Noodles (Non-Veg)", 97),
            new MenuItem(98, "Chi. Singapore Noodles", 200.0, "Noodles (Non-Veg)", 98),
            new MenuItem(99, "Chi. Mushroom Noodles", 200.0, "Noodles (Non-Veg)", 99),
            new MenuItem(100, "Chicken Chilly Garlic Noodles", 250.0, "Noodles (Non-Veg)", 100),

            // Popular Rice (Veg) - 5 items
            new MenuItem(101, "Veg Fry Rice", 170.0, "Rice (Veg)", 101),
            new MenuItem(102, "Veg Schezwan Rice", 180.0, "Rice (Veg)", 102),
            new MenuItem(103, "Veg Singapore Rice", 190.0, "Rice (Veg)", 103),
            new MenuItem(104, "Paneer Fry Rice", 200.0, "Rice (Veg)", 104),
            new MenuItem(105, "Veg Sherpa Rice", 230.0, "Rice (Veg)", 105),

            // Popular Rice (Non-Veg) - 5 items
            new MenuItem(106, "Chi. Fry Rice", 180.0, "Rice (Non-Veg)", 106),
            new MenuItem(107, "Chi. Schezwan Rice", 190.0, "Rice (Non-Veg)", 107),
            new MenuItem(108, "Chi. Singapore Rice", 200.0, "Rice (Non-Veg)", 108),
            new MenuItem(109, "Chi. Hong Kong Rice", 200.0, "Rice (Non-Veg)", 109),
            new MenuItem(110, "Chi. Thousand Rice", 300.0, "Rice (Non-Veg)", 110),

            // Chinese Gravy - 5 items
            new MenuItem(111, "Manchurian Gravy", 180.0, "Chinese Gravy", 111),
            new MenuItem(112, "Schezwan Gravy", 180.0, "Chinese Gravy", 112),
            new MenuItem(113, "Chilly Gravy", 180.0, "Chinese Gravy", 113),
            new MenuItem(114, "Hot Garlic Gravy", 190.0, "Chinese Gravy", 114),
            new MenuItem(115, "Paneer Chilly Gravy", 190.0, "Chinese Gravy", 115),

            // Indian & Punjabi (Veg) - 8 items
            new MenuItem(116, "Dal Fry", 130.0, "Indian & Punjabi (Veg)", 116),
            new MenuItem(117, "Dal Tadka", 150.0, "Indian & Punjabi (Veg)", 117),
            new MenuItem(118, "Paneer Masala", 200.0, "Indian & Punjabi (Veg)", 118),
            new MenuItem(119, "Paneer Butter Masala", 220.0, "Indian & Punjabi (Veg)", 119),
            new MenuItem(120, "Paneer Kadhai", 200.0, "Indian & Punjabi (Veg)", 120),
            new MenuItem(121, "Palak Paneer", 200.0, "Indian & Punjabi (Veg)", 121),
            new MenuItem(122, "Mix Veg", 180.0, "Indian & Punjabi (Veg)", 122),
            new MenuItem(123, "Aloo Mutter", 170.0, "Indian & Punjabi (Veg)", 123),

            // Chicken Gravy - 8 items
            new MenuItem(124, "Chicken Masala", 210.0, "Chicken Gravy", 124),
            new MenuItem(125, "Chicken Curry", 210.0, "Chicken Gravy", 125),
            new MenuItem(126, "Chicken Kadhai", 240.0, "Chicken Gravy", 126),
            new MenuItem(127, "Chicken Tawa Masala", 260.0, "Chicken Gravy", 127),
            new MenuItem(128, "Butter Chicken (H/F)", 290.0, "Chicken Gravy", 128),
            new MenuItem(129, "Chicken Tikka Masala", 260.0, "Chicken Gravy", 129),
            new MenuItem(130, "Chicken Maratha", 280.0, "Chicken Gravy", 130),
            new MenuItem(131, "Chicken Mughlai", 260.0, "Chicken Gravy", 131),

            // Starters (Veg) - 5 items
            new MenuItem(132, "Veg Manchurian", 190.0, "Starters (Veg)", 132),
            new MenuItem(133, "Paneer Chilly", 220.0, "Starters (Veg)", 133),
            new MenuItem(134, "Mushroom Chilly", 200.0, "Starters (Veg)", 134),
            new MenuItem(135, "Crispy Chilly Potato", 220.0, "Starters (Veg)", 135),
            new MenuItem(136, "Honey Chilly Potato", 220.0, "Starters (Veg)", 136),

            // Starters (Non-Veg) - 5 items
            new MenuItem(137, "Chi. Chilly", 220.0, "Starters (Non-Veg)", 137),
            new MenuItem(138, "Chi. Manchurian", 220.0, "Starters (Non-Veg)", 138),
            new MenuItem(139, "Chi. Lollypop 8 Pc.", 230.0, "Starters (Non-Veg)", 139),
            new MenuItem(140, "Chi. Honey Chilly", 270.0, "Starters (Non-Veg)", 140),
            new MenuItem(141, "Chi. Crispy", 250.0, "Starters (Non-Veg)", 141),

            // Egg Dishes - 4 items
            new MenuItem(142, "Egg Bhurji", 110.0, "Egg Dishes", 142),
            new MenuItem(143, "Egg Masala", 170.0, "Egg Dishes", 143),
            new MenuItem(144, "Egg Curry", 170.0, "Egg Dishes", 144),
            new MenuItem(145, "Anda Ghotala", 180.0, "Egg Dishes", 145)
        ));

        return allDishes;
    }

    private void displaySearchResults(List<MenuItem> dishes, String searchText) {
        if (searchResultsContainer == null || searchResultsArea == null) {
            System.err.println("Search results components not initialized");
            return;
        }

        // Clear previous results
        searchResultsContainer.getChildren().clear();

        if (dishes.isEmpty()) {
            // Show no results message
            Label noResultsLabel = new Label("No dishes found for: \"" + searchText + "\"");
            noResultsLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #666; -fx-padding: 20px;");
            searchResultsContainer.getChildren().add(noResultsLabel);
        } else {
            // Show found dishes
            Label resultsHeader = new Label("Found " + dishes.size() + " dish(es) for: \"" + searchText + "\"");
            resultsHeader.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #333; -fx-padding: 10px 0;");
            searchResultsContainer.getChildren().add(resultsHeader);

            // Create cards for each dish
            for (MenuItem dish : dishes) {
                VBox dishCard = createDishCard(dish);
                searchResultsContainer.getChildren().add(dishCard);
            }
        }

        // Show search results area
        searchResultsArea.setVisible(true);
        searchResultsArea.setManaged(true);

        System.out.println("Displayed " + dishes.size() + " search results");
    }

    private void displaySampleSearchResults(String searchText) {
        // Fallback sample data when database is not available - Complete menu with 233 items
        List<MenuItem> allSampleDishes = new ArrayList<>();

        // Tandoori Roti - 13 items
        allSampleDishes.addAll(List.of(
            new MenuItem(1, "Roti", 20.0, "Tandoori Roti", 1),
            new MenuItem(2, "Butter Roti", 25.0, "Tandoori Roti", 2),
            new MenuItem(3, "Naan", 35.0, "Tandoori Roti", 3),
            new MenuItem(4, "Butter Naan", 40.0, "Tandoori Roti", 4),
            new MenuItem(5, "Garlic Naan", 70.0, "Tandoori Roti", 5),
            new MenuItem(6, "Khulcha", 40.0, "Tandoori Roti", 6),
            new MenuItem(7, "Butter Khulcha", 45.0, "Tandoori Roti", 7),
            new MenuItem(8, "Lacha Paratha", 45.0, "Tandoori Roti", 8),
            new MenuItem(9, "Butter Paratha", 45.0, "Tandoori Roti", 9),
            new MenuItem(10, "Aloo Paratha", 70.0, "Tandoori Roti", 10),
            new MenuItem(11, "Chi. Kheema Paratha", 120.0, "Tandoori Roti", 11),
            new MenuItem(12, "Paneer Paratha", 120.0, "Tandoori Roti", 12),
            new MenuItem(13, "Veg Kheema Paratha", 120.0, "Tandoori Roti", 13)
        ));

        // Biryani (Veg/Non Veg) - 14 items
        allSampleDishes.addAll(List.of(
            new MenuItem(14, "Steam Basmati Rice (H/F)", 60.0, "Biryani", 14),
            new MenuItem(15, "Jeera Rice (H/F)", 70.0, "Biryani", 15),
            new MenuItem(16, "Veg Pulao", 170.0, "Biryani", 16),
            new MenuItem(17, "Veg Biryani", 180.0, "Biryani", 17),
            new MenuItem(18, "Egg Biryani", 190.0, "Biryani", 18),
            new MenuItem(19, "Paneer Pulav", 180.0, "Biryani", 19),
            new MenuItem(20, "Paneer Biryani", 190.0, "Biryani", 20),
            new MenuItem(21, "Chicken Biryani", 220.0, "Biryani", 21),
            new MenuItem(22, "Chi. Dum Biryani", 230.0, "Biryani", 22),
            new MenuItem(23, "Chicken Hyderabadi Biryani", 230.0, "Biryani", 23),
            new MenuItem(24, "Chicken Tikka Biryani", 250.0, "Biryani", 24),
            new MenuItem(25, "Mutton Biryani", 280.0, "Biryani", 25),
            new MenuItem(26, "Mutton Dum Biryani", 300.0, "Biryani", 26),
            new MenuItem(27, "Mutton Hyderabadi Biryani", 300.0, "Biryani", 27)
        ));

        // Tandoori (Veg) - 6 items
        allSampleDishes.addAll(List.of(
            new MenuItem(28, "Paneer Tikka", 200.0, "Tandoori", 28),
            new MenuItem(29, "Paneer Malai Tikka", 220.0, "Tandoori", 29),
            new MenuItem(30, "Veg Seek Kabab", 190.0, "Tandoori", 30),
            new MenuItem(31, "Mushroom Tikka", 200.0, "Tandoori", 31),
            new MenuItem(32, "Baby Corn Tikka", 190.0, "Tandoori", 32),
            new MenuItem(33, "Chilly Milly Kabab", 200.0, "Tandoori", 33)
        ));

        // Tandoori Chicken - 13 items
        allSampleDishes.addAll(List.of(
            new MenuItem(34, "Chicken Tikka", 210.0, "Tandoori", 34),
            new MenuItem(35, "Chicken Tandoori Half", 210.0, "Tandoori", 35),
            new MenuItem(36, "Chicken Tandoori Full", 400.0, "Tandoori", 36),
            new MenuItem(37, "Chicken Pahadi Tandoori Half", 220.0, "Tandoori", 37),
            new MenuItem(38, "Chicken Pahadi Tandoori Full", 410.0, "Tandoori", 38),
            new MenuItem(39, "Chicken Lemon Tandoori Half", 240.0, "Tandoori", 39),
            new MenuItem(40, "Chicken Lemon Tandoori Full", 420.0, "Tandoori", 40),
            new MenuItem(41, "Chicken Kalimiri Kabab", 260.0, "Tandoori", 41),
            new MenuItem(42, "Chicken Banjara Kabab", 260.0, "Tandoori", 42),
            new MenuItem(43, "Chicken Sholay Kabab", 260.0, "Tandoori", 43),
            new MenuItem(44, "Chicken Sikkh Kabab", 280.0, "Tandoori", 44),
            new MenuItem(45, "Chicken Tangri Kabab", 200.0, "Tandoori", 45),
            new MenuItem(46, "Chicken Rajwadi Kabab", 300.0, "Tandoori", 46)
        ));

        // Papad & Salad - 8 items
        allSampleDishes.addAll(List.of(
            new MenuItem(47, "Roasted Papad", 20.0, "Papad & Salad", 47),
            new MenuItem(48, "Fry Papad", 25.0, "Papad & Salad", 48),
            new MenuItem(49, "Masala Papad", 50.0, "Papad & Salad", 49),
            new MenuItem(50, "Green Salad", 90.0, "Papad & Salad", 50),
            new MenuItem(51, "Raita", 100.0, "Papad & Salad", 51),
            new MenuItem(52, "Boondi Raita", 130.0, "Papad & Salad", 52),
            new MenuItem(53, "Schzewan Sauce Extra", 20.0, "Papad & Salad", 53),
            new MenuItem(54, "Fry Noodles Extra", 20.0, "Papad & Salad", 54)
        ));

        // Mutton Gravy - 8 items
        allSampleDishes.addAll(List.of(
            new MenuItem(55, "Mutton Masala", 330.0, "Mutton Gravy", 55),
            new MenuItem(56, "Mutton Kadhai", 330.0, "Mutton Gravy", 56),
            new MenuItem(57, "Mutton Kolhapuri", 330.0, "Mutton Gravy", 57),
            new MenuItem(58, "Mutton Hyderabadi", 330.0, "Mutton Gravy", 58),
            new MenuItem(59, "Mutton Handi (Half) 6pcs", 470.0, "Mutton Gravy", 59),
            new MenuItem(60, "Mutton Handi (Full) 12pcs", 750.0, "Mutton Gravy", 60),
            new MenuItem(61, "Mutton Do Pyaza", 330.0, "Mutton Gravy", 61),
            new MenuItem(62, "Mutton Shukar", 350.0, "Mutton Gravy", 62)
        ));

        // Sea Food - 11 items
        allSampleDishes.addAll(List.of(
            new MenuItem(63, "Bangda Fry", 130.0, "Sea Food", 63),
            new MenuItem(64, "Bangda Masala", 180.0, "Sea Food", 64),
            new MenuItem(65, "Mandeli Oil Fry", 150.0, "Sea Food", 65),
            new MenuItem(66, "Surmai Tawa Fry", 195.0, "Sea Food", 66),
            new MenuItem(67, "Surmai Koliwada", 195.0, "Sea Food", 67),
            new MenuItem(68, "Prawns Tawa Fry", 255.0, "Sea Food", 68),
            new MenuItem(69, "Prawns Koliwada", 250.0, "Sea Food", 69),
            new MenuItem(70, "Surmai Gavan Curry", 195.0, "Sea Food", 70),
            new MenuItem(71, "Surmai Masala", 195.0, "Sea Food", 71),
            new MenuItem(82, "Fish Curry", 220.0, "Sea Food", 82),
            new MenuItem(83, "Prawn Curry", 280.0, "Sea Food", 83)
        ));

        // Bulk Order (Per KG) - 10 items
        allSampleDishes.addAll(List.of(
            new MenuItem(72, "Paneer Pulav", 800.0, "Bulk Order", 72),
            new MenuItem(73, "Veg Biryani", 800.0, "Bulk Order", 73),
            new MenuItem(74, "Chicken Biryani", 1000.0, "Bulk Order", 74),
            new MenuItem(75, "Mutton Biryani", 1300.0, "Bulk Order", 75),
            new MenuItem(76, "Veg Pulav", 700.0, "Bulk Order", 76),
            new MenuItem(77, "Chicken Masala", 900.0, "Bulk Order", 77),
            new MenuItem(78, "Jira Rice", 650.0, "Bulk Order", 78),
            new MenuItem(79, "Steam Rice", 650.0, "Bulk Order", 79),
            new MenuItem(80, "Chicken Malai Tikka", 240.0, "Tandoori", 80),
            new MenuItem(81, "Chicken Seekh Kabab", 280.0, "Tandoori", 81)
        ));

        // Soup (Veg) - 5 items
        allSampleDishes.addAll(List.of(
            new MenuItem(84, "Manchow Soup", 110.0, "Soup (Veg)", 84),
            new MenuItem(85, "Schezwan Soup", 110.0, "Soup (Veg)", 85),
            new MenuItem(86, "Noodles Soup", 110.0, "Soup (Veg)", 86),
            new MenuItem(87, "Clear Soup", 110.0, "Soup (Veg)", 87),
            new MenuItem(88, "Hot N Sour Soup", 110.0, "Soup (Veg)", 88)
        ));

        // Soup (Non-Veg) - 6 items
        allSampleDishes.addAll(List.of(
            new MenuItem(89, "Chicken Manchow Soup", 120.0, "Soup (Non-Veg)", 89),
            new MenuItem(90, "Chicken Hot N Sour Soup", 120.0, "Soup (Non-Veg)", 90),
            new MenuItem(91, "Chicken Lung Fung Soup", 120.0, "Soup (Non-Veg)", 91),
            new MenuItem(92, "Chicken Schezwan Soup", 120.0, "Soup (Non-Veg)", 92),
            new MenuItem(93, "Chicken Noodles Soup", 120.0, "Soup (Non-Veg)", 93),
            new MenuItem(94, "Chicken Clear Soup", 120.0, "Soup (Non-Veg)", 94)
        ));

        // Noodles (Veg) - 9 items
        allSampleDishes.addAll(List.of(
            new MenuItem(95, "Veg Hakka Noodles", 160.0, "Noodles (Veg)", 95),
            new MenuItem(96, "Veg Schezwan Noodles", 170.0, "Noodles (Veg)", 96),
            new MenuItem(97, "Veg Singapore Noodles", 190.0, "Noodles (Veg)", 97),
            new MenuItem(98, "Veg Hong Kong Noodles", 190.0, "Noodles (Veg)", 98),
            new MenuItem(99, "Veg Mushroom Noodles", 180.0, "Noodles (Veg)", 99),
            new MenuItem(100, "Veg Manchurian Noodles", 190.0, "Noodles (Veg)", 100),
            new MenuItem(101, "Veg Sherpa Noodles", 220.0, "Noodles (Veg)", 101),
            new MenuItem(102, "Veg Triple Sch. Noodles", 220.0, "Noodles (Veg)", 102),
            new MenuItem(103, "Veg Chilly Garlic Noodles", 220.0, "Noodles (Veg)", 103)
        ));

        // Noodles (Non-Veg) - 12 items
        allSampleDishes.addAll(List.of(
            new MenuItem(104, "Egg Hakka Noodles", 160.0, "Noodles (Non-Veg)", 104),
            new MenuItem(105, "Egg Schezwan Noodles", 170.0, "Noodles (Non-Veg)", 105),
            new MenuItem(106, "Chicken Hakka Noodles", 180.0, "Noodles (Non-Veg)", 106),
            new MenuItem(107, "Chi. Schezwan Noodles", 190.0, "Noodles (Non-Veg)", 107),
            new MenuItem(108, "Chi. Singapore Noodles", 200.0, "Noodles (Non-Veg)", 108),
            new MenuItem(109, "Chi. Hong Kong Noodles", 200.0, "Noodles (Non-Veg)", 109),
            new MenuItem(110, "Chi. Mushroom Noodles", 200.0, "Noodles (Non-Veg)", 110),
            new MenuItem(111, "Chi. Triple Schezwan Noodles", 250.0, "Noodles (Non-Veg)", 111),
            new MenuItem(112, "Chi. Sherpa Noodles", 250.0, "Noodles (Non-Veg)", 112),
            new MenuItem(113, "Chi. Thousand Noodles", 280.0, "Noodles (Non-Veg)", 113),
            new MenuItem(114, "Chi. Chilly Basil Noodles", 250.0, "Noodles (Non-Veg)", 114),
            new MenuItem(115, "Chicken Chilly Garlic Noodles", 250.0, "Noodles (Non-Veg)", 115)
        ));

        // Rice (Veg) - 12 items
        allSampleDishes.addAll(List.of(
            new MenuItem(116, "Veg Fry Rice", 170.0, "Rice (Veg)", 116),
            new MenuItem(117, "Veg Schezwan Rice", 180.0, "Rice (Veg)", 117),
            new MenuItem(118, "Veg Singapore Rice", 190.0, "Rice (Veg)", 118),
            new MenuItem(119, "Veg Hong Kong Rice", 190.0, "Rice (Veg)", 119),
            new MenuItem(120, "Veg Schezwan Combination Rice", 190.0, "Rice (Veg)", 120),
            new MenuItem(121, "Veg Manchurian Rice", 210.0, "Rice (Veg)", 121),
            new MenuItem(122, "Veg Triple Schoz. Rice", 210.0, "Rice (Veg)", 122),
            new MenuItem(123, "Paneer Fry Rice", 200.0, "Rice (Veg)", 123),
            new MenuItem(124, "Paneer Schezwan Rice", 200.0, "Rice (Veg)", 124),
            new MenuItem(125, "Veg Sherpa Rice", 230.0, "Rice (Veg)", 125),
            new MenuItem(126, "Veg Thousand Rice", 230.0, "Rice (Veg)", 126),
            new MenuItem(127, "Veg Chilly Basil Rice", 200.0, "Rice (Veg)", 127)
        ));

        // Rice (Non-Veg) - 16 items
        allSampleDishes.addAll(List.of(
            new MenuItem(128, "Chi. Schezwan Rice", 190.0, "Rice (Non-Veg)", 128),
            new MenuItem(129, "Chi. Singapore Rice", 200.0, "Rice (Non-Veg)", 129),
            new MenuItem(130, "Chi. Hong Kong Rice", 200.0, "Rice (Non-Veg)", 130),
            new MenuItem(131, "Chi. Sez. Combination Rice", 210.0, "Rice (Non-Veg)", 131),
            new MenuItem(132, "Chi. Burn Garlic Rice", 210.0, "Rice (Non-Veg)", 132),
            new MenuItem(133, "Chi. Chilly Garlic Rice", 210.0, "Rice (Non-Veg)", 133),
            new MenuItem(134, "Chi. Manchurian Rice", 250.0, "Rice (Non-Veg)", 134),
            new MenuItem(135, "Chi. Triple Schoz. Rice", 250.0, "Rice (Non-Veg)", 135),
            new MenuItem(136, "Chi. Sherpa Rice", 260.0, "Rice (Non-Veg)", 136),
            new MenuItem(137, "Chi. Thousand Rice", 300.0, "Rice (Non-Veg)", 137),
            new MenuItem(138, "Chi. Jadoo Rice", 280.0, "Rice (Non-Veg)", 138),
            new MenuItem(139, "Chi. Ginger Garlic Rice", 210.0, "Rice (Non-Veg)", 139),
            new MenuItem(140, "Chi. Chilly Basil Rice", 220.0, "Rice (Non-Veg)", 140),
            new MenuItem(141, "Egg Fry Rice", 170.0, "Rice (Non-Veg)", 141),
            new MenuItem(142, "Egg Schezwan Rice", 180.0, "Rice (Non-Veg)", 142),
            new MenuItem(143, "Chi. Fry Rice", 180.0, "Rice (Non-Veg)", 143)
        ));

        // Chinese Gravy - 9 items
        allSampleDishes.addAll(List.of(
            new MenuItem(144, "Manchurian Gravy / Chilly", 180.0, "Chinese Gravy", 144),
            new MenuItem(145, "Schezwan Gravy", 180.0, "Chinese Gravy", 145),
            new MenuItem(146, "Chilly Gravy", 180.0, "Chinese Gravy", 146),
            new MenuItem(147, "Kum Pav Gravy", 180.0, "Chinese Gravy", 147),
            new MenuItem(148, "Hot Garlic Gravy", 190.0, "Chinese Gravy", 148),
            new MenuItem(149, "Oyster Gravy", 190.0, "Chinese Gravy", 149),
            new MenuItem(150, "Paneer Sch. Gravy", 190.0, "Chinese Gravy", 150),
            new MenuItem(151, "Paneer Manch. Gravy", 190.0, "Chinese Gravy", 151),
            new MenuItem(152, "Paneer Chilly Gravy", 190.0, "Chinese Gravy", 152)
        ));

        // Indian & Punjabi (Veg) - 28 items (Part 1)
        allSampleDishes.addAll(List.of(
            new MenuItem(153, "Dal Fry", 130.0, "Indian & Punjabi (Veg)", 153),
            new MenuItem(154, "Dal Tadka", 150.0, "Indian & Punjabi (Veg)", 154),
            new MenuItem(155, "Dal Palak", 150.0, "Indian & Punjabi (Veg)", 155),
            new MenuItem(156, "Dal Khichadi (1000ML)", 220.0, "Indian & Punjabi (Veg)", 156),
            new MenuItem(157, "Palak Khichadi (1000ML)", 240.0, "Indian & Punjabi (Veg)", 157),
            new MenuItem(158, "Dal Khichadi Tadka (1000ML)", 240.0, "Indian & Punjabi (Veg)", 158),
            new MenuItem(159, "Palak Khichadi Tadka (1000ML)", 240.0, "Indian & Punjabi (Veg)", 159),
            new MenuItem(160, "Mix Veg", 180.0, "Indian & Punjabi (Veg)", 160),
            new MenuItem(161, "Veg Kadhai", 190.0, "Indian & Punjabi (Veg)", 161),
            new MenuItem(162, "Veg Kolhapuri", 190.0, "Indian & Punjabi (Veg)", 162),
            new MenuItem(163, "Veg Tawa", 190.0, "Indian & Punjabi (Veg)", 163),
            new MenuItem(164, "Veg Lajawab", 190.0, "Indian & Punjabi (Veg)", 164),
            new MenuItem(165, "Veg Chilly Milly", 220.0, "Indian & Punjabi (Veg)", 165),
            new MenuItem(166, "Aloo Mutter", 170.0, "Indian & Punjabi (Veg)", 166)
        ));

        // Indian & Punjabi (Veg) - 28 items (Part 2)
        allSampleDishes.addAll(List.of(
            new MenuItem(167, "Veg Handi (Half/Full)", 210.0, "Indian & Punjabi (Veg)", 167),
            new MenuItem(168, "Paneer Masala", 200.0, "Indian & Punjabi (Veg)", 168),
            new MenuItem(169, "Paneer Mutter Masala", 200.0, "Indian & Punjabi (Veg)", 169),
            new MenuItem(170, "Paneer Butter Masala", 220.0, "Indian & Punjabi (Veg)", 170),
            new MenuItem(171, "Paneer Kadhai", 200.0, "Indian & Punjabi (Veg)", 171),
            new MenuItem(172, "Paneer Bhurji Masala", 220.0, "Indian & Punjabi (Veg)", 172),
            new MenuItem(173, "Paneer Mutter", 200.0, "Indian & Punjabi (Veg)", 173),
            new MenuItem(174, "Palak Paneer", 200.0, "Indian & Punjabi (Veg)", 174),
            new MenuItem(175, "Mushroom Masala", 210.0, "Indian & Punjabi (Veg)", 175),
            new MenuItem(176, "Mushroom Tikka Masala", 230.0, "Indian & Punjabi (Veg)", 176),
            new MenuItem(177, "Lasuni Palak", 190.0, "Indian & Punjabi (Veg)", 177),
            new MenuItem(178, "Veg Maratha", 250.0, "Indian & Punjabi (Veg)", 178),
            new MenuItem(179, "Sev Bhaji", 180.0, "Indian & Punjabi (Veg)", 179),
            new MenuItem(180, "Masala Fry Masala", 200.0, "Indian & Punjabi (Veg)", 180)
        ));

        // Chicken Gravy - 19 items (Part 1)
        allSampleDishes.addAll(List.of(
            new MenuItem(181, "Chicken Masala", 210.0, "Chicken Gravy", 181),
            new MenuItem(182, "Chicken Curry", 210.0, "Chicken Gravy", 182),
            new MenuItem(183, "Chicken Kadhai", 240.0, "Chicken Gravy", 183),
            new MenuItem(184, "Chicken Bhurjani", 240.0, "Chicken Gravy", 184),
            new MenuItem(185, "Chicken Tawa Masala", 260.0, "Chicken Gravy", 185),
            new MenuItem(186, "Chicken Gayti Masala", 260.0, "Chicken Gravy", 186),
            new MenuItem(187, "Chicken Tikka Masala", 260.0, "Chicken Gravy", 187),
            new MenuItem(188, "Chicken Maratha", 280.0, "Chicken Gravy", 188),
            new MenuItem(189, "Chicken Lasuni Masala", 260.0, "Chicken Gravy", 189),
            new MenuItem(190, "Chicken Japeta (H/F)", 310.0, "Chicken Gravy", 190)
        ));

        // Chicken Gravy - 19 items (Part 2)
        allSampleDishes.addAll(List.of(
            new MenuItem(191, "Butter Chicken (H/F)", 290.0, "Chicken Gravy", 191),
            new MenuItem(192, "Chicken Malvani Masala", 260.0, "Chicken Gravy", 192),
            new MenuItem(193, "Chicken Tikka Lemon Masala", 260.0, "Chicken Gravy", 193),
            new MenuItem(194, "Chicken Hyderabadi Masala", 260.0, "Chicken Gravy", 194),
            new MenuItem(195, "Chicken Mughlai", 260.0, "Chicken Gravy", 195),
            new MenuItem(196, "Chicken Pahadi Masala", 260.0, "Chicken Gravy", 196),
            new MenuItem(197, "Chicken Handi (Half) 6pcs", 260.0, "Chicken Gravy", 197),
            new MenuItem(198, "Chicken Handi (Full) 12pcs", 480.0, "Chicken Gravy", 198),
            new MenuItem(199, "Chicken Do Pyaza", 260.0, "Chicken Gravy", 199)
        ));

        // Starters (Veg) - 14 items
        allSampleDishes.addAll(List.of(
            new MenuItem(200, "Veg Manchurian / Chilly", 190.0, "Starters (Veg)", 200),
            new MenuItem(201, "Veg Chinese Bhel", 190.0, "Starters (Veg)", 201),
            new MenuItem(202, "Mushroom Chilly/ Manchurian", 200.0, "Starters (Veg)", 202),
            new MenuItem(203, "Paneer Chilly/ Manchurian", 220.0, "Starters (Veg)", 203),
            new MenuItem(204, "Paneer Crispy", 220.0, "Starters (Veg)", 204),
            new MenuItem(205, "Paneer Singapur", 220.0, "Starters (Veg)", 205),
            new MenuItem(206, "Veg Crispy", 200.0, "Starters (Veg)", 206),
            new MenuItem(207, "Crispy Chilly Potato", 220.0, "Starters (Veg)", 207),
            new MenuItem(208, "Honey Chilly Potato", 220.0, "Starters (Veg)", 208),
            new MenuItem(209, "Paneer Shangai Wok", 250.0, "Starters (Veg)", 209),
            new MenuItem(210, "Paneer Schezwan Wok", 250.0, "Starters (Veg)", 210),
            new MenuItem(211, "Paneer Chilly Basil Wok", 260.0, "Starters (Veg)", 211),
            new MenuItem(212, "Paneer Honey Chilly Wok", 260.0, "Starters (Veg)", 212),
            new MenuItem(213, "Paneer Kum Pav Wok", 260.0, "Starters (Veg)", 213)
        ));

        // Starters (Non-Veg) - 14 items
        allSampleDishes.addAll(List.of(
            new MenuItem(214, "Chi. Chinese Bhel", 180.0, "Starters (Non-Veg)", 214),
            new MenuItem(215, "Chi. Chilly/Manchurian", 220.0, "Starters (Non-Veg)", 215),
            new MenuItem(216, "Chi. Schezwan", 220.0, "Starters (Non-Veg)", 216),
            new MenuItem(217, "Chi. Chilly Garlic Wok", 230.0, "Starters (Non-Veg)", 217),
            new MenuItem(218, "Chi. Kum Pav Wok", 260.0, "Starters (Non-Veg)", 218),
            new MenuItem(219, "Chi. Crispy", 250.0, "Starters (Non-Veg)", 219),
            new MenuItem(220, "Chi. Singapur", 250.0, "Starters (Non-Veg)", 220),
            new MenuItem(221, "Chi. Lamba", 250.0, "Starters (Non-Veg)", 221),
            new MenuItem(222, "Chi. Oyster Sauces", 250.0, "Starters (Non-Veg)", 222),
            new MenuItem(223, "Chi. Black Paper Wok", 250.0, "Starters (Non-Veg)", 223),
            new MenuItem(224, "Chi. Lollypop 8 Pc.", 230.0, "Starters (Non-Veg)", 224),
            new MenuItem(225, "Chi. Lollypop Schzwn/Hnypp", 300.0, "Starters (Non-Veg)", 225),
            new MenuItem(226, "Chi. Honey Chilly", 270.0, "Starters (Non-Veg)", 226),
            new MenuItem(227, "Chi. Chilly Basil Wok", 270.0, "Starters (Non-Veg)", 227)
        ));

        // Egg Dishes - 6 items
        allSampleDishes.addAll(List.of(
            new MenuItem(228, "Boiled Egg", 40.0, "Egg Dishes", 228),
            new MenuItem(229, "Egg Omlete", 50.0, "Egg Dishes", 229),
            new MenuItem(230, "Egg Bhurji", 110.0, "Egg Dishes", 230),
            new MenuItem(231, "Egg Masala", 170.0, "Egg Dishes", 231),
            new MenuItem(232, "Egg Curry", 170.0, "Egg Dishes", 232),
            new MenuItem(233, "Anda Ghotala", 180.0, "Egg Dishes", 233)
        ));

        // Filter dishes based on search text (including shortcuts)
        String lowerSearch = searchText.toLowerCase();
        List<MenuItem> filteredDishes = allSampleDishes.stream()
            .filter(dish -> MenuShortcuts.matchesItem(dish.getName(), lowerSearch) ||
                           dish.getCategory().toLowerCase().contains(lowerSearch))
            .collect(Collectors.toList());

        displaySearchResults(filteredDishes, searchText);
    }

    private VBox createDishCard(MenuItem dish) {
        VBox card = new VBox(8);
        card.setStyle("-fx-background-color: white; -fx-border-color: #ddd; -fx-border-radius: 8px; " +
                     "-fx-background-radius: 8px; -fx-padding: 15px; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);");

        // Dish name
        Label nameLabel = new Label(dish.getName());
        nameLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #333;");

        // Price and category
        HBox infoBox = new HBox(15);
        infoBox.setAlignment(javafx.geometry.Pos.CENTER_LEFT);

        Label priceLabel = new Label("₹" + String.format("%.0f", dish.getPrice()));
        priceLabel.setStyle("-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #e74c3c;");

        Label categoryLabel = new Label(dish.getCategory());
        categoryLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #666; -fx-background-color: #f8f9fa; " +
                              "-fx-padding: 4px 8px; -fx-background-radius: 12px;");

        infoBox.getChildren().addAll(priceLabel, categoryLabel);

        // Add to order button
        Button addButton = new Button("Add to Order");
        addButton.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-background-radius: 6px; " +
                          "-fx-padding: 8px 16px; -fx-font-size: 12px; -fx-cursor: hand;");
        addButton.setOnAction(e -> addDishToOrder(dish));

        card.getChildren().addAll(nameLabel, infoBox, addButton);
        return card;
    }

    private void addDishToOrder(MenuItem dish) {
        // This would typically add the dish to current order
        showAlert("Add to Order",
                 "Added to order:\n\n" +
                 "🍽️ " + dish.getName() + "\n" +
                 "💰 ₹" + String.format("%.0f", dish.getPrice()) + "\n" +
                 "📂 " + dish.getCategory() + "\n\n" +
                 "This would normally add the item to the current table's order.");
    }

    private void filterTables(String searchText) {
        if (searchText == null) {
            searchText = "";
        }

        String lowerCaseSearch = searchText.toLowerCase().trim();

        // Show all tables if search is empty
        if (lowerCaseSearch.isEmpty()) {
            showAllTables();
            return;
        }

        // Filter tables based on search text
        for (VBox tableCard : allTableCards) {
            if (tableCard != null) {
                boolean shouldShow = false;

                // Get table number from the card
                String tableNumber = getTableNumberFromCard(tableCard);

                // Get table status
                String tableStatus = getTableStatusFromCard(tableCard);

                // Check if search matches table number or status
                if (tableNumber.toLowerCase().contains(lowerCaseSearch) ||
                    tableStatus.toLowerCase().contains(lowerCaseSearch)) {
                    shouldShow = true;
                }

                // Show/hide table based on search match
                tableCard.setVisible(shouldShow);
                tableCard.setManaged(shouldShow);
            }
        }

        System.out.println("Filtered tables with search: '" + searchText + "'");
    }

    private void showAllTables() {
        // Show all tables
        for (VBox tableCard : allTableCards) {
            if (tableCard != null) {
                tableCard.setVisible(true);
                tableCard.setManaged(true);
            }
        }
    }

    private String getTableNumberFromCard(VBox tableCard) {
        // Extract table number from the card
        // The table number is typically in a label within the card
        try {
            for (Node child : tableCard.getChildren()) {
                if (child instanceof Label) {
                    Label label = (Label) child;
                    String text = label.getText();
                    if (text != null && text.startsWith("Table")) {
                        return text;
                    }
                }
                if (child instanceof HBox || child instanceof VBox) {
                    // Check nested containers
                    String result = getTableNumberFromContainer(child);
                    if (result != null) {
                        return result;
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("Error extracting table number: " + e.getMessage());
        }

        // Fallback: determine table number from the card reference
        if (tableCard == table1) return "Table 1";
        if (tableCard == table2) return "Table 2";
        if (tableCard == table3) return "Table 3";
        if (tableCard == table4) return "Table 4";
        if (tableCard == table5) return "Table 5";
        if (tableCard == table6) return "Table 6";
        if (tableCard == table7) return "Table 7";
        if (tableCard == table8) return "Table 8";
        if (tableCard == table9) return "Table 9";

        return "Unknown";
    }

    private String getTableNumberFromContainer(Node container) {
        try {
            if (container instanceof HBox) {
                HBox hbox = (HBox) container;
                for (Node child : hbox.getChildren()) {
                    if (child instanceof Label) {
                        Label label = (Label) child;
                        String text = label.getText();
                        if (text != null && text.startsWith("Table")) {
                            return text;
                        }
                    }
                }
            } else if (container instanceof VBox) {
                VBox vbox = (VBox) container;
                for (Node child : vbox.getChildren()) {
                    if (child instanceof Label) {
                        Label label = (Label) child;
                        String text = label.getText();
                        if (text != null && text.startsWith("Table")) {
                            return text;
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("Error extracting table number from container: " + e.getMessage());
        }
        return null;
    }

    private String getTableStatusFromCard(VBox tableCard) {
        // Extract table status from the card
        // This would typically be determined by the table's current state
        try {
            Integer tableNum = getTableNumberAsInteger(tableCard);
            if (tableNum != null && tableStatuses.containsKey(tableNum)) {
                TableStatus status = tableStatuses.get(tableNum);
                return status.toString();
            }
        } catch (Exception e) {
            System.err.println("Error extracting table status: " + e.getMessage());
        }

        // Default status
        return "Free";
    }

    private Integer getTableNumberAsInteger(VBox tableCard) {
        if (tableCard == table1) return 1;
        if (tableCard == table2) return 2;
        if (tableCard == table3) return 3;
        if (tableCard == table4) return 4;
        if (tableCard == table5) return 5;
        if (tableCard == table6) return 6;
        if (tableCard == table7) return 7;
        if (tableCard == table8) return 8;
        if (tableCard == table9) return 9;
        return null;
    }

    private void loadTableDataAsync() {
        showLoadingState();

        Task<Map<Integer, TableStatus>> loadTask = new Task<Map<Integer, TableStatus>>() {
            @Override
            protected Map<Integer, TableStatus> call() throws Exception {
                return loadTableStatuses();
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    try {
                        Map<Integer, TableStatus> statuses = getValue();
                        tableStatuses.clear();
                        tableStatuses.putAll(statuses);
                        updateTableDisplay();
                        hideLoadingState();
                        updateStatusLabel("Tables loaded successfully");
                        System.out.println("Loaded " + statuses.size() + " table statuses");
                    } catch (Exception e) {
                        e.printStackTrace();
                        loadSampleTableData();
                    }
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    System.err.println("Failed to load table data: " + getException().getMessage());
                    loadSampleTableData();
                });
            }
        };

        Thread backgroundThread = new Thread(loadTask);
        backgroundThread.setDaemon(true);
        backgroundThread.start();
    }

    private Map<Integer, TableStatus> loadTableStatuses() {
        Map<Integer, TableStatus> statuses = new HashMap<>();

        try {
            // Try to load from database
            // This would be replaced with actual database calls
            // For now, create sample data with some realistic scenarios

            // Table 1 - Available
            statuses.put(1, new TableStatus(1));

            // Table 2 - Has active order
            statuses.put(2, new TableStatus(2, "PENDING", 450.00, LocalDateTime.now().minusMinutes(15), 1001));

            // Table 3 - Available
            statuses.put(3, new TableStatus(3));

            // Table 4 - Order ready
            statuses.put(4, new TableStatus(4, "READY", 320.50, LocalDateTime.now().minusMinutes(25), 1002));

            // Table 5 - Working on order
            statuses.put(5, new TableStatus(5, "WORKING", 680.75, LocalDateTime.now().minusMinutes(10), 1003));

            // Table 6 - Available
            statuses.put(6, new TableStatus(6));

            // Table 7 - KOT printed
            statuses.put(7, new TableStatus(7, "KOT_PRINTED", 290.00, LocalDateTime.now().minusMinutes(5), 1004));

            // Table 8 - Available
            statuses.put(8, new TableStatus(8));

            // Table 9 - Completed, ready for billing
            statuses.put(9, new TableStatus(9, "COMPLETED", 520.25, LocalDateTime.now().minusMinutes(30), 1005));

        } catch (Exception e) {
            e.printStackTrace();
            // Fallback to basic available tables
            for (int i = 1; i <= 9; i++) {
                statuses.put(i, new TableStatus(i));
            }
        }

        return statuses;
    }

    private void loadSampleTableData() {
        tableStatuses.clear();

        // Create sample table statuses
        tableStatuses.put(1, new TableStatus(1));
        tableStatuses.put(2, new TableStatus(2, "PENDING", 450.00, LocalDateTime.now().minusMinutes(15), 1001));
        tableStatuses.put(3, new TableStatus(3));
        tableStatuses.put(4, new TableStatus(4, "READY", 320.50, LocalDateTime.now().minusMinutes(25), 1002));
        tableStatuses.put(5, new TableStatus(5, "WORKING", 680.75, LocalDateTime.now().minusMinutes(10), 1003));
        tableStatuses.put(6, new TableStatus(6));
        tableStatuses.put(7, new TableStatus(7, "KOT_PRINTED", 290.00, LocalDateTime.now().minusMinutes(5), 1004));
        tableStatuses.put(8, new TableStatus(8));
        tableStatuses.put(9, new TableStatus(9, "COMPLETED", 520.25, LocalDateTime.now().minusMinutes(30), 1005));

        updateTableDisplay();
        hideLoadingState();
        updateStatusLabel("Sample table data loaded");
        System.out.println("Loaded sample table data for " + tableStatuses.size() + " tables");
    }

    private void updateTableDisplay() {
        System.out.println("Updating table display for " + tableCards.size() + " tables");

        // Ensure GridPane is visible
        if (tablesGrid != null) {
            tablesGrid.setVisible(true);
            tablesGrid.setManaged(true);
            System.out.println("GridPane visibility set to true");
        }

        for (int i = 0; i < tableCards.size(); i++) {
            VBox tableCard = tableCards.get(i);
            int tableNumber = i + 1;
            TableStatus status = tableStatuses.get(tableNumber);

            System.out.println("Updating Table " + tableNumber + " with status: " +
                (status != null ? status.getStatus() : "null"));

            if (tableCard != null) {
                // Ensure table card is visible
                tableCard.setVisible(true);
                tableCard.setManaged(true);

                if (status != null) {
                    updateTableCard(tableCard, status);
                } else {
                    // If no status found, create default available status
                    TableStatus defaultStatus = new TableStatus(tableNumber);
                    updateTableCard(tableCard, defaultStatus);
                }

                System.out.println("Table " + tableNumber + " visibility: " + tableCard.isVisible() +
                    ", managed: " + tableCard.isManaged());
            } else {
                System.err.println("Table card " + tableNumber + " is null!");
            }
        }

        // Force layout refresh
        if (tablesGrid != null) {
            Platform.runLater(() -> {
                tablesGrid.requestLayout();
                System.out.println("GridPane layout refresh requested");
            });
        }

        System.out.println("Table display update complete");
    }

    private void updateTableCard(VBox tableCard, TableStatus status) {
        System.out.println("Updating table card for Table " + status.getTableNumber() +
            " with status: " + status.getStatus());

        // Clear existing style classes
        tableCard.getStyleClass().removeAll("table-free", "table-occupied", "table-preparing",
                                           "blank-table", "running-table", "printed-table",
                                           "paid-table", "running-kot-table");

        // Add appropriate style class based on status
        tableCard.getStyleClass().add("table-card");
        tableCard.getStyleClass().add(status.getStyleClass());

        // Update table content based on structure
        try {
            if (tableCard.getChildren().size() >= 3) {
                // Structure: Label (table number), HBox (seats), Label (status), [optional order badge]

                // Update table number (first child)
                if (tableCard.getChildren().get(0) instanceof Label) {
                    Label tableNumberLabel = (Label) tableCard.getChildren().get(0);
                    tableNumberLabel.setText("Table " + status.getTableNumber());
                }

                // Update seats info (second child - HBox)
                if (tableCard.getChildren().get(1) instanceof HBox) {
                    HBox seatsBox = (HBox) tableCard.getChildren().get(1);
                    if (seatsBox.getChildren().size() >= 2 &&
                        seatsBox.getChildren().get(1) instanceof Label) {
                        Label seatsLabel = (Label) seatsBox.getChildren().get(1);
                        seatsLabel.setText(status.getSeats() + " seats");
                    }
                }

                // Find and update status label (last label in children)
                Label statusLabel = null;
                for (int i = tableCard.getChildren().size() - 1; i >= 0; i--) {
                    if (tableCard.getChildren().get(i) instanceof Label) {
                        Label label = (Label) tableCard.getChildren().get(i);
                        if (label.getStyleClass().contains("table-status") || i == tableCard.getChildren().size() - 1) {
                            statusLabel = label;
                            break;
                        }
                    }
                }

                if (statusLabel != null) {
                    String displayText = status.getDisplayStatus();

                    if (status.hasActiveOrder()) {
                        if (status.shouldShowTiming()) {
                            displayText += "\n" + status.getElapsedTime();
                        }
                        if (status.getTotalAmount() > 0) {
                            displayText += "\n" + status.getFormattedAmount();
                        }
                    }

                    statusLabel.setText(displayText);
                    statusLabel.getStyleClass().clear();
                    statusLabel.getStyleClass().add("table-status");
                }

                // Add order badge if needed
                if (status.hasActiveOrder() && status.getOrderIdAsInteger() != null) {
                    // Check if order badge already exists
                    boolean hasBadge = false;
                    for (Node child : tableCard.getChildren()) {
                        if (child instanceof Label) {
                            Label label = (Label) child;
                            if (label.getStyleClass().contains("order-badge")) {
                                label.setText("Order #" + status.getOrderId());
                                hasBadge = true;
                                break;
                            }
                        }
                    }

                    // Add order badge if it doesn't exist
                    if (!hasBadge && tableCard.getChildren().size() >= 3) {
                        Label orderBadge = new Label("Order #" + status.getOrderId());
                        orderBadge.getStyleClass().add("order-badge");
                        tableCard.getChildren().add(tableCard.getChildren().size() - 1, orderBadge);
                    }
                } else {
                    // Remove order badge if table is available
                    tableCard.getChildren().removeIf(child -> {
                        if (child instanceof Label) {
                            Label label = (Label) child;
                            return label.getStyleClass().contains("order-badge");
                        }
                        return false;
                    });
                }
            }

            System.out.println("Table card updated successfully for Table " + status.getTableNumber());

        } catch (Exception e) {
            System.err.println("Error updating table card for Table " + status.getTableNumber() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void showLoadingState() {
        System.out.println("Loading tables...");
    }

    private void hideLoadingState() {
        System.out.println("Tables loaded");
    }

    private void updateStatusLabel(String message) {
        System.out.println("Status: " + message);
    }

    private void setupAutoRefresh() {
        // Setup a timer to refresh table data every 30 seconds
        Platform.runLater(() -> {
            javafx.animation.Timeline timeline = new javafx.animation.Timeline(
                new javafx.animation.KeyFrame(
                    javafx.util.Duration.seconds(30),
                    e -> loadTableDataAsync()
                )
            );
            timeline.setCycleCount(javafx.animation.Timeline.INDEFINITE);
            timeline.play();
        });
    }
    
    @FXML
    private void selectTable(MouseEvent event) {
        VBox clickedTable = (VBox) event.getSource();
        selectedTableId = clickedTable.getId();
        
        // Extract table number from ID (e.g., "table1" -> "1")
        String tableNumber = selectedTableId.replace("table", "");
        
        // Determine table status based on CSS class
        String tableStatus = getTableStatus(clickedTable);
        
        // Handle different table statuses
        switch (tableStatus) {
            case "free":
                handleFreeTable(tableNumber);
                break;
            case "occupied":
                handleOccupiedTable(tableNumber);
                break;
            case "preparing":
                handlePreparingTable(tableNumber);
                break;
            default:
                showAlert("Error", "Unknown table status");
        }
    }
    
    private String getTableStatus(VBox table) {
        // Check CSS classes to determine status
        if (table.getStyleClass().contains("table-free")) {
            return "free";
        } else if (table.getStyleClass().contains("table-occupied")) {
            return "occupied";
        } else if (table.getStyleClass().contains("table-preparing")) {
            return "preparing";
        }
        return "unknown";
    }
    
    private void handleFreeTable(String tableNumber) {
        // Directly navigate to order entry for free tables
        navigateToOrderEntry(tableNumber);
    }
    
    private void handleOccupiedTable(String tableNumber) {
        // Navigate to order entry to add more items to existing order
        navigateToOrderEntry(tableNumber);
    }
    
    private void handlePreparingTable(String tableNumber) {
        // Navigate to order entry to add more items or view current order
        navigateToOrderEntry(tableNumber);
    }
    
    private String getOrderNumberForTable(String tableNumber) {
        // This would typically query the database
        // For demo purposes, return sample order numbers
        switch (tableNumber) {
            case "2": return "#123";
            case "3": return "#124";
            case "5": return "#125";
            case "6": return "#126";
            case "8": return "#127";
            default: return "#000";
        }
    }
    
    private void createNewOrder(String tableNumber) {
        showAlert("New Order", "Creating new order for Table " + tableNumber + "...\n\nRedirecting to Order Entry...");
        // This would typically navigate to order entry with table pre-selected
    }
    
    private void reserveTable(String tableNumber) {
        showAlert("Reserve Table", "Table " + tableNumber + " has been reserved.\n\nReservation time: 8:00 PM\nCustomer: John Doe\nPhone: +91 98765 43210");
    }
    
    private void viewOrder(String orderNumber) {
        showAlert("View Order", "Order Details for " + orderNumber + ":\n\n" +
            "• Chicken Biryani x2 - ₹500.00\n" +
            "• Paneer Butter Masala x1 - ₹180.00\n" +
            "• Garlic Naan x3 - ₹135.00\n" +
            "• Mango Lassi x2 - ₹160.00\n\n" +
            "Subtotal: ₹975.00\n" +
            "GST (18%): ₹175.50\n" +
            "Service Charge (10%): ₹97.50\n" +
            "Total: ₹1,248.00");
    }
    
    private void addItemsToOrder(String tableNumber, String orderNumber) {
        showAlert("Add Items", "Adding items to " + orderNumber + " for Table " + tableNumber + "...\n\nRedirecting to Menu Selection...");
    }
    
    private void generateBill(String tableNumber, String orderNumber) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Generate Bill");
        alert.setHeaderText("Generate bill for Table " + tableNumber + "?");
        alert.setContentText("Order: " + orderNumber + "\nTotal Amount: ₹1,248.00\n\nThis will mark the table as free after payment.");
        
        alert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                showAlert("Bill Generated", "Bill generated successfully!\n\nTable " + tableNumber + " is now available for new customers.");
                refreshTables();
            }
        });
    }
    
    private void markOrderReady(String tableNumber, String orderNumber) {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Mark Order Ready");
        alert.setHeaderText("Mark " + orderNumber + " as ready to serve?");
        alert.setContentText("This will notify the waiter that food is ready for Table " + tableNumber + ".");
        
        alert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                showAlert("Order Ready", "Order " + orderNumber + " marked as ready!\n\nWaiter has been notified to serve Table " + tableNumber + ".");
                refreshTables();
            }
        });
    }
    

    
    @FXML
    private void goBack() {
        try {
            // Use universal navigation manager for robust back navigation
            UniversalNavigationManager.getInstance().goBack();
        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Could not go back");
        }
    }

    @FXML
    private void refreshTables() {
        System.out.println("Manual refresh triggered");
        loadTableDataAsync();
    }

    @FXML
    private void viewTableDetails() {
        // This method will be called by buttons with userData
        System.out.println("View table details method called");
    }

    @FXML
    private void addItemsToTable() {
        // This method will be called by buttons with userData
        System.out.println("Add items to table method called");
    }

    // Generic method to handle button actions with table numbers
    private void handleTableAction(javafx.event.ActionEvent event, String action) {
        Button button = (Button) event.getSource();
        String tableNumber = button.getUserData().toString();
        System.out.println(action + " for Table " + tableNumber);

        TableStatus status = tableStatuses.get(Integer.parseInt(tableNumber));

        switch (action) {
            case "VIEW_DETAILS":
                if (status != null) {
                    showTableDetailsDialog(status);
                } else {
                    showAlert("Table Details", "Table " + tableNumber + " - Available\n\nNo active orders");
                }
                break;
            case "ADD_ITEMS":
                showAlert("Add Items", "Adding items to Table " + tableNumber + "...\n\nRedirecting to Menu Selection...");
                break;
            case "SELECT_TABLE":
                selectTableForOrder(tableNumber);
                break;
        }
    }

    private void selectTableForOrder(String tableNumber) {
        System.out.println("Selecting Table " + tableNumber + " for new order");
        showAlert("Table Selected", "Table " + tableNumber + " selected for new order.\n\nRedirecting to Menu Selection...");
        // This would typically navigate to menu selection with table pre-selected
    }

    private void showTableDetailsDialog(TableStatus status) {
        StringBuilder details = new StringBuilder();
        details.append("Table ").append(status.getTableNumber()).append(" Details\n\n");
        details.append("Status: ").append(status.getDisplayStatus()).append("\n");
        details.append("Seats: ").append(status.getSeats()).append("\n");

        if (status.hasActiveOrder()) {
            details.append("Order ID: ").append(status.getOrderId()).append("\n");
            if (status.getTotalAmount() > 0) {
                details.append("Amount: ").append(status.getFormattedAmount()).append("\n");
            }
            if (status.shouldShowTiming()) {
                details.append("Duration: ").append(status.getElapsedTime()).append("\n");
            }
        }

        showAlert("Table Details", details.toString());
    }
    
    private void navigateToOrderEntry(String tableNumber) {
        try {
            // Load the MenuSelection FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/MenuSelection.fxml"));
            Parent menuSelectionView = loader.load();

            // Get the controller and set the table information
            MenuSelectionController menuController = loader.getController();
            if (menuController != null) {
                menuController.setTableInfo("Table " + tableNumber);
            }

            // Navigate to the menu selection view
            Stage stage = (Stage) tablesGrid.getScene().getWindow();
            stage.getScene().setRoot(menuSelectionView);

        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Could not open menu selection for Table " + tableNumber);
        }
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
