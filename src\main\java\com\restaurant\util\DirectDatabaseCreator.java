package com.restaurant.util; 
 
import java.sql.*; 
 
public class DirectDatabaseCreator { 
    public static void main(String[] args) { 
        System.out.println("🔧 Creating database directly with Java..."); 
        System.out.println(); 
 
        String url = "*************************"; 
 
        try (Connection conn = DriverManager.getConnection(url)) { 
            System.out.println("✅ Database connection established"); 
 
            // Create users table 
            String createUsersTable = "CREATE TABLE IF NOT EXISTS users (" + 
                "id INTEGER PRIMARY KEY AUTOINCREMENT, " + 
                "username TEXT UNIQUE NOT NULL, " + 
                "password TEXT NOT NULL, " + 
                "role TEXT NOT NULL" + 
                ")"; 
 
            try (Statement stmt = conn.createStatement()) { 
                stmt.execute(createUsersTable); 
                System.out.println("✅ Users table created"); 
            } 
 
            // Create online_orders table 
            String createOrdersTable = "CREATE TABLE IF NOT EXISTS online_orders (" + 
                "id INTEGER PRIMARY KEY AUTOINCREMENT, " + 
                "order_id TEXT UNIQUE NOT NULL, " + 
                "platform TEXT NOT NULL, " + 
                "customer_name TEXT NOT NULL, " + 
                "customer_phone TEXT, " + 
                "delivery_address TEXT, " + 
                "status TEXT NOT NULL, " + 
                "total_amount REAL NOT NULL, " + 
                "order_time TEXT NOT NULL, " + 
                "status_updated_time TEXT, " + 
                "special_instructions TEXT, " + 
                "estimated_prep_time INTEGER" + 
                ")"; 
 
            try (Statement stmt = conn.createStatement()) { 
                stmt.execute(createOrdersTable); 
                System.out.println("✅ Online orders table created"); 
            } 
 
            // Insert admin user with BCrypt hash for 'admin123' 
            String insertAdmin = "INSERT OR REPLACE INTO users (username, password, role) VALUES (?, ?, ?)"; 
            try (PreparedStatement pstmt = conn.prepareStatement(insertAdmin)) { 
                pstmt.setString(1, "admin"); 
                pstmt.setString(2, "$2a$10$N9qo8uLOickgx2ZMRZoMye.Uo0aBOBjXoXebXeQABkc4ILoLGxjvO"); 
                pstmt.setString(3, "ADMIN"); 
                pstmt.executeUpdate(); 
                System.out.println("✅ Admin user created"); 
                System.out.println("   Username: admin"); 
                System.out.println("   Password: admin123"); 
                System.out.println("   Role: ADMIN"); 
            } 
 
            // Insert staff user with BCrypt hash for 'staff123' 
            try (PreparedStatement pstmt = conn.prepareStatement(insertAdmin)) { 
                pstmt.setString(1, "staff"); 
                pstmt.setString(2, "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi."); 
                pstmt.setString(3, "STAFF"); 
                pstmt.executeUpdate(); 
                System.out.println("✅ Staff user created"); 
                System.out.println("   Username: staff"); 
                System.out.println("   Password: staff123"); 
                System.out.println("   Role: STAFF"); 
            } 
 
            // Verify users were created 
            System.out.println(); 
            System.out.println("📋 USERS IN DATABASE:"); 
            String selectUsers = "SELECT id, username, role FROM users"; 
            try (Statement stmt = conn.createStatement(); 
                 ResultSet rs = stmt.executeQuery(selectUsers)) { 
                System.out.println("ID | Username | Role"); 
                System.out.println("---|----------|-----"); 
                while (rs.next()) { 
                    System.out.printf("d | sn", 
                        rs.getInt("id"), 
                        rs.getString("username"), 
                        rs.getString("role")); 
                } 
            } 
 
            System.out.println(); 
            System.out.println("🎉 DATABASE SETUP COMPLETE!"); 
 
        } catch (Exception e) { 
            System.err.println("❌ Error: " + e.getMessage()); 
            e.printStackTrace(); 
        } 
    } 
} 
