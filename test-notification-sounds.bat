@echo off
echo TESTING ALL NOTIFICATION SOUNDS...
echo.

echo 🔊 NOTIFICATION SOUND TEST MENU 🔊
echo.

echo This will test all the different notification sounds implemented:
echo.

echo 1. 🔔 NEW ORDER SOUND (Double Beep)
echo 2. 🍽️ ORDER READY SOUND (Triple Beep)
echo 3. 🚨 URGENT ALERT SOUND (Rapid 5 Beeps)
echo 4. ❌ ERROR SOUND (Long Pause Pattern)
echo 5. ✅ SUCCESS SOUND (Single Beep)
echo 6. 🔔 PERSISTENT RINGING (Urgent Pattern)
echo 7. 🎵 ALL SOUNDS DEMO (Play all sounds in sequence)
echo.

:MENU
echo.
echo Choose a sound to test:
echo [1] New Order Sound
echo [2] Order Ready Sound  
echo [3] Urgent Alert Sound
echo [4] Error Sound
echo [5] Success Sound
echo [6] Persistent Ringing Pattern
echo [7] All Sounds Demo
echo [8] Test with Full Application
echo [9] Exit
echo.
set /p choice="Enter your choice (1-9): "

if "%choice%"=="1" goto NEW_ORDER
if "%choice%"=="2" goto ORDER_READY
if "%choice%"=="3" goto URGENT_ALERT
if "%choice%"=="4" goto ERROR_SOUND
if "%choice%"=="5" goto SUCCESS_SOUND
if "%choice%"=="6" goto PERSISTENT_RING
if "%choice%"=="7" goto ALL_SOUNDS
if "%choice%"=="8" goto FULL_APP
if "%choice%"=="9" goto EXIT

echo Invalid choice. Please try again.
goto MENU

:NEW_ORDER
echo.
echo 🔔 TESTING NEW ORDER SOUND (Double Beep)...
echo Pattern: BEEP-pause-BEEP
echo Use case: When new Swiggy/Zomato order arrives
echo.
java -cp "target/classes" com.restaurant.util.SoundTester newOrder
goto MENU

:ORDER_READY
echo.
echo 🍽️ TESTING ORDER READY SOUND (Triple Beep)...
echo Pattern: BEEP-BEEP-BEEP (rapid)
echo Use case: When food is ready for delivery
echo.
java -cp "target/classes" com.restaurant.util.SoundTester orderReady
goto MENU

:URGENT_ALERT
echo.
echo 🚨 TESTING URGENT ALERT SOUND (Rapid 5 Beeps)...
echo Pattern: BEEP-BEEP-BEEP-BEEP-BEEP (very fast)
echo Use case: For urgent notifications and errors
echo.
java -cp "target/classes" com.restaurant.util.SoundTester urgent
goto MENU

:ERROR_SOUND
echo.
echo ❌ TESTING ERROR SOUND (Long Pause Pattern)...
echo Pattern: BEEP-long pause-BEEP
echo Use case: For system errors and failures
echo.
java -cp "target/classes" com.restaurant.util.SoundTester error
goto MENU

:SUCCESS_SOUND
echo.
echo ✅ TESTING SUCCESS SOUND (Single Beep)...
echo Pattern: BEEP
echo Use case: For successful operations and status changes
echo.
java -cp "target/classes" com.restaurant.util.SoundTester success
goto MENU

:PERSISTENT_RING
echo.
echo 🔔 TESTING PERSISTENT RINGING PATTERN...
echo Pattern: BEEP-BEEP-BEEP (pause) BEEP-BEEP-BEEP
echo Use case: Continuous ringing for unaccepted orders
echo This will ring 3 times to demonstrate the pattern
echo.
java -cp "target/classes" com.restaurant.util.SoundTester persistentRing
goto MENU

:ALL_SOUNDS
echo.
echo 🎵 TESTING ALL SOUNDS IN SEQUENCE...
echo.
echo Playing all notification sounds with descriptions...
echo.

echo 1/6 - New Order Sound (Double Beep)...
java -cp "target/classes" com.restaurant.util.SoundTester newOrder
timeout /t 2 /nobreak >nul

echo 2/6 - Order Ready Sound (Triple Beep)...
java -cp "target/classes" com.restaurant.util.SoundTester orderReady
timeout /t 2 /nobreak >nul

echo 3/6 - Success Sound (Single Beep)...
java -cp "target/classes" com.restaurant.util.SoundTester success
timeout /t 2 /nobreak >nul

echo 4/6 - Urgent Alert Sound (Rapid 5 Beeps)...
java -cp "target/classes" com.restaurant.util.SoundTester urgent
timeout /t 2 /nobreak >nul

echo 5/6 - Error Sound (Long Pause Pattern)...
java -cp "target/classes" com.restaurant.util.SoundTester error
timeout /t 2 /nobreak >nul

echo 6/6 - Persistent Ringing Pattern...
java -cp "target/classes" com.restaurant.util.SoundTester persistentRing

echo.
echo All sounds demo completed!
goto MENU

:FULL_APP
echo.
echo 🚀 TESTING WITH FULL APPLICATION...
echo.
echo This will start the full restaurant application where you can test
echo all notification sounds in real scenarios:
echo.
echo 1. Login with admin/admin123
echo 2. Go to "🍽️ Finish List"
echo 3. You'll hear persistent ringing for NEW orders immediately
echo 4. Click status buttons to hear different notification sounds
echo 5. Add test orders to hear new order notifications
echo.
echo Starting full application...
echo.

java -Dprism.order=sw ^
     -Dprism.allowhidpi=false ^
     -Djavafx.animation.pulse=60 ^
     -Djava.awt.headless=false ^
     -Dsqlite.purejava=false ^
     -Xms512m ^
     -Xmx2g ^
     -XX:+UseG1GC ^
     -XX:MaxGCPauseMillis=200 ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics ^
     -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\pdfbox\2.0.27\pdfbox-2.0.27.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\fontbox\2.0.27\fontbox-2.0.27.jar;%USERPROFILE%\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar" ^
     com.restaurant.RestaurantApp

goto MENU

:EXIT
echo.
echo Exiting notification sound test...
echo.
echo NOTIFICATION SOUNDS SUMMARY:
echo.
echo 🔔 New Order: Double beep (BEEP-pause-BEEP)
echo 🍽️ Order Ready: Triple beep (BEEP-BEEP-BEEP)
echo ✅ Success: Single beep (BEEP)
echo 🚨 Urgent: Rapid 5 beeps (BEEP-BEEP-BEEP-BEEP-BEEP)
echo ❌ Error: Long pause pattern (BEEP-long pause-BEEP)
echo 🔔 Persistent Ring: Urgent pattern (BEEP-BEEP-BEEP pause BEEP-BEEP-BEEP)
echo.
echo All sounds use system beep for maximum compatibility.
echo Custom audio files can be added to src/main/resources/audio/ directory.
echo.
pause
exit
