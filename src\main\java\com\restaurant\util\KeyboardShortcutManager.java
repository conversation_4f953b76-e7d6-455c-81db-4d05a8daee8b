package com.restaurant.util;

import javafx.scene.Scene;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyCodeCombination;
import javafx.scene.input.KeyCombination;
import javafx.scene.control.Alert;
import javafx.scene.control.ButtonType;
import javafx.stage.Stage;
import java.util.HashMap;
import java.util.Map;

/**
 * Centralized keyboard shortcut manager for the restaurant POS system
 * Provides fast billing and order management shortcuts for staff efficiency
 */
public class KeyboardShortcutManager {
    
    private static KeyboardShortcutManager instance;
    private Map<KeyCombination, java.lang.Runnable> shortcuts;
    private Map<KeyCombination, String> shortcutDescriptions;
    
    private KeyboardShortcutManager() {
        shortcuts = new HashMap<>();
        shortcutDescriptions = new HashMap<>();
        initializeShortcuts();
    }
    
    public static KeyboardShortcutManager getInstance() {
        if (instance == null) {
            instance = new KeyboardShortcutManager();
        }
        return instance;
    }
    
    /**
     * Initialize all keyboard shortcuts with their descriptions
     */
    private void initializeShortcuts() {
        // Billing & Order Management Shortcuts
        addShortcut(new KeyCodeCombination(KeyCode.N, KeyCombination.CONTROL_DOWN), 
                   "New Order", () -> System.out.println("New Order shortcut triggered"));
        
        // Ctrl+H (Hold KOT) shortcut removed to prevent application freezing
        
        addShortcut(new KeyCodeCombination(KeyCode.S, KeyCombination.CONTROL_DOWN), 
                   "Settle Bill", () -> System.out.println("Settle Bill shortcut triggered"));
        
        addShortcut(new KeyCodeCombination(KeyCode.P, KeyCombination.CONTROL_DOWN), 
                   "Print KOT", () -> System.out.println("Print KOT shortcut triggered"));
        
        addShortcut(new KeyCodeCombination(KeyCode.DELETE), 
                   "Delete Item from Order", () -> System.out.println("Delete Item shortcut triggered"));
        
        addShortcut(new KeyCodeCombination(KeyCode.BACK_SPACE), 
                   "Delete Item from Order", () -> System.out.println("Delete Item shortcut triggered"));
        
        addShortcut(new KeyCodeCombination(KeyCode.D, KeyCombination.CONTROL_DOWN), 
                   "Apply Discount", () -> System.out.println("Apply Discount shortcut triggered"));
        
        addShortcut(new KeyCodeCombination(KeyCode.B, KeyCombination.CONTROL_DOWN), 
                   "Open Previous Bills", () -> System.out.println("Previous Bills shortcut triggered"));
        
        addShortcut(new KeyCodeCombination(KeyCode.R, KeyCombination.CONTROL_DOWN), 
                   "Reprint Last Bill", () -> System.out.println("Reprint Bill shortcut triggered"));
        
        addShortcut(new KeyCodeCombination(KeyCode.A, KeyCombination.CONTROL_DOWN), 
                   "Open Settings/Admin", () -> System.out.println("Settings shortcut triggered"));
        
        addShortcut(new KeyCodeCombination(KeyCode.L, KeyCombination.CONTROL_DOWN), 
                   "Log Out", () -> System.out.println("Logout shortcut triggered"));
        
        // Additional Navigation Shortcuts
        addShortcut(new KeyCodeCombination(KeyCode.F1),
                   "Show Help/Shortcuts", ShortcutHelpDialog::showShortcutsDialog);
        
        addShortcut(new KeyCodeCombination(KeyCode.ESCAPE),
                   "Cancel/Back", () -> {
                       System.out.println("ESC key detected - delegating to UniversalNavigationManager");
                       com.restaurant.util.UniversalNavigationManager.getInstance().handleEscapeKey();
                   });
        
        addShortcut(new KeyCodeCombination(KeyCode.ENTER), 
                   "Confirm/Select", () -> System.out.println("Confirm shortcut triggered"));
        
        addShortcut(new KeyCodeCombination(KeyCode.F, KeyCombination.CONTROL_DOWN),
                   "Search Menu Item", () -> System.out.println("Search shortcut triggered"));

        addShortcut(new KeyCodeCombination(KeyCode.S, KeyCombination.CONTROL_DOWN),
                   "Search", () -> System.out.println("Search shortcut triggered"));

        addShortcut(new KeyCodeCombination(KeyCode.N, KeyCombination.CONTROL_DOWN),
                   "Save Order", () -> System.out.println("Save Order shortcut triggered"));

        addShortcut(new KeyCodeCombination(KeyCode.P, KeyCombination.CONTROL_DOWN),
                   "Print KOT", () -> System.out.println("Print KOT shortcut triggered"));

        // Note: Ctrl+B removed from global shortcuts to allow MenuSelectionController to handle it
        // for Generate Bill functionality

        addShortcut(new KeyCodeCombination(KeyCode.D, KeyCombination.CONTROL_DOWN),
                   "Order Discount", () -> System.out.println("Order Discount shortcut triggered"));

        // Re-enable Ctrl+K as a global shortcut that works everywhere
        addShortcut(new KeyCodeCombination(KeyCode.K, KeyCombination.CONTROL_DOWN),
                   "Billing & KOT Management", () -> openBillingAndKOTDialog());

        addShortcut(new KeyCodeCombination(KeyCode.ENTER),
                   "Finish/Confirm", () -> System.out.println("Enter shortcut triggered"));

        addShortcut(new KeyCodeCombination(KeyCode.ENTER, KeyCombination.CONTROL_DOWN),
                   "Force Confirm", () -> System.out.println("Ctrl+Enter shortcut triggered"));

        // ESC key handling is now managed by UniversalNavigationManager
        // Removed duplicate ESC shortcut to prevent conflicts

        addShortcut(new KeyCodeCombination(KeyCode.T, KeyCombination.CONTROL_DOWN),
                   "See Tables", () -> System.out.println("See Tables shortcut triggered"));
        
        addShortcut(new KeyCodeCombination(KeyCode.M, KeyCombination.CONTROL_DOWN), 
                   "Menu Management", () -> System.out.println("Menu Management shortcut triggered"));
        
        addShortcut(new KeyCodeCombination(KeyCode.U, KeyCombination.CONTROL_DOWN), 
                   "User Management", () -> System.out.println("User Management shortcut triggered"));
        
        addShortcut(new KeyCodeCombination(KeyCode.I, KeyCombination.CONTROL_DOWN), 
                   "Inventory", () -> System.out.println("Inventory shortcut triggered"));
        
        // Function Key Shortcuts for Quick Actions
        addShortcut(new KeyCodeCombination(KeyCode.F2), 
                   "Quick Cash Payment", () -> System.out.println("Quick Cash Payment shortcut triggered"));
        
        addShortcut(new KeyCodeCombination(KeyCode.F3), 
                   "Quick Card Payment", () -> System.out.println("Quick Card Payment shortcut triggered"));
        
        addShortcut(new KeyCodeCombination(KeyCode.F4), 
                   "Quick UPI Payment", () -> System.out.println("Quick UPI Payment shortcut triggered"));
        
        addShortcut(new KeyCodeCombination(KeyCode.F5), 
                   "Refresh/Reload", () -> System.out.println("Refresh shortcut triggered"));
        
        addShortcut(new KeyCodeCombination(KeyCode.F12), 
                   "Emergency/Manager Call", () -> System.out.println("Emergency shortcut triggered"));
    }
    
    /**
     * Add a keyboard shortcut with its action and description
     */
    private void addShortcut(KeyCombination combination, String description, java.lang.Runnable action) {
        shortcuts.put(combination, action);
        shortcutDescriptions.put(combination, description);
    }
    
    /**
     * Get the global key handler for dashboard shortcuts
     */
    public javafx.event.EventHandler<javafx.scene.input.KeyEvent> getGlobalKeyHandler() {
        return event -> {
            boolean shortcutFound = false;

            // Check for known shortcuts
            for (Map.Entry<KeyCombination, java.lang.Runnable> entry : shortcuts.entrySet()) {
                if (entry.getKey().match(event)) {
                    event.consume();
                    shortcutFound = true;
                    try {
                        entry.getValue().run();
                    } catch (Exception e) {
                        System.err.println("Error executing shortcut: " + e.getMessage());
                        e.printStackTrace();
                    }
                    break;
                }
            }

            // Handle unknown Ctrl shortcuts gracefully
            if (!shortcutFound && event.isControlDown() && !event.getCode().isModifierKey()) {
                handleUnknownCtrlShortcut(event);
            }
        };
    }

    /**
     * Handle unknown Ctrl shortcuts gracefully to prevent UI freezing
     */
    private void handleUnknownCtrlShortcut(javafx.scene.input.KeyEvent event) {
        try {
            String shortcut = "Ctrl+" + event.getCode().toString();
            System.out.println("KeyboardShortcutManager: ⚠️ Unknown shortcut detected: " + shortcut);

            // Log the attempt but don't show error to user unless it's a common mistake
            if (isCommonMistake(event.getCode())) {
                javafx.application.Platform.runLater(() -> {
                    try {
                        // Show a brief, non-blocking notification for common mistakes
                        showBriefNotification("Unknown shortcut: " + shortcut + "\nPress F1 for help");
                    } catch (Exception e) {
                        System.err.println("Error showing unknown shortcut notification: " + e.getMessage());
                    }
                });
            }

            // Always consume the event to prevent further processing and potential freezing
            event.consume();

        } catch (Exception e) {
            System.err.println("Error handling unknown Ctrl shortcut: " + e.getMessage());
            e.printStackTrace();
            // Still consume the event even if error handling fails
            event.consume();
        }
    }

    /**
     * Check if the key combination is a common mistake users might make
     */
    private boolean isCommonMistake(javafx.scene.input.KeyCode keyCode) {
        // Common letters that users might accidentally press with Ctrl
        switch (keyCode) {
            case M: case N: case Q: case W: case E: case R: case Y: case U: case I: case O:
            case A: case F: case G: case J: case L: case Z: case X: case C: case V: case B:
                return true;
            default:
                return false;
        }
    }

    /**
     * Show a brief, non-blocking notification that doesn't interfere with workflow
     */
    private void showBriefNotification(String message) {
        try {
            // Create a simple, non-modal notification
            javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.INFORMATION);
            alert.setTitle("Keyboard Shortcut");
            alert.setHeaderText(null);
            alert.setContentText(message);

            // Make it non-blocking and auto-close
            alert.show();

            // Auto-close after 3 seconds
            javafx.animation.Timeline timeline = new javafx.animation.Timeline(
                new javafx.animation.KeyFrame(javafx.util.Duration.seconds(3), e -> {
                    try {
                        alert.close();
                    } catch (Exception ex) {
                        System.err.println("Error auto-closing notification: " + ex.getMessage());
                    }
                })
            );
            timeline.play();

        } catch (Exception e) {
            System.err.println("Error creating brief notification: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Register shortcuts for a specific scene
     */
    public void registerShortcuts(Scene scene) {
        if (scene == null) return;

        scene.setOnKeyPressed(getGlobalKeyHandler());

        System.out.println("Keyboard shortcuts registered for scene: " + shortcuts.size() + " shortcuts available");
    }
    
    /**
     * Update shortcut action for a specific key combination
     */
    public void updateShortcutAction(KeyCombination combination, java.lang.Runnable newAction) {
        if (shortcuts.containsKey(combination)) {
            shortcuts.put(combination, newAction);
            System.out.println("Updated shortcut action for: " + shortcutDescriptions.get(combination));
        }
    }
    
    /**
     * Show keyboard shortcuts help dialog
     */
    public void showShortcutsDialog() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("🚀 Keyboard Shortcuts – Fast Billing with Just Your Keyboard");
        alert.setHeaderText("Boost your speed and efficiency by using the following keyboard shortcuts:");
        
        StringBuilder content = new StringBuilder();
        content.append("📋 BILLING & ORDER MANAGEMENT:\n");
        content.append("Ctrl + N\t\tNew Order\n");
        content.append("Ctrl + H\t\tHold KOT\n");
        content.append("Ctrl + S\t\tSettle Bill\n");
        content.append("Ctrl + P\t\tPrint KOT\n");
        content.append("Del/Backspace\tDelete Item from Order\n");
        content.append("Ctrl + D\t\tApply Discount\n");
        content.append("Ctrl + B\t\tOpen Previous Bills\n");
        content.append("Ctrl + R\t\tReprint Last Bill\n\n");
        
        content.append("🎛️ NAVIGATION & MANAGEMENT:\n");
        content.append("Ctrl + A\t\tOpen Settings/Admin\n");
        content.append("Ctrl + L\t\tLog Out\n");
        content.append("Ctrl + F\t\tSearch Menu Item\n");
        content.append("Ctrl + S\t\tSearch\n");
        content.append("Ctrl + N\t\tSave Order\n");
        content.append("Ctrl + P\t\tPrint KOT\n");
        content.append("Ctrl + D\t\tOrder Discount\n");
        content.append("Ctrl + K\t\tBilling & KOT\n");
        content.append("Enter\t\t\tFinish/Confirm\n");
        content.append("Ctrl + Enter\t\tForce Confirm\n");
        content.append("Escape\t\t\tCancel/Back\n");
        content.append("Ctrl + T\t\tSee Tables\n");
        content.append("Ctrl + M\t\tMenu Management\n");
        content.append("Ctrl + U\t\tUser Management\n");
        content.append("Ctrl + I\t\tInventory\n\n");
        
        content.append("💳 QUICK PAYMENTS:\n");
        content.append("F2\t\t\tQuick Cash Payment\n");
        content.append("F3\t\t\tQuick Card Payment\n");
        content.append("F4\t\t\tQuick UPI Payment\n\n");
        
        content.append("🔧 SYSTEM:\n");
        content.append("F1\t\t\tShow This Help\n");
        content.append("F5\t\t\tRefresh/Reload\n");
        content.append("F12\t\t\tEmergency/Manager Call\n");
        content.append("Esc\t\t\tCancel/Back\n");
        content.append("Enter\t\t\tConfirm/Select\n\n");
        
        content.append("💡 TIPS:\n");
        content.append("• Type item name directly to search\n");
        content.append("• Type number before item (e.g., 2Burger)\n");
        content.append("• Use Tab to navigate between fields\n");
        content.append("• Use Arrow keys to navigate lists\n");
        
        alert.setContentText(content.toString());
        alert.getDialogPane().setPrefWidth(600);
        alert.getDialogPane().setPrefHeight(500);
        alert.showAndWait();
    }
    
    /**
     * Get all registered shortcuts for debugging
     */
    public Map<KeyCombination, String> getAllShortcuts() {
        return new HashMap<>(shortcutDescriptions);
    }
    
    /**
     * Enable/disable shortcuts (useful for text input fields)
     */
    public void setShortcutsEnabled(boolean enabled) {
        // Implementation for enabling/disabling shortcuts when needed
        System.out.println("Shortcuts " + (enabled ? "enabled" : "disabled"));
    }

    /**
     * Open Billing & KOT Management - Ctrl+K opens the billing interface
     */
    private void openBillingAndKOTDialog() {
        try {
            System.out.println("🎯 Ctrl+K pressed - Opening Billing & KOT Management");

            // Open the billing interface
            openBillingInterface();

        } catch (Exception e) {
            System.err.println("❌ Error opening Billing & KOT Management: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Open the integrated Billing & KOT Management interface within the dashboard
     */
    private void openBillingInterface() {
        try {
            System.out.println("✅ Opening integrated Billing & KOT Management interface...");

            // Try to get the DashboardController instance
            com.restaurant.controller.DashboardController dashboardController =
                com.restaurant.controller.DashboardController.getInstance();

            if (dashboardController != null) {
                // Call the same method that the Billing button uses
                javafx.application.Platform.runLater(() -> {
                    try {
                        dashboardController.loadBillingKOT();
                        System.out.println("✅ Successfully opened integrated Billing & KOT Management");
                    } catch (Exception e) {
                        System.err.println("❌ Error calling loadBillingKOT: " + e.getMessage());
                        e.printStackTrace();
                        openStandaloneBillingInterface();
                    }
                });
                return;
            }

            System.err.println("⚠️ DashboardController instance not found, falling back to standalone billing");
            openStandaloneBillingInterface();

        } catch (Exception e) {
            System.err.println("❌ Error opening integrated Billing & KOT Management: " + e.getMessage());
            e.printStackTrace();

            // Fallback to standalone billing interface
            openStandaloneBillingInterface();
        }
    }

    /**
     * Try to trigger the dashboard billing button programmatically
     */
    private void triggerDashboardBillingButton(javafx.stage.Stage stage) {
        try {
            // Look for the billing button in the scene and trigger it
            javafx.scene.Parent root = stage.getScene().getRoot();
            javafx.scene.Node billingButton = findNodeById(root, "billingBtn");

            if (billingButton instanceof javafx.scene.control.Button) {
                javafx.scene.control.Button btn = (javafx.scene.control.Button) billingButton;
                btn.fire(); // Programmatically click the button
                System.out.println("✅ Successfully triggered dashboard billing button");
                return;
            }

            // If button not found, fall back to standalone
            System.err.println("⚠️ Could not find billing button, falling back to standalone");
            openStandaloneBillingInterface();

        } catch (Exception e) {
            System.err.println("❌ Error triggering dashboard billing button: " + e.getMessage());
            openStandaloneBillingInterface();
        }
    }

    /**
     * Find a node by its fx:id in the scene graph
     */
    private javafx.scene.Node findNodeById(javafx.scene.Parent parent, String id) {
        if (parent.getId() != null && parent.getId().equals(id)) {
            return parent;
        }

        for (javafx.scene.Node child : parent.getChildrenUnmodifiable()) {
            if (child.getId() != null && child.getId().equals(id)) {
                return child;
            }
            if (child instanceof javafx.scene.Parent) {
                javafx.scene.Node found = findNodeById((javafx.scene.Parent) child, id);
                if (found != null) {
                    return found;
                }
            }
        }
        return null;
    }

    /**
     * Fallback: Open standalone billing interface (original behavior)
     */
    private void openStandaloneBillingInterface() {
        try {
            System.out.println("✅ Opening standalone Billing & KOT Management interface...");

            // Load the BillingKOT.fxml file
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(
                getClass().getResource("/fxml/BillingKOT.fxml")
            );
            javafx.scene.Parent billingView = loader.load();

            System.out.println("✅ BillingKOT.fxml loaded successfully");

            // Get the current stage
            javafx.stage.Stage currentStage = getCurrentActiveStage();
            if (currentStage != null) {
                // Navigate to the billing view
                currentStage.getScene().setRoot(billingView);
                currentStage.setTitle("Restaurant Management - Billing & KOT");
                System.out.println("✅ Successfully navigated to standalone Billing & KOT Management");
            } else {
                // If we can't get the current stage, create a new window
                System.out.println("⚠️ Creating new window for Billing & KOT Management");
                javafx.stage.Stage billingStage = new javafx.stage.Stage();
                billingStage.setTitle("Restaurant Management - Billing & KOT");
                billingStage.setScene(new javafx.scene.Scene(billingView));
                billingStage.setMaximized(true);
                billingStage.show();
                System.out.println("✅ Billing & KOT Management opened in new window");
            }

        } catch (Exception e) {
            System.err.println("❌ Error opening standalone Billing & KOT Management: " + e.getMessage());
            e.printStackTrace();

            // Fallback: show error message
            javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.ERROR);
            alert.setTitle("Error");
            alert.setHeaderText("Failed to open Billing & KOT Management");
            alert.setContentText("Error: " + e.getMessage());
            alert.showAndWait();
        }
    }

    /**
     * Get the current active stage
     */
    private javafx.stage.Stage getCurrentActiveStage() {
        try {
            // Get all open stages and find the focused one
            for (javafx.stage.Window window : javafx.stage.Window.getWindows()) {
                if (window instanceof javafx.stage.Stage) {
                    javafx.stage.Stage stage = (javafx.stage.Stage) window;
                    if (stage.isFocused() && stage.isShowing()) {
                        return stage;
                    }
                }
            }

            // If no focused stage, get the first showing stage
            for (javafx.stage.Window window : javafx.stage.Window.getWindows()) {
                if (window instanceof javafx.stage.Stage) {
                    javafx.stage.Stage stage = (javafx.stage.Stage) window;
                    if (stage.isShowing()) {
                        return stage;
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("Error getting current stage: " + e.getMessage());
        }
        return null;
    }

    /**
     * Get the current active stage
     */
    private javafx.stage.Stage getCurrentStage() {
        try {
            // Get all open stages
            for (javafx.stage.Window window : javafx.stage.Window.getWindows()) {
                if (window instanceof javafx.stage.Stage) {
                    javafx.stage.Stage stage = (javafx.stage.Stage) window;
                    if (stage.isFocused() || stage.isShowing()) {
                        return stage;
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("Error getting current stage: " + e.getMessage());
        }
        return null;
    }

    /**
     * Try to open table-specific billing
     */
    private boolean tryOpenTableSpecificBilling(javafx.stage.Stage stage) {
        try {
            // This is a simplified approach - in a real implementation,
            // you would need to access the actual MenuSelectionController instance
            System.out.println("Table-specific billing not yet implemented in global context");
            System.out.println("Please navigate to a table and use Ctrl+K there");

            // Show a message to the user
            javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.INFORMATION);
            alert.setTitle("Billing & KOT");
            alert.setHeaderText("Table-Specific Billing");
            alert.setContentText("To access table-specific billing and KOT management:\n\n" +
                                "1. Press Ctrl+T to view tables\n" +
                                "2. Click on a table to open its menu\n" +
                                "3. Press Ctrl+K in the table menu for detailed billing options");
            alert.showAndWait();

            return true; // We handled it (with a message)

        } catch (Exception e) {
            System.err.println("Error in tryOpenTableSpecificBilling: " + e.getMessage());
            return false;
        }
    }

    /**
     * Open the global billing dialog
     */
    private void openGlobalBillingDialog() {
        try {
            // Create billing and KOT management dialog
            javafx.scene.control.Dialog<javafx.scene.control.ButtonType> billingDialog = new javafx.scene.control.Dialog<>();
            billingDialog.setTitle("Billing & KOT Management");
            billingDialog.setHeaderText("Global Billing & KOT Management");

            // Create content for the dialog
            javafx.scene.layout.VBox content = createBillingAndKOTContent();

            billingDialog.getDialogPane().setContent(content);
            billingDialog.getDialogPane().getButtonTypes().addAll(javafx.scene.control.ButtonType.CLOSE);

            // Set dialog size
            billingDialog.getDialogPane().setPrefSize(600, 500);

            // Show dialog
            billingDialog.showAndWait();

        } catch (Exception e) {
            System.err.println("Error opening global billing dialog: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Create content for Billing and KOT dialog
     */
    private javafx.scene.layout.VBox createBillingAndKOTContent() {
        javafx.scene.layout.VBox content = new javafx.scene.layout.VBox(15);
        content.setPadding(new javafx.geometry.Insets(20));
        content.setStyle("-fx-background-color: #f8f9fa;");

        // Title
        javafx.scene.control.Label titleLabel = new javafx.scene.control.Label("🎛️ Global Billing & KOT Management");
        titleLabel.setStyle("-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        // Info message
        javafx.scene.control.Label infoLabel = new javafx.scene.control.Label(
            "This is the global Billing & KOT management interface.\n" +
            "For table-specific operations, please select a table first."
        );
        infoLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #6c757d;");
        infoLabel.setWrapText(true);

        // Quick Actions Section
        javafx.scene.control.Label actionsLabel = new javafx.scene.control.Label("⚡ Quick Actions");
        actionsLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        javafx.scene.layout.HBox actionsBox = new javafx.scene.layout.HBox(10);
        actionsBox.setAlignment(javafx.geometry.Pos.CENTER);

        javafx.scene.control.Button viewTablesBtn = new javafx.scene.control.Button("🏪 View Tables");
        viewTablesBtn.setStyle("-fx-background-color: #007bff; -fx-text-fill: white; -fx-padding: 10 20; -fx-font-size: 12px;");
        viewTablesBtn.setOnAction(e -> {
            System.out.println("View Tables clicked from global dialog");
            // Close dialog functionality will be handled by the dialog itself
        });

        javafx.scene.control.Button reportsBtn = new javafx.scene.control.Button("📊 Reports");
        reportsBtn.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; -fx-padding: 10 20; -fx-font-size: 12px;");
        reportsBtn.setOnAction(e -> {
            System.out.println("Reports clicked from global dialog");
        });

        javafx.scene.control.Button settingsBtn = new javafx.scene.control.Button("⚙️ Settings");
        settingsBtn.setStyle("-fx-background-color: #6c757d; -fx-text-fill: white; -fx-padding: 10 20; -fx-font-size: 12px;");
        settingsBtn.setOnAction(e -> {
            System.out.println("Settings clicked from global dialog");
        });

        actionsBox.getChildren().addAll(viewTablesBtn, reportsBtn, settingsBtn);

        // Instructions
        javafx.scene.control.Label instructionsLabel = new javafx.scene.control.Label("💡 Instructions");
        instructionsLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        javafx.scene.control.Label instructionsText = new javafx.scene.control.Label(
            "• Press Ctrl+T to view tables\n" +
            "• Click on a table to access table-specific billing\n" +
            "• Press Ctrl+K in table view for detailed billing options\n" +
            "• Use Ctrl+B to generate bills directly\n" +
            "• Press Escape to go back anytime"
        );
        instructionsText.setStyle("-fx-font-size: 12px; -fx-text-fill: #495057;");

        content.getChildren().addAll(
            titleLabel,
            new javafx.scene.control.Separator(),
            infoLabel,
            new javafx.scene.control.Separator(),
            actionsLabel,
            actionsBox,
            new javafx.scene.control.Separator(),
            instructionsLabel,
            instructionsText
        );

        return content;
    }
}
