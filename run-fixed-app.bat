@echo off
echo Running Restaurant Application with Fixed OrderManagement...
echo.

echo This script runs the application with the database hanging fix applied.
echo The OrderManagement should now load without hanging.
echo.

echo Compiling project...
call mvn clean compile -q

if %ERRORLEVEL% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo Compilation successful!
echo.

echo Starting application with fixed OrderDAO (bypasses DatabaseManager hanging)...
echo.
echo FIXES APPLIED:
echo ✅ Direct database connections (bypasses DatabaseManager.initializeDatabase())
echo ✅ Table existence checking before queries
echo ✅ Comprehensive error handling and logging
echo ✅ Graceful degradation if tables don't exist
echo ✅ No more hanging on OrderManagement load
echo.
echo EXPECTED BEHAVIOR:
echo - OrderManagement should load quickly
echo - Detailed logging shows each database operation step
echo - No hanging at "Starting database query..."
echo - Application remains responsive
echo.

java -Dprism.order=sw ^
     -Dprism.allowhidpi=false ^
     -Djavafx.animation.pulse=60 ^
     -Djava.awt.headless=false ^
     -Dsqlite.purejava=false ^
     -Xms512m ^
     -Xmx2g ^
     -XX:+UseG1GC ^
     -XX:MaxGCPauseMillis=200 ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics ^
     -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\pdfbox\2.0.27\pdfbox-2.0.27.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\fontbox\2.0.27\fontbox-2.0.27.jar;%USERPROFILE%\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar" ^
     com.restaurant.RestaurantApp

echo.
echo Application closed.
echo.
echo TESTING RESULTS:
echo.
if %ERRORLEVEL% equ 0 (
    echo ✅ Application exited normally
) else (
    echo ❌ Application exited with error code: %ERRORLEVEL%
)
echo.
echo WHAT TO TEST:
echo 1. Login with admin/admin123
echo 2. Click "Order Management" button
echo 3. Watch console for detailed logging:
echo    - "Creating direct database connection..."
echo    - "Direct connection created successfully"
echo    - "Checking if orders table exists..."
echo    - "OrderManagementController initialized successfully"
echo.
echo If you see all these messages, the hanging issue is FIXED!
echo.
echo If it still hangs, check the LAST message in the console output
echo and report it for further debugging.
echo.
pause
