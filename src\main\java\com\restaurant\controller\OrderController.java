package com.restaurant.controller;

import com.restaurant.model.Order;
import com.restaurant.model.OrderItem;
import com.restaurant.model.MenuItem;
import com.restaurant.util.KeyboardShortcutManager;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyCodeCombination;
import javafx.scene.input.KeyCombination;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import java.net.URL;
import java.util.ResourceBundle;

/**
 * Controller for Order Management with Keyboard Shortcuts
 * Demonstrates fast billing functionality using keyboard shortcuts
 */
public class OrderController implements Initializable {
    
    @FXML private TableView<OrderItem> orderTable;
    @FXML private TableColumn<OrderItem, String> itemNameColumn;
    @FXML private TableColumn<OrderItem, Integer> quantityColumn;
    @FXML private TableColumn<OrderItem, Double> priceColumn;
    @FXML private TableColumn<OrderItem, Double> totalColumn;
    
    @FXML private TextField searchField;
    @FXML private Label totalAmountLabel;
    @FXML private Label tableNumberLabel;
    @FXML private Button newOrderBtn;
    @FXML private Button holdKOTBtn;
    @FXML private Button printKOTBtn;
    @FXML private Button settleBillBtn;
    @FXML private Button applyDiscountBtn;
    
    private Order currentOrder;
    private ObservableList<OrderItem> orderItems;
    private KeyboardShortcutManager shortcutManager;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        initializeOrderTable();
        initializeKeyboardShortcuts();
        createNewOrder();
        
        System.out.println("OrderController initialized with keyboard shortcuts");
    }
    
    /**
     * Initialize the order table columns
     */
    private void initializeOrderTable() {
        orderItems = FXCollections.observableArrayList();
        orderTable.setItems(orderItems);
        
        // Configure table columns
        itemNameColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(
                cellData.getValue().getMenuItem().getName()));
        
        quantityColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleIntegerProperty(
                cellData.getValue().getQuantity()).asObject());
        
        priceColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleDoubleProperty(
                cellData.getValue().getPrice()).asObject());
        
        totalColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleDoubleProperty(
                cellData.getValue().getPrice() * cellData.getValue().getQuantity()).asObject());
    }
    
    /**
     * Initialize keyboard shortcuts for order management
     */
    private void initializeKeyboardShortcuts() {
        shortcutManager = KeyboardShortcutManager.getInstance();
        
        // Register shortcuts when scene is available
        Platform.runLater(() -> {
            if (orderTable != null && orderTable.getScene() != null) {
                shortcutManager.registerShortcuts(orderTable.getScene());
                setupOrderSpecificShortcuts();
                System.out.println("Order management keyboard shortcuts activated");
            }
        });
    }
    
    /**
     * Setup order-specific keyboard shortcuts
     */
    private void setupOrderSpecificShortcuts() {
        // Update shortcut actions for order management
        shortcutManager.updateShortcutAction(
            new KeyCodeCombination(KeyCode.N, KeyCombination.CONTROL_DOWN), 
            this::handleNewOrder
        );
        
        // Ctrl+H (Hold KOT) shortcut removed to prevent application freezing
        
        shortcutManager.updateShortcutAction(
            new KeyCodeCombination(KeyCode.S, KeyCombination.CONTROL_DOWN), 
            this::handleSettleBill
        );
        
        shortcutManager.updateShortcutAction(
            new KeyCodeCombination(KeyCode.P, KeyCombination.CONTROL_DOWN), 
            this::handlePrintKOT
        );
        
        shortcutManager.updateShortcutAction(
            new KeyCodeCombination(KeyCode.DELETE), 
            this::handleDeleteItem
        );
        
        shortcutManager.updateShortcutAction(
            new KeyCodeCombination(KeyCode.BACK_SPACE), 
            this::handleDeleteItem
        );
        
        shortcutManager.updateShortcutAction(
            new KeyCodeCombination(KeyCode.D, KeyCombination.CONTROL_DOWN), 
            this::handleApplyDiscount
        );
        
        shortcutManager.updateShortcutAction(
            new KeyCodeCombination(KeyCode.F, KeyCombination.CONTROL_DOWN), 
            this::handleSearchFocus
        );
    }
    
    // Keyboard Shortcut Handlers
    
    /**
     * Handle Ctrl+N - New Order
     */
    private void handleNewOrder() {
        Platform.runLater(() -> {
            createNewOrder();
            showShortcutNotification("New Order Created", "Ctrl+N - Ready for new order entry");
        });
    }
    
    // Ctrl+H (Hold KOT) handler removed to prevent application freezing
    
    /**
     * Handle Ctrl+S - Settle Bill
     */
    private void handleSettleBill() {
        Platform.runLater(() -> {
            if (currentOrder != null && !orderItems.isEmpty()) {
                double total = currentOrder.calculateTotal();
                showShortcutNotification("Bill Settlement", 
                    String.format("Ctrl+S - Total: ₹%.2f\nReady for payment processing", total));
                System.out.println("Settling bill for order: " + currentOrder.getId());
            } else {
                showShortcutNotification("No Items", "Add items to the order before settling bill");
            }
        });
    }
    
    /**
     * Handle Ctrl+P - Print KOT
     */
    private void handlePrintKOT() {
        Platform.runLater(() -> {
            if (currentOrder != null && !orderItems.isEmpty()) {
                showShortcutNotification("KOT Printed", "Ctrl+P - Kitchen Order Ticket sent to printer");
                System.out.println("Printing KOT for order: " + currentOrder.getId());
            } else {
                showShortcutNotification("No Items", "Add items to the order before printing KOT");
            }
        });
    }
    
    /**
     * Handle Delete/Backspace - Delete Item
     */
    private void handleDeleteItem() {
        Platform.runLater(() -> {
            OrderItem selectedItem = orderTable.getSelectionModel().getSelectedItem();
            if (selectedItem != null) {
                orderItems.remove(selectedItem);
                currentOrder.removeItem(selectedItem);
                updateTotalAmount();
                showShortcutNotification("Item Removed", "Del/Backspace - Item removed from order");
            } else {
                showShortcutNotification("No Selection", "Select an item to remove from the order");
            }
        });
    }
    
    /**
     * Handle Ctrl+D - Apply Discount
     */
    private void handleApplyDiscount() {
        Platform.runLater(() -> {
            if (currentOrder != null && !orderItems.isEmpty()) {
                showShortcutNotification("Discount Applied", "Ctrl+D - 10% discount applied to order");
                System.out.println("Applying discount to order: " + currentOrder.getId());
            } else {
                showShortcutNotification("No Items", "Add items to the order before applying discount");
            }
        });
    }
    
    /**
     * Handle Ctrl+F - Focus Search Field
     */
    private void handleSearchFocus() {
        Platform.runLater(() -> {
            if (searchField != null) {
                searchField.requestFocus();
                showShortcutNotification("Search Active", "Ctrl+F - Type to search menu items");
            }
        });
    }
    
    // Helper Methods
    
    /**
     * Create a new order
     */
    private void createNewOrder() {
        currentOrder = new Order();
        currentOrder.setTableNumber(5); // Example table number
        orderItems.clear();
        updateTotalAmount();
        updateTableNumber();
        
        if (searchField != null) {
            searchField.clear();
        }
    }
    
    /**
     * Update total amount display
     */
    private void updateTotalAmount() {
        if (currentOrder != null && totalAmountLabel != null) {
            double total = currentOrder.calculateTotal();
            totalAmountLabel.setText(String.format("₹%.2f", total));
        }
    }
    
    /**
     * Update table number display
     */
    private void updateTableNumber() {
        if (currentOrder != null && tableNumberLabel != null) {
            tableNumberLabel.setText("Table " + currentOrder.getTableNumber());
        }
    }
    
    /**
     * Show keyboard shortcut notification
     */
    private void showShortcutNotification(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("🚀 " + title);
        alert.setHeaderText("Keyboard Shortcut Activated");
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    // FXML Event Handlers (for button clicks)
    
    @FXML
    private void onNewOrder() {
        handleNewOrder();
    }
    
    @FXML
    private void onHoldKOT() {
        // Hold KOT functionality removed to prevent application freezing
        showShortcutNotification("Hold KOT Disabled", "Hold KOT functionality has been disabled to prevent freezing");
        System.out.println("Hold KOT button clicked - functionality disabled");
    }
    
    @FXML
    private void onPrintKOT() {
        handlePrintKOT();
    }
    
    @FXML
    private void onSettleBill() {
        handleSettleBill();
    }
    
    @FXML
    private void onApplyDiscount() {
        handleApplyDiscount();
    }
}
