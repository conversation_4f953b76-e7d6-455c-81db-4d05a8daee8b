<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.MinimalTableController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: white; -fx-padding: 20px;">
         <children>
            <Button onAction="#goBack" style="-fx-background-color: #6c757d; -fx-text-fill: white; -fx-padding: 10px 20px; -fx-background-radius: 5px;" text="← Back" />
            <Label style="-fx-font-size: 24px; -fx-font-weight: bold;" text="Table Management" />
            <Label fx:id="statusLabel" style="-fx-font-size: 14px; -fx-text-fill: #007bff; -fx-font-weight: bold;" text="Type table number + Enter to open" />
            <Region HBox.hgrow="ALWAYS" />
            <Button onAction="#addNewTable" style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-padding: 10px 20px; -fx-background-radius: 5px;" text="+ Add Table" />
            <Button onAction="#refreshTables" style="-fx-background-color: #007bff; -fx-text-fill: white; -fx-padding: 10px 20px; -fx-background-radius: 5px;" text="🔄 Refresh" />
         </children>
      </HBox>
      
      <!-- Tables Grid -->
      <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
         <content>
            <GridPane fx:id="tablesGrid" alignment="CENTER" hgap="20.0" vgap="20.0" style="-fx-padding: 30px;">
               <columnConstraints>
                  <ColumnConstraints hgrow="SOMETIMES" minWidth="200.0" prefWidth="250.0" />
                  <ColumnConstraints hgrow="SOMETIMES" minWidth="200.0" prefWidth="250.0" />
                  <ColumnConstraints hgrow="SOMETIMES" minWidth="200.0" prefWidth="250.0" />
               </columnConstraints>
               <rowConstraints>
                  <RowConstraints minHeight="200.0" prefHeight="220.0" vgrow="SOMETIMES" />
                  <RowConstraints minHeight="200.0" prefHeight="220.0" vgrow="SOMETIMES" />
                  <RowConstraints minHeight="200.0" prefHeight="220.0" vgrow="SOMETIMES" />
               </rowConstraints>
               <children>
                  <!-- Table 1 -->
                  <VBox alignment="CENTER" spacing="15.0" style="-fx-background-color: white; -fx-border-color: #28a745; -fx-border-width: 3px; -fx-border-radius: 10px; -fx-background-radius: 10px; -fx-padding: 20px; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);" GridPane.columnIndex="0" GridPane.rowIndex="0" onMouseClicked="#handleTableDoubleClick">
                     <children>
                        <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" text="Table 1" />
                        <Label style="-fx-font-size: 14px; -fx-text-fill: #6c757d;" text="👥 4 seats" />
                        <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #28a745;" text="Available" />
                        <HBox alignment="CENTER" spacing="10.0">
                           <children>
                              <Button onAction="#viewTable1" style="-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-padding: 8px 12px; -fx-background-radius: 5px;" text="👁️" />
                              <Button onAction="#selectTable1" style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-padding: 8px 12px; -fx-background-radius: 5px;" text="Select" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 2 -->
                  <VBox alignment="CENTER" spacing="15.0" style="-fx-background-color: white; -fx-border-color: #dc3545; -fx-border-width: 3px; -fx-border-radius: 10px; -fx-background-radius: 10px; -fx-padding: 20px; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);" GridPane.columnIndex="1" GridPane.rowIndex="0" onMouseClicked="#handleTableDoubleClick">
                     <children>
                        <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" text="Table 2" />
                        <Label style="-fx-font-size: 14px; -fx-text-fill: #6c757d;" text="👥 4 seats" />
                        <Label style="-fx-background-color: #f8f9fa; -fx-padding: 4px 8px; -fx-background-radius: 4px; -fx-font-size: 12px;" text="Order #1001" />
                        <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #dc3545;" text="Order Pending" />
                        <HBox alignment="CENTER" spacing="10.0">
                           <children>
                              <Button onAction="#viewTable2" style="-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-padding: 8px 12px; -fx-background-radius: 5px;" text="👁️" />
                              <Button onAction="#addToTable2" style="-fx-background-color: #ffc107; -fx-text-fill: #212529; -fx-padding: 8px 12px; -fx-background-radius: 5px;" text="+" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 3 -->
                  <VBox alignment="CENTER" spacing="15.0" style="-fx-background-color: white; -fx-border-color: #28a745; -fx-border-width: 3px; -fx-border-radius: 10px; -fx-background-radius: 10px; -fx-padding: 20px; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);" GridPane.columnIndex="2" GridPane.rowIndex="0" onMouseClicked="#handleTableDoubleClick">
                     <children>
                        <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" text="Table 3" />
                        <Label style="-fx-font-size: 14px; -fx-text-fill: #6c757d;" text="👥 4 seats" />
                        <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #28a745;" text="Available" />
                        <HBox alignment="CENTER" spacing="10.0">
                           <children>
                              <Button onAction="#viewTable3" style="-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-padding: 8px 12px; -fx-background-radius: 5px;" text="👁️" />
                              <Button onAction="#selectTable3" style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-padding: 8px 12px; -fx-background-radius: 5px;" text="Select" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 4 -->
                  <VBox alignment="CENTER" spacing="15.0" style="-fx-background-color: white; -fx-border-color: #17a2b8; -fx-border-width: 3px; -fx-border-radius: 10px; -fx-background-radius: 10px; -fx-padding: 20px; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);" GridPane.columnIndex="0" GridPane.rowIndex="1" onMouseClicked="#handleTableDoubleClick">
                     <children>
                        <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" text="Table 4" />
                        <Label style="-fx-font-size: 14px; -fx-text-fill: #6c757d;" text="👥 4 seats" />
                        <Label style="-fx-background-color: #f8f9fa; -fx-padding: 4px 8px; -fx-background-radius: 4px; -fx-font-size: 12px;" text="Order #1002" />
                        <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #17a2b8;" text="Ready to Serve" />
                        <HBox alignment="CENTER" spacing="10.0">
                           <children>
                              <Button onAction="#viewTable4" style="-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-padding: 8px 12px; -fx-background-radius: 5px;" text="👁️" />
                              <Button onAction="#addToTable4" style="-fx-background-color: #ffc107; -fx-text-fill: #212529; -fx-padding: 8px 12px; -fx-background-radius: 5px;" text="+" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 5 -->
                  <VBox alignment="CENTER" spacing="15.0" style="-fx-background-color: white; -fx-border-color: #fd7e14; -fx-border-width: 3px; -fx-border-radius: 10px; -fx-background-radius: 10px; -fx-padding: 20px; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);" GridPane.columnIndex="1" GridPane.rowIndex="1" onMouseClicked="#handleTableDoubleClick">
                     <children>
                        <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" text="Table 5" />
                        <Label style="-fx-font-size: 14px; -fx-text-fill: #6c757d;" text="👥 4 seats" />
                        <Label style="-fx-background-color: #f8f9fa; -fx-padding: 4px 8px; -fx-background-radius: 4px; -fx-font-size: 12px;" text="Order #1003" />
                        <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #fd7e14;" text="Preparing" />
                        <HBox alignment="CENTER" spacing="10.0">
                           <children>
                              <Button onAction="#viewTable5" style="-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-padding: 8px 12px; -fx-background-radius: 5px;" text="👁️" />
                              <Button onAction="#addToTable5" style="-fx-background-color: #ffc107; -fx-text-fill: #212529; -fx-padding: 8px 12px; -fx-background-radius: 5px;" text="+" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 6 -->
                  <VBox alignment="CENTER" spacing="15.0" style="-fx-background-color: white; -fx-border-color: #28a745; -fx-border-width: 3px; -fx-border-radius: 10px; -fx-background-radius: 10px; -fx-padding: 20px; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);" GridPane.columnIndex="2" GridPane.rowIndex="1" onMouseClicked="#handleTableDoubleClick">
                     <children>
                        <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" text="Table 6" />
                        <Label style="-fx-font-size: 14px; -fx-text-fill: #6c757d;" text="👥 4 seats" />
                        <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #28a745;" text="Available" />
                        <HBox alignment="CENTER" spacing="10.0">
                           <children>
                              <Button onAction="#viewTable6" style="-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-padding: 8px 12px; -fx-background-radius: 5px;" text="👁️" />
                              <Button onAction="#selectTable6" style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-padding: 8px 12px; -fx-background-radius: 5px;" text="Select" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 7 -->
                  <VBox alignment="CENTER" spacing="15.0" style="-fx-background-color: white; -fx-border-color: #6f42c1; -fx-border-width: 3px; -fx-border-radius: 10px; -fx-background-radius: 10px; -fx-padding: 20px; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);" GridPane.columnIndex="0" GridPane.rowIndex="2" onMouseClicked="#handleTableDoubleClick">
                     <children>
                        <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" text="Table 7" />
                        <Label style="-fx-font-size: 14px; -fx-text-fill: #6c757d;" text="👥 4 seats" />
                        <Label style="-fx-background-color: #f8f9fa; -fx-padding: 4px 8px; -fx-background-radius: 4px; -fx-font-size: 12px;" text="Order #1004" />
                        <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #6f42c1;" text="KOT Printed" />
                        <HBox alignment="CENTER" spacing="10.0">
                           <children>
                              <Button onAction="#viewTable7" style="-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-padding: 8px 12px; -fx-background-radius: 5px;" text="👁️" />
                              <Button onAction="#addToTable7" style="-fx-background-color: #ffc107; -fx-text-fill: #212529; -fx-padding: 8px 12px; -fx-background-radius: 5px;" text="+" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 8 -->
                  <VBox alignment="CENTER" spacing="15.0" style="-fx-background-color: white; -fx-border-color: #28a745; -fx-border-width: 3px; -fx-border-radius: 10px; -fx-background-radius: 10px; -fx-padding: 20px; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);" GridPane.columnIndex="1" GridPane.rowIndex="2" onMouseClicked="#handleTableDoubleClick">
                     <children>
                        <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" text="Table 8" />
                        <Label style="-fx-font-size: 14px; -fx-text-fill: #6c757d;" text="👥 4 seats" />
                        <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #28a745;" text="Available" />
                        <HBox alignment="CENTER" spacing="10.0">
                           <children>
                              <Button onAction="#viewTable8" style="-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-padding: 8px 12px; -fx-background-radius: 5px;" text="👁️" />
                              <Button onAction="#selectTable8" style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-padding: 8px 12px; -fx-background-radius: 5px;" text="Select" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Table 9 -->
                  <VBox alignment="CENTER" spacing="15.0" style="-fx-background-color: white; -fx-border-color: #20c997; -fx-border-width: 3px; -fx-border-radius: 10px; -fx-background-radius: 10px; -fx-padding: 20px; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);" GridPane.columnIndex="2" GridPane.rowIndex="2" onMouseClicked="#handleTableDoubleClick">
                     <children>
                        <Label style="-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" text="Table 9" />
                        <Label style="-fx-font-size: 14px; -fx-text-fill: #6c757d;" text="👥 4 seats" />
                        <Label style="-fx-background-color: #f8f9fa; -fx-padding: 4px 8px; -fx-background-radius: 4px; -fx-font-size: 12px;" text="Order #1005" />
                        <Label style="-fx-font-size: 14px; -fx-font-weight: bold; -fx-text-fill: #20c997;" text="Completed" />
                        <HBox alignment="CENTER" spacing="10.0">
                           <children>
                              <Button onAction="#viewTable9" style="-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-padding: 8px 12px; -fx-background-radius: 5px;" text="👁️" />
                              <Button onAction="#addToTable9" style="-fx-background-color: #ffc107; -fx-text-fill: #212529; -fx-padding: 8px 12px; -fx-background-radius: 5px;" text="+" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
               </children>
            </GridPane>
         </content>
      </ScrollPane>
   </children>
</VBox>
