@echo off
echo Testing OrderDAO Database Hanging Fix...
echo.

echo This script tests the OrderDAO fix that bypasses DatabaseManager hanging.
echo The OrderManagement should now load without hanging.
echo.

echo Compiling project...
call mvn clean compile -q

if %ERRORLEVEL% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo Compilation successful!
echo.

echo Starting application to test OrderDAO fix...
echo.
echo WHAT TO EXPECT:
echo 1. Application starts normally
echo 2. Login with admin/admin123
echo 3. Click "Order Management" button
echo 4. Watch console for detailed OrderDAO logging:
echo    - "OrderDAO.getTodayActiveOrders() - Creating direct database connection..."
echo    - "OrderDAO.getTodayActiveOrders() - Direct connection created successfully"
echo    - "OrderDAO.getTodayActiveOrders() - Checking if orders table exists..."
echo    - "OrderManagementController initialized successfully"
echo.
echo If you see these messages, the hanging issue is FIXED!
echo.

java -Dprism.order=sw ^
     -Dprism.allowhidpi=false ^
     -Djavafx.animation.pulse=60 ^
     -Djava.awt.headless=false ^
     -Dsqlite.purejava=false ^
     -Xms512m ^
     -Xmx2g ^
     -XX:+UseG1GC ^
     -XX:MaxGCPauseMillis=200 ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics ^
     -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\pdfbox\2.0.27\pdfbox-2.0.27.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\fontbox\2.0.27\fontbox-2.0.27.jar;%USERPROFILE%\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar" ^
     com.restaurant.RestaurantApp

echo.
echo Application closed.
echo.
echo ANALYSIS:
echo.
if %ERRORLEVEL% equ 0 (
    echo ✅ Application exited normally
) else (
    echo ❌ Application exited with error code: %ERRORLEVEL%
)
echo.
echo TESTING CHECKLIST:
echo.
echo ✅ Did the application start without errors?
echo ✅ Could you login successfully?
echo ✅ Did "Order Management" button work?
echo ✅ Did you see detailed OrderDAO logging in console?
echo ✅ Did OrderManagement load without hanging?
echo.
echo If all above are YES, then the OrderDAO hanging fix is working!
echo.
echo If OrderManagement still hangs, check the LAST message in console:
echo - If it stops at "Creating direct database connection..." → SQLite driver issue
echo - If it stops at "Checking if orders table exists..." → Database metadata issue
echo - If it stops at "Processing order X" → Order data processing issue
echo.
pause
