// Security policy file for JavaFX Restaurant Management Application
// This grants all permissions for development purposes

grant {
    // All permissions for development
    permission java.security.AllPermission;
    
    // File system permissions
    permission java.io.FilePermission "<<ALL FILES>>", "read,write,execute,delete";
    
    // Network permissions
    permission java.net.SocketPermission "*:*", "connect,listen,accept,resolve";
    
    // Runtime permissions
    permission java.lang.RuntimePermission "*";
    
    // Property permissions
    permission java.util.PropertyPermission "*", "read,write";
    
    // Reflection permissions
    permission java.lang.reflect.ReflectPermission "*";
    
    // JavaFX specific permissions
    permission java.awt.AWTPermission "*";
    
    // Database permissions
    permission java.sql.SQLPermission "*";
    
    // Audio permissions for notification sounds
    permission javax.sound.sampled.AudioPermission "*";
    
    // Management permissions
    permission java.lang.management.ManagementPermission "*";
    
    // Security permissions
    permission java.security.SecurityPermission "*";
    
    // Logging permissions
    permission java.util.logging.LoggingPermission "*";
};

// Specific grants for JavaFX modules
grant {
    permission java.lang.RuntimePermission "accessDeclaredMembers";
    permission java.lang.RuntimePermission "accessClassInPackage.*";
    permission java.lang.RuntimePermission "setContextClassLoader";
    permission java.lang.RuntimePermission "createClassLoader";
    permission java.lang.RuntimePermission "getClassLoader";
    permission java.lang.RuntimePermission "modifyThread";
    permission java.lang.RuntimePermission "modifyThreadGroup";
};

// Grants for the application's own code
grant codeBase "file:${user.dir}/-" {
    permission java.security.AllPermission;
};

// Grants for JavaFX runtime
grant codeBase "jrt:/javafx.base" {
    permission java.security.AllPermission;
};

grant codeBase "jrt:/javafx.controls" {
    permission java.security.AllPermission;
};

grant codeBase "jrt:/javafx.fxml" {
    permission java.security.AllPermission;
};

grant codeBase "jrt:/javafx.graphics" {
    permission java.security.AllPermission;
};

grant codeBase "jrt:/javafx.media" {
    permission java.security.AllPermission;
};
