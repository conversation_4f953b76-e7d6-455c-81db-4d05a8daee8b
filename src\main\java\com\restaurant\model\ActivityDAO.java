package com.restaurant.model;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Data Access Object for Activity logging and retrieval
 * Handles all database operations for activity tracking
 */
public class ActivityDAO {
    
    /**
     * Log a new activity asynchronously to prevent UI blocking
     */
    public static CompletableFuture<Boolean> logActivityAsync(String type, String description, String user) {
        return CompletableFuture.supplyAsync(() -> {
            return logActivity(type, description, user);
        });
    }
    
    /**
     * Log a new activity to the database
     */
    public static boolean logActivity(String type, String description, String user) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "INSERT INTO activities (type, description, user, timestamp) VALUES (?, ?, ?, ?)")) {
            
            ps.setString(1, type);
            ps.setString(2, description);
            ps.setString(3, user);
            ps.setTimestamp(4, Timestamp.valueOf(LocalDateTime.now()));
            
            return ps.executeUpdate() > 0;
            
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Get recent activities asynchronously to prevent UI blocking
     */
    public static CompletableFuture<List<Activity>> getRecentActivitiesAsync(int limit) {
        return CompletableFuture.supplyAsync(() -> {
            return getRecentActivities(limit);
        });
    }
    
    /**
     * Get recent activities from database with improved error handling
     */
    public static List<Activity> getRecentActivities(int limit) {
        List<Activity> activities = new ArrayList<>();

        // Validate input
        if (limit <= 0) {
            limit = 10; // Default limit
        }

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        try {
            conn = DatabaseManager.getConnection();

            // Test connection before using it
            if (conn == null || conn.isClosed()) {
                System.err.println("Database connection is null or closed");
                return getSampleActivities(limit);
            }

            ps = conn.prepareStatement(
                "SELECT id, type, description, user, timestamp FROM activities ORDER BY timestamp DESC LIMIT ?");
            ps.setInt(1, limit);

            rs = ps.executeQuery();

            while (rs.next()) {
                try {
                    Activity activity = new Activity(
                        rs.getInt("id"),
                        rs.getString("type"),
                        rs.getString("description"),
                        rs.getString("user"),
                        rs.getTimestamp("timestamp").toLocalDateTime()
                    );
                    activities.add(activity);
                } catch (Exception e) {
                    System.err.println("Error parsing activity row: " + e.getMessage());
                    // Continue with next row instead of failing completely
                }
            }

            System.out.println("Successfully loaded " + activities.size() + " activities from database");

        } catch (SQLException e) {
            System.err.println("Database error in getRecentActivities: " + e.getMessage());
            e.printStackTrace();
            // Return sample data if database fails
            return getSampleActivities(limit);
        } catch (Exception e) {
            System.err.println("Unexpected error in getRecentActivities: " + e.getMessage());
            e.printStackTrace();
            return getSampleActivities(limit);
        } finally {
            // Clean up resources in reverse order
            try {
                if (rs != null) rs.close();
            } catch (SQLException e) {
                System.err.println("Error closing ResultSet: " + e.getMessage());
            }
            try {
                if (ps != null) ps.close();
            } catch (SQLException e) {
                System.err.println("Error closing PreparedStatement: " + e.getMessage());
            }
            // Note: Don't close the connection here as it's managed by DatabaseManager
        }

        // If no activities found, return sample data
        if (activities.isEmpty()) {
            System.out.println("No activities found in database, returning sample data");
            return getSampleActivities(limit);
        }

        return activities;
    }
    
    /**
     * Get activities for a specific date range
     */
    public static List<Activity> getActivitiesByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        List<Activity> activities = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT * FROM activities WHERE timestamp BETWEEN ? AND ? ORDER BY timestamp DESC")) {
            
            ps.setTimestamp(1, Timestamp.valueOf(startDate));
            ps.setTimestamp(2, Timestamp.valueOf(endDate));
            ResultSet rs = ps.executeQuery();
            
            while (rs.next()) {
                Activity activity = new Activity(
                    rs.getInt("id"),
                    rs.getString("type"),
                    rs.getString("description"),
                    rs.getString("user"),
                    rs.getTimestamp("timestamp").toLocalDateTime()
                );
                activities.add(activity);
            }
            
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return activities;
    }
    
    /**
     * Get activities by type
     */
    public static List<Activity> getActivitiesByType(String type, int limit) {
        List<Activity> activities = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT * FROM activities WHERE type = ? ORDER BY timestamp DESC LIMIT ?")) {
            
            ps.setString(1, type);
            ps.setInt(2, limit);
            ResultSet rs = ps.executeQuery();
            
            while (rs.next()) {
                Activity activity = new Activity(
                    rs.getInt("id"),
                    rs.getString("type"),
                    rs.getString("description"),
                    rs.getString("user"),
                    rs.getTimestamp("timestamp").toLocalDateTime()
                );
                activities.add(activity);
            }
            
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return activities;
    }
    
    /**
     * Clear old activities (older than specified days)
     */
    public static boolean clearOldActivities(int daysToKeep) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "DELETE FROM activities WHERE timestamp < ?")) {
            
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysToKeep);
            ps.setTimestamp(1, Timestamp.valueOf(cutoffDate));
            
            int deletedRows = ps.executeUpdate();
            System.out.println("Cleared " + deletedRows + " old activity records");
            return true;
            
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Get sample activities for fallback when database is not available
     */
    private static List<Activity> getSampleActivities(int limit) {
        List<Activity> activities = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        activities.add(new Activity(1, "Order", "New order #1025 created for Table 5", "John Doe", now.minusMinutes(5)));
        activities.add(new Activity(2, "Payment", "Order #1024 paid ₹850.00", "Admin", now.minusMinutes(10)));
        activities.add(new Activity(3, "Menu", "Added new item: Chicken Biryani", "Admin", now.minusMinutes(15)));
        activities.add(new Activity(4, "User", "New staff member added: Jane Smith", "Admin", now.minusMinutes(20)));
        activities.add(new Activity(5, "Order", "Order #1023 completed for Table 3", "Kitchen Staff", now.minusMinutes(25)));
        activities.add(new Activity(6, "Inventory", "Stock updated: Chicken - 50kg added", "Manager", now.minusMinutes(30)));
        activities.add(new Activity(7, "Table", "Table 7 status changed to Available", "Waiter", now.minusMinutes(35)));
        activities.add(new Activity(8, "System", "Daily backup completed successfully", "System", now.minusMinutes(40)));
        
        return activities.subList(0, Math.min(limit, activities.size()));
    }
    
    /**
     * Initialize activities table if it doesn't exist
     */
    public static void initializeActivitiesTable() {
        try (Connection conn = DatabaseManager.getConnection();
             Statement stmt = conn.createStatement()) {
            
            stmt.execute("CREATE TABLE IF NOT EXISTS activities (" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    "type TEXT NOT NULL," +
                    "description TEXT NOT NULL," +
                    "user TEXT NOT NULL," +
                    "timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP)");
            
            // Create index for better performance
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_activities_timestamp ON activities(timestamp DESC)");
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_activities_type ON activities(type)");
            
            System.out.println("Activities table initialized successfully");
            
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
    
    /**
     * Insert sample activities for demonstration
     */
    public static void insertSampleActivities() {
        List<Activity> sampleActivities = getSampleActivities(10);
        
        for (Activity activity : sampleActivities) {
            logActivity(activity.getType(), activity.getDescription(), activity.getUser());
        }
        
        System.out.println("Sample activities inserted successfully");
    }
}
