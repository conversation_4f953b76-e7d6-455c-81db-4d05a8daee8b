package com.restaurant.util;

import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.Parent;
import javafx.stage.Stage;

/**
 * Launcher for Category Item Manager interface
 * This can be used to test the category item manager independently
 */
public class CategoryItemManagerLauncher extends Application {

    @Override
    public void start(Stage primaryStage) {
        try {
            // Load the FXML file
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/CategoryItemManager.fxml"));
            Parent root = loader.load();
            
            // Create scene
            Scene scene = new Scene(root, 1000, 700);
            
            // Set up stage
            primaryStage.setTitle("Category Item Manager - Restaurant Management");
            primaryStage.setScene(scene);
            primaryStage.setMinWidth(900);
            primaryStage.setMinHeight(600);
            primaryStage.show();
            
            System.out.println("Category Item Manager interface launched successfully");
            
        } catch (Exception e) {
            System.err.println("Error launching Category Item Manager: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        launch(args);
    }
}
