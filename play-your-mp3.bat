@echo off
echo 🎵 PLAYING YOUR MP3 FILE FOR SWIGGY & ZOMATO NOTIFICATIONS 🎵
echo.

echo You have: mixkit-urgent-simple-tone-loop-2976.mp3
echo This will be used for both <PERSON><PERSON><PERSON> and <PERSON>omato notifications.
echo.

echo 🔧 CREATING SIMPLE MP3 PLAYER...

echo import javafx.application.Application; > src\main\java\com\restaurant\util\SimpleMP3Player.java
echo import javafx.scene.media.Media; >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo import javafx.scene.media.MediaPlayer; >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo import javafx.stage.Stage; >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo import java.io.File; >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo. >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo public class SimpleMP3Player extends Application { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo. >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo     @Override >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo     public void start(Stage primaryStage) { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo         System.out.println("🎵 Testing your MP3 file..."); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo         playMP3(); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo     } >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo. >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo     private void playMP3() { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo         try { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             File audioFile = new File("sounds/mixkit-urgent-simple-tone-loop-2976.mp3"); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             if (!audioFile.exists()) { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                 System.out.println("❌ MP3 file not found: " + audioFile.getAbsolutePath()); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                 System.exit(1); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                 return; >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             } >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo. >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             System.out.println("✅ Found MP3 file: " + audioFile.getAbsolutePath()); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             System.out.println("🟠 Playing as Swiggy notification..."); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo. >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             Media media = new Media(audioFile.toURI().toString()); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             MediaPlayer player = new MediaPlayer(media); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             player.setVolume(1.0); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo. >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             player.setOnReady(new Runnable() { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                 public void run() { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                     System.out.println("🎵 Playing Swiggy notification sound..."); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                     player.play(); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                 } >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             }); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo. >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             player.setOnEndOfMedia(new Runnable() { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                 public void run() { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                     System.out.println("✅ Swiggy sound finished"); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                     player.dispose(); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                     playZomatoSound(); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                 } >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             }); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo. >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             player.setOnError(new Runnable() { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                 public void run() { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                     System.out.println("❌ Error playing MP3: " + player.getError()); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                     System.exit(1); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                 } >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             }); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo. >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             System.out.println("❌ Exception: " + e.getMessage()); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             e.printStackTrace(); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo         } >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo     } >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo. >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo     private void playZomatoSound() { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo         try { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             File audioFile = new File("sounds/mixkit-urgent-simple-tone-loop-2976.mp3"); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             System.out.println("🔴 Playing as Zomato notification..."); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo. >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             Media media = new Media(audioFile.toURI().toString()); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             MediaPlayer player = new MediaPlayer(media); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             player.setVolume(1.0); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo. >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             player.setOnReady(new Runnable() { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                 public void run() { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                     System.out.println("🎵 Playing Zomato notification sound..."); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                     player.play(); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                 } >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             }); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo. >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             player.setOnEndOfMedia(new Runnable() { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                 public void run() { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                     System.out.println("✅ Zomato sound finished"); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                     System.out.println("🎉 MP3 notification test complete!"); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                     player.dispose(); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                     System.exit(0); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                 } >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             }); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo. >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             player.setOnError(new Runnable() { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                 public void run() { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                     System.out.println("❌ Error playing Zomato MP3: " + player.getError()); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                     System.exit(1); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo                 } >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             }); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo. >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             System.out.println("❌ Exception in Zomato sound: " + e.getMessage()); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo             e.printStackTrace(); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo         } >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo     } >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo. >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo     public static void main(String[] args) { >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo         launch(args); >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo     } >> src\main\java\com\restaurant\util\SimpleMP3Player.java
echo } >> src\main\java\com\restaurant\util\SimpleMP3Player.java

echo ✅ SimpleMP3Player created
echo.

echo 🔧 COMPILING...
mvn compile -q

if %ERRORLEVEL% neq 0 (
    echo ❌ Compilation failed
    pause
    exit /b 1
)

echo ✅ Compilation successful
echo.

echo 🎵 PLAYING YOUR MP3 FILE...
echo.
echo You will hear:
echo 1. 🟠 Your MP3 as Swiggy notification
echo 2. 🔴 Your MP3 as Zomato notification
echo.

java -Dprism.order=sw ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2-win.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics,javafx.media ^
     -cp "target/classes" ^
     com.restaurant.util.SimpleMP3Player

echo.
echo 🎉 MP3 NOTIFICATION TEST COMPLETE!
echo.
echo WHAT YOU HEARD:
echo 🟠 Swiggy notification: Your mixkit-urgent-simple-tone-loop-2976.mp3 file
echo 🔴 Zomato notification: Same MP3 file (since you don't have separate files)
echo.
echo 🎯 RESULTS:
echo ✅ MP3 playback system is working
echo ✅ Your audio file can be used for notifications
echo ✅ Both Swiggy and Zomato will use this sound
echo ✅ Volume is loud and clear for restaurant use
echo.
echo 📁 TO GET DIFFERENT SOUNDS:
echo 1. Add sounds\swiggy-notification.mp3 for custom Swiggy sound
echo 2. Add sounds\zomato-notification.mp3 for custom Zomato sound
echo 3. System will automatically use them when available
echo.
echo Your MP3 notification system is ready! 🎵🔊
echo.

pause
