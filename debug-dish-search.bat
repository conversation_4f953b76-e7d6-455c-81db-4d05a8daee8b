@echo off
echo DEBUG: TESTING DISH SEARCH FUNCTIONALITY...
echo.

echo DEBUG MODE ENABLED:
echo ✅ Added extensive console logging
echo ✅ Search field initialization debugging
echo ✅ Text change listener debugging
echo ✅ Search results area debugging
echo ✅ Sample data fallback enabled
echo ✅ Error handling and null checks
echo.

echo WHAT TO LOOK FOR:
echo 🔍 Console messages starting with "Setting up search functionality..."
echo 🔍 "Search text changed:" messages when typing
echo 🔍 "autoFilterDishes called with:" messages
echo 🔍 "Search results area hidden/shown" messages
echo 🔍 Any ERROR messages about null components
echo.

echo Starting application in DEBUG MODE...
echo.
echo DEBUGGING INSTRUCTIONS:
echo.
echo 1. LOGIN: Use admin/admin123
echo 2. NAVIGATE: Click on "Table Management"
echo 3. WATCH CONSOLE: Look for initialization messages
echo 4. LOCATE SEARCH FIELD: Should say "Type to search dishes..."
echo 5. TEST TYPING: Type "ch" slowly and watch console
echo 6. CHECK RESULTS: Look for search results area appearing
echo.
echo CONSOLE MESSAGES TO EXPECT:
echo ✅ "Setting up search functionality..."
echo ✅ "Real-time dish search functionality setup complete"
echo ✅ "Search results area initialized and hidden"
echo ✅ "Search text changed: '' -> 'c'" (when typing)
echo ✅ "autoFilterDishes called with: 'ch'"
echo ✅ "Search results area shown" (when results appear)
echo.
echo ERROR MESSAGES TO WATCH FOR:
echo ❌ "ERROR: searchField is null!"
echo ❌ "ERROR: searchResultsArea is null!"
echo ❌ "ERROR: searchResultsContainer is null!"
echo.

java -Dprism.order=sw ^
     -Dprism.allowhidpi=false ^
     -Djavafx.animation.pulse=60 ^
     -Djava.awt.headless=false ^
     -Dsqlite.purejava=false ^
     -Xms512m ^
     -Xmx2g ^
     -XX:+UseG1GC ^
     -XX:MaxGCPauseMillis=200 ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics ^
     -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\pdfbox\2.0.27\pdfbox-2.0.27.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\fontbox\2.0.27\fontbox-2.0.27.jar;%USERPROFILE%\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar" ^
     com.restaurant.RestaurantApp

echo.
echo DEBUG ANALYSIS:
echo.

if %ERRORLEVEL% equ 0 (
    echo ✅ APPLICATION CLOSED NORMALLY
    echo.
    echo PLEASE ANALYZE THE CONSOLE OUTPUT ABOVE:
    echo.
    echo 1. INITIALIZATION CHECK:
    echo    ✅ Did you see "Setting up search functionality..."?
    echo    ✅ Did you see "Real-time dish search functionality setup complete"?
    echo    ✅ Did you see "Search results area initialized and hidden"?
    echo.
    echo 2. SEARCH FIELD CHECK:
    echo    ✅ Was the search field visible in Table Management?
    echo    ✅ Did it have placeholder "Type to search dishes..."?
    echo    ✅ Could you click and type in it?
    echo.
    echo 3. TYPING CHECK:
    echo    ✅ Did you see "Search text changed:" messages when typing?
    echo    ✅ Did you see "autoFilterDishes called with:" messages?
    echo    ✅ Did search results appear after typing 2+ characters?
    echo.
    echo 4. ERROR CHECK:
    echo    ❌ Did you see any "ERROR: ... is null!" messages?
    echo    ❌ Did you see any exception stack traces?
    echo.
    echo COMMON ISSUES AND SOLUTIONS:
    echo.
    echo ISSUE 1: "ERROR: searchField is null!"
    echo SOLUTION: FXML fx:id="searchField" not properly linked
    echo.
    echo ISSUE 2: "ERROR: searchResultsArea is null!"
    echo SOLUTION: FXML fx:id="searchResultsArea" not properly linked
    echo.
    echo ISSUE 3: No "Search text changed:" messages
    echo SOLUTION: Text listener not working, check FXML binding
    echo.
    echo ISSUE 4: Search field not visible
    echo SOLUTION: Check FXML layout, search field might be hidden
    echo.
    echo ISSUE 5: Results not appearing
    echo SOLUTION: Check displaySearchResults method, might have UI issues
    echo.
) else (
    echo ❌ APPLICATION HAD ERRORS
    echo.
    echo Error code: %ERRORLEVEL%
    echo.
    echo POSSIBLE CAUSES:
    echo 1. Compilation errors
    echo 2. FXML loading errors
    echo 3. Missing dependencies
    echo 4. JavaFX runtime issues
    echo.
)

echo.
echo NEXT STEPS BASED ON DEBUG OUTPUT:
echo.
echo IF YOU SAW INITIALIZATION MESSAGES:
echo ✅ Search functionality is being set up correctly
echo ✅ Check if search field is visible and responsive
echo.
echo IF YOU SAW "ERROR: ... is null!" MESSAGES:
echo ❌ FXML binding issue - fx:id attributes not matching
echo ❌ Need to check FXML file for correct fx:id values
echo.
echo IF YOU SAW NO CONSOLE MESSAGES:
echo ❌ Table Management not loading properly
echo ❌ Controller not being initialized
echo.
echo IF SEARCH FIELD NOT VISIBLE:
echo ❌ FXML layout issue
echo ❌ Search field might be hidden or not properly positioned
echo.
echo MANUAL VERIFICATION STEPS:
echo 1. Go to Table Management
echo 2. Look for search field in header area
echo 3. Try typing "chicken" slowly
echo 4. Watch console for debug messages
echo 5. Check if results area appears below search field
echo.
pause
