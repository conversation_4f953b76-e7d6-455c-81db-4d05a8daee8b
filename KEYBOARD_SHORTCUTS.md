# 🎛️ Restaurant Management System - Keyboard Shortcuts Guide

## 📋 **QUICK REFERENCE CARD**

### 🍽️ **ORDER MANAGEMENT**
| Shortcut | Action | Description |
|----------|--------|-------------|
| `Ctrl + N` | New Order | Create a new order |
| `Ctrl + H` | Hold KOT | Hold current Kitchen Order Ticket |
| `Ctrl + P` | Print KOT | Print Kitchen Order Ticket |
| `Ctrl + S` | Settle Bill | Process bill payment |
| `Ctrl + D` | Apply Discount | Apply discount to order/item |
| `Delete` | Delete Item | Remove selected item from order |
| `Backspace` | Delete Item | Remove selected item from order |

### 💳 **PAYMENT SHORTCUTS**
| Shortcut | Action | Description |
|----------|--------|-------------|
| `F2` | Quick Cash Payment | Process cash payment |
| `F3` | Quick Card Payment | Process card payment |
| `F4` | Quick UPI Payment | Process UPI payment |

### 🧭 **NAVIGATION & SEARCH**
| Shortcut | Action | Description |
|----------|--------|-------------|
| `Ctrl + F` | Search Menu Item | Focus on search field |
| `Ctrl + S` | Search | Search menu items |
| `Ctrl + N` | Save Order | Save current order |
| `Ctrl + P` | Print KOT | Print Kitchen Order Ticket |
| `Ctrl + B` | Generate Bill | Generate and print bill |
| `Ctrl + D` | Order Discount | Apply discount to entire order |
| `Ctrl + K` | Billing & KOT | Open billing and KOT management |
| `Ctrl + T` | See Tables | Open table management with keyboard navigation |
| `F1` | Show Help | Display shortcuts guide |
| `F5` | Refresh/Reload | Refresh current view |
| `Escape` | Cancel/Back | Cancel action or go back |
| `Enter` | Finish/Confirm | Complete current task or confirm action |
| `Ctrl + Enter` | Force Confirm | Force confirmation for critical actions |
| `Enter` | Confirm/Select | Confirm current action |

### 🔧 **MANAGEMENT FUNCTIONS**
| Shortcut | Action | Description |
|----------|--------|-------------|
| `Ctrl + M` | Menu Management | Open menu management |
| `Ctrl + U` | User Management | Open user management |
| `Ctrl + I` | Inventory | Open inventory management |
| `Ctrl + A` | Settings/Admin | Open admin settings |
| `Ctrl + L` | Log Out | Log out of system |

### 📊 **TABLE MANAGEMENT**
| Shortcut | Action | Description |
|----------|--------|-------------|
| `1-9` | Quick Table Access | Direct access to tables 1-9 |
| `0` | Table 10 | Access table 10 |
| `Ctrl + T` | See Tables | Open table management view |

### 📋 **IN TABLE MANAGEMENT VIEW**
| Shortcut | Action | Description |
|----------|--------|-------------|
| `1-9, 0` | Type Table Number | Type any table number |
| `Enter` | Open Table | Open selected table menu |
| `Backspace` | Delete Digit | Remove last typed digit |
| `Escape` | Clear Selection | Clear current selection |
| `Ctrl + S` | Search Tables | Filter tables by number or status |

### 🔍 **IN SEARCH MODE (Table Management)**
| Shortcut | Action | Description |
|----------|--------|-------------|
| `Type text` | Filter Tables | Real-time table filtering |
| `Enter` | Select First | Open first visible table |
| `Escape` | Close Search | Exit search mode |
| `Ctrl + S` | Toggle Search | Close search overlay |

### 🍽️ **IN MENU SELECTION VIEW**
| Shortcut | Action | Description |
|----------|--------|-------------|
| `Ctrl + S` | Search | Focus search field |
| `Ctrl + F` | Focus Search | Alternative search shortcut |
| `Ctrl + N` | Save Order | Save current order |
| `Ctrl + P` | Print KOT | Print Kitchen Order Ticket |
| `Ctrl + B` | Generate Bill | Generate and print bill |
| `Ctrl + D` | Order Discount | Apply discount to entire order |
| `Ctrl + K` | Billing & KOT | Open billing and KOT management |
| `Escape` | Go Back | Return to previous view |

### 🔍 **IN MENU SEARCH MODE**
| Shortcut | Action | Description |
|----------|--------|-------------|
| `Type text` | Filter Items | Real-time menu item filtering |
| `↓ Down Arrow` | Next Item | Navigate to next search result |
| `↑ Up Arrow` | Previous Item | Navigate to previous search result |
| `→ Right Arrow` | Next Item | Navigate to next search result |
| `← Left Arrow` | Previous Item | Navigate to previous search result |
| `Enter` | Add Item | Add selected item to order (search stays open) |
| `Escape` | Clear Search | Clear search and exit navigation |



---

## 📱 **CONTEXT-SPECIFIC SHORTCUTS**

### 🍽️ **Menu Selection View**
- **Single Click**: Add item to order
- **Double Click**: Open discount options
- **💰 Button**: Apply discount to item
- **🗑 Button**: Remove item from order

### 📋 **Order Details**
- **+/-**: Adjust item quantity
- **💰**: Apply item discount
- **🗑**: Delete item
- **💰 Apply Discount**: Order-level discount

### 🏪 **Table View**
- **Delivery Toggle**: Switch to delivery mode
- **Pick Up Toggle**: Switch to pickup mode
- **+ Add Table**: Add new table to system

---

## 🎯 **SMART AI ASSISTANT SHORTCUTS**

### 💬 **Voice & Chat Controls**
| Shortcut | Action | Description |
|----------|--------|-------------|
| `Enter` | Send Message | Send chat message |
| `Escape` | Hide Suggestions | Hide auto-suggestions |
| `Down Arrow` | Navigate Suggestions | Move through suggestions |

### 🎤 **Voice Commands**
- **"Hey Restaurant"** - Wake word activation
- **"New order"** - Create new order
- **"Print KOT"** - Print kitchen ticket
- **"Show tables"** - Display table view
- **"Apply discount"** - Open discount dialog

---

## 🚀 **POWER USER TIPS**

### ⚡ **Speed Billing Workflow**
1. `Ctrl + N` → New Order
2. `Ctrl + F` → Search items
3. `Ctrl + D` → Apply discounts
4. `Ctrl + P` → Print KOT
5. `F2/F3/F4` → Quick payment
6. `Ctrl + S` → Settle bill

### 🎯 **Table Management Workflow**
1. `1-9` → Quick table access
2. **Double-click table** → Open menu
3. **Add items** → Build order
4. `Ctrl + P` → Print KOT
5. `Ctrl + S` → Process payment

### 📊 **Management Workflow**
1. `Ctrl + M` → Menu management
2. `Ctrl + I` → Check inventory
3. `Ctrl + U` → Manage users
4. `Ctrl + A` → System settings

---

## 🔧 **SYSTEM SHORTCUTS**

### 🖥️ **General System**
| Shortcut | Action | Description |
|----------|--------|-------------|
| `Alt + Tab` | Switch Windows | Switch between app windows |
| `Ctrl + Alt + Del` | Task Manager | System task manager |
| `Windows + L` | Lock Screen | Lock workstation |

### 🔄 **Application Control**
| Shortcut | Action | Description |
|----------|--------|-------------|
| `Ctrl + Q` | Quit Application | Close application |
| `Ctrl + R` | Restart | Restart application |
| `F11` | Full Screen | Toggle full screen mode |

---

## 📝 **CUSTOMIZATION**

### ⚙️ **Custom Shortcuts**
- Shortcuts can be customized in **Settings → Keyboard Shortcuts**
- Create custom shortcuts for frequently used actions
- Assign shortcuts to specific menu items
- Set up user-specific shortcut profiles

### 🎨 **Accessibility**
- **High Contrast Mode**: `Alt + Left Shift + Print Screen`
- **Magnifier**: `Windows + Plus`
- **Narrator**: `Windows + Ctrl + Enter`
- **On-Screen Keyboard**: `Windows + Ctrl + O`

---

## 🆘 **EMERGENCY SHORTCUTS**

### 🚨 **Critical Actions**
| Shortcut | Action | Description |
|----------|--------|-------------|
| `Ctrl + Alt + R` | Emergency Reset | Reset current operation |
| `Ctrl + Shift + Esc` | Force Close | Force close application |
| `F12` | Debug Mode | Open debug console |

---

## 📚 **LEARNING RESOURCES**

### 🎓 **Training Tips**
1. **Practice Mode**: Use `F1` to access practice shortcuts
2. **Tooltip Hints**: Hover over buttons to see shortcuts
3. **Status Bar**: Shows active shortcuts in bottom bar
4. **Help Menu**: Access full documentation

### 📖 **Documentation**
- **User Manual**: Complete system documentation
- **Video Tutorials**: Step-by-step video guides
- **Quick Start Guide**: Essential shortcuts for new users
- **Advanced Features**: Power user shortcuts and tips

---

## 🔄 **UPDATES & MAINTENANCE**

### 📅 **Regular Updates**
- Shortcuts are updated with each system release
- New features automatically get keyboard shortcuts
- Custom shortcuts are preserved during updates
- Backup shortcut configurations regularly

---

**💡 Pro Tip**: Press `F1` anytime to see context-sensitive shortcuts for your current screen!

**🎯 Remember**: Consistent use of keyboard shortcuts can increase your efficiency by up to 40% in daily operations!
