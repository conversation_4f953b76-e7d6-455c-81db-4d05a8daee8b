package com.restaurant.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class Order {
    private int id;
    private Integer tableNumber;
    private boolean isTakeaway;
    private String status;
    private LocalDateTime timestamp;
    private List<OrderItem> items;

    // Additional fields for customer history
    private int customerId;
    private LocalDateTime orderDate;
    private String orderType;
    private String paymentMethod;
    private String itemName;
    private String outlet;
    private double totalAmount;
    private String notes;
    
    public Order() {
        this.items = new ArrayList<>();
        this.status = "NEW";
        this.timestamp = LocalDateTime.now();
    }

    public Order(int id, Integer tableNumber, boolean isTakeaway, String status, LocalDateTime timestamp) {
        this.id = id;
        this.tableNumber = tableNumber;
        this.isTakeaway = isTakeaway;
        this.status = status;
        this.timestamp = timestamp;
        this.items = new ArrayList<>();
    }

    public double calculateTotal() {
        if (items == null || items.isEmpty()) {
            return 0.0;
        }
        return items.stream()
                .filter(item -> item != null)
                .mapToDouble(item -> item.getPrice() * item.getQuantity())
                .sum();
    }

    public double calculateSubtotal() {
        return calculateTotal();
    }

    public double calculateGST() {
        return calculateSubtotal() * 0.18; // 18% GST
    }

    public double calculateServiceCharge() {
        return calculateSubtotal() * 0.10; // 10% Service Charge
    }

    public double calculateGrandTotal() {
        return calculateSubtotal() + calculateGST() + calculateServiceCharge();
    }

    public void addItem(OrderItem item) {
        if (item == null || item.getMenuItem() == null) {
            return;
        }

        // Check if item already exists, if so, increase quantity
        for (OrderItem existingItem : items) {
            if (existingItem != null && existingItem.getMenuItem() != null &&
                existingItem.getMenuItem().getId() == item.getMenuItem().getId()) {
                existingItem.setQuantity(existingItem.getQuantity() + item.getQuantity());
                return;
            }
        }
        items.add(item);
    }

    public void removeItem(OrderItem item) {
        items.remove(item);
    }

    // Getters and setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public Integer getTableNumber() { return tableNumber; }
    public void setTableNumber(Integer tableNumber) { this.tableNumber = tableNumber; }

    public boolean isTakeaway() { return isTakeaway; }
    public void setTakeaway(boolean takeaway) { isTakeaway = takeaway; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public LocalDateTime getTimestamp() { return timestamp; }
    public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }

    public List<OrderItem> getItems() { return items; }
    public void setItems(List<OrderItem> items) { this.items = items; }

    // Additional methods for Hold KOT functionality

    /**
     * Convert order to JSON string for storage
     */
    public String toJsonString() {
        StringBuilder json = new StringBuilder();
        json.append("{");
        json.append("\"id\":").append(id).append(",");
        json.append("\"tableNumber\":").append(tableNumber).append(",");
        json.append("\"isTakeaway\":").append(isTakeaway).append(",");
        json.append("\"status\":\"").append(status).append("\",");
        json.append("\"timestamp\":\"").append(timestamp).append("\",");
        json.append("\"items\":[");

        if (items != null && !items.isEmpty()) {
            for (int i = 0; i < items.size(); i++) {
                OrderItem item = items.get(i);
                if (i > 0) json.append(",");
                json.append("{");
                json.append("\"menuItemId\":").append(item.getMenuItem().getId()).append(",");
                json.append("\"menuItemName\":\"").append(item.getMenuItem().getName()).append("\",");
                json.append("\"quantity\":").append(item.getQuantity()).append(",");
                json.append("\"price\":").append(item.getPrice());
                json.append("}");
            }
        }

        json.append("]");
        json.append("}");
        return json.toString();
    }

    /**
     * Get order summary for display
     */
    public String getSummary() {
        int itemCount = items != null ? items.size() : 0;
        return String.format("Table %d - %d items - ₹%.2f",
                           tableNumber, itemCount, calculateTotal());
    }

    // Getters and setters for customer history fields
    public int getCustomerId() {
        return customerId;
    }

    public void setCustomerId(int customerId) {
        this.customerId = customerId;
    }

    public LocalDateTime getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(LocalDateTime orderDate) {
        this.orderDate = orderDate;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getOutlet() {
        return outlet;
    }

    public void setOutlet(String outlet) {
        this.outlet = outlet;
    }

    public double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }
}