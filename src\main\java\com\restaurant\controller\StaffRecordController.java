package com.restaurant.controller;

import com.restaurant.model.Employee;
import com.restaurant.model.EmployeeDAO;
import javafx.application.Platform;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.input.KeyCode;
import javafx.scene.layout.*;
import javafx.stage.Modality;
import javafx.stage.Stage;

import java.net.URL;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;

/**
 * Controller for Staff Record Management
 * Displays complete information of all staff members with search functionality
 */
public class StaffRecordController implements Initializable {

    // FXML Components
    @FXML private TextField searchField;
    @FXML private ComboBox<String> departmentFilterCombo;
    @FXML private ComboBox<String> statusFilterCombo;
    @FXML private TableView<Employee> staffTable;
    @FXML private TableColumn<Employee, String> idColumn;
    @FXML private TableColumn<Employee, String> nameColumn;
    @FXML private TableColumn<Employee, String> employeeIdColumn;
    @FXML private TableColumn<Employee, String> departmentColumn;
    @FXML private TableColumn<Employee, String> positionColumn;
    @FXML private TableColumn<Employee, String> contactColumn;
    @FXML private TableColumn<Employee, String> emailColumn;
    @FXML private TableColumn<Employee, String> joinDateColumn;
    @FXML private TableColumn<Employee, String> statusColumn;
    @FXML private TableColumn<Employee, String> salaryColumn;
    @FXML private TableColumn<Employee, String> actionsColumn;

    // Staff Form Dialog
    @FXML private StackPane staffFormDialog;
    @FXML private Label dialogTitle;
    @FXML private TextField nameField;
    @FXML private TextField employeeIdField;
    @FXML private ComboBox<String> departmentCombo;
    @FXML private TextField positionField;
    @FXML private TextField phoneField;
    @FXML private TextField emailField;
    @FXML private TextField addressField;
    @FXML private DatePicker joinDatePicker;
    @FXML private ComboBox<String> statusCombo;
    @FXML private TextField salaryField;
    @FXML private ComboBox<String> shiftCombo;
    @FXML private TextArea notesArea;

    // Data
    private ObservableList<Employee> staffList = FXCollections.observableArrayList();
    private FilteredList<Employee> filteredStaffList;
    private Employee editingEmployee = null;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        setupTableColumns();
        setupFilters();
        setupComboBoxes();
        setupSearch();
        setupModalKeyboardHandling();
        loadStaffData();
    }

    private void setupTableColumns() {
        // ID Column
        idColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(String.valueOf(cellData.getValue().getId())));
        
        // Name Column
        nameColumn.setCellValueFactory(new PropertyValueFactory<>("name"));
        
        // Employee ID Column
        employeeIdColumn.setCellValueFactory(new PropertyValueFactory<>("employeeId"));
        
        // Department Column
        departmentColumn.setCellValueFactory(new PropertyValueFactory<>("department"));
        
        // Position Column
        positionColumn.setCellValueFactory(new PropertyValueFactory<>("position"));
        
        // Contact Column
        contactColumn.setCellValueFactory(new PropertyValueFactory<>("phone"));
        
        // Email Column
        emailColumn.setCellValueFactory(new PropertyValueFactory<>("email"));
        
        // Join Date Column
        joinDateColumn.setCellValueFactory(cellData -> {
            if (cellData.getValue().getDateOfJoining() != null) {
                return new SimpleStringProperty(
                    cellData.getValue().getDateOfJoining().format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))
                );
            }
            return new SimpleStringProperty("-");
        });
        
        // Status Column
        statusColumn.setCellValueFactory(new PropertyValueFactory<>("status"));
        
        // Salary Column
        salaryColumn.setCellValueFactory(cellData -> 
            new SimpleStringProperty(cellData.getValue().getSalaryDisplay()));
        
        // Actions Column
        actionsColumn.setCellFactory(column -> new TableCell<Employee, String>() {
            private final Button viewButton = new Button("👁");
            private final Button editButton = new Button("✏️");
            private final Button deleteButton = new Button("🗑️");
            private final HBox actionBox = new HBox(5, viewButton, editButton, deleteButton);

            {
                viewButton.getStyleClass().add("action-button");
                editButton.getStyleClass().add("action-button");
                deleteButton.getStyleClass().add("action-button");
                
                viewButton.setTooltip(new Tooltip("View Details"));
                editButton.setTooltip(new Tooltip("Edit Staff"));
                deleteButton.setTooltip(new Tooltip("Delete Staff"));

                viewButton.setOnAction(e -> viewStaffDetails(getTableRow().getItem()));
                editButton.setOnAction(e -> editStaff(getTableRow().getItem()));
                deleteButton.setOnAction(e -> deleteStaff(getTableRow().getItem()));
            }

            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                setGraphic(empty ? null : actionBox);
            }
        });

        // Set column widths
        idColumn.setPrefWidth(60);
        nameColumn.setPrefWidth(120);
        employeeIdColumn.setPrefWidth(100);
        departmentColumn.setPrefWidth(100);
        positionColumn.setPrefWidth(120);
        contactColumn.setPrefWidth(120);
        emailColumn.setPrefWidth(150);
        joinDateColumn.setPrefWidth(100);
        statusColumn.setPrefWidth(80);
        salaryColumn.setPrefWidth(100);
        actionsColumn.setPrefWidth(120);
    }

    private void setupFilters() {
        // Department filter
        departmentFilterCombo.setItems(FXCollections.observableArrayList(
            "All Departments", "Kitchen", "Service", "Management", "Cleaning", "Security"
        ));
        departmentFilterCombo.setValue("All Departments");
        departmentFilterCombo.setOnAction(e -> applyFilters());

        // Status filter
        statusFilterCombo.setItems(FXCollections.observableArrayList(
            "All Status", "Active", "Inactive", "On Leave"
        ));
        statusFilterCombo.setValue("All Status");
        statusFilterCombo.setOnAction(e -> applyFilters());
    }

    private void setupComboBoxes() {
        // Department combo for form
        departmentCombo.setItems(FXCollections.observableArrayList(
            "Kitchen", "Service", "Management", "Cleaning", "Security"
        ));

        // Status combo for form
        statusCombo.setItems(FXCollections.observableArrayList(
            "Active", "Inactive", "On Leave"
        ));

        // Shift combo for form
        shiftCombo.setItems(FXCollections.observableArrayList(
            "Morning", "Evening", "Night", "Full Day"
        ));
    }

    private void setupSearch() {
        // Create filtered list
        filteredStaffList = new FilteredList<>(staffList, p -> true);
        staffTable.setItems(filteredStaffList);

        // Setup search field listener
        searchField.textProperty().addListener((observable, oldValue, newValue) -> {
            applyFilters();
        });

        // Set search field prompt
        searchField.setPromptText("Search by Name, ID, Contact, or Role...");
    }

    private void applyFilters() {
        filteredStaffList.setPredicate(employee -> {
            // Get filter values
            String searchText = searchField.getText();
            String departmentFilter = departmentFilterCombo.getValue();
            String statusFilter = statusFilterCombo.getValue();

            // Apply search filter
            if (searchText != null && !searchText.trim().isEmpty()) {
                String lowerCaseFilter = searchText.toLowerCase().trim();
                
                boolean matchesSearch = 
                    (employee.getName() != null && employee.getName().toLowerCase().contains(lowerCaseFilter)) ||
                    (employee.getEmployeeId() != null && employee.getEmployeeId().toLowerCase().contains(lowerCaseFilter)) ||
                    (employee.getPhone() != null && employee.getPhone().toLowerCase().contains(lowerCaseFilter)) ||
                    (employee.getPosition() != null && employee.getPosition().toLowerCase().contains(lowerCaseFilter)) ||
                    (employee.getEmail() != null && employee.getEmail().toLowerCase().contains(lowerCaseFilter));
                
                if (!matchesSearch) {
                    return false;
                }
            }

            // Apply department filter
            if (departmentFilter != null && !departmentFilter.equals("All Departments")) {
                if (employee.getDepartment() == null || !employee.getDepartment().equals(departmentFilter)) {
                    return false;
                }
            }

            // Apply status filter
            if (statusFilter != null && !statusFilter.equals("All Status")) {
                if (employee.getStatus() == null || !employee.getStatus().equals(statusFilter)) {
                    return false;
                }
            }

            return true;
        });
    }

    private void loadStaffData() {
        Platform.runLater(() -> {
            try {
                // Load from database
                List<Employee> employees = EmployeeDAO.getAllEmployees();
                staffList.clear();
                staffList.addAll(employees);

                // If no data in database, create sample data
                if (staffList.isEmpty()) {
                    createSampleStaffData();
                }

                System.out.println("Loaded " + staffList.size() + " staff records");
            } catch (Exception e) {
                e.printStackTrace();
                // Fallback to sample data
                createSampleStaffData();
            }
        });
    }

    private void createSampleStaffData() {
        staffList.addAll(
            new Employee(1, "John Doe", "EMP001", "Kitchen", "Head Chef"),
            new Employee(2, "Jane Smith", "EMP002", "Service", "Waiter"),
            new Employee(3, "Mike Johnson", "EMP003", "Kitchen", "Sous Chef"),
            new Employee(4, "Sarah Wilson", "EMP004", "Service", "Waitress"),
            new Employee(5, "David Brown", "EMP005", "Management", "Manager"),
            new Employee(6, "Lisa Garcia", "EMP006", "Cleaning", "Cleaner"),
            new Employee(7, "Tom Anderson", "EMP007", "Kitchen", "Cook"),
            new Employee(8, "Emily Davis", "EMP008", "Service", "Cashier"),
            new Employee(9, "Chris Martinez", "EMP009", "Security", "Security Guard"),
            new Employee(10, "Anna Taylor", "EMP010", "Service", "Host")
        );

        // Set additional details for sample data
        for (Employee emp : staffList) {
            emp.setPhone("9876543210");
            emp.setEmail(emp.getName().toLowerCase().replace(" ", ".") + "@restaurant.com");
            emp.setStatus("Active");
            emp.setSalary(25000 + (emp.getId() * 2000));
            emp.setShiftTiming("Full Day");
        }
    }

    private void setupModalKeyboardHandling() {
        if (staffFormDialog != null) {
            staffFormDialog.setOnKeyPressed(event -> {
                if (event.getCode() == KeyCode.ESCAPE) {
                    hideStaffDialog();
                }
            });
        }
    }

    @FXML
    private void addNewStaff() {
        editingEmployee = null;
        clearStaffForm();
        dialogTitle.setText("Add New Staff Member");
        showStaffDialog();
    }

    @FXML
    private void refreshStaffList() {
        loadStaffData();
        showAlert("Refreshed", "Staff records have been refreshed");
    }

    @FXML
    private void goBack() {
        try {
            Parent dashboardView = FXMLLoader.load(getClass().getResource("/fxml/Dashboard.fxml"));
            Stage stage = (Stage) staffTable.getScene().getWindow();
            stage.getScene().setRoot(dashboardView);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void viewStaffDetails(Employee employee) {
        if (employee == null) return;
        
        // Create detailed view dialog
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Staff Details");
        alert.setHeaderText(employee.getName() + " (" + employee.getEmployeeId() + ")");
        
        String details = String.format(
            "Department: %s\n" +
            "Position: %s\n" +
            "Contact: %s\n" +
            "Email: %s\n" +
            "Join Date: %s\n" +
            "Status: %s\n" +
            "Salary: %s\n" +
            "Shift: %s\n" +
            "Address: %s",
            employee.getDepartment(),
            employee.getPosition(),
            employee.getPhone(),
            employee.getEmail(),
            employee.getDateOfJoining() != null ? employee.getDateOfJoining().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "N/A",
            employee.getStatus(),
            employee.getSalaryDisplay(),
            employee.getShiftTiming(),
            employee.getAddress() != null ? employee.getAddress() : "N/A"
        );
        
        alert.setContentText(details);
        alert.showAndWait();
    }

    private void editStaff(Employee employee) {
        if (employee == null) return;
        
        editingEmployee = employee;
        populateStaffForm(employee);
        dialogTitle.setText("Edit Staff Member");
        showStaffDialog();
    }

    private void deleteStaff(Employee employee) {
        if (employee == null) return;
        
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Delete Staff");
        alert.setHeaderText("Delete " + employee.getName() + "?");
        alert.setContentText("This action cannot be undone.");
        
        alert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                try {
                    if (EmployeeDAO.deleteEmployee(employee.getId())) {
                        staffList.remove(employee);
                        showAlert("Success", "Staff member deleted successfully");
                    } else {
                        showAlert("Error", "Failed to delete staff member");
                    }
                } catch (Exception e) {
                    // For sample data, just remove from list
                    staffList.remove(employee);
                    showAlert("Success", "Staff member deleted successfully");
                }
            }
        });
    }

    private void showStaffDialog() {
        if (staffFormDialog != null) {
            staffFormDialog.setVisible(true);
            staffFormDialog.toFront();
            Platform.runLater(() -> nameField.requestFocus());
        }
    }

    private void hideStaffDialog() {
        if (staffFormDialog != null) {
            staffFormDialog.setVisible(false);
        }
    }

    private void clearStaffForm() {
        nameField.clear();
        employeeIdField.clear();
        departmentCombo.setValue(null);
        positionField.clear();
        phoneField.clear();
        emailField.clear();
        addressField.clear();
        joinDatePicker.setValue(null);
        statusCombo.setValue("Active");
        salaryField.clear();
        shiftCombo.setValue("Full Day");
        notesArea.clear();
    }

    private void populateStaffForm(Employee employee) {
        nameField.setText(employee.getName());
        employeeIdField.setText(employee.getEmployeeId());
        departmentCombo.setValue(employee.getDepartment());
        positionField.setText(employee.getPosition());
        phoneField.setText(employee.getPhone());
        emailField.setText(employee.getEmail());
        addressField.setText(employee.getAddress());
        joinDatePicker.setValue(employee.getDateOfJoining());
        statusCombo.setValue(employee.getStatus());
        salaryField.setText(String.valueOf(employee.getSalary()));
        shiftCombo.setValue(employee.getShiftTiming());
        notesArea.setText(employee.getNotes());
    }

    @FXML
    private void saveStaff() {
        if (validateStaffForm()) {
            try {
                Employee employee = editingEmployee != null ? editingEmployee : new Employee();
                
                // Set form data
                employee.setName(nameField.getText().trim());
                employee.setEmployeeId(employeeIdField.getText().trim());
                employee.setDepartment(departmentCombo.getValue());
                employee.setPosition(positionField.getText().trim());
                employee.setPhone(phoneField.getText().trim());
                employee.setEmail(emailField.getText().trim());
                employee.setAddress(addressField.getText().trim());
                employee.setDateOfJoining(joinDatePicker.getValue());
                employee.setStatus(statusCombo.getValue());
                employee.setSalary(Double.parseDouble(salaryField.getText().trim()));
                employee.setShiftTiming(shiftCombo.getValue());
                employee.setNotes(notesArea.getText().trim());

                boolean success;
                if (editingEmployee != null) {
                    // Update existing
                    success = EmployeeDAO.updateEmployee(employee);
                    if (success) {
                        // Update in list
                        int index = staffList.indexOf(editingEmployee);
                        if (index >= 0) {
                            staffList.set(index, employee);
                        }
                    }
                } else {
                    // Add new
                    success = EmployeeDAO.addEmployee(employee);
                    if (success) {
                        staffList.add(employee);
                    }
                }

                if (success) {
                    hideStaffDialog();
                    showAlert("Success", editingEmployee != null ? "Staff updated successfully" : "Staff added successfully");
                } else {
                    showAlert("Error", "Failed to save staff member");
                }

            } catch (Exception e) {
                e.printStackTrace();
                showAlert("Error", "Error saving staff: " + e.getMessage());
            }
        }
    }

    @FXML
    private void cancelStaff() {
        hideStaffDialog();
    }

    private boolean validateStaffForm() {
        if (nameField.getText().trim().isEmpty()) {
            showAlert("Validation Error", "Name is required");
            nameField.requestFocus();
            return false;
        }
        
        if (employeeIdField.getText().trim().isEmpty()) {
            showAlert("Validation Error", "Employee ID is required");
            employeeIdField.requestFocus();
            return false;
        }
        
        if (departmentCombo.getValue() == null) {
            showAlert("Validation Error", "Department is required");
            departmentCombo.requestFocus();
            return false;
        }
        
        if (positionField.getText().trim().isEmpty()) {
            showAlert("Validation Error", "Position is required");
            positionField.requestFocus();
            return false;
        }

        try {
            Double.parseDouble(salaryField.getText().trim());
        } catch (NumberFormatException e) {
            showAlert("Validation Error", "Please enter a valid salary amount");
            salaryField.requestFocus();
            return false;
        }

        return true;
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
