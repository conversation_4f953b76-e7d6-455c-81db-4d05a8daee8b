package com.restaurant.util; 
 
import java.sql.*; 
import org.mindrot.jbcrypt.BCrypt; 
 
public class AuthTester { 
    public static void main(String[] args) { 
        System.out.println("🔍 Testing authentication process..."); 
        System.out.println(); 
 
        // Test credentials 
        String testUsername = "admin"; 
        String testPassword = "admin123"; 
 
        try { 
            Class.forName("org.sqlite.JDBC"); 
            System.out.println("✅ SQLite driver loaded"); 
        } catch (Exception e) { 
            System.err.println("❌ Driver error: " + e.getMessage()); 
            return; 
        } 
 
        String url = "*************************"; 
 
        try (Connection conn = DriverManager.getConnection(url)) { 
            System.out.println("✅ Database connected"); 
 
            // First, show all users 
            System.out.println(); 
            System.out.println("📋 ALL USERS IN DATABASE:"); 
            String selectAll = "SELECT id, username, role, password FROM users"; 
            try (Statement stmt = conn.createStatement(); 
                 ResultSet rs = stmt.executeQuery(selectAll)) { 
                while (rs.next()) { 
                    System.out.println("ID: " + rs.getInt("id")); 
                    System.out.println("Username: '" + rs.getString("username") + "'"); 
                    System.out.println("Role: '" + rs.getString("role") + "'"); 
                    System.out.println("Password hash: " + rs.getString("password").substring(0, 20) + "..."); 
                    System.out.println("---"); 
                } 
            } 
 
            // Now test authentication 
            System.out.println(); 
            System.out.println("🧪 TESTING AUTHENTICATION:"); 
            System.out.println("Testing username: '" + testUsername + "'"); 
            System.out.println("Testing password: '" + testPassword + "'"); 
            System.out.println(); 
 
            // Step 1: Find user by username 
            String findUser = "SELECT id, username, password, role FROM users WHERE username = ?"; 
            try (PreparedStatement pstmt = conn.prepareStatement(findUser)) { 
                pstmt.setString(1, testUsername); 
                ResultSet rs = pstmt.executeQuery(); 
 
                if (rs.next()) { 
                    System.out.println("✅ User found in database"); 
                    System.out.println("   Database username: '" + rs.getString("username") + "'"); 
                    System.out.println("   Database role: '" + rs.getString("role") + "'"); 
 
                    // Step 2: Check password 
                    String storedHash = rs.getString("password"); 
                    System.out.println("   Stored hash: " + storedHash.substring(0, 20) + "..."); 
 
                    // Test BCrypt password verification 
                    boolean passwordMatch = BCrypt.checkpw(testPassword, storedHash); 
                    System.out.println("   Password check result: " + passwordMatch); 
 
                    if (passwordMatch) { 
                        System.out.println("✅ AUTHENTICATION SUCCESSFUL!"); 
                        System.out.println("   User ID: " + rs.getInt("id")); 
                        System.out.println("   Username: " + rs.getString("username")); 
                        System.out.println("   Role: " + rs.getString("role")); 
                    } else { 
                        System.out.println("❌ PASSWORD VERIFICATION FAILED!"); 
                        System.out.println("   The password 'admin123' does not match the stored hash"); 
 
                        // Test creating a new hash 
                        String newHash = BCrypt.hashpw(testPassword, BCrypt.gensalt()); 
                        System.out.println("   New hash for 'admin123': " + newHash.substring(0, 20) + "..."); 
                    } 
 
                } else { 
                    System.out.println("❌ USER NOT FOUND!"); 
                    System.out.println("   No user with username '" + testUsername + "' exists"); 
                } 
            } 
 
        } catch (Exception e) { 
            System.err.println("❌ Error: " + e.getMessage()); 
            e.printStackTrace(); 
        } 
    } 
} 
