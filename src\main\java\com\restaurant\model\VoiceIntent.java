package com.restaurant.model;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Model class representing a processed voice intent with extracted entities
 */
public class VoiceIntent {
    private String category;
    private String action;
    private String description;
    private Map<String, String> entities;
    private double confidence;
    private LocalDateTime timestamp;
    private String originalInput;
    private String executionResult;
    private boolean executed;

    // Constructors
    public VoiceIntent() {
        this.timestamp = LocalDateTime.now();
        this.executed = false;
    }

    public VoiceIntent(String category, String action, String description, 
                       Map<String, String> entities, double confidence) {
        this();
        this.category = category;
        this.action = action;
        this.description = description;
        this.entities = entities;
        this.confidence = confidence;
    }

    public VoiceIntent(String category, String action, String description, 
                       Map<String, String> entities, double confidence, String originalInput) {
        this(category, action, description, entities, confidence);
        this.originalInput = originalInput;
    }

    // Getters and Setters
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Map<String, String> getEntities() {
        return entities;
    }

    public void setEntities(Map<String, String> entities) {
        this.entities = entities;
    }

    public double getConfidence() {
        return confidence;
    }

    public void setConfidence(double confidence) {
        this.confidence = confidence;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getOriginalInput() {
        return originalInput;
    }

    public void setOriginalInput(String originalInput) {
        this.originalInput = originalInput;
    }

    public String getExecutionResult() {
        return executionResult;
    }

    public void setExecutionResult(String executionResult) {
        this.executionResult = executionResult;
    }

    public boolean isExecuted() {
        return executed;
    }

    public void setExecuted(boolean executed) {
        this.executed = executed;
    }

    // Utility methods
    public String getEntity(String key) {
        return entities != null ? entities.get(key) : null;
    }

    public String getEntity(String key, String defaultValue) {
        String value = getEntity(key);
        return value != null ? value : defaultValue;
    }

    public boolean hasEntity(String key) {
        return entities != null && entities.containsKey(key);
    }

    public String getConfidenceLevel() {
        if (confidence >= 0.9) return "Very High";
        if (confidence >= 0.75) return "High";
        if (confidence >= 0.6) return "Medium";
        if (confidence >= 0.4) return "Low";
        return "Very Low";
    }

    public boolean isExecutable() {
        return confidence >= 0.5 && !category.equals("UNKNOWN");
    }

    public boolean isHighConfidence() {
        return confidence >= 0.75;
    }

    public String getFormattedTimestamp() {
        return timestamp.toString(); // Can be formatted as needed
    }

    public String getCategoryDisplayName() {
        if (category == null) return "Voice Command";

        switch (category.toLowerCase()) {
            case "forecasting": return "AI Forecasting";
            case "billing": return "Billing & Orders";
            case "table_management":
            case "table": return "Table Management";
            case "menu_search":
            case "menu": return "Menu Search";
            case "reports": return "Reports & Analytics";
            case "inventory": return "Inventory Management";
            case "navigation": return "Navigation";
            case "system_control":
            case "settings": return "System Control";
            case "order":
            case "ordering": return "Order Management";
            case "payment": return "Payment";
            case "search": return "Search";
            case "help": return "Help";
            default: return category;
        }
    }

    public String getActionDisplayName() {
        switch (action) {
            case "GENERATE_FORECAST": return "Generate Forecast";
            case "SHOW_PAST_FORECAST": return "Show Past Forecast";
            case "ADD_ITEM": return "Add Item to Order";
            case "REMOVE_ITEM": return "Remove Item from Order";
            case "SHOW_BILL": return "Show Bill";
            case "PLACE_ORDER": return "Place Order";
            case "OPEN_TABLE": return "Open Table";
            case "CLOSE_TABLE": return "Close Table";
            case "MERGE_TABLES": return "Merge Tables";
            case "SEARCH_ITEM": return "Search Menu Item";
            case "FILTER_BY_CATEGORY": return "Filter by Category";
            case "FILTER_BY_PRICE": return "Filter by Price";
            case "SHOW_SALES_REPORT": return "Show Sales Report";
            case "COMPARE_PERIODS": return "Compare Time Periods";
            case "EXPORT_REPORT": return "Export Report";
            case "CHECK_STOCK": return "Check Stock Level";
            case "UPDATE_STOCK": return "Update Stock";
            case "SHOW_LOW_STOCK": return "Show Low Stock Items";
            case "NAVIGATE_TO": return "Navigate To";
            case "RESET_FILTERS": return "Reset Filters";
            case "LOGOUT": return "Logout";
            case "DOWNLOAD_REPORT": return "Download Report";
            case "REFRESH_DATA": return "Refresh Data";
            case "SAVE_CHANGES": return "Save Changes";
            case "CANCEL_OPERATION": return "Cancel Operation";
            default: return action.replace("_", " ");
        }
    }

    public String getConfidenceIcon() {
        if (confidence >= 0.9) return "🔥";
        if (confidence >= 0.75) return "✅";
        if (confidence >= 0.6) return "👍";
        if (confidence >= 0.4) return "⚠️";
        return "❌";
    }

    public String getCategoryIcon() {
        if (category == null) return "🎤";

        switch (category.toLowerCase()) {
            case "forecasting": return "📊";
            case "billing": return "💰";
            case "table_management":
            case "table": return "🪑";
            case "menu_search":
            case "menu": return "🔍";
            case "reports": return "📈";
            case "inventory": return "📦";
            case "navigation": return "🧭";
            case "system_control":
            case "settings": return "⚙️";
            case "order":
            case "ordering": return "🍽️";
            case "payment": return "💳";
            case "search": return "🔍";
            case "help": return "❓";
            default: return "🎤";
        }
    }

    @Override
    public String toString() {
        return "VoiceIntent{" +
                "category='" + category + '\'' +
                ", action='" + action + '\'' +
                ", description='" + description + '\'' +
                ", confidence=" + confidence +
                ", executed=" + executed +
                ", entities=" + entities +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        VoiceIntent that = (VoiceIntent) o;

        if (!category.equals(that.category)) return false;
        if (!action.equals(that.action)) return false;
        return timestamp.equals(that.timestamp);
    }

    @Override
    public int hashCode() {
        int result = category.hashCode();
        result = 31 * result + action.hashCode();
        result = 31 * result + timestamp.hashCode();
        return result;
    }

    // Builder pattern for easy construction
    public static class Builder {
        private VoiceIntent intent = new VoiceIntent();

        public Builder category(String category) {
            intent.category = category;
            return this;
        }

        public Builder action(String action) {
            intent.action = action;
            return this;
        }

        public Builder description(String description) {
            intent.description = description;
            return this;
        }

        public Builder entities(Map<String, String> entities) {
            intent.entities = entities;
            return this;
        }

        public Builder confidence(double confidence) {
            intent.confidence = confidence;
            return this;
        }

        public Builder originalInput(String originalInput) {
            intent.originalInput = originalInput;
            return this;
        }

        public VoiceIntent build() {
            return intent;
        }
    }


}
