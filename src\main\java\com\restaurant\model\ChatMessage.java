package com.restaurant.model;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Model class representing a chat message in the AI Assistant conversation
 */
public class ChatMessage {
    private String content;
    private String sender; // "user" or "assistant"
    private LocalDateTime timestamp;
    private String messageType; // "text", "action", "error", "system"
    private String messageId;
    private boolean isRead;
    private AIResponse associatedResponse; // For assistant messages

    public ChatMessage() {
        this.timestamp = LocalDateTime.now();
        this.messageId = generateMessageId();
        this.isRead = false;
        this.messageType = "text";
    }

    public ChatMessage(String content, String sender) {
        this();
        this.content = content;
        this.sender = sender;
    }

    public ChatMessage(String content, String sender, LocalDateTime timestamp) {
        this();
        this.content = content;
        this.sender = sender;
        this.timestamp = timestamp;
    }

    public ChatMessage(String content, String sender, String messageType) {
        this();
        this.content = content;
        this.sender = sender;
        this.messageType = messageType;
    }

    // Getters and Setters
    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public boolean isRead() {
        return isRead;
    }

    public void setRead(boolean read) {
        isRead = read;
    }

    public AIResponse getAssociatedResponse() {
        return associatedResponse;
    }

    public void setAssociatedResponse(AIResponse associatedResponse) {
        this.associatedResponse = associatedResponse;
    }

    // Utility methods
    public boolean isFromUser() {
        return "user".equals(sender);
    }

    public boolean isFromAssistant() {
        return "assistant".equals(sender);
    }

    public boolean isSystemMessage() {
        return "system".equals(messageType);
    }

    public boolean isErrorMessage() {
        return "error".equals(messageType);
    }

    public boolean isActionMessage() {
        return "action".equals(messageType);
    }

    public String getFormattedTimestamp() {
        return timestamp.format(DateTimeFormatter.ofPattern("HH:mm"));
    }

    public String getFormattedDate() {
        return timestamp.format(DateTimeFormatter.ofPattern("MMM dd, yyyy"));
    }

    public String getFormattedDateTime() {
        return timestamp.format(DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm"));
    }

    public String getSenderDisplayName() {
        switch (sender) {
            case "user": return "You";
            case "assistant": return "RestaurantBot";
            case "system": return "System";
            default: return sender;
        }
    }

    public String getSenderIcon() {
        switch (sender) {
            case "user": return "👤";
            case "assistant": return "🤖";
            case "system": return "⚙️";
            default: return "💬";
        }
    }

    public String getMessageTypeIcon() {
        switch (messageType) {
            case "text": return "💬";
            case "action": return "⚡";
            case "error": return "❌";
            case "system": return "ℹ️";
            default: return "💬";
        }
    }

    public String getMessagePreview() {
        if (content == null) return "";
        
        String preview = content.trim();
        if (preview.length() > 50) {
            preview = preview.substring(0, 47) + "...";
        }
        return preview;
    }

    public int getWordCount() {
        if (content == null || content.trim().isEmpty()) return 0;
        return content.trim().split("\\s+").length;
    }

    public int getCharacterCount() {
        return content != null ? content.length() : 0;
    }

    public boolean hasAssociatedResponse() {
        return associatedResponse != null;
    }

    public boolean isLongMessage() {
        return getCharacterCount() > 200;
    }

    public boolean isRecentMessage() {
        return timestamp.isAfter(LocalDateTime.now().minusMinutes(5));
    }

    public String getTimeAgo() {
        LocalDateTime now = LocalDateTime.now();
        long minutes = java.time.Duration.between(timestamp, now).toMinutes();
        
        if (minutes < 1) return "Just now";
        if (minutes < 60) return minutes + " min ago";
        
        long hours = minutes / 60;
        if (hours < 24) return hours + " hour" + (hours > 1 ? "s" : "") + " ago";
        
        long days = hours / 24;
        if (days < 7) return days + " day" + (days > 1 ? "s" : "") + " ago";
        
        return getFormattedDate();
    }

    private String generateMessageId() {
        return "msg_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }

    public void markAsRead() {
        this.isRead = true;
    }

    public ChatMessage copy() {
        ChatMessage copy = new ChatMessage();
        copy.content = this.content;
        copy.sender = this.sender;
        copy.timestamp = this.timestamp;
        copy.messageType = this.messageType;
        copy.messageId = this.messageId + "_copy";
        copy.isRead = this.isRead;
        copy.associatedResponse = this.associatedResponse;
        return copy;
    }

    @Override
    public String toString() {
        return "ChatMessage{" +
                "content='" + getMessagePreview() + '\'' +
                ", sender='" + sender + '\'' +
                ", timestamp=" + getFormattedDateTime() +
                ", messageType='" + messageType + '\'' +
                ", messageId='" + messageId + '\'' +
                ", isRead=" + isRead +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ChatMessage that = (ChatMessage) o;

        if (!messageId.equals(that.messageId)) return false;
        return timestamp.equals(that.timestamp);
    }

    @Override
    public int hashCode() {
        int result = messageId.hashCode();
        result = 31 * result + timestamp.hashCode();
        return result;
    }

    // Static factory methods
    public static ChatMessage userMessage(String content) {
        return new ChatMessage(content, "user", "text");
    }

    public static ChatMessage assistantMessage(String content) {
        return new ChatMessage(content, "assistant", "text");
    }

    public static ChatMessage systemMessage(String content) {
        return new ChatMessage(content, "system", "system");
    }

    public static ChatMessage errorMessage(String content) {
        return new ChatMessage(content, "assistant", "error");
    }

    public static ChatMessage actionMessage(String content) {
        return new ChatMessage(content, "assistant", "action");
    }

    // Builder pattern
    public static class Builder {
        private ChatMessage message = new ChatMessage();

        public Builder content(String content) {
            message.content = content;
            return this;
        }

        public Builder sender(String sender) {
            message.sender = sender;
            return this;
        }

        public Builder messageType(String messageType) {
            message.messageType = messageType;
            return this;
        }

        public Builder timestamp(LocalDateTime timestamp) {
            message.timestamp = timestamp;
            return this;
        }

        public Builder associatedResponse(AIResponse response) {
            message.associatedResponse = response;
            return this;
        }

        public Builder read(boolean read) {
            message.isRead = read;
            return this;
        }

        public ChatMessage build() {
            return message;
        }
    }
}
