@echo off
echo 🔍 DEBUGGING AUDIO ISSUES...
echo.

echo STEP 1: Check system volume and audio
echo Make sure your speakers/headphones are connected and volume is up!
echo.

echo STEP 2: Test basic system beep first
echo Testing if any sound works at all...
echo.

echo package com.restaurant.util; > src\main\java\com\restaurant\util\AudioDebugger.java
echo. >> src\main\java\com\restaurant\util\AudioDebugger.java
echo public class AudioDebugger { >> src\main\java\com\restaurant\util\AudioDebugger.java
echo     public static void main(String[] args) { >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         System.out.println("🔍 Testing audio system..."); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         System.out.println(); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo. >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         // Test 1: Basic system beep >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         System.out.println("Test 1: Basic system beep"); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         try { >> src\main\java\com\restaurant\util\AudioDebugger.java
echo             java.awt.Toolkit.getDefaultToolkit().beep(); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo             System.out.println("✅ System beep sent - did you hear it?"); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo             Thread.sleep(2000); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\util\AudioDebugger.java
echo             System.out.println("❌ System beep failed: " + e.getMessage()); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         } >> src\main\java\com\restaurant\util\AudioDebugger.java
echo. >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         // Test 2: Multiple beeps >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         System.out.println("Test 2: Multiple beeps (should hear 3 beeps)"); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         try { >> src\main\java\com\restaurant\util\AudioDebugger.java
echo             for (int i = 0; i ^< 3; i++) { >> src\main\java\com\restaurant\util\AudioDebugger.java
echo                 System.out.println("Beep " + (i + 1)); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo                 java.awt.Toolkit.getDefaultToolkit().beep(); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo                 Thread.sleep(500); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo             } >> src\main\java\com\restaurant\util\AudioDebugger.java
echo             System.out.println("✅ Multiple beeps sent - did you hear 3 beeps?"); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\util\AudioDebugger.java
echo             System.out.println("❌ Multiple beeps failed: " + e.getMessage()); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         } >> src\main\java\com\restaurant\util\AudioDebugger.java
echo. >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         // Test 3: Check audio file >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         System.out.println(); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         System.out.println("Test 3: Checking MP3 file"); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         java.io.File audioFile = new java.io.File("sounds/mixkit-urgent-simple-tone-loop-2976.mp3"); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         if (audioFile.exists()) { >> src\main\java\com\restaurant\util\AudioDebugger.java
echo             System.out.println("✅ MP3 file found: " + audioFile.getAbsolutePath()); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo             System.out.println("   File size: " + audioFile.length() + " bytes"); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo             System.out.println("   Can read: " + audioFile.canRead()); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         } else { >> src\main\java\com\restaurant\util\AudioDebugger.java
echo             System.out.println("❌ MP3 file not found: " + audioFile.getAbsolutePath()); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         } >> src\main\java\com\restaurant\util\AudioDebugger.java
echo. >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         System.out.println(); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         System.out.println("🔍 AUDIO DIAGNOSIS COMPLETE"); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         System.out.println(); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         System.out.println("TROUBLESHOOTING:"); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         System.out.println("1. If you heard NO beeps - check system volume/speakers"); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         System.out.println("2. If you heard beeps but no MP3 - JavaFX Media issue"); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         System.out.println("3. If MP3 file not found - file path issue"); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo         System.out.println("4. Try playing the MP3 file manually with Windows Media Player"); >> src\main\java\com\restaurant\util\AudioDebugger.java
echo     } >> src\main\java\com\restaurant\util\AudioDebugger.java
echo } >> src\main\java\com\restaurant\util\AudioDebugger.java

echo ✅ AudioDebugger created
echo.

echo 🔧 COMPILING...
javac -d target/classes src/main/java/com/restaurant/util/AudioDebugger.java

if %ERRORLEVEL% neq 0 (
    echo ❌ Compilation failed
    pause
    exit /b 1
)

echo ✅ Compilation successful
echo.

echo 🔍 RUNNING AUDIO DIAGNOSIS...
echo.
echo IMPORTANT: Make sure your speakers/headphones are connected and volume is UP!
echo.

java -cp target/classes com.restaurant.util.AudioDebugger

echo.
echo 📋 DIAGNOSIS RESULTS:
echo.
echo Please tell me:
echo 1. Did you hear the system beeps? (Yes/No)
echo 2. Was the MP3 file found? (Check output above)
echo 3. What is your current volume level?
echo 4. Are speakers/headphones connected?
echo.
echo NEXT STEPS:
echo - If NO beeps heard: Check system audio settings
echo - If beeps heard but no MP3: Try JavaFX Media alternative
echo - If MP3 file not found: Check file location
echo.

echo 🎵 MANUAL TEST: Try playing your MP3 file directly
echo Right-click on sounds\mixkit-urgent-simple-tone-loop-2976.mp3
echo Select "Open with" → Windows Media Player
echo If it plays there, the file is good but JavaFX has issues
echo.

pause
