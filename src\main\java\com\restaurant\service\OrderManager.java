package com.restaurant.service;

import com.restaurant.model.Order;
import com.restaurant.model.OrderItem;
import com.restaurant.model.MenuItem;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;

/**
 * Centralized Order Management Service
 * Manages active table orders and ensures persistence across different views
 */
public class OrderManager {
    
    private static OrderManager instance;
    
    // Store active orders by table number
    private final Map<Integer, Order> activeTableOrders = new ConcurrentHashMap<>();
    
    // Store order status by table number
    private final Map<Integer, String> tableOrderStatus = new ConcurrentHashMap<>();
    
    private OrderManager() {
        // Private constructor for singleton
        initializeSampleOrders();
    }
    
    public static OrderManager getInstance() {
        if (instance == null) {
            synchronized (OrderManager.class) {
                if (instance == null) {
                    instance = new OrderManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * Get or create order for a table
     */
    public Order getOrCreateOrderForTable(int tableNumber) {
        return activeTableOrders.computeIfAbsent(tableNumber, this::createNewOrder);
    }
    
    /**
     * Get existing order for a table (returns null if no order exists)
     */
    public Order getOrderForTable(int tableNumber) {
        return activeTableOrders.get(tableNumber);
    }
    
    /**
     * Save/Update order for a table
     */
    public void saveOrderForTable(int tableNumber, Order order) {
        if (order != null) {
            order.setTableNumber(tableNumber);
            order.setTimestamp(LocalDateTime.now());
            activeTableOrders.put(tableNumber, order);
            updateTableStatus(tableNumber, "OCCUPIED");
            System.out.println("OrderManager: Saved order for table " + tableNumber + 
                             " with " + order.getItems().size() + " items");
        }
    }
    
    /**
     * Add item to table order
     */
    public void addItemToTable(int tableNumber, MenuItem menuItem, int quantity) {
        Order order = getOrCreateOrderForTable(tableNumber);
        
        // Check if item already exists in order
        OrderItem existingItem = order.getItems().stream()
            .filter(item -> item.getMenuItem().getId() == menuItem.getId())
            .findFirst()
            .orElse(null);
            
        if (existingItem != null) {
            existingItem.setQuantity(existingItem.getQuantity() + quantity);
        } else {
            order.addItem(new OrderItem(menuItem, quantity));
        }
        
        saveOrderForTable(tableNumber, order);
    }
    
    /**
     * Remove item from table order
     */
    public void removeItemFromTable(int tableNumber, OrderItem orderItem) {
        Order order = getOrderForTable(tableNumber);
        if (order != null) {
            order.getItems().remove(orderItem);
            if (order.getItems().isEmpty()) {
                clearTableOrder(tableNumber);
            } else {
                saveOrderForTable(tableNumber, order);
            }
        }
    }
    
    /**
     * Update item quantity in table order
     */
    public void updateItemQuantity(int tableNumber, OrderItem orderItem, int newQuantity) {
        Order order = getOrderForTable(tableNumber);
        if (order != null) {
            if (newQuantity <= 0) {
                removeItemFromTable(tableNumber, orderItem);
            } else {
                orderItem.setQuantity(newQuantity);
                saveOrderForTable(tableNumber, order);
            }
        }
    }
    
    /**
     * Clear order for a table (when bill is paid or order is cancelled)
     */
    public void clearTableOrder(int tableNumber) {
        activeTableOrders.remove(tableNumber);
        updateTableStatus(tableNumber, "BLANK");
        System.out.println("OrderManager: Cleared order for table " + tableNumber);
    }
    
    /**
     * Mark order as KOT printed
     */
    public void markKOTPrinted(int tableNumber) {
        updateTableStatus(tableNumber, "RUNNING_KOT");
        Order order = getOrderForTable(tableNumber);
        if (order != null) {
            order.setStatus("KOT_PRINTED");
        }
    }
    
    /**
     * Mark order as bill generated
     */
    public void markBillGenerated(int tableNumber) {
        updateTableStatus(tableNumber, "PRINTED");
        Order order = getOrderForTable(tableNumber);
        if (order != null) {
            order.setStatus("BILL_GENERATED");
        }
    }
    
    /**
     * Mark order as paid and clear it
     */
    public void markOrderPaid(int tableNumber) {
        Order order = getOrderForTable(tableNumber);
        if (order != null) {
            order.setStatus("PAID");
            // Keep order for a short time for reference, then clear
            clearTableOrder(tableNumber);
        }
    }

    /**
     * Mark order as held with reason
     */
    public void markOrderHeld(int tableNumber, String reason) {
        updateTableStatus(tableNumber, "ON_HOLD");
        Order order = getOrderForTable(tableNumber);
        if (order != null) {
            order.setStatus("HELD");
            // Add hold reason to order notes
            String currentNotes = order.getNotes() != null ? order.getNotes() : "";
            String holdNote = "\n[HELD] " + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) +
                             " - Reason: " + reason;
            order.setNotes(currentNotes + holdNote);
        }
        System.out.println("OrderManager: Order held for table " + tableNumber + " with reason: " + reason);
    }
    
    /**
     * Get all active table numbers with orders
     */
    public List<Integer> getActiveTableNumbers() {
        return new ArrayList<>(activeTableOrders.keySet());
    }
    
    /**
     * Check if table has active order
     */
    public boolean hasActiveOrder(int tableNumber) {
        Order order = getOrderForTable(tableNumber);
        return order != null && !order.getItems().isEmpty();
    }
    
    /**
     * Get table order status
     */
    public String getTableStatus(int tableNumber) {
        return tableOrderStatus.getOrDefault(tableNumber, "BLANK");
    }
    
    /**
     * Get order total amount for a table
     */
    public double getTableOrderTotal(int tableNumber) {
        Order order = getOrderForTable(tableNumber);
        return order != null ? order.calculateTotal() : 0.0;
    }
    
    /**
     * Get order item count for a table
     */
    public int getTableOrderItemCount(int tableNumber) {
        Order order = getOrderForTable(tableNumber);
        return order != null ? order.getItems().stream()
            .mapToInt(OrderItem::getQuantity).sum() : 0;
    }
    
    private Order createNewOrder(int tableNumber) {
        Order order = new Order();
        order.setTableNumber(tableNumber);
        order.setStatus("NEW");
        order.setTimestamp(LocalDateTime.now());
        System.out.println("OrderManager: Created new order for table " + tableNumber);
        return order;
    }
    
    private void updateTableStatus(int tableNumber, String status) {
        tableOrderStatus.put(tableNumber, status);
        System.out.println("OrderManager: Updated table " + tableNumber + " status to " + status);
    }
    
    /**
     * Initialize with some sample orders for testing
     */
    private void initializeSampleOrders() {
        // This can be removed in production - just for testing
        System.out.println("OrderManager: Initializing sample orders for testing");

        try {
            // Create sample order for Table 4 (to test running order scenario)
            Order sampleOrder = new Order();
            sampleOrder.setTableNumber(4);
            sampleOrder.setStatus("ACTIVE");
            sampleOrder.setTimestamp(LocalDateTime.now());

            // Add sample items
            MenuItem item1 = new MenuItem(1, "Chili Mushroom", 90.0, "Appetizers", 1);
            MenuItem item2 = new MenuItem(2, "Raj Kachori", 100.0, "Snacks", 2);
            MenuItem item3 = new MenuItem(3, "Strawberry Mojito", 150.0, "Beverages", 3);

            sampleOrder.addItem(new OrderItem(item1, 1));
            sampleOrder.addItem(new OrderItem(item2, 1));
            sampleOrder.addItem(new OrderItem(item3, 2));

            // Save to active orders
            activeTableOrders.put(4, sampleOrder);
            updateTableStatus(4, "OCCUPIED");

            System.out.println("OrderManager: ✅ Created sample running order for Table 4 with " +
                             sampleOrder.getItems().size() + " items (Total: ₹" +
                             String.format("%.2f", sampleOrder.calculateTotal()) + ")");

            // Create sample order for Table 8 as well
            Order sampleOrder2 = new Order();
            sampleOrder2.setTableNumber(8);
            sampleOrder2.setStatus("ACTIVE");
            sampleOrder2.setTimestamp(LocalDateTime.now());

            MenuItem item4 = new MenuItem(4, "Dahi Ke Sholay", 80.0, "Snacks", 4);
            sampleOrder2.addItem(new OrderItem(item4, 1));

            activeTableOrders.put(8, sampleOrder2);
            updateTableStatus(8, "OCCUPIED");

            System.out.println("OrderManager: ✅ Created sample running order for Table 8 with " +
                             sampleOrder2.getItems().size() + " items (Total: ₹" +
                             String.format("%.2f", sampleOrder2.calculateTotal()) + ")");

        } catch (Exception e) {
            System.err.println("OrderManager: ❌ Error creating sample orders: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Get all active orders (for management views)
     */
    public Map<Integer, Order> getAllActiveOrders() {
        return new ConcurrentHashMap<>(activeTableOrders);
    }
    
    /**
     * Apply discount to table order
     */
    public void applyDiscountToTable(int tableNumber, double discountPercent) {
        Order order = getOrderForTable(tableNumber);
        if (order != null) {
            // Apply discount logic here
            order.setNotes("Discount: " + discountPercent + "%");
            saveOrderForTable(tableNumber, order);
        }
    }
}
