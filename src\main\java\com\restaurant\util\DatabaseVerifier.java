package com.restaurant.util; 
 
import java.sql.*; 
 
public class DatabaseVerifier { 
    public static void main(String[] args) { 
        System.out.println("🔍 Verifying database contents..."); 
        System.out.println(); 
 
        String url = "*************************"; 
 
        try (Connection conn = DriverManager.getConnection(url)) { 
            System.out.println("✅ Database connection successful"); 
 
            // Check if users table exists 
            DatabaseMetaData meta = conn.getMetaData(); 
            ResultSet tables = meta.getTables(null, null, "users", null); 
            if (tables.next()) { 
                System.out.println("✅ Users table exists"); 
            } else { 
                System.out.println("❌ Users table does not exist"); 
                return; 
            } 
            tables.close(); 
 
            // List all users 
            System.out.println(); 
            System.out.println("📋 ALL USERS IN DATABASE:"); 
            String selectUsers = "SELECT id, username, role, password FROM users"; 
            try (Statement stmt = conn.createStatement(); 
                 ResultSet rs = stmt.executeQuery(selectUsers)) { 
                System.out.println("ID | Username | Role     | Password Hash"); 
                System.out.println("---|----------|----------|---------------"); 
                boolean hasUsers = false; 
                while (rs.next()) { 
                    hasUsers = true; 
                    String passwordHash = rs.getString("password"); 
                    String shortHash = passwordHash.length() > 20 ? passwordHash.substring(0, 20) + "..." : passwordHash; 
                    System.out.printf("d | -8s | n", 
                        rs.getInt("id"), 
                        rs.getString("username"), 
                        rs.getString("role"), 
                        shortHash); 
                } 
 
                if (!hasUsers) { 
                    System.out.println("❌ NO USERS FOUND IN DATABASE!"); 
                    System.out.println("   This is why authentication is failing."); 
                } else { 
                    System.out.println(); 
                    System.out.println("✅ Users found in database"); 
                } 
            } 
 
        } catch (Exception e) { 
            System.err.println("❌ Error: " + e.getMessage()); 
            e.printStackTrace(); 
        } 
    } 
} 
