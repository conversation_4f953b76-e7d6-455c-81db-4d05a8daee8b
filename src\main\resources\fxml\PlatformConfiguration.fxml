<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.PlatformConfigurationController" stylesheets="@../css/application.css">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="module-header">
         <children>
            <Label styleClass="module-title" text="🔧 Platform Configuration">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="saveConfigBtn" onAction="#saveConfiguration" styleClass="primary-button" text="💾 Save Configuration" />
         </children>
         <padding>
            <Insets bottom="20.0" left="25.0" right="25.0" top="20.0" />
         </padding>
      </HBox>
      
      <!-- Configuration Content -->
      <ScrollPane styleClass="universal-scroll-pane" fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="ALWAYS" VBox.vgrow="ALWAYS">
         <content>
            <VBox spacing="30.0" styleClass="settings-content">
               <children>
                  <!-- Platform Selection Section -->
                  <VBox spacing="20.0" styleClass="settings-section">
                     <children>
                        <Label styleClass="section-title" text="Configure on*">
                           <font>
                              <Font name="System Bold" size="16.0" />
                           </font>
                        </Label>
                        
                        <!-- Platform Selection Grid -->
                        <GridPane fx:id="platformGrid" hgap="20.0" vgap="15.0" styleClass="platform-selection-grid">
                           <children>
                              <!-- Row 1 -->
                              <RadioButton fx:id="swiggyRadio" styleClass="platform-radio" GridPane.columnIndex="0" GridPane.rowIndex="0">
                                 <graphic>
                                    <HBox alignment="CENTER" spacing="8.0" styleClass="platform-option swiggy-option">
                                       <children>
                                          <Label text="🍊" styleClass="platform-icon" />
                                          <Label text="Swiggy" styleClass="platform-label" />
                                       </children>
                                    </HBox>
                                 </graphic>
                              </RadioButton>
                              
                              <RadioButton fx:id="zomatoRadio" styleClass="platform-radio" GridPane.columnIndex="1" GridPane.rowIndex="0">
                                 <graphic>
                                    <HBox alignment="CENTER" spacing="8.0" styleClass="platform-option zomato-option">
                                       <children>
                                          <Label text="Z" styleClass="platform-icon zomato-icon" />
                                          <Label text="Zomato" styleClass="platform-label" />
                                       </children>
                                    </HBox>
                                 </graphic>
                              </RadioButton>
                              
                              <RadioButton fx:id="diceRadio" styleClass="platform-radio" GridPane.columnIndex="2" GridPane.rowIndex="0">
                                 <graphic>
                                    <HBox alignment="CENTER" spacing="8.0" styleClass="platform-option dice-option">
                                       <children>
                                          <Label text="🎲" styleClass="platform-icon" />
                                          <Label text="Dice" styleClass="platform-label" />
                                       </children>
                                    </HBox>
                                 </graphic>
                              </RadioButton>
                              
                              <!-- Row 2 -->
                              <RadioButton fx:id="deliverooRadio" styleClass="platform-radio" GridPane.columnIndex="0" GridPane.rowIndex="1">
                                 <graphic>
                                    <HBox alignment="CENTER" spacing="8.0" styleClass="platform-option deliveroo-option">
                                       <children>
                                          <Label text="✓" styleClass="platform-icon deliveroo-icon" />
                                          <Label text="Deliveroo" styleClass="platform-label" />
                                       </children>
                                    </HBox>
                                 </graphic>
                              </RadioButton>
                              
                              <RadioButton fx:id="globalRadio" styleClass="platform-radio" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                 <graphic>
                                    <HBox alignment="CENTER" spacing="8.0" styleClass="platform-option global-option">
                                       <children>
                                          <Label text="🌐" styleClass="platform-icon" />
                                          <Label text="Global" styleClass="platform-label" />
                                       </children>
                                    </HBox>
                                 </graphic>
                              </RadioButton>
                              
                              <RadioButton fx:id="talabatRadio" styleClass="platform-radio" GridPane.columnIndex="2" GridPane.rowIndex="1">
                                 <graphic>
                                    <HBox alignment="CENTER" spacing="8.0" styleClass="platform-option talabat-option">
                                       <children>
                                          <Label text="T" styleClass="platform-icon talabat-icon" />
                                          <Label text="Talabat" styleClass="platform-label" />
                                       </children>
                                    </HBox>
                                 </graphic>
                              </RadioButton>
                           </children>
                           <columnConstraints>
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" prefWidth="150.0" />
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" prefWidth="150.0" />
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" prefWidth="150.0" />
                           </columnConstraints>
                           <rowConstraints>
                              <RowConstraints minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
                              <RowConstraints minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
                           </rowConstraints>
                        </GridPane>
                     </children>
                  </VBox>
                  
                  <!-- Outlet List Section -->
                  <VBox spacing="20.0" styleClass="settings-section">
                     <children>
                        <Label styleClass="section-title" text="Outlet list">
                           <font>
                              <Font name="System Bold" size="16.0" />
                           </font>
                        </Label>
                        
                        <!-- Outlet Checkboxes -->
                        <VBox spacing="12.0" styleClass="outlet-list">
                           <children>
                              <CheckBox fx:id="bakeryBrandCheckbox" styleClass="outlet-checkbox" text="Bakery Brand" />
                              <CheckBox fx:id="chineseBrandCheckbox" styleClass="outlet-checkbox" text="Chinese Brand" />
                              <CheckBox fx:id="thaiFoodBrandCheckbox" styleClass="outlet-checkbox" text="Thai Food Brand" />
                              <CheckBox fx:id="indianBrandCheckbox" styleClass="outlet-checkbox" text="Indian Brand" />
                              <CheckBox fx:id="italianBrandCheckbox" styleClass="outlet-checkbox" text="Italian Brand" />
                              <CheckBox fx:id="mexicanBrandCheckbox" styleClass="outlet-checkbox" text="Mexican Brand" />
                           </children>
                        </VBox>
                     </children>
                  </VBox>
                  
                  <!-- Configuration Options -->
                  <VBox spacing="20.0" styleClass="settings-section">
                     <children>
                        <Label styleClass="section-title" text="⚙️ Configuration Options">
                           <font>
                              <Font name="System Bold" size="16.0" />
                           </font>
                        </Label>
                        
                        <GridPane hgap="20.0" vgap="15.0">
                           <children>
                              <VBox spacing="8.0" GridPane.columnIndex="0" GridPane.rowIndex="0">
                                 <children>
                                    <Label text="API Endpoint" />
                                    <TextField fx:id="apiEndpointField" promptText="https://api.platform.com" />
                                 </children>
                              </VBox>
                              
                              <VBox spacing="8.0" GridPane.columnIndex="1" GridPane.rowIndex="0">
                                 <children>
                                    <Label text="API Key" />
                                    <PasswordField fx:id="apiKeyField" promptText="Enter API Key" />
                                 </children>
                              </VBox>
                              
                              <VBox spacing="8.0" GridPane.columnIndex="0" GridPane.rowIndex="1">
                                 <children>
                                    <Label text="Sync Interval (minutes)" />
                                    <Spinner fx:id="syncIntervalSpinner" min="1" max="60" initialValue="5" prefWidth="150.0" />
                                 </children>
                              </VBox>
                              
                              <VBox spacing="8.0" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                 <children>
                                    <Label text="Timeout (seconds)" />
                                    <Spinner fx:id="timeoutSpinner" min="10" max="300" initialValue="30" prefWidth="150.0" />
                                 </children>
                              </VBox>
                           </children>
                           <columnConstraints>
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="250.0" prefWidth="250.0" />
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="250.0" prefWidth="250.0" />
                           </columnConstraints>
                           <rowConstraints>
                              <RowConstraints minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
                              <RowConstraints minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
                           </rowConstraints>
                        </GridPane>
                        
                        <!-- Additional Options -->
                        <VBox spacing="10.0">
                           <children>
                              <CheckBox fx:id="enableNotificationsCheckbox" selected="true" text="Enable platform notifications" />
                              <CheckBox fx:id="autoAcceptOrdersCheckbox" text="Auto-accept orders (not recommended)" />
                              <CheckBox fx:id="enableLoggingCheckbox" selected="true" text="Enable detailed logging" />
                           </children>
                        </VBox>
                     </children>
                  </VBox>
                  
                  <!-- Action Buttons -->
                  <HBox alignment="CENTER" spacing="15.0" styleClass="action-buttons">
                     <children>
                        <Button fx:id="testConnectionBtn" onAction="#testConnection" styleClass="secondary-button" text="🔗 Test Connection" />
                        <Button fx:id="resetConfigBtn" onAction="#resetConfiguration" styleClass="danger-button" text="🔄 Reset to Default" />
                        <Button fx:id="exportConfigBtn" onAction="#exportConfiguration" styleClass="secondary-button" text="📤 Export Config" />
                        <Button fx:id="importConfigBtn" onAction="#importConfiguration" styleClass="secondary-button" text="📥 Import Config" />
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="30.0" left="25.0" right="25.0" top="10.0" />
               </padding>
            </VBox>
         </content>
      </ScrollPane>
   </children>
</VBox>
