@echo off
echo Fixing CSS border-radius and background-radius issues...

cd /d "e:\restaurant-desktop"

echo Creating backup of original CSS file...
copy "src\main\resources\css\application.css" "src\main\resources\css\application.css.backup"

echo Fixing CSS syntax issues...
powershell -Command "(Get-Content 'src\main\resources\css\application.css') -replace '-fx-border-radius: ([0-9]+);', '-fx-border-radius: $1px;' -replace '-fx-background-radius: ([0-9]+);', '-fx-background-radius: $1px;' | Set-Content 'src\main\resources\css\application.css'"

echo CSS fixes applied successfully!
echo.
echo Starting application...
mvn javafx:run

pause
