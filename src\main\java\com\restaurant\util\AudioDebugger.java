package com.restaurant.util; 
 
public class AudioDebugger { 
    public static void main(String[] args) { 
        System.out.println("🔍 Testing audio system..."); 
        System.out.println(); 
 
        // Test 1: Basic system beep 
        System.out.println("Test 1: Basic system beep"); 
        try { 
            java.awt.Toolkit.getDefaultToolkit().beep(); 
            System.out.println("✅ System beep sent - did you hear it?"); 
            Thread.sleep(2000); 
        } catch (Exception e) { 
            System.out.println("❌ System beep failed: " + e.getMessage()); 
        } 
 
        // Test 2: Multiple beeps 
        System.out.println("Test 2: Multiple beeps (should hear 3 beeps)"); 
        try { 
            for (int i = 0; i < 3; i++) { 
                System.out.println("Beep " + (i + 1)); 
                java.awt.Toolkit.getDefaultToolkit().beep(); 
                Thread.sleep(500); 
            } 
            System.out.println("✅ Multiple beeps sent - did you hear 3 beeps?"); 
        } catch (Exception e) { 
            System.out.println("❌ Multiple beeps failed: " + e.getMessage()); 
        } 
 
        // Test 3: Check audio file 
        System.out.println(); 
        System.out.println("Test 3: Checking MP3 file"); 
        java.io.File audioFile = new java.io.File("sounds/mixkit-urgent-simple-tone-loop-2976.mp3"); 
        if (audioFile.exists()) { 
            System.out.println("✅ MP3 file found: " + audioFile.getAbsolutePath()); 
            System.out.println("   File size: " + audioFile.length() + " bytes"); 
            System.out.println("   Can read: " + audioFile.canRead()); 
        } else { 
            System.out.println("❌ MP3 file not found: " + audioFile.getAbsolutePath()); 
        } 
 
        System.out.println(); 
        System.out.println("🔍 AUDIO DIAGNOSIS COMPLETE"); 
        System.out.println(); 
        System.out.println("TROUBLESHOOTING:"); 
        System.out.println("1. If you heard NO beeps - check system volume/speakers"); 
        System.out.println("2. If you heard beeps but no MP3 - JavaFX Media issue"); 
        System.out.println("3. If MP3 file not found - file path issue"); 
        System.out.println("4. Try playing the MP3 file manually with Windows Media Player"); 
    } 
} 
