-- Business Configuration Table
-- Stores business rules and operational settings

CREATE TABLE IF NOT EXISTS business_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key TEXT NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default business configuration values
INSERT OR IGNORE INTO business_config (config_key, config_value, description) VALUES 
('day_end_time', '02:00', 'Day-end time for business day calculation (HH:mm format)'),
('business_start_time', '18:00', 'Business start time (HH:mm format)'),
('business_end_time', '02:00', 'Business end time (HH:mm format)'),
('hold_kot_before_billing', 'false', 'Whether to hold KOTs before billing'),
('restrict_billing_without_kot', 'true', 'Restrict billing if KOT is not generated'),
('kot_hold_warning_minutes', '15', 'Minutes after which to warn about long-held KOTs'),
('custom_bill_footer', 'Thank you for dining with us! Visit again soon.', 'Custom footer text for printed bills'),
('enable_kot_notifications', 'true', 'Enable notifications for long-held KOTs');

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_business_config_key ON business_config(config_key);
