<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.AdvancedInventoryController" stylesheets="@../css/application.css">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="page-header">
         <children>
            <Button fx:id="backButton" onAction="#goBack" styleClass="back-button" text="✕ Close" />
            <Label styleClass="page-title" text="Advanced Inventory Management">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label styleClass="page-subtitle" text="Purchase Orders &amp; Internal Transfers" />
         </children>
         <padding>
            <Insets bottom="10.0" left="15.0" right="15.0" top="10.0" />
         </padding>
      </HBox>
      
      <!-- Main Content -->
      <ScrollPane styleClass="universal-scroll-pane" fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED" VBox.vgrow="ALWAYS">
         <content>
            <VBox spacing="15.0">
               <children>
                  <!-- Purchase Orders Section -->
                  <VBox styleClass="inventory-section">
                     <children>
                        <!-- Purchase Orders Header -->
                        <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="section-header">
                           <children>
                              <HBox alignment="CENTER_LEFT" spacing="8.0">
                                 <children>
                                    <Label styleClass="section-icon" text="🏪" />
                                    <Label styleClass="section-title" text="Purchase order">
                                       <font>
                                          <Font name="System Bold" size="14.0" />
                                       </font>
                                    </Label>
                                 </children>
                              </HBox>

                              <Region HBox.hgrow="ALWAYS" />

                              <!-- Date Range Controls -->
                              <HBox alignment="CENTER_RIGHT" spacing="8.0">
                                 <children>
                                    <DatePicker fx:id="purchaseStartDate" promptText="Start Date" styleClass="date-picker-compact" />
                                    <DatePicker fx:id="purchaseEndDate" promptText="End Date" styleClass="date-picker-compact" />
                                    <Button fx:id="addPurchaseOrderBtn" onAction="#addPurchaseOrder" styleClass="primary-button-compact" text="+ Add Order" />
                                    <Button fx:id="refreshPurchaseBtn" onAction="#refreshPurchaseOrders" styleClass="secondary-button-compact" text="Refresh" />
                                 </children>
                              </HBox>
                           </children>
                           <padding>
                              <Insets bottom="8.0" left="15.0" right="15.0" top="8.0" />
                           </padding>
                        </HBox>
                        
                        <!-- Purchase Orders Table -->
                        <VBox fx:id="purchaseOrdersContainer" styleClass="inventory-table-container">
                           <children>
                              <!-- Purchase order rows will be dynamically added here -->
                           </children>
                        </VBox>
                     </children>
                  </VBox>
                  
                  <!-- Internal Transfers Section -->
                  <VBox styleClass="inventory-section">
                     <children>
                        <!-- Internal Transfers Header -->
                        <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="section-header">
                           <children>
                              <HBox alignment="CENTER_LEFT" spacing="8.0">
                                 <children>
                                    <Label styleClass="section-icon" text="🔄" />
                                    <Label styleClass="section-title" text="Internal Transfer">
                                       <font>
                                          <Font name="System Bold" size="14.0" />
                                       </font>
                                    </Label>
                                 </children>
                              </HBox>

                              <Region HBox.hgrow="ALWAYS" />

                              <!-- Date Range Controls -->
                              <HBox alignment="CENTER_RIGHT" spacing="8.0">
                                 <children>
                                    <DatePicker fx:id="transferStartDate" promptText="Start Date" styleClass="date-picker-compact" />
                                    <DatePicker fx:id="transferEndDate" promptText="End Date" styleClass="date-picker-compact" />
                                    <Button fx:id="addInternalTransferBtn" onAction="#addInternalTransfer" styleClass="primary-button-compact" text="+ Add Transfer" />
                                    <Button fx:id="refreshTransferBtn" onAction="#refreshInternalTransfers" styleClass="secondary-button-compact" text="Refresh" />
                                 </children>
                              </HBox>
                           </children>
                           <padding>
                              <Insets bottom="8.0" left="15.0" right="15.0" top="8.0" />
                           </padding>
                        </HBox>
                        
                        <!-- Internal Transfers Table -->
                        <VBox fx:id="internalTransfersContainer" styleClass="inventory-table-container">
                           <children>
                              <!-- Internal transfer rows will be dynamically added here -->
                           </children>
                        </VBox>
                        
                        <!-- Central Kitchen Diagram -->
                        <HBox alignment="CENTER" styleClass="diagram-section">
                           <children>
                              <VBox alignment="CENTER" spacing="8.0" styleClass="location-diagram">
                                 <children>
                                    <!-- Location connections diagram -->
                                    <HBox alignment="CENTER" spacing="20.0">
                                       <children>
                                          <!-- Vastrapur -->
                                          <VBox alignment="CENTER" spacing="3.0" styleClass="location-node-compact">
                                             <children>
                                                <Label styleClass="location-icon-compact" text="🏪" />
                                                <Label styleClass="location-name-compact" text="Vastrapur" />
                                             </children>
                                          </VBox>

                                          <!-- Arrow -->
                                          <Label styleClass="connection-arrow-compact" text="↔" />

                                          <!-- Central Kitchen -->
                                          <VBox alignment="CENTER" spacing="3.0" styleClass="location-node-compact central-kitchen">
                                             <children>
                                                <Label styleClass="location-icon-compact" text="🏭" />
                                                <Label styleClass="location-name-compact" text="Central Kitchen" />
                                             </children>
                                          </VBox>

                                          <!-- Arrow -->
                                          <Label styleClass="connection-arrow-compact" text="↔" />

                                          <!-- Makaraba -->
                                          <VBox alignment="CENTER" spacing="3.0" styleClass="location-node-compact">
                                             <children>
                                                <Label styleClass="location-icon-compact" text="🏪" />
                                                <Label styleClass="location-name-compact" text="Makaraba" />
                                             </children>
                                          </VBox>
                                       </children>
                                    </HBox>

                                    <!-- Connection lines to warehouse -->
                                    <VBox alignment="CENTER" spacing="3.0">
                                       <children>
                                          <Label styleClass="connection-arrow-compact" text="↕" />
                                          <VBox alignment="CENTER" spacing="3.0" styleClass="location-node-compact">
                                             <children>
                                                <Label styleClass="location-icon-compact" text="📦" />
                                                <Label styleClass="location-name-compact" text="Warehouse" />
                                             </children>
                                          </VBox>
                                       </children>
                                    </VBox>
                                 </children>
                                 <padding>
                                    <Insets bottom="10.0" left="15.0" right="15.0" top="10.0" />
                                 </padding>
                              </VBox>
                           </children>
                           <padding>
                              <Insets bottom="10.0" left="15.0" right="15.0" top="5.0" />
                           </padding>
                        </HBox>
                     </children>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="15.0" left="15.0" right="15.0" top="5.0" />
               </padding>
            </VBox>
         </content>
      </ScrollPane>
   </children>
</VBox>
