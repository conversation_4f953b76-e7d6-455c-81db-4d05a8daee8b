package com.restaurant.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for UniversalNavigationManager ESC key functionality
 */
public class UniversalNavigationManagerTest {
    
    private UniversalNavigationManager navigationManager;
    
    @BeforeEach
    void setUp() {
        navigationManager = UniversalNavigationManager.getInstance();
    }
    
    @Test
    void testSingletonInstance() {
        UniversalNavigationManager instance1 = UniversalNavigationManager.getInstance();
        UniversalNavigationManager instance2 = UniversalNavigationManager.getInstance();
        
        assertSame(instance1, instance2, "UniversalNavigationManager should be a singleton");
    }
    
    @Test
    void testDoubleEscThreshold() {
        // This test verifies the double ESC timing logic
        // Note: This is a simplified test since we can't easily test JavaFX UI in unit tests
        
        long threshold = 800; // Expected threshold in milliseconds
        
        // Simulate first ESC press
        long firstPress = System.currentTimeMillis();
        
        // Simulate second ESC press within threshold
        long secondPress = firstPress + 500; // Within 800ms threshold
        
        assertTrue(secondPress - firstPress < threshold, 
                  "Second ESC press should be within threshold for double ESC detection");
        
        // Simulate second ESC press outside threshold
        long thirdPress = firstPress + 1000; // Outside 800ms threshold
        
        assertFalse(thirdPress - firstPress < threshold, 
                   "Third ESC press should be outside threshold for single ESC detection");
    }
    
    @Test
    void testNavigationManagerNotNull() {
        assertNotNull(navigationManager, "UniversalNavigationManager instance should not be null");
    }
    
    // Note: Full UI testing would require JavaFX Application Thread and actual scenes
    // For comprehensive testing, run the application and test manually:
    // 1. Navigate to different views
    // 2. Press ESC once - should go back
    // 3. Press ESC twice quickly - should go to dashboard
}
