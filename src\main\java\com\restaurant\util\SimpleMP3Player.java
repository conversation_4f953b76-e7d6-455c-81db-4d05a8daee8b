import javafx.application.Application; 
import javafx.scene.media.Media; 
import javafx.scene.media.MediaPlayer; 
import javafx.stage.Stage; 
import java.io.File; 
 
public class SimpleMP3Player extends Application { 
 
    @Override 
    public void start(Stage primaryStage) { 
        System.out.println("🎵 Testing your MP3 file..."); 
        playMP3(); 
    } 
 
    private void playMP3() { 
        try { 
            File audioFile = new File("sounds/mixkit-urgent-simple-tone-loop-2976.mp3"); 
            if (!audioFile.exists()) { 
                System.out.println("❌ MP3 file not found: " + audioFile.getAbsolutePath()); 
                System.exit(1); 
                return; 
            } 
 
            System.out.println("✅ Found MP3 file: " + audioFile.getAbsolutePath()); 
            System.out.println("🟠 Playing as Swiggy notification..."); 
 
            Media media = new Media(audioFile.toURI().toString()); 
            MediaPlayer player = new MediaPlayer(media); 
            player.setVolume(1.0); 
 
            player.setOnReady(new Runnable() { 
                public void run() { 
                    System.out.println("🎵 Playing Swiggy notification sound..."); 
                    player.play(); 
                } 
            }); 
 
            player.setOnEndOfMedia(new Runnable() { 
                public void run() { 
                    System.out.println("✅ Swiggy sound finished"); 
                    player.dispose(); 
                    playZomatoSound(); 
                } 
            }); 
 
            player.setOnError(new Runnable() { 
                public void run() { 
                    System.out.println("❌ Error playing MP3: " + player.getError()); 
                    System.exit(1); 
                } 
            }); 
 
        } catch (Exception e) { 
            System.out.println("❌ Exception: " + e.getMessage()); 
            e.printStackTrace(); 
        } 
    } 
 
    private void playZomatoSound() { 
        try { 
            File audioFile = new File("sounds/mixkit-urgent-simple-tone-loop-2976.mp3"); 
            System.out.println("🔴 Playing as Zomato notification..."); 
 
            Media media = new Media(audioFile.toURI().toString()); 
            MediaPlayer player = new MediaPlayer(media); 
            player.setVolume(1.0); 
 
            player.setOnReady(new Runnable() { 
                public void run() { 
                    System.out.println("🎵 Playing Zomato notification sound..."); 
                    player.play(); 
                } 
            }); 
 
            player.setOnEndOfMedia(new Runnable() { 
                public void run() { 
                    System.out.println("✅ Zomato sound finished"); 
                    System.out.println("🎉 MP3 notification test complete!"); 
                    player.dispose(); 
                    System.exit(0); 
                } 
            }); 
 
            player.setOnError(new Runnable() { 
                public void run() { 
                    System.out.println("❌ Error playing Zomato MP3: " + player.getError()); 
                    System.exit(1); 
                } 
            }); 
 
        } catch (Exception e) { 
            System.out.println("❌ Exception in Zomato sound: " + e.getMessage()); 
            e.printStackTrace(); 
        } 
    } 
 
    public static void main(String[] args) { 
        launch(args); 
    } 
} 
