package com.restaurant.model;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.LocalDateTime;
import java.time.Duration;

public class Attendance {
    private int id;
    private int employeeId;
    private String employeeName;
    private String department;
    private LocalDate attendanceDate;
    private LocalTime checkInTime;
    private LocalTime checkOutTime;
    private String status; // "Present", "Absent", "Late", "Half Day", "On Leave"
    private double hoursWorked;
    private String notes;
    private LocalDateTime createdAt;
    private LocalDateTime lastUpdated;
    
    // Constructors
    public Attendance() {
        this.attendanceDate = LocalDate.now();
        this.createdAt = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
        this.status = "Present";
    }
    
    public Attendance(int employeeId, String employeeName, String department, LocalDate attendanceDate) {
        this.employeeId = employeeId;
        this.employeeName = employeeName;
        this.department = department;
        this.attendanceDate = attendanceDate;
        this.createdAt = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
        this.status = "Present";
    }
    
    public Attendance(int id, int employeeId, String employeeName, String department, LocalDate attendanceDate, 
                     LocalTime checkInTime, LocalTime checkOutTime, String status) {
        this.id = id;
        this.employeeId = employeeId;
        this.employeeName = employeeName;
        this.department = department;
        this.attendanceDate = attendanceDate;
        this.checkInTime = checkInTime;
        this.checkOutTime = checkOutTime;
        this.status = status;
        this.createdAt = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
        calculateHoursWorked();
    }
    
    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public int getEmployeeId() { return employeeId; }
    public void setEmployeeId(int employeeId) { 
        this.employeeId = employeeId;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public String getEmployeeName() { return employeeName; }
    public void setEmployeeName(String employeeName) { 
        this.employeeName = employeeName;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public String getDepartment() { return department; }
    public void setDepartment(String department) { 
        this.department = department;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public LocalDate getAttendanceDate() { return attendanceDate; }
    public void setAttendanceDate(LocalDate attendanceDate) { 
        this.attendanceDate = attendanceDate;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public LocalTime getCheckInTime() { return checkInTime; }
    public void setCheckInTime(LocalTime checkInTime) { 
        this.checkInTime = checkInTime;
        this.lastUpdated = LocalDateTime.now();
        calculateHoursWorked();
    }
    
    public LocalTime getCheckOutTime() { return checkOutTime; }
    public void setCheckOutTime(LocalTime checkOutTime) { 
        this.checkOutTime = checkOutTime;
        this.lastUpdated = LocalDateTime.now();
        calculateHoursWorked();
    }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { 
        this.status = status;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public double getHoursWorked() { return hoursWorked; }
    public void setHoursWorked(double hoursWorked) { 
        this.hoursWorked = hoursWorked;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { 
        this.notes = notes;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getLastUpdated() { return lastUpdated; }
    public void setLastUpdated(LocalDateTime lastUpdated) { this.lastUpdated = lastUpdated; }
    
    // Helper methods
    public void calculateHoursWorked() {
        if (checkInTime != null && checkOutTime != null) {
            Duration duration = Duration.between(checkInTime, checkOutTime);
            this.hoursWorked = duration.toMinutes() / 60.0;
        } else {
            this.hoursWorked = 0.0;
        }
    }
    
    public String getCheckInTimeDisplay() {
        return checkInTime != null ? checkInTime.toString() : "-";
    }
    
    public String getCheckOutTimeDisplay() {
        return checkOutTime != null ? checkOutTime.toString() : "-";
    }
    
    public String getHoursWorkedDisplay() {
        if (hoursWorked <= 0) return "0.0";
        return String.format("%.1f", hoursWorked);
    }
    
    public String getStatusIcon() {
        if (status == null) return "⚪";
        
        switch (status) {
            case "Present": return "🟢";
            case "Late": return "🟡";
            case "Absent": return "🔴";
            case "Half Day": return "🟠";
            case "On Leave": return "🔵";
            default: return "⚪";
        }
    }
    
    public String getDepartmentIcon() {
        if (department == null) return "👤";
        
        switch (department.toLowerCase()) {
            case "kitchen": return "👨‍🍳";
            case "service": return "👨‍💼";
            case "management": return "👔";
            case "cleaning": return "🧹";
            case "security": return "🛡️";
            default: return "👤";
        }
    }
    
    public boolean isPresent() {
        return "Present".equals(status) || "Late".equals(status);
    }
    
    public boolean isLate() {
        return "Late".equals(status);
    }
    
    public boolean isAbsent() {
        return "Absent".equals(status);
    }
    
    public boolean isOnLeave() {
        return "On Leave".equals(status);
    }
    
    public boolean isHalfDay() {
        return "Half Day".equals(status);
    }
    
    public String getAttendanceSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(getStatusIcon()).append(" ").append(status);
        
        if (isPresent()) {
            summary.append(" (").append(getHoursWorkedDisplay()).append(" hrs)");
        }
        
        if (isLate() && checkInTime != null) {
            summary.append(" - Late by ").append(calculateLateMinutes()).append(" mins");
        }
        
        return summary.toString();
    }
    
    private int calculateLateMinutes() {
        if (checkInTime == null) return 0;
        
        LocalTime standardStartTime = LocalTime.of(9, 0); // 9:00 AM
        if (checkInTime.isAfter(standardStartTime)) {
            return (int) Duration.between(standardStartTime, checkInTime).toMinutes();
        }
        return 0;
    }
    
    public void markPresent(LocalTime checkIn, LocalTime checkOut) {
        this.checkInTime = checkIn;
        this.checkOutTime = checkOut;
        this.status = "Present";
        calculateHoursWorked();
        this.lastUpdated = LocalDateTime.now();
    }
    
    public void markLate(LocalTime checkIn, LocalTime checkOut) {
        this.checkInTime = checkIn;
        this.checkOutTime = checkOut;
        this.status = "Late";
        calculateHoursWorked();
        this.lastUpdated = LocalDateTime.now();
    }
    
    public void markAbsent() {
        this.checkInTime = null;
        this.checkOutTime = null;
        this.status = "Absent";
        this.hoursWorked = 0.0;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public void markOnLeave() {
        this.checkInTime = null;
        this.checkOutTime = null;
        this.status = "On Leave";
        this.hoursWorked = 0.0;
        this.lastUpdated = LocalDateTime.now();
    }
    
    @Override
    public String toString() {
        return employeeName + " - " + attendanceDate + " (" + status + ")";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Attendance attendance = (Attendance) obj;
        return id == attendance.id;
    }
    
    @Override
    public int hashCode() {
        return Integer.hashCode(id);
    }
}
