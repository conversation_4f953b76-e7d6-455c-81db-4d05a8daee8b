package com.restaurant.model;

import java.sql.*;

/**
 * Test class to verify the direct database connection approach
 * that fixes the OrderDAO hanging issue
 */
public class TestDatabaseConnection {
    
    public static void main(String[] args) {
        System.out.println("Testing Direct Database Connection (OrderDAO Fix)...");
        System.out.println("=".repeat(60));
        
        try {
            // Test 1: Direct connection (the fix) - JDBC should auto-load SQLite driver
            System.out.println("Test 1: Creating direct database connection...");
            String dbUrl = "*************************";
            Connection conn = DriverManager.getConnection(dbUrl);
            System.out.println("✅ Direct database connection successful");

            // Test 3: Check if we can query basic info
            System.out.println("\nTest 3: Testing basic database operations...");
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery("SELECT 1 as test");
            if (rs.next()) {
                System.out.println("✅ Basic query execution successful");
            }
            
            // Test 4: Check table existence (like OrderDAO does)
            System.out.println("\nTest 4: Testing table existence checking...");
            DatabaseMetaData meta = conn.getMetaData();
            ResultSet tables = meta.getTables(null, null, "orders", null);
            if (tables.next()) {
                System.out.println("✅ Orders table exists");
            } else {
                System.out.println("ℹ️  Orders table does not exist (this is OK for new installations)");
            }
            
            // Test 5: Test the problematic DatabaseManager approach
            System.out.println("\nTest 5: Testing DatabaseManager approach (the problematic one)...");
            try {
                Connection dmConn = DatabaseManager.getConnection();
                System.out.println("✅ DatabaseManager connection successful (initialization completed)");
                dmConn.close();
            } catch (Exception e) {
                System.out.println("❌ DatabaseManager connection failed: " + e.getMessage());
                System.out.println("   This confirms why we needed the direct connection fix");
            }
            
            // Clean up
            rs.close();
            stmt.close();
            conn.close();
            
            System.out.println("\n" + "=".repeat(60));
            System.out.println("DATABASE CONNECTION TEST RESULTS:");
            System.out.println("✅ Direct connection approach works (this fixes OrderDAO hanging)");
            System.out.println("✅ Basic database operations functional");
            System.out.println("✅ Table existence checking works");
            System.out.println("\nCONCLUSION:");
            System.out.println("The OrderDAO fix should resolve the OrderManagement hanging issue.");
            System.out.println("OrderManagement should now load without hanging at 'Starting database query...'");
            
        } catch (SQLException e) {
            System.err.println("❌ Database connection test failed:");
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
            
            System.out.println("\nTROUBLESHOOTING:");
            System.out.println("1. Make sure SQLite JDBC driver is in classpath");
            System.out.println("2. Check if restaurant.db file permissions are correct");
            System.out.println("3. Ensure no other process is locking the database");
            
        } catch (Exception e) {
            System.err.println("❌ Unexpected error during database test:");
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
