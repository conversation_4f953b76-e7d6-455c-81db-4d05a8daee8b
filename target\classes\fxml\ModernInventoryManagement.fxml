<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/17.0.2-ea" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.ModernInventoryController" styleClass="modern-inventory-root">
   <top>
      <!-- Modern Header Section -->
      <VBox styleClass="modern-inventory-header">
         <children>
            <!-- Title and Actions Row -->
            <HBox alignment="CENTER_LEFT" spacing="25.0" styleClass="header-main-row">
               <children>
                  <Button fx:id="backButton" mnemonicParsing="false" onAction="#goBack" styleClass="modern-back-button" text="← Back" />
                  <VBox spacing="4.0">
                     <children>
                        <Label styleClass="modern-page-title" text="📦 Inventory Management">
                           <font>
                              <Font name="System Bold" size="28.0" />
                           </font>
                        </Label>
                        <Label styleClass="modern-page-subtitle" text="Track, manage and optimize your kitchen inventory" />
                     </children>
                  </VBox>
                  <Region HBox.hgrow="ALWAYS" />
                  <HBox spacing="12.0" alignment="CENTER_RIGHT">
                     <children>
                        <Button fx:id="refreshButton" mnemonicParsing="false" onAction="#refreshInventory" styleClass="modern-refresh-button" text="🔄 Refresh" />
                        <Button fx:id="addItemButton" mnemonicParsing="false" onAction="#addNewItem" styleClass="modern-add-button" text="➕ Add Item" />
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="20.0" left="25.0" right="25.0" top="25.0" />
               </padding>
            </HBox>
            
            <!-- Search and Filter Row -->
            <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="header-filter-row">
               <children>
                  <HBox spacing="8.0" alignment="CENTER_LEFT" styleClass="search-container">
                     <children>
                        <Label text="🔍" styleClass="search-icon" />
                        <TextField fx:id="searchField" promptText="Search inventory items..." styleClass="modern-search-field" prefWidth="300.0" />
                     </children>
                  </HBox>
                  <ComboBox fx:id="categoryFilter" promptText="All Categories" styleClass="modern-filter-combo" prefWidth="150.0" />
                  <ComboBox fx:id="statusFilter" promptText="All Status" styleClass="modern-filter-combo" prefWidth="130.0" />
                  <ComboBox fx:id="sortFilter" promptText="Sort by Name" styleClass="modern-filter-combo" prefWidth="140.0" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Label fx:id="itemCountLabel" text="0 items" styleClass="item-count-label" />
               </children>
               <padding>
                  <Insets bottom="20.0" left="25.0" right="25.0" top="5.0" />
               </padding>
            </HBox>
            
            <!-- Modern Tab Navigation -->
            <HBox styleClass="modern-inventory-tabs" spacing="0.0">
               <children>
                  <Button fx:id="ingredientsTab" mnemonicParsing="false" onAction="#showIngredients" styleClass="modern-tab-button,modern-tab-active" text="📋 Ingredients" />
                  <Button fx:id="stockAlertsTab" mnemonicParsing="false" onAction="#showStockAlerts" styleClass="modern-tab-button" text="⚠️ Stock Alerts" />
                  <Button fx:id="wastageLogTab" mnemonicParsing="false" onAction="#showWastageLog" styleClass="modern-tab-button" text="🗑️ Wastage Log" />
                  <Button fx:id="suppliersTab" mnemonicParsing="false" onAction="#showSuppliers" styleClass="modern-tab-button" text="🏪 Suppliers" />
               </children>
               <padding>
                  <Insets bottom="0.0" left="25.0" right="25.0" top="0.0" />
               </padding>
            </HBox>
         </children>
      </VBox>
   </top>
   
   <center>
      <!-- Main Content Area with Loading Indicator -->
      <StackPane fx:id="contentArea" styleClass="modern-content-area">
         <children>
            <!-- Loading Indicator -->
            <VBox fx:id="loadingIndicator" alignment="CENTER" spacing="15.0" styleClass="loading-container" visible="false">
               <children>
                  <ProgressIndicator styleClass="modern-progress" />
                  <Label text="Loading inventory data..." styleClass="loading-text" />
               </children>
            </VBox>
            
            <!-- Ingredients View -->
            <ScrollPane fx:id="ingredientsView" fitToWidth="true" styleClass="modern-scroll-pane" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED">
               <content>
                  <VBox spacing="0.0" styleClass="modern-content-container">
                     <children>
                        <!-- Stats Cards Row -->
                        <HBox spacing="20.0" styleClass="stats-cards-container">
                           <children>
                              <VBox styleClass="stats-card,stats-card-primary" spacing="8.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                       <children>
                                          <Label text="📦" styleClass="stats-icon" />
                                          <Label text="Total Items" styleClass="stats-label" />
                                       </children>
                                    </HBox>
                                    <Label fx:id="totalItemsLabel" text="0" styleClass="stats-value" />
                                 </children>
                              </VBox>
                              <VBox styleClass="stats-card,stats-card-warning" spacing="8.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                       <children>
                                          <Label text="⚠️" styleClass="stats-icon" />
                                          <Label text="Low Stock" styleClass="stats-label" />
                                       </children>
                                    </HBox>
                                    <Label fx:id="lowStockLabel" text="0" styleClass="stats-value" />
                                 </children>
                              </VBox>
                              <VBox styleClass="stats-card,stats-card-danger" spacing="8.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                       <children>
                                          <Label text="❌" styleClass="stats-icon" />
                                          <Label text="Out of Stock" styleClass="stats-label" />
                                       </children>
                                    </HBox>
                                    <Label fx:id="outOfStockLabel" text="0" styleClass="stats-value" />
                                 </children>
                              </VBox>
                              <VBox styleClass="stats-card,stats-card-success" spacing="8.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                       <children>
                                          <Label text="✅" styleClass="stats-icon" />
                                          <Label text="In Stock" styleClass="stats-label" />
                                       </children>
                                    </HBox>
                                    <Label fx:id="inStockLabel" text="0" styleClass="stats-value" />
                                 </children>
                              </VBox>
                           </children>
                           <padding>
                              <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
                           </padding>
                        </HBox>
                        
                        <!-- Inventory Items Table -->
                        <TableView fx:id="inventoryTable" styleClass="modern-table-view" VBox.vgrow="ALWAYS">
                           <columns>
                              <TableColumn fx:id="itemIconColumn" prefWidth="50.0" text="" />
                              <TableColumn fx:id="itemNameColumn" prefWidth="200.0" text="Item Name" />
                              <TableColumn fx:id="categoryColumn" prefWidth="120.0" text="Category" />
                              <TableColumn fx:id="quantityColumn" prefWidth="100.0" text="Quantity" />
                              <TableColumn fx:id="unitColumn" prefWidth="80.0" text="Unit" />
                              <TableColumn fx:id="statusColumn" prefWidth="120.0" text="Status" />
                              <TableColumn fx:id="supplierColumn" prefWidth="150.0" text="Supplier" />
                              <TableColumn fx:id="lastUpdatedColumn" prefWidth="130.0" text="Last Updated" />
                              <TableColumn fx:id="actionsColumn" prefWidth="120.0" text="Actions" />
                           </columns>
                           <VBox.margin>
                              <Insets bottom="25.0" left="25.0" right="25.0" top="0.0" />
                           </VBox.margin>
                        </TableView>
                     </children>
                  </VBox>
               </content>
            </ScrollPane>
            
            <!-- Stock Alerts View -->
            <ScrollPane fx:id="stockAlertsView" fitToWidth="true" styleClass="modern-scroll-pane" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED" visible="false">
               <content>
                  <VBox fx:id="stockAlertsContainer" spacing="15.0" styleClass="modern-content-container">
                     <padding>
                        <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
                     </padding>
                  </VBox>
               </content>
            </ScrollPane>
            
            <!-- Wastage Log View -->
            <ScrollPane fx:id="wastageLogView" fitToWidth="true" styleClass="modern-scroll-pane" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED" visible="false">
               <content>
                  <VBox fx:id="wastageLogContainer" spacing="15.0" styleClass="modern-content-container">
                     <padding>
                        <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
                     </padding>
                  </VBox>
               </content>
            </ScrollPane>
            
            <!-- Suppliers View -->
            <ScrollPane fx:id="suppliersView" fitToWidth="true" styleClass="modern-scroll-pane" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED" visible="false">
               <content>
                  <VBox fx:id="suppliersContainer" spacing="15.0" styleClass="modern-content-container">
                     <padding>
                        <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
                     </padding>
                  </VBox>
               </content>
            </ScrollPane>
         </children>
      </StackPane>
   </center>
   
   <!-- Add Item Dialog -->
   <StackPane fx:id="addItemDialog" styleClass="modal-overlay" visible="false">
      <children>
         <VBox styleClass="modern-modal-dialog" spacing="20.0" maxWidth="600.0" maxHeight="700.0">
            <children>
               <!-- Dialog Header -->
               <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="modal-header">
                  <children>
                     <Label fx:id="dialogTitle" styleClass="modal-title" text="➕ Add New Inventory Item">
                        <font>
                           <Font name="System Bold" size="20.0" />
                        </font>
                     </Label>
                     <Region HBox.hgrow="ALWAYS" />
                     <Button mnemonicParsing="false" onAction="#hideAddItemDialog" styleClass="modal-close-button" text="✕" />
                  </children>
               </HBox>
               
               <!-- Form Content -->
               <ScrollPane fitToWidth="true" styleClass="form-scroll" VBox.vgrow="ALWAYS">
                  <content>
                     <VBox spacing="20.0" styleClass="form-container">
                        <children>
                           <!-- Basic Information -->
                           <VBox spacing="15.0">
                              <children>
                                 <Label styleClass="section-title" text="📋 Basic Information" />
                                 <HBox spacing="15.0">
                                    <children>
                                       <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                          <children>
                                             <Label text="Item Name *" styleClass="field-label" />
                                             <TextField fx:id="itemNameField" promptText="Enter item name" styleClass="modern-form-field" />
                                          </children>
                                       </VBox>
                                       <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                          <children>
                                             <Label text="Category *" styleClass="field-label" />
                                             <ComboBox fx:id="categoryCombo" promptText="Select category" styleClass="modern-form-field" maxWidth="Infinity" />
                                          </children>
                                       </VBox>
                                    </children>
                                 </HBox>
                              </children>
                           </VBox>
                           
                           <!-- Quantity Information -->
                           <VBox spacing="15.0">
                              <children>
                                 <Label styleClass="section-title" text="📊 Quantity Information" />
                                 <HBox spacing="15.0">
                                    <children>
                                       <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                          <children>
                                             <Label text="Current Quantity *" styleClass="field-label" />
                                             <TextField fx:id="quantityField" promptText="Enter quantity" styleClass="modern-form-field" />
                                          </children>
                                       </VBox>
                                       <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                          <children>
                                             <Label text="Unit *" styleClass="field-label" />
                                             <ComboBox fx:id="unitCombo" promptText="Select unit" styleClass="modern-form-field" maxWidth="Infinity" />
                                          </children>
                                       </VBox>
                                       <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                          <children>
                                             <Label text="Minimum Threshold" styleClass="field-label" />
                                             <TextField fx:id="thresholdField" promptText="Min quantity" styleClass="modern-form-field" />
                                          </children>
                                       </VBox>
                                    </children>
                                 </HBox>
                              </children>
                           </VBox>
                           
                           <!-- Supplier Information -->
                           <VBox spacing="15.0">
                              <children>
                                 <Label styleClass="section-title" text="🏪 Supplier Information" />
                                 <VBox spacing="8.0">
                                    <children>
                                       <Label text="Supplier" styleClass="field-label" />
                                       <ComboBox fx:id="supplierCombo" promptText="Select supplier" styleClass="modern-form-field" maxWidth="Infinity" />
                                    </children>
                                 </VBox>
                              </children>
                           </VBox>
                        </children>
                        <padding>
                           <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
                        </padding>
                     </VBox>
                  </content>
               </ScrollPane>
               
               <!-- Dialog Footer -->
               <HBox alignment="CENTER_RIGHT" spacing="12.0" styleClass="modal-footer">
                  <children>
                     <Button mnemonicParsing="false" onAction="#hideAddItemDialog" styleClass="modern-secondary-button" text="Cancel" />
                     <Button mnemonicParsing="false" onAction="#saveInventoryItem" styleClass="modern-primary-button" text="Save Item" />
                  </children>
               </HBox>
            </children>
            <padding>
               <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
            </padding>
         </VBox>
      </children>
   </StackPane>
</BorderPane>
