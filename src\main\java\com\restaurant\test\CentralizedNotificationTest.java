package com.restaurant.test; 
 
import com.restaurant.util.CentralizedNotificationManager; 
import com.restaurant.model.OnlineOrder; 
import java.time.LocalDateTime; 
import java.util.Scanner; 
 
public class CentralizedNotificationTest { 
    public static void main(String[] args) { 
        System.out.println("🔔 TESTING CENTRALIZED NOTIFICATION SYSTEM"); 
        System.out.println(); 
 
        CentralizedNotificationManager notificationManager = 
            CentralizedNotificationManager.getInstance(); 
        Scanner scanner = new Scanner(System.in); 
 
        try { 
            System.out.println("🟠 Testing Swiggy Order Notification..."); 
            OnlineOrder swiggyOrder = createTestOrder("SWIGGY001", OnlineOrder.Platform.SWIGGY); 
            notificationManager.notifyNewSwiggyOrder(swiggyOrder); 
            System.out.println("✅ Swiggy notification sent (with continuous ringing)"); 
            System.out.println(); 
 
            Thread.sleep(3000); 
 
            System.out.println("🔴 Testing Zomato Order Notification..."); 
            OnlineOrder zomatoOrder = createTestOrder("ZOMATO001", OnlineOrder.Platform.ZOMATO); 
            notificationManager.notifyNewZomatoOrder(zomatoOrder); 
            System.out.println("✅ Zomato notification sent (with continuous ringing)"); 
            System.out.println(); 
 
            Thread.sleep(3000); 
 
            System.out.println("✅ Testing Order Acceptance (stops ringing)..."); 
            notificationManager.notifyOrderAccepted("SWIGGY001", "Swiggy"); 
            System.out.println("✅ Swiggy order accepted - ringing stopped"); 
            System.out.println(); 
 
            Thread.sleep(2000); 
 
            System.out.println("🍽️ Testing Order Ready Notification..."); 
            notificationManager.notifyOrderReady("SWIGGY001", "John Doe"); 
            System.out.println("✅ Order ready notification sent"); 
            System.out.println(); 
 
            Thread.sleep(2000); 
 
            System.out.println("💰 Testing Order Pricing Notification..."); 
            notificationManager.notifyOrderPricing("SWIGGY001"); 
            System.out.println("✅ Order pricing notification sent"); 
            System.out.println(); 
 
            Thread.sleep(2000); 
 
            System.out.println("📦 Testing Order Completion Notification..."); 
            notificationManager.notifyOrderCompleted("SWIGGY001"); 
            System.out.println("✅ Order completion notification sent"); 
            System.out.println(); 
 
            Thread.sleep(2000); 
 
            System.out.println("🔔 Testing System Notifications..."); 
            notificationManager.notifySuccess("Test Success", "This is a success message"); 
            Thread.sleep(1000); 
            notificationManager.notifyWarning("Test Warning", "This is a warning message"); 
            Thread.sleep(1000); 
            notificationManager.notifyError("Test Error", "This is an error message"); 
            Thread.sleep(1000); 
            notificationManager.notifyUrgent("Test Urgent", "This is an urgent message"); 
            System.out.println("✅ System notifications sent"); 
            System.out.println(); 
 
            System.out.println("🎉 ALL CENTRALIZED NOTIFICATION TESTS COMPLETED!"); 
            System.out.println(); 
            System.out.println("📋 SOUND MANAGEMENT SUMMARY:"); 
            System.out.println("🔔 New Orders: MP3 sounds + continuous ringing"); 
            System.out.println("✅ Order Accepted: 2 quick beeps + ringing stops"); 
            System.out.println("🍽️ Order Ready: 3 beeps"); 
            System.out.println("💰 Order Pricing: 1 long beep"); 
            System.out.println("📦 Order Completed: 4 ascending beeps"); 
            System.out.println("🔔 System Sounds: Various beep patterns"); 
            System.out.println(); 
            System.out.println("🔇 Finishing Orders: SILENT (no sounds)"); 
            System.out.println("🔔 All Sounds: Managed by CentralizedNotificationManager"); 
 
            // Cleanup 
            notificationManager.cleanup(); 
 
        } catch (Exception e) { 
            System.err.println("Error during testing: " + e.getMessage()); 
            e.printStackTrace(); 
        } 
    } 
 
    private static OnlineOrder createTestOrder(String orderId, OnlineOrder.Platform platform) { 
        OnlineOrder order = new OnlineOrder(); 
        order.setOrderId(orderId); 
        order.setPlatform(platform); 
        order.setCustomerName("Test Customer"); 
        order.setTotalAmount(250.0); 
        order.setOrderTime(LocalDateTime.now()); 
        order.setStatus(OnlineOrder.OrderStatus.NEW); 
        return order; 
    } 
} 
