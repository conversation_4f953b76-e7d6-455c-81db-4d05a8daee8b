<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.image.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.SettingsController" stylesheets="@../css/application.css">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="module-header">
         <children>
            <Label styleClass="module-title" text="🛠️ System Settings">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />

            <!-- Home and Notifications buttons -->
            <Button fx:id="homeBtn" mnemonicParsing="false" onAction="#goToHome" styleClass="icon-button" text="🏠">
               <tooltip>
                  <Tooltip text="Go to Dashboard Home" />
               </tooltip>
            </Button>
            <Button fx:id="notificationsBtn" mnemonicParsing="false" onAction="#showNotifications" styleClass="icon-button" text="🔔">
               <tooltip>
                  <Tooltip text="View Notifications" />
               </tooltip>
            </Button>

            <Button mnemonicParsing="false" onAction="#openPlatformConfiguration" styleClass="secondary-button" text="🔧 Platform &amp; Menu Manager" />
            <Button mnemonicParsing="false" onAction="#saveAllSettings" styleClass="primary-button" text="💾 Save All Settings" />
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </HBox>
      
      <!-- Settings Content -->
      <ScrollPane styleClass="universal-scroll-pane" fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="ALWAYS" VBox.vgrow="ALWAYS">
         <content>
            <VBox spacing="25.0" styleClass="settings-content">
               <children>
                  <!-- Restaurant Information -->
                  <VBox spacing="15.0" styleClass="settings-section">
                     <children>
                        <Label styleClass="section-title" text="🏪 Restaurant Information">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        
                        <HBox spacing="20.0">
                           <children>
                              <!-- Logo Section -->
                              <VBox spacing="10.0" styleClass="logo-section">
                                 <children>
                                    <Label text="Restaurant Logo:" />
                                    <VBox alignment="CENTER" spacing="10.0" styleClass="logo-preview">
                                       <children>
                                          <ImageView fx:id="logoImageView" fitHeight="100.0" fitWidth="100.0" preserveRatio="true" styleClass="logo-image">
                                             <image>
                                                <Image url="@../images/default-logo.png" />
                                             </image>
                                          </ImageView>
                                          <Button mnemonicParsing="false" onAction="#uploadLogo" styleClass="upload-button" text="📁 Upload Logo" />
                                       </children>
                                    </VBox>
                                 </children>
                              </VBox>
                              
                              <!-- Restaurant Details -->
                              <VBox spacing="15.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <HBox spacing="20.0">
                                       <children>
                                          <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                             <children>
                                                <Label text="Restaurant Name *" />
                                                <TextField fx:id="restaurantNameField" text="WOK KA TADKA" />
                                             </children>
                                          </VBox>
                                          <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                             <children>
                                                <Label text="Contact Phone *" />
                                                <TextField fx:id="contactPhoneField" text="+91 98765 43210" />
                                             </children>
                                          </VBox>
                                       </children>
                                    </HBox>
                                    
                                    <VBox spacing="8.0">
                                       <children>
                                          <Label text="Address *" />
                                          <TextArea fx:id="addressTextArea" prefRowCount="3" text="123 Food Street, Flavor City, FC 12345" />
                                       </children>
                                    </VBox>
                                    
                                    <HBox spacing="20.0">
                                       <children>
                                          <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                             <children>
                                                <Label text="Email" />
                                                <TextField fx:id="emailField" text="<EMAIL>" />
                                             </children>
                                          </VBox>
                                          <VBox spacing="8.0" HBox.hgrow="ALWAYS">
                                             <children>
                                                <Label text="Website" />
                                                <TextField fx:id="websiteField" text="www.wokkatadka.com" />
                                             </children>
                                          </VBox>
                                       </children>
                                    </HBox>
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Theme Settings -->
                  <VBox spacing="15.0" styleClass="settings-section">
                     <children>
                        <Label styleClass="section-title" text="🎨 Theme Settings">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        
                        <HBox spacing="20.0">
                           <children>
                              <VBox spacing="10.0">
                                 <children>
                                    <Label text="Application Theme:" />
                                    <ComboBox fx:id="themeCombo" prefWidth="200.0" />
                                 </children>
                              </VBox>
                              
                              <VBox spacing="10.0">
                                 <children>
                                    <Label text="Accent Color:" />
                                    <ComboBox fx:id="accentColorCombo" prefWidth="200.0" />
                                 </children>
                              </VBox>
                              
                              <VBox spacing="10.0">
                                 <children>
                                    <Label text="Font Size:" />
                                    <ComboBox fx:id="fontSizeCombo" prefWidth="150.0" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                        
                        <HBox spacing="10.0">
                           <children>
                              <Button mnemonicParsing="false" onAction="#previewTheme" styleClass="secondary-button" text="👁️ Preview Theme" />
                              <Button mnemonicParsing="false" onAction="#applyTheme" styleClass="primary-button" text="✅ Apply Theme" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Printer Settings -->
                  <VBox spacing="15.0" styleClass="settings-section">
                     <children>
                        <Label styleClass="section-title" text="🖨️ Printer Settings">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        
                        <HBox spacing="20.0">
                           <children>
                              <VBox spacing="15.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <VBox spacing="8.0">
                                       <children>
                                          <Label text="Default Invoice Printer:" />
                                          <ComboBox fx:id="defaultInvoicePrinterCombo" prefWidth="250.0" />
                                       </children>
                                    </VBox>
                                    
                                    <VBox spacing="8.0">
                                       <children>
                                          <Label text="Default KOT Printer:" />
                                          <ComboBox fx:id="defaultKOTPrinterCombo" prefWidth="250.0" />
                                       </children>
                                    </VBox>
                                 </children>
                              </VBox>
                              
                              <VBox spacing="15.0">
                                 <children>
                                    <VBox spacing="8.0">
                                       <children>
                                          <Label text="Print Options:" />
                                          <CheckBox fx:id="autoPrintKOTCheckbox" text="Auto-print KOT on order" />
                                          <CheckBox fx:id="autoPrintInvoiceCheckbox" text="Auto-print invoice on payment" />
                                          <CheckBox fx:id="printCustomerCopyCheckbox" text="Print customer copy" />
                                       </children>
                                    </VBox>
                                    
                                    <HBox spacing="10.0">
                                       <children>
                                          <Button mnemonicParsing="false" onAction="#testPrintInvoice" styleClass="test-button" text="🖨️ Test Invoice" />
                                          <Button mnemonicParsing="false" onAction="#testPrintKOT" styleClass="test-button" text="🖨️ Test KOT" />
                                       </children>
                                    </HBox>
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Database Settings -->
                  <VBox spacing="15.0" styleClass="settings-section">
                     <children>
                        <Label styleClass="section-title" text="🗄️ Database Settings">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        
                        <HBox spacing="20.0">
                           <children>
                              <VBox spacing="15.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <VBox spacing="8.0">
                                       <children>
                                          <Label text="Database Location:" />
                                          <HBox spacing="10.0">
                                             <children>
                                                <TextField fx:id="databaseLocationField" editable="false" text="./restaurant.db" HBox.hgrow="ALWAYS" />
                                                <Button mnemonicParsing="false" onAction="#browseDatabaseLocation" styleClass="browse-button" text="📁 Browse" />
                                             </children>
                                          </HBox>
                                       </children>
                                    </VBox>
                                    
                                    <VBox spacing="8.0">
                                       <children>
                                          <Label text="Backup Location:" />
                                          <HBox spacing="10.0">
                                             <children>
                                                <TextField fx:id="backupLocationField" text="./backups/" HBox.hgrow="ALWAYS" />
                                                <Button mnemonicParsing="false" onAction="#browseBackupLocation" styleClass="browse-button" text="📁 Browse" />
                                             </children>
                                          </HBox>
                                       </children>
                                    </VBox>
                                    
                                    <VBox spacing="8.0">
                                       <children>
                                          <Label text="Auto Backup:" />
                                          <HBox spacing="15.0">
                                             <children>
                                                <CheckBox fx:id="autoBackupCheckbox" text="Enable automatic backup" />
                                                <ComboBox fx:id="backupFrequencyCombo" prefWidth="150.0" />
                                             </children>
                                          </HBox>
                                       </children>
                                    </VBox>
                                 </children>
                              </VBox>
                              
                              <VBox spacing="15.0">
                                 <children>
                                    <VBox spacing="8.0">
                                       <children>
                                          <Label text="Database Actions:" />
                                          <Button mnemonicParsing="false" onAction="#createBackup" styleClass="backup-button" text="💾 Create Backup Now" />
                                          <Button mnemonicParsing="false" onAction="#restoreBackup" styleClass="restore-button" text="📥 Restore from Backup" />
                                          <Button mnemonicParsing="false" onAction="#exportData" styleClass="export-button" text="📤 Export Data" />
                                       </children>
                                    </VBox>
                                    
                                    <VBox spacing="8.0">
                                       <children>
                                          <Label text="Database Info:" />
                                          <Label fx:id="databaseSizeLabel" text="Size: 2.5 MB" />
                                          <Label fx:id="lastBackupLabel" text="Last Backup: Never" />
                                          <Label fx:id="recordCountLabel" text="Total Records: 1,234" />
                                       </children>
                                    </VBox>
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Business Settings -->
                  <VBox spacing="15.0" styleClass="settings-section">
                     <children>
                        <Label styleClass="section-title" text="🏪 Business Settings">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>

                        <HBox spacing="20.0">
                           <children>
                              <VBox spacing="15.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <VBox spacing="8.0">
                                       <children>
                                          <Label text="Business Hours &amp; Day-End:" />

                                          <!-- Day-End Time Configuration -->
                                          <VBox spacing="5.0" styleClass="info-section">
                                             <children>
                                                <HBox spacing="10.0">
                                                   <children>
                                                      <Label text="Day-End Time:" />
                                                      <TextField fx:id="dayEndTimeField" prefWidth="80.0" promptText="02:00" />
                                                      <Label text="(HH:mm format)" />
                                                   </children>
                                                </HBox>

                                                <!-- Description for Day-End Time -->
                                                <VBox spacing="3.0" styleClass="description-box">
                                                   <children>
                                                      <Label styleClass="description-title" text="📋 Description: Set the time when the business day ends.">
                                                         <font>
                                                            <Font name="System Bold" size="12.0" />
                                                         </font>
                                                      </Label>
                                                      <Label styleClass="description-text" text="Example: If set to 2:00 AM, all orders placed between 12:00 AM and 2:00 AM will be counted as part of the previous day for:" wrapText="true">
                                                         <font>
                                                            <Font size="11.0" />
                                                         </font>
                                                      </Label>
                                                      <VBox spacing="2.0" styleClass="description-list">
                                                         <children>
                                                            <Label styleClass="description-item" text="• Sales Reports" />
                                                            <Label styleClass="description-item" text="• Tax Summary" />
                                                            <Label styleClass="description-item" text="• Inventory Tracking" />
                                                            <Label styleClass="description-item" text="• KOT/BOT logs" />
                                                         </children>
                                                      </VBox>
                                                   </children>
                                                   <padding>
                                                      <Insets bottom="5.0" left="10.0" right="10.0" top="5.0" />
                                                   </padding>
                                                </VBox>
                                             </children>
                                          </VBox>

                                          <HBox spacing="10.0">
                                             <children>
                                                <Label text="Business Start:" />
                                                <TextField fx:id="businessStartTimeField" prefWidth="80.0" promptText="18:00" />
                                                <Label text="Business End:" />
                                                <TextField fx:id="businessEndTimeField" prefWidth="80.0" promptText="02:00" />
                                             </children>
                                          </HBox>
                                       </children>
                                    </VBox>

                                    <VBox spacing="8.0">
                                       <children>
                                          <Label text="KOT &amp; Billing Settings:" />
                                          <CheckBox fx:id="holdKOTBeforeBillingCheckbox" text="Hold KOTs before billing" />
                                          <CheckBox fx:id="restrictBillingWithoutKOTCheckbox" text="Restrict billing if KOT not generated" />
                                          <CheckBox fx:id="enableKOTNotificationsCheckbox" text="Enable notifications for long-held KOTs" />
                                          <HBox spacing="10.0">
                                             <children>
                                                <Label text="KOT Hold Warning (minutes):" />
                                                <Spinner fx:id="kotHoldWarningSpinner" editable="true" max="60.0" min="5.0" initialValue="15.0" />
                                             </children>
                                          </HBox>
                                       </children>
                                    </VBox>
                                 </children>
                              </VBox>

                              <VBox spacing="15.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <VBox spacing="8.0">
                                       <children>
                                          <Label text="Custom Bill Footer:" />
                                          <TextArea fx:id="customBillFooterArea" prefRowCount="3" promptText="Enter custom footer text for printed bills..." />
                                       </children>
                                    </VBox>

                                    <VBox spacing="8.0">
                                       <children>
                                          <Label text="Business Actions:" />
                                          <Button mnemonicParsing="false" onAction="#saveBusinessSettings" styleClass="primary-button" text="💾 Save Business Settings" />
                                          <Button mnemonicParsing="false" onAction="#resetBusinessSettings" styleClass="reset-button" text="🔄 Reset Business Settings" />
                                       </children>
                                    </VBox>
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                     </children>
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                  </VBox>

                  <!-- System Settings -->
                  <VBox spacing="15.0" styleClass="settings-section">
                     <children>
                        <Label styleClass="section-title" text="⚙️ System Settings">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        
                        <HBox spacing="20.0">
                           <children>
                              <VBox spacing="15.0" HBox.hgrow="ALWAYS">
                                 <children>
                                    <VBox spacing="8.0">
                                       <children>
                                          <Label text="Application Settings:" />
                                          <CheckBox fx:id="startupLoginCheckbox" text="Show login screen on startup" />
                                          <CheckBox fx:id="rememberLastUserCheckbox" text="Remember last logged in user" />
                                          <CheckBox fx:id="autoSaveCheckbox" text="Auto-save changes" />
                                          <CheckBox fx:id="soundNotificationsCheckbox" text="Enable sound notifications" />
                                       </children>
                                    </VBox>
                                    
                                    <VBox spacing="8.0">
                                       <children>
                                          <Label text="Session Settings:" />
                                          <HBox spacing="10.0">
                                             <children>
                                                <Label text="Session Timeout (minutes):" />
                                                <Spinner fx:id="sessionTimeoutSpinner" editable="true" max="480.0" min="5.0" initialValue="60.0" />
                                             </children>
                                          </HBox>
                                       </children>
                                    </VBox>
                                 </children>
                              </VBox>
                              
                              <VBox spacing="15.0">
                                 <children>
                                    <VBox spacing="8.0">
                                       <children>
                                          <Label text="System Actions:" />
                                          <Button mnemonicParsing="false" onAction="#clearCache" styleClass="clear-button" text="🗑️ Clear Cache" />
                                          <Button mnemonicParsing="false" onAction="#resetSettings" styleClass="reset-button" text="🔄 Reset to Defaults" />
                                          <Button mnemonicParsing="false" onAction="#viewLogs" styleClass="logs-button" text="📋 View System Logs" />
                                       </children>
                                    </VBox>
                                    
                                    <VBox spacing="8.0">
                                       <children>
                                          <Label text="System Info:" />
                                          <Label fx:id="versionLabel" text="Version: 1.0.0" />
                                          <Label fx:id="buildDateLabel" text="Build: 06/07/2025" />
                                          <Label fx:id="javaVersionLabel" text="Java: 11.0.1" />
                                       </children>
                                    </VBox>
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                     </children>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="30.0" left="20.0" right="20.0" top="10.0" />
               </padding>
            </VBox>
         </content>
      </ScrollPane>
   </children>
</VBox>
