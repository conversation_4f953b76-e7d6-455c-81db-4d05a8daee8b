package com.restaurant.model;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class WeeklyReport {
    private int id;
    private LocalDate weekStartDate;
    private LocalDate weekEndDate;
    private int totalOrders;
    private double totalRevenue;
    private int swiggyOrders;
    private int zomatoOrders;
    private double swiggyRevenue;
    private double zomatoRevenue;
    private double avgDailyOrders;
    private double avgDailyRevenue;
    private String bestDay;
    private LocalDateTime createdAt;
    
    // Constructors
    public WeeklyReport() {}
    
    public WeeklyReport(LocalDate weekStartDate, LocalDate weekEndDate) {
        this.weekStartDate = weekStartDate;
        this.weekEndDate = weekEndDate;
        this.createdAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public LocalDate getWeekStartDate() { return weekStartDate; }
    public void setWeekStartDate(LocalDate weekStartDate) { this.weekStartDate = weekStartDate; }
    
    public LocalDate getWeekEndDate() { return weekEndDate; }
    public void setWeekEndDate(LocalDate weekEndDate) { this.weekEndDate = weekEndDate; }
    
    public int getTotalOrders() { return totalOrders; }
    public void setTotalOrders(int totalOrders) { this.totalOrders = totalOrders; }
    
    public double getTotalRevenue() { return totalRevenue; }
    public void setTotalRevenue(double totalRevenue) { this.totalRevenue = totalRevenue; }
    
    public int getSwiggyOrders() { return swiggyOrders; }
    public void setSwiggyOrders(int swiggyOrders) { this.swiggyOrders = swiggyOrders; }
    
    public int getZomatoOrders() { return zomatoOrders; }
    public void setZomatoOrders(int zomatoOrders) { this.zomatoOrders = zomatoOrders; }
    
    public double getSwiggyRevenue() { return swiggyRevenue; }
    public void setSwiggyRevenue(double swiggyRevenue) { this.swiggyRevenue = swiggyRevenue; }
    
    public double getZomatoRevenue() { return zomatoRevenue; }
    public void setZomatoRevenue(double zomatoRevenue) { this.zomatoRevenue = zomatoRevenue; }
    
    public double getAvgDailyOrders() { return avgDailyOrders; }
    public void setAvgDailyOrders(double avgDailyOrders) { this.avgDailyOrders = avgDailyOrders; }
    
    public double getAvgDailyRevenue() { return avgDailyRevenue; }
    public void setAvgDailyRevenue(double avgDailyRevenue) { this.avgDailyRevenue = avgDailyRevenue; }
    
    public String getBestDay() { return bestDay; }
    public void setBestDay(String bestDay) { this.bestDay = bestDay; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    // Utility methods
    public double getSwiggyPercentage() {
        return totalOrders > 0 ? (swiggyOrders * 100.0) / totalOrders : 0.0;
    }
    
    public double getZomatoPercentage() {
        return totalOrders > 0 ? (zomatoOrders * 100.0) / totalOrders : 0.0;
    }
    
    public double getAvgOrderValue() {
        return totalOrders > 0 ? totalRevenue / totalOrders : 0.0;
    }
    
    public String getWeekPeriod() {
        return weekStartDate + " to " + weekEndDate;
    }
    
    @Override
    public String toString() {
        return String.format("WeeklyReport{period=%s to %s, orders=%d, revenue=%.2f}", 
                           weekStartDate, weekEndDate, totalOrders, totalRevenue);
    }
}
