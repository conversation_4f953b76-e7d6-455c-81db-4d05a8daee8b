package com.restaurant.model;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Model class representing an AI response with visual data and actions
 */
public class AIResponse {
    private String message;
    private String intent;
    private Map<String, String> entities;
    private double confidence;
    private LocalDateTime timestamp;
    
    // Visual data
    private String chartData;
    private String tableData;
    private String imageData;
    
    // Action data
    private List<String> actionButtons;
    private List<String> suggestedActions;
    private String executedAction;
    private String actionDetails;
    
    // Response metadata
    private String responseType;
    private boolean hasActions;
    private boolean hasVisualData;

    public AIResponse() {
        this.timestamp = LocalDateTime.now();
        this.hasActions = false;
        this.hasVisualData = false;
        this.responseType = "text";
    }

    public AIResponse(String message, String intent, double confidence) {
        this();
        this.message = message;
        this.intent = intent;
        this.confidence = confidence;
    }

    // Getters and Setters
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getIntent() {
        return intent;
    }

    public void setIntent(String intent) {
        this.intent = intent;
    }

    public Map<String, String> getEntities() {
        return entities;
    }

    public void setEntities(Map<String, String> entities) {
        this.entities = entities;
    }

    public double getConfidence() {
        return confidence;
    }

    public void setConfidence(double confidence) {
        this.confidence = confidence;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getChartData() {
        return chartData;
    }

    public void setChartData(String chartData) {
        this.chartData = chartData;
        updateVisualDataFlag();
    }

    public String getTableData() {
        return tableData;
    }

    public void setTableData(String tableData) {
        this.tableData = tableData;
        updateVisualDataFlag();
    }

    public String getImageData() {
        return imageData;
    }

    public void setImageData(String imageData) {
        this.imageData = imageData;
        updateVisualDataFlag();
    }

    public List<String> getActionButtons() {
        return actionButtons;
    }

    public void setActionButtons(List<String> actionButtons) {
        this.actionButtons = actionButtons;
        updateActionsFlag();
    }

    public List<String> getSuggestedActions() {
        return suggestedActions;
    }

    public void setSuggestedActions(List<String> suggestedActions) {
        this.suggestedActions = suggestedActions;
    }

    public String getExecutedAction() {
        return executedAction;
    }

    public void setExecutedAction(String executedAction) {
        this.executedAction = executedAction;
        updateActionsFlag();
    }

    public String getActionDetails() {
        return actionDetails;
    }

    public void setActionDetails(String actionDetails) {
        this.actionDetails = actionDetails;
    }

    public String getResponseType() {
        return responseType;
    }

    public void setResponseType(String responseType) {
        this.responseType = responseType;
    }

    public boolean hasActions() {
        return hasActions;
    }

    public void setHasActions(boolean hasActions) {
        this.hasActions = hasActions;
    }

    public boolean hasVisualData() {
        return hasVisualData;
    }

    public void setHasVisualData(boolean hasVisualData) {
        this.hasVisualData = hasVisualData;
    }

    // Utility methods
    private void updateVisualDataFlag() {
        this.hasVisualData = (chartData != null && !chartData.isEmpty()) ||
                           (tableData != null && !tableData.isEmpty()) ||
                           (imageData != null && !imageData.isEmpty());
    }

    private void updateActionsFlag() {
        this.hasActions = (actionButtons != null && !actionButtons.isEmpty()) ||
                         (executedAction != null && !executedAction.isEmpty());
    }

    public String getEntity(String key) {
        return entities != null ? entities.get(key) : null;
    }

    public String getEntity(String key, String defaultValue) {
        String value = getEntity(key);
        return value != null ? value : defaultValue;
    }

    public boolean hasEntity(String key) {
        return entities != null && entities.containsKey(key);
    }

    public String getConfidenceLevel() {
        if (confidence >= 0.9) return "Very High";
        if (confidence >= 0.75) return "High";
        if (confidence >= 0.6) return "Medium";
        if (confidence >= 0.4) return "Low";
        return "Very Low";
    }

    public boolean isHighConfidence() {
        return confidence >= 0.75;
    }

    public String getIntentDisplayName() {
        switch (intent) {
            case "FORECAST_SALES": return "Sales Forecasting";
            case "SALES_TREND": return "Sales Trend Analysis";
            case "GROWTH_RATE": return "Growth Rate Analysis";
            case "CREATE_ORDER": return "Order Creation";
            case "ADD_ITEMS": return "Add Items to Order";
            case "SHOW_BILL": return "Show Bill";
            case "LOW_STOCK": return "Low Stock Alert";
            case "UPDATE_STOCK": return "Update Inventory";
            case "CHECK_STOCK": return "Check Stock Status";
            case "SALES_SUMMARY": return "Sales Summary";
            case "COMPARE_PERIODS": return "Period Comparison";
            case "DOWNLOAD_REPORT": return "Download Report";
            case "SEARCH_MENU": return "Menu Search";
            case "FILTER_BY_PRICE": return "Price Filter";
            case "POPULAR_ITEMS": return "Popular Items";
            case "NAVIGATE": return "Navigation";
            case "RESET_FILTERS": return "Reset Filters";
            default: return intent;
        }
    }

    public String getIntentIcon() {
        switch (intent) {
            case "FORECAST_SALES": return "📊";
            case "SALES_TREND": return "📈";
            case "GROWTH_RATE": return "📊";
            case "CREATE_ORDER": return "🛒";
            case "ADD_ITEMS": return "➕";
            case "SHOW_BILL": return "💰";
            case "LOW_STOCK": return "⚠️";
            case "UPDATE_STOCK": return "📦";
            case "CHECK_STOCK": return "📋";
            case "SALES_SUMMARY": return "💰";
            case "COMPARE_PERIODS": return "📊";
            case "DOWNLOAD_REPORT": return "📥";
            case "SEARCH_MENU": return "🔍";
            case "FILTER_BY_PRICE": return "💰";
            case "POPULAR_ITEMS": return "🏆";
            case "NAVIGATE": return "🧭";
            case "RESET_FILTERS": return "🔄";
            default: return "🤖";
        }
    }

    public String getFormattedTimestamp() {
        return timestamp.toString(); // Can be formatted as needed
    }

    public boolean isSuccessful() {
        return !intent.equals("UNKNOWN") && confidence >= 0.5;
    }

    public boolean requiresUserAction() {
        return actionButtons != null && !actionButtons.isEmpty();
    }

    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("Intent: ").append(getIntentDisplayName());
        summary.append(" | Confidence: ").append(String.format("%.0f%%", confidence * 100));
        
        if (hasVisualData) {
            summary.append(" | Has Visual Data");
        }
        
        if (hasActions) {
            summary.append(" | Has Actions");
        }
        
        return summary.toString();
    }

    @Override
    public String toString() {
        return "AIResponse{" +
                "message='" + message + '\'' +
                ", intent='" + intent + '\'' +
                ", confidence=" + confidence +
                ", hasActions=" + hasActions +
                ", hasVisualData=" + hasVisualData +
                ", timestamp=" + timestamp +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        AIResponse that = (AIResponse) o;

        if (!message.equals(that.message)) return false;
        if (!intent.equals(that.intent)) return false;
        return timestamp.equals(that.timestamp);
    }

    @Override
    public int hashCode() {
        int result = message.hashCode();
        result = 31 * result + intent.hashCode();
        result = 31 * result + timestamp.hashCode();
        return result;
    }

    // Builder pattern for easy construction
    public static class Builder {
        private AIResponse response = new AIResponse();

        public Builder message(String message) {
            response.message = message;
            return this;
        }

        public Builder intent(String intent) {
            response.intent = intent;
            return this;
        }

        public Builder entities(Map<String, String> entities) {
            response.entities = entities;
            return this;
        }

        public Builder confidence(double confidence) {
            response.confidence = confidence;
            return this;
        }

        public Builder chartData(String chartData) {
            response.setChartData(chartData);
            return this;
        }

        public Builder tableData(String tableData) {
            response.setTableData(tableData);
            return this;
        }

        public Builder actionButtons(List<String> actionButtons) {
            response.setActionButtons(actionButtons);
            return this;
        }

        public Builder suggestedActions(List<String> suggestedActions) {
            response.suggestedActions = suggestedActions;
            return this;
        }

        public Builder executedAction(String executedAction) {
            response.setExecutedAction(executedAction);
            return this;
        }

        public AIResponse build() {
            return response;
        }
    }
}
