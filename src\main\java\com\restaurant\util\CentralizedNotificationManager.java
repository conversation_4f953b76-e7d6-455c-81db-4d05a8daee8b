package com.restaurant.util;

import com.restaurant.model.OnlineOrder;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * Centralized Notification Manager - ALL SOUNDS MANAGED HERE
 * This class handles all audio notifications for the entire restaurant system
 */
public class CentralizedNotificationManager {
    
    private static CentralizedNotificationManager instance;
    private boolean audioEnabled = true;
    private boolean notificationsEnabled = true;
    
    // Audio management
    private MP3AudioPlayer audioPlayer;
    private final ConcurrentHashMap<String, ScheduledFuture<?>> activeAlerts = new ConcurrentHashMap<>();
    private final ScheduledExecutorService alertScheduler = Executors.newScheduledThreadPool(3);
    
    // Notification types with audio
    public enum NotificationType {
        // Order-related notifications
        NEW_SWIGGY_ORDER("🟠 New Swiggy Order", "#ff6b35"),
        NEW_ZOMATO_ORDER("🔴 New Zomato Order", "#e23744"),
        NEW_WOK_KA_TADKA_ORDER("🟢 New Wok Ka Tadka Order", "#2e7d32"),
        ORDER_ACCEPTED("✅ Order Accepted", "#27ae60"),
        ORDER_REJECTED("❌ Order Rejected", "#e74c3c"),
        ORDER_READY("🍽️ Order Ready", "#3498db"),
        ORDER_PRICING("💰 Order Pricing", "#9b59b6"),
        ORDER_COMPLETED("📦 Order Completed", "#2ecc71"),

        // System notifications
        SUCCESS("✅ Success", "#27ae60"),
        WARNING("⚠️ Warning", "#f39c12"),
        ERROR("❌ Error", "#e74c3c"),
        INFO("ℹ️ Info", "#3498db"),
        URGENT("🚨 Urgent", "#e74c3c");
        
        private final String displayText;
        private final String color;
        
        NotificationType(String displayText, String color) {
            this.displayText = displayText;
            this.color = color;
        }
        
        public String getDisplayText() { return displayText; }
        public String getColor() { return color; }
    }
    
    private CentralizedNotificationManager() {
        this.audioPlayer = MP3AudioPlayer.getInstance();
        System.out.println("CentralizedNotificationManager: Initialized - All sounds centralized here");
    }
    
    public static CentralizedNotificationManager getInstance() {
        if (instance == null) {
            instance = new CentralizedNotificationManager();
        }
        return instance;
    }
    
    // ==================== NEW ORDER NOTIFICATIONS ====================
    
    /**
     * Handle new Swiggy order with continuous ringing until accepted
     */
    public void notifyNewSwiggyOrder(OnlineOrder order) {
        System.out.println("🟠 NEW SWIGGY ORDER: " + order.getOrderId());
        
        // Immediate notification with sound
        showNotificationWithSound(NotificationType.NEW_SWIGGY_ORDER,
            "New Swiggy Order #" + order.getOrderId(),
            "Customer: " + order.getCustomerName() + " | Amount: ₹" + String.format("%.2f", order.getTotalAmount()));
        
        // Start continuous ringing until accepted
        startContinuousAlert(order.getOrderId(), "SWIGGY");
    }
    
    /**
     * Handle new Zomato order with continuous ringing until accepted
     */
    public void notifyNewZomatoOrder(OnlineOrder order) {
        System.out.println("🔴 NEW ZOMATO ORDER: " + order.getOrderId());

        // Immediate notification with sound
        showNotificationWithSound(NotificationType.NEW_ZOMATO_ORDER,
            "New Zomato Order #" + order.getOrderId(),
            "Customer: " + order.getCustomerName() + " | Amount: ₹" + String.format("%.2f", order.getTotalAmount()));

        // Start continuous ringing until accepted
        startContinuousAlert(order.getOrderId(), "ZOMATO");
    }

    /**
     * Handle new Wok Ka Tadka order with continuous ringing until accepted
     */
    public void notifyNewWokKaTadkaOrder(OnlineOrder order) {
        System.out.println("🟢 NEW WOK KA TADKA ORDER: " + order.getOrderId());

        // Immediate notification with sound
        showNotificationWithSound(NotificationType.NEW_WOK_KA_TADKA_ORDER,
            "New Wok Ka Tadka Order #" + order.getOrderId(),
            "Customer: " + order.getCustomerName() + " | Amount: ₹" + String.format("%.2f", order.getTotalAmount()));

        // Start continuous ringing until accepted
        startContinuousAlert(order.getOrderId(), "WOK_KA_TADKA");
    }
    
    // ==================== ORDER STATUS NOTIFICATIONS ====================
    
    /**
     * Order accepted - stop ringing and play acceptance sound
     */
    public void notifyOrderAccepted(String orderId, String platform) {
        System.out.println("✅ ORDER ACCEPTED: " + orderId);

        // Stop continuous ringing
        stopContinuousAlert(orderId);

        // Play distinctive acceptance sound (3 quick beeps)
        showNotificationWithSound(NotificationType.ORDER_ACCEPTED,
            "Order Accepted",
            "Order " + orderId + " (" + platform + ") is now being prepared");
    }

    /**
     * Order rejected - stop ringing and play rejection sound
     */
    public void notifyOrderRejected(String orderId, String platform) {
        System.out.println("❌ ORDER REJECTED: " + orderId);

        // Stop continuous ringing
        stopContinuousAlert(orderId);

        // Play distinctive rejection sound (2 descending beeps)
        showNotificationWithSound(NotificationType.ORDER_REJECTED,
            "Order Rejected",
            "Order " + orderId + " (" + platform + ") has been rejected");
    }
    
    /**
     * Order ready for pickup
     */
    public void notifyOrderReady(String orderId, String customerName) {
        System.out.println("🍽️ ORDER READY: " + orderId);
        
        showNotificationWithSound(NotificationType.ORDER_READY,
            "Order Ready for Pickup",
            "Order " + orderId + " for " + customerName + " is ready");
    }
    
    /**
     * Order moved to pricing
     */
    public void notifyOrderPricing(String orderId) {
        System.out.println("💰 ORDER PRICING: " + orderId);
        
        showNotificationWithSound(NotificationType.ORDER_PRICING,
            "Order Pricing Started",
            "Order " + orderId + " moved to pricing & packaging");
    }
    
    /**
     * Order completed and handed over
     */
    public void notifyOrderCompleted(String orderId) {
        System.out.println("📦 ORDER COMPLETED: " + orderId);
        
        showNotificationWithSound(NotificationType.ORDER_COMPLETED,
            "Order Completed",
            "Order " + orderId + " has been handed over to delivery partner");
    }
    
    // ==================== SYSTEM NOTIFICATIONS ====================
    
    public void notifySuccess(String title, String message) {
        showNotificationWithSound(NotificationType.SUCCESS, title, message);
    }
    
    public void notifyWarning(String title, String message) {
        showNotificationWithSound(NotificationType.WARNING, title, message);
    }
    
    public void notifyError(String title, String message) {
        showNotificationWithSound(NotificationType.ERROR, title, message);
    }
    
    public void notifyInfo(String title, String message) {
        showNotificationWithSound(NotificationType.INFO, title, message);
    }
    
    public void notifyUrgent(String title, String message) {
        showNotificationWithSound(NotificationType.URGENT, title, message);
    }
    
    // ==================== CONTINUOUS ALERT MANAGEMENT ====================
    
    /**
     * Start continuous ringing for new orders
     */
    private void startContinuousAlert(String orderId, String platform) {
        if (!audioEnabled) return;
        
        // Stop any existing alert for this order
        stopContinuousAlert(orderId);
        
        // Start new continuous alert
        ScheduledFuture<?> alertTask = alertScheduler.scheduleAtFixedRate(() -> {
            try {
                if ("SWIGGY".equals(platform)) {
                    audioPlayer.playSwiggyNotification();
                    System.out.println("🟠 Continuous Swiggy alert for order: " + orderId);
                } else if ("ZOMATO".equals(platform)) {
                    audioPlayer.playZomatoNotification();
                    System.out.println("🔴 Continuous Zomato alert for order: " + orderId);
                } else if ("WOK_KA_TADKA".equals(platform)) {
                    audioPlayer.playWokKaTadkaNotification();
                    System.out.println("🟢 Continuous Wok Ka Tadka alert for order: " + orderId);
                }
            } catch (Exception e) {
                System.err.println("Error in continuous alert: " + e.getMessage());
            }
        }, 0, 10, TimeUnit.SECONDS); // Ring every 10 seconds
        
        activeAlerts.put(orderId, alertTask);
        System.out.println("🔔 Started continuous alert for order: " + orderId + " (" + platform + ")");
    }
    
    /**
     * Stop continuous ringing for an order
     */
    private void stopContinuousAlert(String orderId) {
        ScheduledFuture<?> alertTask = activeAlerts.remove(orderId);
        if (alertTask != null) {
            alertTask.cancel(false);
            System.out.println("🔕 Stopped continuous alert for order: " + orderId);
        }
    }
    
    // ==================== CORE NOTIFICATION METHODS ====================
    
    /**
     * Show notification with appropriate sound
     */
    private void showNotificationWithSound(NotificationType type, String title, String message) {
        if (!notificationsEnabled) return;
        
        // Play sound
        if (audioEnabled) {
            playNotificationSound(type);
        }
        
        // Show console notification (simplified for testing)
        showConsoleNotification(type, title, message);
        
        // Log notification
        System.out.println("NOTIFICATION: " + type.getDisplayText() + " - " + title + ": " + message);
    }
    
    /**
     * Play appropriate sound for notification type
     */
    private void playNotificationSound(NotificationType type) {
        CompletableFuture.runAsync(() -> {
            try {
                switch (type) {
                    case NEW_SWIGGY_ORDER:
                        audioPlayer.playSwiggyNotification();
                        break;
                    case NEW_ZOMATO_ORDER:
                        audioPlayer.playZomatoNotification();
                        break;
                    case NEW_WOK_KA_TADKA_ORDER:
                        audioPlayer.playWokKaTadkaNotification();
                        break;
                    case ORDER_ACCEPTED:
                        playSystemBeepPattern("acceptance"); // 3 quick beeps
                        break;
                    case ORDER_REJECTED:
                        playSystemBeepPattern("rejection"); // 2 descending beeps
                        break;
                    case ORDER_READY:
                        playSystemBeepPattern("ready"); // 3 beeps
                        break;
                    case ORDER_PRICING:
                        playSystemBeepPattern("pricing"); // 1 long beep
                        break;
                    case ORDER_COMPLETED:
                        playSystemBeepPattern("completed"); // 4 ascending beeps
                        break;
                    case SUCCESS:
                        playSystemBeepPattern("success"); // 1 beep
                        break;
                    case WARNING:
                        playSystemBeepPattern("warning"); // 2 beeps
                        break;
                    case ERROR:
                        playSystemBeepPattern("error"); // 3 urgent beeps
                        break;
                    case URGENT:
                        playSystemBeepPattern("urgent"); // 5 rapid beeps
                        break;
                    default:
                        playSystemBeepPattern("default"); // 1 beep
                        break;
                }
            } catch (Exception e) {
                System.err.println("Error playing notification sound: " + e.getMessage());
            }
        });
    }
    
    /**
     * Play system beep patterns for different notification types
     */
    private void playSystemBeepPattern(String pattern) {
        try {
            switch (pattern) {
                case "acceptance":
                    // 3 quick beeps for acceptance
                    for (int i = 0; i < 3; i++) {
                        java.awt.Toolkit.getDefaultToolkit().beep();
                        Thread.sleep(150);
                    }
                    break;
                case "rejection":
                    // 2 descending beeps for rejection (longer intervals)
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(400);
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    break;
                case "ready":
                    // 3 beeps for ready
                    for (int i = 0; i < 3; i++) {
                        java.awt.Toolkit.getDefaultToolkit().beep();
                        Thread.sleep(300);
                    }
                    break;
                case "pricing":
                    // 1 long beep for pricing
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    break;
                case "completed":
                    // 4 ascending beeps for completion
                    for (int i = 0; i < 4; i++) {
                        java.awt.Toolkit.getDefaultToolkit().beep();
                        Thread.sleep(150);
                    }
                    break;
                case "success":
                    // 1 beep for success
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    break;
                case "warning":
                    // 2 beeps for warning
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(300);
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    break;
                case "error":
                    // 3 urgent beeps for error
                    for (int i = 0; i < 3; i++) {
                        java.awt.Toolkit.getDefaultToolkit().beep();
                        Thread.sleep(200);
                    }
                    break;
                case "urgent":
                    // 5 rapid beeps for urgent
                    for (int i = 0; i < 5; i++) {
                        java.awt.Toolkit.getDefaultToolkit().beep();
                        Thread.sleep(100);
                    }
                    break;
                default:
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    break;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * Show console notification (simplified for testing without JavaFX)
     */
    private void showConsoleNotification(NotificationType type, String title, String message) {
        try {
            System.out.println("╔══════════════════════════════════════════════════════════════╗");
            System.out.println("║ " + type.getDisplayText() + " NOTIFICATION");
            System.out.println("╠══════════════════════════════════════════════════════════════╣");
            System.out.println("║ TITLE: " + title);
            System.out.println("║ MESSAGE: " + message);
            System.out.println("║ TIME: " + java.time.LocalDateTime.now().format(
                java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss")));
            System.out.println("╚══════════════════════════════════════════════════════════════╝");
            System.out.println();
        } catch (Exception e) {
            System.err.println("Error showing console notification: " + e.getMessage());
        }
    }
    
    // ==================== SETTINGS ====================
    
    public void setAudioEnabled(boolean enabled) {
        this.audioEnabled = enabled;
        System.out.println("CentralizedNotificationManager: Audio " + (enabled ? "enabled" : "disabled"));
    }
    
    public void setNotificationsEnabled(boolean enabled) {
        this.notificationsEnabled = enabled;
        System.out.println("CentralizedNotificationManager: Notifications " + (enabled ? "enabled" : "disabled"));
    }
    
    public boolean isAudioEnabled() { return audioEnabled; }
    public boolean isNotificationsEnabled() { return notificationsEnabled; }
    
    /**
     * Stop all active alerts and cleanup
     */
    public void cleanup() {
        // Stop all active alerts
        activeAlerts.values().forEach(task -> task.cancel(false));
        activeAlerts.clear();
        
        // Shutdown scheduler
        alertScheduler.shutdown();
        
        // Stop all audio
        if (audioPlayer != null) {
            audioPlayer.stopAllAudio();
        }
        
        System.out.println("CentralizedNotificationManager: Cleanup complete");
    }
}
