package com.restaurant.model;

import java.time.LocalDateTime;

/**
 * Customer Feedback model for CRM system
 */
public class CustomerFeedback {
    private int id;
    private int customerId;
    private String customerName;
    private int orderId;
    private int rating; // 1-5 stars
    private String comment;
    private String category; // "Food Quality", "Service", "Ambiance", "Value", "Overall"
    private String status; // "New", "Reviewed", "Resolved", "Escalated"
    private String response;
    private String respondedBy;
    private LocalDateTime feedbackDate;
    private LocalDateTime responseDate;
    private boolean isPositive;
    private String actionTaken;
    private LocalDateTime createdAt;

    // Constructors
    public CustomerFeedback() {
        this.createdAt = LocalDateTime.now();
        this.feedbackDate = LocalDateTime.now();
        this.status = "New";
    }

    public CustomerFeedback(int customerId, String customerName, int rating, String comment) {
        this();
        this.customerId = customerId;
        this.customerName = customerName;
        this.rating = rating;
        this.comment = comment;
        this.isPositive = rating >= 4;
        this.category = "Overall";
    }

    public CustomerFeedback(int customerId, String customerName, int orderId, int rating, String comment, String category) {
        this(customerId, customerName, rating, comment);
        this.orderId = orderId;
        this.category = category;
    }

    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public int getCustomerId() { return customerId; }
    public void setCustomerId(int customerId) { this.customerId = customerId; }

    public String getCustomerName() { return customerName; }
    public void setCustomerName(String customerName) { this.customerName = customerName; }

    public int getOrderId() { return orderId; }
    public void setOrderId(int orderId) { this.orderId = orderId; }

    public int getRating() { return rating; }
    public void setRating(int rating) { 
        this.rating = rating;
        this.isPositive = rating >= 4;
    }

    public String getComment() { return comment; }
    public void setComment(String comment) { this.comment = comment; }

    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public String getResponse() { return response; }
    public void setResponse(String response) { 
        this.response = response;
        this.responseDate = LocalDateTime.now();
        if (this.status.equals("New")) {
            this.status = "Reviewed";
        }
    }

    public String getRespondedBy() { return respondedBy; }
    public void setRespondedBy(String respondedBy) { this.respondedBy = respondedBy; }

    public LocalDateTime getFeedbackDate() { return feedbackDate; }
    public void setFeedbackDate(LocalDateTime feedbackDate) { this.feedbackDate = feedbackDate; }

    public LocalDateTime getResponseDate() { return responseDate; }
    public void setResponseDate(LocalDateTime responseDate) { this.responseDate = responseDate; }

    public boolean isPositive() { return isPositive; }
    public void setPositive(boolean positive) { isPositive = positive; }

    public String getActionTaken() { return actionTaken; }
    public void setActionTaken(String actionTaken) { 
        this.actionTaken = actionTaken;
        if (this.status.equals("New") || this.status.equals("Reviewed")) {
            this.status = "Resolved";
        }
    }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    // Utility Methods
    public String getRatingStars() {
        StringBuilder stars = new StringBuilder();
        for (int i = 1; i <= 5; i++) {
            if (i <= rating) {
                stars.append("⭐");
            } else {
                stars.append("☆");
            }
        }
        return stars.toString();
    }

    public String getRatingText() {
        switch (rating) {
            case 1: return "Very Poor";
            case 2: return "Poor";
            case 3: return "Average";
            case 4: return "Good";
            case 5: return "Excellent";
            default: return "Not Rated";
        }
    }

    public String getStatusColor() {
        switch (status) {
            case "New": return "#dc3545"; // Red
            case "Reviewed": return "#ffc107"; // Yellow
            case "Resolved": return "#28a745"; // Green
            case "Escalated": return "#fd7e14"; // Orange
            default: return "#6c757d"; // Gray
        }
    }

    public String getSentimentIcon() {
        if (isPositive) {
            return "😊";
        } else {
            return "😞";
        }
    }

    public String getTimeAgo() {
        LocalDateTime now = LocalDateTime.now();
        long hours = java.time.Duration.between(feedbackDate, now).toHours();
        
        if (hours < 1) {
            return "Just now";
        } else if (hours < 24) {
            return hours + " hour" + (hours > 1 ? "s" : "") + " ago";
        } else {
            long days = hours / 24;
            return days + " day" + (days > 1 ? "s" : "") + " ago";
        }
    }

    public boolean needsAttention() {
        return !isPositive && status.equals("New");
    }

    public boolean isOverdue() {
        if (status.equals("Resolved")) return false;
        
        LocalDateTime now = LocalDateTime.now();
        long hours = java.time.Duration.between(feedbackDate, now).toHours();
        
        // Consider feedback overdue if negative and not resolved within 24 hours
        return !isPositive && hours > 24;
    }

    @Override
    public String toString() {
        return customerName + " - " + getRatingStars() + " (" + getTimeAgo() + ")";
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        CustomerFeedback feedback = (CustomerFeedback) obj;
        return id == feedback.id;
    }

    @Override
    public int hashCode() {
        return Integer.hashCode(id);
    }
}
