package com.restaurant.controller;

import com.restaurant.model.Customer;
import com.restaurant.model.CustomerFeedback;
import com.restaurant.model.MarketingCampaign;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.*;

import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import javafx.util.StringConverter;

/**
 * Controller for Customer CRM System
 */
public class CustomerCRMController implements Initializable {

    // FXML Controls
    @FXML private Button backButton;
    @FXML private Label totalCustomersLabel;
    @FXML private Label activeCustomersLabel;
    @FXML private Label averageRatingLabel;
    @FXML private Label totalRevenueLabel;
    @FXML private Button addCustomerBtn;
    @FXML private Button customerManagementBtn;
    @FXML private Button sendCampaignBtn;
    @FXML private Button birthdayCampaignBtn;
    @FXML private Button viewHistoryBtn;
    @FXML private Button viewFeedbackBtn;
    @FXML private Button loyaltyProgramBtn;

    // Birthday Campaign Controls
    @FXML private VBox birthdayCampaignSection;
    @FXML private Button scheduleBtn;
    @FXML private Button onBillPrintBtn;
    @FXML private Button birthdayBtn;
    @FXML private Button anniversaryBtn;
    @FXML private Label messageTitle;
    @FXML private Label messageBody;
    @FXML private Label messageSender;
    @FXML private TextField campaignNameField;
    @FXML private TextField discountField;
    @FXML private TextField validDaysField;
    @FXML private ComboBox<String> messageTypeCombo;
    @FXML private TextField customerSearchField;
    @FXML private ComboBox<String> segmentFilterCombo;
    @FXML private Button selectAllBtn;
    @FXML private Button clearAllBtn;
    @FXML private CheckBox selectAllCheckbox;
    @FXML private VBox campaignCustomerListContainer;
    @FXML private VBox customerInfoContainer;
    @FXML private Label selectedCountLabel;

    // Tag filter buttons
    @FXML private Button vipTagBtn;
    @FXML private Button corporateTagBtn;
    @FXML private Button premiumTagBtn;
    @FXML private Button addTagBtn;
    @FXML private Button closeCampaignBtn;
    @FXML private Button previewBtn;
    @FXML private Button saveDraftBtn;
    @FXML private Button sendCampaignBtn2;
    @FXML private Label newCustomersCount;
    @FXML private Label regularCustomersCount;
    @FXML private Label highSpendersCount;
    @FXML private Label lapsedCustomersCount;
    @FXML private TextField searchField;
    @FXML private ComboBox<String> segmentFilter;
    @FXML private VBox customerTableContainer;
    @FXML private VBox recentActivityContainer;

    // Data
    private List<Customer> customers;
    private List<CustomerFeedback> feedbacks;
    private List<MarketingCampaign> campaigns;
    private String currentFilter = "All";

    // Birthday Campaign Data
    private List<Customer> selectedCustomers;
    private String selectedCampaignType = "Birthday";
    private Map<String, String> campaignTemplates;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        initializeData();
        setupFilters();
        setupEventHandlers();
        setupBirthdayCampaign();
        displayCustomerInfo();
        updateDashboardStats();
        displayCustomers();
        displayRecentActivity();
    }

    private void initializeData() {
        // Initialize customers with sample data
        customers = new ArrayList<>();
        
        // Sample customers with realistic data
        Customer customer1 = new Customer(1, "Rajesh Kumar", "9876543210", "<EMAIL>");
        customer1.setAddress("123 MG Road, Bangalore");
        customer1.addOrder(850.0);
        customer1.addOrder(1200.0);
        customer1.addOrder(950.0);
        customer1.addPreferredDish("Butter Chicken");
        customer1.addPreferredDish("Biryani");
        customer1.setLastVisit(LocalDateTime.now().minusDays(2));
        customers.add(customer1);

        Customer customer2 = new Customer(2, "Priya Sharma", "9876543211", "<EMAIL>");
        customer2.setAddress("456 Brigade Road, Bangalore");
        customer2.addOrder(1500.0);
        customer2.addOrder(2200.0);
        customer2.addOrder(1800.0);
        customer2.addOrder(2500.0);
        customer2.addPreferredDish("Paneer Tikka");
        customer2.addPreferredDish("Dal Makhani");
        customer2.setLastVisit(LocalDateTime.now().minusDays(1));
        customers.add(customer2);

        Customer customer3 = new Customer(3, "Amit Patel", "9876543212", "<EMAIL>");
        customer3.setAddress("789 Commercial Street, Bangalore");
        customer3.addOrder(650.0);
        customer3.setLastVisit(LocalDateTime.now().minusDays(45));
        customer3.addPreferredDish("Masala Dosa");
        customers.add(customer3);

        Customer customer4 = new Customer(4, "Sneha Reddy", "9876543213", "<EMAIL>");
        customer4.setAddress("321 Koramangala, Bangalore");
        customer4.addOrder(3200.0);
        customer4.addOrder(2800.0);
        customer4.addOrder(3500.0);
        customer4.addOrder(4100.0);
        customer4.addOrder(2900.0);
        customer4.addPreferredDish("Mutton Biryani");
        customer4.addPreferredDish("Chicken Tikka");
        customer4.setLastVisit(LocalDateTime.now().minusHours(6));
        customers.add(customer4);

        Customer customer5 = new Customer(5, "Vikram Singh", "9876543214", "<EMAIL>");
        customer5.setAddress("654 Indiranagar, Bangalore");
        customer5.addOrder(450.0);
        customer5.setLastVisit(LocalDateTime.now().minusDays(3));
        customers.add(customer5);

        // Add customers matching the image data
        Customer harpreet = new Customer(6, "Harpreet Singh", "91 XXXXX XXXXX", "<EMAIL>");
        harpreet.setSegment("VIP Client");
        harpreet.setCreatedAt(LocalDateTime.of(2022, 12, 5, 10, 0));
        harpreet.addOrder(2403.0);
        customers.add(harpreet);

        Customer subhash = new Customer(7, "Subhash Roy", "91 XXXXX XXXXY", "<EMAIL>");
        subhash.setSegment("Corporate Client");
        subhash.setCreatedAt(LocalDateTime.of(2021, 1, 4, 14, 30));
        subhash.addOrder(1800.0);
        customers.add(subhash);

        Customer eddie = new Customer(8, "Eddie Vedor", "91 XXXXX XXXXZ", "<EMAIL>");
        eddie.setSegment("Premium Client");
        eddie.setCreatedAt(LocalDateTime.of(2022, 3, 24, 16, 45));
        eddie.addOrder(2200.0);
        customers.add(eddie);

        Customer manoj = new Customer(9, "Manoj Desai", "91 XXXXX XXXXA", "<EMAIL>");
        manoj.setSegment("VIP Client");
        manoj.setCreatedAt(LocalDateTime.of(2022, 8, 18, 12, 15));
        manoj.addOrder(1950.0);
        customers.add(manoj);

        Customer om = new Customer(10, "Om Reddy", "91 XXXXX XXXXB", "<EMAIL>");
        om.setSegment("Premium Client");
        om.setCreatedAt(LocalDateTime.of(2023, 2, 3, 11, 20));
        om.addOrder(1750.0);
        customers.add(om);

        // Initialize feedback data
        feedbacks = new ArrayList<>();
        feedbacks.add(new CustomerFeedback(1, "Rajesh Kumar", 1001, 5, "Excellent food and service!", "Overall"));
        feedbacks.add(new CustomerFeedback(2, "Priya Sharma", 1002, 4, "Good ambiance, loved the paneer tikka", "Food Quality"));
        feedbacks.add(new CustomerFeedback(3, "Amit Patel", 1003, 2, "Food was cold when delivered", "Service"));
        feedbacks.add(new CustomerFeedback(4, "Sneha Reddy", 1004, 5, "Amazing biryani! Will definitely come back", "Food Quality"));
        feedbacks.add(new CustomerFeedback(5, "Vikram Singh", 1005, 3, "Average experience, room for improvement", "Overall"));

        // Initialize campaign data
        campaigns = new ArrayList<>();
        MarketingCampaign campaign1 = new MarketingCampaign("Weekend Special", "SMS", "Regular", "50% off on your favorite dish this weekend!");
        campaign1.setDiscountPercentage(50);
        campaign1.setStatus("Active");
        campaign1.setSentCount(150);
        campaign1.setDeliveredCount(145);
        campaign1.setRedeemedCount(23);
        campaigns.add(campaign1);

        MarketingCampaign campaign2 = new MarketingCampaign("Welcome Back", "WhatsApp", "Lapsed Customer", "We miss you! Come back for a 20% discount.");
        campaign2.setDiscountPercentage(20);
        campaign2.setStatus("Completed");
        campaign2.setSentCount(45);
        campaign2.setDeliveredCount(42);
        campaign2.setRedeemedCount(8);
        campaigns.add(campaign2);

        // Initialize birthday campaign data
        selectedCustomers = new ArrayList<>();

        // Add birthday data to customers
        customers.get(0).setNotes("Birthday: 15-03-1990"); // Rajesh Kumar
        customers.get(1).setNotes("Birthday: 22-07-1985"); // Priya Sharma
        customers.get(2).setNotes("Birthday: 08-12-1992"); // Amit Patel
        customers.get(3).setNotes("Birthday: 30-09-1988"); // Sneha Reddy
        customers.get(4).setNotes("Birthday: 14-05-1991"); // Vikram Singh

        // Initialize campaign templates
        campaignTemplates = new HashMap<>();
        campaignTemplates.put("Birthday", "Wishing you a very Happy Birthday\n\nVisit any of the CDI outlets and show this message to enjoy {discount}% off on your bill");
        campaignTemplates.put("Anniversary", "Happy Anniversary!\n\nCelebrate your special day with us. Enjoy {discount}% off on your bill");
        campaignTemplates.put("Schedule", "Special Offer Just for You!\n\nDon't miss out on our exclusive {discount}% discount");
        campaignTemplates.put("On Bill Print", "Thank you for dining with us!\n\nShow this message on your next visit for {discount}% off");
    }

    private void setupFilters() {
        segmentFilter.getItems().addAll("All", "New Customer", "Regular", "High Spender", "Lapsed Customer");
        segmentFilter.setValue("All");
    }

    private void setupEventHandlers() {
        // Search functionality
        searchField.textProperty().addListener((obs, oldVal, newVal) -> {
            displayCustomers();
        });

        // Segment filter
        segmentFilter.setOnAction(e -> {
            currentFilter = segmentFilter.getValue();
            displayCustomers();
        });
    }

    private void setupBirthdayCampaign() {
        // Setup birthday campaign controls
        if (messageTypeCombo != null) {
            messageTypeCombo.getItems().addAll("SMS", "WhatsApp", "Email", "Push Notification");
            messageTypeCombo.setValue("SMS");
        }

        if (segmentFilterCombo != null) {
            segmentFilterCombo.getItems().addAll("All", "New Customer", "Regular", "High Spender", "Lapsed Customer");
            segmentFilterCombo.setValue("All");
        }

        // Set default values
        if (campaignNameField != null) {
            campaignNameField.setText("Birthday Special Campaign");
        }
        if (discountField != null) {
            discountField.setText("30");
        }
        if (validDaysField != null) {
            validDaysField.setText("7");
        }

        // Setup event handlers for birthday campaign
        if (customerSearchField != null) {
            customerSearchField.textProperty().addListener((obs, oldVal, newVal) -> {
                displayCampaignCustomers();
            });
        }

        if (segmentFilterCombo != null) {
            segmentFilterCombo.setOnAction(e -> displayCampaignCustomers());
        }

        if (discountField != null) {
            discountField.textProperty().addListener((obs, oldVal, newVal) -> {
                updateMessagePreview();
            });
        }

        if (campaignNameField != null) {
            campaignNameField.textProperty().addListener((obs, oldVal, newVal) -> {
                updateMessagePreview();
            });
        }

        // Initialize message preview
        updateMessagePreview();
    }

    private void updateDashboardStats() {
        // Calculate stats
        int totalCustomers = customers.size();
        long activeCustomers = customers.stream()
            .filter(c -> c.getDaysSinceLastVisit().equals("Today") || 
                        c.getDaysSinceLastVisit().equals("Yesterday") ||
                        c.getDaysSinceLastVisit().contains("days ago") && 
                        Integer.parseInt(c.getDaysSinceLastVisit().split(" ")[0]) <= 7)
            .count();
        
        double totalRevenue = customers.stream()
            .mapToDouble(Customer::getTotalSpent)
            .sum();
        
        double averageRating = feedbacks.stream()
            .mapToInt(CustomerFeedback::getRating)
            .average()
            .orElse(0.0);

        // Update labels
        totalCustomersLabel.setText(String.valueOf(totalCustomers));
        activeCustomersLabel.setText(String.valueOf(activeCustomers));
        averageRatingLabel.setText(String.format("%.1f", averageRating));
        totalRevenueLabel.setText(String.format("₹%.0f", totalRevenue));

        // Update segment counts
        updateSegmentCounts();
    }

    private void updateSegmentCounts() {
        Map<String, Long> segmentCounts = customers.stream()
            .collect(Collectors.groupingBy(Customer::getSegment, Collectors.counting()));

        newCustomersCount.setText(String.valueOf(segmentCounts.getOrDefault("New Customer", 0L)));
        regularCustomersCount.setText(String.valueOf(segmentCounts.getOrDefault("Regular", 0L)));
        highSpendersCount.setText(String.valueOf(segmentCounts.getOrDefault("High Spender", 0L)));
        lapsedCustomersCount.setText(String.valueOf(segmentCounts.getOrDefault("Lapsed Customer", 0L)));
    }

    private void displayCustomers() {
        customerTableContainer.getChildren().clear();
        
        List<Customer> filteredCustomers = customers.stream()
            .filter(this::matchesSearchAndFilter)
            .collect(Collectors.toList());

        for (Customer customer : filteredCustomers) {
            HBox customerRow = createCustomerRow(customer);
            customerTableContainer.getChildren().add(customerRow);
        }
    }

    private boolean matchesSearchAndFilter(Customer customer) {
        // Search filter
        String searchText = searchField.getText().toLowerCase();
        boolean matchesSearch = searchText.isEmpty() || 
            customer.getName().toLowerCase().contains(searchText) ||
            customer.getPhone().contains(searchText) ||
            (customer.getEmail() != null && customer.getEmail().toLowerCase().contains(searchText));

        // Segment filter
        boolean matchesSegment = "All".equals(currentFilter) || 
            customer.getSegment().equals(currentFilter);

        return matchesSearch && matchesSegment;
    }

    private HBox createCustomerRow(Customer customer) {
        HBox row = new HBox(15);
        row.setAlignment(Pos.CENTER_LEFT);
        row.getStyleClass().add("customer-row");
        row.setPadding(new Insets(12, 15, 12, 15));

        // Customer Info
        VBox customerInfo = new VBox(2);
        customerInfo.setPrefWidth(200);
        Label nameLabel = new Label(customer.getName());
        nameLabel.getStyleClass().add("customer-name");
        Label membershipLabel = new Label(customer.getMembershipLevel());
        membershipLabel.getStyleClass().addAll("membership-badge", customer.getMembershipLevel().toLowerCase().replace(" ", "-"));
        customerInfo.getChildren().addAll(nameLabel, membershipLabel);
        HBox.setHgrow(customerInfo, Priority.ALWAYS);

        // Contact
        VBox contactInfo = new VBox(2);
        contactInfo.setPrefWidth(150);
        Label phoneLabel = new Label(customer.getPhone());
        phoneLabel.getStyleClass().add("customer-phone");
        Label emailLabel = new Label(customer.getEmail() != null ? customer.getEmail() : "No email");
        emailLabel.getStyleClass().add("customer-email");
        contactInfo.getChildren().addAll(phoneLabel, emailLabel);

        // Orders
        Label ordersLabel = new Label(String.valueOf(customer.getTotalOrders()));
        ordersLabel.getStyleClass().add("customer-orders");
        ordersLabel.setPrefWidth(60);

        // Total Spent
        Label spentLabel = new Label(customer.getFormattedTotalSpent());
        spentLabel.getStyleClass().add("customer-spent");
        spentLabel.setPrefWidth(100);

        // Segment
        Label segmentLabel = new Label(customer.getSegment());
        segmentLabel.getStyleClass().addAll("segment-badge", customer.getSegment().toLowerCase().replace(" ", "-"));
        segmentLabel.setPrefWidth(120);

        // Last Visit
        Label visitLabel = new Label(customer.getDaysSinceLastVisit());
        visitLabel.getStyleClass().add("customer-visit");
        visitLabel.setPrefWidth(100);

        // Actions
        HBox actionsBox = new HBox(5);
        actionsBox.setAlignment(Pos.CENTER);
        actionsBox.setPrefWidth(120);

        Button viewBtn = new Button("👁");
        viewBtn.getStyleClass().add("action-btn-small");
        viewBtn.setOnAction(e -> showCustomerDetails(customer));

        Button editBtn = new Button("✏");
        editBtn.getStyleClass().add("action-btn-small");
        editBtn.setOnAction(e -> editCustomer(customer));

        Button messageBtn = new Button("💬");
        messageBtn.getStyleClass().add("action-btn-small");
        messageBtn.setOnAction(e -> sendMessage(customer));

        actionsBox.getChildren().addAll(viewBtn, editBtn, messageBtn);

        row.getChildren().addAll(customerInfo, contactInfo, ordersLabel, spentLabel, segmentLabel, visitLabel, actionsBox);

        return row;
    }

    private void displayRecentActivity() {
        recentActivityContainer.getChildren().clear();

        // Create recent activity items
        List<String> activities = Arrays.asList(
            "🆕 New customer Vikram Singh registered",
            "⭐ Sneha Reddy left a 5-star review",
            "📱 Weekend Special campaign sent to 150 customers",
            "🎁 Priya Sharma redeemed 500 loyalty points",
            "💬 Negative feedback received from Amit Patel - Action needed",
            "🔄 Rajesh Kumar made repeat order - Butter Chicken"
        );

        for (String activity : activities) {
            Label activityLabel = new Label(activity);
            activityLabel.getStyleClass().add("activity-item");
            activityLabel.setPadding(new Insets(8, 12, 8, 12));
            recentActivityContainer.getChildren().add(activityLabel);
        }
    }

    // Action Methods
    @FXML
    private void goBack() {
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/Dashboard.fxml"));
            javafx.scene.Parent root = loader.load();

            javafx.stage.Stage stage = (javafx.stage.Stage) backButton.getScene().getWindow();
            stage.getScene().setRoot(root);

            System.out.println("Navigated back to dashboard from Customer CRM");

        } catch (Exception e) {
            System.err.println("Error navigating back to dashboard: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @FXML
    private void showAddCustomerDialog() {
        showAlert("Add Customer", "Add Customer dialog would open here.\n\nFeatures:\n• Customer details form\n• Contact information\n• Preferences setup\n• Loyalty program enrollment");
    }

    @FXML
    private void showCampaignDialog() {
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/CampaignCreator.fxml"));
            javafx.scene.Parent root = loader.load();

            javafx.stage.Stage stage = (javafx.stage.Stage) sendCampaignBtn.getScene().getWindow();
            stage.getScene().setRoot(root);

            System.out.println("Navigated to Campaign Creator from Customer CRM");

        } catch (Exception e) {
            System.err.println("Error navigating to Campaign Creator: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Failed to load Campaign Creator: " + e.getMessage());
        }
    }

    @FXML
    private void showCustomerHistory() {
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/CustomerHistoryView.fxml"));
            javafx.scene.Parent root = loader.load();

            javafx.stage.Stage stage = (javafx.stage.Stage) viewHistoryBtn.getScene().getWindow();
            stage.getScene().setRoot(root);

            System.out.println("Navigated to Customer History from Customer CRM");

        } catch (Exception e) {
            System.err.println("Error navigating to Customer History: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Failed to load Customer History: " + e.getMessage());
        }
    }

    @FXML
    private void showFeedbackDialog() {
        StringBuilder feedbackSummary = new StringBuilder("Recent Customer Feedback:\n\n");

        for (CustomerFeedback feedback : feedbacks) {
            feedbackSummary.append(feedback.getRatingStars())
                          .append(" ")
                          .append(feedback.getCustomerName())
                          .append("\n")
                          .append(feedback.getComment())
                          .append("\n\n");
        }

        showAlert("Customer Feedback", feedbackSummary.toString());
    }

    @FXML
    private void showLoyaltyDialog() {
        StringBuilder loyaltyInfo = new StringBuilder("Loyalty Program Status:\n\n");

        for (Customer customer : customers) {
            loyaltyInfo.append(customer.getName())
                      .append(": ")
                      .append(customer.getLoyaltyPoints())
                      .append(" points (")
                      .append(customer.getMembershipLevel())
                      .append(")\n");
        }

        showAlert("Loyalty Program", loyaltyInfo.toString());
    }

    // Segment Filter Methods
    @FXML
    private void filterNewCustomers() {
        segmentFilter.setValue("New Customer");
        currentFilter = "New Customer";
        displayCustomers();
    }

    @FXML
    private void filterRegularCustomers() {
        segmentFilter.setValue("Regular");
        currentFilter = "Regular";
        displayCustomers();
    }

    @FXML
    private void filterHighSpenders() {
        segmentFilter.setValue("High Spender");
        currentFilter = "High Spender";
        displayCustomers();
    }

    @FXML
    private void filterLapsedCustomers() {
        segmentFilter.setValue("Lapsed Customer");
        currentFilter = "Lapsed Customer";
        displayCustomers();
    }

    // Customer Action Methods
    private void showCustomerDetails(Customer customer) {
        StringBuilder details = new StringBuilder();
        details.append("Customer Details:\n\n");
        details.append("Name: ").append(customer.getName()).append("\n");
        details.append("Phone: ").append(customer.getPhone()).append("\n");
        details.append("Email: ").append(customer.getEmail()).append("\n");
        details.append("Address: ").append(customer.getAddress()).append("\n");
        details.append("Total Orders: ").append(customer.getTotalOrders()).append("\n");
        details.append("Total Spent: ").append(customer.getFormattedTotalSpent()).append("\n");
        details.append("Average Spend: ").append(customer.getFormattedAverageSpend()).append("\n");
        details.append("Loyalty Points: ").append(customer.getLoyaltyPoints()).append("\n");
        details.append("Membership: ").append(customer.getMembershipLevel()).append("\n");
        details.append("Segment: ").append(customer.getSegment()).append("\n");
        details.append("Preferred Dishes: ").append(customer.getPreferredDishesString()).append("\n");
        details.append("Last Visit: ").append(customer.getDaysSinceLastVisit()).append("\n");

        showAlert("Customer Details", details.toString());
    }

    private void editCustomer(Customer customer) {
        showAlert("Edit Customer", "Edit customer dialog would open here for: " + customer.getName() + "\n\nFeatures:\n• Update contact information\n• Modify preferences\n• Adjust loyalty points\n• Add notes");
    }

    private void sendMessage(Customer customer) {
        showAlert("Send Message", "Message dialog would open here for: " + customer.getName() + "\n\nFeatures:\n• SMS campaigns\n• WhatsApp messages\n• Email marketing\n• Personalized offers");
    }

    // Birthday Campaign Methods
    @FXML
    private void showBirthdayCampaign() {
        birthdayCampaignSection.setVisible(true);
        birthdayCampaignSection.setManaged(true);
        displayCampaignCustomers();
        updateSelectedCount();
    }

    @FXML
    private void closeBirthdayCampaign() {
        birthdayCampaignSection.setVisible(false);
        birthdayCampaignSection.setManaged(false);
        selectedCustomers.clear();
    }

    @FXML
    private void selectSchedule() {
        selectCampaignType("Schedule", scheduleBtn);
    }

    @FXML
    private void selectOnBillPrint() {
        selectCampaignType("On Bill Print", onBillPrintBtn);
    }

    @FXML
    private void selectBirthday() {
        selectCampaignType("Birthday", birthdayBtn);
    }

    @FXML
    private void selectAnniversary() {
        selectCampaignType("Anniversary", anniversaryBtn);
    }

    private void selectCampaignType(String type, Button selectedButton) {
        selectedCampaignType = type;

        // Update button styles
        if (scheduleBtn != null) scheduleBtn.getStyleClass().remove("selected");
        if (onBillPrintBtn != null) onBillPrintBtn.getStyleClass().remove("selected");
        if (birthdayBtn != null) birthdayBtn.getStyleClass().remove("selected");
        if (anniversaryBtn != null) anniversaryBtn.getStyleClass().remove("selected");

        selectedButton.getStyleClass().add("selected");

        // Update campaign name
        if (campaignNameField != null) {
            campaignNameField.setText(type + " Special Campaign");
        }

        // Update message preview
        updateMessagePreview();
    }

    private void updateMessagePreview() {
        if (campaignTemplates == null || messageTitle == null || messageBody == null) return;

        String template = campaignTemplates.get(selectedCampaignType);
        String discount = (discountField != null && !discountField.getText().isEmpty()) ? discountField.getText() : "30";

        if (template != null) {
            String message = template.replace("{discount}", discount);
            String[] parts = message.split("\n\n", 2);

            messageTitle.setText(parts[0]);
            if (parts.length > 1) {
                messageBody.setText(parts[1]);
            }
        }

        if (messageSender != null) {
            messageSender.setText("Sent by " + (campaignNameField != null && !campaignNameField.getText().isEmpty() ? "RESTAURANT" : "RESTAURANT"));
        }
    }

    private void displayCampaignCustomers() {
        if (campaignCustomerListContainer == null) return;

        campaignCustomerListContainer.getChildren().clear();

        String searchText = (customerSearchField != null) ? customerSearchField.getText().toLowerCase() : "";
        String segmentFilterValue = (segmentFilterCombo != null) ? segmentFilterCombo.getValue() : "All";
        final String segmentFilter = (segmentFilterValue != null) ? segmentFilterValue : "All";

        List<Customer> filteredCustomers = customers.stream()
            .filter(customer -> {
                // Search filter
                boolean matchesSearch = searchText.isEmpty() ||
                    customer.getName().toLowerCase().contains(searchText) ||
                    customer.getPhone().contains(searchText);

                // Segment filter
                boolean matchesSegment = "All".equals(segmentFilter) ||
                    customer.getSegment().equals(segmentFilter);

                return matchesSearch && matchesSegment;
            })
            .collect(Collectors.toList());

        for (Customer customer : filteredCustomers) {
            HBox customerRow = createCampaignCustomerRow(customer);
            campaignCustomerListContainer.getChildren().add(customerRow);
        }
    }

    private HBox createCampaignCustomerRow(Customer customer) {
        HBox row = new HBox();
        row.setAlignment(Pos.CENTER_LEFT);
        row.getStyleClass().add("customer-selection-row");
        row.setPadding(new Insets(12, 15, 12, 15));

        // Checkbox
        CheckBox checkbox = new CheckBox();
        checkbox.getStyleClass().add("customer-checkbox");
        checkbox.setSelected(selectedCustomers.contains(customer));
        checkbox.setOnAction(e -> {
            if (checkbox.isSelected()) {
                if (!selectedCustomers.contains(customer)) {
                    selectedCustomers.add(customer);
                }
            } else {
                selectedCustomers.remove(customer);
            }
            updateSelectedCount();
            updateSelectAllCheckbox();
        });

        // Phone
        Label phoneLabel = new Label(customer.getPhone());
        phoneLabel.getStyleClass().add("customer-phone-label");
        phoneLabel.setPrefWidth(150);

        // Name
        Label nameLabel = new Label(customer.getName());
        nameLabel.getStyleClass().add("customer-name-label");
        nameLabel.setPrefWidth(150);

        // Segment
        Label segmentLabel = new Label(customer.getSegment());
        segmentLabel.getStyleClass().addAll("segment-badge-small", customer.getSegment().toLowerCase().replace(" ", "-"));
        segmentLabel.setPrefWidth(120);

        // Birthday (extracted from notes)
        String birthday = extractBirthday(customer.getNotes());
        Label birthdayLabel = new Label(birthday);
        birthdayLabel.getStyleClass().add("customer-birthday-label");
        birthdayLabel.setPrefWidth(100);

        row.getChildren().addAll(checkbox, phoneLabel, nameLabel, segmentLabel, birthdayLabel);

        return row;
    }

    private String extractBirthday(String notes) {
        if (notes != null && notes.contains("Birthday:")) {
            String[] parts = notes.split("Birthday:");
            if (parts.length > 1) {
                return parts[1].trim();
            }
        }
        return "Not set";
    }

    @FXML
    private void selectAllCustomers() {
        selectedCustomers.clear();
        selectedCustomers.addAll(customers);
        if (selectAllCheckbox != null) selectAllCheckbox.setSelected(true);
        displayCampaignCustomers();
        updateSelectedCount();
    }

    @FXML
    private void clearAllCustomers() {
        selectedCustomers.clear();
        if (selectAllCheckbox != null) selectAllCheckbox.setSelected(false);
        displayCampaignCustomers();
        updateSelectedCount();
    }

    @FXML
    private void toggleSelectAll() {
        if (selectAllCheckbox != null) {
            if (selectAllCheckbox.isSelected()) {
                selectAllCustomers();
            } else {
                clearAllCustomers();
            }
        }
    }

    private void updateSelectedCount() {
        if (selectedCountLabel != null) {
            int count = selectedCustomers.size();
            selectedCountLabel.setText(count + " customer" + (count != 1 ? "s" : "") + " selected");
        }
    }

    private void updateSelectAllCheckbox() {
        if (selectAllCheckbox != null) {
            selectAllCheckbox.setSelected(selectedCustomers.size() == customers.size());
        }
    }

    @FXML
    private void previewCampaign() {
        if (selectedCustomers.isEmpty()) {
            showAlert("No Recipients", "Please select at least one customer to preview the campaign.");
            return;
        }

        StringBuilder preview = new StringBuilder();
        preview.append("Campaign Preview:\n\n");
        preview.append("Type: ").append(selectedCampaignType).append("\n");
        preview.append("Name: ").append(campaignNameField != null ? campaignNameField.getText() : "").append("\n");
        preview.append("Message Type: ").append(messageTypeCombo != null ? messageTypeCombo.getValue() : "").append("\n");
        preview.append("Discount: ").append(discountField != null ? discountField.getText() : "").append("%\n");
        preview.append("Valid Days: ").append(validDaysField != null ? validDaysField.getText() : "").append("\n");
        preview.append("Recipients: ").append(selectedCustomers.size()).append(" customers\n\n");

        preview.append("Message:\n");
        preview.append(messageTitle != null ? messageTitle.getText() : "").append("\n\n");
        preview.append(messageBody != null ? messageBody.getText() : "").append("\n\n");
        preview.append(messageSender != null ? messageSender.getText() : "").append("\n\n");

        preview.append("Recipients:\n");
        for (Customer customer : selectedCustomers.stream().limit(5).collect(Collectors.toList())) {
            preview.append("• ").append(customer.getName()).append(" (").append(customer.getPhone()).append(")\n");
        }
        if (selectedCustomers.size() > 5) {
            preview.append("... and ").append(selectedCustomers.size() - 5).append(" more");
        }

        showAlert("Campaign Preview", preview.toString());
    }

    @FXML
    private void saveDraft() {
        showAlert("Draft Saved", "Campaign draft has been saved successfully!\n\n" +
            "Campaign: " + (campaignNameField != null ? campaignNameField.getText() : "") + "\n" +
            "Type: " + selectedCampaignType + "\n" +
            "Recipients: " + selectedCustomers.size() + " customers");
    }

    @FXML
    private void sendCampaign() {
        if (selectedCustomers.isEmpty()) {
            showAlert("No Recipients", "Please select at least one customer to send the campaign.");
            return;
        }

        StringBuilder result = new StringBuilder();
        result.append("Campaign sent successfully!\n\n");
        result.append("Campaign: ").append(campaignNameField != null ? campaignNameField.getText() : "").append("\n");
        result.append("Type: ").append(selectedCampaignType).append("\n");
        result.append("Message Type: ").append(messageTypeCombo != null ? messageTypeCombo.getValue() : "").append("\n");
        result.append("Recipients: ").append(selectedCustomers.size()).append(" customers\n");
        result.append("Delivery Rate: 95%\n\n");
        result.append("The campaign has been sent to all selected customers!");

        showAlert("Campaign Sent", result.toString());

        // Close campaign after sending
        closeBirthdayCampaign();
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    // Customer Tags and Information Methods

    @FXML
    private void filterByVIP() {
        filterCustomersByTag("VIP Client");
    }

    @FXML
    private void filterByCorporate() {
        filterCustomersByTag("Corporate Client");
    }

    @FXML
    private void filterByPremium() {
        filterCustomersByTag("Premium Client");
    }

    @FXML
    private void showAddTagDialog() {
        // Show dialog to add new tag
        TextInputDialog dialog = new TextInputDialog();
        dialog.setTitle("Add New Tag");
        dialog.setHeaderText("Create New Customer Tag");
        dialog.setContentText("Enter tag name:");

        Optional<String> result = dialog.showAndWait();
        result.ifPresent(tagName -> {
            if (!tagName.trim().isEmpty()) {
                // Add new tag button
                Button newTagBtn = new Button(tagName);
                newTagBtn.getStyleClass().addAll("tag-button", "tag-custom");
                newTagBtn.setOnAction(e -> filterCustomersByTag(tagName));

                // Add to tags list (before the Add Tag button)
                VBox tagsList = (VBox) addTagBtn.getParent();
                int addButtonIndex = tagsList.getChildren().indexOf(addTagBtn);
                tagsList.getChildren().add(addButtonIndex, newTagBtn);
            }
        });
    }

    @FXML
    private void showCustomerClassificationDialog() {
        // Create dialog for customer classification and management
        Dialog<ButtonType> dialog = new Dialog<>();
        dialog.setTitle("Customer Classification & Management");
        dialog.setHeaderText("Automatic Customer Classification Rules & Manual Assignment");

        // Create content
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));

        // Automatic Classification Rules Section
        Label rulesTitle = new Label("🤖 Automatic Classification Rules");
        rulesTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");

        VBox rulesSection = new VBox(10);
        rulesSection.getChildren().addAll(
            new Label("📊 VIP Client: Total spent ≥ ₹50,000 OR Orders ≥ 20"),
            new Label("🏢 Corporate Client: Manually assigned for business customers"),
            new Label("⭐ Premium Client: Total spent ≥ ₹20,000 OR Orders ≥ 10"),
            new Label("🆕 New Customer: Less than 5 orders"),
            new Label("🔄 Regular Customer: 5-9 orders, spent < ₹20,000"),
            new Label("😴 Lapsed Customer: No visit in last 30 days")
        );

        // Manual Assignment Section
        Label manualTitle = new Label("👤 Manual Customer Assignment");
        manualTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");

        HBox manualSection = new HBox(15);

        // Customer selection
        VBox customerSelection = new VBox(10);
        Label selectLabel = new Label("Select Customer:");
        ComboBox<Customer> customerCombo = new ComboBox<>();
        customerCombo.getItems().addAll(customers);
        customerCombo.setConverter(new StringConverter<Customer>() {
            @Override
            public String toString(Customer customer) {
                return customer != null ? customer.getName() + " (" + customer.getPhone() + ")" : "";
            }

            @Override
            public Customer fromString(String string) {
                return null;
            }
        });
        customerSelection.getChildren().addAll(selectLabel, customerCombo);

        // Tag selection
        VBox tagSelection = new VBox(10);
        Label tagLabel = new Label("Assign Tag:");
        ComboBox<String> tagCombo = new ComboBox<>();
        tagCombo.getItems().addAll("VIP Client", "Corporate Client", "Premium Client", "Regular Customer", "New Customer");
        tagSelection.getChildren().addAll(tagLabel, tagCombo);

        // Assign button
        Button assignBtn = new Button("Assign Tag");
        assignBtn.setOnAction(e -> {
            Customer selectedCustomer = customerCombo.getValue();
            String selectedTag = tagCombo.getValue();
            if (selectedCustomer != null && selectedTag != null) {
                selectedCustomer.setSegment(selectedTag);
                displayCustomerInfo();
                showAlert("Success", "Tag assigned successfully to " + selectedCustomer.getName());
            }
        });

        manualSection.getChildren().addAll(customerSelection, tagSelection, assignBtn);

        // Add New Customer Section
        Label addCustomerTitle = new Label("➕ Add New Customer");
        addCustomerTitle.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");

        GridPane addCustomerGrid = new GridPane();
        addCustomerGrid.setHgap(10);
        addCustomerGrid.setVgap(10);

        TextField nameField = new TextField();
        nameField.setPromptText("Customer Name");
        TextField phoneField = new TextField();
        phoneField.setPromptText("Phone Number");
        TextField emailField = new TextField();
        emailField.setPromptText("Email Address");
        ComboBox<String> newCustomerTagCombo = new ComboBox<>();
        newCustomerTagCombo.getItems().addAll("VIP Client", "Corporate Client", "Premium Client", "Regular Customer", "New Customer");
        newCustomerTagCombo.setValue("New Customer");

        addCustomerGrid.add(new Label("Name:"), 0, 0);
        addCustomerGrid.add(nameField, 1, 0);
        addCustomerGrid.add(new Label("Phone:"), 0, 1);
        addCustomerGrid.add(phoneField, 1, 1);
        addCustomerGrid.add(new Label("Email:"), 2, 0);
        addCustomerGrid.add(emailField, 3, 0);
        addCustomerGrid.add(new Label("Tag:"), 2, 1);
        addCustomerGrid.add(newCustomerTagCombo, 3, 1);

        Button addCustomerBtn = new Button("Add Customer");
        addCustomerBtn.setOnAction(e -> {
            if (!nameField.getText().trim().isEmpty() && !phoneField.getText().trim().isEmpty()) {
                Customer newCustomer = new Customer(customers.size() + 1, nameField.getText().trim(),
                                                  phoneField.getText().trim(), emailField.getText().trim());
                newCustomer.setSegment(newCustomerTagCombo.getValue());
                customers.add(newCustomer);
                displayCustomerInfo();
                displayCustomers();
                showAlert("Success", "New customer added successfully!");
                nameField.clear();
                phoneField.clear();
                emailField.clear();
                newCustomerTagCombo.setValue("New Customer");
            } else {
                showAlert("Error", "Please fill in at least Name and Phone fields.");
            }
        });

        // Auto-classify button
        Button autoClassifyBtn = new Button("🤖 Auto-Classify All Customers");
        autoClassifyBtn.setStyle("-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-weight: bold;");
        autoClassifyBtn.setOnAction(e -> {
            autoClassifyCustomers();
            displayCustomerInfo();
            showAlert("Success", "All customers have been automatically classified!");
        });

        content.getChildren().addAll(
            rulesTitle, rulesSection,
            new Separator(),
            manualTitle, manualSection,
            new Separator(),
            addCustomerTitle, addCustomerGrid, addCustomerBtn,
            new Separator(),
            autoClassifyBtn
        );

        ScrollPane scrollPane = new ScrollPane(content);
        scrollPane.setFitToWidth(true);
        scrollPane.setPrefSize(800, 600);

        dialog.getDialogPane().setContent(scrollPane);
        dialog.getDialogPane().getButtonTypes().addAll(ButtonType.CLOSE);

        dialog.showAndWait();
    }

    private void filterCustomersByTag(String tag) {
        // Update customer info display based on selected tag
        displayCustomerInfo(tag);
    }

    private void displayCustomerInfo() {
        displayCustomerInfo(null);
    }

    private void displayCustomerInfo(String filterTag) {
        if (customerInfoContainer == null) return;

        customerInfoContainer.getChildren().clear();

        // Filter customers by tag if specified
        List<Customer> filteredCustomers = customers;
        if (filterTag != null) {
            filteredCustomers = customers.stream()
                .filter(customer -> customer.getSegment().equals(filterTag))
                .collect(Collectors.toList());
        }

        // Create customer info rows
        for (Customer customer : filteredCustomers) {
            HBox customerRow = createCustomerInfoRow(customer);
            customerInfoContainer.getChildren().add(customerRow);
        }
    }

    private HBox createCustomerInfoRow(Customer customer) {
        HBox row = new HBox();
        row.getStyleClass().add("customer-info-row");
        row.setSpacing(15.0);

        // Name
        Label nameLabel = new Label(customer.getName());
        nameLabel.getStyleClass().add("info-name");
        nameLabel.setPrefWidth(150);

        // Tag
        Label tagLabel = new Label(customer.getSegment());
        tagLabel.getStyleClass().addAll("info-tag", getTagStyleClass(customer.getSegment()));
        tagLabel.setPrefWidth(120);

        // Created date
        String formattedDate = customer.getCreatedAt().format(DateTimeFormatter.ofPattern("d MMM, yyyy"));
        Label dateLabel = new Label(formattedDate);
        dateLabel.getStyleClass().add("info-date");
        dateLabel.setPrefWidth(100);

        row.getChildren().addAll(nameLabel, tagLabel, dateLabel);

        return row;
    }

    private String getTagStyleClass(String segment) {
        switch (segment) {
            case "VIP Client":
                return "tag-vip-label";
            case "Corporate Client":
                return "tag-corporate-label";
            case "Premium Client":
                return "tag-premium-label";
            default:
                return "tag-default-label";
        }
    }

    private void autoClassifyCustomers() {
        for (Customer customer : customers) {
            String newSegment = determineCustomerSegment(customer);
            customer.setSegment(newSegment);
        }
    }

    private String determineCustomerSegment(Customer customer) {
        double totalSpent = customer.getTotalSpent();
        int totalOrders = customer.getTotalOrders();
        long daysSinceLastVisit = getDaysSinceLastVisit(customer);

        // VIP Client: High spending or frequent orders
        if (totalSpent >= 50000 || totalOrders >= 20) {
            return "VIP Client";
        }

        // Premium Client: Medium-high spending or regular orders
        if (totalSpent >= 20000 || totalOrders >= 10) {
            return "Premium Client";
        }

        // Lapsed Customer: No recent activity
        if (daysSinceLastVisit > 30) {
            return "Lapsed Customer";
        }

        // Regular Customer: Some activity
        if (totalOrders >= 5) {
            return "Regular Customer";
        }

        // New Customer: Limited activity
        return "New Customer";
    }

    private long getDaysSinceLastVisit(Customer customer) {
        if (customer.getLastVisit() == null) {
            return 999; // Very high number for customers who never visited
        }
        return java.time.Duration.between(customer.getLastVisit(), LocalDateTime.now()).toDays();
    }

    @FXML
    private void showCustomerManagement() {
        showCustomerClassificationDialog();
    }
}
