<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.chart.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.EnhancedReportsController">
   <top>
      <!-- Navigation Bar -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-background-color: #2c3e50; -fx-padding: 10;">
         <children>
            <Label text="📊 Advanced Reports &amp; Analytics" textFill="WHITE">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="homeBtn" mnemonicParsing="false" style="-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-weight: bold;" text="🏠 Home" />
            <Button fx:id="notificationsBtn" mnemonicParsing="false" style="-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-weight: bold;" text="🔔 Notifications" />
         </children>
      </HBox>
   </top>
   <center>
      <TabPane fx:id="reportsTabPane" tabClosingPolicy="UNAVAILABLE">
         <tabs>
            <!-- Analytics Dashboard Tab -->
            <Tab text="📈 Analytics Dashboard">
               <content>
                  <ScrollPane fitToWidth="true">
                     <content>
                        <VBox fx:id="analyticsContainer" spacing="20.0">
                           <padding>
                              <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                           </padding>
                           <children>
                              <!-- Summary Cards -->
                              <HBox spacing="20.0">
                                 <children>
                                    <!-- Today's Stats -->
                                    <VBox alignment="CENTER" spacing="10.0" style="-fx-background-color: #3498db; -fx-background-radius: 10; -fx-padding: 20;" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label text="Today" textFill="WHITE">
                                             <font>
                                                <Font name="System Bold" size="16.0" />
                                             </font>
                                          </Label>
                                          <Label fx:id="todayOrdersLabel" text="0" textFill="WHITE">
                                             <font>
                                                <Font name="System Bold" size="24.0" />
                                             </font>
                                          </Label>
                                          <Label text="Orders" textFill="WHITE" />
                                          <Label fx:id="todayRevenueLabel" text="₹0.00" textFill="WHITE">
                                             <font>
                                                <Font name="System Bold" size="18.0" />
                                             </font>
                                          </Label>
                                       </children>
                                    </VBox>
                                    <!-- This Week's Stats -->
                                    <VBox alignment="CENTER" spacing="10.0" style="-fx-background-color: #2ecc71; -fx-background-radius: 10; -fx-padding: 20;" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label text="This Week" textFill="WHITE">
                                             <font>
                                                <Font name="System Bold" size="16.0" />
                                             </font>
                                          </Label>
                                          <Label fx:id="weekOrdersLabel" text="0" textFill="WHITE">
                                             <font>
                                                <Font name="System Bold" size="24.0" />
                                             </font>
                                          </Label>
                                          <Label text="Orders" textFill="WHITE" />
                                          <Label fx:id="weekRevenueLabel" text="₹0.00" textFill="WHITE">
                                             <font>
                                                <Font name="System Bold" size="18.0" />
                                             </font>
                                          </Label>
                                       </children>
                                    </VBox>
                                    <!-- This Month's Stats -->
                                    <VBox alignment="CENTER" spacing="10.0" style="-fx-background-color: #e74c3c; -fx-background-radius: 10; -fx-padding: 20;" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label text="This Month" textFill="WHITE">
                                             <font>
                                                <Font name="System Bold" size="16.0" />
                                             </font>
                                          </Label>
                                          <Label fx:id="monthOrdersLabel" text="0" textFill="WHITE">
                                             <font>
                                                <Font name="System Bold" size="24.0" />
                                             </font>
                                          </Label>
                                          <Label text="Orders" textFill="WHITE" />
                                          <Label fx:id="monthRevenueLabel" text="₹0.00" textFill="WHITE">
                                             <font>
                                                <Font name="System Bold" size="18.0" />
                                             </font>
                                          </Label>
                                          <Label fx:id="monthGrowthLabel" text="0.0%" textFill="WHITE">
                                             <font>
                                                <Font name="System Bold" size="14.0" />
                                             </font>
                                          </Label>
                                       </children>
                                    </VBox>
                                 </children>
                              </HBox>
                              <!-- Charts Row -->
                              <HBox spacing="20.0">
                                 <children>
                                    <!-- Platform Distribution Chart -->
                                    <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label text="Platform Distribution" style="-fx-font-weight: bold; -fx-font-size: 16px;" />
                                          <PieChart fx:id="platformChart" prefHeight="300.0" />
                                       </children>
                                    </VBox>
                                    <!-- Revenue Trend Chart -->
                                    <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                                       <children>
                                          <Label text="Revenue Trend (Last 7 Days)" style="-fx-font-weight: bold; -fx-font-size: 16px;" />
                                          <LineChart fx:id="revenueChart" prefHeight="300.0">
                                             <xAxis>
                                                <CategoryAxis side="BOTTOM" />
                                             </xAxis>
                                             <yAxis>
                                                <NumberAxis side="LEFT" />
                                             </yAxis>
                                          </LineChart>
                                       </children>
                                    </VBox>
                                 </children>
                              </HBox>
                           </children>
                        </VBox>
                     </content>
                  </ScrollPane>
               </content>
            </Tab>
            <!-- Daily Reports Tab -->
            <Tab text="📅 Daily Reports">
               <content>
                  <VBox spacing="20.0">
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                     <children>
                        <!-- Daily Report Controls -->
                        <HBox alignment="CENTER_LEFT" spacing="15.0">
                           <children>
                              <Label text="Select Date:" style="-fx-font-weight: bold;" />
                              <DatePicker fx:id="dailyDatePicker" />
                              <Button fx:id="generateDailyReportBtn" style="-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-weight: bold;" text="📊 Generate Daily Report" />
                           </children>
                        </HBox>
                        <!-- Daily Reports Table -->
                        <TableView fx:id="dailyReportsTable" prefHeight="400.0">
                           <columns>
                              <TableColumn fx:id="dailyDateColumn" prefWidth="120.0" text="Date" />
                              <TableColumn fx:id="dailyOrdersColumn" prefWidth="100.0" text="Total Orders" />
                              <TableColumn fx:id="dailyRevenueColumn" prefWidth="120.0" text="Revenue" />
                              <TableColumn fx:id="dailySwiggyColumn" prefWidth="100.0" text="Swiggy Orders" />
                              <TableColumn fx:id="dailyZomatoColumn" prefWidth="100.0" text="Zomato Orders" />
                              <TableColumn fx:id="dailyPeakHourColumn" prefWidth="150.0" text="Peak Hour" />
                           </columns>
                        </TableView>
                     </children>
                  </VBox>
               </content>
            </Tab>
            <!-- Weekly Reports Tab -->
            <Tab text="📊 Weekly Reports">
               <content>
                  <VBox spacing="20.0">
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                     <children>
                        <!-- Weekly Report Controls -->
                        <HBox alignment="CENTER_LEFT" spacing="15.0">
                           <children>
                              <Label text="Week Start:" style="-fx-font-weight: bold;" />
                              <DatePicker fx:id="weeklyStartDatePicker" />
                              <Label text="Week End:" style="-fx-font-weight: bold;" />
                              <DatePicker fx:id="weeklyEndDatePicker" />
                              <Button fx:id="generateWeeklyReportBtn" style="-fx-background-color: #2ecc71; -fx-text-fill: white; -fx-font-weight: bold;" text="📈 Generate Weekly Report" />
                           </children>
                        </HBox>
                        <!-- Weekly Reports Table -->
                        <TableView fx:id="weeklyReportsTable" prefHeight="400.0">
                           <columns>
                              <TableColumn fx:id="weeklyPeriodColumn" prefWidth="200.0" text="Week Period" />
                              <TableColumn fx:id="weeklyOrdersColumn" prefWidth="120.0" text="Total Orders" />
                              <TableColumn fx:id="weeklyRevenueColumn" prefWidth="120.0" text="Revenue" />
                              <TableColumn fx:id="weeklyAvgOrdersColumn" prefWidth="120.0" text="Avg Daily Orders" />
                              <TableColumn fx:id="weeklyBestDayColumn" prefWidth="150.0" text="Best Day" />
                           </columns>
                        </TableView>
                     </children>
                  </VBox>
               </content>
            </Tab>
            <!-- Monthly Reports Tab -->
            <Tab text="📆 Monthly Reports">
               <content>
                  <VBox spacing="20.0">
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                     <children>
                        <!-- Monthly Report Controls -->
                        <HBox alignment="CENTER_LEFT" spacing="15.0">
                           <children>
                              <Label text="Month:" style="-fx-font-weight: bold;" />
                              <ComboBox fx:id="monthComboBox" prefWidth="100.0" />
                              <Label text="Year:" style="-fx-font-weight: bold;" />
                              <ComboBox fx:id="yearComboBox" prefWidth="100.0" />
                              <Button fx:id="generateMonthlyReportBtn" style="-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-weight: bold;" text="📊 Generate Monthly Report" />
                           </children>
                        </HBox>
                        <!-- Monthly Reports Table -->
                        <TableView fx:id="monthlyReportsTable" prefHeight="400.0">
                           <columns>
                              <TableColumn fx:id="monthlyPeriodColumn" prefWidth="150.0" text="Month" />
                              <TableColumn fx:id="monthlyOrdersColumn" prefWidth="120.0" text="Total Orders" />
                              <TableColumn fx:id="monthlyRevenueColumn" prefWidth="120.0" text="Revenue" />
                              <TableColumn fx:id="monthlyGrowthColumn" prefWidth="120.0" text="Growth %" />
                              <TableColumn fx:id="monthlyBestWeekColumn" prefWidth="150.0" text="Best Week" />
                           </columns>
                        </TableView>
                     </children>
                  </VBox>
               </content>
            </Tab>
         </tabs>
      </TabPane>
   </center>
</BorderPane>
