package com.restaurant.model;

/**
 * Model class for Monthly Trends Report data
 */
public class MonthlyTrendsReport {
    private String month;
    private int orders;
    private double sales;
    private String growth;
    
    public MonthlyTrendsReport() {}
    
    public MonthlyTrendsReport(String month, int orders, double sales, String growth) {
        this.month = month;
        this.orders = orders;
        this.sales = sales;
        this.growth = growth;
    }
    
    // Getters and Setters
    public String getMonth() {
        return month;
    }
    
    public void setMonth(String month) {
        this.month = month;
    }
    
    public int getOrders() {
        return orders;
    }
    
    public void setOrders(int orders) {
        this.orders = orders;
    }
    
    public double getSales() {
        return sales;
    }
    
    public void setSales(double sales) {
        this.sales = sales;
    }
    
    public String getGrowth() {
        return growth;
    }
    
    public void setGrowth(String growth) {
        this.growth = growth;
    }
    
    @Override
    public String toString() {
        return "MonthlyTrendsReport{" +
                "month='" + month + '\'' +
                ", orders=" + orders +
                ", sales=" + sales +
                ", growth='" + growth + '\'' +
                '}';
    }
}
