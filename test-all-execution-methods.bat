@echo off
echo Testing All JavaFX Execution Methods...
echo.

REM Set Java path
set JAVA_HOME=C:\Program Files\Java\jdk-23
set PATH=%JAVA_HOME%\bin;%PATH%

echo Preparing dependencies...
call mvn clean compile dependency:copy-dependencies
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to build project
    pause
    exit /b 1
)

echo.
echo ========================================
echo Testing Method 1: AntRun Plugin (Recommended)
echo ========================================
echo Running: mvn antrun:run
call mvn antrun:run
if %ERRORLEVEL% equ 0 (
    echo ✅ AntRun Plugin: SUCCESS
) else (
    echo ❌ AntRun Plugin: FAILED
)

echo.
echo ========================================
echo Testing Method 2: Profile-based execution
echo ========================================
echo Running: mvn compile -Prun-app
timeout /t 3 >nul
call mvn compile -Prun-app
if %ERRORLEVEL% equ 0 (
    echo ✅ Profile execution: SUCCESS
) else (
    echo ❌ Profile execution: FAILED
)

echo.
echo ========================================
echo Testing Method 3: JavaFX Maven Plugin
echo ========================================
echo Running: mvn javafx:run
timeout /t 3 >nul
call mvn javafx:run
if %ERRORLEVEL% equ 0 (
    echo ✅ JavaFX Plugin: SUCCESS
) else (
    echo ❌ JavaFX Plugin: FAILED
)

echo.
echo ========================================
echo SUMMARY
echo ========================================
echo All execution methods tested.
echo Recommended for NetBeans: mvn antrun:run
echo Alternative: mvn compile -Prun-app
echo.
pause
