package com.restaurant.util;

import java.sql.*;

public class ReportsSchemaCreator {
    
    public static void main(String[] args) {
        System.out.println("🗄️ Creating Reports Database Schema...");
        System.out.println();
        
        try {
            Class.forName("org.sqlite.JDBC");
            System.out.println("✅ SQLite driver loaded");
        } catch (Exception e) {
            System.err.println("❌ Driver error: " + e.getMessage());
            return;
        }
        
        String url = "*************************";
        
        try (Connection conn = DriverManager.getConnection(url)) {
            System.out.println("✅ Database connected");
            
            // Create daily_reports table
            String createDailyReports = "CREATE TABLE IF NOT EXISTS daily_reports (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "report_date DATE NOT NULL UNIQUE," +
                "total_orders INTEGER DEFAULT 0," +
                "total_revenue REAL DEFAULT 0.0," +
                "swiggy_orders INTEGER DEFAULT 0," +
                "zomato_orders INTEGER DEFAULT 0," +
                "swiggy_revenue REAL DEFAULT 0.0," +
                "zomato_revenue REAL DEFAULT 0.0," +
                "avg_order_value REAL DEFAULT 0.0," +
                "peak_hour TEXT," +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ")";
            
            // Create weekly_reports table
            String createWeeklyReports = "CREATE TABLE IF NOT EXISTS weekly_reports (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "week_start_date DATE NOT NULL," +
                "week_end_date DATE NOT NULL," +
                "total_orders INTEGER DEFAULT 0," +
                "total_revenue REAL DEFAULT 0.0," +
                "swiggy_orders INTEGER DEFAULT 0," +
                "zomato_orders INTEGER DEFAULT 0," +
                "swiggy_revenue REAL DEFAULT 0.0," +
                "zomato_revenue REAL DEFAULT 0.0," +
                "avg_daily_orders REAL DEFAULT 0.0," +
                "avg_daily_revenue REAL DEFAULT 0.0," +
                "best_day TEXT," +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "UNIQUE(week_start_date, week_end_date)" +
                ")";
            
            // Create monthly_reports table
            String createMonthlyReports = "CREATE TABLE IF NOT EXISTS monthly_reports (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "report_month INTEGER NOT NULL," +
                "report_year INTEGER NOT NULL," +
                "total_orders INTEGER DEFAULT 0," +
                "total_revenue REAL DEFAULT 0.0," +
                "swiggy_orders INTEGER DEFAULT 0," +
                "zomato_orders INTEGER DEFAULT 0," +
                "swiggy_revenue REAL DEFAULT 0.0," +
                "zomato_revenue REAL DEFAULT 0.0," +
                "avg_daily_orders REAL DEFAULT 0.0," +
                "avg_daily_revenue REAL DEFAULT 0.0," +
                "growth_percentage REAL DEFAULT 0.0," +
                "best_week TEXT," +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "UNIQUE(report_month, report_year)" +
                ")";
            
            // Create analytics_metrics table
            String createAnalyticsMetrics = "CREATE TABLE IF NOT EXISTS analytics_metrics (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "metric_date DATE NOT NULL," +
                "metric_type TEXT NOT NULL," +
                "metric_value REAL NOT NULL," +
                "platform TEXT," +
                "additional_data TEXT," +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ")";

            // Create order_analytics table for detailed analytics
            String createOrderAnalytics = "CREATE TABLE IF NOT EXISTS order_analytics (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "order_id TEXT NOT NULL," +
                "platform TEXT NOT NULL," +
                "order_date DATE NOT NULL," +
                "order_hour INTEGER NOT NULL," +
                "order_value REAL NOT NULL," +
                "preparation_time INTEGER," +
                "customer_rating INTEGER," +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "FOREIGN KEY (order_id) REFERENCES online_orders(order_id)" +
                ")";
            
            // Execute table creation
            try (Statement stmt = conn.createStatement()) {
                stmt.execute(createDailyReports);
                System.out.println("✅ Daily reports table created");
                
                stmt.execute(createWeeklyReports);
                System.out.println("✅ Weekly reports table created");
                
                stmt.execute(createMonthlyReports);
                System.out.println("✅ Monthly reports table created");
                
                stmt.execute(createAnalyticsMetrics);
                System.out.println("✅ Analytics metrics table created");
                
                stmt.execute(createOrderAnalytics);
                System.out.println("✅ Order analytics table created");
            }
            
            // Create indexes for better performance
            String[] indexes = {
                "CREATE INDEX IF NOT EXISTS idx_daily_reports_date ON daily_reports(report_date)",
                "CREATE INDEX IF NOT EXISTS idx_weekly_reports_dates ON weekly_reports(week_start_date, week_end_date)",
                "CREATE INDEX IF NOT EXISTS idx_monthly_reports_period ON monthly_reports(report_year, report_month)",
                "CREATE INDEX IF NOT EXISTS idx_analytics_date_type ON analytics_metrics(metric_date, metric_type)",
                "CREATE INDEX IF NOT EXISTS idx_order_analytics_date ON order_analytics(order_date)",
                "CREATE INDEX IF NOT EXISTS idx_order_analytics_platform ON order_analytics(platform)"
            };
            
            try (Statement stmt = conn.createStatement()) {
                for (String index : indexes) {
                    stmt.execute(index);
                }
                System.out.println("✅ Database indexes created");
            }
            
            System.out.println();
            System.out.println("🎉 Reports database schema created successfully!");
            System.out.println();
            System.out.println("📊 CREATED TABLES:");
            System.out.println("✅ daily_reports - Daily sales and order summaries");
            System.out.println("✅ weekly_reports - Weekly performance analytics");
            System.out.println("✅ monthly_reports - Monthly business insights");
            System.out.println("✅ analytics_metrics - Detailed metrics tracking");
            System.out.println("✅ order_analytics - Individual order analytics");
            System.out.println();
            System.out.println("🔍 ANALYTICS FEATURES:");
            System.out.println("📈 Daily revenue and order tracking");
            System.out.println("📊 Platform-wise performance (Swiggy vs Zomato)");
            System.out.println("⏰ Peak hour analysis");
            System.out.println("📅 Weekly and monthly trends");
            System.out.println("💹 Growth percentage calculations");
            System.out.println("🏆 Best performing periods identification");
            
        } catch (Exception e) {
            System.err.println("❌ Error creating reports schema: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
