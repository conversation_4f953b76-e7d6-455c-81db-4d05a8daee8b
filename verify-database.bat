@echo off
echo 🔍 VERIFYING DATABASE CONTENTS...
echo.

echo STEP 1: Check if database file exists...
if exist restaurant.db (
    echo ✅ Database file exists: restaurant.db
    dir restaurant.db
) else (
    echo ❌ Database file does not exist
    echo Creating database now...
    goto CREATE_DB
)

echo.
echo STEP 2: Creating database verifier...

echo package com.restaurant.util; > src\main\java\com\restaurant\util\DatabaseVerifier.java
echo. >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo import java.sql.*; >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo. >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo public class DatabaseVerifier { >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo     public static void main(String[] args) { >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo         System.out.println("🔍 Verifying database contents..."); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo         System.out.println(); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo. >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo         String url = "*************************"; >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo. >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo         try (Connection conn = DriverManager.getConnection(url)) { >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo             System.out.println("✅ Database connection successful"); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo. >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo             // Check if users table exists >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo             DatabaseMetaData meta = conn.getMetaData(); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo             ResultSet tables = meta.getTables(null, null, "users", null); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo             if (tables.next()) { >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                 System.out.println("✅ Users table exists"); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo             } else { >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                 System.out.println("❌ Users table does not exist"); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                 return; >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo             } >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo             tables.close(); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo. >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo             // List all users >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo             System.out.println(); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo             System.out.println("📋 ALL USERS IN DATABASE:"); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo             String selectUsers = "SELECT id, username, role, password FROM users"; >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo             try (Statement stmt = conn.createStatement(); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                  ResultSet rs = stmt.executeQuery(selectUsers)) { >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                 System.out.println("ID | Username | Role     | Password Hash"); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                 System.out.println("---|----------|----------|---------------"); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                 boolean hasUsers = false; >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                 while (rs.next()) { >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                     hasUsers = true; >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                     String passwordHash = rs.getString("password"); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                     String shortHash = passwordHash.length() ^> 20 ? passwordHash.substring(0, 20) + "..." : passwordHash; >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                     System.out.printf("%2d | %-8s | %-8s | %s%n", >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                         rs.getInt("id"), >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                         rs.getString("username"), >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                         rs.getString("role"), >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                         shortHash); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                 } >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo. >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                 if (!hasUsers) { >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                     System.out.println("❌ NO USERS FOUND IN DATABASE!"); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                     System.out.println("   This is why authentication is failing."); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                 } else { >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                     System.out.println(); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                     System.out.println("✅ Users found in database"); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo                 } >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo             } >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo. >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo             System.err.println("❌ Error: " + e.getMessage()); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo             e.printStackTrace(); >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo         } >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo     } >> src\main\java\com\restaurant\util\DatabaseVerifier.java
echo } >> src\main\java\com\restaurant\util\DatabaseVerifier.java

echo ✅ DatabaseVerifier created
echo.

echo STEP 3: Compiling verifier...
mvn compile -q

echo STEP 4: Running verification...
java -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar" com.restaurant.util.DatabaseVerifier

echo.
echo 📋 VERIFICATION COMPLETE!
echo.

goto END

:CREATE_DB
echo.
echo Creating database with admin user...
.\java-database-fix.bat
goto END

:END
echo.
echo 🔍 DATABASE VERIFICATION RESULTS:
echo.
echo If you see "NO USERS FOUND", that's why login is failing.
echo If you see users listed, then the issue might be in the authentication code.
echo.
echo NEXT STEPS:
echo 1. If no users found: Run .\java-database-fix.bat
echo 2. If users exist but login fails: Check authentication code
echo 3. Try exact credentials: admin / admin123 / ADMIN
echo.

pause
