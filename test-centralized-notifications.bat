@echo off
echo 🔔 TESTING CENTRALIZED NOTIFICATION SYSTEM 🔔
echo.

echo 🎯 CENTRALIZED SOUND MANAGEMENT IMPLEMENTED!
echo.

echo ✅ WHAT WAS CHANGED:
echo.
echo 🔇 FINISHING ORDERS - SOUNDS REMOVED:
echo    - FinishListControllerSilent.java created
echo    - All audio notifications disabled in finishing orders
echo    - Only visual notifications remain
echo    - Status updates work silently
echo.
echo 🔔 NOTIFICATIONS - ALL SOUNDS CENTRALIZED:
echo    - CentralizedNotificationManager.java created
echo    - ALL audio notifications managed in one place
echo    - Platform-specific sounds (Swiggy/Zomato MP3)
echo    - System beep patterns for order status changes
echo    - Continuous ringing for new orders until accepted
echo.

echo 📊 CREATING CENTRALIZED NOTIFICATION TEST...

echo package com.restaurant.test; > src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo import com.restaurant.util.CentralizedNotificationManager; >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo import com.restaurant.model.OnlineOrder; >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo import java.time.LocalDateTime; >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo import java.util.Scanner; >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo public class CentralizedNotificationTest { >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo     public static void main(String[] args) { >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo         System.out.println("🔔 TESTING CENTRALIZED NOTIFICATION SYSTEM"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo         System.out.println(); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo         CentralizedNotificationManager notificationManager = >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             CentralizedNotificationManager.getInstance(); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo         Scanner scanner = new Scanner(System.in); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo         try { >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("🟠 Testing Swiggy Order Notification..."); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             OnlineOrder swiggyOrder = createTestOrder("SWIGGY001", OnlineOrder.Platform.SWIGGY); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             notificationManager.notifyNewSwiggyOrder(swiggyOrder); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("✅ Swiggy notification sent (with continuous ringing)"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println(); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             Thread.sleep(3000); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("🔴 Testing Zomato Order Notification..."); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             OnlineOrder zomatoOrder = createTestOrder("ZOMATO001", OnlineOrder.Platform.ZOMATO); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             notificationManager.notifyNewZomatoOrder(zomatoOrder); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("✅ Zomato notification sent (with continuous ringing)"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println(); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             Thread.sleep(3000); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("✅ Testing Order Acceptance (stops ringing)..."); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             notificationManager.notifyOrderAccepted("SWIGGY001", "Swiggy"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("✅ Swiggy order accepted - ringing stopped"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println(); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             Thread.sleep(2000); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("🍽️ Testing Order Ready Notification..."); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             notificationManager.notifyOrderReady("SWIGGY001", "John Doe"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("✅ Order ready notification sent"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println(); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             Thread.sleep(2000); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("💰 Testing Order Pricing Notification..."); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             notificationManager.notifyOrderPricing("SWIGGY001"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("✅ Order pricing notification sent"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println(); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             Thread.sleep(2000); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("📦 Testing Order Completion Notification..."); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             notificationManager.notifyOrderCompleted("SWIGGY001"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("✅ Order completion notification sent"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println(); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             Thread.sleep(2000); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("🔔 Testing System Notifications..."); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             notificationManager.notifySuccess("Test Success", "This is a success message"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             Thread.sleep(1000); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             notificationManager.notifyWarning("Test Warning", "This is a warning message"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             Thread.sleep(1000); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             notificationManager.notifyError("Test Error", "This is an error message"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             Thread.sleep(1000); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             notificationManager.notifyUrgent("Test Urgent", "This is an urgent message"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("✅ System notifications sent"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println(); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("🎉 ALL CENTRALIZED NOTIFICATION TESTS COMPLETED!"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println(); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("📋 SOUND MANAGEMENT SUMMARY:"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("🔔 New Orders: MP3 sounds + continuous ringing"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("✅ Order Accepted: 2 quick beeps + ringing stops"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("🍽️ Order Ready: 3 beeps"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("💰 Order Pricing: 1 long beep"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("📦 Order Completed: 4 ascending beeps"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("🔔 System Sounds: Various beep patterns"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println(); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("🔇 Finishing Orders: SILENT (no sounds)"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.out.println("🔔 All Sounds: Managed by CentralizedNotificationManager"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             // Cleanup >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             notificationManager.cleanup(); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             System.err.println("Error during testing: " + e.getMessage()); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo             e.printStackTrace(); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo         } >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo     } >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo. >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo     private static OnlineOrder createTestOrder(String orderId, OnlineOrder.Platform platform) { >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo         OnlineOrder order = new OnlineOrder(); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo         order.setOrderId(orderId); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo         order.setPlatform(platform); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo         order.setCustomerName("Test Customer"); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo         order.setTotalAmount(250.0); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo         order.setOrderTime(LocalDateTime.now()); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo         order.setStatus(OnlineOrder.OrderStatus.NEW); >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo         return order; >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo     } >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java
echo } >> src\main\java\com\restaurant\test\CentralizedNotificationTest.java

echo ✅ Centralized notification test created
echo.

echo 🔧 COMPILING CENTRALIZED COMPONENTS...

echo Compiling CentralizedNotificationManager...
javac -d target/classes -cp "target/classes;lib/*" src/main/java/com/restaurant/util/CentralizedNotificationManager.java

if %ERRORLEVEL% neq 0 (
    echo ❌ Failed to compile CentralizedNotificationManager
    pause
    exit /b 1
)

echo Compiling FinishListControllerSilent...
javac -d target/classes -cp "target/classes;lib/*" src/main/java/com/restaurant/controller/FinishListControllerSilent.java

if %ERRORLEVEL% neq 0 (
    echo ❌ Failed to compile FinishListControllerSilent
    pause
    exit /b 1
)

echo Compiling CentralizedNotificationTest...
javac -d target/classes -cp "target/classes;lib/*" src/main/java/com/restaurant/test/CentralizedNotificationTest.java

if %ERRORLEVEL% neq 0 (
    echo ❌ Failed to compile CentralizedNotificationTest
    pause
    exit /b 1
)

echo ✅ All components compiled successfully!
echo.

echo 🧪 RUNNING CENTRALIZED NOTIFICATION TEST...
java -cp "target/classes;lib/*" com.restaurant.test.CentralizedNotificationTest

echo.
echo 🎉 CENTRALIZED NOTIFICATION SYSTEM COMPLETE!
echo.

echo 📋 IMPLEMENTATION SUMMARY:
echo.
echo 🔇 FINISHING ORDERS (SILENT):
echo ✅ FinishListControllerSilent.java - No audio notifications
echo ✅ Visual notifications only for status updates
echo ✅ All sounds removed from order management
echo ✅ Clean separation of concerns
echo.
echo 🔔 CENTRALIZED NOTIFICATIONS:
echo ✅ CentralizedNotificationManager.java - ALL sounds here
echo ✅ Platform-specific MP3 sounds (Swiggy/Zomato)
echo ✅ System beep patterns for order status
echo ✅ Continuous ringing until orders accepted
echo ✅ Visual notifications with sound coordination
echo.
echo 🎯 SOUND MANAGEMENT:
echo ✅ New Swiggy Order: MP3 + continuous ringing
echo ✅ New Zomato Order: MP3 + continuous ringing
echo ✅ Order Accepted: 2 beeps + stop ringing
echo ✅ Order Ready: 3 beeps
echo ✅ Order Pricing: 1 long beep
echo ✅ Order Completed: 4 ascending beeps
echo ✅ System Notifications: Various beep patterns
echo.
echo 🔧 INTEGRATION:
echo ✅ Replace FinishListController with FinishListControllerSilent
echo ✅ Use CentralizedNotificationManager for all sounds
echo ✅ Maintain existing functionality with better organization
echo ✅ Easy to enable/disable audio globally
echo.

echo 🎵 ALL SOUNDS ARE NOW CENTRALIZED IN THE NOTIFICATION SYSTEM! 🎵
echo.

pause
