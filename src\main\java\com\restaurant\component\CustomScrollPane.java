package com.restaurant.component;

import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.geometry.Bounds;
import javafx.scene.Node;
import javafx.scene.layout.Pane;
import javafx.scene.layout.Region;
import javafx.scene.shape.Rectangle;

/**
 * Custom ScrollPane that uses our CustomScrollBar for full control
 * Provides smooth scrolling with exact specifications
 */
public class CustomScrollPane extends Region {
    
    // Properties
    private final ObjectProperty<Node> content = new SimpleObjectProperty<>();
    
    // Components
    private Pane viewport;
    private CustomScrollBar vScrollBar;
    private Node contentNode;
    
    // Clipping
    private Rectangle clip;
    
    public CustomScrollPane() {
        initializeComponents();
        setupBindings();
        getStyleClass().add("custom-scroll-pane");
    }
    
    public CustomScrollPane(Node content) {
        this();
        setContent(content);
    }
    
    private void initializeComponents() {
        // Create viewport for content
        viewport = new Pane();
        viewport.getStyleClass().add("viewport");
        
        // Create custom scroll bar
        vScrollBar = new CustomScrollBar();
        
        // Create clipping rectangle
        clip = new Rectangle();
        viewport.setClip(clip);
        
        getChildren().addAll(viewport, vScrollBar);
    }
    
    private void setupBindings() {
        // Content property binding
        content.addListener((obs, oldContent, newContent) -> {
            if (oldContent != null) {
                viewport.getChildren().remove(oldContent);
            }
            if (newContent != null) {
                viewport.getChildren().add(newContent);
                contentNode = newContent;
                updateScrollBarProperties();
            }
        });
        
        // Scroll bar value binding
        vScrollBar.valueProperty().addListener((obs, oldVal, newVal) -> {
            updateContentPosition();
        });
        
        // Size change listeners
        widthProperty().addListener((obs, oldVal, newVal) -> updateLayout());
        heightProperty().addListener((obs, oldVal, newVal) -> updateLayout());
        
        // Content bounds change listener
        viewport.boundsInLocalProperty().addListener((obs, oldVal, newVal) -> {
            updateScrollBarProperties();
        });

        // Mouse wheel scrolling support
        setOnScroll(event -> {
            double deltaY = event.getDeltaY();
            if (deltaY > 0) {
                scrollUp(30); // Scroll up 30 pixels
            } else {
                scrollDown(30); // Scroll down 30 pixels
            }
            event.consume();
        });
    }
    
    @Override
    protected void layoutChildren() {
        super.layoutChildren();
        updateLayout();
    }
    
    private void updateLayout() {
        double width = getWidth();
        double height = getHeight();
        
        if (width <= 0 || height <= 0) return;
        
        double scrollBarWidth = vScrollBar.getPrefWidth();
        double viewportWidth = width - scrollBarWidth;
        
        // Position viewport
        viewport.resizeRelocate(0, 0, viewportWidth, height);
        
        // Position scroll bar
        vScrollBar.resizeRelocate(viewportWidth, 0, scrollBarWidth, height);
        
        // Update clipping
        clip.setWidth(viewportWidth);
        clip.setHeight(height);
        
        // Update scroll bar properties
        updateScrollBarProperties();
        updateContentPosition();
    }
    
    private void updateScrollBarProperties() {
        if (contentNode == null) return;
        
        // Get content bounds
        Bounds contentBounds = contentNode.getBoundsInLocal();
        double contentHeight = contentBounds.getHeight();
        double viewportHeight = viewport.getHeight();
        
        if (contentHeight <= viewportHeight) {
            // Content fits in viewport - no scrolling needed
            vScrollBar.setMin(0);
            vScrollBar.setMax(0);
            vScrollBar.setVisibleAmount(viewportHeight);
            vScrollBar.setValue(0);
            vScrollBar.setVisible(false);
        } else {
            // Content larger than viewport - scrolling needed
            double maxScroll = contentHeight - viewportHeight;
            vScrollBar.setMin(0);
            vScrollBar.setMax(maxScroll);
            vScrollBar.setVisibleAmount(viewportHeight);
            vScrollBar.setVisible(true);
            
            // Ensure current value is within new range
            double currentValue = vScrollBar.getValue();
            if (currentValue > maxScroll) {
                vScrollBar.setValue(maxScroll);
            }
        }
    }
    
    private void updateContentPosition() {
        if (contentNode == null) return;
        
        double scrollValue = vScrollBar.getValue();
        contentNode.setLayoutY(-scrollValue);
    }
    
    // Public API
    public void setContent(Node content) {
        this.content.set(content);
    }
    
    public Node getContent() {
        return content.get();
    }
    
    public ObjectProperty<Node> contentProperty() {
        return content;
    }
    
    public void scrollTo(double value) {
        vScrollBar.setValue(value);
    }
    
    public void scrollToTop() {
        vScrollBar.setValue(vScrollBar.getMin());
    }
    
    public void scrollToBottom() {
        vScrollBar.setValue(vScrollBar.getMax());
    }
    
    public double getScrollValue() {
        return vScrollBar.getValue();
    }
    
    public double getMaxScrollValue() {
        return vScrollBar.getMax();
    }
    
    // Scroll by specific amounts
    public void scrollUp(double amount) {
        double newValue = vScrollBar.getValue() - amount;
        vScrollBar.setValue(Math.max(vScrollBar.getMin(), newValue));
    }
    
    public void scrollDown(double amount) {
        double newValue = vScrollBar.getValue() + amount;
        vScrollBar.setValue(Math.min(vScrollBar.getMax(), newValue));
    }
    
    public void pageUp() {
        scrollUp(vScrollBar.getVisibleAmount() * 0.9); // 90% of visible area
    }
    
    public void pageDown() {
        scrollDown(vScrollBar.getVisibleAmount() * 0.9); // 90% of visible area
    }
    
    // Get the custom scroll bar for additional customization
    public CustomScrollBar getVScrollBar() {
        return vScrollBar;
    }
    
    // Utility methods for smooth scrolling
    public void smoothScrollTo(double targetValue) {
        // TODO: Implement smooth scrolling animation if needed
        scrollTo(targetValue);
    }
    
    public void smoothScrollToTop() {
        smoothScrollTo(vScrollBar.getMin());
    }
    
    public void smoothScrollToBottom() {
        smoothScrollTo(vScrollBar.getMax());
    }
}
