@echo off
echo Testing Database Connection Fix...
echo.

echo This script tests the direct database connection approach
echo that bypasses the DatabaseManager hanging issue.
echo.

echo Compiling project...
call mvn clean compile -q

if %ERRORLEVEL% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo.
echo Testing direct database connection (the fix for OrderDAO hanging)...
echo.

java -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\********\sqlite-jdbc-********.jar" ^
     com.restaurant.model.TestDatabaseConnection

echo.
echo Database connection test completed.
echo.
echo If you see "✅ Direct database connection successful" above,
echo then the OrderManagement hanging issue should be fixed.
echo.
pause
