package com.restaurant.test; 
 
import com.restaurant.service.NotificationService; 
import com.restaurant.util.CentralizedNotificationManager; 
import com.restaurant.model.Notification; 
import java.util.Scanner; 
 
public class NotificationOrderAcceptanceTest { 
    public static void main(String[] args) { 
        System.out.println("🔔 TESTING NOTIFICATION-BASED ORDER ACCEPTANCE"); 
        System.out.println(); 
 
        NotificationService notificationService = NotificationService.getInstance(); 
        CentralizedNotificationManager soundManager = 
            CentralizedNotificationManager.getInstance(); 
 
        try { 
            System.out.println("🟠 Testing Swiggy Order Notification in Notifications Panel..."); 
            notificationService.notifyOnlineOrder("Swiggy", "SW12345", 450.00); 
            System.out.println("✅ Swiggy order notification sent to notifications panel"); 
            System.out.println("   - Accept/Reject buttons available"); 
            System.out.println("   - Continuous ringing until action taken"); 
            System.out.println(); 
 
            Thread.sleep(3000); 
 
            System.out.println("🔴 Testing Zomato Order Notification in Notifications Panel..."); 
            notificationService.notifyOnlineOrder("Zomato", "ZM67890", 320.50); 
            System.out.println("✅ Zomato order notification sent to notifications panel"); 
            System.out.println("   - Accept/Reject buttons available"); 
            System.out.println("   - Continuous ringing until action taken"); 
            System.out.println(); 
 
            Thread.sleep(3000); 
 
            System.out.println("💻 Testing Online Order Notification..."); 
            notificationService.notifyOnlineOrder("Online", "ON98765", 275.75); 
            System.out.println("✅ Online order notification sent to notifications panel"); 
            System.out.println("   - Accept/Reject buttons available"); 
            System.out.println("   - Standard notification sound"); 
            System.out.println(); 
 
            Thread.sleep(2000); 
 
            System.out.println("📋 Testing Notification List..."); 
            System.out.println("Total notifications: " + notificationService.getNotifications().size()); 
            System.out.println("Unread notifications: " + notificationService.getUnreadCount()); 
            System.out.println(); 
 
            System.out.println("🎉 ALL NOTIFICATION ORDER ACCEPTANCE TESTS COMPLETED!"); 
            System.out.println(); 
            System.out.println("📋 NOTIFICATION PANEL FEATURES:"); 
            System.out.println("✅ Accept Order Button - Stops ringing, moves to kitchen"); 
            System.out.println("❌ Reject Order Button - Stops ringing, marks as rejected"); 
            System.out.println("👁️ View Details Button - Shows order information"); 
            System.out.println("🔔 Platform-Specific Sounds - Swiggy vs Zomato identification"); 
            System.out.println("🔄 Continuous Ringing - Until user takes action"); 
            System.out.println("📊 Real-time Status - Pending order counts"); 
            System.out.println(); 
            System.out.println("🔇 FINISHING ORDERS REMAIN SILENT:"); 
            System.out.println("✅ FinishListControllerSilent - No audio notifications"); 
            System.out.println("✅ Visual status updates only"); 
            System.out.println("✅ Clean separation from order acceptance"); 
 
        } catch (Exception e) { 
            System.err.println("Error during testing: " + e.getMessage()); 
            e.printStackTrace(); 
        } 
    } 
} 
