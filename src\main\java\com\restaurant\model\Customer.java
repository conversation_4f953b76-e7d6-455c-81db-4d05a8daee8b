package com.restaurant.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Customer model for CRM system
 */
public class Customer {
    private int id;
    private String name;
    private String phone;
    private String email;
    private String address;
    private LocalDateTime firstVisit;
    private LocalDateTime lastVisit;
    private int totalOrders;
    private double totalSpent;
    private double averageSpend;
    private int loyaltyPoints;
    private String membershipLevel; // "New", "Regular", "VIP", "Premium"
    private String segment; // "New Customer", "Regular", "High Spender", "Lapsed"
    private List<String> preferredDishes;
    private double averageRating;
    private String notes;
    private boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime lastUpdated;

    // Constructors
    public Customer() {
        this.createdAt = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
        this.firstVisit = LocalDateTime.now();
        this.preferredDishes = new ArrayList<>();
        this.isActive = true;
        this.membershipLevel = "New";
        this.segment = "New Customer";
        this.loyaltyPoints = 0;
        this.totalOrders = 0;
        this.totalSpent = 0.0;
        this.averageSpend = 0.0;
        this.averageRating = 0.0;
    }

    public Customer(String name, String phone) {
        this();
        this.name = name;
        this.phone = phone;
    }

    public Customer(String name, String phone, String email) {
        this(name, phone);
        this.email = email;
    }

    public Customer(int id, String name, String phone, String email) {
        this(name, phone, email);
        this.id = id;
    }

    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { 
        this.name = name;
        this.lastUpdated = LocalDateTime.now();
    }

    public String getPhone() { return phone; }
    public void setPhone(String phone) { 
        this.phone = phone;
        this.lastUpdated = LocalDateTime.now();
    }

    public String getEmail() { return email; }
    public void setEmail(String email) { 
        this.email = email;
        this.lastUpdated = LocalDateTime.now();
    }

    public String getAddress() { return address; }
    public void setAddress(String address) { 
        this.address = address;
        this.lastUpdated = LocalDateTime.now();
    }

    public LocalDateTime getFirstVisit() { return firstVisit; }
    public void setFirstVisit(LocalDateTime firstVisit) { this.firstVisit = firstVisit; }

    public LocalDateTime getLastVisit() { return lastVisit; }
    public void setLastVisit(LocalDateTime lastVisit) { 
        this.lastVisit = lastVisit;
        this.lastUpdated = LocalDateTime.now();
    }

    public int getTotalOrders() { return totalOrders; }
    public void setTotalOrders(int totalOrders) { 
        this.totalOrders = totalOrders;
        updateAverageSpend();
        updateSegment();
        this.lastUpdated = LocalDateTime.now();
    }

    public double getTotalSpent() { return totalSpent; }
    public void setTotalSpent(double totalSpent) { 
        this.totalSpent = totalSpent;
        updateAverageSpend();
        updateMembershipLevel();
        updateSegment();
        this.lastUpdated = LocalDateTime.now();
    }

    public double getAverageSpend() { return averageSpend; }
    public void setAverageSpend(double averageSpend) { this.averageSpend = averageSpend; }

    public int getLoyaltyPoints() { return loyaltyPoints; }
    public void setLoyaltyPoints(int loyaltyPoints) { 
        this.loyaltyPoints = loyaltyPoints;
        this.lastUpdated = LocalDateTime.now();
    }

    public String getMembershipLevel() { return membershipLevel; }
    public void setMembershipLevel(String membershipLevel) { 
        this.membershipLevel = membershipLevel;
        this.lastUpdated = LocalDateTime.now();
    }

    public String getSegment() { return segment; }
    public void setSegment(String segment) { 
        this.segment = segment;
        this.lastUpdated = LocalDateTime.now();
    }

    public List<String> getPreferredDishes() { return preferredDishes; }
    public void setPreferredDishes(List<String> preferredDishes) { 
        this.preferredDishes = preferredDishes;
        this.lastUpdated = LocalDateTime.now();
    }

    public double getAverageRating() { return averageRating; }
    public void setAverageRating(double averageRating) { 
        this.averageRating = averageRating;
        this.lastUpdated = LocalDateTime.now();
    }

    public String getNotes() { return notes; }
    public void setNotes(String notes) { 
        this.notes = notes;
        this.lastUpdated = LocalDateTime.now();
    }

    public boolean isActive() { return isActive; }
    public void setActive(boolean active) { 
        isActive = active;
        this.lastUpdated = LocalDateTime.now();
    }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getLastUpdated() { return lastUpdated; }
    public void setLastUpdated(LocalDateTime lastUpdated) { this.lastUpdated = lastUpdated; }

    // Business Logic Methods
    public void addOrder(double orderAmount) {
        this.totalOrders++;
        this.totalSpent += orderAmount;
        this.lastVisit = LocalDateTime.now();
        
        // Add loyalty points (1 point per ₹10 spent)
        int pointsEarned = (int) (orderAmount / 10);
        this.loyaltyPoints += pointsEarned;
        
        updateAverageSpend();
        updateMembershipLevel();
        updateSegment();
        this.lastUpdated = LocalDateTime.now();
    }

    public void addPreferredDish(String dish) {
        if (!preferredDishes.contains(dish)) {
            preferredDishes.add(dish);
            this.lastUpdated = LocalDateTime.now();
        }
    }

    public void redeemPoints(int points) {
        if (this.loyaltyPoints >= points) {
            this.loyaltyPoints -= points;
            this.lastUpdated = LocalDateTime.now();
        }
    }

    private void updateAverageSpend() {
        if (totalOrders > 0) {
            this.averageSpend = totalSpent / totalOrders;
        }
    }

    private void updateMembershipLevel() {
        if (totalSpent >= 50000) {
            this.membershipLevel = "Premium";
        } else if (totalSpent >= 20000) {
            this.membershipLevel = "VIP";
        } else if (totalOrders >= 5) {
            this.membershipLevel = "Regular";
        } else {
            this.membershipLevel = "New";
        }
    }

    private void updateSegment() {
        LocalDateTime now = LocalDateTime.now();
        long daysSinceLastVisit = java.time.Duration.between(lastVisit, now).toDays();
        
        if (totalOrders == 0) {
            this.segment = "New Customer";
        } else if (daysSinceLastVisit > 30) {
            this.segment = "Lapsed Customer";
        } else if (averageSpend > 1000) {
            this.segment = "High Spender";
        } else if (totalOrders >= 5) {
            this.segment = "Regular";
        } else {
            this.segment = "New Customer";
        }
    }

    public String getFormattedTotalSpent() {
        return String.format("₹%.2f", totalSpent);
    }

    public String getFormattedAverageSpend() {
        return String.format("₹%.2f", averageSpend);
    }

    public String getPreferredDishesString() {
        return String.join(", ", preferredDishes);
    }

    public String getDaysSinceLastVisit() {
        if (lastVisit == null) return "Never";
        
        LocalDateTime now = LocalDateTime.now();
        long days = java.time.Duration.between(lastVisit, now).toDays();
        
        if (days == 0) return "Today";
        if (days == 1) return "Yesterday";
        return days + " days ago";
    }

    @Override
    public String toString() {
        return name + " (" + phone + ")";
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Customer customer = (Customer) obj;
        return id == customer.id;
    }

    @Override
    public int hashCode() {
        return Integer.hashCode(id);
    }
}
