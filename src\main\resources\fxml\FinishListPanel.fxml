<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.FinishListController" stylesheets="@../css/application.css,@../css/finish-list.css">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="header-section">
         <children>
            <Label styleClass="page-title" text="🍽️ Online Orders - Finish List" />
            <Region HBox.hgrow="ALWAYS" />
            <!-- Status Filter -->
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <children>
                  <Label text="Filter by Status:" styleClass="filter-label" />
                  <ComboBox fx:id="statusFilterCombo" onAction="#filterByStatus" prefWidth="120.0" styleClass="status-filter" />
               </children>
            </HBox>
            <!-- Platform Filter -->
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <children>
                  <Label text="Platform:" styleClass="filter-label" />
                  <ComboBox fx:id="platformFilterCombo" onAction="#filterByPlatform" prefWidth="100.0" styleClass="platform-filter" />
               </children>
            </HBox>
            <!-- Refresh Button -->
            <Button fx:id="refreshButton" onAction="#refreshOrders" styleClass="refresh-button" text="🔄 Refresh" />
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>
      
      <!-- Statistics Section -->
      <HBox alignment="CENTER" spacing="30.0" styleClass="stats-section">
         <children>
            <VBox alignment="CENTER" styleClass="stat-card preparing">
               <children>
                  <Label fx:id="preparingCountLabel" styleClass="stat-number" text="0" />
                  <Label styleClass="stat-label" text="Preparing" />
               </children>
            </VBox>
            <VBox alignment="CENTER" styleClass="stat-card ready">
               <children>
                  <Label fx:id="readyCountLabel" styleClass="stat-number" text="0" />
                  <Label styleClass="stat-label" text="Ready" />
               </children>
            </VBox>
            <VBox alignment="CENTER" styleClass="stat-card pricing">
               <children>
                  <Label fx:id="pricingCountLabel" styleClass="stat-number" text="0" />
                  <Label styleClass="stat-label" text="Pricing" />
               </children>
            </VBox>
            <VBox alignment="CENTER" styleClass="stat-card total">
               <children>
                  <Label fx:id="totalOrdersLabel" styleClass="stat-number" text="0" />
                  <Label styleClass="stat-label" text="Total Orders" />
               </children>
            </VBox>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="10.0" />
         </padding>
      </HBox>
      
      <!-- Orders List Section -->
      <ScrollPane fitToWidth="true" styleClass="orders-scroll-pane" VBox.vgrow="ALWAYS">
         <content>
            <VBox fx:id="ordersContainer" spacing="10.0" styleClass="orders-container">
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
               </padding>
            </VBox>
         </content>
      </ScrollPane>
      
      <!-- Action Buttons Section -->
      <HBox alignment="CENTER" spacing="15.0" styleClass="action-section">
         <children>
            <Button fx:id="addTestOrderButton" onAction="#addTestOrder" styleClass="test-button" text="➕ Add Test Order" />
            <Button fx:id="markAllReadyButton" onAction="#markAllReady" styleClass="bulk-action-button" text="✅ Mark All Ready" />
            <Button fx:id="clearCompletedButton" onAction="#clearCompleted" styleClass="clear-button" text="🗑️ Clear Completed" />
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>
   </children>
</VBox>
