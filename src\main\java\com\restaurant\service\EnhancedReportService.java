package com.restaurant.service;

import com.restaurant.model.*;
import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

public class EnhancedReportService {
    private static final String DB_URL = "*************************";
    
    // Enhanced report generation with filters
    public static List<DailyReport> generateFilteredReports(LocalDate startDate, LocalDate endDate, 
                                                           List<String> orderTypes, String reportType) {
        List<DailyReport> reports = new ArrayList<>();
        
        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            String sql = buildFilteredQuery(orderTypes, reportType);
            
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, startDate.toString());
                pstmt.setString(2, endDate.toString());
                
                ResultSet rs = pstmt.executeQuery();
                
                while (rs.next()) {
                    DailyReport report = new DailyReport();
                    report.setReportDate(LocalDate.parse(rs.getString("report_date")));
                    report.setTotalOrders(rs.getInt("total_orders"));
                    report.setTotalRevenue(rs.getDouble("total_revenue"));
                    report.setSwiggyOrders(rs.getInt("swiggy_orders"));
                    report.setZomatoOrders(rs.getInt("zomato_orders"));
                    report.setSwiggyRevenue(rs.getDouble("swiggy_revenue"));
                    report.setZomatoRevenue(rs.getDouble("zomato_revenue"));
                    
                    if (report.getTotalOrders() > 0) {
                        report.setAvgOrderValue(report.getTotalRevenue() / report.getTotalOrders());
                    }
                    
                    reports.add(report);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error generating filtered reports: " + e.getMessage());
        }
        
        return reports;
    }
    
    // Build SQL query based on filters
    private static String buildFilteredQuery(List<String> orderTypes, String reportType) {
        StringBuilder sql = new StringBuilder();
        
        if ("daily".equals(reportType)) {
            sql.append("SELECT DATE(order_time) as report_date, ");
        } else if ("weekly".equals(reportType)) {
            sql.append("SELECT strftime('%Y-W%W', order_time) as report_date, ");
        } else if ("monthly".equals(reportType)) {
            sql.append("SELECT strftime('%Y-%m', order_time) as report_date, ");
        }
        
        sql.append("COUNT(*) as total_orders, ")
           .append("COALESCE(SUM(total_amount), 0) as total_revenue, ")
           .append("COUNT(CASE WHEN platform = 'Swiggy' THEN 1 END) as swiggy_orders, ")
           .append("COUNT(CASE WHEN platform = 'Zomato' THEN 1 END) as zomato_orders, ")
           .append("COALESCE(SUM(CASE WHEN platform = 'Swiggy' THEN total_amount ELSE 0 END), 0) as swiggy_revenue, ")
           .append("COALESCE(SUM(CASE WHEN platform = 'Zomato' THEN total_amount ELSE 0 END), 0) as zomato_revenue ")
           .append("FROM online_orders ")
           .append("WHERE DATE(order_time) BETWEEN ? AND ? ");
        
        // Add order type filter
        if (orderTypes != null && !orderTypes.isEmpty() && !orderTypes.contains("All")) {
            sql.append("AND platform IN (");
            for (int i = 0; i < orderTypes.size(); i++) {
                sql.append("'").append(orderTypes.get(i)).append("'");
                if (i < orderTypes.size() - 1) {
                    sql.append(", ");
                }
            }
            sql.append(") ");
        }
        
        if ("daily".equals(reportType)) {
            sql.append("GROUP BY DATE(order_time) ORDER BY DATE(order_time) DESC");
        } else if ("weekly".equals(reportType)) {
            sql.append("GROUP BY strftime('%Y-W%W', order_time) ORDER BY strftime('%Y-W%W', order_time) DESC");
        } else if ("monthly".equals(reportType)) {
            sql.append("GROUP BY strftime('%Y-%m', order_time) ORDER BY strftime('%Y-%m', order_time) DESC");
        }
        
        return sql.toString();
    }
    
    // Get analytics summary with filters
    public static Map<String, Object> getFilteredAnalyticsSummary(LocalDate startDate, LocalDate endDate, 
                                                                 List<String> orderTypes) {
        Map<String, Object> analytics = new HashMap<>();
        
        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            String sql = "SELECT " +
                "COUNT(*) as total_orders, " +
                "COALESCE(SUM(total_amount), 0) as total_revenue, " +
                "COUNT(CASE WHEN platform = 'Swiggy' THEN 1 END) as swiggy_orders, " +
                "COUNT(CASE WHEN platform = 'Zomato' THEN 1 END) as zomato_orders, " +
                "COALESCE(SUM(CASE WHEN platform = 'Swiggy' THEN total_amount ELSE 0 END), 0) as swiggy_revenue, " +
                "COALESCE(SUM(CASE WHEN platform = 'Zomato' THEN total_amount ELSE 0 END), 0) as zomato_revenue " +
                "FROM online_orders " +
                "WHERE DATE(order_time) BETWEEN ? AND ? ";
            
            // Add order type filter
            if (orderTypes != null && !orderTypes.isEmpty() && !orderTypes.contains("All")) {
                sql += "AND platform IN (";
                for (int i = 0; i < orderTypes.size(); i++) {
                    sql += "'" + orderTypes.get(i) + "'";
                    if (i < orderTypes.size() - 1) {
                        sql += ", ";
                    }
                }
                sql += ") ";
            }
            
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, startDate.toString());
                pstmt.setString(2, endDate.toString());
                ResultSet rs = pstmt.executeQuery();
                
                if (rs.next()) {
                    int totalOrders = rs.getInt("total_orders");
                    double totalRevenue = rs.getDouble("total_revenue");
                    int swiggyOrders = rs.getInt("swiggy_orders");
                    int zomatoOrders = rs.getInt("zomato_orders");
                    double swiggyRevenue = rs.getDouble("swiggy_revenue");
                    double zomatoRevenue = rs.getDouble("zomato_revenue");
                    
                    analytics.put("totalOrders", totalOrders);
                    analytics.put("totalRevenue", totalRevenue);
                    analytics.put("swiggyOrders", swiggyOrders);
                    analytics.put("zomatoOrders", zomatoOrders);
                    analytics.put("swiggyRevenue", swiggyRevenue);
                    analytics.put("zomatoRevenue", zomatoRevenue);
                    
                    // Calculate percentages
                    if (totalOrders > 0) {
                        analytics.put("swiggyPercentage", (swiggyOrders * 100.0) / totalOrders);
                        analytics.put("zomatoPercentage", (zomatoOrders * 100.0) / totalOrders);
                        analytics.put("avgOrderValue", totalRevenue / totalOrders);
                    } else {
                        analytics.put("swiggyPercentage", 0.0);
                        analytics.put("zomatoPercentage", 0.0);
                        analytics.put("avgOrderValue", 0.0);
                    }
                    
                    if (totalRevenue > 0) {
                        analytics.put("swiggyRevenuePercentage", (swiggyRevenue * 100.0) / totalRevenue);
                        analytics.put("zomatoRevenuePercentage", (zomatoRevenue * 100.0) / totalRevenue);
                    } else {
                        analytics.put("swiggyRevenuePercentage", 0.0);
                        analytics.put("zomatoRevenuePercentage", 0.0);
                    }
                }
            }
            
            // Get peak hour for the date range
            String peakHourSql = "SELECT strftime('%H', order_time) as hour, COUNT(*) as order_count " +
                "FROM online_orders " +
                "WHERE DATE(order_time) BETWEEN ? AND ? ";
            
            if (orderTypes != null && !orderTypes.isEmpty() && !orderTypes.contains("All")) {
                peakHourSql += "AND platform IN (";
                for (int i = 0; i < orderTypes.size(); i++) {
                    peakHourSql += "'" + orderTypes.get(i) + "'";
                    if (i < orderTypes.size() - 1) {
                        peakHourSql += ", ";
                    }
                }
                peakHourSql += ") ";
            }
            
            peakHourSql += "GROUP BY strftime('%H', order_time) ORDER BY order_count DESC LIMIT 1";
            
            try (PreparedStatement pstmt = conn.prepareStatement(peakHourSql)) {
                pstmt.setString(1, startDate.toString());
                pstmt.setString(2, endDate.toString());
                ResultSet rs = pstmt.executeQuery();
                
                if (rs.next()) {
                    String hour = rs.getString("hour");
                    int count = rs.getInt("order_count");
                    analytics.put("peakHour", hour + ":00 (" + count + " orders)");
                } else {
                    analytics.put("peakHour", "No data");
                }
            }
            
        } catch (SQLException e) {
            System.err.println("Error getting filtered analytics: " + e.getMessage());
            // Set default values
            analytics.put("totalOrders", 0);
            analytics.put("totalRevenue", 0.0);
            analytics.put("swiggyOrders", 0);
            analytics.put("zomatoOrders", 0);
            analytics.put("swiggyRevenue", 0.0);
            analytics.put("zomatoRevenue", 0.0);
            analytics.put("swiggyPercentage", 0.0);
            analytics.put("zomatoPercentage", 0.0);
            analytics.put("avgOrderValue", 0.0);
            analytics.put("peakHour", "No data");
        }
        
        return analytics;
    }
    
    // Get order type breakdown
    public static Map<String, Integer> getOrderTypeBreakdown(LocalDate startDate, LocalDate endDate) {
        Map<String, Integer> breakdown = new HashMap<>();
        
        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            String sql = "SELECT platform, COUNT(*) as order_count " +
                "FROM online_orders " +
                "WHERE DATE(order_time) BETWEEN ? AND ? " +
                "GROUP BY platform " +
                "ORDER BY order_count DESC";
            
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, startDate.toString());
                pstmt.setString(2, endDate.toString());
                ResultSet rs = pstmt.executeQuery();
                
                while (rs.next()) {
                    breakdown.put(rs.getString("platform"), rs.getInt("order_count"));
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting order type breakdown: " + e.getMessage());
        }
        
        return breakdown;
    }
    
    // Get revenue trend data for charts
    public static List<Map<String, Object>> getRevenueTrendData(LocalDate startDate, LocalDate endDate, 
                                                               List<String> orderTypes, String groupBy) {
        List<Map<String, Object>> trendData = new ArrayList<>();
        
        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            String dateFormat;
            switch (groupBy.toLowerCase()) {
                case "daily":
                    dateFormat = "DATE(order_time)";
                    break;
                case "weekly":
                    dateFormat = "strftime('%Y-W%W', order_time)";
                    break;
                case "monthly":
                    dateFormat = "strftime('%Y-%m', order_time)";
                    break;
                default:
                    dateFormat = "DATE(order_time)";
            }
            
            String sql = "SELECT " + dateFormat + " as period, " +
                "COUNT(*) as orders, " +
                "COALESCE(SUM(total_amount), 0) as revenue " +
                "FROM online_orders " +
                "WHERE DATE(order_time) BETWEEN ? AND ? ";
            
            if (orderTypes != null && !orderTypes.isEmpty() && !orderTypes.contains("All")) {
                sql += "AND platform IN (";
                for (int i = 0; i < orderTypes.size(); i++) {
                    sql += "'" + orderTypes.get(i) + "'";
                    if (i < orderTypes.size() - 1) {
                        sql += ", ";
                    }
                }
                sql += ") ";
            }
            
            sql += "GROUP BY " + dateFormat + " ORDER BY " + dateFormat;
            
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, startDate.toString());
                pstmt.setString(2, endDate.toString());
                ResultSet rs = pstmt.executeQuery();
                
                while (rs.next()) {
                    Map<String, Object> dataPoint = new HashMap<>();
                    dataPoint.put("period", rs.getString("period"));
                    dataPoint.put("orders", rs.getInt("orders"));
                    dataPoint.put("revenue", rs.getDouble("revenue"));
                    trendData.add(dataPoint);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting revenue trend data: " + e.getMessage());
        }
        
        return trendData;
    }
    
    // Export filtered data
    public static boolean exportFilteredData(LocalDate startDate, LocalDate endDate, 
                                           List<String> orderTypes, String reportType, String filePath) {
        try {
            List<DailyReport> reports = generateFilteredReports(startDate, endDate, orderTypes, reportType);
            return ReportExportService.exportDailyReportsToCSV(reports, filePath);
        } catch (Exception e) {
            System.err.println("Error exporting filtered data: " + e.getMessage());
            return false;
        }
    }
}
