/* Restaurant Management System - Fixed CSS Styling */

/* Global Root Styling for Better Appearance */
.root {
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-background-color: #f8f9fa;
}

/* Improved Button Styling - FIXED SYNTAX */
.button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 10px 20px 10px 20px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #333333, 3, 0, 0, 1);
}

.button:hover {
    -fx-background-color: #0056b3;
    -fx-effect: dropshadow(gaussian, #00000066, 5, 0, 0, 2);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.button:pressed {
    -fx-background-color: #004085;
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

/* Primary Button Variant */
.button.primary {
    -fx-background-color: #28a745;
}

.button.primary:hover {
    -fx-background-color: #218838;
}

/* Secondary Button Variant */
.button.secondary {
    -fx-background-color: #6c757d;
}

.button.secondary:hover {
    -fx-background-color: #545b62;
}

/* Danger Button Variant */
.button.danger {
    -fx-background-color: #dc3545;
}

.button.danger:hover {
    -fx-background-color: #c82333;
}

/* Improved Text Field Styling */
.text-field {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 12px 16px 12px 16px;
    -fx-font-size: 14px;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 2, 0, 0, 1);
}

.text-field:focused {
    -fx-border-color: #007bff;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, #007bff, 4, 0, 0, 0);
}

/* Improved ComboBox Styling */
.combo-box {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 12px 16px 12px 16px;
    -fx-font-size: 14px;
}

.combo-box:focused {
    -fx-border-color: #007bff;
    -fx-border-width: 2px;
}

/* Improved Label Styling */
.label {
    -fx-text-fill: #495057;
    -fx-font-size: 14px;
}

.label.heading {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #212529;
}

.label.subheading {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #343a40;
}

/* Card-like containers */
.card {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-effect: dropshadow(gaussian, #333333, 8, 0, 0, 2);
    -fx-padding: 20px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
}

.card:hover {
    -fx-effect: dropshadow(gaussian, #4d4d4d, 12, 0, 0, 4);
    -fx-translate-y: -2;
}

/* WOK KA TADKA Login Screen Styles */
.wok-login-container {
    -fx-background-color: linear-gradient(135deg, #2D1B69 0%, #8B5CF6 50%, #EC4899 100%);
    -fx-padding: 40px;
}

.wok-header {
    -fx-alignment: center;
}

.wok-title {
    -fx-text-fill: #FF6B35;
    -fx-font-weight: bold;
    -fx-font-size: 48px;
    -fx-effect: dropshadow(gaussian, #4d4d4d, 4, 0, 0, 2);
}

.wok-subtitle {
    -fx-text-fill: white;
    -fx-font-size: 18px;
    -fx-opacity: 0.9;
}

.wok-login-card {
    -fx-background-color: white;
    -fx-background-radius: 20px;
    -fx-effect: dropshadow(gaussian, #333333, 25, 0, 0, 8);
    -fx-border-color: #FF6B35;
    -fx-border-width: 3px;
    -fx-border-radius: 20px;
}

.wok-card-header {
    -fx-background-color: #F8F9FA;
    -fx-background-radius: 17px 17px 0px 0px;
    -fx-border-color: #E9ECEF;
    -fx-border-width: 0px 0px 1px 0px;
}

.wok-user-icon {
    -fx-background-color: #FF6B35;
    -fx-text-fill: white;
    -fx-background-radius: 25px;
    -fx-min-width: 50px;
    -fx-min-height: 50px;
    -fx-max-width: 50px;
    -fx-max-height: 50px;
    -fx-alignment: center;
    -fx-font-size: 24px;
}

.wok-card-title {
    -fx-text-fill: #2D3748;
    -fx-font-weight: bold;
    -fx-font-size: 20px;
}

.wok-card-subtitle {
    -fx-text-fill: #718096;
    -fx-font-size: 12px;
}

.wok-form-section {
    -fx-background-color: white;
    -fx-background-radius: 0px 0px 17px 17px;
}

.wok-field-label {
    -fx-text-fill: #4A5568;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
}

.wok-input-field {
    -fx-background-color: #F7FAFC;
    -fx-border-color: #E2E8F0;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 15px 20px 15px 20px;
    -fx-font-size: 14px;
    -fx-pref-height: 45px;
}

.wok-input-field:focused {
    -fx-border-color: #FF6B35;
    -fx-background-color: white;
    -fx-effect: dropshadow(gaussian, #ff6b35, 4, 0, 0, 0);
}

.wok-combo-field {
    -fx-background-color: #F7FAFC;
    -fx-border-color: #E2E8F0;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 15px 20px 15px 20px;
    -fx-font-size: 14px;
    -fx-pref-height: 45px;
}

.wok-combo-field:focused {
    -fx-border-color: #FF6B35;
    -fx-background-color: white;
}

.wok-combo-field .arrow-button {
    -fx-background-color: transparent;
}

.wok-combo-field .arrow {
    -fx-background-color: #718096;
}

.wok-login-button {
    -fx-background-color: #FF6B35;
    -fx-text-fill: white;
    -fx-background-radius: 8px;
    -fx-padding: 15px 30px 15px 30px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #ff6b35, 8, 0, 0, 4);
    -fx-pref-height: 50px;
}

.wok-login-button:hover {
    -fx-background-color: #E55A2B;
    -fx-effect: dropshadow(gaussian, #ff6b35, 12, 0, 0, 6);
    -fx-scale-y: 1.02;
    -fx-scale-x: 1.02;
}

.wok-login-button:pressed {
    -fx-scale-y: 0.98;
    -fx-scale-x: 0.98;
}

/* Enhanced Dashboard Styles */
.dashboard-scroll {
    -fx-background-color: transparent;
    -fx-background: transparent;
}

.dashboard-content {
    -fx-background-color: #F8F9FA;
    -fx-padding: 15px;
}

.welcome-section {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-padding: 20px;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 8, 0, 0, 2);
    -fx-alignment: center;
}

.welcome-title {
    -fx-text-fill: #FF6B35;
    -fx-font-weight: bold;
}

.welcome-subtitle {
    -fx-text-fill: #6C757D;
}

.stats-container {
    -fx-alignment: center;
}

.stat-card {
    -fx-background-color: white;
    -fx-background-radius: 10px;
    -fx-padding: 15px;
    -fx-alignment: center;
    -fx-spacing: 8;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.08), 6, 0, 0, 2);
    -fx-min-width: 120px;
    -fx-pref-width: 140px;
    -fx-cursor: hand;
}

.stat-card:hover {
    -fx-effect: dropshadow(gaussian, #262626, 12, 0, 0, 4);
    -fx-scale-y: 1.02;
    -fx-scale-x: 1.02;
    -fx-background-color: #F8F9FA;
}

.stat-icon {
    -fx-font-size: 32px;
}

.stat-value {
    -fx-text-fill: #FF6B35;
    -fx-font-weight: bold;
    -fx-cursor: hand;
}

.stat-value:hover {
    -fx-text-fill: #E55A2B;
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.stat-label {
    -fx-text-fill: #6C757D;
    -fx-font-size: 12px;
}

/* ===== RESPONSIVE QUICK ACTIONS GRID ===== */

.quick-actions-section {
    -fx-background-color: white;
    -fx-background-radius: 15px;
    -fx-padding: 20px;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 12, 0, 0, 3);
    -fx-spacing: 15px;
}

.quick-actions-title {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
    -fx-font-size: 20px;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-padding: 0px 0px 10px 0px;
}

.quick-actions-responsive-grid {
    -fx-alignment: center;
    -fx-padding: 10px 0px;
}

/* Icon Card Styling */
.quick-action-icon-card {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-background-radius: 12px;
    -fx-padding: 10px 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 2);
    -fx-cursor: hand;
    -fx-alignment: center;
    -fx-spacing: 6px;
    -fx-max-height: 105px;
    -fx-pref-height: 105px;
}

.quick-action-icon-card:hover {
    -fx-background-color: white;
    -fx-border-color: #007bff;
    -fx-effect: dropshadow(gaussian, #007bff, 12, 0, 0, 4);
    -fx-translate-y: -3px;
}

/* Icon Styling */
.quick-action-icon {
    -fx-font-size: 24px;
    -fx-text-fill: #007bff;
    -fx-alignment: center;
    -fx-font-family: "Segoe UI Emoji", "Apple Color Emoji", "Noto Color Emoji", sans-serif;
}

.quick-action-icon-card:hover .quick-action-icon {
    -fx-font-size: 28px;
    -fx-text-fill: #0056b3;
}

/* Label Styling */
.quick-action-label {
    -fx-text-fill: #495057;
    -fx-font-weight: 600;
    -fx-font-size: 12px;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-text-alignment: center;
    -fx-alignment: center;
    -fx-wrap-text: true;
    -fx-max-width: 120px;
}

.quick-action-icon-card:hover .quick-action-label {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
}

/* Modern Button Styling */
.quick-action-button-modern {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-border-color: transparent;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 6px 12px;
    -fx-font-size: 11px;
    -fx-font-weight: 600;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #007bff, 3, 0, 0, 1);
    -fx-min-width: 65px;
}

.quick-action-button-modern:hover {
    -fx-background-color: #0056b3;
    -fx-effect: dropshadow(gaussian, #007bff, 6, 0, 0, 3);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.quick-action-button-modern:pressed {
    -fx-background-color: #004085;
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

/* ===== RESPONSIVE DESIGN FOR QUICK ACTIONS ===== */

/* Large Desktop (1200px and above) - 4 columns */
@media screen and (min-width: 1200px) {
    .quick-actions-responsive-grid {
        -fx-hgap: 25px;
        -fx-vgap: 25px;
    }

    .quick-action-icon-card {
        -fx-padding: 25px 20px;
        -fx-min-width: 180px;
        -fx-pref-width: 200px;
    }

    .quick-action-icon {
        -fx-font-size: 40px;
    }

    .quick-action-icon-card:hover .quick-action-icon {
        -fx-font-size: 44px;
    }

    .quick-action-label {
        -fx-font-size: 15px;
        -fx-max-width: 160px;
    }

    .quick-action-button-modern {
        -fx-padding: 10px 18px;
        -fx-font-size: 13px;
        -fx-min-width: 90px;
    }
}

/* Medium Desktop (992px to 1199px) - 4 columns */
@media screen and (min-width: 992px) and (max-width: 1199px) {
    .quick-actions-responsive-grid {
        -fx-hgap: 20px;
        -fx-vgap: 20px;
    }

    .quick-action-icon-card {
        -fx-padding: 20px 15px;
        -fx-min-width: 160px;
        -fx-pref-width: 180px;
    }

    .quick-action-icon {
        -fx-font-size: 36px;
    }

    .quick-action-icon-card:hover .quick-action-icon {
        -fx-font-size: 40px;
    }

    .quick-action-label {
        -fx-font-size: 14px;
        -fx-max-width: 140px;
    }

    .quick-action-button-modern {
        -fx-padding: 8px 16px;
        -fx-font-size: 12px;
        -fx-min-width: 80px;
    }
}

/* Tablet (768px to 991px) - 3 columns */
@media screen and (min-width: 768px) and (max-width: 991px) {
    .quick-actions-responsive-grid {
        -fx-hgap: 18px;
        -fx-vgap: 18px;
    }

    .quick-action-icon-card {
        -fx-padding: 18px 12px;
        -fx-min-width: 140px;
        -fx-pref-width: 160px;
    }

    .quick-action-icon {
        -fx-font-size: 32px;
    }

    .quick-action-icon-card:hover .quick-action-icon {
        -fx-font-size: 36px;
    }

    .quick-action-label {
        -fx-font-size: 13px;
        -fx-max-width: 120px;
    }

    .quick-action-button-modern {
        -fx-padding: 7px 14px;
        -fx-font-size: 11px;
        -fx-min-width: 70px;
    }
}

/* Mobile (576px to 767px) - 2 columns */
@media screen and (min-width: 576px) and (max-width: 767px) {
    .quick-actions-responsive-grid {
        -fx-hgap: 15px;
        -fx-vgap: 15px;
    }

    .quick-action-icon-card {
        -fx-padding: 15px 10px;
        -fx-min-width: 120px;
        -fx-pref-width: 140px;
    }

    .quick-action-icon {
        -fx-font-size: 28px;
    }

    .quick-action-icon-card:hover .quick-action-icon {
        -fx-font-size: 32px;
    }

    .quick-action-label {
        -fx-font-size: 12px;
        -fx-max-width: 100px;
    }

    .quick-action-button-modern {
        -fx-padding: 6px 12px;
        -fx-font-size: 10px;
        -fx-min-width: 60px;
    }
}

/* Small Mobile (below 576px) - 1 column */
@media screen and (max-width: 575px) {
    .quick-actions-section {
        -fx-padding: 20px 15px;
    }

    .quick-actions-responsive-grid {
        -fx-hgap: 12px;
        -fx-vgap: 12px;
    }

    .quick-action-icon-card {
        -fx-padding: 12px 8px;
        -fx-min-width: 100px;
        -fx-pref-width: 120px;
    }

    .quick-action-icon {
        -fx-font-size: 24px;
    }

    .quick-action-icon-card:hover .quick-action-icon {
        -fx-font-size: 28px;
    }

    .quick-action-label {
        -fx-font-size: 11px;
        -fx-max-width: 80px;
    }

    .quick-action-button-modern {
        -fx-padding: 5px 10px;
        -fx-font-size: 9px;
        -fx-min-width: 50px;
    }

    .quick-actions-title {
        -fx-font-size: 18px;
    }
}

/* ===== ENHANCED VISUAL EFFECTS ===== */

/* Loading Animation for Quick Actions */
.quick-action-icon-card.loading {
    -fx-opacity: 0.7;
}

.quick-action-icon-card.loading .quick-action-icon {
    -fx-rotate: 360deg;
}

/* Focus States for Accessibility */
.quick-action-icon-card:focused {
    -fx-border-color: #007bff;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, #007bff, 8, 0, 0, 0);
}

.quick-action-button-modern:focused {
    -fx-border-color: #80bdff;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, #007bff, 6, 0, 0, 0);
}

/* Disabled States */
.quick-action-icon-card:disabled {
    -fx-opacity: 0.5;
    -fx-cursor: default;
}

.quick-action-icon-card:disabled .quick-action-icon {
    -fx-text-fill: #6c757d;
}

.quick-action-icon-card:disabled .quick-action-label {
    -fx-text-fill: #6c757d;
}

.quick-action-button-modern:disabled {
    -fx-background-color: #6c757d;
    -fx-opacity: 0.6;
    -fx-cursor: default;
}

/* Success/Error States */
.quick-action-icon-card.success {
    -fx-border-color: #28a745;
    -fx-background-color: #f8fff9;
}

.quick-action-icon-card.success .quick-action-icon {
    -fx-text-fill: #28a745;
}

.quick-action-icon-card.error {
    -fx-border-color: #dc3545;
    -fx-background-color: #fff8f8;
}

.quick-action-icon-card.error .quick-action-icon {
    -fx-text-fill: #dc3545;
}

/* Smooth Transitions */
.quick-action-icon-card {
    -fx-transition: all 0.3s ease;
}

.quick-action-icon {
    -fx-transition: all 0.2s ease;
}

.quick-action-button-modern {
    -fx-transition: all 0.2s ease;
}

/* Grid Layout Adjustments for Better Spacing */
.quick-actions-responsive-grid .quick-action-icon-card {
    -fx-max-width: 220px;
    -fx-max-height: 180px;
}

/* Ensure Equal Heights */
.quick-actions-responsive-grid > * {
    -fx-alignment: center;
    -fx-fill-height: true;
}

/* Professional Shadow Effects */
.quick-actions-section {
    -fx-effect: dropshadow(gaussian, #1a1a1a, 15, 0, 0, 5);
}

.quick-action-icon-card {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.08), 8, 0, 0, 3);
}

.quick-action-icon-card:hover {
    -fx-effect: dropshadow(gaussian, #007bff, 15, 0, 0, 6);
}

.section-title {
    -fx-text-fill: #2D3748;
    -fx-font-weight: bold;
}

.quick-actions-grid {
    -fx-alignment: center-left;
}

/* Enhanced Quick Action Buttons */
.quick-action-btn-primary {
    -fx-background-color: #FF6B35;
    -fx-text-fill: white;
    -fx-background-radius: 10px;
    -fx-padding: 15px 20px 15px 20px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #ff6b35, 8, 0, 0, 3);
}

.quick-action-btn-primary:hover {
    -fx-background-color: #E55A2B;
    -fx-scale-y: 1.05;
    -fx-scale-x: 1.05;
    -fx-effect: dropshadow(gaussian, #ff6b35, 12, 0, 0, 5);
}

.quick-action-btn-secondary {
    -fx-background-color: #4299E1;
    -fx-text-fill: white;
    -fx-background-radius: 10px;
    -fx-padding: 15px 20px 15px 20px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(66, 153, 225, 0.3), 8, 0, 0, 3);
}

.quick-action-btn-secondary:hover {
    -fx-background-color: #3182CE;
    -fx-scale-y: 1.05;
    -fx-scale-x: 1.05;
    -fx-effect: dropshadow(gaussian, rgba(66, 153, 225, 0.5), 12, 0, 0, 5);
}

.quick-action-btn-warning {
    -fx-background-color: #F6AD55;
    -fx-text-fill: white;
    -fx-background-radius: 10px;
    -fx-padding: 15px 20px 15px 20px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(246, 173, 85, 0.3), 8, 0, 0, 3);
}

.quick-action-btn-warning:hover {
    -fx-background-color: #ED8936;
    -fx-scale-y: 1.05;
    -fx-scale-x: 1.05;
    -fx-effect: dropshadow(gaussian, rgba(246, 173, 85, 0.5), 12, 0, 0, 5);
}

.quick-action-btn-info {
    -fx-background-color: #38B2AC;
    -fx-text-fill: white;
    -fx-background-radius: 10px;
    -fx-padding: 15px 20px 15px 20px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(56, 178, 172, 0.3), 8, 0, 0, 3);
}

.quick-action-btn-info:hover {
    -fx-background-color: #319795;
    -fx-scale-y: 1.05;
    -fx-scale-x: 1.05;
    -fx-effect: dropshadow(gaussian, rgba(56, 178, 172, 0.5), 12, 0, 0, 5);
}

.quick-action-btn-urgent {
    -fx-background-color: #F56565;
    -fx-text-fill: white;
    -fx-background-radius: 10px;
    -fx-padding: 15px 20px 15px 20px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(245, 101, 101, 0.3), 8, 0, 0, 3);
    -fx-border-color: #FC8181;
    -fx-border-width: 2px;
    -fx-border-radius: 10px;
}

.quick-action-btn-urgent:hover {
    -fx-background-color: #E53E3E;
    -fx-scale-y: 1.05;
    -fx-scale-x: 1.05;
    -fx-effect: dropshadow(gaussian, rgba(245, 101, 101, 0.6), 12, 0, 0, 5);
}

.quick-action-btn-success {
    -fx-background-color: #48BB78;
    -fx-text-fill: white;
    -fx-background-radius: 10px;
    -fx-padding: 15px 20px 15px 20px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(72, 187, 120, 0.3), 8, 0, 0, 3);
}

.quick-action-btn-success:hover {
    -fx-background-color: #38A169;
    -fx-scale-y: 1.05;
    -fx-scale-x: 1.05;
    -fx-effect: dropshadow(gaussian, rgba(72, 187, 120, 0.5), 12, 0, 0, 5);
}

.quick-action-btn-maintenance {
    -fx-background-color: #805AD5;
    -fx-text-fill: white;
    -fx-background-radius: 10px;
    -fx-padding: 15px 20px 15px 20px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(128, 90, 213, 0.3), 8, 0, 0, 3);
}

.quick-action-btn-maintenance:hover {
    -fx-background-color: #6B46C1;
    -fx-scale-y: 1.05;
    -fx-scale-x: 1.05;
    -fx-effect: dropshadow(gaussian, rgba(128, 90, 213, 0.5), 12, 0, 0, 5);
}

.quick-action-btn-emergency {
    -fx-background-color: #E53E3E;
    -fx-text-fill: white;
    -fx-background-radius: 10px;
    -fx-padding: 15px 20px 15px 20px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(229, 62, 62, 0.4), 8, 0, 0, 3);
    -fx-border-color: #FC8181;
    -fx-border-width: 2px;
    -fx-border-radius: 10px;
}

.quick-action-btn-emergency:hover {
    -fx-background-color: #C53030;
    -fx-scale-y: 1.08;
    -fx-scale-x: 1.08;
    -fx-effect: dropshadow(gaussian, rgba(229, 62, 62, 0.7), 15, 0, 0, 6);
}

/* Legacy quick action button for backward compatibility */
.quick-action-btn {
    -fx-background-color: #F7FAFC;
    -fx-text-fill: #4A5568;
    -fx-background-radius: 8px;
    -fx-padding: 15px 20px 15px 20px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-border-color: #E2E8F0;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
}

.quick-action-btn:hover {
    -fx-background-color: #FF6B35;
    -fx-text-fill: white;
    -fx-border-color: #FF6B35;
    -fx-effect: dropshadow(gaussian, #ff6b35, 6, 0, 0, 2);
}

/* Table Management Styles */
.back-button {
    -fx-background-color: #E2E8F0;
    -fx-text-fill: #4A5568;
    -fx-background-radius: 20px;
    -fx-padding: 10px 15px 10px 15px;
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-border-color: #CBD5E0;
    -fx-border-width: 1px;
    -fx-border-radius: 20px;
}

.back-button:hover {
    -fx-background-color: #CBD5E0;
    -fx-border-color: #A0AEC0;
}

.module-subtitle {
    -fx-text-fill: #718096;
    -fx-font-size: 14px;
}

.status-legend {
    -fx-background-color: #F7FAFC;
    -fx-border-color: #E2E8F0;
    -fx-border-width: 0px 0px 1px 0px;
}

.status-indicator-free {
    -fx-text-fill: #68D391;
    -fx-font-size: 16px;
}

.status-indicator-occupied {
    -fx-text-fill: #FC8181;
    -fx-font-size: 16px;
}

.status-indicator-preparing {
    -fx-text-fill: #F6AD55;
    -fx-font-size: 16px;
}

.tables-scroll {
    -fx-background-color: #F8F9FA;
    -fx-background: #F8F9FA;
    -fx-fit-to-width: true;
    -fx-fit-to-height: true;
}

.tables-grid {
    -fx-background-color: #F8F9FA;
    -fx-alignment: center;
    -fx-hgap: 30;
    -fx-vgap: 30;
    -fx-padding: 20px;
    -fx-min-height: 600px;
    -fx-pref-height: 600px;
}

.table-card {
    -fx-background-color: white;
    -fx-background-radius: 15px;
    -fx-padding: 20px;
    -fx-alignment: center;
    -fx-spacing: 8;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 10, 0, 0, 3);
    -fx-min-height: 180px;
    -fx-max-height: 220px;
    -fx-min-width: 220px;
    -fx-max-width: 280px;
    -fx-pref-height: 200px;
    -fx-pref-width: 250px;
    -fx-opacity: 1.0;
    visibility: visible;
}

.table-card:hover {
    -fx-effect: dropshadow(gaussian, #333333, 15, 0, 0, 5);
    -fx-scale-y: 1.02;
    -fx-scale-x: 1.02;
}

/* Table Action Buttons */
.table-actions {
    -fx-padding: 8px 0 0 0;
    -fx-spacing: 8px;
}

.table-action-btn {
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 6px 12px;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-min-width: 60px;
    -fx-pref-width: 70px;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 2, 0, 0, 1);
}

.eye-button {
    -fx-background-color: #17a2b8;
    -fx-text-fill: white;
    -fx-border-color: #138496;
    -fx-border-width: 1px;
}

.eye-button:hover {
    -fx-background-color: #138496;
    -fx-effect: dropshadow(gaussian, rgba(23, 162, 184, 0.3), 4, 0, 0, 2);
}

.select-button {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-border-color: #1e7e34;
    -fx-border-width: 1px;
}

.select-button:hover {
    -fx-background-color: #218838;
    -fx-effect: dropshadow(gaussian, #28a745, 4, 0, 0, 2);
}

.add-button {
    -fx-background-color: #ffc107;
    -fx-text-fill: #212529;
    -fx-border-color: #e0a800;
    -fx-border-width: 1px;
}

.add-button:hover {
    -fx-background-color: #e0a800;
    -fx-effect: dropshadow(gaussian, rgba(255, 193, 7, 0.3), 4, 0, 0, 2);
}

/* Simple Table Management Styles */
.simple-table-card {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 2px;
    -fx-padding: 20px;
    -fx-spacing: 10px;
    -fx-alignment: center;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 8, 0, 0, 2);
    -fx-min-height: 200px;
    -fx-pref-height: 220px;
    -fx-min-width: 200px;
    -fx-pref-width: 250px;
}

.simple-table-card:hover {
    -fx-effect: dropshadow(gaussian, #333333, 12, 0, 0, 4);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.table-number {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
    -fx-font-size: 20px;
}

.table-seats {
    -fx-text-fill: #6c757d;
    -fx-font-size: 14px;
}

.table-status-available {
    -fx-text-fill: #28a745;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
}

.table-status-occupied {
    -fx-text-fill: #dc3545;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
}

.table-status-preparing {
    -fx-text-fill: #fd7e14;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
}

.table-status-ready {
    -fx-text-fill: #17a2b8;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
}

.table-status-kot {
    -fx-text-fill: #6f42c1;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
}

.table-status-completed {
    -fx-text-fill: #20c997;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
}

.order-badge-simple {
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #495057;
    -fx-padding: 4px 8px;
    -fx-background-radius: 4px;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

.simple-eye-btn {
    -fx-background-color: #17a2b8;
    -fx-text-fill: white;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 8px 12px;
    -fx-font-size: 14px;
    -fx-cursor: hand;
    -fx-min-width: 40px;
}

.simple-eye-btn:hover {
    -fx-background-color: #138496;
}

.simple-select-btn {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 8px 12px;
    -fx-font-size: 12px;
    -fx-cursor: hand;
    -fx-min-width: 60px;
}

.simple-select-btn:hover {
    -fx-background-color: #218838;
}

.simple-add-btn {
    -fx-background-color: #ffc107;
    -fx-text-fill: #212529;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 8px 12px;
    -fx-font-size: 14px;
    -fx-cursor: hand;
    -fx-min-width: 40px;
}

.simple-add-btn:hover {
    -fx-background-color: #e0a800;
}

.table-free {
    -fx-border-color: #68D391;
    -fx-border-width: 3px;
    -fx-border-radius: 15px;
}

.table-free:hover {
    -fx-background-color: #F0FFF4;
    -fx-border-color: #48BB78;
}

.table-occupied {
    -fx-border-color: #FC8181;
    -fx-border-width: 3px;
    -fx-border-radius: 15px;
}

.table-occupied:hover {
    -fx-background-color: #FFF5F5;
    -fx-border-color: #F56565;
}

.table-preparing {
    -fx-border-color: #F6AD55;
    -fx-border-width: 3px;
    -fx-border-radius: 15px;
}

.table-preparing:hover {
    -fx-background-color: #FFFAF0;
    -fx-border-color: #ED8936;
}

.table-number {
    -fx-text-fill: #2D3748;
    -fx-font-weight: bold;
}

.table-status {
    -fx-text-fill: #718096;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

.order-badge {
    -fx-background-color: #4299E1;
    -fx-text-fill: white;
    -fx-background-radius: 12px;
    -fx-padding: 5px 10px 5px 10px;
    -fx-font-size: 11px;
    -fx-font-weight: bold;
}

.recent-activity-section {
    -fx-background-color: white;
    -fx-background-radius: 15px;
    -fx-padding: 25px;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 10, 0, 0, 2);
}

.activity-table {
    -fx-background-color: transparent;
    -fx-background-radius: 8px;
    -fx-border-color: #E2E8F0;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
}

/* Module Styles */
.module-header {
    -fx-background-color: white;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 8, 0, 0, 2);
}

.module-title {
    -fx-text-fill: #2D3748;
    -fx-font-weight: bold;
}

.primary-button {
    -fx-background-color: #FF6B35;
    -fx-text-fill: white;
    -fx-background-radius: 8px;
    -fx-padding: 12px 20px 12px 20px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #ff6b35, 6, 0, 0, 2);
}

.primary-button:hover {
    -fx-background-color: #E55A2B;
    -fx-effect: dropshadow(gaussian, #ff6b35, 8, 0, 0, 3);
}

.secondary-button {
    -fx-background-color: #F7FAFC;
    -fx-text-fill: #4A5568;
    -fx-background-radius: 8px;
    -fx-padding: 12px 20px 12px 20px;
    -fx-font-size: 14px;
    -fx-cursor: hand;
    -fx-border-color: #E2E8F0;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
}

.secondary-button:hover {
    -fx-background-color: #EDF2F7;
    -fx-border-color: #CBD5E0;
}

.filter-section {
    -fx-background-color: #F8F9FA;
    -fx-padding: 15px;
    -fx-border-color: #E9ECEF;
    -fx-border-width: 0px 0px 1px 0px;
}

.search-field {
    -fx-background-color: white;
    -fx-border-color: #E2E8F0;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 10px 15px 10px 15px;
    -fx-font-size: 14px;
    -fx-pref-width: 250px;
}

.search-field:focused {
    -fx-border-color: #FF6B35;
    -fx-effect: dropshadow(gaussian, #ff6b35, 4, 0, 0, 0);
}

.filter-combo {
    -fx-background-color: white;
    -fx-border-color: #E2E8F0;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 10px 15px 10px 15px;
    -fx-font-size: 14px;
    -fx-pref-width: 150px;
}

.filter-combo:focused {
    -fx-border-color: #FF6B35;
}

.refresh-button {
    -fx-background-color: #E3F2FD;
    -fx-text-fill: #1976D2;
    -fx-background-radius: 6px;
    -fx-padding: 10px 15px 10px 15px;
    -fx-font-size: 14px;
    -fx-cursor: hand;
    -fx-border-color: #BBDEFB;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
}

.refresh-button:hover {
    -fx-background-color: #BBDEFB;
}

.table-section {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 10, 0, 0, 2);
}

.users-table, .orders-table {
    -fx-background-color: transparent;
    -fx-background-radius: 8px;
}

.users-table .table-row-cell:selected,
.orders-table .table-row-cell:selected {
    -fx-background-color: rgba(255, 107, 53, 0.1);
    -fx-text-fill: #2D3748;
}

.users-table .table-row-cell:hover,
.orders-table .table-row-cell:hover {
    -fx-background-color: rgba(255, 107, 53, 0.05);
}

/* Centered Modal Dialog System */
.centered-modal-overlay {
    -fx-background-color: #999999;
    -fx-alignment: center;
}

.modal-backdrop {
    -fx-background-color: #999999;
    -fx-cursor: hand;
}

.centered-modal-dialog {
    -fx-background-color: white;
    -fx-background-radius: 10px;
    -fx-effect: dropshadow(gaussian, #666666, 20, 0, 0, 5);
    -fx-border-color: #262626;
    -fx-border-width: 1px;
    -fx-border-radius: 10px;
    -fx-spacing: 0;
    -fx-alignment: center;
}

/* Modal Header */
.modal-header {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 10px 10px 0px 0px;
    -fx-padding: 20px 25px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 0px 0px 1px 0px;
}

.modal-title {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
    -fx-font-size: 18px;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.modal-close-btn {
    -fx-background-color: #f1f3f4;
    -fx-text-fill: #6c757d;
    -fx-background-radius: 5px;
    -fx-border-radius: 5px;
    -fx-padding: 8px 10px;
    -fx-font-size: 14px;
    -fx-cursor: hand;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-min-width: 30px;
    -fx-min-height: 30px;
}

.modal-close-btn:hover {
    -fx-background-color: #e9ecef;
    -fx-text-fill: #495057;
    -fx-border-color: #adb5bd;
}

/* Modal Body */
.modal-scroll-pane {
    -fx-background-color: transparent;
    -fx-background: transparent;
    -fx-fit-to-width: true;
    -fx-hbar-policy: never;
    -fx-vbar-policy: as-needed;
    -fx-max-height: 350px;
}

.modal-scroll-pane .viewport {
    -fx-background-color: transparent;
}

.modal-scroll-pane .scroll-bar:vertical {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 4px;
    -fx-pref-width: 8px;
}

.modal-scroll-pane .scroll-bar:vertical .track {
    -fx-background-color: transparent;
}

.modal-scroll-pane .scroll-bar:vertical .thumb {
    -fx-background-color: #dee2e6;
    -fx-background-radius: 4px;
}

.modal-scroll-pane .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #adb5bd;
}

.modal-body {
    -fx-padding: 25px;
    -fx-background-color: white;
}

.form-label {
    -fx-text-fill: #495057;
    -fx-font-weight: 500;
    -fx-font-size: 14px;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.form-field {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-width: 1px;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
    -fx-padding: 10px 12px;
    -fx-font-size: 14px;
    -fx-text-fill: #495057;
    -fx-prompt-text-fill: #6c757d;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-min-height: 38px;
}

.form-field:focused {
    -fx-border-color: #007bff;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, #007bff, 4, 0, 0, 0);
}

/* Modal Footer */
.modal-footer {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 0px 0px 10px 10px;
    -fx-padding: 18px 25px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px 0px 0px 0px;
}

.cancel-btn {
    -fx-background-color: white;
    -fx-text-fill: #6c757d;
    -fx-background-radius: 5px;
    -fx-border-radius: 5px;
    -fx-padding: 10px 20px;
    -fx-font-size: 14px;
    -fx-font-weight: 500;
    -fx-cursor: hand;
    -fx-border-color: #ced4da;
    -fx-border-width: 1px;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-min-width: 90px;
}

.cancel-btn:hover {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #adb5bd;
    -fx-text-fill: #495057;
}

.save-btn {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-background-radius: 5px;
    -fx-border-radius: 5px;
    -fx-padding: 10px 20px;
    -fx-font-size: 14px;
    -fx-font-weight: 500;
    -fx-cursor: hand;
    -fx-border-color: transparent;
    -fx-border-width: 1px;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-min-width: 110px;
}

.save-btn:hover {
    -fx-background-color: #0056b3;
}

/* Add User Page Styles */
.page-header {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 0px 0px 1px 0px;
}

/* Primary back button style - keep this one */
.back-button {
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #6c757d;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 10px 18px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-min-width: 80px;
    -fx-pref-width: 100px;
    -fx-min-height: 36px;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 2, 0, 0, 1);
}

.back-button:hover {
    -fx-background-color: #e9ecef;
    -fx-text-fill: #495057;
    -fx-border-color: #adb5bd;
    -fx-effect: dropshadow(gaussian, #262626, 4, 0, 0, 2);
}

.back-button:pressed {
    -fx-background-color: #dee2e6;
    -fx-effect: dropshadow(gaussian, #333333, 2, 0, 0, 1);
}

.page-title {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
    -fx-font-size: 24px;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.add-user-container {
    -fx-background-color: #f8f9fa;
    -fx-min-height: 600px;
}

.form-card {
    -fx-background-color: white;
    -fx-background-radius: 8px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-padding: 30px;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 10, 0, 0, 2);
}

.section-title {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
    -fx-font-size: 18px;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.field-label {
    -fx-text-fill: #495057;
    -fx-font-weight: 500;
    -fx-font-size: 14px;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.input-field {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 12px 16px;
    -fx-font-size: 14px;
    -fx-text-fill: #495057;
    -fx-prompt-text-fill: #6c757d;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-min-height: 42px;
}

.input-field:focused {
    -fx-border-color: #007bff;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, #007bff, 4, 0, 0, 0);
}

.action-buttons {
    -fx-padding: 20px 0px 0px 0px;
}

.primary-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 12px 24px;
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-cursor: hand;
    -fx-border-color: transparent;
    -fx-border-width: 1px;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-min-width: 120px;
}

.primary-button:hover {
    -fx-background-color: #0056b3;
    -fx-effect: dropshadow(gaussian, #007bff, 6, 0, 0, 2);
}

.secondary-button {
    -fx-background-color: white;
    -fx-text-fill: #6c757d;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 12px 24px;
    -fx-font-size: 14px;
    -fx-font-weight: 500;
    -fx-cursor: hand;
    -fx-border-color: #ced4da;
    -fx-border-width: 1px;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-min-width: 100px;
}

.secondary-button:hover {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #adb5bd;
    -fx-text-fill: #495057;
}

/* Responsive Modal Z-Index and Viewport Management */
.responsive-modal-overlay {
    -fx-view-order: -1000;
}

.responsive-modal-backdrop {
    -fx-view-order: -999;
}

.responsive-modal-container {
    -fx-view-order: -998;
}

.responsive-modal-dialog {
    -fx-view-order: -997;
}

/* Additional Viewport Constraints */
.responsive-modal-scroll-pane {
    -fx-max-height: 450px;
    -fx-min-height: 200px;
}

/* Prevent Background Interaction */
.responsive-modal-overlay {
    -fx-mouse-transparent: false;
}

.responsive-modal-backdrop {
    -fx-mouse-transparent: false;
}

/* Animation Support */
.responsive-modal-overlay.modal-show {
    -fx-opacity: 1;
}

.responsive-modal-overlay.modal-hide {
    -fx-opacity: 0;
}

/* Enhanced Accessibility and Focus Management */
.responsive-form-field:focused {
    -fx-border-color: #1a73e8;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, rgba(26, 115, 232, 0.25), 6, 0, 0, 0);
}

.responsive-modal-close-btn:focused,
.responsive-cancel-btn:focused,
.responsive-save-btn:focused {
    -fx-effect: dropshadow(gaussian, rgba(26, 115, 232, 0.3), 8, 0, 0, 0);
}

/* Responsive Sizing for Smaller Screens */
.responsive-modal-container.small-screen {
    -fx-max-width: 600px;
    -fx-min-width: 320px;
    -fx-max-height: 650px;
}

.responsive-modal-scroll-pane.small-screen {
    -fx-max-height: 400px;
}

.responsive-modal-body.small-screen {
    -fx-padding: 16px;
}

.responsive-modal-header.small-screen {
    -fx-padding: 12px 16px;
}

.responsive-modal-footer.small-screen {
    -fx-padding: 12px 16px;
}

/* Dashboard Styles */
.header {
    -fx-background-color: white;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 4, 0, 0, 2);
}

.top-bar {
    -fx-background-color: #1F2937;
}

.app-title {
    -fx-text-fill: white;
}

.user-info {
    -fx-text-fill: #D1D5DB;
}

.logout-button {
    -fx-background-color: linear-gradient(to bottom, #EF4444, #DC2626);
    -fx-text-fill: white;
    -fx-background-radius: 8px;
    -fx-padding: 8px 14px;
    -fx-cursor: hand;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-min-width: 65px;
    -fx-pref-width: 75px;
    -fx-min-height: 32px;
    -fx-pref-height: 36px;
    -fx-border-radius: 8px;
    -fx-border-color: #B91C1C;
    -fx-border-width: 1px;
    -fx-effect: dropshadow(gaussian, rgba(239, 68, 68, 0.4), 4, 0, 0, 2);
}

.logout-button:hover {
    -fx-background-color: linear-gradient(to bottom, #DC2626, #B91C1C);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
    -fx-effect: dropshadow(gaussian, rgba(239, 68, 68, 0.6), 6, 0, 0, 3);
}

/* Header Icon Buttons - Better visibility */
.header-icon-button {
    -fx-background-color: linear-gradient(to bottom, #3B82F6, #2563EB);
    -fx-text-fill: white;
    -fx-background-radius: 8px;
    -fx-padding: 8px 14px 8px 14px;
    -fx-cursor: hand;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-min-width: 65px;
    -fx-min-height: 32px;
    -fx-pref-width: 75px;
    -fx-pref-height: 36px;
    -fx-border-radius: 8px;
    -fx-border-color: #1D4ED8;
    -fx-border-width: 1px;
    -fx-effect: dropshadow(gaussian, rgba(59, 130, 246, 0.4), 4, 0, 0, 2);
}

.header-icon-button:hover {
    -fx-background-color: linear-gradient(to bottom, #2563EB, #1D4ED8);
    -fx-text-fill: white;
    -fx-border-color: #1E40AF;
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
    -fx-effect: dropshadow(gaussian, rgba(59, 130, 246, 0.6), 6, 0, 0, 3);
}

.header-icon-button:pressed {
    -fx-background-color: #374151;
    -fx-text-fill: white;
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

/* Icon Buttons (Home and Notifications) */
.icon-button {
    -fx-background-color: #374151;
    -fx-text-fill: white;
    -fx-background-radius: 8px;
    -fx-padding: 12px 16px 12px 16px;
    -fx-cursor: hand;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-min-width: 80px;
    -fx-min-height: 40px;
    -fx-pref-width: 80px;
    -fx-pref-height: 40px;
    -fx-border-radius: 8px;
    -fx-border-color: #4B5563;
    -fx-border-width: 1px;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 3, 0, 0, 1);
}

.icon-button:hover {
    -fx-background-color: #4B5563;
    -fx-text-fill: white;
    -fx-border-color: #6B7280;
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 5, 0, 0, 2);
}

.icon-button:pressed {
    -fx-background-color: #1F2937;
    -fx-text-fill: white;
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

/* Navigation Icon Buttons (in navigation bar) */
.nav-icon-button {
    -fx-background-color: transparent;
    -fx-text-fill: #6b7280;
    -fx-background-radius: 6px;
    -fx-padding: 8px 12px;
    -fx-cursor: hand;
    -fx-font-size: 16px;
    -fx-min-width: 40px;
    -fx-min-height: 36px;
    -fx-border-radius: 6px;
    -fx-border-color: transparent;
    -fx-border-width: 1px;
}

.nav-icon-button:hover {
    -fx-background-color: -fx-primary-50;
    -fx-text-fill: -fx-primary-600;
    -fx-border-color: -fx-primary-200;
}

.nav-icon-button:pressed {
    -fx-background-color: -fx-primary-100;
    -fx-text-fill: -fx-primary-700;
}

.navigation-bar {
    -fx-background-color: #F3F4F6;
    -fx-border-color: #E5E7EB;
    -fx-border-width: 0 0 1px 0;
}

.nav-button {
    -fx-background-color: transparent;
    -fx-text-fill: #6B7280;
    -fx-background-radius: 8px;
    -fx-padding: 8px 12px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
    -fx-font-size: 12px;
}

.nav-button:hover {
    -fx-background-color: #E5E7EB;
    -fx-text-fill: #374151;
}

.nav-button-active {
    -fx-background-color: #3B82F6;
    -fx-text-fill: white;
}

.nav-button-active:hover {
    -fx-background-color: #2563EB;
}

/* AI-Powered Feature Buttons */
.ai-button {
    -fx-background-color: linear-gradient(to bottom, #667eea 0%, #764ba2 100%);
    -fx-text-fill: white;
    -fx-background-radius: 8px;
    -fx-padding: 10px 16px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
    -fx-border-color: transparent;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(three-pass-box, #333333, 4, 0, 0, 2);
}

.ai-button:hover {
    -fx-background-color: linear-gradient(to bottom, #5a67d8 0%, #6b46c1 100%);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
    -fx-effect: dropshadow(three-pass-box, #4d4d4d, 6, 0, 0, 3);
}

.ai-button:pressed {
    -fx-background-color: linear-gradient(to bottom, #4c51bf 0%, #553c9a 100%);
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

.content-area {
    -fx-background-color: #f9fafb;
}

.welcome-message {
    -fx-text-fill: -fx-secondary-600;
}

/* Section Styles */
.section-title {
    -fx-text-fill: -fx-secondary-800;
    -fx-font-weight: bold;
}

.order-header, .menu-header, .reports-header {
    -fx-background-color: white;
    -fx-background-radius: 12px 12px 0 0;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.05), 4, 0, 0, 1);
}

.menu-section, .order-section, .table-section, .form-section, .report-section {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.05), 8, 0, 0, 2);
}

/* Button Styles */
.action-button {
    -fx-background-radius: 8px;
    -fx-padding: 10px 16px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 2, 0, 0, 1);
}

.add-button, .new-button {
    -fx-background-color: -fx-success-600;
    -fx-text-fill: white;
}

.add-button:hover, .new-button:hover {
    -fx-background-color: -fx-success-700;
}

.edit-button, .save-button {
    -fx-background-color: -fx-primary-600;
    -fx-text-fill: white;
}

.edit-button:hover, .save-button:hover {
    -fx-background-color: -fx-primary-700;
}

.delete-button, .remove-button {
    -fx-background-color: -fx-error-600;
    -fx-text-fill: white;
}

.delete-button:hover, .remove-button:hover {
    -fx-background-color: -fx-error-700;
}

.cancel-button, .clear-button {
    -fx-background-color: -fx-gray-500;
    -fx-text-fill: white;
}

.cancel-button:hover, .clear-button:hover {
    -fx-background-color: #6b7280;
}

.refresh-button {
    -fx-background-color: -fx-secondary-600;
    -fx-text-fill: white;
}

.refresh-button:hover {
    -fx-background-color: -fx-secondary-700;
}

.kot-button {
    -fx-background-color: -fx-warning-600;
    -fx-text-fill: white;
}

.kot-button:hover {
    -fx-background-color: -fx-warning-700;
}

.bill-button, .generate-button {
    -fx-background-color: #8b5cf6;
    -fx-text-fill: white;
}

.bill-button:hover, .generate-button:hover {
    -fx-background-color: #7c3aed;
}

.export-button {
    -fx-background-color: #06b6d4;
    -fx-text-fill: white;
}

.export-button:hover {
    -fx-background-color: #0891b2;
}

/* Order Summary Styles */
.order-summary {
    -fx-background-color: #f9fafb;
    -fx-background-radius: 8px;
    -fx-border-color: #e5e7eb;
    -fx-border-radius: 8px;
    -fx-border-width: 1px;
}

.total-label, .total-amount {
    -fx-text-fill: -fx-secondary-800;
}

/* Summary Cards */
.summary-cards {
    -fx-spacing: 20px;
}

.summary-card {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.05), 8, 0, 0, 2);
    -fx-min-width: 150px;
}

.card-title {
    -fx-text-fill: -fx-secondary-600;
}

.card-value {
    -fx-text-fill: -fx-secondary-800;
}

/* Table Styles */
.table-view {
    -fx-background-color: white;
    -fx-background-radius: 8px;
    -fx-border-color: #e5e7eb;
    -fx-border-radius: 8px;
    -fx-border-width: 1px;
}

.table-view .column-header {
    -fx-background-color: -fx-gray-100;
    -fx-text-fill: -fx-secondary-700;
    -fx-font-weight: bold;
    -fx-border-color: #e5e7eb;
}

.table-view .table-row-cell {
    -fx-background-color: white;
    -fx-border-color: -fx-gray-100;
}

.table-view .table-row-cell:selected {
    -fx-background-color: -fx-primary-50;
    -fx-text-fill: -fx-primary-700;
}

.table-view .table-row-cell:hover {
    -fx-background-color: #f9fafb;
}

/* Form Controls */
.combo-box, .text-field, .spinner {
    -fx-background-color: white;
    -fx-border-color: #d1d5db;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 8px 12px;
}

.combo-box:focused, .text-field:focused, .spinner:focused {
    -fx-border-color: #007bff;
    -fx-effect: dropshadow(gaussian, rgba(14, 165, 233, 0.2), 4, 0, 0, 0);
}

.check-box {
    -fx-text-fill: -fx-secondary-700;
}

.date-picker {
    -fx-background-color: white;
    -fx-border-color: #d1d5db;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
}

/* Recent Orders Section */
.recent-orders-section {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.05), 8, 0, 0, 2);
}

/* Menu Selection Styles */
.menu-header {
    -fx-background-color: #ffffff;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 0px 0px 1px 0px;
}

.top-nav {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
}

.restaurant-name {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
}

.table-info {
    -fx-text-fill: #e74c3c;
    -fx-font-weight: bold;
}

.order-tabs {
    -fx-background-color: #ffffff;
    -fx-padding: 0px;
}

.tab-button {
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #6c757d;
    -fx-font-weight: bold;
    -fx-padding: 12px 24px 12px 24px;
    -fx-background-radius: 0px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 1px 1px 0px;
    -fx-cursor: hand;
}

.tab-button:hover {
    -fx-background-color: #e9ecef;
}

.tab-button.tab-active {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
}

.search-section {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
}

.search-field {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
    -fx-padding: 8px 12px 8px 12px;
}

.search-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-font-size: 14px;
    -fx-padding: 8px 12px 8px 12px;
    -fx-background-radius: 5px;
    -fx-border-radius: 5px;
    -fx-cursor: hand;
}

.search-button:hover {
    -fx-background-color: #0056b3;
}

.categories-panel {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 1px 0px 0px;
}

.categories-title {
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
    -fx-padding: 10px 0px 10px 0px;
}

.categories-list {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.categories-list .list-cell {
    -fx-background-color: transparent;
    -fx-text-fill: #495057;
    -fx-padding: 8px 12px 8px 12px;
    -fx-border-color: transparent;
}

.categories-list .list-cell:selected {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
}

.categories-list .list-cell:hover {
    -fx-background-color: #e9ecef;
}

.menu-scroll {
    -fx-background-color: white;
    -fx-border-color: transparent;
}

.menu-grid {
    -fx-background-color: white;
    -fx-hgap: 20px;
    -fx-vgap: 20px;
}

.menu-item-card {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 15px;
    -fx-alignment: center;
    -fx-spacing: 8;
    -fx-effect: dropshadow(three-pass-box, #1a1a1a, 5, 0, 0, 2);
    -fx-min-width: 160px;
    -fx-pref-width: 180px;
    -fx-max-width: 200px;
}

.menu-item-card:hover {
    -fx-effect: dropshadow(three-pass-box, #262626, 8, 0, 0, 3);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.item-name {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
    -fx-text-alignment: center;
    -fx-alignment: center;
}

.item-price {
    -fx-text-fill: #e74c3c;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
    -fx-text-alignment: center;
    -fx-alignment: center;
}

.add-button {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 6px 20px 6px 20px;
    -fx-background-radius: 5px;
    -fx-border-radius: 5px;
    -fx-cursor: hand;
}

.add-button:hover {
    -fx-background-color: #218838;
}

.order-panel {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 0px 1px;
}

.order-header {
    -fx-background-color: #ffffff;
    -fx-padding: 15px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
}

.order-title {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
}

.item-count {
    -fx-text-fill: #6c757d;
    -fx-font-size: 12px;
}

.order-scroll {
    -fx-background-color: white;
    -fx-border-color: transparent;
}

.order-items {
    -fx-background-color: white;
    -fx-padding: 10px;
}

/* Action Buttons Container */
.action-buttons {
    -fx-background-color: #f8f9fa;
    -fx-padding: 12px;
    -fx-spacing: 8;
    -fx-alignment: center;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px 0px 0px 0px;
}

.action-buttons .button {
    -fx-min-width: 120px;
    -fx-pref-width: 140px;
    -fx-max-width: 160px;
    -fx-min-height: 32px;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

.empty-order {
    -fx-text-fill: #6c757d;
    -fx-font-style: italic;
    -fx-text-alignment: center;
    -fx-alignment: center;
    -fx-padding: 20px;
}

.order-item-row {
    -fx-background-color: #f8f9fa;
    -fx-padding: 8px;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
    -fx-alignment: center-left;
}

.quantity-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 2px 8px 2px 8px;
    -fx-background-radius: 3px;
    -fx-border-radius: 3px;
    -fx-cursor: hand;
    -fx-min-width: 25px;
    -fx-max-width: 25px;
}

.quantity-button:hover {
    -fx-background-color: #5a6268;
}

.order-summary {
    -fx-background-color: white;
    -fx-padding: 15px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px 0px 0px 0px;
}

.total-label {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
}

.total-amount {
    -fx-text-fill: #e74c3c;
    -fx-font-weight: bold;
}

.discount-amount {
    -fx-text-fill: #e74c3c;
    -fx-font-weight: bold;
}

.discount-button {
    -fx-background-color: #ffc107;
    -fx-text-fill: #212529;
    -fx-font-weight: bold;
    -fx-padding: 4px 8px;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-cursor: hand;
    -fx-font-size: 10px;
}

.discount-button:hover {
    -fx-background-color: #e0a800;
}

.action-buttons {
    -fx-padding: 15px;
    -fx-spacing: 10;
}

.save-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 6px 12px;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-cursor: hand;
    -fx-font-size: 11px;
}

.save-button:hover {
    -fx-background-color: #0056b3;
}

.kot-button {
    -fx-background-color: #ffc107;
    -fx-text-fill: #212529;
    -fx-font-weight: bold;
    -fx-padding: 6px 12px;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-cursor: hand;
    -fx-font-size: 11px;
}

.kot-button:hover {
    -fx-background-color: #e0a800;
}

.bill-button {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 6px 12px;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-cursor: hand;
    -fx-font-size: 11px;
}

.bill-button:hover {
    -fx-background-color: #218838;
}

/* Inventory Management Styles */
.inventory-header {
    -fx-background-color: #ffffff;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 0px 0px 1px 0px;
}

.page-title {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
}

.page-subtitle {
    -fx-text-fill: #6c757d;
    -fx-font-size: 14px;
}

.add-item-button {
    -fx-background-color: #ff6b35;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 10px 20px 10px 20px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-cursor: hand;
}

.add-item-button:hover {
    -fx-background-color: #e55a2b;
}

.inventory-tabs {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
}

.inventory-scroll {
    -fx-background-color: #f8f9fa;
    -fx-border-color: transparent;
}

.inventory-container {
    -fx-background-color: #f8f9fa;
}

.inventory-item-card {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.08), 4, 0, 0, 2);
}

.inventory-item-card:hover {
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.12), 6, 0, 0, 3);
}

.inventory-icon {
    -fx-text-fill: #6c757d;
}

.item-name {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
}

.item-quantity {
    -fx-text-fill: #495057;
    -fx-font-size: 14px;
}

.item-time {
    -fx-text-fill: #6c757d;
    -fx-font-size: 12px;
}

.status-badge {
    -fx-padding: 4px 12px 4px 12px;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

.status-in-stock {
    -fx-background-color: #d4edda;
    -fx-text-fill: #155724;
}

.status-low-stock {
    -fx-background-color: #fff3cd;
    -fx-text-fill: #856404;
}

.status-out-stock {
    -fx-background-color: #f8d7da;
    -fx-text-fill: #721c24;
}

.action-button {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 6px 8px 6px 8px;
    -fx-cursor: hand;
    -fx-font-size: 12px;
    -fx-min-width: 30px;
    -fx-pref-width: 30px;
    -fx-max-width: 30px;
    -fx-min-height: 30px;
    -fx-pref-height: 30px;
    -fx-max-height: 30px;
}

.action-button:hover {
    -fx-background-color: #e9ecef;
}

/* Specific action button styles */
.edit-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
}

.edit-button:hover {
    -fx-background-color: #0056b3;
}

.delete-button {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
}

.delete-button:hover {
    -fx-background-color: #c82333;
}

.reset-button {
    -fx-background-color: #ffc107;
    -fx-text-fill: #212529;
}

.reset-button:hover {
    -fx-background-color: #e0a800;
}

/* Compact action buttons for table cells */
.action-button-small {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-padding: 2px 4px 2px 4px;
    -fx-cursor: hand;
    -fx-font-size: 10px;
    -fx-min-width: 24px;
    -fx-pref-width: 24px;
    -fx-max-width: 24px;
    -fx-min-height: 24px;
    -fx-pref-height: 24px;
    -fx-max-height: 24px;
}

.action-button-small:hover {
    -fx-background-color: #e9ecef;
}

/* Override specific colors for small buttons */
.action-button-small.edit-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
}

.action-button-small.edit-button:hover {
    -fx-background-color: #0056b3;
}

.action-button-small.delete-button {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
}

.action-button-small.delete-button:hover {
    -fx-background-color: #c82333;
}

.action-button-small.reset-button {
    -fx-background-color: #ffc107;
    -fx-text-fill: #212529;
}

.action-button-small.reset-button:hover {
    -fx-background-color: #e0a800;
}

/* Table action buttons for User Management */
.table-action-button {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-padding: 4px 8px;
    -fx-cursor: hand;
    -fx-font-size: 11px;
    -fx-font-weight: normal;
    -fx-min-width: 50px;
    -fx-pref-width: 50px;
    -fx-max-width: 50px;
    -fx-min-height: 26px;
    -fx-pref-height: 26px;
    -fx-max-height: 26px;
}

.table-action-button:hover {
    -fx-background-color: #e9ecef;
}

.table-action-button.edit-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
}

.table-action-button.edit-button:hover {
    -fx-background-color: #0056b3;
}

.table-action-button.delete-button {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
}

.table-action-button.delete-button:hover {
    -fx-background-color: #c82333;
}

/* Table styling with visible borders */
.users-table {
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-background-color: white;
}

.users-table .table-view {
    -fx-table-cell-border-color: #dee2e6;
    -fx-border-color: #dee2e6;
}

.users-table .column-header {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 1px 1px 0;
    -fx-padding: 8px;
}

.users-table .table-cell {
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 1px 1px 0;
    -fx-padding: 6px 8px;
    -fx-background-color: white;
}

.users-table .table-row-cell {
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1px 0;
}

.users-table .table-row-cell:selected {
    -fx-background-color: #e3f2fd;
}

.users-table .table-row-cell:hover {
    -fx-background-color: #f5f5f5;
}

/* Order Management action buttons */
.order-action-button {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-padding: 4px 8px;
    -fx-cursor: hand;
    -fx-font-size: 11px;
    -fx-font-weight: bold;
    -fx-min-width: 50px;
    -fx-pref-width: 50px;
    -fx-max-width: 50px;
    -fx-min-height: 26px;
    -fx-pref-height: 26px;
    -fx-max-height: 26px;
}

.order-action-button:hover {
    -fx-background-color: #e9ecef;
}

.order-action-button.view-button {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
}

.order-action-button.view-button:hover {
    -fx-background-color: #218838;
}

.order-action-button.edit-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
}

.order-action-button.edit-button:hover {
    -fx-background-color: #0056b3;
}

/* Orders table styling improvements */
.orders-table {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
}

.orders-table .column-header {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 1px 1px 0;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-padding: 8px;
}

.orders-table .table-cell {
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 1px 1px 0;
    -fx-padding: 6px 8px;
    -fx-text-fill: #495057;
}

.orders-table .table-row-cell {
    -fx-background-color: white;
}

.orders-table .table-row-cell:odd {
    -fx-background-color: #f8f9fa;
}

.orders-table .table-row-cell:selected {
    -fx-background-color: #e3f2fd;
}

.orders-table .table-row-cell:hover {
    -fx-background-color: #f5f5f5;
}

.alert-card {
    -fx-border-color: #ffc107;
    -fx-border-width: 2px;
}

.empty-state {
    -fx-text-fill: #6c757d;
    -fx-font-style: italic;
    -fx-text-alignment: center;
    -fx-alignment: center;
    -fx-padding: 40px;
}

/* Staff Record Management Styles */
.staff-record-button {
    -fx-background-color: #17a2b8;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 10px 20px 10px 20px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-cursor: hand;
    -fx-margin: 0 10 0 0;
}

.staff-record-button:hover {
    -fx-background-color: #138496;
}

/* Staff Record Table Styles */
.staff-record-table {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
}

.staff-record-table .column-header {
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
    -fx-padding: 12px 8px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1px 0;
}

.staff-record-table .table-row-cell {
    -fx-background-color: white;
    -fx-border-color: #f8f9fa;
    -fx-border-width: 0 0 1px 0;
    -fx-padding: 8px;
}

.staff-record-table .table-row-cell:hover {
    -fx-background-color: #f8f9fa;
}

.staff-record-table .table-row-cell:selected {
    -fx-background-color: #e3f2fd;
    -fx-text-fill: #1976d2;
}

/* Staff Form Styles */
.section-title {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-padding: 10px 0 5px 0;
}

.form-scroll {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.form-container {
    -fx-background-color: white;
    -fx-padding: 15px;
}

.action-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-padding: 4px 8px;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-cursor: hand;
    -fx-min-width: 30px;
    -fx-pref-width: 30px;
    -fx-max-width: 30px;
}

.action-button:hover {
    -fx-background-color: #5a6268;
}

/* Improved Menu Management Button Styles */
.action-buttons-container {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px 0 0 0;
    -fx-border-radius: 0px;
    -fx-background-radius: 0px;
}

.primary-action-button {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #28a745, 4, 0, 0, 2);
}

.primary-action-button:hover {
    -fx-background-color: #218838;
    -fx-effect: dropshadow(gaussian, #28a745, 6, 0, 0, 3);
}

.secondary-action-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #007bff, 4, 0, 0, 2);
}

.secondary-action-button:hover {
    -fx-background-color: #0056b3;
    -fx-effect: dropshadow(gaussian, #007bff, 6, 0, 0, 3);
}

.tertiary-action-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(108, 117, 125, 0.3), 4, 0, 0, 2);
}

.tertiary-action-button:hover {
    -fx-background-color: #545b62;
    -fx-effect: dropshadow(gaussian, rgba(108, 117, 125, 0.4), 6, 0, 0, 3);
}

/* Improved Notification Panel Button Styles */
.notification-action-btn {
    -fx-background-color: linear-gradient(to bottom, #20C997, #17A2B8);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-font-size: 12px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-cursor: hand;
    -fx-border-color: #17A2B8;
    -fx-border-width: 1px;
    -fx-effect: dropshadow(gaussian, rgba(32, 201, 151, 0.4), 4, 0, 0, 2);
}

.notification-action-btn:hover {
    -fx-background-color: linear-gradient(to bottom, #17A2B8, #138496);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
    -fx-effect: dropshadow(gaussian, rgba(32, 201, 151, 0.6), 6, 0, 0, 3);
}

.notification-action-btn-danger {
    -fx-background-color: linear-gradient(to bottom, #EF4444, #DC2626);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-font-size: 12px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-cursor: hand;
    -fx-border-color: #DC2626;
    -fx-border-width: 1px;
    -fx-effect: dropshadow(gaussian, rgba(239, 68, 68, 0.4), 4, 0, 0, 2);
}

.notification-action-btn-danger:hover {
    -fx-background-color: linear-gradient(to bottom, #DC2626, #B91C1C);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
    -fx-effect: dropshadow(gaussian, rgba(239, 68, 68, 0.6), 6, 0, 0, 3);
}

.quick-actions-grid {
    -fx-background-color: #ffffff;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1px 0;
    -fx-alignment: center;
}

.notification-quick-btn {
    -fx-background-color: linear-gradient(to bottom, #FFFFFF, #F8F9FA);
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
    -fx-font-size: 11px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-border-color: #DEE2E6;
    -fx-border-width: 1px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 3, 0, 0, 1);
}

.notification-quick-btn:hover {
    -fx-background-color: linear-gradient(to bottom, #E9ECEF, #DEE2E6);
    -fx-border-color: #6C757D;
    -fx-text-fill: #212529;
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.15), 4, 0, 0, 2);
}

.notification-quick-btn:pressed {
    -fx-background-color: #DEE2E6;
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

/* Modern Inventory Management Styles */
.modern-inventory-root {
    -fx-background-color: #f8f9fa;
}

.modern-inventory-header {
    -fx-background-color: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);
    -fx-border-color: #e9ecef;
    -fx-border-width: 0 0 2px 0;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 8, 0, 0, 2);
}

.header-main-row {
    -fx-background-color: #ffffff;
}

.header-filter-row {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px 0 0 0;
}

.modern-page-title {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 2, 0, 0, 1);
}

.modern-page-subtitle {
    -fx-text-fill: #6c757d;
    -fx-font-size: 14px;
}

.modern-back-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 12px 20px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(108, 117, 125, 0.3), 4, 0, 0, 2);
}

.modern-back-button:hover {
    -fx-background-color: #5a6268;
    -fx-effect: dropshadow(gaussian, rgba(108, 117, 125, 0.4), 6, 0, 0, 3);
}

.modern-refresh-button {
    -fx-background-color: #17a2b8;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 12px 18px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(23, 162, 184, 0.3), 4, 0, 0, 2);
}

.modern-refresh-button:hover {
    -fx-background-color: #138496;
    -fx-effect: dropshadow(gaussian, rgba(23, 162, 184, 0.4), 6, 0, 0, 3);
}

.modern-add-button {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 12px 20px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #28a745, 4, 0, 0, 2);
}

.modern-add-button:hover {
    -fx-background-color: #218838;
    -fx-effect: dropshadow(gaussian, #28a745, 6, 0, 0, 3);
}

.search-container {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 8px 12px;
}

.search-icon {
    -fx-text-fill: #6c757d;
    -fx-font-size: 16px;
}

.modern-search-field {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-text-fill: #495057;
    -fx-prompt-text-fill: #6c757d;
    -fx-font-size: 14px;
}

.modern-search-field:focused {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.modern-filter-combo {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-text-fill: #495057;
    -fx-prompt-text-fill: #6c757d;
    -fx-font-size: 14px;
    -fx-cursor: hand;
}

.modern-filter-combo:hover {
    -fx-border-color: #adb5bd;
}

.modern-filter-combo:focused {
    -fx-border-color: #80bdff;
    -fx-effect: dropshadow(gaussian, #007bff, 4, 0, 0, 0);
}

.item-count-label {
    -fx-text-fill: #6c757d;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
}

/* Modern Tab Styles */
.modern-inventory-tabs {
    -fx-background-color: #ffffff;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px 0 0 0;
}

.modern-tab-button {
    -fx-background-color: transparent;
    -fx-text-fill: #6c757d;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
    -fx-padding: 15px 25px;
    -fx-border-color: transparent;
    -fx-border-width: 0 0 3px 0;
    -fx-cursor: hand;
}

.modern-tab-button:hover {
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #495057;
    -fx-border-color: #dee2e6;
}

.modern-tab-active {
    -fx-text-fill: #007bff !important;
    -fx-border-color: #007bff !important;
    -fx-background-color: #ffffff !important;
}

/* Content Area Styles */
.modern-content-area {
    -fx-background-color: #f8f9fa;
}

.modern-scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.modern-content-container {
    -fx-background-color: #f8f9fa;
}

.loading-container {
    -fx-background-color: rgba(248, 249, 250, 0.9);
}

.modern-progress {
    -fx-progress-color: #007bff;
}

.loading-text {
    -fx-text-fill: #6c757d;
    -fx-font-size: 16px;
    -fx-font-weight: bold;
}

/* Stats Cards */
.stats-cards-container {
    -fx-background-color: #f8f9fa;
}

.stats-card {
    -fx-background-color: white;
    -fx-border-radius: 12px;
    -fx-background-radius: 12px;
    -fx-padding: 20px;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 8, 0, 0, 2);
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
}

.stats-card-primary {
    -fx-border-color: #007bff;
    -fx-border-width: 0 0 0 4px;
}

.stats-card-success {
    -fx-border-color: #28a745;
    -fx-border-width: 0 0 0 4px;
}

.stats-card-warning {
    -fx-border-color: #ffc107;
    -fx-border-width: 0 0 0 4px;
}

.stats-card-danger {
    -fx-border-color: #dc3545;
    -fx-border-width: 0 0 0 4px;
}

.stats-icon {
    -fx-font-size: 24px;
}

.stats-label {
    -fx-text-fill: #6c757d;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
}

.stats-value {
    -fx-text-fill: #2c3e50;
    -fx-font-size: 32px;
    -fx-font-weight: bold;
}

/* Modern Table Styles */
.modern-table-view {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 8, 0, 0, 2);
}

.modern-table-view .column-header {
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
    -fx-padding: 15px 10px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 0 0 2px 0;
}

.modern-table-view .table-row-cell {
    -fx-background-color: white;
    -fx-border-color: #f8f9fa;
    -fx-border-width: 0 0 1px 0;
    -fx-padding: 12px 10px;
}

.modern-table-view .table-row-cell:hover {
    -fx-background-color: #f8f9fa;
}

.modern-table-view .table-row-cell:selected {
    -fx-background-color: #e3f2fd;
    -fx-text-fill: #1976d2;
}

.action-button-edit {
    -fx-background-color: #17a2b8;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-padding: 6px 10px;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-cursor: hand;
    -fx-min-width: 32px;
    -fx-pref-width: 32px;
}

.action-button-edit:hover {
    -fx-background-color: #138496;
}

.action-button-delete {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-padding: 6px 10px;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-cursor: hand;
    -fx-min-width: 32px;
    -fx-pref-width: 32px;
}

.action-button-delete:hover {
    -fx-background-color: #c82333;
}

/* Modern Modal Dialog Styles */
.modern-modal-dialog {
    -fx-background-color: white;
    -fx-border-radius: 16px;
    -fx-background-radius: 16px;
    -fx-effect: dropshadow(gaussian, #4d4d4d, 20, 0, 0, 8);
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
}

.modal-header {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 0 0 1px 0;
    -fx-border-radius: 16px 16px 0 0;
    -fx-background-radius: 16px 16px 0 0;
    -fx-padding: 20px 25px;
}

.modal-title {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
}

.modal-close-button {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
    -fx-padding: 8px 12px;
    -fx-background-radius: 50%;
    -fx-border-radius: 50%;
    -fx-cursor: hand;
    -fx-min-width: 32px;
    -fx-pref-width: 32px;
    -fx-min-height: 32px;
    -fx-pref-height: 32px;
}

.modal-close-button:hover {
    -fx-background-color: #c82333;
}

.form-scroll {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.form-container {
    -fx-background-color: white;
    -fx-padding: 20px;
}

.section-title {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-padding: 0 0 10px 0;
}

.field-label {
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
    -fx-font-size: 14px;
}

.modern-form-field {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 12px 15px;
    -fx-text-fill: #495057;
    -fx-prompt-text-fill: #6c757d;
    -fx-font-size: 14px;
}

.modern-form-field:focused {
    -fx-border-color: #80bdff;
    -fx-effect: dropshadow(gaussian, #007bff, 4, 0, 0, 0);
}

.modal-footer {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px 0 0 0;
    -fx-border-radius: 0 0 16px 16px;
    -fx-background-radius: 0 0 16px 16px;
    -fx-padding: 20px 25px;
}

.modern-primary-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 12px 24px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #007bff, 4, 0, 0, 2);
}

.modern-primary-button:hover {
    -fx-background-color: #0056b3;
    -fx-effect: dropshadow(gaussian, #007bff, 6, 0, 0, 3);
}

.modern-secondary-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 12px 24px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(108, 117, 125, 0.3), 4, 0, 0, 2);
}

.modern-secondary-button:hover {
    -fx-background-color: #5a6268;
    -fx-effect: dropshadow(gaussian, rgba(108, 117, 125, 0.4), 6, 0, 0, 3);
}

/* Responsive Design for Mobile and Tablet */
@media screen and (max-width: 768px) {
    .action-buttons-container .primary-action-button,
    .action-buttons-container .secondary-action-button,
    .action-buttons-container .tertiary-action-button {
        -fx-pref-width: 180px;
        -fx-max-width: 180px;
        -fx-min-width: 180px;
        -fx-font-size: 12px;
        -fx-pref-height: 40px;
    }

    .back-button {
        -fx-pref-width: 80px;
        -fx-min-width: 80px;
        -fx-font-size: 12px;
        -fx-padding: 8px 14px;
    }

    .notification-quick-btn {
        -fx-pref-width: 75px;
        -fx-font-size: 10px;
        -fx-pref-height: 32px;
    }

    .notification-action-btn {
        -fx-pref-width: 110px;
        -fx-font-size: 11px;
        -fx-pref-height: 32px;
    }

    .notification-action-btn-danger {
        -fx-pref-width: 85px;
        -fx-font-size: 11px;
        -fx-pref-height: 32px;
    }
}

@media screen and (max-width: 480px) {
    .action-buttons-container {
        -fx-padding: 15px 10px;
    }

    .action-buttons-container .primary-action-button,
    .action-buttons-container .secondary-action-button,
    .action-buttons-container .tertiary-action-button {
        -fx-pref-width: 160px;
        -fx-max-width: 160px;
        -fx-min-width: 160px;
        -fx-font-size: 11px;
        -fx-pref-height: 38px;
    }

    .quick-actions-grid {
        -fx-hgap: 6px;
        -fx-vgap: 6px;
    }

    .notification-quick-btn {
        -fx-pref-width: 65px;
        -fx-font-size: 9px;
        -fx-pref-height: 30px;
    }
}

.filter-section {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
}

.date-picker {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
}

.filter-combo {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
}

.refresh-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 8px 16px 8px 16px;
    -fx-background-radius: 5px;
    -fx-border-radius: 5px;
    -fx-cursor: hand;
}

.refresh-button:hover {
    -fx-background-color: #5a6268;
}

.summary-section {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
}

.summary-card {
    -fx-background-color: white;
    -fx-border-radius: 12px;
    -fx-background-radius: 12px;
    -fx-padding: 20px;
    -fx-alignment: center;
    -fx-spacing: 8;
    -fx-effect: dropshadow(three-pass-box, #1a1a1a, 4, 0, 0, 2);
    -fx-min-width: 120px;
    -fx-pref-width: 120px;
}

.present-card {
    -fx-border-color: #28a745;
    -fx-border-width: 0px 0px 4px 0px;
}

.absent-card {
    -fx-border-color: #dc3545;
    -fx-border-width: 0px 0px 4px 0px;
}

.late-card {
    -fx-border-color: #ffc107;
    -fx-border-width: 0px 0px 4px 0px;
}

.total-card {
    -fx-border-color: #007bff;
    -fx-border-width: 0px 0px 4px 0px;
}

.summary-number {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
    -fx-text-alignment: center;
    -fx-alignment: center;
}

.summary-label {
    -fx-text-fill: #6c757d;
    -fx-font-size: 12px;
    -fx-text-alignment: center;
    -fx-alignment: center;
}

.table-section {
    -fx-background-color: white;
}

.attendance-table {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
}

.attendance-table .column-header {
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
}

.attendance-table .table-row-cell {
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
}

.attendance-table .table-row-cell:selected {
    -fx-background-color: #e3f2fd;
}

.edit-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-font-size: 11px;
    -fx-padding: 4px 8px 4px 8px;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-cursor: hand;
}

.edit-button:hover {
    -fx-background-color: #0056b3;
}

.delete-button {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-font-size: 11px;
    -fx-padding: 4px 8px 4px 8px;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-cursor: hand;
}

.delete-button:hover {
    -fx-background-color: #c82333;
}

.dialog-overlay {
    -fx-background-color: #999999;
    -fx-alignment: center;
    -fx-pref-width: 100%;
    -fx-pref-height: 100%;
    -fx-min-width: 100%;
    -fx-min-height: 100%;
    -fx-max-width: 100%;
    -fx-max-height: 100%;
}

.dialog-content {
    -fx-background-color: white;
    -fx-border-radius: 12px;
    -fx-background-radius: 12px;
    -fx-padding: 0px;
    -fx-spacing: 0;
    -fx-max-width: 500px;
    -fx-min-width: 400px;
    -fx-pref-width: 450px;
    -fx-effect: dropshadow(three-pass-box, #666666, 15, 0, 0, 8);
    -fx-border-color: #1a1a1a;
    -fx-border-width: 1px;
    -fx-alignment: center;
}

.dialog-header {
    -fx-background-color: #f8f9fa;
    -fx-padding: 15px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
    -fx-background-radius: 12px 12px 0px 0px;
}

.dialog-title {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
}

.close-button {
    -fx-background-color: transparent;
    -fx-text-fill: #6c757d;
    -fx-font-size: 16px;
    -fx-padding: 5px;
    -fx-cursor: hand;
}

.close-button:hover {
    -fx-background-color: #e9ecef;
    -fx-background-radius: 50%;
}

.dialog-body {
    -fx-padding: 20px;
}

.dialog-footer {
    -fx-background-color: #f8f9fa;
    -fx-padding: 15px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px 0px 0px 0px;
    -fx-background-radius: 0px 0px 12px 12px;
}

.cancel-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 8px 16px 8px 16px;
    -fx-background-radius: 5px;
    -fx-border-radius: 5px;
    -fx-cursor: hand;
}

.cancel-button:hover {
    -fx-background-color: #5a6268;
}

.save-button {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 8px 16px 8px 16px;
    -fx-background-radius: 5px;
    -fx-border-radius: 5px;
    -fx-cursor: hand;
}

.save-button:hover {
    -fx-background-color: #218838;
}

/* Menu Management Specific Styles */
.form-section-title {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
}

.form-field {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
    -fx-padding: 8px 12px 8px 12px;
}

.form-checkbox {
    -fx-text-fill: #495057;
}

.item-category {
    -fx-text-fill: #6c757d;
    -fx-font-size: 10px;
}

.status-available {
    -fx-background-color: #d4edda;
    -fx-text-fill: #155724;
    -fx-font-size: 10px;
    -fx-padding: 2px 6px 2px 6px;
    -fx-background-radius: 10px;
}

.status-unavailable {
    -fx-background-color: #f8d7da;
    -fx-text-fill: #721c24;
    -fx-font-size: 10px;
    -fx-padding: 2px 6px 2px 6px;
    -fx-background-radius: 10px;
}

/* Billing Table View Styles */
.table-view-section {
    -fx-background-color: white;
    -fx-background-radius: 8px;
    -fx-border-color: #e9ecef;
    -fx-border-radius: 8px;
    -fx-border-width: 1px;
}

.table-view-header {
    -fx-padding: 15px 20px 10px 20px;
    -fx-border-color: transparent transparent #e9ecef transparent;
    -fx-border-width: 0 0 1px 0;
}

.table-legend {
    -fx-padding: 10px 20px;
    -fx-background-color: #f8f9fa;
}

.floor-section {
    -fx-padding: 15px 20px;
}

.floor-title {
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
}

.tables-grid {
    -fx-padding: 10px 0;
}

/* Table Button Styles */
.blank-table {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 2px;
    -fx-border-style: dashed;
    -fx-text-fill: #6c757d;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-alignment: center;
}

.blank-table:hover {
    -fx-background-color: #e9ecef;
    -fx-border-color: #adb5bd;
}

.running-table {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-border-color: #0056b3;
    -fx-border-width: 2px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-font-size: 10px;
    -fx-font-weight: bold;
    -fx-text-alignment: center;
    -fx-line-spacing: 3px;
}

.running-table:hover {
    -fx-background-color: #0056b3;
}

.printed-table {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-border-color: #1e7e34;
    -fx-border-width: 2px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-font-size: 10px;
    -fx-font-weight: bold;
    -fx-text-alignment: center;
    -fx-line-spacing: 3px;
}

.printed-table:hover {
    -fx-background-color: #1e7e34;
}

.paid-table {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-border-color: #495057;
    -fx-border-width: 2px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-font-size: 9px;
    -fx-font-weight: bold;
    -fx-text-alignment: center;
    -fx-line-spacing: 2px;
}

.paid-table:hover {
    -fx-background-color: #495057;
}

.running-kot-table {
    -fx-background-color: #ffc107;
    -fx-text-fill: #212529;
    -fx-border-color: #d39e00;
    -fx-border-width: 2px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-font-size: 10px;
    -fx-font-weight: bold;
    -fx-text-alignment: center;
    -fx-line-spacing: 3px;
}

.running-kot-table:hover {
    -fx-background-color: #d39e00;
}

/* Legend Dots */
.legend-dot {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-alignment: center;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
}

.legend-dot.blank-table {
    -fx-text-fill: #6c757d;
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 2px;
    -fx-border-style: dashed;
}

.legend-dot.running-table {
    -fx-text-fill: white;
    -fx-background-color: #007bff;
    -fx-border-color: #0056b3;
    -fx-border-width: 2px;
}

.legend-dot.printed-table {
    -fx-text-fill: white;
    -fx-background-color: #28a745;
    -fx-border-color: #1e7e34;
    -fx-border-width: 2px;
}

.legend-dot.paid-table {
    -fx-text-fill: white;
    -fx-background-color: #6c757d;
    -fx-border-color: #495057;
    -fx-border-width: 2px;
}

.legend-dot.running-kot-table {
    -fx-text-fill: #212529;
    -fx-background-color: #ffc107;
    -fx-border-color: #d39e00;
    -fx-border-width: 2px;
}

/* Action Buttons */
.delivery-button {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 8px 16px;
    -fx-font-weight: bold;
}

.delivery-button:hover {
    -fx-background-color: #c82333;
}

.pickup-button {
    -fx-background-color: #fd7e14;
    -fx-text-fill: white;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 8px 16px;
    -fx-font-weight: bold;
}

.pickup-button:hover {
    -fx-background-color: #e8650e;
}

.add-button {
    -fx-background-color: #17a2b8;
    -fx-text-fill: white;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 8px 16px;
    -fx-font-weight: bold;
}

.add-button:hover {
    -fx-background-color: #138496;
}

/* Order Details Section Styles */
.order-details-section {
    -fx-background-color: #ffffff;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-padding: 20px;
    -fx-spacing: 15;
}

.order-details-header {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 8px;
    -fx-padding: 15px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
}

.order-info-section {
    -fx-spacing: 20;
}

.order-info-left, .order-info-right {
    -fx-spacing: 10;
    -fx-padding: 15px;
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 8px;
}

.subsection-title {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.info-label {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-min-width: 100px;
}

.info-value {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-text-fill: #212529;
}

.order-items-section {
    -fx-spacing: 10;
}

.order-summary-section {
    -fx-spacing: 20;
}

.summary-label {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-min-width: 120px;
}

.summary-value {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-text-fill: #212529;
}

.grand-total-label {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-min-width: 120px;
}

.grand-total-value {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #28a745;
}

.billing-actions {
    -fx-spacing: 15;
    -fx-padding: 15px;
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 8px;
}

.apply-button {
    -fx-background-color: #17a2b8;
    -fx-text-fill: white;
    -fx-background-radius: 6px;
    -fx-padding: 5px 10px;
    -fx-font-size: 11px;
}

.apply-button:hover {
    -fx-background-color: #138496;
}

.success-button {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-background-radius: 6px;
    -fx-padding: 10px 20px;
    -fx-font-size: 13px;
    -fx-font-weight: bold;
}

.success-button:hover {
    -fx-background-color: #218838;
}

/* Description Box Styles for Settings */
.description-box {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
}

.description-title {
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
}

.description-text {
    -fx-text-fill: #6c757d;
    -fx-wrap-text: true;
}

.description-list {
    -fx-padding: 5px 0px 0px 15px;
}

.description-item {
    -fx-text-fill: #6c757d;
    -fx-font-size: 11px;
}

.info-section {
    -fx-padding: 10px 0px;
}

/* Notification Panel Styles */
.notification-panel {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
}

.notification-header {
    -fx-background-color: linear-gradient(to bottom, #ffffff, #f8f9fa);
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
}

.notification-title {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
}

.unread-count {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-background-radius: 10px;
    -fx-padding: 2px 6px 2px 6px;
    -fx-font-size: 10px;
    -fx-font-weight: bold;
}

.action-btn {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-background-radius: 4px;
    -fx-padding: 4px 8px 4px 8px;
    -fx-font-size: 10px;
}

.action-btn:hover {
    -fx-background-color: #5a6268;
}

.quick-actions {
    -fx-background-color: #ffffff;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
}

.quick-action-btn {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-background-radius: 6px;
    -fx-padding: 6px 12px 6px 12px;
    -fx-font-size: 11px;
    -fx-font-weight: bold;
}

.quick-action-btn:hover {
    -fx-background-color: #0056b3;
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.filter-tabs {
    -fx-background-color: #ffffff;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
}

.filter-tab {
    -fx-background-color: transparent;
    -fx-text-fill: #6c757d;
    -fx-border-color: transparent;
    -fx-background-radius: 4px;
    -fx-padding: 4px 8px 4px 8px;
    -fx-font-size: 10px;
}

.filter-tab:selected {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
}

.filter-tab:hover {
    -fx-background-color: #e9ecef;
}

.notification-container {
    -fx-background-color: #ffffff;
    -fx-min-height: 1000px;
    -fx-pref-height: 1000px;
}

/* Universal ScrollPane Styles - Enhanced for Proper Movement */
.universal-scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-focus-color: transparent;
    -fx-faint-focus-color: transparent;
    -fx-fit-to-width: true;
    -fx-hbar-policy: never;
    -fx-vbar-policy: always;
}

.universal-scroll-pane .viewport {
    -fx-background-color: transparent;
}

.universal-scroll-pane .content {
    -fx-background-color: transparent;
}

/* Enhanced Scroll Bar Styling for Better Visibility and Functionality */
.universal-scroll-pane .scroll-bar:vertical {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-pref-width: 14px;
    -fx-max-width: 14px;
    -fx-min-width: 14px;
    -fx-opacity: 1.0;
}

.universal-scroll-pane .scroll-bar:vertical .track {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 6px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
}

.universal-scroll-pane .scroll-bar:vertical .thumb {
    -fx-background-color: #6c757d;
    -fx-background-radius: 5px;
    -fx-border-radius: 5px;
}

.universal-scroll-pane .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #495057;
}

.universal-scroll-pane .scroll-bar:vertical .thumb:pressed {
    -fx-background-color: #343a40;
}

/* Improved scroll pane base styling */
.universal-scroll-pane {
    -fx-background-color: transparent;
    -fx-fit-to-width: true;
    -fx-hbar-policy: never;
    -fx-vbar-policy: as-needed;
}

.universal-scroll-pane .viewport {
    -fx-background-color: transparent;
}

/* Modern scroll bar for all scroll panes */
.scroll-pane .scroll-bar:vertical {
    -fx-background-color: #f8f9fa;
    -fx-pref-width: 14px;
    -fx-max-width: 14px;
    -fx-min-width: 14px;
    -fx-background-radius: 6px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
}

.scroll-pane .scroll-bar:vertical .track {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 6px;
}

.scroll-pane .scroll-bar:vertical .thumb {
    -fx-background-color: #6c757d;
    -fx-background-radius: 5px;
}

.scroll-pane .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #495057;
}

.scroll-pane .scroll-bar:vertical .thumb:pressed {
    -fx-background-color: #343a40;
}

.universal-scroll-pane .scroll-bar:vertical .track {
    -fx-background-color: #f0f0f0;
    -fx-background-radius: 0px;
    -fx-border-color: transparent;
    -fx-background-insets: 0;
    -fx-opacity: 1.0;
}

.universal-scroll-pane .scroll-bar:vertical .thumb {
    -fx-background-color: #c0c0c0;
    -fx-background-radius: 0px;
    -fx-border-color: transparent;
    -fx-background-insets: 0;
    -fx-opacity: 1.0;
}

.universal-scroll-pane .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #a0a0a0;
}

.universal-scroll-pane .scroll-bar:vertical .thumb:pressed {
    -fx-background-color: #808080;
}

/* Scroll bar buttons (up/down arrows) */
.universal-scroll-pane .scroll-bar:vertical .increment-button,
.universal-scroll-pane .scroll-bar:vertical .decrement-button {
    -fx-background-color: #f0f0f0;
    -fx-background-radius: 0px;
    -fx-border-color: transparent;
    -fx-background-insets: 0;
    -fx-pref-height: 12px;
    -fx-max-height: 12px;
    -fx-min-height: 12px;
}

.universal-scroll-pane .scroll-bar:vertical .increment-button:hover,
.universal-scroll-pane .scroll-bar:vertical .decrement-button:hover {
    -fx-background-color: #e0e0e0;
}

.universal-scroll-pane .scroll-bar:vertical .increment-button:pressed,
.universal-scroll-pane .scroll-bar:vertical .decrement-button:pressed {
    -fx-background-color: #d0d0d0;
}

.universal-scroll-pane .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #0056b3;
    -fx-opacity: 1.0;
}

.universal-scroll-pane .scroll-bar:vertical .thumb:pressed {
    -fx-background-color: #004085;
    -fx-opacity: 1.0;
}

.universal-scroll-pane .scroll-bar:vertical .increment-button,
.universal-scroll-pane .scroll-bar:vertical .decrement-button {
    -fx-background-color: #6c757d;
    -fx-background-radius: 6px;
    -fx-padding: 4px;
    -fx-pref-height: 18px;
    -fx-max-height: 18px;
    -fx-min-height: 18px;
    -fx-opacity: 1.0;
}

.universal-scroll-pane .scroll-bar:vertical .increment-button:hover,
.universal-scroll-pane .scroll-bar:vertical .decrement-button:hover {
    -fx-background-color: #495057;
    -fx-opacity: 1.0;
}

.universal-scroll-pane .scroll-bar:vertical .increment-arrow,
.universal-scroll-pane .scroll-bar:vertical .decrement-arrow {
    -fx-background-color: white;
    -fx-padding: 2px;
    -fx-opacity: 1.0;
}

.universal-scroll-pane .scroll-bar:vertical .increment-arrow {
    -fx-shape: "M 0 0 L 5 5 L 10 0 Z";
}

.universal-scroll-pane .scroll-bar:vertical .decrement-arrow {
    -fx-shape: "M 0 5 L 5 0 L 10 5 Z";
}

/* Force scroll bar to always be visible */
.universal-scroll-pane .scroll-bar:vertical:disabled {
    -fx-opacity: 1.0;
}

.universal-scroll-pane .scroll-bar:vertical .track:disabled {
    -fx-opacity: 1.0;
}

.universal-scroll-pane .scroll-bar:vertical .thumb:disabled {
    -fx-opacity: 0.5;
}

/* Modal Container Styles */
.modal-container {
    -fx-background-color: #ffffff;
    -fx-padding: 20px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-effect: dropshadow(gaussian, #4d4d4d, 10, 0, 0, 2);
}

/* Modal ScrollPane Styles */
.modal-scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-focus-color: transparent;
    -fx-faint-focus-color: transparent;
    -fx-fit-to-width: true;
    -fx-hbar-policy: never;
    -fx-vbar-policy: as-needed;
    -fx-max-height: 600px;
}

.modal-scroll-pane .viewport {
    -fx-background-color: transparent;
}

.modal-scroll-pane .content {
    -fx-background-color: transparent;
}

.modal-scroll-pane .scroll-bar:vertical {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-pref-width: 14px;
    -fx-max-width: 14px;
    -fx-min-width: 14px;
    -fx-opacity: 1.0;
}

.modal-scroll-pane .scroll-bar:vertical .track {
    -fx-background-color: #e9ecef;
    -fx-background-radius: 8px;
    -fx-border-color: transparent;
    -fx-background-insets: 2;
    -fx-opacity: 1.0;
}

.modal-scroll-pane .scroll-bar:vertical .thumb {
    -fx-background-color: #28a745;
    -fx-background-radius: 6px;
    -fx-border-color: transparent;
    -fx-background-insets: 1;
    -fx-opacity: 1.0;
    -fx-min-height: 30px;
}

.modal-scroll-pane .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #218838;
    -fx-opacity: 1.0;
}

.modal-scroll-pane .scroll-bar:vertical .thumb:pressed {
    -fx-background-color: #1e7e34;
    -fx-opacity: 1.0;
}

/* Modal Form Styles */
.modal-form {
    -fx-spacing: 15;
    -fx-padding: 20px;
    -fx-max-width: 500px;
    -fx-pref-width: 500px;
}

.modal-form .form-row {
    -fx-spacing: 10;
    -fx-alignment: center-left;
}

.modal-form .form-label {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-min-width: 120px;
    -fx-pref-width: 120px;
}

.modal-form .form-field {
    -fx-pref-width: 300px;
    -fx-max-width: 300px;
}

/* Modal Button Styles */
.modal-buttons {
    -fx-spacing: 10;
    -fx-alignment: center;
    -fx-padding: 20px 0px 0px 0px;
}

.modal-buttons .button {
    -fx-min-width: 100px;
    -fx-pref-width: 120px;
    -fx-min-height: 35px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
}

/* Notification ScrollPane Styles */
.notification-scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-focus-color: transparent;
    -fx-faint-focus-color: transparent;
}

.notification-scroll-pane .viewport {
    -fx-background-color: transparent;
}

.notification-scroll-pane .content {
    -fx-background-color: transparent;
}

.notification-scroll-pane .scroll-bar:vertical {
    -fx-background-color: #e9ecef;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-pref-width: 14px;
    -fx-max-width: 14px;
    -fx-min-width: 14px;
}

.notification-scroll-pane .scroll-bar:vertical .track {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 6px;
    -fx-border-color: transparent;
    -fx-background-insets: 1;
}

.notification-scroll-pane .scroll-bar:vertical .thumb {
    -fx-background-color: #6c757d;
    -fx-background-radius: 5px;
    -fx-border-color: transparent;
    -fx-background-insets: 2;
}

.notification-scroll-pane .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #495057;
}

.notification-scroll-pane .scroll-bar:vertical .thumb:pressed {
    -fx-background-color: #343a40;
}

.notification-scroll-pane .scroll-bar:vertical .increment-button,
.notification-scroll-pane .scroll-bar:vertical .decrement-button {
    -fx-background-color: #6c757d;
    -fx-background-radius: 4px;
    -fx-padding: 3px;
    -fx-pref-height: 16px;
    -fx-max-height: 16px;
    -fx-min-height: 16px;
}

.notification-scroll-pane .scroll-bar:vertical .increment-button:hover,
.notification-scroll-pane .scroll-bar:vertical .decrement-button:hover {
    -fx-background-color: #495057;
}

.notification-scroll-pane .scroll-bar:vertical .increment-arrow,
.notification-scroll-pane .scroll-bar:vertical .decrement-arrow {
    -fx-background-color: white;
    -fx-padding: 1px;
}

.notification-scroll-pane .scroll-bar:vertical .increment-arrow {
    -fx-shape: "M 0 0 L 4 4 L 8 0 Z";
}

.notification-scroll-pane .scroll-bar:vertical .decrement-arrow {
    -fx-shape: "M 0 4 L 4 0 L 8 4 Z";
}

.online-order-panel, .kot-panel, .scanner-panel {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px 0px 0px 0px;
}

.panel-title {
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
}

.platform-btn {
    -fx-background-radius: 6px;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 6px 12px 6px 12px;
}

.swiggy-btn {
    -fx-background-color: #fc8019;
}

.swiggy-btn:hover {
    -fx-background-color: #e6730f;
}

.zomato-btn {
    -fx-background-color: #e23744;
}

.zomato-btn:hover {
    -fx-background-color: #c8313c;
}

.uber-btn {
    -fx-background-color: #000000;
}

.uber-btn:hover {
    -fx-background-color: #333333;
}

.kot-action-btn, .scanner-action-btn {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-background-radius: 4px;
    -fx-padding: 4px 8px 4px 8px;
    -fx-font-size: 10px;
}

.kot-action-btn:hover, .scanner-action-btn:hover {
    -fx-background-color: #218838;
}

.status-label {
    -fx-text-fill: #28a745;
    -fx-font-weight: bold;
    -fx-font-size: 11px;
}

.status-bar {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px 0px 0px 0px;
}

.status-text {
    -fx-text-fill: #6c757d;
    -fx-font-size: 10px;
}

.refresh-btn {
    -fx-background-color: #17a2b8;
    -fx-text-fill: white;
    -fx-background-radius: 50%;
    -fx-padding: 4px 8px 4px 8px;
    -fx-font-size: 12px;
}

.refresh-btn:hover {
    -fx-background-color: #138496;
    -fx-rotate: 180;
}

/* Platform Configuration Styles */
.platform-selection-grid {
    -fx-padding: 20px;
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
}

.platform-radio {
    -fx-padding: 0px;
}

.platform-radio .radio-button {
    -fx-opacity: 0;
    -fx-max-width: 0;
    -fx-max-height: 0;
}

.platform-option {
    -fx-padding: 15px 20px;
    -fx-border-radius: 12px;
    -fx-background-radius: 12px;
    -fx-border-width: 2px;
    -fx-border-color: #e9ecef;
    -fx-background-color: white;
    -fx-cursor: hand;
    -fx-min-width: 120px;
    -fx-alignment: center;
}

.platform-option:hover {
    -fx-border-color: #007bff;
    -fx-background-color: #f8f9ff;
}

.platform-radio:selected .platform-option {
    -fx-border-color: #007bff;
    -fx-background-color: #e3f2fd;
}

.platform-icon {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
}

.platform-label {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

/* Platform-specific styles */
.swiggy-option {
    -fx-border-color: #fc8019;
}

.platform-radio:selected .swiggy-option {
    -fx-background-color: #fff3e0;
    -fx-border-color: #fc8019;
}

.zomato-option {
    -fx-border-color: #e23744;
}

.platform-radio:selected .zomato-option {
    -fx-background-color: #fce4ec;
    -fx-border-color: #e23744;
}

.zomato-icon {
    -fx-background-color: #e23744;
    -fx-text-fill: white;
    -fx-background-radius: 4px;
    -fx-padding: 4px 8px;
    -fx-font-size: 16px;
}

.dice-option {
    -fx-border-color: #6c757d;
}

.platform-radio:selected .dice-option {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #6c757d;
}

.deliveroo-option {
    -fx-border-color: #00ccbc;
}

.platform-radio:selected .deliveroo-option {
    -fx-background-color: #e0f2f1;
    -fx-border-color: #00ccbc;
}

.deliveroo-icon {
    -fx-background-color: #00ccbc;
    -fx-text-fill: white;
    -fx-background-radius: 4px;
    -fx-padding: 4px 8px;
    -fx-font-size: 16px;
}

.global-option {
    -fx-border-color: #17a2b8;
}

.platform-radio:selected .global-option {
    -fx-background-color: #e1f5fe;
    -fx-border-color: #17a2b8;
}

.talabat-option {
    -fx-border-color: #ff5722;
}

.platform-radio:selected .talabat-option {
    -fx-background-color: #fff3e0;
    -fx-border-color: #ff5722;
}

.talabat-icon {
    -fx-background-color: #ff5722;
    -fx-text-fill: white;
    -fx-background-radius: 4px;
    -fx-padding: 4px 8px;
    -fx-font-size: 16px;
}

/* Outlet List Styles */
.outlet-list {
    -fx-padding: 20px;
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
}

.outlet-checkbox {
    -fx-font-size: 14px;
    -fx-text-fill: #495057;
    -fx-padding: 8px 0px;
}

.outlet-checkbox .box {
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-width: 2px;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
}

.outlet-checkbox:selected .box {
    -fx-background-color: #007bff;
    -fx-border-color: #007bff;
}

.outlet-checkbox:selected .mark {
    -fx-background-color: white;
}

.outlet-checkbox:hover .box {
    -fx-border-color: #007bff;
}

/* Action Buttons */
.action-buttons {
    -fx-padding: 20px 0px;
}

.primary-button, .secondary-button, .danger-button {
    -fx-padding: 12px 24px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-cursor: hand;
}

.primary-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-border-color: #007bff;
}

.primary-button:hover {
    -fx-background-color: #0056b3;
    -fx-border-color: #0056b3;
}

.secondary-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-border-color: #6c757d;
}

.secondary-button:hover {
    -fx-background-color: #545b62;
    -fx-border-color: #545b62;
}

.danger-button {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-border-color: #dc3545;
}

.danger-button:hover {
    -fx-background-color: #c82333;
    -fx-border-color: #c82333;
}

/* Category Item Manager Styles */
.categories-sidebar {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 1px 0px 0px;
}

.sidebar-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.categories-list {
    -fx-background-color: transparent;
}

.category-button {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-padding: 12px 15px;
    -fx-alignment: center-left;
    -fx-cursor: hand;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
}

.category-button:hover {
    -fx-background-color: #e9ecef;
}

.category-button-active {
    -fx-background-color: #e3f2fd;
    -fx-border-color: #2196f3;
    -fx-border-width: 1px;
}

.items-content {
    -fx-background-color: white;
}

.content-header {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
}

.content-header .primary-button {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-border-color: #28a745;
    -fx-font-weight: bold;
    -fx-padding: 8px 16px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
}

.content-header .primary-button:hover {
    -fx-background-color: #218838;
    -fx-border-color: #218838;
}

.content-header .secondary-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-border-color: #6c757d;
    -fx-font-weight: bold;
    -fx-padding: 8px 16px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-margin: 0px 10px 0px 0px;
}

.content-header .secondary-button:hover {
    -fx-background-color: #5a6268;
    -fx-border-color: #5a6268;
}

.content-title {
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.table-header {
    -fx-background-color: #f1f3f4;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
}

.table-header-label {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-text-fill: #6c757d;
    -fx-text-transform: uppercase;
}

.items-list {
    -fx-background-color: white;
}

.item-row {
    -fx-background-color: white;
    -fx-border-color: #f1f3f4;
    -fx-border-width: 0px 0px 1px 0px;
    -fx-cursor: hand;
}

.item-row:hover {
    -fx-background-color: #f8f9fa;
}

.status-radio {
    -fx-padding: 0px;
}

.status-radio .radio-button {
    -fx-border-color: #ced4da;
    -fx-border-width: 2px;
    -fx-border-radius: 50%;
    -fx-background-radius: 50%;
    -fx-background-color: white;
}

.status-radio:selected .radio-button {
    -fx-border-color: #007bff;
    -fx-background-color: #007bff;
}

/* Filter Buttons */
.filter-btn {
    -fx-padding: 8px 16px;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-border-radius: 20px;
    -fx-background-radius: 20px;
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #6c757d;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-cursor: hand;
}

.filter-btn:hover {
    -fx-background-color: #e9ecef;
}

.filter-btn-active {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-border-color: #dc3545;
}

/* Toggle Switch Styles */
.toggle-switch {
    -fx-border-color: #ced4da;
    -fx-border-width: 1px;
    -fx-border-radius: 15px;
    -fx-background-radius: 15px;
    -fx-background-color: #f8f9fa;
}

.toggle-label {
    -fx-font-size: 11px;
    -fx-font-weight: bold;
    -fx-cursor: hand;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
}

.toggle-off {
    -fx-text-fill: #6c757d;
    -fx-background-color: transparent;
}

.toggle-on {
    -fx-text-fill: #6c757d;
    -fx-background-color: transparent;
}

.toggle-off.toggle-active {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
}

.toggle-on.toggle-active {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
}

/* Integrated Platform Manager Styles */
.platform-selection-section {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 2px 0px;
}

.platform-icons-container {
    -fx-background-color: white;
    -fx-border-radius: 15px;
    -fx-background-radius: 15px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);
}

.platform-item {
    -fx-cursor: hand;
    -fx-padding: 10px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
}

.platform-item:hover {
    -fx-background-color: #f8f9fa;
}

.platform-icon {
    -fx-pref-width: 60px;
    -fx-pref-height: 60px;
    -fx-min-width: 60px;
    -fx-min-height: 60px;
    -fx-max-width: 60px;
    -fx-max-height: 60px;
    -fx-border-radius: 12px;
    -fx-background-radius: 12px;
    -fx-alignment: center;
    -fx-border-width: 2px;
    -fx-border-color: transparent;
}

.swiggy-icon {
    -fx-background-color: #fc8019;
}

.zomato-icon {
    -fx-background-color: #e23744;
}

.talabat-icon {
    -fx-background-color: #8e44ad;
}

.global-icon {
    -fx-background-color: #3498db;
}

.platform-icon-text {
    -fx-font-size: 24px;
    -fx-text-fill: white;
    -fx-font-weight: bold;
}

.zomato-text {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

.talabat-text {
    -fx-font-size: 28px;
    -fx-font-weight: bold;
}

.platform-check {
    -fx-font-size: 16px;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-color: #28a745;
    -fx-border-radius: 50%;
    -fx-background-radius: 50%;
    -fx-pref-width: 20px;
    -fx-pref-height: 20px;
    -fx-min-width: 20px;
    -fx-min-height: 20px;
    -fx-max-width: 20px;
    -fx-max-height: 20px;
    -fx-alignment: center;
    -fx-translate-x: 20px;
    -fx-translate-y: -20px;
}

.restaurant-icon-section {
    -fx-background-color: #f8f9fa;
}

.restaurant-icon {
    -fx-font-size: 32px;
}

.category-manager-section {
    -fx-background-color: white;
}

.filter-buttons-container {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
}

/* Advanced Inventory Management Styles */
.inventory-section {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);
}

.section-header {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
}

.section-icon {
    -fx-font-size: 20px;
}

.section-title {
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
}

.inventory-table-container {
    -fx-background-color: white;
}

.inventory-table-header {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
}

.inventory-table-row {
    -fx-background-color: white;
    -fx-border-color: #f1f3f4;
    -fx-border-width: 0px 0px 1px 0px;
}

.inventory-table-row:hover,
.inventory-table-row-hover {
    -fx-background-color: #f8f9fa;
}

.table-header-label {
    -fx-font-weight: bold;
    -fx-text-fill: #6c757d;
    -fx-font-size: 12px;
}

.table-cell-label {
    -fx-text-fill: #495057;
    -fx-font-size: 14px;
}

.status-label {
    -fx-alignment: center;
    -fx-font-size: 11px;
    -fx-font-weight: bold;
}

.date-picker {
    -fx-pref-width: 140px;
    -fx-min-width: 140px;
}

.date-picker .text-field {
    -fx-pref-width: 120px;
    -fx-min-width: 120px;
}

/* Action Buttons */
.edit-button {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-font-size: 11px;
    -fx-padding: 4px 8px;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
    -fx-min-width: 50px;
}

.edit-button:hover {
    -fx-background-color: #218838;
}

.delete-button {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-font-size: 11px;
    -fx-padding: 4px 8px;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
    -fx-min-width: 50px;
}

.delete-button:hover {
    -fx-background-color: #c82333;
}

/* Legacy icon button support */
.icon-button {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-font-size: 14px;
    -fx-padding: 5px;
    -fx-cursor: hand;
}

.icon-button:hover {
    -fx-background-color: #f8f9fa;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
}

/* Location Diagram Styles */
.diagram-section {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px 0px 0px 0px;
}

.location-diagram {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 3, 0, 0, 1);
}

.location-node {
    -fx-background-color: #e3f2fd;
    -fx-border-color: #2196f3;
    -fx-border-width: 2px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 10px;
    -fx-min-width: 80px;
    -fx-alignment: center;
}

.location-node.central-kitchen {
    -fx-background-color: #e8f5e8;
    -fx-border-color: #4caf50;
}

.location-icon {
    -fx-font-size: 24px;
}

.location-name {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.connection-arrow {
    -fx-font-size: 20px;
    -fx-text-fill: #6c757d;
    -fx-font-weight: bold;
}

/* Compact Styles for Smaller Interface */
.date-picker-compact {
    -fx-pref-width: 130px;
    -fx-min-width: 130px;
    -fx-font-size: 12px;
}

.date-picker-compact .text-field {
    -fx-pref-width: 110px;
    -fx-min-width: 110px;
}

/* Additional date picker styling for better visibility */
.date-picker .text-field,
.date-picker-compact .text-field {
    -fx-padding: 6px 8px;
    -fx-font-size: 12px;
    -fx-border-color: #ced4da;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-background-color: white;
}

.date-picker .arrow-button,
.date-picker-compact .arrow-button {
    -fx-padding: 6px 8px;
    -fx-background-color: #f8f9fa;
    -fx-border-color: #ced4da;
    -fx-border-width: 0px 0px 0px 1px;
}

.date-picker .arrow-button:hover,
.date-picker-compact .arrow-button:hover {
    -fx-background-color: #e9ecef;
}

.primary-button-compact {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-padding: 6px 12px;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-cursor: hand;
}

.primary-button-compact:hover {
    -fx-background-color: #0056b3;
}

.secondary-button-compact {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-padding: 6px 12px;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-cursor: hand;
}

.secondary-button-compact:hover {
    -fx-background-color: #545b62;
}

.inventory-table-header {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
    -fx-padding: 8px 15px;
}

.inventory-table-row {
    -fx-background-color: white;
    -fx-border-color: #f1f3f4;
    -fx-border-width: 0px 0px 1px 0px;
    -fx-padding: 8px 15px;
}

.table-header-label {
    -fx-font-weight: bold;
    -fx-text-fill: #6c757d;
    -fx-font-size: 11px;
}

.table-cell-label {
    -fx-text-fill: #495057;
    -fx-font-size: 13px;
}

.status-label {
    -fx-alignment: center;
    -fx-font-size: 10px;
    -fx-font-weight: bold;
    -fx-padding: 3px 8px;
    -fx-background-radius: 10px;
}

/* Compact Location Diagram */
.location-node-compact {
    -fx-background-color: #e3f2fd;
    -fx-border-color: #2196f3;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 6px;
    -fx-min-width: 60px;
    -fx-alignment: center;
}

.location-node-compact.central-kitchen {
    -fx-background-color: #e8f5e8;
    -fx-border-color: #4caf50;
}

.location-icon-compact {
    -fx-font-size: 16px;
}

.location-name-compact {
    -fx-font-size: 10px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.connection-arrow-compact {
    -fx-font-size: 14px;
    -fx-text-fill: #6c757d;
    -fx-font-weight: bold;
}

.section-icon {
    -fx-font-size: 16px;
}

.page-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.page-subtitle {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
}

/* Back button for navigation */
.back-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-font-size: 13px;
    -fx-padding: 8px 16px;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
    -fx-min-width: 80px;
}

.back-button:hover {
    -fx-background-color: #5a6268;
}

/* Page header styling */
.page-header {
    -fx-spacing: 15px;
    -fx-alignment: CENTER_LEFT;
    -fx-padding: 10px 0px;
}

.page-header .back-button {
    -fx-margin: 0px 10px 0px 0px;
}

/* Edit Dialog Styles */
.dialog-pane {
    -fx-background-color: white;
}

.dialog-pane .header-panel {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
}

.dialog-pane .header-panel .label {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.dialog-pane .content {
    -fx-padding: 20px;
}

.dialog-pane .button-bar {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px 0px 0px 0px;
}

/* Form Field Styles */
.dialog-pane .text-field {
    -fx-pref-width: 200px;
    -fx-padding: 8px;
    -fx-border-color: #ced4da;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
}

.dialog-pane .text-field:focused {
    -fx-border-color: #007bff;
    -fx-effect: dropshadow(gaussian, rgba(0,123,255,0.25), 3, 0, 0, 0);
}

.dialog-pane .combo-box {
    -fx-pref-width: 200px;
}

.dialog-pane .label {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

/* Success Alert Styling */
.success-alert .header-panel {
    -fx-background-color: #d4edda;
    -fx-border-color: #c3e6cb;
}

.success-alert .header-panel .label {
    -fx-text-fill: #155724;
}

/* Validation Error Styling */
.text-field.error {
    -fx-border-color: #dc3545;
    -fx-effect: dropshadow(gaussian, rgba(220,53,69,0.25), 3, 0, 0, 0);
}

.combo-box.error {
    -fx-border-color: #dc3545;
}

/* Recipe Management Styles */
.recipe-cards-container {
    -fx-spacing: 20px;
    -fx-alignment: TOP_LEFT;
}

.recipe-card {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 20px;
    -fx-spacing: 15px;
    -fx-pref-width: 350px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

.recipe-card-header {
    -fx-spacing: 10px;
    -fx-alignment: CENTER_LEFT;
}

.recipe-card-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.add-ingredient-button {
    -fx-background-color: #e74c3c;
    -fx-text-fill: white;
    -fx-font-size: 11px;
    -fx-padding: 4px 8px;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.add-ingredient-button:hover {
    -fx-background-color: #c0392b;
}

.ingredients-header {
    -fx-spacing: 10px;
    -fx-padding: 8px 0px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
}

.ingredient-column-header {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-text-fill: #6c757d;
}

.ingredients-container {
    -fx-spacing: 2px;
}

.ingredient-row {
    -fx-spacing: 10px;
    -fx-padding: 8px 0px;
    -fx-border-color: #f8f9fa;
    -fx-border-width: 0px 0px 1px 0px;
}

.ingredient-row:hover {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 4px;
}

.ingredient-material {
    -fx-font-size: 13px;
    -fx-text-fill: #495057;
}

.ingredient-quantity {
    -fx-font-size: 13px;
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
}

.ingredient-unit {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
}

.convert-button-container {
    -fx-padding: 10px 0px 0px 0px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px 0px 0px 0px;
}

.convert-button {
    -fx-background-color: #495057;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-padding: 8px 16px;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.convert-button:hover {
    -fx-background-color: #343a40;
}

/* Multi-Stage Recipe Styles */
.multi-stage-section {
    -fx-spacing: 15px;
}

.section-title {
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.multi-stage-table {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

.multi-stage-header {
    -fx-spacing: 15px;
    -fx-padding: 15px 20px;
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
    -fx-background-radius: 8px 8px 0px 0px;
}

.multi-stage-column-header {
    -fx-font-size: 13px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.multi-stage-content {
    -fx-spacing: 0px;
}

.multi-stage-row {
    -fx-spacing: 15px;
    -fx-padding: 15px 20px;
    -fx-border-color: #f8f9fa;
    -fx-border-width: 0px 0px 1px 0px;
}

.multi-stage-row:hover {
    -fx-background-color: #f8f9fa;
}

.multi-stage-name {
    -fx-font-size: 14px;
    -fx-text-fill: #495057;
    -fx-font-weight: bold;
}

.auto-convert-checkbox {
    -fx-cursor: hand;
}

.auto-convert-checkbox .box {
    -fx-background-color: white;
    -fx-border-color: #28a745;
    -fx-border-width: 2px;
    -fx-border-radius: 3px;
    -fx-background-radius: 3px;
}

.auto-convert-checkbox:selected .mark {
    -fx-background-color: #28a745;
}

.auto-convert-checkbox:selected .box {
    -fx-background-color: #28a745;
}

/* Purchase Order Request Styles */
.purchase-request-form {
    -fx-background-color: #f8f5f0;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 25px;
    -fx-spacing: 20px;
}

.supplier-header {
    -fx-spacing: 10px;
    -fx-alignment: CENTER_LEFT;
}

.supplier-label {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.supplier-combo {
    -fx-pref-width: 200px;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
}

.item-request-section {
    -fx-spacing: 20px;
}

.request-form-grid {
    -fx-hgap: 15px;
    -fx-vgap: 15px;
}

.form-label {
    -fx-font-size: 13px;
    -fx-font-weight: bold;
    -fx-text-fill: #6c757d;
}

.item-combo {
    -fx-pref-width: 200px;
    -fx-font-size: 13px;
}

.quantity-field {
    -fx-pref-width: 120px;
    -fx-font-size: 13px;
    -fx-alignment: CENTER;
}

.unit-combo {
    -fx-pref-width: 100px;
    -fx-font-size: 13px;
}

.current-stock-section {
    -fx-spacing: 10px;
    -fx-alignment: CENTER_LEFT;
}

.current-stock-label {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
}

.current-stock-label.normal-stock {
    -fx-text-fill: #28a745;
}

.current-stock-label.low-stock {
    -fx-text-fill: #ffc107;
}

.current-stock-label.out-of-stock {
    -fx-text-fill: #dc3545;
}

.action-buttons-section {
    -fx-spacing: 15px;
    -fx-alignment: CENTER_LEFT;
}

.save-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-font-size: 13px;
    -fx-padding: 10px 20px;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
    -fx-min-width: 80px;
}

.save-button:hover {
    -fx-background-color: #5a6268;
}

.send-request-button {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-font-size: 13px;
    -fx-padding: 10px 20px;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
    -fx-min-width: 120px;
}

.send-request-button:hover {
    -fx-background-color: #c82333;
}

/* Purchase Orders Table Styles */
.purchase-orders-section {
    -fx-spacing: 15px;
}

.orders-table {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

.orders-table-header {
    -fx-spacing: 15px;
    -fx-padding: 15px;
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
    -fx-background-radius: 8px 8px 0px 0px;
}

.table-header-cell {
    -fx-font-size: 13px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.orders-table-content {
    -fx-spacing: 0px;
}

.order-row {
    -fx-spacing: 15px;
    -fx-padding: 12px 15px;
    -fx-border-color: #f8f9fa;
    -fx-border-width: 0px 0px 1px 0px;
}

.order-row:hover {
    -fx-background-color: #f8f9fa;
}

.order-cell {
    -fx-font-size: 13px;
    -fx-text-fill: #495057;
}

.status-label {
    -fx-font-weight: bold;
    -fx-padding: 4px 8px;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-alignment: CENTER;
}

.status-saved {
    -fx-background-color: #fff3cd;
    -fx-text-fill: #856404;
    -fx-border-color: #ffeaa7;
    -fx-border-width: 1px;
}

.status-processed {
    -fx-background-color: #cce5ff;
    -fx-text-fill: #004085;
    -fx-border-color: #99d6ff;
    -fx-border-width: 1px;
}

.status-delivered {
    -fx-background-color: #d4edda;
    -fx-text-fill: #155724;
    -fx-border-color: #c3e6cb;
    -fx-border-width: 1px;
}

.status-cancelled {
    -fx-background-color: #f8d7da;
    -fx-text-fill: #721c24;
    -fx-border-color: #f5c6cb;
    -fx-border-width: 1px;
}

/* Customer CRM Styles */
.crm-dashboard-section {
    -fx-spacing: 15px;
}

/* Customer Tags and Information Layout */
.customer-tags-layout {
    -fx-background-color: #f8f9fa;
    -fx-padding: 20;
    -fx-background-radius: 10;
    -fx-border-radius: 10;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1;
}

/* Tags Panel */
.tags-panel {
    -fx-background-color: white;
    -fx-padding: 20;
    -fx-background-radius: 8;
    -fx-border-radius: 8;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-min-width: 200;
    -fx-max-width: 200;
}

.tags-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #343a40;
}

.tags-list {
    -fx-spacing: 10;
}

/* Tag Buttons */
.tag-button {
    -fx-padding: 12 20;
    -fx-background-radius: 6;
    -fx-border-radius: 6;
    -fx-font-size: 14px;
    -fx-font-weight: 500;
    -fx-cursor: hand;
    -fx-min-width: 160;
    -fx-alignment: center-left;
}

.tag-vip {
    -fx-background-color: #fff3cd;
    -fx-text-fill: #856404;
    -fx-border-color: #ffeaa7;
    -fx-border-width: 1;
}

.tag-vip:hover {
    -fx-background-color: #ffeaa7;
}

.tag-corporate {
    -fx-background-color: #d1ecf1;
    -fx-text-fill: #0c5460;
    -fx-border-color: #bee5eb;
    -fx-border-width: 1;
}

.tag-corporate:hover {
    -fx-background-color: #bee5eb;
}

.tag-premium {
    -fx-background-color: #d4edda;
    -fx-text-fill: #155724;
    -fx-border-color: #c3e6cb;
    -fx-border-width: 1;
}

.tag-premium:hover {
    -fx-background-color: #c3e6cb;
}

.tag-add {
    -fx-background-color: transparent;
    -fx-text-fill: #6c757d;
    -fx-border-color: #ced4da;
    -fx-border-width: 2;
    -fx-border-style: dashed;
}

.tag-add:hover {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #adb5bd;
}

/* Customer Information Panel */
.customer-info-panel {
    -fx-background-color: white;
    -fx-padding: 20;
    -fx-background-radius: 8;
    -fx-border-radius: 8;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
}

.info-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #343a40;
}

.customer-info-table {
    -fx-spacing: 0;
}

.info-header {
    -fx-background-color: #f8f9fa;
    -fx-padding: 15 20;
    -fx-spacing: 15;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1 0;
}

.header-name {
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-min-width: 150;
    -fx-max-width: 150;
}

.header-tags {
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-min-width: 120;
    -fx-max-width: 120;
}

.header-created {
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-min-width: 100;
    -fx-max-width: 100;
}

.info-scroll {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.info-content {
    -fx-spacing: 0;
}

.customer-info-row {
    -fx-padding: 15 20;
    -fx-spacing: 15;
    -fx-border-color: #f1f3f4;
    -fx-border-width: 0 0 1 0;
}

.customer-info-row:hover {
    -fx-background-color: #f8f9fa;
}

.info-name {
    -fx-font-size: 14px;
    -fx-text-fill: #343a40;
    -fx-min-width: 150;
    -fx-max-width: 150;
}

.info-tag {
    -fx-font-size: 12px;
    -fx-font-weight: 500;
    -fx-padding: 4 8;
    -fx-background-radius: 12;
    -fx-min-width: 120;
    -fx-max-width: 120;
    -fx-alignment: center;
}

.tag-vip-label {
    -fx-background-color: #fff3cd;
    -fx-text-fill: #856404;
}

.tag-corporate-label {
    -fx-background-color: #d1ecf1;
    -fx-text-fill: #0c5460;
}

.tag-premium-label {
    -fx-background-color: #d4edda;
    -fx-text-fill: #155724;
}

.tag-default-label {
    -fx-background-color: #e9ecef;
    -fx-text-fill: #6c757d;
}

.info-date {
    -fx-font-size: 14px;
    -fx-text-fill: #6c757d;
    -fx-min-width: 100;
    -fx-max-width: 100;
}

.stats-grid {
    -fx-hgap: 20px;
    -fx-vgap: 15px;
}

.stat-card {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 20px;
    -fx-spacing: 8px;
    -fx-alignment: CENTER;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);
    -fx-min-width: 150px;
}

.stat-icon {
    -fx-font-size: 24px;
    -fx-alignment: CENTER;
}

.stat-number {
    -fx-font-size: 28px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
    -fx-alignment: CENTER;
}

.stat-label {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-alignment: CENTER;
}

.quick-actions-section {
    -fx-spacing: 15px;
}

.action-buttons-row {
    -fx-spacing: 15px;
    -fx-alignment: CENTER_LEFT;
}

.action-button-primary {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-font-size: 13px;
    -fx-padding: 12px 20px;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.action-button-primary:hover {
    -fx-background-color: #0056b3;
}

.action-button-secondary {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-font-size: 13px;
    -fx-padding: 12px 20px;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.action-button-secondary:hover {
    -fx-background-color: #5a6268;
}

.action-button-birthday {
    -fx-background-color: #e91e63;
    -fx-text-fill: white;
    -fx-font-size: 13px;
    -fx-padding: 12px 20px;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.action-button-birthday:hover {
    -fx-background-color: #c2185b;
}

.action-button-history {
    -fx-background-color: #17a2b8;
    -fx-text-fill: white;
    -fx-font-size: 13px;
    -fx-padding: 12px 20px;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.action-button-history:hover {
    -fx-background-color: #138496;
}

.segments-section {
    -fx-spacing: 15px;
}

.segments-grid {
    -fx-hgap: 15px;
    -fx-vgap: 15px;
}

.segment-card {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 20px;
    -fx-spacing: 12px;
    -fx-alignment: CENTER;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);
    -fx-min-width: 180px;
}

.segment-card.new-customers {
    -fx-border-color: #17a2b8;
    -fx-border-width: 2px;
}

.segment-card.regular-customers {
    -fx-border-color: #28a745;
    -fx-border-width: 2px;
}

.segment-card.high-spenders {
    -fx-border-color: #ffc107;
    -fx-border-width: 2px;
}

.segment-card.lapsed-customers {
    -fx-border-color: #dc3545;
    -fx-border-width: 2px;
}

.segment-icon {
    -fx-font-size: 20px;
}

.segment-title {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.segment-count {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.segment-button {
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #495057;
    -fx-font-size: 11px;
    -fx-padding: 6px 12px;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-cursor: hand;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
}

.segment-button:hover {
    -fx-background-color: #e9ecef;
}

.customer-list-section {
    -fx-spacing: 15px;
}

.search-field {
    -fx-pref-width: 250px;
    -fx-font-size: 13px;
    -fx-padding: 8px 12px;
}

.filter-combo {
    -fx-pref-width: 180px;
    -fx-font-size: 13px;
}

.customer-table {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

.customer-table-header {
    -fx-spacing: 15px;
    -fx-padding: 15px;
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0px 0px 1px 0px;
    -fx-background-radius: 8px 8px 0px 0px;
}

.customer-table-scroll {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.customer-table-content {
    -fx-spacing: 0px;
}

.customer-row {
    -fx-spacing: 15px;
    -fx-padding: 12px 15px;
    -fx-border-color: #f8f9fa;
    -fx-border-width: 0px 0px 1px 0px;
}

.customer-row:hover {
    -fx-background-color: #f8f9fa;
}

.customer-name {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.customer-phone {
    -fx-font-size: 12px;
    -fx-text-fill: #495057;
}

.customer-email {
    -fx-font-size: 11px;
    -fx-text-fill: #6c757d;
}

.customer-orders {
    -fx-font-size: 13px;
    -fx-text-fill: #495057;
    -fx-alignment: CENTER;
}

.customer-spent {
    -fx-font-size: 13px;
    -fx-font-weight: bold;
    -fx-text-fill: #28a745;
    -fx-alignment: CENTER;
}

.customer-visit {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-alignment: CENTER;
}

.membership-badge {
    -fx-font-size: 10px;
    -fx-padding: 2px 6px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-font-weight: bold;
}

.membership-badge.new {
    -fx-background-color: #e3f2fd;
    -fx-text-fill: #1976d2;
    -fx-border-color: #bbdefb;
    -fx-border-width: 1px;
}

.membership-badge.regular {
    -fx-background-color: #e8f5e8;
    -fx-text-fill: #2e7d32;
    -fx-border-color: #c8e6c9;
    -fx-border-width: 1px;
}

.membership-badge.vip {
    -fx-background-color: #fff3e0;
    -fx-text-fill: #f57c00;
    -fx-border-color: #ffcc02;
    -fx-border-width: 1px;
}

.membership-badge.premium {
    -fx-background-color: #fce4ec;
    -fx-text-fill: #c2185b;
    -fx-border-color: #f8bbd9;
    -fx-border-width: 1px;
}

.segment-badge {
    -fx-font-size: 11px;
    -fx-padding: 4px 8px;
    -fx-background-radius: 10px;
    -fx-border-radius: 10px;
    -fx-font-weight: bold;
    -fx-alignment: CENTER;
}

.segment-badge.new-customer {
    -fx-background-color: #cce5ff;
    -fx-text-fill: #004085;
    -fx-border-color: #99d6ff;
    -fx-border-width: 1px;
}

.segment-badge.regular {
    -fx-background-color: #d4edda;
    -fx-text-fill: #155724;
    -fx-border-color: #c3e6cb;
    -fx-border-width: 1px;
}

.segment-badge.high-spender {
    -fx-background-color: #fff3cd;
    -fx-text-fill: #856404;
    -fx-border-color: #ffeaa7;
    -fx-border-width: 1px;
}

.segment-badge.lapsed-customer {
    -fx-background-color: #f8d7da;
    -fx-text-fill: #721c24;
    -fx-border-color: #f5c6cb;
    -fx-border-width: 1px;
}

.action-btn-small {
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #495057;
    -fx-font-size: 12px;
    -fx-padding: 4px 6px;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-cursor: hand;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-min-width: 28px;
    -fx-min-height: 28px;
}

.action-btn-small:hover {
    -fx-background-color: #e9ecef;
}

.recent-activity-section {
    -fx-spacing: 15px;
}

.activity-list {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 15px;
    -fx-spacing: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

.activity-item {
    -fx-font-size: 13px;
    -fx-text-fill: #495057;
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
}

.activity-item:hover {
    -fx-background-color: #e9ecef;
}

/* Campaign Creator Styles */
.campaign-layout {
    -fx-spacing: 25px;
}

.campaign-type-panel {
    -fx-background-color: #f0f0f0;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 0px;
    -fx-spacing: 0px;
    -fx-min-width: 200px;
    -fx-max-width: 200px;
}

.panel-title {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
    -fx-padding: 15px 20px 10px 20px;
    -fx-background-color: #f0f0f0;
    -fx-background-radius: 8px 8px 0px 0px;
}

.campaign-type-list {
    -fx-spacing: 0px;
}

.campaign-type-item {
    -fx-background-color: #f0f0f0;
    -fx-text-fill: #666666;
    -fx-font-size: 14px;
    -fx-padding: 12px 20px;
    -fx-alignment: CENTER_LEFT;
    -fx-border-color: transparent;
    -fx-border-width: 0px;
    -fx-background-radius: 0px;
    -fx-cursor: hand;
    -fx-min-width: 200px;
    -fx-max-width: 200px;
}

.campaign-type-item:hover {
    -fx-background-color: #e8e8e8;
}

.campaign-type-item.selected {
    -fx-background-color: #d4b5d4;
    -fx-text-fill: #333333;
    -fx-font-weight: bold;
}

.campaign-preview-panel {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 20px;
    -fx-spacing: 20px;
}

.message-preview {
    -fx-spacing: 15px;
}

.email-icon {
    -fx-font-size: 24px;
    -fx-text-fill: #dc3545;
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-padding: 8px;
    -fx-background-radius: 50%;
    -fx-min-width: 40px;
    -fx-min-height: 40px;
    -fx-alignment: CENTER;
}

.message-content {
    -fx-spacing: 10px;
    -fx-padding: 15px;
    -fx-background-color: #f8f9fa;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
}

.message-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

.message-body {
    -fx-font-size: 14px;
    -fx-text-fill: #555555;
    -fx-wrap-text: true;
}

.message-sender {
    -fx-font-size: 12px;
    -fx-text-fill: #888888;
    -fx-font-style: italic;
}

.campaign-config {
    -fx-spacing: 15px;
}

.config-title {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

.config-grid {
    -fx-hgap: 15px;
    -fx-vgap: 12px;
}

.config-label {
    -fx-font-size: 13px;
    -fx-text-fill: #555555;
    -fx-font-weight: bold;
}

.config-field {
    -fx-font-size: 13px;
    -fx-padding: 8px 12px;
    -fx-pref-width: 200px;
}

.discount-field {
    -fx-font-size: 13px;
    -fx-padding: 8px 12px;
    -fx-pref-width: 60px;
    -fx-alignment: CENTER;
}

.discount-label {
    -fx-font-size: 13px;
    -fx-text-fill: #555555;
    -fx-alignment: CENTER_LEFT;
}

.config-combo {
    -fx-font-size: 13px;
    -fx-pref-width: 200px;
}

.customer-selection-section {
    -fx-spacing: 15px;
}

.selection-controls {
    -fx-spacing: 15px;
    -fx-alignment: CENTER_LEFT;
}

.customer-search-field {
    -fx-font-size: 13px;
    -fx-padding: 8px 12px;
    -fx-pref-width: 250px;
}

.segment-filter-combo {
    -fx-font-size: 13px;
    -fx-pref-width: 180px;
}

.select-all-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-padding: 8px 16px;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-cursor: hand;
}

.select-all-button:hover {
    -fx-background-color: #0056b3;
}

.clear-all-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-padding: 8px 16px;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-cursor: hand;
}

.clear-all-button:hover {
    -fx-background-color: #5a6268;
}

.customer-selection-table {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
}

.customer-selection-header {
    -fx-spacing: 15px;
    -fx-padding: 12px 15px;
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 0px 0px 1px 0px;
    -fx-background-radius: 8px 8px 0px 0px;
    -fx-alignment: CENTER_LEFT;
}

.header-checkbox {
    -fx-min-width: 20px;
}

.header-label {
    -fx-font-size: 13px;
    -fx-font-weight: bold;
    -fx-text-fill: #555555;
}

.phone-header {
    -fx-pref-width: 150px;
}

.name-header {
    -fx-pref-width: 150px;
}

.segment-header {
    -fx-pref-width: 120px;
}

.birthday-header {
    -fx-pref-width: 100px;
}

.customer-list-scroll {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.customer-list-content {
    -fx-spacing: 0px;
}

.customer-selection-row {
    -fx-spacing: 15px;
    -fx-padding: 12px 15px;
    -fx-border-color: #f0f0f0;
    -fx-border-width: 0px 0px 1px 0px;
    -fx-alignment: CENTER_LEFT;
}

.customer-selection-row:hover {
    -fx-background-color: #f8f9fa;
}

.customer-checkbox {
    -fx-min-width: 20px;
}

.customer-phone-label {
    -fx-font-size: 13px;
    -fx-text-fill: #555555;
    -fx-pref-width: 150px;
}

.customer-name-label {
    -fx-font-size: 13px;
    -fx-text-fill: #333333;
    -fx-font-weight: bold;
    -fx-pref-width: 150px;
}

.segment-badge-small {
    -fx-font-size: 10px;
    -fx-padding: 3px 8px;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-font-weight: bold;
    -fx-alignment: CENTER;
    -fx-pref-width: 120px;
}

.segment-badge-small.new-customer {
    -fx-background-color: #cce5ff;
    -fx-text-fill: #004085;
    -fx-border-color: #99d6ff;
    -fx-border-width: 1px;
}

.segment-badge-small.regular {
    -fx-background-color: #d4edda;
    -fx-text-fill: #155724;
    -fx-border-color: #c3e6cb;
    -fx-border-width: 1px;
}

.segment-badge-small.high-spender {
    -fx-background-color: #fff3cd;
    -fx-text-fill: #856404;
    -fx-border-color: #ffeaa7;
    -fx-border-width: 1px;
}

.segment-badge-small.lapsed-customer {
    -fx-background-color: #f8d7da;
    -fx-text-fill: #721c24;
    -fx-border-color: #f5c6cb;
    -fx-border-width: 1px;
}

.customer-birthday-label {
    -fx-font-size: 12px;
    -fx-text-fill: #666666;
    -fx-pref-width: 100px;
}

.selection-summary {
    -fx-spacing: 10px;
    -fx-alignment: CENTER_LEFT;
}

.selected-count {
    -fx-font-size: 13px;
    -fx-text-fill: #555555;
    -fx-font-weight: bold;
}

.action-buttons-section {
    -fx-spacing: 15px;
    -fx-alignment: CENTER_RIGHT;
}

.preview-button {
    -fx-background-color: #17a2b8;
    -fx-text-fill: white;
    -fx-font-size: 13px;
    -fx-padding: 12px 20px;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.preview-button:hover {
    -fx-background-color: #138496;
}

.save-draft-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-font-size: 13px;
    -fx-padding: 12px 20px;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.save-draft-button:hover {
    -fx-background-color: #5a6268;
}

.send-campaign-button {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-font-size: 13px;
    -fx-padding: 12px 20px;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.send-campaign-button:hover {
    -fx-background-color: #218838;
}

.close-campaign-button {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-font-size: 13px;
    -fx-padding: 12px 20px;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.close-campaign-button:hover {
    -fx-background-color: #c82333;
}

.birthday-campaign-section {
    -fx-background-color: #fff8f0;
    -fx-border-color: #e91e63;
    -fx-border-width: 2px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 20px;
    -fx-spacing: 20px;
}

/* Customer History Styles */
.customer-history-layout {
    -fx-spacing: 25px;
}

.customer-details-panel {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 20px;
    -fx-spacing: 20px;
    -fx-min-width: 300px;
    -fx-max-width: 300px;
}

.customer-info-card {
    -fx-background-color: #f8f9fa;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 20px;
    -fx-spacing: 15px;
}

.info-row {
    -fx-spacing: 15px;
    -fx-alignment: CENTER_LEFT;
}

.info-label {
    -fx-font-size: 14px;
    -fx-text-fill: #666666;
    -fx-font-weight: normal;
    -fx-min-width: 80px;
}

.info-value {
    -fx-font-size: 14px;
    -fx-text-fill: #333333;
    -fx-font-weight: bold;
}

.info-value-amount {
    -fx-font-size: 14px;
    -fx-text-fill: #333333;
    -fx-font-weight: bold;
}

.statistics-panel {
    -fx-spacing: 20px;
}

.stat-card-large {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 20px;
    -fx-spacing: 10px;
}

.stat-icon-large {
    -fx-font-size: 20px;
    -fx-text-fill: #666666;
}

.stat-title-large {
    -fx-font-size: 14px;
    -fx-text-fill: #666666;
    -fx-font-weight: normal;
}

.stat-amount-large {
    -fx-font-size: 24px;
    -fx-text-fill: #333333;
    -fx-font-weight: bold;
}

.stat-date-large {
    -fx-font-size: 18px;
    -fx-text-fill: #333333;
    -fx-font-weight: bold;
}

.stat-count-large {
    -fx-font-size: 18px;
    -fx-text-fill: #333333;
    -fx-font-weight: bold;
}

.order-history-section {
    -fx-spacing: 15px;
}

.order-history-table {
    -fx-background-color: white;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
}

.order-history-header {
    -fx-spacing: 20px;
    -fx-padding: 15px 20px;
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 0px 0px 1px 0px;
    -fx-background-radius: 8px 8px 0px 0px;
    -fx-alignment: CENTER_LEFT;
}

.header-cell {
    -fx-font-size: 13px;
    -fx-font-weight: bold;
    -fx-text-fill: #555555;
}

.date-header {
    -fx-pref-width: 120px;
}

.type-header {
    -fx-pref-width: 100px;
}

.payment-header {
    -fx-pref-width: 80px;
}

.item-header {
    -fx-pref-width: 150px;
}

.outlet-header {
    -fx-pref-width: 100px;
}

.amount-header {
    -fx-pref-width: 100px;
}

.order-list-scroll {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
}

.order-list-content {
    -fx-spacing: 0px;
}

.order-history-row {
    -fx-spacing: 20px;
    -fx-padding: 15px 20px;
    -fx-border-color: #f0f0f0;
    -fx-border-width: 0px 0px 1px 0px;
    -fx-alignment: CENTER_LEFT;
}

.order-history-row:hover {
    -fx-background-color: #f8f9fa;
}

.order-date {
    -fx-font-size: 13px;
    -fx-text-fill: #333333;
    -fx-font-weight: bold;
}

.order-time {
    -fx-font-size: 11px;
    -fx-text-fill: #666666;
}

.order-type {
    -fx-font-size: 13px;
    -fx-text-fill: #555555;
    -fx-pref-width: 100px;
}

.order-payment {
    -fx-font-size: 13px;
    -fx-text-fill: #555555;
    -fx-pref-width: 80px;
}

.order-item {
    -fx-font-size: 13px;
    -fx-text-fill: #333333;
    -fx-font-weight: bold;
    -fx-pref-width: 150px;
}

.order-outlet {
    -fx-font-size: 13px;
    -fx-text-fill: #555555;
    -fx-pref-width: 100px;
}

.order-amount {
    -fx-font-size: 13px;
    -fx-text-fill: #333333;
    -fx-font-weight: bold;
    -fx-pref-width: 100px;
    -fx-alignment: CENTER_RIGHT;
}

.export-button {
    -fx-background-color: #17a2b8;
    -fx-text-fill: white;
    -fx-font-size: 13px;
    -fx-padding: 12px 20px;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.export-button:hover {
    -fx-background-color: #138496;
}

.print-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-font-size: 13px;
    -fx-padding: 12px 20px;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.print-button:hover {
    -fx-background-color: #5a6268;
}

.email-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-font-size: 13px;
    -fx-padding: 12px 20px;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-cursor: hand;
    -fx-font-weight: bold;
}

.email-button:hover {
    -fx-background-color: #0056b3;
}

/* Notification Panel Container */
.notification-panel-container {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #007bff;
    -fx-border-width: 2px 0px 0px 2px;
    -fx-min-width: 400px;
    -fx-max-width: 400px;
    -fx-pref-width: 400px;
    -fx-padding: 10px;
    -fx-alignment: top-left;
    -fx-fill-width: true;
}

/* 🤖 Enhanced AI Forecaster Styles */

/* Header Section */
.ai-forecaster-header {
    -fx-background-color: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    -fx-effect: dropshadow(gaussian, #333333, 15, 0, 0, 3);
}

.ai-header-icon {
    -fx-font-size: 32px;
    -fx-text-fill: white;
}

.ai-page-title {
    -fx-font-size: 28px;
    -fx-font-weight: bold;
    -fx-fill: white;
    -fx-effect: dropshadow(gaussian, #4d4d4d, 2, 0, 0, 1);
}

.ai-page-subtitle {
    -fx-font-size: 14px;
    -fx-fill: #ffffffe6;
    -fx-font-style: italic;
}

.ai-voice-button {
    -fx-background-color: rgba(255, 255, 255, 0.15);
    -fx-text-fill: white;
    -fx-background-radius: 25px;
    -fx-border-radius: 25px;
    -fx-border-color: #ffffff4d;
    -fx-border-width: 1px;
    -fx-padding: 10px 16px;
    -fx-font-weight: 500;
}

.ai-voice-button:hover {
    -fx-background-color: rgba(255, 255, 255, 0.25);
    -fx-effect: dropshadow(gaussian, #ffffff4d, 8, 0, 0, 0);
}

.ai-help-button {
    -fx-background-color: rgba(255, 255, 255, 0.15);
    -fx-text-fill: white;
    -fx-background-radius: 25px;
    -fx-border-radius: 25px;
    -fx-border-color: #ffffff4d;
    -fx-border-width: 1px;
    -fx-padding: 10px 16px;
    -fx-font-weight: 500;
}

.ai-help-button:hover {
    -fx-background-color: rgba(255, 255, 255, 0.25);
}

/* Content Area */
.forecaster-content {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);
}

/* Parameters Section */
.ai-parameters-section {
    -fx-background-color: white;
    -fx-background-radius: 16px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.08), 12, 0, 0, 3);
    -fx-padding: 30px;
}

.section-header {
    -fx-padding: 0 0 20px 0;
}

.ai-section-icon {
    -fx-font-size: 24px;
    -fx-text-fill: #667eea;
}

.ai-section-title {
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-fill: #2c3e50;
}

.ai-section-desc {
    -fx-font-size: 13px;
    -fx-fill: #6c757d;
}

.ai-parameter-grid {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 12px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
}

.ai-input-group {
    -fx-background-color: white;
    -fx-background-radius: 8px;
    -fx-padding: 15px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.05), 5, 0, 0, 1);
}

.ai-input-group:hover {
    -fx-effect: dropshadow(gaussian, #667eea, 8, 0, 0, 2);
    -fx-translate-y: -1px;
}

.ai-input-icon {
    -fx-font-size: 16px;
    -fx-text-fill: #667eea;
}

.ai-input-label {
    -fx-font-size: 13px;
    -fx-font-weight: 600;
    -fx-text-fill: #495057;
}

.ai-date-picker, .ai-combo-box {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 10px 12px;
    -fx-font-size: 13px;
}

.ai-date-picker:focused, .ai-combo-box:focused {
    -fx-border-color: #667eea;
    -fx-effect: dropshadow(gaussian, #667eea, 5, 0, 0, 0);
}

/* Action Buttons */
.ai-generate-button {
    -fx-background-color: linear-gradient(to bottom, #667eea, #5a67d8);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-padding: 12px 24px;
    -fx-font-size: 14px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #667eea, 8, 0, 0, 2);
}

.ai-generate-button:hover {
    -fx-background-color: linear-gradient(to bottom, #5a67d8, #4c51bf);
    -fx-effect: dropshadow(gaussian, #667eea, 12, 0, 0, 4);
    -fx-translate-y: -2px;
}

.ai-reset-button {
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #6c757d;
    -fx-font-weight: 600;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-padding: 12px 24px;
    -fx-font-size: 14px;
    -fx-cursor: hand;
}

.ai-reset-button:hover {
    -fx-background-color: #e9ecef;
    -fx-border-color: #adb5bd;
}

.button-icon {
    -fx-font-size: 14px;
}

.button-text {
    -fx-font-size: 14px;
    -fx-font-weight: 600;
}

/* Summary Section */
.ai-summary-section {
    -fx-background-color: white;
    -fx-background-radius: 16px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.08), 12, 0, 0, 3);
    -fx-padding: 30px;
}

.ai-timestamp {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-font-style: italic;
}

.ai-summary-cards {
    -fx-padding: 20px 0 0 0;
}

.ai-summary-card {
    -fx-background-radius: 16px;
    -fx-padding: 24px;
    -fx-spacing: 12px;
    -fx-min-width: 240px;
    -fx-pref-width: 240px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.06), 8, 0, 0, 2);
}

.ai-summary-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.12), 16, 0, 0, 4);
    -fx-translate-y: -3px;
}

.ai-card-primary {
    -fx-background-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.ai-card-success {
    -fx-background-color: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.ai-card-info {
    -fx-background-color: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
}

.ai-card-warning {
    -fx-background-color: linear-gradient(135deg, #f39c12 0%, #f7dc6f 100%);
}

.ai-card-icon-container {
    -fx-background-radius: 12px;
    -fx-padding: 12px;
    -fx-min-width: 48px;
    -fx-min-height: 48px;
    -fx-max-width: 48px;
    -fx-max-height: 48px;
}

.ai-icon-sales {
    -fx-background-color: #ffffff33;
}

.ai-icon-growth {
    -fx-background-color: #ffffff33;
}

.ai-icon-category {
    -fx-background-color: #ffffff33;
}

.ai-icon-confidence {
    -fx-background-color: #ffffff33;
}

.ai-card-icon {
    -fx-font-size: 20px;
    -fx-text-fill: white;
}

.ai-card-label {
    -fx-font-size: 13px;
    -fx-fill: #ffffffe6;
    -fx-font-weight: 500;
}

.ai-card-period {
    -fx-font-size: 11px;
    -fx-fill: #ffffffb3;
}

.ai-card-value {
    -fx-font-size: 28px;
    -fx-font-weight: bold;
    -fx-fill: white;
}

.ai-value-primary {
    -fx-fill: white;
}

.ai-value-success {
    -fx-fill: white;
}

.ai-value-info {
    -fx-fill: white;
}

.ai-value-warning {
    -fx-fill: white;
}

.ai-trend-icon {
    -fx-font-size: 14px;
    -fx-text-fill: #ffffffe6;
}

.ai-card-change {
    -fx-font-size: 13px;
    -fx-font-weight: 600;
}

.ai-change-positive {
    -fx-fill: rgba(255, 255, 255, 0.95);
}

.ai-change-negative {
    -fx-fill: rgba(255, 255, 255, 0.95);
}

.ai-change-neutral {
    -fx-fill: rgba(255, 255, 255, 0.85);
}

/* Chart Section */
.ai-chart-section {
    -fx-background-color: white;
    -fx-background-radius: 16px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.08), 12, 0, 0, 3);
    -fx-padding: 30px;
}

.ai-toggle-group {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 8px;
    -fx-padding: 4px;
}

.ai-view-toggle {
    -fx-background-color: transparent;
    -fx-text-fill: #6c757d;
    -fx-padding: 8px 16px;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-border-width: 0px;
}

.ai-view-toggle:selected {
    -fx-background-color: white;
    -fx-text-fill: #667eea;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 4, 0, 0, 1);
}

.ai-toggle-left {
    -fx-background-radius: 6px 0 0 6px;
}

.ai-toggle-center {
    -fx-background-radius: 0px;
}

.ai-toggle-right {
    -fx-background-radius: 0 6px 6px 0;
}

.ai-export-button {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 8px 16px;
    -fx-font-size: 13px;
    -fx-font-weight: 600;
}

.ai-export-button:hover {
    -fx-background-color: #218838;
}

.ai-chart-container {
    -fx-background-color: #fafbfc;
    -fx-background-radius: 12px;
    -fx-padding: 20px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
}

.ai-forecast-chart {
    -fx-background-color: transparent;
    -fx-padding: 15px;
}

.ai-forecast-chart .chart-plot-background {
    -fx-background-color: white;
    -fx-background-radius: 8px;
}

.ai-forecast-chart .chart-vertical-grid-lines {
    -fx-stroke: #e9ecef;
    -fx-stroke-width: 1px;
    -fx-stroke-dash-array: 5 5;
}

.ai-forecast-chart .chart-horizontal-grid-lines {
    -fx-stroke: #e9ecef;
    -fx-stroke-width: 1px;
    -fx-stroke-dash-array: 5 5;
}

.ai-forecast-chart .chart-series-line {
    -fx-stroke-width: 4px;
}

.ai-forecast-chart .default-color0.chart-series-line {
    -fx-stroke: linear-gradient(to right, #ff7f00, #ff9500);
}

.ai-forecast-chart .default-color1.chart-series-line {
    -fx-stroke: linear-gradient(to right, #ff7f00, #ffab40);
    -fx-stroke-dash-array: 8 4;
}

.ai-forecast-chart .default-color0.chart-line-symbol {
    -fx-background-color: #ff7f00;
    -fx-background-radius: 6px;
    -fx-padding: 6px;
}

.ai-forecast-chart .default-color1.chart-line-symbol {
    -fx-background-color: #ffab40;
    -fx-background-radius: 6px;
    -fx-padding: 6px;
}

.ai-chart-axis {
    -fx-tick-label-font-size: 11px;
    -fx-tick-label-fill: #6c757d;
}

.ai-chart-legend {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 10px;
    -fx-padding: 20px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 10px;
}

.ai-legend-item {
    -fx-padding: 8px 12px;
    -fx-background-color: white;
    -fx-background-radius: 8px;
}

.ai-legend-dot {
    -fx-stroke-width: 0;
}

.ai-legend-historical {
    -fx-fill: #ff7f00;
}

.ai-legend-forecast {
    -fx-fill: #ffab40;
}

.ai-legend-band {
    -fx-fill: rgba(255, 171, 64, 0.3);
    -fx-arc-width: 4;
    -fx-arc-height: 4;
}

.ai-legend-confidence {
    -fx-fill: rgba(255, 171, 64, 0.3);
}

.ai-legend-title {
    -fx-font-size: 13px;
    -fx-font-weight: 600;
    -fx-fill: #495057;
}

.ai-legend-desc {
    -fx-font-size: 11px;
    -fx-fill: #6c757d;
}

/* Insights Panel */
.ai-insights-panel {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 12px;
    -fx-padding: 20px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
}

.ai-insight-icon {
    -fx-font-size: 18px;
    -fx-text-fill: #667eea;
}

.ai-insight-title {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-fill: #495057;
}

.ai-insights-list {
    -fx-padding: 10px 0 0 0;
}

/* Loading States */
.ai-loading-container {
    -fx-background-color: white;
    -fx-background-radius: 16px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.08), 12, 0, 0, 3);
    -fx-padding: 50px;
}

.ai-loading-content {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 16px;
    -fx-padding: 40px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 16px;
}

.ai-progress-indicator {
    -fx-progress-color: #667eea;
    -fx-pref-width: 60px;
    -fx-pref-height: 60px;
}

.ai-loading-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-fill: #495057;
}

.ai-loading-status {
    -fx-font-size: 14px;
    -fx-fill: #6c757d;
    -fx-font-style: italic;
}

.ai-loading-steps {
    -fx-padding: 20px 0 0 0;
}

.ai-step {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-padding: 15px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-opacity: 0.6;
}

.ai-step-active {
    -fx-opacity: 1.0;
    -fx-border-color: #667eea;
    -fx-effect: dropshadow(gaussian, #667eea, 8, 0, 0, 2);
}

.ai-step-icon {
    -fx-font-size: 20px;
    -fx-text-fill: #667eea;
}

.ai-step-text {
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-fill: #495057;
}

.ai-step-arrow {
    -fx-font-size: 16px;
    -fx-text-fill: #adb5bd;
}

/* Welcome State */
.ai-welcome-state {
    -fx-background-color: white;
    -fx-background-radius: 16px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.08), 12, 0, 0, 3);
    -fx-padding: 60px;
}

.ai-welcome-content {
    -fx-background-color: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    -fx-background-radius: 20px;
    -fx-padding: 50px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 20px;
}

.ai-welcome-icon {
    -fx-font-size: 80px;
    -fx-text-fill: #667eea;
    -fx-effect: dropshadow(gaussian, #667eea, 10, 0, 0, 3);
}

.ai-welcome-title {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-fill: #2c3e50;
}

.ai-welcome-subtitle {
    -fx-font-size: 16px;
    -fx-fill: #6c757d;
    -fx-text-alignment: center;
    -fx-wrap-text: true;
}

.ai-feature-highlights {
    -fx-padding: 30px 0;
}

.ai-feature {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-padding: 20px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.05), 5, 0, 0, 1);
}

.ai-feature-icon {
    -fx-font-size: 24px;
    -fx-text-fill: #667eea;
}

.ai-feature-text {
    -fx-font-size: 13px;
    -fx-font-weight: 600;
    -fx-fill: #495057;
}

.ai-start-button {
    -fx-background-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 16px;
    -fx-border-radius: 16px;
    -fx-padding: 16px 32px;
    -fx-font-size: 16px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #667eea, 12, 0, 0, 4);
}

.ai-start-button:hover {
    -fx-background-color: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    -fx-effect: dropshadow(gaussian, #667eea, 16, 0, 0, 6);
    -fx-translate-y: -3px;
}

/* 🎤 Voice Input Component Styles */

/* Main Container */
.voice-input-container {
    -fx-background-color: white;
    -fx-background-radius: 16px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.08), 12, 0, 0, 3);
    -fx-padding: 20px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 16px;
}

.voice-input-container.compact-mode {
    -fx-padding: 12px;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
}

/* Voice Activation Section */
.voice-activation-section {
    -fx-padding: 0 0 10px 0;
}

.mic-button-container {
    -fx-min-width: 60px;
    -fx-min-height: 60px;
    -fx-max-width: 60px;
    -fx-max-height: 60px;
}

/* Mic Button States */
.voice-mic-button {
    -fx-background-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -fx-background-radius: 30px;
    -fx-border-radius: 30px;
    -fx-min-width: 50px;
    -fx-min-height: 50px;
    -fx-max-width: 50px;
    -fx-max-height: 50px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #667eea, 8, 0, 0, 2);
}

.voice-mic-button:hover {
    -fx-effect: dropshadow(gaussian, #667eea, 12, 0, 0, 4);
    -fx-translate-y: -2px;
}

.voice-mic-button.listening {
    -fx-background-color: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    -fx-effect: dropshadow(gaussian, #e74c3c, 12, 0, 0, 3);
}

.voice-mic-button.processing {
    -fx-background-color: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    -fx-effect: dropshadow(gaussian, #f39c12, 12, 0, 0, 3);
}

.mic-icon {
    -fx-font-size: 20px;
    -fx-text-fill: white;
}

/* Pulse Animation */
.pulse-ring {
    -fx-fill: transparent;
    -fx-stroke: #667eea;
    -fx-stroke-width: 2px;
    -fx-opacity: 0.7;
}

/* Waveform Animation */
.waveform-container {
    -fx-background-color: #667eea;
    -fx-background-radius: 20px;
    -fx-padding: 8px 12px;
}

.waveform-bar {
    -fx-fill: #667eea;
    -fx-arc-width: 2;
    -fx-arc-height: 2;
}

/* Status and Controls */
.voice-status-icon {
    -fx-font-size: 16px;
    -fx-text-fill: #667eea;
}

.voice-status-text {
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-fill: #495057;
}

/* Command Hints */
.command-hints {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 8px;
    -fx-padding: 8px 12px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
}

.hint-label {
    -fx-font-size: 11px;
    -fx-text-fill: #6c757d;
    -fx-font-weight: 500;
}

.hint-button {
    -fx-background-color: #e9ecef;
    -fx-text-fill: #495057;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-padding: 4px 8px;
    -fx-font-size: 10px;
    -fx-font-weight: 500;
    -fx-cursor: hand;
}

.hint-button:hover {
    -fx-background-color: #667eea;
    -fx-text-fill: white;
}

/* Voice Settings */
.voice-settings-button {
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #6c757d;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-padding: 8px 12px;
    -fx-font-size: 12px;
}

.voice-settings-button:hover {
    -fx-background-color: #e9ecef;
}

/* Transcription Area */
.transcription-area {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 12px;
    -fx-padding: 16px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
}

.transcription-icon {
    -fx-font-size: 16px;
    -fx-text-fill: #667eea;
}

.transcription-title {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-fill: #495057;
}

.clear-button {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 4px 8px;
    -fx-font-size: 12px;
    -fx-cursor: hand;
}

.clear-button:hover {
    -fx-background-color: #c82333;
}

.transcription-scroll {
    -fx-background-color: white;
    -fx-background-radius: 8px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
}

.transcription-text {
    -fx-background-color: white;
    -fx-text-fill: #495057;
    -fx-font-size: 13px;
    -fx-background-radius: 8px;
    -fx-border-color: transparent;
    -fx-focus-color: #667eea;
}

/* Command Status */
.command-status {
    -fx-background-color: #e3f2fd;
    -fx-background-radius: 8px;
    -fx-padding: 10px 12px;
    -fx-border-color: #bbdefb;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
}

.command-status-icon {
    -fx-font-size: 14px;
    -fx-text-fill: #1976d2;
}

.command-status-text {
    -fx-font-size: 13px;
    -fx-fill: #1976d2;
    -fx-font-weight: 500;
}

.command-progress {
    -fx-progress-color: #1976d2;
}

/* Action Buttons */
.voice-submit-button {
    -fx-background-color: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 10px 16px;
    -fx-font-size: 13px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #28a745, 6, 0, 0, 2);
}

.voice-submit-button:hover {
    -fx-background-color: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    -fx-effect: dropshadow(gaussian, #28a745, 8, 0, 0, 3);
}

.voice-submit-button:disabled {
    -fx-background-color: #6c757d;
    -fx-opacity: 0.6;
    -fx-effect: none;
}

.voice-retry-button {
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #6c757d;
    -fx-font-weight: 600;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-padding: 10px 16px;
    -fx-font-size: 13px;
    -fx-cursor: hand;
}

.voice-retry-button:hover {
    -fx-background-color: #e9ecef;
    -fx-border-color: #adb5bd;
}

.voice-cancel-button {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-font-weight: 600;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 10px 16px;
    -fx-font-size: 13px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #dc3545, 6, 0, 0, 2);
}

.voice-cancel-button:hover {
    -fx-background-color: #c82333;
    -fx-effect: dropshadow(gaussian, #dc3545, 8, 0, 0, 3);
}

/* Suggestions Panel */
.suggestions-panel {
    -fx-background-color: #fff3cd;
    -fx-background-radius: 12px;
    -fx-padding: 16px;
    -fx-border-color: #ffeaa7;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
}

.suggestions-icon {
    -fx-font-size: 16px;
    -fx-text-fill: #856404;
}

.suggestions-title {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-fill: #856404;
}

.suggestions-flow {
    -fx-padding: 8px 0 0 0;
}

.suggestion-button {
    -fx-background-color: white;
    -fx-text-fill: #495057;
    -fx-background-radius: 16px;
    -fx-border-radius: 16px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-padding: 6px 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 500;
    -fx-cursor: hand;
}

.suggestion-button:hover {
    -fx-background-color: #667eea;
    -fx-text-fill: white;
    -fx-border-color: #667eea;
    -fx-effect: dropshadow(gaussian, #667eea, 4, 0, 0, 1);
}

/* Match Result Container */
.match-result-container {
    -fx-background-color: #d4edda;
    -fx-background-radius: 12px;
    -fx-padding: 16px;
    -fx-border-color: #c3e6cb;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
}

.match-result-icon {
    -fx-font-size: 20px;
    -fx-text-fill: #155724;
}

.match-result-title {
    -fx-font-size: 15px;
    -fx-font-weight: bold;
    -fx-fill: #155724;
}

.match-result-description {
    -fx-font-size: 13px;
    -fx-fill: #155724;
}

.execute-match-button {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 8px 16px;
    -fx-font-size: 13px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #28a745, 6, 0, 0, 2);
}

.execute-match-button:hover {
    -fx-background-color: #218838;
    -fx-effect: dropshadow(gaussian, #28a745, 8, 0, 0, 3);
}

/* Error Container */
.error-container {
    -fx-background-color: #f8d7da;
    -fx-background-radius: 12px;
    -fx-padding: 16px;
    -fx-border-color: #f5c6cb;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
}

.error-icon {
    -fx-font-size: 20px;
    -fx-text-fill: #721c24;
}

.error-title {
    -fx-font-size: 15px;
    -fx-font-weight: bold;
    -fx-fill: #721c24;
}

.error-description {
    -fx-font-size: 13px;
    -fx-fill: #721c24;
}

.retry-error-button {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 8px 16px;
    -fx-font-size: 13px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #dc3545, 6, 0, 0, 2);
}

.retry-error-button:hover {
    -fx-background-color: #c82333;
    -fx-effect: dropshadow(gaussian, #dc3545, 8, 0, 0, 3);
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    .voice-input-container {
        -fx-padding: 16px;
    }

    .mic-button-container {
        -fx-min-width: 50px;
        -fx-min-height: 50px;
        -fx-max-width: 50px;
        -fx-max-height: 50px;
    }

    .voice-mic-button {
        -fx-min-width: 40px;
        -fx-min-height: 40px;
        -fx-max-width: 40px;
        -fx-max-height: 40px;
    }

    .mic-icon {
        -fx-font-size: 16px;
    }

    .command-hints {
        -fx-padding: 6px 8px;
    }

    .hint-button {
        -fx-padding: 3px 6px;
        -fx-font-size: 9px;
    }
}

/* Animation Classes */
.voice-pulse {
    -fx-effect: dropshadow(gaussian, #667eea, 15, 0, 0, 0);
}

.voice-listening-glow {
    -fx-effect: dropshadow(gaussian, #e74c3c, 20, 0, 0, 0);
}

.voice-processing-glow {
    -fx-effect: dropshadow(gaussian, #f39c12, 20, 0, 0, 0);
}

/* 🤖 Global AI Voice Assistant Styles */

/* Main Container */
.global-voice-container {
    -fx-background-color: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    -fx-background-radius: 20px;
    -fx-effect: dropshadow(gaussian, #262626, 20, 0, 0, 5);
    -fx-padding: 24px;
    -fx-border-color: #ffffff33;
    -fx-border-width: 1px;
    -fx-border-radius: 20px;
}

/* Header Section */
.global-voice-header {
    -fx-padding: 0 0 16px 0;
}

.ai-avatar-container {
    -fx-min-width: 60px;
    -fx-min-height: 60px;
}

.ai-avatar-background {
    -fx-fill: #ffffffe6;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 8, 0, 0, 2);
}

.ai-avatar-icon {
    -fx-font-size: 24px;
    -fx-text-fill: #667eea;
}

.ai-pulse-ring {
    -fx-fill: transparent;
    -fx-stroke: #ffffff99;
    -fx-stroke-width: 2px;
}

.ai-assistant-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-fill: white;
    -fx-effect: dropshadow(gaussian, #4d4d4d, 2, 0, 0, 1);
}

.ai-assistant-status {
    -fx-font-size: 13px;
    -fx-fill: #ffffffe6;
    -fx-font-style: italic;
}

.context-badge {
    -fx-background-color: #ffffff33;
    -fx-text-fill: white;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-border-color: #ffffff4d;
    -fx-border-width: 1px;
    -fx-padding: 4px 8px;
    -fx-font-size: 10px;
    -fx-font-weight: 600;
}

.context-dashboard { -fx-background-color: rgba(52, 152, 219, 0.8); }
.context-forecasting { -fx-background-color: rgba(155, 89, 182, 0.8); }
.context-billing { -fx-background-color: rgba(46, 204, 113, 0.8); }
.context-tables { -fx-background-color: rgba(230, 126, 34, 0.8); }
.context-menu { -fx-background-color: #e74c3c; }
.context-reports { -fx-background-color: rgba(241, 196, 15, 0.8); }

/* Main Voice Button */
.main-voice-button-container {
    -fx-min-width: 70px;
    -fx-min-height: 70px;
}

.main-voice-button {
    -fx-background-color: #ffffffe6;
    -fx-background-radius: 35px;
    -fx-border-radius: 35px;
    -fx-min-width: 60px;
    -fx-min-height: 60px;
    -fx-max-width: 60px;
    -fx-max-height: 60px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #333333, 12, 0, 0, 3);
}

.main-voice-button:hover {
    -fx-background-color: white;
    -fx-effect: dropshadow(gaussian, #ffffff66, 16, 0, 0, 4);
    -fx-translate-y: -2px;
}

.main-voice-icon {
    -fx-font-size: 24px;
    -fx-text-fill: #667eea;
}

.main-pulse-ring {
    -fx-fill: transparent;
    -fx-stroke: #ffffffcc;
    -fx-stroke-width: 3px;
}

.voice-settings-menu {
    -fx-background-color: rgba(255, 255, 255, 0.15);
    -fx-text-fill: white;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-border-color: #ffffff4d;
    -fx-border-width: 1px;
    -fx-padding: 8px 12px;
    -fx-font-size: 14px;
}

.voice-settings-menu:hover {
    -fx-background-color: rgba(255, 255, 255, 0.25);
}

/* Voice Input Area */
.voice-input-area {
    -fx-background-color: rgba(255, 255, 255, 0.95);
    -fx-background-radius: 16px;
    -fx-padding: 20px;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 10, 0, 0, 2);
}

.transcription-icon {
    -fx-font-size: 16px;
    -fx-text-fill: #667eea;
}

.transcription-label {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-fill: #495057;
}

.confidence-indicator {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-padding: 4px 8px;
}

.confidence-high {
    -fx-background-color: #d4edda;
    -fx-text-fill: #155724;
}

.confidence-medium {
    -fx-background-color: #fff3cd;
    -fx-text-fill: #856404;
}

.confidence-low {
    -fx-background-color: #f8d7da;
    -fx-text-fill: #721c24;
}

.live-transcription {
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #495057;
    -fx-font-size: 14px;
    -fx-background-radius: 8px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-focus-color: #667eea;
}

/* Intent Display */
.intent-display {
    -fx-background-color: #e3f2fd;
    -fx-background-radius: 12px;
    -fx-padding: 16px;
    -fx-border-color: #bbdefb;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
}

.intent-icon {
    -fx-font-size: 20px;
    -fx-text-fill: #1976d2;
}

.intent-category {
    -fx-font-size: 15px;
    -fx-font-weight: bold;
    -fx-fill: #1976d2;
}

.intent-confidence {
    -fx-font-size: 11px;
    -fx-font-weight: bold;
    -fx-background-color: #1976d2;
    -fx-text-fill: white;
    -fx-background-radius: 8px;
    -fx-padding: 2px 6px;
}

.intent-description {
    -fx-font-size: 13px;
    -fx-fill: #1976d2;
}

.execute-intent-button {
    -fx-background-color: #1976d2;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 8px 16px;
    -fx-font-size: 13px;
    -fx-cursor: hand;
}

.execute-intent-button:hover {
    -fx-background-color: #1565c0;
}

/* Action Buttons */
.confirm-voice-button {
    -fx-background-color: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 10px;
    -fx-border-radius: 10px;
    -fx-padding: 10px 16px;
    -fx-font-size: 13px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #28a745, 8, 0, 0, 2);
}

.confirm-voice-button:hover {
    -fx-background-color: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    -fx-effect: dropshadow(gaussian, #28a745, 10, 0, 0, 3);
}

.confirm-voice-button:disabled {
    -fx-background-color: #6c757d;
    -fx-opacity: 0.6;
    -fx-effect: none;
}

.edit-command-button {
    -fx-background-color: #17a2b8;
    -fx-text-fill: white;
    -fx-font-weight: 600;
    -fx-background-radius: 10px;
    -fx-border-radius: 10px;
    -fx-padding: 10px 16px;
    -fx-font-size: 13px;
    -fx-cursor: hand;
}

.edit-command-button:hover {
    -fx-background-color: #138496;
}

.try-again-voice-button {
    -fx-background-color: #ffc107;
    -fx-text-fill: #212529;
    -fx-font-weight: 600;
    -fx-background-radius: 10px;
    -fx-border-radius: 10px;
    -fx-padding: 10px 16px;
    -fx-font-size: 13px;
    -fx-cursor: hand;
}

.try-again-voice-button:hover {
    -fx-background-color: #e0a800;
}

.cancel-voice-button {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-font-weight: 600;
    -fx-background-radius: 10px;
    -fx-border-radius: 10px;
    -fx-padding: 10px 16px;
    -fx-font-size: 13px;
    -fx-cursor: hand;
}

.cancel-voice-button:hover {
    -fx-background-color: #c82333;
}

/* Suggestions Section */
.suggestions-section {
    -fx-background-color: #ffffffe6;
    -fx-background-radius: 16px;
    -fx-padding: 16px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.08), 8, 0, 0, 2);
}

.suggestions-icon {
    -fx-font-size: 16px;
    -fx-text-fill: #667eea;
}

.suggestions-title {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-fill: #495057;
}

.refresh-suggestions-button {
    -fx-background-color: #f8f9fa;
    -fx-text-fill: #6c757d;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-padding: 4px 8px;
    -fx-font-size: 12px;
    -fx-cursor: hand;
}

.refresh-suggestions-button:hover {
    -fx-background-color: #e9ecef;
}

.contextual-suggestion-button {
    -fx-background-color: white;
    -fx-text-fill: #495057;
    -fx-background-radius: 16px;
    -fx-border-radius: 16px;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-padding: 6px 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 500;
    -fx-cursor: hand;
}

.contextual-suggestion-button:hover {
    -fx-background-color: #667eea;
    -fx-text-fill: white;
    -fx-border-color: #667eea;
    -fx-effect: dropshadow(gaussian, #667eea, 6, 0, 0, 2);
}

/* Execution Result */
.execution-result {
    -fx-background-color: rgba(212, 237, 218, 0.9);
    -fx-background-radius: 12px;
    -fx-padding: 16px;
    -fx-border-color: rgba(195, 230, 203, 0.8);
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
}

.execution-icon {
    -fx-font-size: 20px;
    -fx-text-fill: #155724;
}

.execution-title {
    -fx-font-size: 15px;
    -fx-font-weight: bold;
    -fx-fill: #155724;
}

.execution-description {
    -fx-font-size: 13px;
    -fx-fill: #155724;
}

.view-result-button {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 8px 12px;
    -fx-font-size: 12px;
    -fx-cursor: hand;
}

.view-result-button:hover {
    -fx-background-color: #218838;
}

/* Error Display */
.error-display {
    -fx-background-color: rgba(248, 215, 218, 0.9);
    -fx-background-radius: 12px;
    -fx-padding: 16px;
    -fx-border-color: rgba(245, 198, 203, 0.8);
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
}

.error-icon {
    -fx-font-size: 20px;
    -fx-text-fill: #721c24;
}

.error-title {
    -fx-font-size: 15px;
    -fx-font-weight: bold;
    -fx-fill: #721c24;
}

.error-description {
    -fx-font-size: 13px;
    -fx-fill: #721c24;
}

.retry-error-button {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 8px 12px;
    -fx-font-size: 12px;
    -fx-cursor: hand;
}

.retry-error-button:hover {
    -fx-background-color: #c82333;
}

/* Quick Actions Bar */
.quick-actions-bar {
    -fx-background-color: rgba(255, 255, 255, 0.85);
    -fx-background-radius: 20px;
    -fx-padding: 12px 16px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.08), 8, 0, 0, 2);
}

.quick-action-button {
    -fx-background-color: #667eea;
    -fx-text-fill: #667eea;
    -fx-font-weight: 600;
    -fx-background-radius: 16px;
    -fx-border-radius: 16px;
    -fx-border-color: #667eea;
    -fx-border-width: 1px;
    -fx-padding: 8px 12px;
    -fx-font-size: 11px;
    -fx-cursor: hand;
}

.quick-action-button:hover {
    -fx-background-color: #667eea;
    -fx-text-fill: white;
    -fx-border-color: #667eea;
    -fx-effect: dropshadow(gaussian, #667eea, 6, 0, 0, 2);
}

/* Voice Activity Indicator */
.voice-activity-indicator {
    -fx-background-color: #ffffffe6;
    -fx-background-radius: 20px;
    -fx-padding: 8px 16px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.05), 5, 0, 0, 1);
}

.activity-dot {
    -fx-fill: #667eea;
}

.activity-text {
    -fx-font-size: 12px;
    -fx-fill: #6c757d;
    -fx-font-style: italic;
}

/* Responsive Design for Global Voice */
@media screen and (max-width: 768px) {
    .global-voice-container {
        -fx-padding: 16px;
    }

    .main-voice-button-container {
        -fx-min-width: 60px;
        -fx-min-height: 60px;
    }

    .main-voice-button {
        -fx-min-width: 50px;
        -fx-min-height: 50px;
        -fx-max-width: 50px;
        -fx-max-height: 50px;
    }

    .main-voice-icon {
        -fx-font-size: 20px;
    }

    .ai-assistant-title {
        -fx-font-size: 16px;
    }

    .quick-action-button {
        -fx-padding: 6px 8px;
        -fx-font-size: 10px;
    }
}

/* Animation Classes for Global Voice */
.global-voice-listening {
    -fx-effect: dropshadow(gaussian, #667eea, 25, 0, 0, 0);
}

.global-voice-processing {
    -fx-effect: dropshadow(gaussian, #f39c12, 25, 0, 0, 0);
}

.global-voice-success {
    -fx-effect: dropshadow(gaussian, rgba(40, 167, 69, 0.8), 25, 0, 0, 0);
}

.global-voice-error {
    -fx-effect: dropshadow(gaussian, #dc3545, 25, 0, 0, 0);
}

/* Global Voice Navigation Button */
.global-voice-nav {
    -fx-background-color: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-effect: dropshadow(gaussian, #667eea, 12, 0, 0, 3);
}

.global-voice-nav:hover {
    -fx-background-color: linear-gradient(135deg, #5a67d8 0%, #6b46c1 50%, #e879f9 100%);
    -fx-effect: dropshadow(gaussian, #667eea, 16, 0, 0, 5);
    -fx-translate-y: -2px;
}

/* 🤖 Modern AI Assistant Styles */

/* Main Container */
.modern-ai-container {
    -fx-background-color: white;
    -fx-background-radius: 16px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.08), 24, 0, 0, 4);
    -fx-border-color: #e5e7eb;
    -fx-border-width: 1px;
    -fx-border-radius: 16px;
    -fx-min-width: 600px;
    -fx-min-height: 500px;
}

/* Header Section */
.modern-header {
    -fx-background-color: white;
    -fx-background-radius: 16px 16px 0 0;
    -fx-padding: 24px 24px 20px 24px;
    -fx-border-color: #e5e7eb;
    -fx-border-width: 0 0 1px 0;
}

.modern-ai-icon {
    -fx-font-size: 24px;
    -fx-text-fill: #f97316;
    -fx-min-width: 32px;
    -fx-min-height: 32px;
    -fx-alignment: center;
}

.modern-title {
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-fill: #111827;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.modern-subtitle {
    -fx-font-size: 14px;
    -fx-fill: #6b7280;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.modern-control-button {
    -fx-background-color: transparent;
    -fx-text-fill: #6b7280;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-border-color: transparent;
    -fx-border-width: 1px;
    -fx-padding: 8px;
    -fx-font-size: 16px;
    -fx-cursor: hand;
}

.modern-control-button:hover {
    -fx-background-color: rgba(243, 244, 246, 1);
    -fx-text-fill: #374151;
}

/* Chat Area */
.modern-chat-scroll {
    -fx-background-color: transparent;
    -fx-background: transparent;
}

.modern-chat-scroll .viewport {
    -fx-background-color: transparent;
}

.modern-chat-scroll .scroll-bar {
    -fx-background-color: #e5e7eb;
    -fx-background-radius: 4px;
}

/* Functional scrollbar that actually scrolls - based on your specifications but JavaFX compatible */
.notification-scroll-functional .scroll-bar:vertical {
    -fx-background-color: #f2f2f2;
    -fx-pref-width: 15px;
    -fx-max-width: 15px;
    -fx-min-width: 15px;
    -fx-background-radius: 0px;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 1px;
}

/* Track: light grey background that allows thumb movement */
.notification-scroll-functional .scroll-bar:vertical .track {
    -fx-background-color: #f2f2f2;
    -fx-background-radius: 0px;
    -fx-border-color: transparent;
    -fx-background-insets: 0;
    -fx-opacity: 1.0;
}

/* Thumb: small, light grey, moves proportionally with content */
.notification-scroll-functional .scroll-bar:vertical .thumb {
    -fx-background-color: #cccccc;
    -fx-background-radius: 3px;
    -fx-border-color: transparent;
    -fx-background-insets: 1;
    -fx-opacity: 1.0;
    -fx-min-height: 20px;
    -fx-pref-height: 30px;
}

/* Hover effect: darker grey on hover */
.notification-scroll-functional .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #999999;
}

.notification-scroll-functional .scroll-bar:vertical .thumb:pressed {
    -fx-background-color: #777777;
}

/* Up and Down Arrows: functional buttons */
.notification-scroll-functional .scroll-bar:vertical .increment-button,
.notification-scroll-functional .scroll-bar:vertical .decrement-button {
    -fx-background-color: #e0e0e0;
    -fx-background-radius: 0px;
    -fx-border-color: #cccccc;
    -fx-border-width: 1px;
    -fx-background-insets: 0;
    -fx-pref-height: 15px;
    -fx-max-height: 15px;
    -fx-min-height: 15px;
}

.notification-scroll-functional .scroll-bar:vertical .increment-button:hover,
.notification-scroll-functional .scroll-bar:vertical .decrement-button:hover {
    -fx-background-color: #d0d0d0;
}

.notification-scroll-functional .scroll-bar:vertical .increment-button:pressed,
.notification-scroll-functional .scroll-bar:vertical .decrement-button:pressed {
    -fx-background-color: #c0c0c0;
}

/* Arrow icons */
.notification-scroll-functional .scroll-bar:vertical .increment-arrow {
    -fx-background-color: #666666;
    -fx-padding: 1px;
    -fx-shape: "M 2 1 L 6 5 L 10 1 Z";
}

.notification-scroll-functional .scroll-bar:vertical .decrement-arrow {
    -fx-background-color: #666666;
    -fx-padding: 1px;
    -fx-shape: "M 2 5 L 6 1 L 10 5 Z";
}

.modern-chat-container {
    -fx-padding: 24px;
    -fx-background-color: transparent;
}

/* Message Containers */
.modern-message-container {
    -fx-spacing: 12px;
    -fx-padding: 0 0 16px 0;
    -fx-alignment: CENTER_LEFT;
}

.modern-avatar-container {
    -fx-min-width: 32px;
    -fx-min-height: 32px;
    -fx-max-width: 32px;
    -fx-max-height: 32px;
}

.modern-message-avatar {
    -fx-font-size: 18px;
    -fx-text-fill: #f97316;
    -fx-min-width: 32px;
    -fx-min-height: 32px;
    -fx-alignment: center;
    -fx-background-color: rgba(249, 115, 22, 0.1);
    -fx-background-radius: 16px;
    -fx-border-radius: 16px;
}

/* Message Bubbles */
.modern-message-bubble {
    -fx-background-color: rgba(254, 243, 199, 1);
    -fx-background-radius: 12px;
    -fx-padding: 16px;
    -fx-max-width: 500px;
    -fx-border-color: rgba(251, 191, 36, 0.2);
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
}

.modern-message-text {
    -fx-font-size: 14px;
    -fx-fill: #374151;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-line-spacing: 2px;
}

.user-message-bubble {
    -fx-background-color: #3b82f6;
    -fx-background-radius: 12px;
    -fx-padding: 16px;
    -fx-max-width: 500px;
}

.user-message-text {
    -fx-font-size: 14px;
    -fx-fill: white;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-line-spacing: 2px;
}

/* Capability Tags */
.capability-tags {
    -fx-padding: 8px 0 0 0;
}

.capability-tag {
    -fx-background-color: #667eea;
    -fx-text-fill: #667eea;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-border-color: #667eea;
    -fx-border-width: 1px;
    -fx-padding: 4px 8px;
    -fx-font-size: 11px;
    -fx-font-weight: 600;
}

.dark-theme .capability-tag {
    -fx-background-color: #667eea;
    -fx-border-color: #667eea;
}

/* Visual Response Elements */
.visual-response-container {
    -fx-padding: 12px 0 0 0;
    -fx-border-color: #cbd5e1;
    -fx-border-width: 1px 0 0 0;
}

.chart-placeholder, .table-placeholder {
    -fx-background-color: rgba(102, 126, 234, 0.05);
    -fx-text-fill: #667eea;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-border-color: #667eea;
    -fx-border-width: 1px;
    -fx-padding: 12px;
    -fx-font-size: 13px;
    -fx-font-weight: 500;
}

.dark-theme .chart-placeholder,
.dark-theme .table-placeholder {
    -fx-background-color: #667eea;
    -fx-border-color: #667eea;
}

.response-action-buttons {
    -fx-padding: 8px 0 0 0;
}

.response-action-button {
    -fx-background-color: #667eea;
    -fx-text-fill: white;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 6px 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-cursor: hand;
}

.response-action-button:hover {
    -fx-background-color: #5a67d8;
    -fx-effect: dropshadow(gaussian, #667eea, 6, 0, 0, 2);
}

/* Quick Suggestions Bar */
.quick-suggestions-bar {
    -fx-background-color: #ffffffcc;
    -fx-padding: 12px 20px;
    -fx-border-color: #cbd5e1;
    -fx-border-width: 1px 0;
}

.dark-theme .quick-suggestions-bar {
    -fx-background-color: rgba(30, 41, 59, 0.8);
    -fx-border-color: rgba(71, 85, 105, 0.5);
}

.suggestions-icon {
    -fx-font-size: 16px;
    -fx-text-fill: #667eea;
    -fx-min-width: 20px;
}

.suggestions-scroll {
    -fx-background-color: transparent;
    -fx-background: transparent;
}

.suggestions-container {
    -fx-alignment: CENTER_LEFT;
}

.suggestion-chip {
    -fx-background-color: #667eea;
    -fx-text-fill: #667eea;
    -fx-background-radius: 16px;
    -fx-border-radius: 16px;
    -fx-border-color: #667eea;
    -fx-border-width: 1px;
    -fx-padding: 6px 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 500;
    -fx-cursor: hand;
}

.suggestion-chip:hover {
    -fx-background-color: #667eea;
    -fx-text-fill: white;
    -fx-border-color: #667eea;
    -fx-effect: dropshadow(gaussian, #667eea, 6, 0, 0, 2);
}

.dark-theme .suggestion-chip {
    -fx-background-color: #667eea;
    -fx-border-color: #667eea;
}

/* Auto-suggestions */
.auto-suggestions-container {
    -fx-background-color: rgba(255, 255, 255, 0.98);
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, #262626, 12, 0, 0, 4);
    -fx-border-color: #cbd5e1;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-padding: 8px;
}

.dark-theme .auto-suggestions-container {
    -fx-background-color: rgba(30, 41, 59, 0.98);
    -fx-border-color: rgba(71, 85, 105, 0.8);
}

.auto-suggestions-list {
    -fx-background-color: transparent;
    -fx-background-radius: 8px;
    -fx-border-color: transparent;
}

.auto-suggestions-list .list-cell {
    -fx-background-color: transparent;
    -fx-text-fill: #334155;
    -fx-padding: 8px 12px;
    -fx-background-radius: 6px;
    -fx-font-size: 13px;
}

.auto-suggestions-list .list-cell:hover {
    -fx-background-color: #667eea;
}

.auto-suggestions-list .list-cell:selected {
    -fx-background-color: #667eea;
    -fx-text-fill: white;
}

.dark-theme .auto-suggestions-list .list-cell {
    -fx-text-fill: #e2e8f0;
}

/* Input Area */
.modern-input-area {
    -fx-background-color: white;
    -fx-background-radius: 0 0 16px 16px;
    -fx-padding: 20px 24px 24px 24px;
    -fx-border-color: #e5e7eb;
    -fx-border-width: 1px 0 0 0;
}

.modern-input-row {
    -fx-alignment: CENTER_LEFT;
    -fx-spacing: 12px;
}

.modern-message-input {
    -fx-background-color: white;
    -fx-text-fill: #111827;
    -fx-background-radius: 12px;
    -fx-border-color: rgba(209, 213, 219, 1);
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-padding: 12px 16px;
    -fx-font-size: 14px;
    -fx-prompt-text-fill: #9ca3af;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.modern-message-input:focused {
    -fx-border-color: #3b82f6;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, #3b82f6, 4, 0, 0, 0);
}

.voice-button-stack {
    -fx-min-width: 44px;
    -fx-min-height: 44px;
}

.voice-pulse {
    -fx-fill: transparent;
    -fx-stroke: #667eea;
    -fx-stroke-width: 2px;
}

.voice-input-button {
    -fx-background-color: #667eea;
    -fx-background-radius: 22px;
    -fx-border-radius: 22px;
    -fx-min-width: 40px;
    -fx-min-height: 40px;
    -fx-max-width: 40px;
    -fx-max-height: 40px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #667eea, 8, 0, 0, 2);
}

.voice-input-button:hover {
    -fx-background-color: #5a67d8;
    -fx-effect: dropshadow(gaussian, #667eea, 10, 0, 0, 3);
    -fx-translate-y: -1px;
}

.voice-icon {
    -fx-font-size: 16px;
    -fx-text-fill: white;
}

.modern-send-button {
    -fx-background-color: #f97316;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-min-width: 40px;
    -fx-min-height: 40px;
    -fx-max-width: 40px;
    -fx-max-height: 40px;
    -fx-cursor: hand;
}

.modern-send-button:hover {
    -fx-background-color: #ea580c;
}

.modern-send-button:disabled {
    -fx-background-color: #d1d5db;
    -fx-opacity: 0.6;
}

.modern-send-icon {
    -fx-font-size: 16px;
    -fx-text-fill: white;
}

/* Input Status Bar */
.input-status-bar {
    -fx-background-color: #667eea;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-border-color: #667eea;
    -fx-border-width: 1px;
    -fx-padding: 8px 12px;
}

.input-status-icon {
    -fx-font-size: 14px;
    -fx-text-fill: #667eea;
}

.input-status-text {
    -fx-font-size: 13px;
    -fx-fill: #667eea;
    -fx-font-weight: 500;
}

.cancel-input-button {
    -fx-background-color: #f87171;
    -fx-text-fill: #ef4444;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-border-color: #f87171;
    -fx-border-width: 1px;
    -fx-padding: 4px 8px;
    -fx-font-size: 12px;
    -fx-cursor: hand;
}

.cancel-input-button:hover {
    -fx-background-color: #f87171;
}

/* Action Buttons Area */
.action-buttons-area {
    -fx-background-color: rgba(248, 250, 252, 0.6);
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-border-color: #cbd5e1;
    -fx-border-width: 1px;
    -fx-padding: 12px 16px;
}

.dark-theme .action-buttons-area {
    -fx-background-color: rgba(51, 65, 85, 0.6);
    -fx-border-color: rgba(71, 85, 105, 0.4);
}

.action-button {
    -fx-background-color: #64748b;
    -fx-text-fill: #64748b;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-border-color: #64748b;
    -fx-border-width: 1px;
    -fx-padding: 6px 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 500;
    -fx-cursor: hand;
}

.action-button:hover {
    -fx-background-color: #64748b;
    -fx-border-color: #64748b;
}

.dark-theme .action-button {
    -fx-background-color: #94a3b8;
    -fx-text-fill: #94a3b8;
    -fx-border-color: #94a3b8;
}

.dark-theme .action-button:hover {
    -fx-background-color: #94a3b8;
    -fx-border-color: #94a3b8;
}

/* Responsive Design for Smart AI Assistant */
@media screen and (max-width: 768px) {
    .smart-assistant-container {
        -fx-min-width: 400px;
        -fx-padding: 12px;
    }

    .assistant-header {
        -fx-padding: 12px 16px;
    }

    .chat-messages-container {
        -fx-padding: 12px 16px;
    }

    .input-area {
        -fx-padding: 12px 16px;
    }

    .message-bubble {
        -fx-max-width: 300px;
    }

    .assistant-name {
        -fx-font-size: 16px;
    }

    .message-text {
        -fx-font-size: 13px;
    }
}

/* Animation Classes for Smart AI Assistant */
.assistant-thinking {
    -fx-effect: dropshadow(gaussian, #667eea, 15, 0, 0, 0);
}

.assistant-speaking {
    -fx-effect: dropshadow(gaussian, #10b981, 15, 0, 0, 0);
}

.assistant-listening {
    -fx-effect: dropshadow(gaussian, #f59e0b, 15, 0, 0, 0);
}

.assistant-error {
    -fx-effect: dropshadow(gaussian, #f87171, 15, 0, 0, 0);
}

/* Smart Assistant Navigation Button */
.smart-assistant-nav {
    -fx-background-color: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-effect: dropshadow(gaussian, #10b981, 12, 0, 0, 3);
}

.smart-assistant-nav:hover {
    -fx-background-color: linear-gradient(135deg, #059669 0%, #047857 50%, #065f46 100%);
    -fx-effect: dropshadow(gaussian, #10b981, 16, 0, 0, 5);
    -fx-translate-y: -2px;
}

/* Floating AI Assistant Button */
.floating-ai-button {
    -fx-background-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -fx-background-radius: 30px;
    -fx-border-radius: 30px;
    -fx-min-width: 120px;
    -fx-min-height: 60px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #667eea, 15, 0, 0, 5);
    -fx-alignment: center;
}

.floating-ai-button:hover {
    -fx-background-color: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    -fx-effect: dropshadow(gaussian, #667eea, 20, 0, 0, 8);
    -fx-translate-y: -3px;
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.floating-ai-icon {
    -fx-font-size: 20px;
    -fx-text-fill: white;
}

.floating-ai-text {
    -fx-font-size: 11px;
    -fx-font-weight: bold;
    -fx-text-fill: white;
}

.metrics-container {
    -fx-padding: 0px;
}

.metric-card {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.08), 8, 0, 0, 2);
    -fx-padding: 20px;
    -fx-spacing: 8px;
    -fx-min-width: 200px;
    -fx-pref-width: 200px;
}

.metric-card:hover {
    -fx-effect: dropshadow(gaussian, #262626, 12, 0, 0, 4);
    -fx-translate-y: -2px;
}

.metric-label {
    -fx-font-size: 12px;
    -fx-fill: #7f8c8d;
    -fx-font-weight: 500;
}

.metric-value {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-fill: #2c3e50;
}

.metric-change {
    -fx-font-size: 11px;
    -fx-fill: #27ae60;
    -fx-font-weight: 500;
}

.metric-icon-positive {
    -fx-fill: #27ae60;
}

.metric-icon-negative {
    -fx-fill: #e74c3c;
}

.metric-icon-neutral {
    -fx-fill: #3498db;
}

.chart-section {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 10, 0, 0, 2);
    -fx-padding: 25px;
    -fx-spacing: 20px;
}

.forecast-chart {
    -fx-background-color: transparent;
    -fx-padding: 10px;
}

.forecast-chart .chart-plot-background {
    -fx-background-color: #fafbfc;
}

.forecast-chart .chart-vertical-grid-lines {
    -fx-stroke: #e1e8ed;
    -fx-stroke-width: 0.5px;
}

.forecast-chart .chart-horizontal-grid-lines {
    -fx-stroke: #e1e8ed;
    -fx-stroke-width: 0.5px;
}

.forecast-chart .chart-series-line {
    -fx-stroke-width: 3px;
}

.forecast-chart .default-color0.chart-series-line {
    -fx-stroke: #3498db;
}

.forecast-chart .default-color1.chart-series-line {
    -fx-stroke: #e74c3c;
    -fx-stroke-dash-array: 5 5;
}

.view-toggle {
    -fx-background-color: #ecf0f1;
    -fx-text-fill: #5a6c7d;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-padding: 6px 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 500;
}

.view-toggle:selected {
    -fx-background-color: #3498db;
    -fx-text-fill: white;
}

.view-toggle:hover {
    -fx-background-color: #d5dbdb;
}

.view-toggle:selected:hover {
    -fx-background-color: #2980b9;
}

.chart-legend {
    -fx-background-color: #f8f9fa;
    -fx-background-radius: 6px;
    -fx-padding: 12px;
}

.legend-color-historical {
    -fx-background-color: #3498db;
    -fx-min-width: 16px;
    -fx-min-height: 3px;
    -fx-background-radius: 2px;
}

.legend-color-forecast {
    -fx-background-color: #e74c3c;
    -fx-min-width: 16px;
    -fx-min-height: 3px;
    -fx-background-radius: 2px;
}

.legend-color-confidence {
    -fx-background-color: rgba(231, 76, 60, 0.3);
    -fx-min-width: 16px;
    -fx-min-height: 3px;
    -fx-background-radius: 2px;
}

.legend-text {
    -fx-font-size: 12px;
    -fx-fill: #5a6c7d;
    -fx-font-weight: 500;
}

.loading-container {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 10, 0, 0, 2);
    -fx-padding: 40px;
}

.loading-text {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-fill: #2c3e50;
}

.loading-subtext {
    -fx-font-size: 13px;
    -fx-fill: #7f8c8d;
}

.empty-state {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 10, 0, 0, 2);
    -fx-padding: 60px 40px;
}

.empty-state-icon {
    -fx-fill: #bdc3c7;
}

.empty-state-title {
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-fill: #2c3e50;
}

.empty-state-subtitle {
    -fx-font-size: 14px;
    -fx-fill: #7f8c8d;
    -fx-text-alignment: center;
    -fx-wrap-text: true;
}

/* ===== ORDER MANAGEMENT INTERACTIVE STYLES ===== */

/* Add Item Dialog Styles */
.dialog-overlay {
    -fx-background-color: #808080;
    -fx-alignment: center;
    -fx-padding: 20px;
}

.dialog-content {
    -fx-background-color: white;
    -fx-background-radius: 15px;
    -fx-padding: 0px;
    -fx-effect: dropshadow(gaussian, #4d4d4d, 10, 0, 0, 5);
    -fx-max-width: 500px;
    -fx-min-width: 450px;
}

.dialog-header {
    -fx-background-color: #007bff;
    -fx-background-radius: 15px 15px 0px 0px;
    -fx-padding: 15px 20px;
}

.dialog-title {
    -fx-text-fill: white;
    -fx-font-size: 16px;
    -fx-font-weight: bold;
}

.close-button {
    -fx-background-color: transparent;
    -fx-text-fill: white;
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-border-color: transparent;
    -fx-cursor: hand;
}

.close-button:hover {
    -fx-background-color: #ffffff33;
    -fx-background-radius: 3px;
}

.dialog-body {
    -fx-padding: 20px;
}

.dialog-actions {
    -fx-padding: 15px 20px 20px 20px;
}

.item-combo {
    -fx-pref-height: 35px;
    -fx-font-size: 14px;
}

.quantity-field, .price-field, .discount-field {
    -fx-pref-height: 35px;
    -fx-font-size: 14px;
    -fx-max-width: 120px;
}

.notes-field {
    -fx-font-size: 14px;
}

.total-display {
    -fx-background-color: #f8f9fa;
    -fx-padding: 10px;
    -fx-background-radius: 5px;
    -fx-alignment: center-left;
}

.total-label {
    -fx-font-weight: bold;
    -fx-font-size: 14px;
}

.total-amount {
    -fx-font-weight: bold;
    -fx-font-size: 16px;
    -fx-text-fill: #28a745;
}

/* Action Buttons in Table */
.action-button-small {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-font-size: 14px;
    -fx-cursor: hand;
    -fx-padding: 2px 5px;
    -fx-background-radius: 3px;
}

.action-button-small:hover {
    -fx-background-color: #f8f9fa;
}

/* ===== REPORTS LAYOUT STYLES ===== */

/* Main Reports Container */
.reports-scroll-pane {
    -fx-background-color: #f8f9fa;
    -fx-background: #f8f9fa;
}

.reports-container {
    -fx-background-color: #f8f9fa;
    -fx-padding: 0px;
    -fx-spacing: 30px;
}

/* Header Section */
.reports-header-section {
    -fx-background-color: white;
    -fx-padding: 30px 40px;
    -fx-spacing: 25px;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 8, 0, 0, 2);
}

.reports-title-row {
    -fx-padding: 0px 0px 15px 0px;
    -fx-border-color: transparent transparent #e9ecef transparent;
    -fx-border-width: 0px 0px 2px 0px;
}

.page-title {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
    -fx-font-size: 28px;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.nav-icons-container {
    -fx-spacing: 12px;
}

.nav-icon-button {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 10px 12px;
    -fx-font-size: 16px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #1a1a1a, 2, 0, 0, 1);
}

.nav-icon-button:hover {
    -fx-background-color: #e9ecef;
    -fx-border-color: #adb5bd;
    -fx-effect: dropshadow(gaussian, #262626, 4, 0, 0, 2);
}

.reports-filters-row {
    -fx-padding: 15px 0px 0px 0px;
    -fx-spacing: 15px;
}

.filter-label {
    -fx-text-fill: #495057;
    -fx-font-weight: 600;
    -fx-font-size: 14px;
    -fx-alignment: center-left;
    -fx-min-width: 80px;
}

.filter-separator {
    -fx-text-fill: #6c757d;
    -fx-font-size: 14px;
    -fx-alignment: center;
}

.date-picker-field {
    -fx-pref-width: 150px;
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 8px 12px;
    -fx-font-size: 14px;
}

.date-picker-field:focused {
    -fx-border-color: #007bff;
    -fx-effect: dropshadow(gaussian, #007bff, 4, 0, 0, 0);
}

.action-buttons-container {
    -fx-spacing: 12px;
}

.primary-action-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-border-color: transparent;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 12px 20px;
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #007bff, 4, 0, 0, 2);
}

.primary-action-button:hover {
    -fx-background-color: #0056b3;
    -fx-effect: dropshadow(gaussian, #007bff, 6, 0, 0, 3);
}

.secondary-action-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-border-color: transparent;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 12px 20px;
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(108,117,125,0.3), 4, 0, 0, 2);
}

.secondary-action-button:hover {
    -fx-background-color: #545b62;
    -fx-effect: dropshadow(gaussian, rgba(108,117,125,0.4), 6, 0, 0, 3);
}

/* Summary Section */
.summary-section {
    -fx-background-color: transparent;
    -fx-padding: 0px 40px;
    -fx-spacing: 25px;
}

.section-header {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
    -fx-font-size: 20px;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-padding: 0px 0px 15px 0px;
}

.summary-cards-grid {
    -fx-padding: 0px;
}

.summary-card {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-background-radius: 12px;
    -fx-padding: 25px 20px;
    -fx-spacing: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);
    -fx-alignment: center;
}

.summary-card:hover {
    -fx-effect: dropshadow(gaussian, #262626, 12, 0, 0, 4);
    -fx-translate-y: -2px;
}

.card-icon {
    -fx-font-size: 32px;
    -fx-text-fill: #007bff;
}

.card-title {
    -fx-text-fill: #6c757d;
    -fx-font-weight: 600;
    -fx-font-size: 14px;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.card-value {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
    -fx-font-size: 24px;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

/* Reports Grid Section */
.reports-grid-section {
    -fx-background-color: transparent;
    -fx-padding: 0px 40px 40px 40px;
    -fx-spacing: 25px;
}

.reports-grid {
    -fx-padding: 0px;
}

.report-card {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-background-radius: 12px;
    -fx-padding: 20px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 8, 0, 0, 2);
}

.report-card:hover {
    -fx-effect: dropshadow(gaussian, #262626, 12, 0, 0, 4);
}

.report-card-header {
    -fx-padding: 0px 0px 15px 0px;
    -fx-border-color: transparent transparent #f1f3f4 transparent;
    -fx-border-width: 0px 0px 1px 0px;
}

.report-icon {
    -fx-font-size: 20px;
    -fx-text-fill: #007bff;
}

.report-title {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
    -fx-font-size: 16px;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.export-button-small {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 6px 8px;
    -fx-font-size: 12px;
    -fx-cursor: hand;
}

.export-button-small:hover {
    -fx-background-color: #e9ecef;
    -fx-border-color: #adb5bd;
}

.refresh-button-small {
    -fx-background-color: #e3f2fd;
    -fx-border-color: #90caf9;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 6px 8px;
    -fx-font-size: 12px;
    -fx-cursor: hand;
}

.refresh-button-small:hover {
    -fx-background-color: #bbdefb;
    -fx-border-color: #64b5f6;
}

/* Table Styles */
.table-scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-padding: 0px;
}

.report-table {
    -fx-background-color: white;
    -fx-border-color: transparent;
    -fx-table-cell-border-color: #f1f3f4;
    -fx-font-size: 13px;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.report-table .column-header {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-text-fill: #495057;
    -fx-font-weight: 600;
    -fx-font-size: 12px;
    -fx-padding: 12px 8px;
}

.report-table .table-row-cell {
    -fx-background-color: white;
    -fx-border-color: transparent transparent #f1f3f4 transparent;
    -fx-border-width: 0px 0px 1px 0px;
    -fx-padding: 8px;
}

.report-table .table-row-cell:hover {
    -fx-background-color: #f8f9fa;
}

.report-table .table-row-cell:selected {
    -fx-background-color: #e3f2fd;
    -fx-text-fill: #1976d2;
}

.report-table .table-cell {
    -fx-padding: 8px;
    -fx-alignment: center-left;
    -fx-text-fill: #495057;
}

/* Footer Styles */
.report-card-footer {
    -fx-padding: 15px 0px 0px 0px;
    -fx-border-color: #f1f3f4 transparent transparent transparent;
    -fx-border-width: 1px 0px 0px 0px;
}

.export-button {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-border-color: transparent;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 8px 16px;
    -fx-font-size: 13px;
    -fx-font-weight: 600;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, #28a745, 3, 0, 0, 1);
}

.export-button:hover {
    -fx-background-color: #218838;
    -fx-effect: dropshadow(gaussian, #28a745, 4, 0, 0, 2);
}

.refresh-button {
    -fx-background-color: #17a2b8;
    -fx-text-fill: white;
    -fx-border-color: transparent;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 8px 16px;
    -fx-font-size: 13px;
    -fx-font-weight: 600;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(23,162,184,0.3), 3, 0, 0, 1);
}

.refresh-button:hover {
    -fx-background-color: #138496;
    -fx-effect: dropshadow(gaussian, rgba(23,162,184,0.4), 4, 0, 0, 2);
}

/* Responsive Design for Reports */
@media screen and (max-width: 1200px) {
    .summary-cards-grid {
        -fx-hgap: 15px;
        -fx-vgap: 15px;
    }

    .reports-grid {
        -fx-hgap: 20px;
        -fx-vgap: 20px;
    }

    .reports-header-section {
        -fx-padding: 25px 30px;
    }

    .summary-section {
        -fx-padding: 0px 30px;
    }

    .reports-grid-section {
        -fx-padding: 0px 30px 30px 30px;
    }
}

@media screen and (max-width: 768px) {
    .reports-filters-row {
        -fx-spacing: 10px;
    }

    .date-picker-field {
        -fx-pref-width: 120px;
    }

    .action-buttons-container {
        -fx-spacing: 8px;
    }

    .primary-action-button, .secondary-action-button {
        -fx-padding: 10px 16px;
        -fx-font-size: 13px;
    }
}

/* Keyboard Shortcuts Styles */
.shortcuts-container {
    -fx-background-color: white;
    -fx-padding: 20px;
}

.shortcut-section {
    -fx-background-color: #F8F9FA;
    -fx-padding: 15px;
    -fx-background-radius: 8px;
    -fx-border-color: #E5E7EB;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
}

.shortcut-key {
    -fx-background-color: #374151;
    -fx-text-fill: white;
    -fx-padding: 5px 10px;
    -fx-background-radius: 4px;
    -fx-font-family: "Courier New", monospace;
    -fx-font-weight: bold;
    -fx-font-size: 12px;
    -fx-min-width: 100px;
    -fx-alignment: center;
}

.shortcut-desc {
    -fx-text-fill: #374151;
    -fx-font-size: 14px;
    -fx-padding: 5px 0;
}

.tips-section {
    -fx-background-color: #EFF6FF;
    -fx-padding: 15px;
    -fx-background-radius: 8px;
    -fx-border-color: #3B82F6;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
}

.tip-item {
    -fx-text-fill: #1E40AF;
    -fx-font-size: 13px;
    -fx-padding: 2px 0;
}

.shortcuts-scroll {
    -fx-background-color: white;
    -fx-border-color: transparent;
}

.shortcuts-help {
    -fx-background-color: #F3F4F6;
    -fx-padding: 10px;
    -fx-background-radius: 6px;
    -fx-border-color: #D1D5DB;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
}

.shortcuts-title {
    -fx-text-fill: #374151;
    -fx-font-weight: bold;
}

.shortcut-item {
    -fx-text-fill: #6B7280;
    -fx-font-size: 12px;
}

/* Action Button Styles for Order Management */
.action-button {
    -fx-padding: 10px 15px;
    -fx-background-radius: 6px;
    -fx-font-weight: bold;
    -fx-font-size: 12px;
    -fx-cursor: hand;
    -fx-min-width: 120px;
}

.new-order-btn {
    -fx-background-color: #10B981;
    -fx-text-fill: white;
}

.new-order-btn:hover {
    -fx-background-color: #059669;
}

.hold-kot-btn {
    -fx-background-color: #F59E0B;
    -fx-text-fill: white;
}

.hold-kot-btn:hover {
    -fx-background-color: #D97706;
}

.print-kot-btn {
    -fx-background-color: #3B82F6;
    -fx-text-fill: white;
}

.print-kot-btn:hover {
    -fx-background-color: #2563EB;
}

.settle-bill-btn {
    -fx-background-color: #EF4444;
    -fx-text-fill: white;
}

.settle-bill-btn:hover {
    -fx-background-color: #DC2626;
}

.discount-btn {
    -fx-background-color: #8B5CF6;
    -fx-text-fill: white;
}

.discount-btn:hover {
    -fx-background-color: #7C3AED;
}

.help-button {
    -fx-background-color: #6B7280;
    -fx-text-fill: white;
    -fx-padding: 8px 12px;
    -fx-background-radius: 4px;
    -fx-font-size: 12px;
}

.help-button:hover {
    -fx-background-color: #4B5563;
}

.close-button {
    -fx-background-color: #6B7280;
    -fx-text-fill: white;
    -fx-padding: 10px 20px;
    -fx-background-radius: 6px;
    -fx-font-weight: bold;
}

.close-button:hover {
    -fx-background-color: #4B5563;
}

.print-button {
    -fx-background-color: #3B82F6;
    -fx-text-fill: white;
    -fx-padding: 10px 20px;
    -fx-background-radius: 6px;
    -fx-font-weight: bold;
}

.print-button:hover {
    -fx-background-color: #2563EB;
}

/* ===== TABLE VIEW STYLES ===== */
.table-node {
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-border-width: 2px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

.table-node:hover {
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 6, 0, 0, 3);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.blank-table {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
}

.running-table {
    -fx-background-color: #fff3cd;
    -fx-border-color: #ffc107;
}

.printed-table {
    -fx-background-color: #d1ecf1;
    -fx-border-color: #17a2b8;
}

.paid-table {
    -fx-background-color: #d4edda;
    -fx-border-color: #28a745;
}

.running-kot-table {
    -fx-background-color: #f8d7da;
    -fx-border-color: #dc3545;
}

.table-number {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.table-status {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-text-alignment: center;
}

.table-amount {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #28a745;
}

.control-panel {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1px 0;
}

.action-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-padding: 8 16;
    -fx-font-size: 12px;
}

.action-button:hover {
    -fx-background-color: #0056b3;
}

.delivery-toggle {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-padding: 8 16;
}

.delivery-toggle:selected {
    -fx-background-color: #c82333;
}

.pickup-toggle {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-padding: 8 16;
}

.pickup-toggle:selected {
    -fx-background-color: #1e7e34;
}

.delivery-toggle-active {
    -fx-background-color: #c82333;
    -fx-text-fill: white;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-padding: 8 16;
    -fx-font-weight: bold;
}

.pickup-toggle-active {
    -fx-background-color: #1e7e34;
    -fx-text-fill: white;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-padding: 8 16;
    -fx-font-weight: bold;
}

.status-legend {
    -fx-background-color: #ffffff;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1px 0;
}

.legend-title {
    -fx-font-weight: bold;
    -fx-font-size: 14px;
    -fx-text-fill: #495057;
}

.legend-text {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
}

.legend-indicator {
    -fx-border-radius: 3px;
    -fx-background-radius: 3px;
    -fx-border-width: 1px;
}

.floor-selection {
    -fx-background-color: #ffffff;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1px 0;
}

.floor-title {
    -fx-text-fill: #495057;
}

.tables-scroll-pane {
    -fx-background-color: #ffffff;
}

.tables-grid {
    -fx-background-color: #ffffff;
    -fx-padding: 20;
}

.control-label {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-font-weight: bold;
}

/* Menu Management Responsive Styles - Applied via Java code */
.menu-item-card.compact {
    -fx-min-width: 120px;
    -fx-pref-width: 140px;
    -fx-max-width: 160px;
    -fx-padding: 10px;
}

.menu-item-card.small {
    -fx-min-width: 100px;
    -fx-pref-width: 120px;
    -fx-max-width: 140px;
    -fx-padding: 8px;
}

.menu-grid.compact {
    -fx-hgap: 12px;
    -fx-vgap: 12px;
}

.menu-grid.small {
    -fx-hgap: 10px;
    -fx-vgap: 10px;
}




