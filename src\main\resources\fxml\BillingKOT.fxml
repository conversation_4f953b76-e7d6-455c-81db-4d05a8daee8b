<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.BillingKOTController" stylesheets="@../css/application.css">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="module-header">
         <children>
            <Label styleClass="module-title" text="🧾 Billing &amp; KOT Management">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Button mnemonicParsing="false" onAction="#openSettings" styleClass="secondary-button" text="⚙️ Settings" />
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </HBox>

      <!-- Table View Section -->
      <VBox spacing="15.0" styleClass="table-view-section">
         <children>
            <!-- Table View Header -->
            <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="table-view-header">
               <children>
                  <Label styleClass="section-title" text="Table View">
                     <font>
                        <Font name="System Bold" size="18.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button mnemonicParsing="false" onAction="#refreshTables" styleClass="refresh-button" text="🔄" />
                  <ToggleButton fx:id="deliveryToggle" mnemonicParsing="false" onAction="#toggleDeliveryMode" styleClass="delivery-toggle" text="Delivery" />
                  <ToggleButton fx:id="pickUpToggle" mnemonicParsing="false" onAction="#togglePickUpMode" styleClass="pickup-toggle" text="Pick Up" />
                  <Button mnemonicParsing="false" onAction="#addTable" styleClass="add-button" text="+ Add Table" />
               </children>
            </HBox>

            <!-- Control Panel -->
            <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="control-panel">
               <children>
                  <Button fx:id="tableReservationBtn" mnemonicParsing="false" onAction="#openTableReservation" styleClass="action-button" text="+ Table Reservation" />
                  <Button fx:id="contestationsBtn" mnemonicParsing="false" onAction="#openContestations" styleClass="action-button" text="+ Contestations" />

                  <Region HBox.hgrow="ALWAYS" />

                  <!-- Move KOT/Items Controls -->
                  <VBox spacing="5.0" alignment="CENTER">
                     <children>
                        <Label text="Move KOT / Items" styleClass="control-label" />
                        <HBox spacing="10.0" alignment="CENTER">
                           <children>
                              <ComboBox fx:id="fromTableCombo" promptText="From Table" prefWidth="120.0" />
                              <Label text="→" />
                              <ComboBox fx:id="toTableCombo" promptText="To Table" prefWidth="120.0" />
                              <Button fx:id="moveKOTBtn" mnemonicParsing="false" onAction="#moveKOT" styleClass="secondary-button" text="Move" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="15.0" left="0.0" right="0.0" top="15.0" />
               </padding>
            </HBox>

            <!-- Table Status Legend -->
            <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="table-legend">
               <children>
                  <HBox alignment="CENTER_LEFT" spacing="5.0">
                     <children>
                        <Label styleClass="legend-dot blank-table" text="□" prefWidth="25" prefHeight="20" />
                        <Label text="Blank Table" />
                     </children>
                  </HBox>
                  <HBox alignment="CENTER_LEFT" spacing="5.0">
                     <children>
                        <Label styleClass="legend-dot running-table" text="■" prefWidth="25" prefHeight="20" />
                        <Label text="Running Table" />
                     </children>
                  </HBox>
                  <HBox alignment="CENTER_LEFT" spacing="5.0">
                     <children>
                        <Label styleClass="legend-dot printed-table" text="■" prefWidth="25" prefHeight="20" />
                        <Label text="Printed Table" />
                     </children>
                  </HBox>
                  <HBox alignment="CENTER_LEFT" spacing="5.0">
                     <children>
                        <Label styleClass="legend-dot paid-table" text="■" prefWidth="25" prefHeight="20" />
                        <Label text="Paid Table" />
                     </children>
                  </HBox>
                  <HBox alignment="CENTER_LEFT" spacing="5.0">
                     <children>
                        <Label styleClass="legend-dot running-kot-table" text="■" prefWidth="25" prefHeight="20" />
                        <Label text="Running KOT Table" />
                     </children>
                  </HBox>
               </children>
            </HBox>

            <!-- Ground Floor Tables -->
            <VBox spacing="10.0" styleClass="floor-section">
               <children>
                  <Label styleClass="floor-title" text="Ground Floor">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <GridPane fx:id="groundFloorGrid" hgap="15.0" vgap="15.0" styleClass="tables-grid">
                     <!-- Tables will be added dynamically -->
                  </GridPane>
               </children>
            </VBox>

            <!-- Party Hall Tables -->
            <VBox spacing="10.0" styleClass="floor-section">
               <children>
                  <Label styleClass="floor-title" text="Party Hall">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <GridPane fx:id="partyHallGrid" hgap="15.0" vgap="15.0" styleClass="tables-grid">
                     <!-- Tables will be added dynamically -->
                  </GridPane>
               </children>
            </VBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
         </padding>
      </VBox>
      
      <!-- Main Content -->
      <HBox spacing="20.0" VBox.vgrow="ALWAYS">
         <children>
            <!-- Left Panel - Configuration -->
            <VBox spacing="20.0" styleClass="config-panel" HBox.hgrow="NEVER">
               <children>
                  <!-- GST Configuration -->
                  <VBox spacing="15.0" styleClass="config-section">
                     <children>
                        <Label styleClass="section-title" text="GST Configuration">
                           <font>
                              <Font name="System Bold" size="16.0" />
                           </font>
                        </Label>
                        
                        <VBox spacing="10.0">
                           <children>
                              <HBox alignment="CENTER_LEFT" spacing="10.0">
                                 <children>
                                    <CheckBox fx:id="enableGSTCheckbox" text="Enable GST" />
                                 </children>
                              </HBox>
                              
                              <HBox alignment="CENTER_LEFT" spacing="10.0">
                                 <children>
                                    <Label text="GST Rate (%):" />
                                    <Spinner fx:id="gstRateSpinner" editable="true" max="50.0" min="0.0" initialValue="18.0" />
                                 </children>
                              </HBox>
                              
                              <VBox spacing="5.0">
                                 <children>
                                    <Label text="GST Number:" />
                                    <TextField fx:id="gstNumberField" promptText="Enter GST registration number" />
                                 </children>
                              </VBox>
                           </children>
                        </VBox>
                     </children>
                  </VBox>
                  
                  <!-- Service Charge Configuration -->
                  <VBox spacing="15.0" styleClass="config-section">
                     <children>
                        <Label styleClass="section-title" text="Service Charge Configuration">
                           <font>
                              <Font name="System Bold" size="16.0" />
                           </font>
                        </Label>
                        
                        <VBox spacing="10.0">
                           <children>
                              <HBox alignment="CENTER_LEFT" spacing="10.0">
                                 <children>
                                    <CheckBox fx:id="enableServiceChargeCheckbox" text="Enable Service Charge" />
                                 </children>
                              </HBox>
                              
                              <HBox alignment="CENTER_LEFT" spacing="10.0">
                                 <children>
                                    <Label text="Service Charge (%):" />
                                    <Spinner fx:id="serviceChargeRateSpinner" editable="true" max="25.0" min="0.0" initialValue="10.0" />
                                 </children>
                              </HBox>
                           </children>
                        </VBox>
                     </children>
                  </VBox>
                  
                  <!-- Printer Configuration -->
                  <VBox spacing="15.0" styleClass="config-section">
                     <children>
                        <Label styleClass="section-title" text="Printer Configuration">
                           <font>
                              <Font name="System Bold" size="16.0" />
                           </font>
                        </Label>
                        
                        <VBox spacing="10.0">
                           <children>
                              <VBox spacing="5.0">
                                 <children>
                                    <Label text="Invoice Printer:" />
                                    <ComboBox fx:id="invoicePrinterCombo" promptText="Select invoice printer" />
                                 </children>
                              </VBox>
                              
                              <VBox spacing="5.0">
                                 <children>
                                    <Label text="KOT Printer:" />
                                    <ComboBox fx:id="kotPrinterCombo" promptText="Select KOT printer" />
                                 </children>
                              </VBox>
                              
                              <HBox spacing="10.0">
                                 <children>
                                    <Button mnemonicParsing="false" onAction="#testInvoicePrinter" styleClass="test-button" text="🖨️ Test Invoice" />
                                    <Button mnemonicParsing="false" onAction="#testKOTPrinter" styleClass="test-button" text="🖨️ Test KOT" />
                                 </children>
                              </HBox>
                           </children>
                        </VBox>
                     </children>
                  </VBox>
                  
                  <!-- Save Configuration -->
                  <Button mnemonicParsing="false" onAction="#saveConfiguration" styleClass="primary-button" text="💾 Save Configuration" />
               </children>
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
               </padding>
            </VBox>
            
            <!-- Right Panel - Invoice Preview -->
            <VBox spacing="15.0" styleClass="preview-panel" HBox.hgrow="ALWAYS">
               <children>
                  <HBox alignment="CENTER_LEFT" spacing="15.0">
                     <children>
                        <Label styleClass="section-title" text="Invoice Preview">
                           <font>
                              <Font name="System Bold" size="16.0" />
                           </font>
                        </Label>
                        <Region HBox.hgrow="ALWAYS" />
                        <ComboBox fx:id="sampleOrderCombo" promptText="Select sample order" />
                        <Button mnemonicParsing="false" onAction="#refreshPreview" styleClass="refresh-button" text="🔄 Refresh" />
                     </children>
                  </HBox>
                  
                  <!-- Invoice Preview Area -->
                  <ScrollPane styleClass="invoice-preview-scroll" VBox.vgrow="ALWAYS">
                     <content>
                        <VBox fx:id="invoicePreview" styleClass="invoice-preview">
                           <children>
                              <!-- Restaurant Header -->
                              <VBox alignment="CENTER" spacing="5.0" styleClass="invoice-header">
                                 <children>
                                    <Label styleClass="restaurant-name" text="WOK KA TADKA">
                                       <font>
                                          <Font name="System Bold" size="20.0" />
                                       </font>
                                    </Label>
                                    <Label styleClass="restaurant-address" text="123 Food Street, Flavor City, FC 12345" />
                                    <Label styleClass="restaurant-contact" text="Phone: +91 98765 43210 | Email: <EMAIL>" />
                                    <Label fx:id="gstNumberLabel" styleClass="restaurant-gst" text="GST No: 27XXXXX1234X1ZX" />
                                 </children>
                              </VBox>
                              
                              <Separator />
                              
                              <!-- Invoice Details -->
                              <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="invoice-details">
                                 <children>
                                    <VBox spacing="5.0">
                                       <children>
                                          <Label text="Invoice No: INV-001" />
                                          <Label text="Date: 06/07/2025" />
                                          <Label text="Time: 18:30:45" />
                                       </children>
                                    </VBox>
                                    <Region HBox.hgrow="ALWAYS" />
                                    <VBox spacing="5.0">
                                       <children>
                                          <Label text="Table: 5" />
                                          <Label text="Waiter: John Doe" />
                                          <Label text="Cashier: Admin" />
                                       </children>
                                    </VBox>
                                 </children>
                              </HBox>
                              
                              <Separator />
                              
                              <!-- Items Table -->
                              <TableView fx:id="previewItemsTable" styleClass="preview-items-table">
                                 <columns>
                                    <TableColumn fx:id="previewItemColumn" prefWidth="200.0" text="Item" />
                                    <TableColumn fx:id="previewQtyColumn" prefWidth="60.0" text="Qty" />
                                    <TableColumn fx:id="previewPriceColumn" prefWidth="80.0" text="Price" />
                                    <TableColumn fx:id="previewTotalColumn" prefWidth="80.0" text="Total" />
                                 </columns>
                              </TableView>
                              
                              <Separator />
                              
                              <!-- Totals -->
                              <VBox spacing="5.0" styleClass="invoice-totals">
                                 <children>
                                    <HBox alignment="CENTER_LEFT">
                                       <children>
                                          <Label text="Subtotal:" />
                                          <Region HBox.hgrow="ALWAYS" />
                                          <Label fx:id="previewSubtotal" text="₹450.00" />
                                       </children>
                                    </HBox>
                                    <HBox fx:id="previewGSTRow" alignment="CENTER_LEFT">
                                       <children>
                                          <Label fx:id="previewGSTLabel" text="GST (18%):" />
                                          <Region HBox.hgrow="ALWAYS" />
                                          <Label fx:id="previewGSTAmount" text="₹81.00" />
                                       </children>
                                    </HBox>
                                    <HBox fx:id="previewServiceChargeRow" alignment="CENTER_LEFT">
                                       <children>
                                          <Label fx:id="previewServiceChargeLabel" text="Service Charge (10%):" />
                                          <Region HBox.hgrow="ALWAYS" />
                                          <Label fx:id="previewServiceChargeAmount" text="₹45.00" />
                                       </children>
                                    </HBox>
                                    <Separator />
                                    <HBox alignment="CENTER_LEFT" styleClass="grand-total-row">
                                       <children>
                                          <Label styleClass="grand-total-label" text="Grand Total:">
                                             <font>
                                                <Font name="System Bold" size="14.0" />
                                             </font>
                                          </Label>
                                          <Region HBox.hgrow="ALWAYS" />
                                          <Label fx:id="previewGrandTotal" styleClass="grand-total-amount" text="₹576.00">
                                             <font>
                                                <Font name="System Bold" size="14.0" />
                                             </font>
                                          </Label>
                                       </children>
                                    </HBox>
                                 </children>
                              </VBox>
                              
                              <Separator />
                              
                              <!-- Footer -->
                              <VBox alignment="CENTER" spacing="5.0" styleClass="invoice-footer">
                                 <children>
                                    <Label text="Thank you for dining with us!" />
                                    <Label text="Visit us again!" />
                                 </children>
                              </VBox>
                           </children>
                        </VBox>
                     </content>
                  </ScrollPane>
                  
                  <!-- Preview Actions -->
                  <HBox alignment="CENTER" spacing="10.0" styleClass="preview-actions">
                     <children>
                        <Button mnemonicParsing="false" onAction="#printSampleInvoice" styleClass="primary-button" text="🖨️ Print Sample Invoice" />
                        <Button mnemonicParsing="false" onAction="#printSampleKOT" styleClass="secondary-button" text="🖨️ Print Sample KOT" />
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
               </padding>
            </VBox>
         </children>
      </HBox>

      <!-- Order Details and Billing Section -->
      <VBox fx:id="orderDetailsSection" spacing="15.0" styleClass="order-details-section" visible="false" managed="false">
         <children>
            <!-- Order Details Header -->
            <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="order-details-header">
               <children>
                  <Label fx:id="orderDetailsTitle" styleClass="section-title" text="Order Details - Table 1">
                     <font>
                        <Font name="System Bold" size="18.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button mnemonicParsing="false" onAction="#closeOrderDetails" styleClass="close-button" text="X Close" />
               </children>
            </HBox>

            <!-- Order Information -->
            <HBox spacing="20.0" styleClass="order-info-section">
               <children>
                  <VBox spacing="10.0" styleClass="order-info-left">
                     <children>
                        <Label text="Order Information" styleClass="subsection-title" />
                        <HBox spacing="10.0">
                           <children>
                              <Label text="Order ID:" styleClass="info-label" />
                              <Label fx:id="orderIdLabel" text="-" styleClass="info-value" />
                           </children>
                        </HBox>
                        <HBox spacing="10.0">
                           <children>
                              <Label text="Table Number:" styleClass="info-label" />
                              <Label fx:id="tableNumberLabel" text="-" styleClass="info-value" />
                           </children>
                        </HBox>
                        <HBox spacing="10.0">
                           <children>
                              <Label text="Order Time:" styleClass="info-label" />
                              <Label fx:id="orderTimeLabel" text="-" styleClass="info-value" />
                           </children>
                        </HBox>
                        <HBox spacing="10.0">
                           <children>
                              <Label text="Status:" styleClass="info-label" />
                              <Label fx:id="orderStatusLabel" text="-" styleClass="info-value" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>

                  <VBox spacing="10.0" styleClass="order-info-right">
                     <children>
                        <Label text="Payment Information" styleClass="subsection-title" />
                        <HBox spacing="10.0">
                           <children>
                              <Label text="Payment Method:" styleClass="info-label" />
                              <ComboBox fx:id="paymentMethodCombo" prefWidth="150.0" />
                           </children>
                        </HBox>
                        <HBox spacing="10.0">
                           <children>
                              <Label text="Discount (%):" styleClass="info-label" />
                              <TextField fx:id="discountField" prefWidth="80.0" text="0" />
                              <Button mnemonicParsing="false" onAction="#applyDiscount" styleClass="apply-button" text="Apply" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
               </children>
            </HBox>

            <!-- Order Items Table -->
            <VBox spacing="10.0" styleClass="order-items-section">
               <children>
                  <Label text="Order Items" styleClass="subsection-title" />
                  <TableView fx:id="orderItemsTable" prefHeight="200.0">
                     <columns>
                        <TableColumn fx:id="itemNameColumn" prefWidth="200.0" text="Item Name" />
                        <TableColumn fx:id="itemQuantityColumn" prefWidth="80.0" text="Qty" />
                        <TableColumn fx:id="itemPriceColumn" prefWidth="100.0" text="Price" />
                        <TableColumn fx:id="itemTotalColumn" prefWidth="100.0" text="Total" />
                     </columns>
                  </TableView>
               </children>
            </VBox>

            <!-- Order Summary -->
            <HBox spacing="20.0" styleClass="order-summary-section">
               <children>
                  <Region HBox.hgrow="ALWAYS" />
                  <VBox spacing="10.0" styleClass="order-summary">
                     <children>
                        <Label text="Order Summary" styleClass="subsection-title" />
                        <HBox spacing="10.0">
                           <children>
                              <Label text="Subtotal:" styleClass="summary-label" />
                              <Label fx:id="subtotalLabel" text="₹0.00" styleClass="summary-value" />
                           </children>
                        </HBox>
                        <HBox spacing="10.0">
                           <children>
                              <Label text="GST (18%):" styleClass="summary-label" />
                              <Label fx:id="gstLabel" text="₹0.00" styleClass="summary-value" />
                           </children>
                        </HBox>
                        <HBox spacing="10.0">
                           <children>
                              <Label text="Service Charge (10%):" styleClass="summary-label" />
                              <Label fx:id="serviceChargeLabel" text="₹0.00" styleClass="summary-value" />
                           </children>
                        </HBox>
                        <Separator />
                        <HBox spacing="10.0">
                           <children>
                              <Label text="Grand Total:" styleClass="grand-total-label" />
                              <Label fx:id="grandTotalLabel" text="₹0.00" styleClass="grand-total-value" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
               </children>
            </HBox>

            <!-- Action Buttons -->
            <HBox alignment="CENTER" spacing="15.0" styleClass="billing-actions">
               <children>
                  <Button fx:id="generateBillBtn" mnemonicParsing="false" onAction="#generateBill" styleClass="primary-button" text="Generate Bill" />
                  <Button fx:id="markPaidBtn" mnemonicParsing="false" onAction="#markTableAvailable" styleClass="success-button" text="Mark Paid &amp; Available" />
                  <Button mnemonicParsing="false" onAction="#printKOT" styleClass="secondary-button" text="Print KOT" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </VBox>
   </children>
</VBox>
