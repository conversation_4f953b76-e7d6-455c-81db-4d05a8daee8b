package com.restaurant.util;

import javafx.scene.Scene;
import javafx.scene.input.KeyEvent;
import javafx.stage.Stage;
import javafx.event.EventHandler;
import java.util.HashMap;
import java.util.Map;
import java.util.ArrayList;
import java.util.List;

/**
 * Centralized manager for scene-level event handlers to prevent conflicts
 * between different controllers when navigating between views.
 */
public class SceneEventHandlerManager {
    
    private static SceneEventHandlerManager instance;
    
    // Map to track active event handlers for each scene
    private Map<Scene, List<EventHandler<KeyEvent>>> activeHandlers = new HashMap<>();

    // Map to track which controller owns which handlers
    private Map<Scene, String> sceneOwners = new HashMap<>();

    // Map to track dashboard global shortcuts that should be preserved
    private Map<Scene, EventHandler<KeyEvent>> dashboardGlobalHandlers = new HashMap<>();

    // Special identifier for dashboard controller
    private static final String DASHBOARD_CONTROLLER = "DashboardController";

    // Primary stage reference for reliable navigation
    private Stage primaryStage;

    private SceneEventHandlerManager() {
        // Private constructor for singleton
    }
    
    public static SceneEventHandlerManager getInstance() {
        if (instance == null) {
            instance = new SceneEventHandlerManager();
        }
        return instance;
    }

    /**
     * Set the primary stage for reliable navigation
     */
    public void setPrimaryStage(Stage stage) {
        this.primaryStage = stage;
        System.out.println("SceneEventHandlerManager: Primary stage set for reliable navigation");
    }

    /**
     * Get the primary stage for navigation
     */
    public Stage getPrimaryStage() {
        return primaryStage;
    }
    
    /**
     * Register dashboard global shortcuts that should be preserved
     */
    public void registerDashboardGlobalHandler(Scene scene, EventHandler<KeyEvent> handler) {
        if (scene == null || handler == null) {
            System.err.println("SceneEventHandlerManager: Cannot register null scene or handler for dashboard");
            return;
        }

        try {
            System.out.println("SceneEventHandlerManager: Registering dashboard global shortcuts");

            // Store the dashboard global handler
            dashboardGlobalHandlers.put(scene, handler);
            sceneOwners.put(scene, DASHBOARD_CONTROLLER);

            System.out.println("SceneEventHandlerManager: Dashboard global shortcuts registered successfully");

        } catch (Exception e) {
            System.err.println("SceneEventHandlerManager: Error registering dashboard global handler: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Register a key event handler for a scene with a specific controller
     */
    public void registerKeyHandler(Scene scene, String controllerName, EventHandler<KeyEvent> handler) {
        if (scene == null || handler == null) {
            System.err.println("SceneEventHandlerManager: Cannot register null scene or handler");
            return;
        }

        try {
            System.out.println("SceneEventHandlerManager: Registering key handler for " + controllerName);

            // Only clean up if this is not the dashboard controller
            if (!DASHBOARD_CONTROLLER.equals(controllerName)) {
                cleanupNonDashboardHandlers(scene);
            }

            // Add the new handler
            scene.addEventHandler(KeyEvent.KEY_PRESSED, handler);

            // Track the handler
            activeHandlers.computeIfAbsent(scene, k -> new ArrayList<>()).add(handler);
            sceneOwners.put(scene, controllerName);

            System.out.println("SceneEventHandlerManager: Key handler registered successfully for " + controllerName);

        } catch (Exception e) {
            System.err.println("SceneEventHandlerManager: Error registering key handler: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Register a key event filter for a scene with a specific controller
     */
    public void registerKeyFilter(Scene scene, String controllerName, EventHandler<KeyEvent> filter) {
        if (scene == null || filter == null) {
            System.err.println("SceneEventHandlerManager: Cannot register null scene or filter");
            return;
        }
        
        try {
            System.out.println("SceneEventHandlerManager: Registering key filter for " + controllerName);
            
            // Add the filter (filters have higher priority than handlers)
            scene.addEventFilter(KeyEvent.KEY_PRESSED, filter);
            
            // Track the filter
            activeHandlers.computeIfAbsent(scene, k -> new ArrayList<>()).add(filter);
            sceneOwners.put(scene, controllerName);
            
            System.out.println("SceneEventHandlerManager: Key filter registered successfully for " + controllerName);
            
        } catch (Exception e) {
            System.err.println("SceneEventHandlerManager: Error registering key filter: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Clean up only non-dashboard handlers, preserving dashboard global shortcuts
     */
    public void cleanupNonDashboardHandlers(Scene scene) {
        if (scene == null) {
            return;
        }

        try {
            String owner = sceneOwners.get(scene);
            System.out.println("SceneEventHandlerManager: Cleaning up non-dashboard handlers" +
                             (owner != null ? " for " + owner : ""));

            // Only remove non-dashboard handlers
            List<EventHandler<KeyEvent>> handlers = activeHandlers.get(scene);
            if (handlers != null && !DASHBOARD_CONTROLLER.equals(owner)) {
                for (EventHandler<KeyEvent> handler : handlers) {
                    scene.removeEventHandler(KeyEvent.KEY_PRESSED, handler);
                    scene.removeEventFilter(KeyEvent.KEY_PRESSED, handler);
                }
                handlers.clear();

                // Remove from tracking only if not dashboard
                activeHandlers.remove(scene);

                // Don't remove scene owner if it's dashboard
                if (!DASHBOARD_CONTROLLER.equals(owner)) {
                    sceneOwners.remove(scene);
                }
            }

            // Don't clear scene's key event handlers if dashboard shortcuts exist
            if (!dashboardGlobalHandlers.containsKey(scene)) {
                scene.setOnKeyPressed(null);
            }

            System.out.println("SceneEventHandlerManager: Non-dashboard cleanup complete");

        } catch (Exception e) {
            System.err.println("SceneEventHandlerManager: Error cleaning up non-dashboard handlers: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Clean up all event handlers for a specific scene (including dashboard)
     */
    public void cleanupScene(Scene scene) {
        if (scene == null) {
            return;
        }

        try {
            String owner = sceneOwners.get(scene);
            System.out.println("SceneEventHandlerManager: Cleaning up ALL scene handlers" +
                             (owner != null ? " for " + owner : ""));

            // Remove all tracked handlers
            List<EventHandler<KeyEvent>> handlers = activeHandlers.get(scene);
            if (handlers != null) {
                for (EventHandler<KeyEvent> handler : handlers) {
                    scene.removeEventHandler(KeyEvent.KEY_PRESSED, handler);
                    scene.removeEventFilter(KeyEvent.KEY_PRESSED, handler);
                }
                handlers.clear();
            }

            // Clear the scene's key event handlers
            scene.setOnKeyPressed(null);

            // Remove from all tracking
            activeHandlers.remove(scene);
            sceneOwners.remove(scene);
            dashboardGlobalHandlers.remove(scene);

            System.out.println("SceneEventHandlerManager: Complete scene cleanup finished");

        } catch (Exception e) {
            System.err.println("SceneEventHandlerManager: Error cleaning up scene: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Clean up handlers for a specific controller across all scenes
     */
    public void cleanupController(String controllerName) {
        try {
            System.out.println("SceneEventHandlerManager: Cleaning up all handlers for " + controllerName);

            // Find all scenes owned by this controller
            List<Scene> scenesToCleanup = new ArrayList<>();
            for (Map.Entry<Scene, String> entry : sceneOwners.entrySet()) {
                if (controllerName.equals(entry.getValue()) && !DASHBOARD_CONTROLLER.equals(controllerName)) {
                    scenesToCleanup.add(entry.getKey());
                }
            }

            // Clean up each scene (preserve dashboard shortcuts)
            for (Scene scene : scenesToCleanup) {
                cleanupNonDashboardHandlers(scene);
            }

            System.out.println("SceneEventHandlerManager: Controller cleanup complete for " + controllerName);

        } catch (Exception e) {
            System.err.println("SceneEventHandlerManager: Error cleaning up controller: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Get the current owner of a scene
     */
    public String getSceneOwner(Scene scene) {
        return sceneOwners.get(scene);
    }
    
    /**
     * Check if a scene has active handlers
     */
    public boolean hasActiveHandlers(Scene scene) {
        List<EventHandler<KeyEvent>> handlers = activeHandlers.get(scene);
        return handlers != null && !handlers.isEmpty();
    }
    
    /**
     * Get debug information about active handlers
     */
    public void printDebugInfo() {
        System.out.println("=== SceneEventHandlerManager Debug Info ===");
        System.out.println("Active scenes: " + sceneOwners.size());
        for (Map.Entry<Scene, String> entry : sceneOwners.entrySet()) {
            Scene scene = entry.getKey();
            String owner = entry.getValue();
            int handlerCount = activeHandlers.getOrDefault(scene, new ArrayList<>()).size();
            System.out.println("  Scene owned by " + owner + " has " + handlerCount + " handlers");
        }
        System.out.println("===========================================");
    }
}
