<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.chart.LineChart?>
<?import javafx.scene.chart.NumberAxis?>
<?import javafx.scene.chart.CategoryAxis?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.shape.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.AIForecasterController">
   <top>
      <VBox styleClass="ai-forecaster-header">
         <padding>
            <Insets bottom="25.0" left="40.0" right="40.0" top="25.0" />
         </padding>
         <children>
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label text="🤖" styleClass="ai-header-icon" />
                  <VBox spacing="5.0">
                     <children>
                        <Text styleClass="ai-page-title" text="AI Sales Forecaster" />
                        <Text styleClass="ai-page-subtitle" text="Advanced predictive analytics powered by machine learning" />
                     </children>
                  </VBox>
                  <Region HBox.hgrow="ALWAYS" />
                  <HBox spacing="10.0">
                     <children>
                        <!-- Embedded Voice Input Component -->
                        <VBox fx:id="embeddedVoiceInput" styleClass="voice-input-container compact-mode" spacing="8.0">
                           <children>
                              <HBox alignment="CENTER_LEFT" spacing="10.0">
                                 <children>
                                    <StackPane styleClass="mic-button-container">
                                       <children>
                                          <Circle fx:id="voicePulseRing" radius="25.0" styleClass="pulse-ring" visible="false" />
                                          <Button fx:id="embeddedMicButton" onAction="#handleEmbeddedVoiceInput" styleClass="voice-mic-button">
                                             <graphic>
                                                <Label fx:id="embeddedMicIcon" text="🎤" styleClass="mic-icon" />
                                             </graphic>
                                             <tooltip>
                                                <Tooltip text="Voice commands for forecasting" />
                                             </tooltip>
                                          </Button>
                                       </children>
                                    </StackPane>
                                    <VBox spacing="2.0">
                                       <children>
                                          <Text fx:id="voiceStatusText" styleClass="voice-status-text" text="Voice Ready" />
                                          <Text styleClass="ai-page-subtitle" text="Try: 'Forecast next 30 days'" />
                                       </children>
                                    </VBox>
                                 </children>
                              </HBox>
                           </children>
                        </VBox>
                        <Button fx:id="helpButton" styleClass="ai-help-button" text="❓ Help" />
                     </children>
                  </HBox>
               </children>
            </HBox>
         </children>
      </VBox>
   </top>
   
   <center>
      <ScrollPane fitToWidth="true" styleClass="forecaster-content">
         <content>
            <VBox spacing="25.0">
               <padding>
                  <Insets bottom="30.0" left="30.0" right="30.0" top="10.0" />
               </padding>
               <children>
                  <!-- 🔍 Forecast Parameter Inputs Section -->
                  <VBox styleClass="ai-parameters-section">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="section-header">
                           <children>
                              <Label text="🔍" styleClass="ai-section-icon" />
                              <VBox spacing="3.0">
                                 <children>
                                    <Text styleClass="ai-section-title" text="Forecast Parameters" />
                                    <Text styleClass="ai-section-desc" text="Configure your prediction settings" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>

                        <GridPane hgap="25.0" vgap="20.0" styleClass="ai-parameter-grid">
                           <columnConstraints>
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="220.0" />
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="220.0" />
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="220.0" />
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="180.0" />
                           </columnConstraints>
                           <rowConstraints>
                              <RowConstraints />
                              <RowConstraints />
                              <RowConstraints />
                           </rowConstraints>
                           <children>
                              <!-- Date Range Row 1 -->
                              <VBox spacing="8.0" GridPane.columnIndex="0" GridPane.rowIndex="0" styleClass="ai-input-group">
                                 <children>
                                    <HBox alignment="CENTER_LEFT" spacing="8.0">
                                       <children>
                                          <Label text="📅" styleClass="ai-input-icon" />
                                          <Label styleClass="ai-input-label" text="Start Date" />
                                       </children>
                                    </HBox>
                                    <DatePicker fx:id="startDatePicker" styleClass="ai-date-picker" />
                                 </children>
                              </VBox>

                              <VBox spacing="8.0" GridPane.columnIndex="1" GridPane.rowIndex="0" styleClass="ai-input-group">
                                 <children>
                                    <HBox alignment="CENTER_LEFT" spacing="8.0">
                                       <children>
                                          <Label text="📅" styleClass="ai-input-icon" />
                                          <Label styleClass="ai-input-label" text="End Date" />
                                       </children>
                                    </HBox>
                                    <DatePicker fx:id="endDatePicker" styleClass="ai-date-picker" />
                                 </children>
                              </VBox>

                              <!-- Category & Period Row 1 -->
                              <VBox spacing="8.0" GridPane.columnIndex="2" GridPane.rowIndex="0" styleClass="ai-input-group">
                                 <children>
                                    <HBox alignment="CENTER_LEFT" spacing="8.0">
                                       <children>
                                          <Label text="🍽️" styleClass="ai-input-icon" />
                                          <Label styleClass="ai-input-label" text="Category" />
                                       </children>
                                    </HBox>
                                    <ComboBox fx:id="categoryComboBox" prefWidth="220.0" promptText="Select category" styleClass="ai-combo-box" />
                                 </children>
                              </VBox>

                              <VBox spacing="8.0" GridPane.columnIndex="3" GridPane.rowIndex="0" styleClass="ai-input-group">
                                 <children>
                                    <HBox alignment="CENTER_LEFT" spacing="8.0">
                                       <children>
                                          <Label text="⏱️" styleClass="ai-input-icon" />
                                          <Label styleClass="ai-input-label" text="Forecast Period" />
                                       </children>
                                    </HBox>
                                    <ComboBox fx:id="forecastPeriodComboBox" prefWidth="180.0" styleClass="ai-combo-box" />
                                 </children>
                              </VBox>

                              <!-- Channel & Location Row 2 -->
                              <VBox spacing="8.0" GridPane.columnIndex="0" GridPane.rowIndex="1" styleClass="ai-input-group">
                                 <children>
                                    <HBox alignment="CENTER_LEFT" spacing="8.0">
                                       <children>
                                          <Label text="🛒" styleClass="ai-input-icon" />
                                          <Label styleClass="ai-input-label" text="Sales Channel" />
                                       </children>
                                    </HBox>
                                    <ComboBox fx:id="salesChannelComboBox" prefWidth="220.0" styleClass="ai-combo-box" />
                                 </children>
                              </VBox>

                              <VBox spacing="8.0" GridPane.columnIndex="1" GridPane.rowIndex="1" styleClass="ai-input-group">
                                 <children>
                                    <HBox alignment="CENTER_LEFT" spacing="8.0">
                                       <children>
                                          <Label text="📍" styleClass="ai-input-icon" />
                                          <Label styleClass="ai-input-label" text="Location" />
                                       </children>
                                    </HBox>
                                    <ComboBox fx:id="locationComboBox" prefWidth="220.0" styleClass="ai-combo-box" />
                                 </children>
                              </VBox>

                              <!-- Action Buttons Row 2 -->
                              <HBox alignment="CENTER_LEFT" spacing="20.0" GridPane.columnIndex="2" GridPane.columnSpan="2" GridPane.rowIndex="1">
                                 <children>
                                    <Button fx:id="generateForecastButton" onAction="#handleGenerateForecast" styleClass="ai-generate-button">
                                       <graphic>
                                          <HBox alignment="CENTER" spacing="8.0">
                                             <children>
                                                <Label text="🤖" styleClass="button-icon" />
                                                <Label text="Generate AI Forecast" styleClass="button-text" />
                                             </children>
                                          </HBox>
                                       </graphic>
                                    </Button>
                                    <Button fx:id="resetButton" onAction="#handleReset" styleClass="ai-reset-button">
                                       <graphic>
                                          <HBox alignment="CENTER" spacing="8.0">
                                             <children>
                                                <Label text="🔄" styleClass="button-icon" />
                                                <Label text="Reset" styleClass="button-text" />
                                             </children>
                                          </HBox>
                                       </graphic>
                                    </Button>
                                 </children>
                              </HBox>
                           </children>
                           <padding>
                              <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
                           </padding>
                        </GridPane>
                     </children>
                  </VBox>
                  
                  <!-- 📊 Forecast Summary Panel Section -->
                  <VBox fx:id="summaryContainer" styleClass="ai-summary-section" visible="false">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="section-header">
                           <children>
                              <Label text="📊" styleClass="ai-section-icon" />
                              <VBox spacing="3.0">
                                 <children>
                                    <Text styleClass="ai-section-title" text="Forecast Summary" />
                                    <Text styleClass="ai-section-desc" text="AI-powered predictions and insights" />
                                 </children>
                              </VBox>
                              <Region HBox.hgrow="ALWAYS" />
                              <Label fx:id="lastUpdatedLabel" styleClass="ai-timestamp" text="Last updated: --" />
                           </children>
                        </HBox>

                        <HBox spacing="25.0" styleClass="ai-summary-cards">
                           <children>
                              <!-- Predicted Sales Card -->
                              <VBox styleClass="ai-summary-card ai-card-primary">
                                 <children>
                                    <HBox alignment="CENTER_LEFT" spacing="12.0">
                                       <children>
                                          <VBox alignment="CENTER" styleClass="ai-card-icon-container ai-icon-sales">
                                             <children>
                                                <Label text="💰" styleClass="ai-card-icon" />
                                             </children>
                                          </VBox>
                                          <VBox spacing="2.0">
                                             <children>
                                                <Text styleClass="ai-card-label" text="Predicted Sales" />
                                                <Text styleClass="ai-card-period" text="Next forecast period" />
                                             </children>
                                          </VBox>
                                       </children>
                                    </HBox>
                                    <Text fx:id="predictedSalesText" styleClass="ai-card-value ai-value-primary" text="₹6,38,504" />
                                    <HBox alignment="CENTER_LEFT" spacing="8.0">
                                       <children>
                                          <Label fx:id="salesGrowthIcon" text="📈" styleClass="ai-trend-icon ai-trend-positive" />
                                          <Text fx:id="salesGrowthText" styleClass="ai-card-change ai-change-positive" text="+6.1% Growth" />
                                       </children>
                                    </HBox>
                                 </children>
                              </VBox>

                              <!-- Growth Rate Card -->
                              <VBox styleClass="ai-summary-card ai-card-success">
                                 <children>
                                    <HBox alignment="CENTER_LEFT" spacing="12.0">
                                       <children>
                                          <VBox alignment="CENTER" styleClass="ai-card-icon-container ai-icon-growth">
                                             <children>
                                                <Label text="📈" styleClass="ai-card-icon" />
                                             </children>
                                          </VBox>
                                          <VBox spacing="2.0">
                                             <children>
                                                <Text styleClass="ai-card-label" text="Growth Rate" />
                                                <Text styleClass="ai-card-period" text="vs previous period" />
                                             </children>
                                          </VBox>
                                       </children>
                                    </HBox>
                                    <Text fx:id="growthRateText" styleClass="ai-card-value ai-value-success" text="+6.1%" />
                                    <HBox alignment="CENTER_LEFT" spacing="8.0">
                                       <children>
                                          <Label fx:id="growthTrendIcon" text="🚀" styleClass="ai-trend-icon" />
                                          <Text fx:id="growthTrendText" styleClass="ai-card-change ai-change-positive" text="Growing" />
                                       </children>
                                    </HBox>
                                 </children>
                              </VBox>

                              <!-- Top Category Card -->
                              <VBox styleClass="ai-summary-card ai-card-info">
                                 <children>
                                    <HBox alignment="CENTER_LEFT" spacing="12.0">
                                       <children>
                                          <VBox alignment="CENTER" styleClass="ai-card-icon-container ai-icon-category">
                                             <children>
                                                <Label text="🏆" styleClass="ai-card-icon" />
                                             </children>
                                          </VBox>
                                          <VBox spacing="2.0">
                                             <children>
                                                <Text styleClass="ai-card-label" text="Top Performing" />
                                                <Text styleClass="ai-card-period" text="Best category" />
                                             </children>
                                          </VBox>
                                       </children>
                                    </HBox>
                                    <Text fx:id="topCategoryText" styleClass="ai-card-value ai-value-info" text="Main Course" />
                                    <HBox alignment="CENTER_LEFT" spacing="8.0">
                                       <children>
                                          <Label text="📊" styleClass="ai-trend-icon" />
                                          <Text fx:id="topCategoryPercentText" styleClass="ai-card-change ai-change-neutral" text="35% of total sales" />
                                       </children>
                                    </HBox>
                                 </children>
                              </VBox>

                              <!-- Confidence Score Card -->
                              <VBox styleClass="ai-summary-card ai-card-warning">
                                 <children>
                                    <HBox alignment="CENTER_LEFT" spacing="12.0">
                                       <children>
                                          <VBox alignment="CENTER" styleClass="ai-card-icon-container ai-icon-confidence">
                                             <children>
                                                <Label text="🎯" styleClass="ai-card-icon" />
                                             </children>
                                          </VBox>
                                          <VBox spacing="2.0">
                                             <children>
                                                <Text styleClass="ai-card-label" text="Confidence Score" />
                                                <Text styleClass="ai-card-period" text="Prediction accuracy" />
                                             </children>
                                          </VBox>
                                       </children>
                                    </HBox>
                                    <Text fx:id="confidenceText" styleClass="ai-card-value ai-value-warning" text="83%" />
                                    <HBox alignment="CENTER_LEFT" spacing="8.0">
                                       <children>
                                          <Label fx:id="confidenceIcon" text="🔥" styleClass="ai-trend-icon" />
                                          <Text fx:id="confidenceDescText" styleClass="ai-card-change ai-change-positive" text="High Accuracy" />
                                       </children>
                                    </HBox>
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- 📈 Sales Forecast Graph Section -->
                  <VBox fx:id="chartContainer" styleClass="ai-chart-section" visible="false">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="section-header">
                           <children>
                              <Label text="📈" styleClass="ai-section-icon" />
                              <VBox spacing="3.0">
                                 <children>
                                    <Text styleClass="ai-section-title" text="Sales Forecast Graph" />
                                    <Text styleClass="ai-section-desc" text="Historical data vs AI predictions with confidence bands" />
                                 </children>
                              </VBox>
                              <Region HBox.hgrow="ALWAYS" />

                              <!-- Chart Controls -->
                              <HBox spacing="15.0" alignment="CENTER_RIGHT">
                                 <children>
                                    <!-- View Toggle Group -->
                                    <HBox spacing="2.0" styleClass="ai-toggle-group">
                                       <children>
                                          <ToggleButton fx:id="dailyViewButton" styleClass="ai-view-toggle ai-toggle-left" text="📅 Daily" />
                                          <ToggleButton fx:id="weeklyViewButton" styleClass="ai-view-toggle ai-toggle-center" text="📊 Weekly" />
                                          <ToggleButton fx:id="monthlyViewButton" styleClass="ai-view-toggle ai-toggle-right" text="📈 Monthly" />
                                       </children>
                                    </HBox>

                                    <!-- Export Options -->
                                    <MenuButton styleClass="ai-export-button" text="📥 Export">
                                       <items>
                                          <MenuItem fx:id="exportCsvMenuItem" onAction="#handleExportCsv" text="📄 Export as CSV" />
                                          <MenuItem fx:id="exportPdfMenuItem" onAction="#handleExportPdf" text="📋 Export as PDF" />
                                          <MenuItem fx:id="exportImageMenuItem" onAction="#handleExportImage" text="🖼️ Export as Image" />
                                       </items>
                                    </MenuButton>
                                 </children>
                              </HBox>
                           </children>
                        </HBox>

                        <!-- Chart Container with Enhanced Styling -->
                        <VBox styleClass="ai-chart-container">
                           <children>
                              <LineChart fx:id="forecastChart" styleClass="ai-forecast-chart" prefHeight="450.0" animated="true">
                                 <xAxis>
                                    <CategoryAxis fx:id="xAxis" label="📅 Time Period" styleClass="ai-chart-axis" />
                                 </xAxis>
                                 <yAxis>
                                    <NumberAxis fx:id="yAxis" label="💰 Sales Amount (₹)" styleClass="ai-chart-axis" />
                                 </yAxis>
                              </LineChart>

                              <!-- Enhanced Chart Legend -->
                              <HBox alignment="CENTER" spacing="40.0" styleClass="ai-chart-legend">
                                 <children>
                                    <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="ai-legend-item">
                                       <children>
                                          <Circle radius="6.0" styleClass="ai-legend-dot ai-legend-historical" />
                                          <VBox spacing="2.0">
                                             <children>
                                                <Text styleClass="ai-legend-title" text="Historical Data" />
                                                <Text styleClass="ai-legend-desc" text="Actual sales (solid orange line)" />
                                             </children>
                                          </VBox>
                                       </children>
                                    </HBox>

                                    <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="ai-legend-item">
                                       <children>
                                          <Circle radius="6.0" styleClass="ai-legend-dot ai-legend-forecast" />
                                          <VBox spacing="2.0">
                                             <children>
                                                <Text styleClass="ai-legend-title" text="AI Forecast" />
                                                <Text styleClass="ai-legend-desc" text="Predicted sales (dashed gradient)" />
                                             </children>
                                          </VBox>
                                       </children>
                                    </HBox>

                                    <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="ai-legend-item">
                                       <children>
                                          <Rectangle width="12.0" height="8.0" styleClass="ai-legend-band ai-legend-confidence" />
                                          <VBox spacing="2.0">
                                             <children>
                                                <Text styleClass="ai-legend-title" text="Confidence Band" />
                                                <Text styleClass="ai-legend-desc" text="Prediction margin (±15%)" />
                                             </children>
                                          </VBox>
                                       </children>
                                    </HBox>
                                 </children>
                              </HBox>
                           </children>
                        </VBox>

                        <!-- Chart Insights Panel -->
                        <VBox fx:id="insightsPanel" styleClass="ai-insights-panel" visible="false">
                           <children>
                              <HBox alignment="CENTER_LEFT" spacing="10.0">
                                 <children>
                                    <Label text="🧠" styleClass="ai-insight-icon" />
                                    <Text styleClass="ai-insight-title" text="Smart Insights" />
                                 </children>
                              </HBox>
                              <VBox fx:id="insightsList" spacing="8.0" styleClass="ai-insights-list">
                                 <!-- Dynamic insights will be added here -->
                              </VBox>
                           </children>
                        </VBox>
                     </children>
                  </VBox>
                  
                  <!-- AI Processing Indicator -->
                  <VBox fx:id="loadingContainer" alignment="CENTER" spacing="20.0" styleClass="ai-loading-container" visible="false">
                     <children>
                        <VBox alignment="CENTER" spacing="15.0" styleClass="ai-loading-content">
                           <children>
                              <ProgressIndicator fx:id="loadingIndicator" styleClass="ai-progress-indicator" />
                              <VBox alignment="CENTER" spacing="8.0">
                                 <children>
                                    <Text styleClass="ai-loading-title" text="🤖 AI Processing in Progress..." />
                                    <Text fx:id="loadingStatusText" styleClass="ai-loading-status" text="Analyzing historical data patterns" />
                                 </children>
                              </VBox>
                              <HBox alignment="CENTER" spacing="15.0" styleClass="ai-loading-steps">
                                 <children>
                                    <VBox alignment="CENTER" spacing="5.0" styleClass="ai-step ai-step-active">
                                       <children>
                                          <Label text="📊" styleClass="ai-step-icon" />
                                          <Text styleClass="ai-step-text" text="Data Analysis" />
                                       </children>
                                    </VBox>
                                    <Label text="→" styleClass="ai-step-arrow" />
                                    <VBox alignment="CENTER" spacing="5.0" styleClass="ai-step">
                                       <children>
                                          <Label text="🧠" styleClass="ai-step-icon" />
                                          <Text styleClass="ai-step-text" text="AI Processing" />
                                       </children>
                                    </VBox>
                                    <Label text="→" styleClass="ai-step-arrow" />
                                    <VBox alignment="CENTER" spacing="5.0" styleClass="ai-step">
                                       <children>
                                          <Label text="📈" styleClass="ai-step-icon" />
                                          <Text styleClass="ai-step-text" text="Forecast Generation" />
                                       </children>
                                    </VBox>
                                 </children>
                              </HBox>
                           </children>
                        </VBox>
                     </children>
                  </VBox>

                  <!-- Welcome State -->
                  <VBox fx:id="emptyStateContainer" alignment="CENTER" spacing="30.0" styleClass="ai-welcome-state" visible="true">
                     <children>
                        <VBox alignment="CENTER" spacing="20.0" styleClass="ai-welcome-content">
                           <children>
                              <Label text="🤖" styleClass="ai-welcome-icon" />
                              <VBox alignment="CENTER" spacing="10.0">
                                 <children>
                                    <Text styleClass="ai-welcome-title" text="AI Sales Forecaster Ready" />
                                    <Text styleClass="ai-welcome-subtitle" text="Configure your parameters and let AI predict your future sales trends" />
                                 </children>
                              </VBox>

                              <!-- Feature Highlights -->
                              <HBox alignment="CENTER" spacing="40.0" styleClass="ai-feature-highlights">
                                 <children>
                                    <VBox alignment="CENTER" spacing="8.0" styleClass="ai-feature">
                                       <children>
                                          <Label text="📊" styleClass="ai-feature-icon" />
                                          <Text styleClass="ai-feature-text" text="Advanced Analytics" />
                                       </children>
                                    </VBox>
                                    <VBox alignment="CENTER" spacing="8.0" styleClass="ai-feature">
                                       <children>
                                          <Label text="🎯" styleClass="ai-feature-icon" />
                                          <Text styleClass="ai-feature-text" text="High Accuracy" />
                                       </children>
                                    </VBox>
                                    <VBox alignment="CENTER" spacing="8.0" styleClass="ai-feature">
                                       <children>
                                          <Label text="⚡" styleClass="ai-feature-icon" />
                                          <Text styleClass="ai-feature-text" text="Real-time Insights" />
                                       </children>
                                    </VBox>
                                 </children>
                              </HBox>

                              <Button onAction="#handleGenerateForecast" styleClass="ai-start-button">
                                 <graphic>
                                    <HBox alignment="CENTER" spacing="10.0">
                                       <children>
                                          <Label text="🚀" styleClass="button-icon" />
                                          <Label text="Start AI Forecasting" styleClass="button-text" />
                                       </children>
                                    </HBox>
                                 </graphic>
                              </Button>
                           </children>
                        </VBox>
                     </children>
                  </VBox>
               </children>
            </VBox>
         </content>
      </ScrollPane>
   </center>
</BorderPane>
