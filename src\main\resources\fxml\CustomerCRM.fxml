<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.CustomerCRMController">
   <top>
      <VBox styleClass="header-section">
         <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="page-header">
            <Button fx:id="backButton" onAction="#goBack" styleClass="back-button" text="← Back" />
            <Label styleClass="page-title" text="👥 Customer CRM" />
            <Label styleClass="page-subtitle" text="Customer Relationship Management System" />
         </HBox>
      </VBox>
   </top>
   
   <center>
      <ScrollPane fitToWidth="true" styleClass="content-scroll">
         <VBox spacing="25.0" styleClass="main-content">
            <padding>
               <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
            </padding>
            
            <!-- CRM Dashboard Stats -->
            <VBox spacing="15.0" styleClass="crm-dashboard-section">
               <Label styleClass="section-title" text="📊 CRM Dashboard" />
               
               <GridPane hgap="20.0" vgap="15.0" styleClass="stats-grid">
                  <!-- Total Customers -->
                  <VBox styleClass="stat-card" GridPane.columnIndex="0" GridPane.rowIndex="0">
                     <Label styleClass="stat-icon" text="👥" />
                     <Label fx:id="totalCustomersLabel" styleClass="stat-number" text="0" />
                     <Label styleClass="stat-label" text="Total Customers" />
                  </VBox>
                  
                  <!-- Active Customers -->
                  <VBox styleClass="stat-card" GridPane.columnIndex="1" GridPane.rowIndex="0">
                     <Label styleClass="stat-icon" text="🟢" />
                     <Label fx:id="activeCustomersLabel" styleClass="stat-number" text="0" />
                     <Label styleClass="stat-label" text="Active Customers" />
                  </VBox>
                  
                  <!-- Average Rating -->
                  <VBox styleClass="stat-card" GridPane.columnIndex="2" GridPane.rowIndex="0">
                     <Label styleClass="stat-icon" text="⭐" />
                     <Label fx:id="averageRatingLabel" styleClass="stat-number" text="0.0" />
                     <Label styleClass="stat-label" text="Average Rating" />
                  </VBox>
                  
                  <!-- Total Revenue -->
                  <VBox styleClass="stat-card" GridPane.columnIndex="3" GridPane.rowIndex="0">
                     <Label styleClass="stat-icon" text="💰" />
                     <Label fx:id="totalRevenueLabel" styleClass="stat-number" text="₹0" />
                     <Label styleClass="stat-label" text="Total Revenue" />
                  </VBox>
               </GridPane>
            </VBox>
            
            <!-- Quick Actions -->
            <VBox spacing="15.0" styleClass="quick-actions-section">
               <Label styleClass="section-title" text="⚡ Quick Actions" />
               
               <HBox spacing="15.0" styleClass="action-buttons-row">
                  <Button fx:id="addCustomerBtn" onAction="#showAddCustomerDialog" styleClass="action-button-primary" text="Add Customer" />
                  <Button fx:id="customerManagementBtn" onAction="#showCustomerManagement" styleClass="action-button-primary" text="Manage Customers" />
                  <Button fx:id="sendCampaignBtn" onAction="#showCampaignDialog" styleClass="action-button-secondary" text="Send Campaign" />
                  <Button fx:id="birthdayCampaignBtn" onAction="#showBirthdayCampaign" styleClass="action-button-birthday" text="Birthday Campaign" />
                  <Button fx:id="viewHistoryBtn" onAction="#showCustomerHistory" styleClass="action-button-history" text="View History" />
                  <Button fx:id="viewFeedbackBtn" onAction="#showFeedbackDialog" styleClass="action-button-secondary" text="View Feedback" />
                  <Button fx:id="loyaltyProgramBtn" onAction="#showLoyaltyDialog" styleClass="action-button-secondary" text="Loyalty Program" />
               </HBox>
            </VBox>
            
            <!-- Customer Segments -->
            <VBox spacing="15.0" styleClass="segments-section">
               <Label styleClass="section-title" text="🎯 Customer Segments" />
               
               <GridPane hgap="15.0" vgap="15.0" styleClass="segments-grid">
                  <!-- New Customers -->
                  <VBox styleClass="segment-card new-customers" GridPane.columnIndex="0" GridPane.rowIndex="0">
                     <HBox alignment="CENTER_LEFT" spacing="10.0">
                        <Label styleClass="segment-icon" text="🆕" />
                        <Label styleClass="segment-title" text="New Customers" />
                     </HBox>
                     <Label fx:id="newCustomersCount" styleClass="segment-count" text="0" />
                     <Button onAction="#filterNewCustomers" styleClass="segment-button" text="View Details" />
                  </VBox>
                  
                  <!-- Regular Customers -->
                  <VBox styleClass="segment-card regular-customers" GridPane.columnIndex="1" GridPane.rowIndex="0">
                     <HBox alignment="CENTER_LEFT" spacing="10.0">
                        <Label styleClass="segment-icon" text="🔄" />
                        <Label styleClass="segment-title" text="Regular" />
                     </HBox>
                     <Label fx:id="regularCustomersCount" styleClass="segment-count" text="0" />
                     <Button onAction="#filterRegularCustomers" styleClass="segment-button" text="View Details" />
                  </VBox>
                  
                  <!-- High Spenders -->
                  <VBox styleClass="segment-card high-spenders" GridPane.columnIndex="2" GridPane.rowIndex="0">
                     <HBox alignment="CENTER_LEFT" spacing="10.0">
                        <Label styleClass="segment-icon" text="💎" />
                        <Label styleClass="segment-title" text="High Spenders" />
                     </HBox>
                     <Label fx:id="highSpendersCount" styleClass="segment-count" text="0" />
                     <Button onAction="#filterHighSpenders" styleClass="segment-button" text="View Details" />
                  </VBox>
                  
                  <!-- Lapsed Customers -->
                  <VBox styleClass="segment-card lapsed-customers" GridPane.columnIndex="3" GridPane.rowIndex="0">
                     <HBox alignment="CENTER_LEFT" spacing="10.0">
                        <Label styleClass="segment-icon" text="😴" />
                        <Label styleClass="segment-title" text="Lapsed" />
                     </HBox>
                     <Label fx:id="lapsedCustomersCount" styleClass="segment-count" text="0" />
                     <Button onAction="#filterLapsedCustomers" styleClass="segment-button" text="View Details" />
                  </VBox>
               </GridPane>
            </VBox>

            <!-- Customer Tags and Information Section -->
            <HBox spacing="25.0" styleClass="customer-tags-layout">
               <!-- Left Panel - Tags -->
               <VBox spacing="15.0" styleClass="tags-panel">
                  <Label styleClass="tags-title" text="Tags" />

                  <VBox spacing="10.0" styleClass="tags-list">
                     <Button fx:id="vipTagBtn" onAction="#filterByVIP" styleClass="tag-button tag-vip" text="VIP Client" />
                     <Button fx:id="corporateTagBtn" onAction="#filterByCorporate" styleClass="tag-button tag-corporate" text="Corporate Client" />
                     <Button fx:id="premiumTagBtn" onAction="#filterByPremium" styleClass="tag-button tag-premium" text="Premium Client" />
                     <Button fx:id="addTagBtn" onAction="#showAddTagDialog" styleClass="tag-button tag-add" text="+ Add Tag" />
                  </VBox>
               </VBox>

               <!-- Right Panel - Customer Information -->
               <VBox spacing="15.0" styleClass="customer-info-panel" HBox.hgrow="ALWAYS">
                  <Label styleClass="info-title" text="Customer Information" />

                  <VBox styleClass="customer-info-table">
                     <!-- Table Header -->
                     <HBox styleClass="info-header">
                        <Label styleClass="header-name" text="Name" />
                        <Label styleClass="header-tags" text="Tags" />
                        <Label styleClass="header-created" text="Created on" />
                     </HBox>

                     <!-- Customer Info List -->
                     <ScrollPane styleClass="info-scroll" maxHeight="300">
                        <VBox fx:id="customerInfoContainer" styleClass="info-content">
                           <!-- Customer info rows will be added dynamically -->
                        </VBox>
                     </ScrollPane>
                  </VBox>
               </VBox>
            </HBox>

            <!-- Customer List -->
            <VBox spacing="15.0" styleClass="customer-list-section">
               <HBox alignment="CENTER_LEFT" spacing="15.0">
                  <Label styleClass="section-title" text="📋 Customer List" />
                  <TextField fx:id="searchField" promptText="Search customers..." styleClass="search-field" />
                  <ComboBox fx:id="segmentFilter" promptText="Filter by segment" styleClass="filter-combo" />
               </HBox>
               
               <!-- Customer Table -->
               <VBox styleClass="customer-table">
                  <!-- Table Header -->
                  <HBox styleClass="customer-table-header">
                     <Label styleClass="table-header-cell" text="Customer" HBox.hgrow="ALWAYS" />
                     <Label styleClass="table-header-cell" text="Contact" />
                     <Label styleClass="table-header-cell" text="Orders" />
                     <Label styleClass="table-header-cell" text="Total Spent" />
                     <Label styleClass="table-header-cell" text="Segment" />
                     <Label styleClass="table-header-cell" text="Last Visit" />
                     <Label styleClass="table-header-cell" text="Actions" />
                  </HBox>
                  
                  <!-- Table Content -->
                  <ScrollPane styleClass="customer-table-scroll" maxHeight="400">
                     <VBox fx:id="customerTableContainer" styleClass="customer-table-content">
                        <!-- Customer rows will be added dynamically -->
                     </VBox>
                  </ScrollPane>
               </VBox>
            </VBox>
            
            <!-- Birthday Campaign Creator -->
            <VBox fx:id="birthdayCampaignSection" spacing="15.0" styleClass="birthday-campaign-section" visible="false" managed="false">
               <Label styleClass="section-title" text="🎂 Birthday Campaign Creator" />

               <HBox spacing="25.0" styleClass="campaign-layout">
                  <!-- Left Panel - Campaign Type -->
                  <VBox spacing="0.0" styleClass="campaign-type-panel">
                     <Label styleClass="panel-title" text="Campaign Type" />

                     <VBox spacing="0.0" styleClass="campaign-type-list">
                        <Button fx:id="scheduleBtn" onAction="#selectSchedule" styleClass="campaign-type-item" text="Schedule" />
                        <Button fx:id="onBillPrintBtn" onAction="#selectOnBillPrint" styleClass="campaign-type-item" text="On Bill Print" />
                        <Button fx:id="birthdayBtn" onAction="#selectBirthday" styleClass="campaign-type-item selected" text="Birthday" />
                        <Button fx:id="anniversaryBtn" onAction="#selectAnniversary" styleClass="campaign-type-item" text="Anniversary" />
                     </VBox>
                  </VBox>

                  <!-- Right Panel - Campaign Preview -->
                  <VBox spacing="15.0" styleClass="campaign-preview-panel" HBox.hgrow="ALWAYS">
                     <!-- Email Icon and Message -->
                     <VBox spacing="15.0" styleClass="message-preview">
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <Label styleClass="email-icon" text="📧" />
                        </HBox>

                        <VBox spacing="10.0" styleClass="message-content">
                           <Label fx:id="messageTitle" styleClass="message-title" text="Wishing you a very Happy Birthday" />

                           <VBox spacing="8.0">
                              <Label fx:id="messageBody" styleClass="message-body" text="Visit any of the CDI outlets and show this message to enjoy 30% off on your bill" />
                           </VBox>

                           <Label fx:id="messageSender" styleClass="message-sender" text="Sent by CDICAFE" />
                        </VBox>
                     </VBox>

                     <!-- Campaign Configuration -->
                     <VBox spacing="15.0" styleClass="campaign-config">
                        <Label styleClass="config-title" text="Campaign Settings" />

                        <GridPane hgap="15.0" vgap="12.0" styleClass="config-grid">
                           <Label text="Campaign Name:" styleClass="config-label" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                           <TextField fx:id="campaignNameField" promptText="Enter campaign name" styleClass="config-field" GridPane.columnIndex="1" GridPane.rowIndex="0" />

                           <Label text="Discount:" styleClass="config-label" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                           <TextField fx:id="discountField" promptText="30" styleClass="discount-field" GridPane.columnIndex="1" GridPane.rowIndex="1" />

                           <Label text="Valid Days:" styleClass="config-label" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                           <TextField fx:id="validDaysField" promptText="7" styleClass="config-field" GridPane.columnIndex="1" GridPane.rowIndex="2" />

                           <Label text="Message Type:" styleClass="config-label" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                           <ComboBox fx:id="messageTypeCombo" promptText="Select type" styleClass="config-combo" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                        </GridPane>
                     </VBox>
                  </VBox>
               </HBox>

               <!-- Customer Selection Section -->
               <VBox spacing="15.0" styleClass="customer-selection-section">
                  <Label styleClass="section-title" text="Select Customer/Recipients" />

                  <!-- Search and Filter -->
                  <HBox spacing="15.0" alignment="CENTER_LEFT" styleClass="selection-controls">
                     <TextField fx:id="customerSearchField" promptText="Search customers..." styleClass="customer-search-field" />
                     <ComboBox fx:id="segmentFilterCombo" promptText="Filter by segment" styleClass="segment-filter-combo" />
                     <Button fx:id="selectAllBtn" onAction="#selectAllCustomers" styleClass="select-all-button" text="Select All" />
                     <Button fx:id="clearAllBtn" onAction="#clearAllCustomers" styleClass="clear-all-button" text="Clear All" />
                  </HBox>

                  <!-- Customer List -->
                  <VBox styleClass="customer-selection-table">
                     <!-- Table Header -->
                     <HBox styleClass="customer-selection-header">
                        <CheckBox fx:id="selectAllCheckbox" onAction="#toggleSelectAll" styleClass="header-checkbox" />
                        <Label styleClass="header-label phone-header" text="Phone" />
                        <Label styleClass="header-label name-header" text="Name" />
                        <Label styleClass="header-label segment-header" text="Segment" />
                        <Label styleClass="header-label birthday-header" text="Birthday" />
                     </HBox>

                     <!-- Customer List Container -->
                     <ScrollPane styleClass="customer-list-scroll" maxHeight="300">
                        <VBox fx:id="campaignCustomerListContainer" styleClass="customer-list-content">
                           <!-- Customer rows will be added dynamically -->
                        </VBox>
                     </ScrollPane>
                  </VBox>

                  <!-- Selected Count -->
                  <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="selection-summary">
                     <Label fx:id="selectedCountLabel" styleClass="selected-count" text="0 customers selected" />
                  </HBox>
               </VBox>

               <!-- Action Buttons -->
               <HBox spacing="15.0" alignment="CENTER_RIGHT" styleClass="action-buttons-section">
                  <Button fx:id="closeCampaignBtn" onAction="#closeBirthdayCampaign" styleClass="close-campaign-button" text="Close" />
                  <Button fx:id="previewBtn" onAction="#previewCampaign" styleClass="preview-button" text="Preview Campaign" />
                  <Button fx:id="saveDraftBtn" onAction="#saveDraft" styleClass="save-draft-button" text="Save Draft" />
                  <Button fx:id="sendCampaignBtn2" onAction="#sendCampaign" styleClass="send-campaign-button" text="Send Campaign" />
               </HBox>
            </VBox>

            <!-- Recent Activity -->
            <VBox spacing="15.0" styleClass="recent-activity-section">
               <Label styleClass="section-title" text="🕒 Recent Activity" />

               <VBox fx:id="recentActivityContainer" styleClass="activity-list">
                  <!-- Recent activity items will be added dynamically -->
               </VBox>
            </VBox>
         </VBox>
      </ScrollPane>
   </center>
</BorderPane>
