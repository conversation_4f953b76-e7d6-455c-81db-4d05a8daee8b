package com.restaurant.controller;

import com.restaurant.model.InventoryItem;
import com.restaurant.util.UniversalNavigationManager;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Parent;
import javafx.scene.control.*;
import javafx.scene.input.KeyCode;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;
import javafx.scene.text.Font;
import javafx.stage.Stage;

import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

public class InventoryManagementController implements Initializable {
    
    // FXML Components
    @FXML private Button backButton;
    @FXML private Button addItemButton;
    @FXML private Button ingredientsTab, stockAlertsTab, wastageLogTab, suppliersTab;
    @FXML private Button advancedInventoryBtn;
    @FXML private TextField searchField;
    @FXML private ScrollPane ingredientsView, stockAlertsView, wastageLogView, suppliersView;
    @FXML private VBox ingredientsContainer, stockAlertsContainer, wastageLogContainer, suppliersContainer;
    
    // Data
    private ObservableList<InventoryItem> inventoryItems = FXCollections.observableArrayList();
    private String currentTab = "ingredients";
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("InventoryManagementController: Starting simplified initialization...");

        // Use Platform.runLater to prevent hanging during FXML loading
        Platform.runLater(() -> {
            try {
                System.out.println("InventoryManagementController: Setting up basic components...");

                // Only do essential setup, defer complex operations
                setupBasicInventory();

                System.out.println("InventoryManagementController: Basic initialization complete");

                // Schedule complex operations for later
                scheduleComplexInventoryInitialization();

            } catch (Exception e) {
                System.err.println("Error in InventoryManagementController initialization: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    private void setupBasicInventory() {
        // Minimal setup to prevent hanging
        System.out.println("Inventory: Basic components setup complete");
        currentTab = "ingredients"; // Set default tab
    }

    private void scheduleComplexInventoryInitialization() {
        // Schedule complex operations for background thread
        Task<Void> initTask = new Task<Void>() {
            @Override
            protected Void call() throws Exception {
                Thread.sleep(100); // Small delay to ensure UI is ready
                return null;
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    try {
                        // Do complex setup here if needed
                        setupEscapeKeyHandler();
                        showIngredients(); // Show ingredients tab by default
                        System.out.println("Inventory: Complex initialization complete");
                    } catch (Exception e) {
                        System.err.println("Error in complex Inventory init: " + e.getMessage());
                    }
                });
            }
        };

        Thread initThread = new Thread(initTask, "Inventory-Init");
        initThread.setDaemon(true);
        initThread.start();
    }
    
    private void setupSampleData() {
        // Load inventory items from database
        loadInventoryItemsFromDatabase();
    }

    private void loadInventoryItemsFromDatabase() {
        inventoryItems.clear();
        inventoryItems.addAll(com.restaurant.model.InventoryDAO.getAllInventoryItems());
    }
    
    private void setupEscapeKeyHandler() {
        Platform.runLater(() -> {
            if (backButton != null && backButton.getScene() != null) {
                backButton.getScene().setOnKeyPressed(event -> {
                    if (event.getCode() == KeyCode.ESCAPE) {
                        // Use universal navigation for ESC handling
                        UniversalNavigationManager.getInstance().handleEscapeKey();
                    }
                });
            }
        });

        // Register with universal navigation manager
        UniversalNavigationManager.getInstance().setCurrentController("InventoryManagementController");
    }
    
    @FXML
    private void showIngredients() {
        setActiveTab("ingredients");
        displayInventoryItems();
    }
    
    @FXML
    private void showStockAlerts() {
        setActiveTab("stockAlerts");
        displayStockAlerts();
    }
    
    @FXML
    private void showWastageLog() {
        setActiveTab("wastageLog");
        displayWastageLog();
    }
    
    @FXML
    private void showSuppliers() {
        setActiveTab("suppliers");
        displaySuppliers();
    }
    
    private void setActiveTab(String tabName) {
        currentTab = tabName;
        
        // Remove active class from all tabs
        ingredientsTab.getStyleClass().remove("tab-active");
        stockAlertsTab.getStyleClass().remove("tab-active");
        wastageLogTab.getStyleClass().remove("tab-active");
        suppliersTab.getStyleClass().remove("tab-active");
        
        // Hide all views
        ingredientsView.setVisible(false);
        stockAlertsView.setVisible(false);
        wastageLogView.setVisible(false);
        suppliersView.setVisible(false);
        
        // Show selected view and set active tab
        switch (tabName) {
            case "ingredients":
                ingredientsTab.getStyleClass().add("tab-active");
                ingredientsView.setVisible(true);
                break;
            case "stockAlerts":
                stockAlertsTab.getStyleClass().add("tab-active");
                stockAlertsView.setVisible(true);
                break;
            case "wastageLog":
                wastageLogTab.getStyleClass().add("tab-active");
                wastageLogView.setVisible(true);
                break;
            case "suppliers":
                suppliersTab.getStyleClass().add("tab-active");
                suppliersView.setVisible(true);
                break;
        }
    }
    
    private void displayInventoryItems() {
        ingredientsContainer.getChildren().clear();
        
        String searchText = searchField.getText().toLowerCase().trim();
        List<InventoryItem> filteredItems = inventoryItems.stream()
            .filter(item -> searchText.isEmpty() || 
                   item.getName().toLowerCase().contains(searchText))
            .collect(Collectors.toList());
        
        if (filteredItems.isEmpty()) {
            Label emptyLabel = new Label("No ingredients found");
            emptyLabel.getStyleClass().add("empty-state");
            ingredientsContainer.getChildren().add(emptyLabel);
            return;
        }
        
        for (InventoryItem item : filteredItems) {
            VBox itemCard = createInventoryItemCard(item);
            ingredientsContainer.getChildren().add(itemCard);
        }
    }
    
    private VBox createInventoryItemCard(InventoryItem item) {
        VBox card = new VBox();
        card.getStyleClass().add("inventory-item-card");
        card.setSpacing(8);
        card.setPadding(new Insets(15));
        
        // Main content row
        HBox mainRow = new HBox();
        mainRow.setAlignment(Pos.CENTER_LEFT);
        mainRow.setSpacing(15);
        
        // Icon placeholder (cube icon)
        Label iconLabel = new Label("📦");
        iconLabel.getStyleClass().add("inventory-icon");
        iconLabel.setFont(Font.font(24));
        
        // Item details
        VBox detailsBox = new VBox();
        detailsBox.setSpacing(4);
        HBox.setHgrow(detailsBox, Priority.ALWAYS);
        
        Label nameLabel = new Label(item.getName());
        nameLabel.getStyleClass().add("item-name");
        nameLabel.setFont(Font.font("System Bold", 16));
        
        Label quantityLabel = new Label(item.getQuantityDisplay());
        quantityLabel.getStyleClass().add("item-quantity");
        
        Label timeLabel = new Label("last used " + item.getTimeAgo());
        timeLabel.getStyleClass().add("item-time");
        
        detailsBox.getChildren().addAll(nameLabel, quantityLabel, timeLabel);
        
        // Status badge
        Label statusLabel = new Label(item.getStatus());
        statusLabel.getStyleClass().addAll("status-badge", getStatusClass(item.getStatus()));
        
        // Action buttons
        HBox actionsBox = new HBox();
        actionsBox.setSpacing(10);
        actionsBox.setAlignment(Pos.CENTER_RIGHT);
        
        Button editButton = new Button("✏️");
        editButton.getStyleClass().add("action-button");
        editButton.setOnAction(e -> editItem(item));
        
        Button deleteButton = new Button("🗑️");
        deleteButton.getStyleClass().add("action-button");
        deleteButton.setOnAction(e -> deleteItem(item));
        
        actionsBox.getChildren().addAll(editButton, deleteButton);
        
        mainRow.getChildren().addAll(iconLabel, detailsBox, statusLabel, actionsBox);
        card.getChildren().add(mainRow);
        
        return card;
    }
    
    private String getStatusClass(String status) {
        switch (status) {
            case "In Stock": return "status-in-stock";
            case "Low Stock": return "status-low-stock";
            case "Out of Stock": return "status-out-stock";
            default: return "status-unknown";
        }
    }
    
    private void displayStockAlerts() {
        stockAlertsContainer.getChildren().clear();
        
        List<InventoryItem> lowStockItems = inventoryItems.stream()
            .filter(item -> "Low Stock".equals(item.getStatus()) || "Out of Stock".equals(item.getStatus()))
            .collect(Collectors.toList());
        
        if (lowStockItems.isEmpty()) {
            Label emptyLabel = new Label("No stock alerts");
            emptyLabel.getStyleClass().add("empty-state");
            stockAlertsContainer.getChildren().add(emptyLabel);
            return;
        }
        
        for (InventoryItem item : lowStockItems) {
            VBox alertCard = createStockAlertCard(item);
            stockAlertsContainer.getChildren().add(alertCard);
        }
    }
    
    private VBox createStockAlertCard(InventoryItem item) {
        VBox card = createInventoryItemCard(item);
        card.getStyleClass().add("alert-card");
        return card;
    }
    
    private void displayWastageLog() {
        // Placeholder for wastage log functionality
        wastageLogContainer.getChildren().clear();
        Label emptyLabel = new Label("No wastage records found");
        emptyLabel.getStyleClass().add("empty-state");
        wastageLogContainer.getChildren().add(emptyLabel);
    }
    
    private void displaySuppliers() {
        // Placeholder for suppliers functionality
        suppliersContainer.getChildren().clear();
        Label emptyLabel = new Label("No suppliers configured");
        emptyLabel.getStyleClass().add("empty-state");
        suppliersContainer.getChildren().add(emptyLabel);
    }
    
    @FXML
    private void searchItems() {
        if ("ingredients".equals(currentTab)) {
            displayInventoryItems();
        }
    }

    @FXML
    private void openAdvancedInventory() {
        try {
            // Load the Advanced Inventory Management interface
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/AdvancedInventoryManagement.fxml"));
            javafx.scene.Parent root = loader.load();

            // Create new stage for advanced inventory with compact size
            javafx.stage.Stage advancedStage = new javafx.stage.Stage();
            advancedStage.setTitle("Advanced Inventory Management");
            advancedStage.setScene(new javafx.scene.Scene(root, 900, 650));
            advancedStage.setMinWidth(800);
            advancedStage.setMinHeight(550);

            // Set as modal window
            advancedStage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            advancedStage.initOwner(backButton.getScene().getWindow());

            advancedStage.show();

            System.out.println("Advanced Inventory Management opened successfully");

        } catch (Exception e) {
            System.err.println("Error opening Advanced Inventory Management: " + e.getMessage());
            e.printStackTrace();
            showAlert("Error", "Failed to open Advanced Inventory Management: " + e.getMessage());
        }
    }


    
    @FXML
    private void showAddItemDialog() {
        // Placeholder for add item dialog
        showAlert("Add Item", "Add item functionality will be implemented here");
    }
    
    private void editItem(InventoryItem item) {
        // Placeholder for edit item functionality
        showAlert("Edit Item", "Edit functionality for " + item.getName() + " will be implemented here");
    }
    
    private void deleteItem(InventoryItem item) {
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("Delete Item");
        confirmAlert.setHeaderText(null);
        confirmAlert.setContentText("Are you sure you want to delete " + item.getName() + "?");
        
        confirmAlert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                inventoryItems.remove(item);
                displayInventoryItems();
                if ("stockAlerts".equals(currentTab)) {
                    displayStockAlerts();
                }
            }
        });
    }
    
    @FXML
    private void goBack() {
        try {
            // Use universal navigation manager for robust back navigation
            UniversalNavigationManager.getInstance().goBack();
        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Could not go back");
        }
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
