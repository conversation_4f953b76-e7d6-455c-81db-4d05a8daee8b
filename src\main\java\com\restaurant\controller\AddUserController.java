package com.restaurant.controller;

import com.restaurant.model.User;
import com.restaurant.model.UserDAO;
import com.restaurant.util.PasswordUtil;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.stage.Stage;
import javafx.collections.FXCollections;

import java.io.IOException;

public class AddUserController {
    
    @FXML private TextField usernameField;
    @FXML private TextField fullNameField;
    @FXML private TextField emailField;
    @FXML private TextField phoneField;
    @FXML private ComboBox<String> roleCombo;
    @FXML private ComboBox<String> statusCombo;
    @FXML private PasswordField passwordField;
    @FXML private PasswordField confirmPasswordField;
    
    private UserDAO userDAO;
    private User currentUser;
    
    @FXML
    private void initialize() {
        System.out.println("AddUserController.initialize() called");
        
        // Initialize DAO
        userDAO = new UserDAO();
        
        // Setup combo boxes
        setupComboBoxes();
        
        // Add validation listeners
        setupValidation();
    }
    
    private void setupComboBoxes() {
        // Setup role combo box
        roleCombo.setItems(FXCollections.observableArrayList(
            "ADMIN", "MANAGER", "STAFF", "CASHIER", "WAITER", "CHEF"
        ));
        
        // Setup status combo box
        statusCombo.setItems(FXCollections.observableArrayList(
            "Active", "Inactive"
        ));
        statusCombo.setValue("Active"); // Default to Active
    }
    
    private void setupValidation() {
        // Add real-time validation if needed
        usernameField.textProperty().addListener((observable, oldValue, newValue) -> {
            // Remove spaces and convert to lowercase
            if (newValue != null && !newValue.equals(newValue.toLowerCase().replaceAll("\\s", ""))) {
                usernameField.setText(newValue.toLowerCase().replaceAll("\\s", ""));
            }
        });
    }
    
    public void initData(User user) {
        this.currentUser = user;
        System.out.println("AddUserController.initData() called with user: " +
                          (user != null ? user.getUsername() : "null"));
    }

    // Optional method for cases where no user is passed
    public void initData() {
        this.currentUser = null;
        System.out.println("AddUserController.initData() called without user");
    }
    
    @FXML
    private void saveUser() {
        System.out.println("=== SAVE USER BUTTON CLICKED ===");
        
        if (!validateForm()) {
            return;
        }
        
        try {
            // Create new user object
            User newUser = new User();
            newUser.setUsername(usernameField.getText().trim());
            newUser.setFullName(fullNameField.getText().trim());
            newUser.setEmail(emailField.getText().trim());
            newUser.setPhone(phoneField.getText().trim());
            newUser.setRole(roleCombo.getValue());
            newUser.setStatus(statusCombo.getValue());
            
            // Hash the password
            String hashedPassword = PasswordUtil.hashPassword(passwordField.getText());
            newUser.setPasswordHash(hashedPassword);
            
            // Save to database
            boolean success = userDAO.createUser(newUser);
            
            if (success) {
                showSuccessAlert("User created successfully!");
                goBack(); // Return to user management
            } else {
                showErrorAlert("Failed to create user. Username might already exist.");
            }
            
        } catch (Exception e) {
            System.err.println("Error creating user: " + e.getMessage());
            e.printStackTrace();
            showErrorAlert("An error occurred while creating the user: " + e.getMessage());
        }
    }
    
    private boolean validateForm() {
        StringBuilder errors = new StringBuilder();
        
        // Validate required fields
        if (usernameField.getText().trim().isEmpty()) {
            errors.append("• Username is required\n");
        }
        
        if (fullNameField.getText().trim().isEmpty()) {
            errors.append("• Full Name is required\n");
        }
        
        if (roleCombo.getValue() == null) {
            errors.append("• Role is required\n");
        }
        
        if (passwordField.getText().isEmpty()) {
            errors.append("• Password is required\n");
        }
        
        if (confirmPasswordField.getText().isEmpty()) {
            errors.append("• Confirm Password is required\n");
        }
        
        // Validate password match
        if (!passwordField.getText().equals(confirmPasswordField.getText())) {
            errors.append("• Passwords do not match\n");
        }
        
        // Validate password strength
        if (passwordField.getText().length() < 6) {
            errors.append("• Password must be at least 6 characters long\n");
        }
        
        // Validate email format if provided
        String email = emailField.getText().trim();
        if (!email.isEmpty() && !email.matches("^[A-Za-z0-9+_.-]+@(.+)$")) {
            errors.append("• Invalid email format\n");
        }
        
        if (errors.length() > 0) {
            showErrorAlert("Please fix the following errors:\n\n" + errors.toString());
            return false;
        }
        
        return true;
    }
    
    @FXML
    private void goBack() {
        System.out.println("Going back to User Management");

        try {
            // Load User Management FXML
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/UserManagement.fxml"));
            Parent userManagementRoot = loader.load();

            // Find the main BorderPane container (dashboard's mainContainer)
            Parent currentRoot = usernameField.getScene().getRoot();
            if (currentRoot instanceof javafx.scene.layout.BorderPane) {
                javafx.scene.layout.BorderPane mainContainer = (javafx.scene.layout.BorderPane) currentRoot;
                mainContainer.setCenter(userManagementRoot);
                System.out.println("Successfully navigated back to User Management within dashboard");
            } else {
                // Fallback: replace entire scene if we can't find BorderPane
                Stage stage = (Stage) usernameField.getScene().getWindow();
                Scene scene = new Scene(userManagementRoot);
                stage.setScene(scene);
                stage.setTitle("User Management - Restaurant Management System");
                System.out.println("Successfully navigated back to User Management (fallback method)");
            }

        } catch (IOException e) {
            System.err.println("Error loading User Management: " + e.getMessage());
            e.printStackTrace();
            showErrorAlert("Error returning to User Management: " + e.getMessage());
        }
    }
    
    private void showSuccessAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Success");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showErrorAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("Error");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void clearForm() {
        usernameField.clear();
        fullNameField.clear();
        emailField.clear();
        phoneField.clear();
        roleCombo.setValue(null);
        statusCombo.setValue("Active");
        passwordField.clear();
        confirmPasswordField.clear();
    }
}
