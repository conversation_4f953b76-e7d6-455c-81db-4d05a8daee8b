package com.restaurant.controller;

import com.restaurant.model.OnlineOrder;
import com.restaurant.model.OnlineOrderDAO;
import com.restaurant.model.OnlineOrderItem;
import com.restaurant.util.NotificationManager;
import com.restaurant.util.PersistentNotificationManager;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import java.net.URL;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Controller for the Finish List Panel - Admin dashboard for tracking Swiggy & Zomato orders
 */
public class FinishListController implements Initializable {
    
    // FXML Components
    @FXML private ComboBox<String> statusFilterCombo;
    @FXML private ComboBox<String> platformFilterCombo;
    @FXML private Button refreshButton;
    @FXML private Label preparingCountLabel;
    @FXML private Label readyCountLabel;
    @FXML private Label pricingCountLabel;
    @FXML private Label totalOrdersLabel;
    @FXML private VBox ordersContainer;
    @FXML private Button addTestOrderButton;
    @FXML private Button markAllReadyButton;
    @FXML private Button clearCompletedButton;
    
    // Data
    private List<OnlineOrder> allOrders = new ArrayList<>();
    private List<OnlineOrder> filteredOrders = new ArrayList<>();
    private ScheduledExecutorService refreshScheduler;
    private NotificationManager notificationManager;
    private PersistentNotificationManager persistentNotificationManager;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        System.out.println("FinishListController: Initializing...");

        // Initialize notification managers
        notificationManager = NotificationManager.getInstance();
        persistentNotificationManager = PersistentNotificationManager.getInstance();

        // Initialize database
        OnlineOrderDAO.initializeDatabase();

        // Setup UI components
        setupFilters();
        setupAutoRefresh();

        // Load initial data
        refreshOrders();

        // Show welcome notification
        notificationManager.notifySuccess("Finish List Ready",
            "Online order tracking system is now active");

        System.out.println("FinishListController: Initialization complete");
    }
    
    private void setupFilters() {
        // Setup status filter
        statusFilterCombo.setItems(FXCollections.observableArrayList(
            "All Statuses", "Preparing", "Ready", "Pricing", "Completed"
        ));
        statusFilterCombo.setValue("All Statuses");
        
        // Setup platform filter
        platformFilterCombo.setItems(FXCollections.observableArrayList(
            "All Platforms", "Swiggy", "Zomato"
        ));
        platformFilterCombo.setValue("All Platforms");
    }
    
    private void setupAutoRefresh() {
        // Auto-refresh every 30 seconds
        refreshScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "FinishList-Refresh");
            t.setDaemon(true);
            return t;
        });
        
        refreshScheduler.scheduleAtFixedRate(() -> {
            Platform.runLater(this::refreshOrders);
        }, 30, 30, TimeUnit.SECONDS);
    }
    
    @FXML
    private void refreshOrders() {
        System.out.println("Refreshing online orders...");

        try {
            // Load all orders from database
            allOrders = OnlineOrderDAO.getAllOnlineOrders();

            // If no orders exist, add sample data for demonstration
            if (allOrders.isEmpty()) {
                System.out.println("No orders found in database, loading sample data for demonstration");
                loadSampleData();
                return;
            }

            // Apply current filters
            applyFilters();

            // Update statistics
            updateStatistics();

            // Update UI
            displayOrders();

            System.out.println("Loaded " + allOrders.size() + " online orders");
        } catch (Exception e) {
            System.err.println("Error refreshing orders: " + e.getMessage());
            e.printStackTrace();

            // Show sample data if database fails
            loadSampleData();
        }
    }
    
    @FXML
    private void filterByStatus() {
        applyFilters();
        displayOrders();
    }
    
    @FXML
    private void filterByPlatform() {
        applyFilters();
        displayOrders();
    }
    
    private void applyFilters() {
        String statusFilter = statusFilterCombo.getValue();
        String platformFilter = platformFilterCombo.getValue();
        
        filteredOrders = allOrders.stream()
            .filter(order -> {
                // Status filter
                if (!"All Statuses".equals(statusFilter)) {
                    if (!order.getStatus().getDisplayName().equals(statusFilter)) {
                        return false;
                    }
                }
                
                // Platform filter
                if (!"All Platforms".equals(platformFilter)) {
                    if (!order.getPlatform().getDisplayName().equals(platformFilter)) {
                        return false;
                    }
                }
                
                return true;
            })
            .toList();
    }
    
    private void updateStatistics() {
        long preparingCount = allOrders.stream()
            .filter(order -> order.getStatus() == OnlineOrder.OrderStatus.PREPARING)
            .count();
        
        long readyCount = allOrders.stream()
            .filter(order -> order.getStatus() == OnlineOrder.OrderStatus.READY)
            .count();
        
        long pricingCount = allOrders.stream()
            .filter(order -> order.getStatus() == OnlineOrder.OrderStatus.PRICING)
            .count();
        
        preparingCountLabel.setText(String.valueOf(preparingCount));
        readyCountLabel.setText(String.valueOf(readyCount));
        pricingCountLabel.setText(String.valueOf(pricingCount));
        totalOrdersLabel.setText(String.valueOf(allOrders.size()));
    }
    
    private void displayOrders() {
        ordersContainer.getChildren().clear();
        
        if (filteredOrders.isEmpty()) {
            // Show empty state
            Label emptyLabel = new Label("No orders found");
            emptyLabel.setStyle("-fx-font-size: 16px; -fx-text-fill: #666; -fx-padding: 50px;");
            ordersContainer.getChildren().add(emptyLabel);
            return;
        }
        
        // Create order cards
        for (OnlineOrder order : filteredOrders) {
            VBox orderCard = createOrderCard(order);
            ordersContainer.getChildren().add(orderCard);
        }
    }
    
    private VBox createOrderCard(OnlineOrder order) {
        VBox card = new VBox(10);
        card.setStyle(String.format(
            "-fx-background-color: white; " +
            "-fx-border-color: %s; " +
            "-fx-border-width: 2px; " +
            "-fx-border-radius: 8px; " +
            "-fx-background-radius: 8px; " +
            "-fx-padding: 15px; " +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);",
            order.getStatusColor()
        ));
        
        // Header with order info and platform
        HBox header = createOrderHeader(order);
        
        // Order items
        VBox itemsSection = createOrderItems(order);
        
        // Customer info
        VBox customerSection = createCustomerInfo(order);
        
        // Status and actions
        HBox actionsSection = createActionsSection(order);
        
        card.getChildren().addAll(header, itemsSection, customerSection, actionsSection);
        return card;
    }
    
    private HBox createOrderHeader(OnlineOrder order) {
        HBox header = new HBox(15);
        header.setAlignment(Pos.CENTER_LEFT);
        
        // Platform badge
        Label platformLabel = new Label(order.getPlatform().getDisplayName());
        platformLabel.setStyle(String.format(
            "-fx-background-color: %s; " +
            "-fx-text-fill: white; " +
            "-fx-padding: 4px 8px; " +
            "-fx-background-radius: 12px; " +
            "-fx-font-weight: bold; " +
            "-fx-font-size: 12px;",
            order.getPlatformColor()
        ));
        
        // Order ID
        Label orderIdLabel = new Label("Order #" + order.getOrderId());
        orderIdLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 14px;");
        
        // Order time
        Label timeLabel = new Label(order.getFormattedOrderTime());
        timeLabel.setStyle("-fx-text-fill: #666; -fx-font-size: 12px;");
        
        // Total amount
        Label amountLabel = new Label(String.format("₹%.2f", order.getTotalAmount()));
        amountLabel.setStyle("-fx-font-weight: bold; -fx-font-size: 14px; -fx-text-fill: #2e7d32;");
        
        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);
        
        header.getChildren().addAll(platformLabel, orderIdLabel, timeLabel, spacer, amountLabel);
        return header;
    }
    
    private VBox createOrderItems(OnlineOrder order) {
        VBox itemsSection = new VBox(5);
        
        Label itemsHeader = new Label("Items:");
        itemsHeader.setStyle("-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #333;");
        itemsSection.getChildren().add(itemsHeader);
        
        if (order.getItems() != null && !order.getItems().isEmpty()) {
            for (OnlineOrderItem item : order.getItems()) {
                Label itemLabel = new Label(item.getDisplayText());
                itemLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #555; -fx-padding: 2px 0 2px 10px;");
                itemsSection.getChildren().add(itemLabel);
            }
        } else {
            Label noItemsLabel = new Label("No items details available");
            noItemsLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #999; -fx-padding: 2px 0 2px 10px;");
            itemsSection.getChildren().add(noItemsLabel);
        }
        
        return itemsSection;
    }
    
    private VBox createCustomerInfo(OnlineOrder order) {
        VBox customerSection = new VBox(3);
        
        Label customerLabel = new Label("Customer: " + order.getCustomerName());
        customerLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #333;");
        
        if (order.getCustomerPhone() != null && !order.getCustomerPhone().isEmpty()) {
            Label phoneLabel = new Label("Phone: " + order.getCustomerPhone());
            phoneLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #666;");
            customerSection.getChildren().add(phoneLabel);
        }
        
        if (order.getDeliveryAddress() != null && !order.getDeliveryAddress().isEmpty()) {
            Label addressLabel = new Label("Address: " + order.getDeliveryAddress());
            addressLabel.setStyle("-fx-font-size: 11px; -fx-text-fill: #666;");
            addressLabel.setWrapText(true);
            customerSection.getChildren().add(addressLabel);
        }
        
        customerSection.getChildren().add(0, customerLabel);
        return customerSection;
    }
    
    private HBox createActionsSection(OnlineOrder order) {
        HBox actionsSection = new HBox(8);
        actionsSection.setAlignment(Pos.CENTER_LEFT);

        // Current status label
        Label statusLabel = new Label("Status: " + order.getStatus().getDisplayName());
        statusLabel.setStyle(String.format(
            "-fx-background-color: %s; " +
            "-fx-text-fill: white; " +
            "-fx-padding: 4px 8px; " +
            "-fx-background-radius: 6px; " +
            "-fx-font-size: 11px; " +
            "-fx-font-weight: bold;",
            order.getStatusColor()
        ));

        // Status update buttons
        HBox buttonGroup = new HBox(5);
        buttonGroup.setAlignment(Pos.CENTER_LEFT);

        // Create buttons for each status (except current one)
        // NOTE: NEW orders are handled in notifications panel - no Accept/Reject here
        for (OnlineOrder.OrderStatus status : OnlineOrder.OrderStatus.values()) {
            if (status != order.getStatus()) {
                // Skip Accept/Reject for NEW orders - handled in notifications panel
                if (order.getStatus() == OnlineOrder.OrderStatus.NEW) {
                    // NEW orders: No status change buttons - handled in notifications
                    continue;
                }
                Button statusButton = createStatusButton(order, status);
                buttonGroup.getChildren().add(statusButton);
            }
        }

        // Add message for NEW orders
        if (order.getStatus() == OnlineOrder.OrderStatus.NEW) {
            Label newOrderMessage = new Label("📱 Accept/Reject in Notifications Panel");
            newOrderMessage.setStyle("-fx-text-fill: #e74c3c; -fx-font-weight: bold; -fx-font-style: italic;");
            buttonGroup.getChildren().add(newOrderMessage);
        }

        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);

        actionsSection.getChildren().addAll(statusLabel, buttonGroup, spacer);
        return actionsSection;
    }

    private Button createStatusButton(OnlineOrder order, OnlineOrder.OrderStatus targetStatus) {
        Button button = new Button();
        button.getStyleClass().add("status-button");

        // Set button text and specific style class based on target status
        switch (targetStatus) {
            case NEW:
                button.setText("🚨 Mark as New");
                button.getStyleClass().add("status-button-new");
                break;
            case PREPARING:
                button.setText("🍽️ Mark Preparing");
                button.getStyleClass().add("status-button-preparing");
                break;
            case READY:
                button.setText("✅ Mark Ready");
                button.getStyleClass().add("status-button-ready");
                break;
            case PRICING:
                button.setText("💰 Start Pricing");
                button.getStyleClass().add("status-button-pricing");
                break;
            case COMPLETED:
                button.setText("✔️ Complete");
                button.getStyleClass().add("status-button-completed");
                break;
        }

        // Set button action with confirmation for important status changes
        button.setOnAction(e -> {
            // Show confirmation for completing orders
            if (targetStatus == OnlineOrder.OrderStatus.COMPLETED) {
                Alert confirmation = new Alert(Alert.AlertType.CONFIRMATION);
                confirmation.setTitle("Confirm Order Completion");
                confirmation.setHeaderText("Complete Order " + order.getOrderId());
                confirmation.setContentText("Are you sure you want to mark this order as completed?\n" +
                                          "This means it has been handed over to the delivery partner.");

                confirmation.showAndWait().ifPresent(response -> {
                    if (response == ButtonType.OK) {
                        updateOrderStatus(order, targetStatus);
                    }
                });
            } else {
                // Direct update for other status changes
                updateOrderStatus(order, targetStatus);
            }
        });

        return button;
    }
    
    private void updateOrderStatus(OnlineOrder order, OnlineOrder.OrderStatus newStatus) {
        try {
            OnlineOrder.OrderStatus oldStatus = order.getStatus();
            boolean success = OnlineOrderDAO.updateOrderStatus(order.getId(), newStatus);
            if (success) {
                order.setStatus(newStatus);
                System.out.println("Updated order " + order.getOrderId() + " status to " + newStatus);

                // SILENT: No sound notifications on manual status changes
                // All sounds are handled automatically by the database trigger system
                switch (newStatus) {
                    case NEW:
                        // No persistent ringing on manual changes - only on automatic order creation
                        System.out.println("SILENT: Order " + order.getOrderId() + " marked as NEW (no sound)");
                        break;
                    case PREPARING:
                        // Silent status change - sounds handled by notifications panel
                        System.out.println("SILENT: Order " + order.getOrderId() + " moved to PREPARING (no sound)");
                        break;
                    case READY:
                        // Silent status change - no sound on manual updates
                        System.out.println("SILENT: Order " + order.getOrderId() + " marked as READY (no sound)");
                        break;
                    case PRICING:
                        // Silent status change - no sound on manual updates
                        System.out.println("SILENT: Order " + order.getOrderId() + " moved to PRICING (no sound)");
                        break;
                    case COMPLETED:
                        // Silent status change - no sound on manual updates
                        System.out.println("SILENT: Order " + order.getOrderId() + " marked as COMPLETED (no sound)");
                        break;
                }

                // Refresh display
                Platform.runLater(() -> {
                    updateStatistics();
                    displayOrders();
                });

            } else {
                notificationManager.notifyError("Update Failed",
                    "Failed to update order " + order.getOrderId() + " status");
            }
        } catch (Exception e) {
            System.err.println("Error updating order status: " + e.getMessage());
            notificationManager.notifyError("System Error",
                "Error updating order status: " + e.getMessage());
        }
    }
    
    @FXML
    private void addTestOrder() {
        // Create a test order for demonstration
        OnlineOrder testOrder = createTestOrder();

        try {
            int orderId = OnlineOrderDAO.createOnlineOrder(testOrder);
            if (orderId > 0) {
                System.out.println("SILENT: Created test order with ID: " + orderId);
                System.out.println("🔔 MP3 notification will be triggered automatically by database");

                // NO manual sound trigger - sounds handled automatically by OnlineOrderDAO
                // The triggerNewOrderNotification() method will handle all MP3 sounds

                refreshOrders();
            } else {
                System.err.println("SILENT: Failed to create test order");
            }
        } catch (Exception e) {
            System.err.println("SILENT: Error creating test order: " + e.getMessage());
        }
    }
    
    @FXML
    private void markAllReady() {
        long preparingCount = allOrders.stream()
            .filter(order -> order.getStatus() == OnlineOrder.OrderStatus.PREPARING)
            .count();
        
        if (preparingCount == 0) {
            showAlert("Info", "No orders in 'Preparing' status to update");
            return;
        }
        
        Alert confirmation = new Alert(Alert.AlertType.CONFIRMATION);
        confirmation.setTitle("Confirm Action");
        confirmation.setHeaderText("Mark All Ready");
        confirmation.setContentText("Are you sure you want to mark all " + preparingCount + " preparing orders as ready?");
        
        confirmation.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                allOrders.stream()
                    .filter(order -> order.getStatus() == OnlineOrder.OrderStatus.PREPARING)
                    .forEach(order -> updateOrderStatus(order, OnlineOrder.OrderStatus.READY));

                // Send bulk action notification
                notificationManager.notifySuccess("Bulk Update Complete",
                    preparingCount + " orders marked as ready");
            }
        });
    }
    
    @FXML
    private void clearCompleted() {
        long completedCount = allOrders.stream()
            .filter(order -> order.getStatus() == OnlineOrder.OrderStatus.COMPLETED)
            .count();
        
        if (completedCount == 0) {
            showAlert("Info", "No completed orders to clear");
            return;
        }
        
        Alert confirmation = new Alert(Alert.AlertType.CONFIRMATION);
        confirmation.setTitle("Confirm Action");
        confirmation.setHeaderText("Clear Completed Orders");
        confirmation.setContentText("Are you sure you want to clear all " + completedCount + " completed orders?");
        
        confirmation.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                // In a real implementation, you might archive or delete completed orders
                showNotification("Completed Orders Cleared", completedCount + " completed orders cleared");
                refreshOrders();
            }
        });
    }
    
    private OnlineOrder createTestOrder() {
        // Create sample order data
        OnlineOrder order = new OnlineOrder();
        order.setOrderId("SW" + System.currentTimeMillis());
        // Random platform selection including Wok Ka Tadka
        double random = Math.random();
        if (random < 0.33) {
            order.setPlatform(OnlineOrder.Platform.SWIGGY);
        } else if (random < 0.66) {
            order.setPlatform(OnlineOrder.Platform.ZOMATO);
        } else {
            order.setPlatform(OnlineOrder.Platform.WOK_KA_TADKA);
        }
        order.setCustomerName("Test Customer " + (int)(Math.random() * 100));
        order.setCustomerPhone("+91 98765 43210");
        order.setDeliveryAddress("123 Test Street, Test City, 123456");
        order.setStatus(OnlineOrder.OrderStatus.NEW);  // Start as NEW to trigger persistent ringing
        order.setOrderTime(LocalDateTime.now());
        order.setStatusUpdatedTime(LocalDateTime.now());
        order.setEstimatedPrepTime(30);
        
        // Add sample items
        List<OnlineOrderItem> items = new ArrayList<>();
        items.add(new OnlineOrderItem("Chicken Biryani", 2, 220.0, "Main Course"));
        items.add(new OnlineOrderItem("Paneer Tikka", 1, 180.0, "Starters"));
        items.add(new OnlineOrderItem("Garlic Naan", 3, 35.0, "Bread"));
        
        double total = items.stream().mapToDouble(OnlineOrderItem::getTotalPrice).sum();
        order.setTotalAmount(total);
        order.setItems(items);
        
        return order;
    }
    
    private void loadSampleData() {
        // Load comprehensive sample data for demonstration
        allOrders.clear();
        System.out.println("Loading sample Swiggy & Zomato orders for demonstration...");

        // Sample Order 1: Swiggy - Preparing
        OnlineOrder order1 = new OnlineOrder();
        order1.setId(1);
        order1.setOrderId("SW1001");
        order1.setPlatform(OnlineOrder.Platform.SWIGGY);
        order1.setCustomerName("Rajesh Kumar");
        order1.setCustomerPhone("+91 98765 43210");
        order1.setDeliveryAddress("A-123, Green Park, New Delhi, 110016");
        order1.setStatus(OnlineOrder.OrderStatus.NEW);  // Start as NEW for persistent ringing
        order1.setTotalAmount(485.0);
        order1.setOrderTime(LocalDateTime.now().minusMinutes(25));
        order1.setStatusUpdatedTime(LocalDateTime.now().minusMinutes(20));
        order1.setSpecialInstructions("Extra spicy, no onions");
        order1.setEstimatedPrepTime(30);

        List<OnlineOrderItem> items1 = new ArrayList<>();
        items1.add(new OnlineOrderItem("Chicken Biryani", 2, 220.0, "Main Course"));
        items1.add(new OnlineOrderItem("Garlic Naan", 3, 35.0, "Bread"));
        items1.add(new OnlineOrderItem("Raita", 1, 80.0, "Sides"));
        order1.setItems(items1);
        allOrders.add(order1);

        // Sample Order 2: Zomato - Ready
        OnlineOrder order2 = new OnlineOrder();
        order2.setId(2);
        order2.setOrderId("ZM2002");
        order2.setPlatform(OnlineOrder.Platform.ZOMATO);
        order2.setCustomerName("Priya Sharma");
        order2.setCustomerPhone("+91 87654 32109");
        order2.setDeliveryAddress("B-456, Sector 18, Noida, 201301");
        order2.setStatus(OnlineOrder.OrderStatus.READY);
        order2.setTotalAmount(320.0);
        order2.setOrderTime(LocalDateTime.now().minusMinutes(45));
        order2.setStatusUpdatedTime(LocalDateTime.now().minusMinutes(5));
        order2.setSpecialInstructions("Less oil, medium spice");
        order2.setEstimatedPrepTime(25);

        List<OnlineOrderItem> items2 = new ArrayList<>();
        items2.add(new OnlineOrderItem("Paneer Tikka", 1, 180.0, "Starters"));
        items2.add(new OnlineOrderItem("Dal Makhani", 1, 140.0, "Main Course"));
        order2.setItems(items2);
        allOrders.add(order2);

        // Sample Order 3: Swiggy - Pricing
        OnlineOrder order3 = new OnlineOrder();
        order3.setId(3);
        order3.setOrderId("SW1003");
        order3.setPlatform(OnlineOrder.Platform.SWIGGY);
        order3.setCustomerName("Amit Patel");
        order3.setCustomerPhone("+91 76543 21098");
        order3.setDeliveryAddress("C-789, Bandra West, Mumbai, 400050");
        order3.setStatus(OnlineOrder.OrderStatus.PRICING);
        order3.setTotalAmount(650.0);
        order3.setOrderTime(LocalDateTime.now().minusMinutes(60));
        order3.setStatusUpdatedTime(LocalDateTime.now().minusMinutes(2));
        order3.setSpecialInstructions("Pack separately, include extra chutneys");
        order3.setEstimatedPrepTime(35);

        List<OnlineOrderItem> items3 = new ArrayList<>();
        items3.add(new OnlineOrderItem("Mutton Curry", 1, 280.0, "Main Course"));
        items3.add(new OnlineOrderItem("Jeera Rice", 2, 120.0, "Rice"));
        items3.add(new OnlineOrderItem("Tandoori Roti", 4, 25.0, "Bread"));
        items3.add(new OnlineOrderItem("Gulab Jamun", 1, 130.0, "Desserts"));
        order3.setItems(items3);
        allOrders.add(order3);

        // Sample Order 4: Zomato - Preparing
        OnlineOrder order4 = new OnlineOrder();
        order4.setId(4);
        order4.setOrderId("ZM2004");
        order4.setPlatform(OnlineOrder.Platform.ZOMATO);
        order4.setCustomerName("Sneha Reddy");
        order4.setCustomerPhone("+91 65432 10987");
        order4.setDeliveryAddress("D-321, Koramangala, Bangalore, 560034");
        order4.setStatus(OnlineOrder.OrderStatus.NEW);  // Another NEW order for testing
        order4.setTotalAmount(275.0);
        order4.setOrderTime(LocalDateTime.now().minusMinutes(15));
        order4.setStatusUpdatedTime(LocalDateTime.now().minusMinutes(10));
        order4.setSpecialInstructions("Jain food - no onion, garlic, potato");
        order4.setEstimatedPrepTime(20);

        List<OnlineOrderItem> items4 = new ArrayList<>();
        items4.add(new OnlineOrderItem("Veg Hakka Noodles", 1, 160.0, "Chinese"));
        items4.add(new OnlineOrderItem("Manchurian Dry", 1, 115.0, "Chinese"));
        order4.setItems(items4);
        allOrders.add(order4);

        // Sample Order 5: Swiggy - Ready
        OnlineOrder order5 = new OnlineOrder();
        order5.setId(5);
        order5.setOrderId("SW1005");
        order5.setPlatform(OnlineOrder.Platform.SWIGGY);
        order5.setCustomerName("Vikram Singh");
        order5.setCustomerPhone("+91 54321 09876");
        order5.setDeliveryAddress("E-654, Cyber City, Gurgaon, 122002");
        order5.setStatus(OnlineOrder.OrderStatus.READY);
        order5.setTotalAmount(420.0);
        order5.setOrderTime(LocalDateTime.now().minusMinutes(35));
        order5.setStatusUpdatedTime(LocalDateTime.now().minusMinutes(8));
        order5.setSpecialInstructions("Extra sauce packets, no plastic cutlery");
        order5.setEstimatedPrepTime(25);

        List<OnlineOrderItem> items5 = new ArrayList<>();
        items5.add(new OnlineOrderItem("Chicken Tikka Masala", 1, 240.0, "Main Course"));
        items5.add(new OnlineOrderItem("Butter Naan", 2, 40.0, "Bread"));
        items5.add(new OnlineOrderItem("Mixed Veg", 1, 140.0, "Main Course"));
        order5.setItems(items5);
        allOrders.add(order5);

        // Sample Order 6: Zomato - Completed (for demonstration)
        OnlineOrder order6 = new OnlineOrder();
        order6.setId(6);
        order6.setOrderId("ZM2006");
        order6.setPlatform(OnlineOrder.Platform.ZOMATO);
        order6.setCustomerName("Anita Gupta");
        order6.setCustomerPhone("+91 43210 98765");
        order6.setDeliveryAddress("F-987, Salt Lake, Kolkata, 700064");
        order6.setStatus(OnlineOrder.OrderStatus.COMPLETED);
        order6.setTotalAmount(195.0);
        order6.setOrderTime(LocalDateTime.now().minusMinutes(90));
        order6.setStatusUpdatedTime(LocalDateTime.now().minusMinutes(30));
        order6.setSpecialInstructions("Call before delivery");
        order6.setEstimatedPrepTime(15);

        List<OnlineOrderItem> items6 = new ArrayList<>();
        items6.add(new OnlineOrderItem("Masala Dosa", 2, 85.0, "South Indian"));
        items6.add(new OnlineOrderItem("Filter Coffee", 1, 25.0, "Beverages"));
        order6.setItems(items6);
        allOrders.add(order6);

        System.out.println("Loaded " + allOrders.size() + " sample orders:");
        System.out.println("- 2 NEW orders (Swiggy & Zomato) - WILL START RINGING!");
        System.out.println("- 1 Ready order (Zomato)");
        System.out.println("- 1 Pricing order (Swiggy)");
        System.out.println("- 1 Ready order (Swiggy)");
        System.out.println("- 1 Completed order (Zomato)");

        applyFilters();
        updateStatistics();
        displayOrders();

        // SILENT: No persistent notifications on refresh
        // NEW orders will have their MP3 notifications triggered automatically when created
        allOrders.stream()
            .filter(order -> order.getStatus() == OnlineOrder.OrderStatus.NEW)
            .forEach(order -> {
                System.out.println("SILENT: Found NEW order " + order.getOrderId() + " (no sound on refresh)");
            });
    }
    
    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    private void showNotification(String title, String message) {
        // Simple notification - in a real app you might use a toast notification
        System.out.println("NOTIFICATION: " + title + " - " + message);
    }
    
    public void cleanup() {
        if (refreshScheduler != null && !refreshScheduler.isShutdown()) {
            refreshScheduler.shutdown();
        }
    }
}
