package com.restaurant.util;

/**
 * Utility class for testing notification sounds
 * Allows testing individual sound patterns without running the full application
 */
public class SoundTester {

    public static void main(String[] args) {
        if (args.length == 0) {
            System.out.println("Usage: java SoundTester <soundType>");
            System.out.println("Sound types: newOrder, orderReady, urgent, error, success, persistentRing");
            return;
        }

        String soundType = args[0].toLowerCase();

        System.out.println("🔊 Testing notification sound: " + soundType);
        System.out.println("Make sure your system volume is turned on!");
        System.out.println();

        switch (soundType) {
            case "neworder":
                testNewOrderSound();
                break;
            case "swiggy":
                testSwiggySound();
                break;
            case "zomato":
                testZomatoSound();
                break;
            case "orderready":
                testOrderReadySound();
                break;
            case "urgent":
                testUrgentSound();
                break;
            case "error":
                testErrorSound();
                break;
            case "success":
                testSuccessSound();
                break;
            case "persistentring":
                testPersistentRingSound();
                break;
            case "swiggyring":
                testSwiggyRingSound();
                break;
            case "zomatorring":
                testZomatoRingSound();
                break;
            default:
                System.out.println("Unknown sound type: " + soundType);
                System.out.println("Available types: newOrder, swiggy, zomato, orderReady, urgent, error, success, persistentRing, swiggyRing, zomatoRing");
        }
    }
    
    /**
     * Test new order notification sound (Double beep)
     */
    private static void testNewOrderSound() {
        System.out.println("🔔 NEW ORDER SOUND - Double Beep");
        System.out.println("Pattern: BEEP-pause-BEEP");
        System.out.println("Use case: When new Swiggy/Zomato order arrives");
        System.out.println();
        
        try {
            System.out.println("Playing... BEEP");
            java.awt.Toolkit.getDefaultToolkit().beep();
            Thread.sleep(200);
            System.out.println("Playing... BEEP");
            java.awt.Toolkit.getDefaultToolkit().beep();
            System.out.println("✅ New order sound test completed!");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * Test order ready notification sound (Triple beep)
     */
    private static void testOrderReadySound() {
        System.out.println("🍽️ ORDER READY SOUND - Triple Beep");
        System.out.println("Pattern: BEEP-BEEP-BEEP (rapid)");
        System.out.println("Use case: When food is ready for delivery");
        System.out.println();
        
        try {
            for (int i = 0; i < 3; i++) {
                System.out.println("Playing... BEEP " + (i + 1));
                java.awt.Toolkit.getDefaultToolkit().beep();
                Thread.sleep(150);
            }
            System.out.println("✅ Order ready sound test completed!");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * Test urgent notification sound (Rapid 5 beeps)
     */
    private static void testUrgentSound() {
        System.out.println("🚨 URGENT ALERT SOUND - Rapid 5 Beeps");
        System.out.println("Pattern: BEEP-BEEP-BEEP-BEEP-BEEP (very fast)");
        System.out.println("Use case: For urgent notifications and errors");
        System.out.println();
        
        try {
            for (int i = 0; i < 5; i++) {
                System.out.println("Playing... URGENT BEEP " + (i + 1));
                java.awt.Toolkit.getDefaultToolkit().beep();
                Thread.sleep(100);
            }
            System.out.println("✅ Urgent alert sound test completed!");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * Test error notification sound (Long pause pattern)
     */
    private static void testErrorSound() {
        System.out.println("❌ ERROR SOUND - Long Pause Pattern");
        System.out.println("Pattern: BEEP-long pause-BEEP");
        System.out.println("Use case: For system errors and failures");
        System.out.println();
        
        try {
            System.out.println("Playing... BEEP");
            java.awt.Toolkit.getDefaultToolkit().beep();
            System.out.println("Waiting... (long pause)");
            Thread.sleep(500);
            System.out.println("Playing... BEEP");
            java.awt.Toolkit.getDefaultToolkit().beep();
            System.out.println("✅ Error sound test completed!");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * Test Swiggy-specific notification sound (Loud 4-2 pattern)
     */
    private static void testSwiggySound() {
        System.out.println("🟠 SWIGGY ORDER SOUND - Loud 4-2 Pattern");
        System.out.println("Pattern: 4 rapid beeps, pause, 2 long beeps");
        System.out.println("Use case: When new Swiggy order arrives");
        System.out.println();

        try {
            System.out.println("Playing Swiggy pattern...");
            // 4 rapid beeps
            for (int i = 0; i < 4; i++) {
                System.out.println("  Rapid BEEP " + (i + 1));
                java.awt.Toolkit.getDefaultToolkit().beep();
                Thread.sleep(80);
            }
            Thread.sleep(300);
            // 2 longer beeps
            for (int i = 0; i < 2; i++) {
                System.out.println("  Long BEEP " + (i + 1));
                java.awt.Toolkit.getDefaultToolkit().beep();
                Thread.sleep(400);
            }
            System.out.println("✅ Swiggy sound test completed!");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Test Zomato-specific notification sound (Triple-double pattern)
     */
    private static void testZomatoSound() {
        System.out.println("🔴 ZOMATO ORDER SOUND - Triple-Double Pattern");
        System.out.println("Pattern: 3 sets of double beeps");
        System.out.println("Use case: When new Zomato order arrives");
        System.out.println();

        try {
            System.out.println("Playing Zomato pattern...");
            for (int set = 0; set < 3; set++) {
                System.out.println("  Set " + (set + 1) + ":");
                for (int pair = 0; pair < 2; pair++) {
                    System.out.println("    BEEP " + (pair + 1));
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(90);
                    System.out.println("    BEEP " + (pair + 2));
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(150);
                }
                Thread.sleep(250);
            }
            System.out.println("✅ Zomato sound test completed!");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Test success notification sound (Single beep)
     */
    private static void testSuccessSound() {
        System.out.println("✅ SUCCESS SOUND - Single Beep");
        System.out.println("Pattern: BEEP");
        System.out.println("Use case: For successful operations and status changes");
        System.out.println();

        System.out.println("Playing... BEEP");
        java.awt.Toolkit.getDefaultToolkit().beep();
        System.out.println("✅ Success sound test completed!");
    }
    
    /**
     * Test persistent ringing sound (Urgent pattern for unaccepted orders)
     */
    private static void testPersistentRingSound() {
        System.out.println("🔔 PERSISTENT RINGING SOUND - Urgent Pattern");
        System.out.println("Pattern: BEEP-BEEP-BEEP (pause) BEEP-BEEP-BEEP");
        System.out.println("Use case: Continuous ringing for unaccepted orders");
        System.out.println("This will demonstrate 3 ring cycles...");
        System.out.println();
        
        try {
            for (int cycle = 1; cycle <= 3; cycle++) {
                System.out.println("Ring cycle " + cycle + ":");
                
                // First set of 3 beeps
                for (int i = 0; i < 3; i++) {
                    System.out.println("  Playing... BEEP " + (i + 1));
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(100);
                }
                
                System.out.println("  Pause...");
                Thread.sleep(300);
                
                // Second set of 3 beeps
                for (int i = 0; i < 3; i++) {
                    System.out.println("  Playing... BEEP " + (i + 4));
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(100);
                }
                
                if (cycle < 3) {
                    System.out.println("  Waiting for next ring cycle...");
                    Thread.sleep(2000); // 2 second pause between cycles
                }
            }
            
            System.out.println("✅ Persistent ringing sound test completed!");
            System.out.println("In the real application, this pattern repeats every 10 seconds");
            System.out.println("until the order is accepted or 30 minutes maximum.");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Test Swiggy persistent ring sound
     */
    private static void testSwiggyRingSound() {
        System.out.println("🟠 SWIGGY PERSISTENT RING - Loud 4-2 Pattern");
        System.out.println("Pattern: 4 rapid beeps, pause, 2 long beeps (repeating)");
        System.out.println("Use case: Continuous ringing for unaccepted Swiggy orders");
        System.out.println("This will demonstrate 2 ring cycles...");
        System.out.println();

        try {
            for (int cycle = 1; cycle <= 2; cycle++) {
                System.out.println("Swiggy ring cycle " + cycle + ":");

                // 4 rapid beeps
                for (int i = 0; i < 4; i++) {
                    System.out.println("  Rapid BEEP " + (i + 1));
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(80);
                }
                Thread.sleep(200);

                // 2 longer beeps
                for (int i = 0; i < 2; i++) {
                    System.out.println("  Long BEEP " + (i + 1));
                    java.awt.Toolkit.getDefaultToolkit().beep();
                    Thread.sleep(300);
                }

                if (cycle < 2) {
                    System.out.println("  Waiting for next ring cycle...");
                    Thread.sleep(2000);
                }
            }

            System.out.println("✅ Swiggy persistent ring test completed!");
            System.out.println("In the real application, this pattern repeats every 10 seconds for Swiggy orders");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Test Zomato persistent ring sound
     */
    private static void testZomatoRingSound() {
        System.out.println("🔴 ZOMATO PERSISTENT RING - Triple-Double Pattern");
        System.out.println("Pattern: 3 sets of double beeps (repeating)");
        System.out.println("Use case: Continuous ringing for unaccepted Zomato orders");
        System.out.println("This will demonstrate 2 ring cycles...");
        System.out.println();

        try {
            for (int cycle = 1; cycle <= 2; cycle++) {
                System.out.println("Zomato ring cycle " + cycle + ":");

                for (int set = 0; set < 3; set++) {
                    System.out.println("  Set " + (set + 1) + ":");
                    for (int pair = 0; pair < 2; pair++) {
                        System.out.println("    BEEP " + (pair + 1));
                        java.awt.Toolkit.getDefaultToolkit().beep();
                        Thread.sleep(90);
                        System.out.println("    BEEP " + (pair + 2));
                        java.awt.Toolkit.getDefaultToolkit().beep();
                        Thread.sleep(150);
                    }
                    Thread.sleep(250);
                }

                if (cycle < 2) {
                    System.out.println("  Waiting for next ring cycle...");
                    Thread.sleep(2000);
                }
            }

            System.out.println("✅ Zomato persistent ring test completed!");
            System.out.println("In the real application, this pattern repeats every 10 seconds for Zomato orders");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * Test all sounds in sequence
     */
    public static void testAllSounds() {
        System.out.println("🎵 TESTING ALL NOTIFICATION SOUNDS");
        System.out.println("=====================================");
        System.out.println();

        testNewOrderSound();
        sleep(1000);

        testOrderReadySound();
        sleep(1000);

        testSuccessSound();
        sleep(1000);

        testUrgentSound();
        sleep(1000);

        testErrorSound();
        sleep(1000);

        testPersistentRingSound();

        System.out.println();
        System.out.println("🎉 All notification sounds tested successfully!");
    }

    /**
     * Helper method for sleep without exception handling
     */
    private static void sleep(int milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
