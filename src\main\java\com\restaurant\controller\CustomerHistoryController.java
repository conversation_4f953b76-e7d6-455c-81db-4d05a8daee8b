package com.restaurant.controller;

import com.restaurant.model.Customer;
import com.restaurant.model.Order;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.*;

import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Controller for Customer History
 */
public class CustomerHistoryController implements Initializable {

    // FXML Controls
    @FXML private Button backButton;
    @FXML private Label customerNameLabel;
    @FXML private Label customerMobileLabel;
    @FXML private Label maxOrderLabel;
    @FXML private Label averageBillLabel;
    @FXML private Label comingSinceLabel;
    @FXML private Label visitsLabel;
    @FXML private VBox orderHistoryContainer;
    @FXML private Button exportBtn;
    @FXML private Button printBtn;
    @FXML private Button sendEmailBtn;

    // Data
    private Customer currentCustomer;
    private List<Order> orderHistory;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        initializeData();
        displayCustomerInfo();
        displayOrderHistory();
    }

    private void initializeData() {
        // Initialize sample customer data (matching your image)
        currentCustomer = new Customer(1, "Arpit Shah", "+91 XXXXX XXXXX", "<EMAIL>");
        currentCustomer.setAddress("123 MG Road, Bangalore");
        
        // Initialize order history
        orderHistory = new ArrayList<>();
        
        // Order 1: 12 Nov, 2022 - Pick Up
        Order order1 = new Order();
        order1.setId(1);
        order1.setCustomerId(1);
        order1.setOrderDate(LocalDateTime.of(2022, 11, 12, 21, 42, 0));
        order1.setOrderType("Pick Up");
        order1.setPaymentMethod("Cash");
        order1.setItemName("Lachha Paratha");
        order1.setOutlet("Ahmedabad");
        order1.setTotalAmount(703.00);
        orderHistory.add(order1);
        
        // Order 2: 30 Oct, 2022 - My Delivery
        Order order2 = new Order();
        order2.setId(2);
        order2.setCustomerId(1);
        order2.setOrderDate(LocalDateTime.of(2022, 10, 30, 11, 49, 0));
        order2.setOrderType("My Delivery");
        order2.setPaymentMethod("Cash");
        order2.setItemName("New Biryani");
        order2.setOutlet("Mumbai");
        order2.setTotalAmount(3403.00);
        orderHistory.add(order2);
        
        // Order 3: 12 Oct, 2022 - Dine in
        Order order3 = new Order();
        order3.setId(3);
        order3.setCustomerId(1);
        order3.setOrderDate(LocalDateTime.of(2022, 10, 12, 15, 43, 20));
        order3.setOrderType("Dine in");
        order3.setPaymentMethod("Cash");
        order3.setItemName("Veg Fried Rice");
        order3.setOutlet("Delhi");
        order3.setTotalAmount(314.00);
        orderHistory.add(order3);
    }

    private void displayCustomerInfo() {
        // Display customer basic info
        customerNameLabel.setText(currentCustomer.getName());
        customerMobileLabel.setText(currentCustomer.getPhone());
        
        // Calculate statistics
        double maxOrder = orderHistory.stream()
            .mapToDouble(Order::getTotalAmount)
            .max()
            .orElse(0.0);
        
        double averageBill = orderHistory.stream()
            .mapToDouble(Order::getTotalAmount)
            .average()
            .orElse(0.0);
        
        LocalDateTime firstOrderDate = orderHistory.stream()
            .map(Order::getOrderDate)
            .min(LocalDateTime::compareTo)
            .orElse(LocalDateTime.now());
        
        int totalVisits = orderHistory.size();
        
        // Display statistics
        maxOrderLabel.setText(String.format("$%.2f", maxOrder));
        averageBillLabel.setText(String.format("$%.2f", averageBill));
        comingSinceLabel.setText(firstOrderDate.format(DateTimeFormatter.ofPattern("dd-MM-yyyy")));
        visitsLabel.setText(totalVisits + " Times");
    }

    private void displayOrderHistory() {
        orderHistoryContainer.getChildren().clear();
        
        for (Order order : orderHistory) {
            HBox orderRow = createOrderRow(order);
            orderHistoryContainer.getChildren().add(orderRow);
        }
    }

    private HBox createOrderRow(Order order) {
        HBox row = new HBox();
        row.setAlignment(Pos.CENTER_LEFT);
        row.getStyleClass().add("order-history-row");
        row.setPadding(new Insets(15, 20, 15, 20));

        // Order Date
        VBox dateBox = new VBox();
        dateBox.setAlignment(Pos.CENTER_LEFT);
        dateBox.setPrefWidth(120);
        
        Label dateLabel = new Label(order.getOrderDate().format(DateTimeFormatter.ofPattern("dd MMM, yyyy")));
        dateLabel.getStyleClass().add("order-date");
        
        Label timeLabel = new Label("at " + order.getOrderDate().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        timeLabel.getStyleClass().add("order-time");
        
        dateBox.getChildren().addAll(dateLabel, timeLabel);

        // Order Type
        Label typeLabel = new Label(order.getOrderType());
        typeLabel.getStyleClass().add("order-type");
        typeLabel.setPrefWidth(100);

        // Payment Method
        Label paymentLabel = new Label(order.getPaymentMethod());
        paymentLabel.getStyleClass().add("order-payment");
        paymentLabel.setPrefWidth(80);

        // Item Name
        Label itemLabel = new Label(order.getItemName());
        itemLabel.getStyleClass().add("order-item");
        itemLabel.setPrefWidth(150);

        // Outlet
        Label outletLabel = new Label(order.getOutlet());
        outletLabel.getStyleClass().add("order-outlet");
        outletLabel.setPrefWidth(100);

        // Amount
        Label amountLabel = new Label(String.format("₹%.2f", order.getTotalAmount()));
        amountLabel.getStyleClass().add("order-amount");
        amountLabel.setPrefWidth(100);
        amountLabel.setAlignment(Pos.CENTER_RIGHT);

        row.getChildren().addAll(dateBox, typeLabel, paymentLabel, itemLabel, outletLabel, amountLabel);

        return row;
    }

    @FXML
    private void goBack() {
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/CustomerCRM.fxml"));
            javafx.scene.Parent root = loader.load();

            javafx.stage.Stage stage = (javafx.stage.Stage) backButton.getScene().getWindow();
            stage.getScene().setRoot(root);

            System.out.println("Navigated back to Customer CRM from Customer History");

        } catch (Exception e) {
            System.err.println("Error navigating back to Customer CRM: " + e.getMessage());
            e.printStackTrace();
            showAlert("Navigation Error", "Failed to go back to Customer CRM: " + e.getMessage());
        }
    }

    @FXML
    private void exportHistory() {
        StringBuilder export = new StringBuilder();
        export.append("Customer History Report\n");
        export.append("======================\n\n");
        export.append("Customer: ").append(currentCustomer.getName()).append("\n");
        export.append("Mobile: ").append(currentCustomer.getPhone()).append("\n");
        export.append("Max Order: ").append(maxOrderLabel.getText()).append("\n");
        export.append("Average Bill: ").append(averageBillLabel.getText()).append("\n");
        export.append("Coming Since: ").append(comingSinceLabel.getText()).append("\n");
        export.append("Total Visits: ").append(visitsLabel.getText()).append("\n\n");
        
        export.append("Order History:\n");
        export.append("--------------\n");
        for (Order order : orderHistory) {
            export.append(String.format("%s | %s | %s | %s | %s | ₹%.2f\n",
                order.getOrderDate().format(DateTimeFormatter.ofPattern("dd MMM, yyyy HH:mm")),
                order.getOrderType(),
                order.getPaymentMethod(),
                order.getItemName(),
                order.getOutlet(),
                order.getTotalAmount()));
        }
        
        showAlert("Export Successful", "Customer history has been exported successfully!\n\n" + export.toString());
    }

    @FXML
    private void printHistory() {
        showAlert("Print History", "Customer history report has been sent to printer!\n\n" +
            "Customer: " + currentCustomer.getName() + "\n" +
            "Total Orders: " + orderHistory.size() + "\n" +
            "Average Bill: " + averageBillLabel.getText() + "\n" +
            "Print job queued successfully.");
    }

    @FXML
    private void sendEmailReport() {
        showAlert("Email Report", "Customer history report has been sent via email!\n\n" +
            "To: " + currentCustomer.getEmail() + "\n" +
            "Subject: Your Order History Report\n" +
            "Customer: " + currentCustomer.getName() + "\n" +
            "Total Orders: " + orderHistory.size() + "\n" +
            "Average Bill: " + averageBillLabel.getText() + "\n\n" +
            "Email sent successfully!");
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    // Method to set customer data from external source
    public void setCustomer(Customer customer) {
        this.currentCustomer = customer;
        if (customerNameLabel != null) {
            displayCustomerInfo();
        }
    }

    // Method to set order history from external source
    public void setOrderHistory(List<Order> orders) {
        this.orderHistory = orders;
        if (orderHistoryContainer != null) {
            displayOrderHistory();
        }
    }
}
