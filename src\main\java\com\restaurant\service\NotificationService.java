package com.restaurant.service;

import com.restaurant.model.Notification;
import com.restaurant.model.Notification.NotificationType;
import com.restaurant.model.Notification.Priority;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * Notification service for managing restaurant notifications
 * Handles online orders, KOT alerts, billing notifications, scanner events
 */
public class NotificationService {
    
    private static NotificationService instance;
    private final ObservableList<Notification> notifications;
    private final List<Consumer<Notification>> notificationListeners;
    private final ScheduledExecutorService scheduler;
    private int nextId = 1;
    
    private NotificationService() {
        this.notifications = FXCollections.observableArrayList();
        this.notificationListeners = new ArrayList<>();
        this.scheduler = Executors.newScheduledThreadPool(2);
        
        // Initialize with sample notifications
        initializeSampleNotifications();
        
        // Start periodic checks
        startPeriodicChecks();
    }
    
    public static synchronized NotificationService getInstance() {
        if (instance == null) {
            instance = new NotificationService();
        }
        return instance;
    }
    
    /**
     * Add a new notification
     */
    public void addNotification(Notification notification) {
        notification.setId(nextId++);
        Platform.runLater(() -> {
            notifications.add(0, notification); // Add to beginning for latest first
            
            // Keep only last 100 notifications
            if (notifications.size() > 100) {
                notifications.remove(notifications.size() - 1);
            }
            
            // Notify listeners
            notificationListeners.forEach(listener -> listener.accept(notification));
        });
    }
    
    /**
     * Add notification listener
     */
    public void addNotificationListener(Consumer<Notification> listener) {
        notificationListeners.add(listener);
    }
    
    /**
     * Get all notifications
     */
    public ObservableList<Notification> getNotifications() {
        return notifications;
    }
    
    /**
     * Get unread notifications count
     */
    public long getUnreadCount() {
        return notifications.stream().filter(n -> !n.isRead()).count();
    }
    
    /**
     * Mark notification as read
     */
    public void markAsRead(int notificationId) {
        notifications.stream()
            .filter(n -> n.getId() == notificationId)
            .findFirst()
            .ifPresent(n -> n.setRead(true));
    }
    
    /**
     * Mark all notifications as read
     */
    public void markAllAsRead() {
        notifications.forEach(n -> n.setRead(true));
    }
    
    /**
     * Clear all notifications
     */
    public void clearAll() {
        Platform.runLater(() -> notifications.clear());
    }
    
    /**
     * Get notifications by type
     */
    public List<Notification> getNotificationsByType(NotificationType type) {
        return notifications.stream()
            .filter(n -> n.getType() == type)
            .toList();
    }
    
    /**
     * Get urgent notifications
     */
    public List<Notification> getUrgentNotifications() {
        return notifications.stream()
            .filter(n -> n.getPriority() == Priority.URGENT)
            .toList();
    }

    /**
     * Get notification count by type
     */
    public long getNotificationCountByType(NotificationType type) {
        return notifications.stream()
            .filter(n -> n.getType() == type)
            .count();
    }

    /**
     * Get urgent notifications count
     */
    public long getUrgentNotificationCount() {
        return notifications.stream()
            .filter(n -> n.getPriority() == Priority.URGENT)
            .count();
    }

    /**
     * Get total notifications count
     */
    public long getTotalNotificationCount() {
        return notifications.size();
    }
    
    // Convenience methods for creating specific notifications
    
    public void notifyOnlineOrder(String platform, String orderNumber, double amount) {
        Notification notification = Notification.createOnlineOrder(platform, orderNumber, amount);
        addNotification(notification);

        // The EnhancedNotificationPanelController will handle the sound management
        // when it receives this notification through the listener
        System.out.println("📱 New online order notification: " + platform + " #" + orderNumber + " - ₹" + amount);
    }
    
    public void notifyKOTAlert(String tableNumber, int waitingMinutes) {
        addNotification(Notification.createKOTAlert(tableNumber, waitingMinutes));
    }
    
    public void notifyBillingRequest(String tableNumber, double amount) {
        addNotification(Notification.createBillingAlert(tableNumber, amount));
    }
    
    public void notifyLowStock(String itemName, int remainingStock) {
        addNotification(Notification.createInventoryAlert(itemName, remainingStock));
    }
    
    public void notifyQRScan(String qrData, String tableNumber) {
        addNotification(Notification.createScannerAlert(qrData, tableNumber));
    }
    
    public void notifyKitchenUpdate(String orderNumber, String status) {
        addNotification(Notification.createKitchenAlert(orderNumber, status));
    }
    
    public void notifyTableAlert(String tableNumber, String message) {
        addNotification(new Notification(
            NotificationType.TABLE_ALERT,
            Priority.MEDIUM,
            "Table " + tableNumber + " Alert",
            message,
            "Table " + tableNumber
        ));
    }
    
    public void notifyPaymentReceived(String method, double amount, String reference) {
        addNotification(new Notification(
            NotificationType.PAYMENT,
            Priority.LOW,
            "Payment Received",
            method + " - ₹" + String.format("%.2f", amount) + " (Ref: " + reference + ")",
            "Payment"
        ));
    }
    
    public void notifySystemAlert(String title, String message, Priority priority) {
        addNotification(new Notification(
            NotificationType.SYSTEM,
            priority,
            title,
            message,
            "System"
        ));
    }
    
    /**
     * Initialize sample notifications for demonstration
     */
    private void initializeSampleNotifications() {
        // Online orders
        notifyOnlineOrder("Swiggy", "SW12345", 450.00);
        notifyOnlineOrder("Zomato", "ZM67890", 320.50);
        
        // KOT alerts
        notifyKOTAlert("5", 18);
        notifyKOTAlert("12", 25);
        
        // Billing requests
        notifyBillingRequest("8", 1250.00);
        
        // Inventory alerts
        notifyLowStock("Chicken Breast", 3);
        notifyLowStock("Basmati Rice", 8);
        
        // Kitchen updates
        notifyKitchenUpdate("ORD001", "Ready to serve");
        notifyKitchenUpdate("ORD002", "In preparation");
        
        // Scanner events
        notifyQRScan("TABLE_MENU_15", "15");
        
        // Payment notifications
        notifyPaymentReceived("UPI", 850.00, "TXN123456");
        
        // System alerts
        notifySystemAlert("Daily Backup", "Backup completed successfully", Priority.LOW);
        notifySystemAlert("Printer Issue", "Kitchen printer offline", Priority.HIGH);
    }
    
    /**
     * Start periodic checks for time-sensitive notifications
     */
    private void startPeriodicChecks() {
        // Check for long-waiting KOTs every 2 minutes
        scheduler.scheduleAtFixedRate(this::checkLongWaitingKOTs, 2, 2, TimeUnit.MINUTES);
        
        // Check for inventory alerts every 30 minutes
        scheduler.scheduleAtFixedRate(this::checkInventoryLevels, 30, 30, TimeUnit.MINUTES);
        
        // Simulate online orders every 5-15 minutes
        scheduler.scheduleAtFixedRate(this::simulateOnlineOrders, 5, 10, TimeUnit.MINUTES);
    }
    
    private void checkLongWaitingKOTs() {
        // This would typically check database for actual long-waiting orders
        // For demo, we'll simulate some alerts
        if (Math.random() < 0.3) { // 30% chance
            int tableNumber = (int) (Math.random() * 20) + 1;
            int waitingTime = (int) (Math.random() * 10) + 15; // 15-25 minutes
            notifyKOTAlert(String.valueOf(tableNumber), waitingTime);
        }
    }
    
    private void checkInventoryLevels() {
        // This would typically check actual inventory levels
        // For demo, we'll simulate some low stock alerts
        String[] items = {"Tomatoes", "Onions", "Chicken", "Paneer", "Rice", "Oil"};
        if (Math.random() < 0.2) { // 20% chance
            String item = items[(int) (Math.random() * items.length)];
            int stock = (int) (Math.random() * 10) + 1; // 1-10 units
            notifyLowStock(item, stock);
        }
    }
    
    private void simulateOnlineOrders() {
        // Simulate incoming online orders
        if (Math.random() < 0.4) { // 40% chance
            String[] platforms = {"Swiggy", "Zomato", "Uber Eats"};
            String platform = platforms[(int) (Math.random() * platforms.length)];
            String orderNumber = platform.substring(0, 2).toUpperCase() + 
                               String.format("%05d", (int) (Math.random() * 99999));
            double amount = Math.round((Math.random() * 1000 + 200) * 100.0) / 100.0; // 200-1200
            notifyOnlineOrder(platform, orderNumber, amount);
        }
    }
    
    /**
     * Shutdown the service
     */
    public void shutdown() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
        }
    }
}
