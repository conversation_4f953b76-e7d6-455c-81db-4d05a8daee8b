package com.restaurant.service;

import com.restaurant.model.VoiceCommand;
import com.restaurant.controller.VoiceInputController;

import java.util.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * Advanced Voice Command Processor with NLP-like capabilities
 * Handles intent recognition, entity extraction, and command execution
 */
public class VoiceCommandProcessor {

    private VoiceInputController.VoiceCommandCallback callback;
    private Map<String, CommandPattern> commandPatterns;
    private List<String> commonSuggestions;

    public VoiceCommandProcessor() {
        initializeCommandPatterns();
        initializeCommonSuggestions();
    }

    private void initializeCommandPatterns() {
        commandPatterns = new HashMap<>();
        
        // Forecasting Commands
        commandPatterns.put("FORECAST", new CommandPattern(
            Arrays.asList(
                "forecast.*?(?:for|next)\\s+(\\d+)\\s+(days?|weeks?|months?)",
                "show.*?forecast.*?(\\d+)\\s+(days?|weeks?|months?)",
                "predict.*?sales.*?(\\d+)\\s+(days?|weeks?|months?)",
                "generate.*?forecast.*?(\\d+)\\s+(days?|weeks?|months?)"
            ),
            "Generate sales forecast",
            "FORECASTING"
        ));

        // Search Commands
        commandPatterns.put("SEARCH", new CommandPattern(
            Arrays.asList(
                "search\\s+(?:for\\s+)?(.+)",
                "find\\s+(.+)",
                "look\\s+for\\s+(.+)",
                "show\\s+me\\s+(.+)"
            ),
            "Search for items",
            "SEARCH"
        ));

        // Table Management Commands
        commandPatterns.put("TABLE", new CommandPattern(
            Arrays.asList(
                "open\\s+table\\s+(\\d+)",
                "show\\s+table\\s+(\\d+)",
                "table\\s+(\\d+)\\s+(?:order|details|status)",
                "go\\s+to\\s+table\\s+(\\d+)"
            ),
            "Manage table operations",
            "TABLE_MANAGEMENT"
        ));

        // Navigation Commands
        commandPatterns.put("NAVIGATE", new CommandPattern(
            Arrays.asList(
                "go\\s+to\\s+(\\w+)",
                "open\\s+(\\w+)\\s+(?:section|page|panel)",
                "navigate\\s+to\\s+(\\w+)",
                "show\\s+(\\w+)\\s+(?:dashboard|panel|section)"
            ),
            "Navigate to different sections",
            "NAVIGATION"
        ));

        // Reports Commands
        commandPatterns.put("REPORTS", new CommandPattern(
            Arrays.asList(
                "show\\s+(?:top\\s+)?(?:selling|popular)\\s+items",
                "generate\\s+(?:sales\\s+)?report",
                "export\\s+(?:sales\\s+)?(?:data|report)",
                "show\\s+(?:sales\\s+)?(?:trends|analytics)"
            ),
            "Generate reports and analytics",
            "REPORTS"
        ));

        // Filter Commands
        commandPatterns.put("FILTER", new CommandPattern(
            Arrays.asList(
                "reset\\s+(?:all\\s+)?filters?",
                "clear\\s+(?:all\\s+)?filters?",
                "filter\\s+by\\s+(.+)",
                "show\\s+only\\s+(.+)"
            ),
            "Manage filters and views",
            "FILTER"
        ));

        // Category-specific Commands
        commandPatterns.put("CATEGORY", new CommandPattern(
            Arrays.asList(
                "forecast.*?(?:for\\s+)?(appetizers?|main\\s+course|beverages?|desserts?)",
                "show.*?(appetizers?|main\\s+course|beverages?|desserts?)",
                "(?:appetizers?|main\\s+course|beverages?|desserts?).*?(?:sales|forecast)"
            ),
            "Category-specific operations",
            "CATEGORY"
        ));

        // Date-specific Commands
        commandPatterns.put("DATE", new CommandPattern(
            Arrays.asList(
                "(?:for|in|during)\\s+(january|february|march|april|may|june|july|august|september|october|november|december)",
                "(?:for|in|during)\\s+(\\d{4})",
                "(?:this|last|next)\\s+(week|month|year)",
                "(?:today|yesterday|tomorrow)"
            ),
            "Date-specific operations",
            "DATE"
        ));
    }

    private void initializeCommonSuggestions() {
        commonSuggestions = Arrays.asList(
            "forecast sales for next 30 days",
            "search butter chicken",
            "open table 5 order",
            "show top selling items",
            "generate sales report",
            "reset all filters",
            "navigate to billing section",
            "forecast for main course",
            "show trends for this month",
            "export sales data"
        );
    }

    public VoiceCommand processCommand(String input) {
        if (input == null || input.trim().isEmpty()) {
            return new VoiceCommand(input, false, "UNKNOWN", "Empty command", null);
        }

        String normalizedInput = input.toLowerCase().trim();
        
        // Try to match against known patterns
        for (Map.Entry<String, CommandPattern> entry : commandPatterns.entrySet()) {
            String commandType = entry.getKey();
            CommandPattern pattern = entry.getValue();
            
            for (String regex : pattern.getPatterns()) {
                Pattern p = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
                Matcher m = p.matcher(normalizedInput);
                
                if (m.find()) {
                    Map<String, String> entities = extractEntities(m);
                    String description = generateDescription(commandType, entities, normalizedInput);
                    
                    return new VoiceCommand(
                        input,
                        true,
                        commandType,
                        description,
                        entities
                    );
                }
            }
        }

        // If no exact match, try fuzzy matching
        VoiceCommand fuzzyMatch = attemptFuzzyMatch(normalizedInput);
        if (fuzzyMatch != null) {
            return fuzzyMatch;
        }

        // Return unrecognized command
        return new VoiceCommand(input, false, "UNKNOWN", "Command not recognized", null);
    }

    private Map<String, String> extractEntities(Matcher matcher) {
        Map<String, String> entities = new HashMap<>();
        
        for (int i = 1; i <= matcher.groupCount(); i++) {
            String group = matcher.group(i);
            if (group != null) {
                entities.put("entity_" + i, group.trim());
            }
        }
        
        return entities;
    }

    private String generateDescription(String commandType, Map<String, String> entities, String originalInput) {
        switch (commandType) {
            case "FORECAST":
                String period = entities.getOrDefault("entity_1", "30");
                String unit = entities.getOrDefault("entity_2", "days");
                return String.format("Generate sales forecast for next %s %s", period, unit);
                
            case "SEARCH":
                String searchTerm = entities.getOrDefault("entity_1", "items");
                return String.format("Search for '%s' in menu", searchTerm);
                
            case "TABLE":
                String tableNumber = entities.getOrDefault("entity_1", "unknown");
                return String.format("Open table %s order details", tableNumber);
                
            case "NAVIGATE":
                String section = entities.getOrDefault("entity_1", "dashboard");
                return String.format("Navigate to %s section", section);
                
            case "REPORTS":
                return "Generate sales report and analytics";
                
            case "FILTER":
                return "Reset all filters and views";
                
            case "CATEGORY":
                String category = entities.getOrDefault("entity_1", "all categories");
                return String.format("Show data for %s", category);
                
            case "DATE":
                String dateRef = entities.getOrDefault("entity_1", "current period");
                return String.format("Filter by date: %s", dateRef);
                
            default:
                return "Execute: " + originalInput;
        }
    }

    private VoiceCommand attemptFuzzyMatch(String input) {
        // Simple fuzzy matching based on keywords
        Map<String, Integer> scores = new HashMap<>();
        
        // Forecasting keywords
        if (containsAny(input, Arrays.asList("forecast", "predict", "future", "trend", "growth"))) {
            scores.put("FORECAST", scores.getOrDefault("FORECAST", 0) + 3);
        }
        
        // Search keywords
        if (containsAny(input, Arrays.asList("search", "find", "look", "show"))) {
            scores.put("SEARCH", scores.getOrDefault("SEARCH", 0) + 2);
        }
        
        // Table keywords
        if (containsAny(input, Arrays.asList("table", "order", "bill"))) {
            scores.put("TABLE", scores.getOrDefault("TABLE", 0) + 3);
        }
        
        // Navigation keywords
        if (containsAny(input, Arrays.asList("go", "navigate", "open", "dashboard"))) {
            scores.put("NAVIGATE", scores.getOrDefault("NAVIGATE", 0) + 2);
        }
        
        // Find highest scoring command
        String bestMatch = scores.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse(null);
        
        if (bestMatch != null && scores.get(bestMatch) >= 2) {
            return new VoiceCommand(
                input,
                true,
                bestMatch,
                "Fuzzy match: " + generateDescription(bestMatch, new HashMap<>(), input),
                new HashMap<>()
            );
        }
        
        return null;
    }

    private boolean containsAny(String text, List<String> keywords) {
        return keywords.stream().anyMatch(text::contains);
    }

    public List<String> getSuggestions(String partialInput) {
        if (partialInput == null || partialInput.length() < 2) {
            return commonSuggestions.subList(0, Math.min(5, commonSuggestions.size()));
        }
        
        String normalized = partialInput.toLowerCase();
        List<String> suggestions = new ArrayList<>();
        
        // Add matching common suggestions
        for (String suggestion : commonSuggestions) {
            if (suggestion.toLowerCase().contains(normalized) || 
                calculateSimilarity(normalized, suggestion.toLowerCase()) > 0.3) {
                suggestions.add(suggestion);
            }
        }
        
        // Add contextual suggestions based on partial input
        if (normalized.contains("forecast") || normalized.contains("predict")) {
            suggestions.addAll(Arrays.asList(
                "forecast sales for next 7 days",
                "forecast for main course category",
                "predict growth trends for July"
            ));
        }
        
        if (normalized.contains("search") || normalized.contains("find")) {
            suggestions.addAll(Arrays.asList(
                "search butter chicken",
                "find popular items",
                "search by category"
            ));
        }
        
        if (normalized.contains("table")) {
            suggestions.addAll(Arrays.asList(
                "open table 1 order",
                "show table 5 status",
                "table 3 bill details"
            ));
        }
        
        // Remove duplicates and limit results
        return suggestions.stream()
            .distinct()
            .limit(8)
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    private double calculateSimilarity(String s1, String s2) {
        // Simple Levenshtein distance-based similarity
        int maxLen = Math.max(s1.length(), s2.length());
        if (maxLen == 0) return 1.0;
        
        int distance = levenshteinDistance(s1, s2);
        return 1.0 - (double) distance / maxLen;
    }

    private int levenshteinDistance(String s1, String s2) {
        int[][] dp = new int[s1.length() + 1][s2.length() + 1];
        
        for (int i = 0; i <= s1.length(); i++) {
            for (int j = 0; j <= s2.length(); j++) {
                if (i == 0) {
                    dp[i][j] = j;
                } else if (j == 0) {
                    dp[i][j] = i;
                } else {
                    dp[i][j] = Math.min(
                        dp[i - 1][j - 1] + (s1.charAt(i - 1) == s2.charAt(j - 1) ? 0 : 1),
                        Math.min(dp[i - 1][j] + 1, dp[i][j - 1] + 1)
                    );
                }
            }
        }
        
        return dp[s1.length()][s2.length()];
    }

    public void executeCommand(VoiceCommand command) {
        if (command == null || !command.isRecognized()) {
            if (callback != null) {
                callback.onCommandFailed("Invalid or unrecognized command");
            }
            return;
        }

        try {
            // Simulate command execution based on type
            switch (command.getCommandType()) {
                case "FORECAST":
                    executeForecastCommand(command);
                    break;
                case "SEARCH":
                    executeSearchCommand(command);
                    break;
                case "TABLE":
                    executeTableCommand(command);
                    break;
                case "NAVIGATE":
                    executeNavigationCommand(command);
                    break;
                case "REPORTS":
                    executeReportsCommand(command);
                    break;
                case "FILTER":
                    executeFilterCommand(command);
                    break;
                default:
                    if (callback != null) {
                        callback.onCommandFailed("Command type not supported: " + command.getCommandType());
                    }
                    return;
            }

            if (callback != null) {
                callback.onCommandExecuted(command);
            }

        } catch (Exception e) {
            if (callback != null) {
                callback.onCommandFailed("Error executing command: " + e.getMessage());
            }
        }
    }

    private void executeForecastCommand(VoiceCommand command) {
        // Integration point for AI Forecaster
        System.out.println("Executing forecast command: " + command.getDescription());
        // This would trigger the AI Forecaster with the specified parameters
    }

    private void executeSearchCommand(VoiceCommand command) {
        // Integration point for search functionality
        System.out.println("Executing search command: " + command.getDescription());
        // This would trigger search in menu, inventory, or other sections
    }

    private void executeTableCommand(VoiceCommand command) {
        // Integration point for table management
        System.out.println("Executing table command: " + command.getDescription());
        // This would open table details, orders, or billing
    }

    private void executeNavigationCommand(VoiceCommand command) {
        // Integration point for navigation
        System.out.println("Executing navigation command: " + command.getDescription());
        // This would navigate to different sections of the application
    }

    private void executeReportsCommand(VoiceCommand command) {
        // Integration point for reports
        System.out.println("Executing reports command: " + command.getDescription());
        // This would generate or display reports
    }

    private void executeFilterCommand(VoiceCommand command) {
        // Integration point for filter management
        System.out.println("Executing filter command: " + command.getDescription());
        // This would reset or apply filters
    }

    public void setCallback(VoiceInputController.VoiceCommandCallback callback) {
        this.callback = callback;
    }

    // Helper class for command patterns
    private static class CommandPattern {
        private final List<String> patterns;
        private final String description;
        private final String category;

        public CommandPattern(List<String> patterns, String description, String category) {
            this.patterns = patterns;
            this.description = description;
            this.category = category;
        }

        public List<String> getPatterns() { return patterns; }
        public String getDescription() { return description; }
        public String getCategory() { return category; }
    }
}
