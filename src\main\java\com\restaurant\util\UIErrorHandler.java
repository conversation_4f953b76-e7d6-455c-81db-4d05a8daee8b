package com.restaurant.util;

import javafx.application.Platform;
import javafx.scene.control.Alert;
import javafx.scene.control.ButtonType;

/**
 * Utility class for handling UI errors and preventing NullPointerExceptions
 */
public class UIErrorHandler {
    
    private static volatile boolean isDialogOpen = false;
    
    /**
     * Safe alert dialog that won't crash if called multiple times or from any thread
     */
    public static void showSafeAlert(String title, String message) {
        try {
            if (!isDialogOpen) {
                isDialogOpen = true;
                
                // Ensure we're on the JavaFX Application Thread
                if (Platform.isFxApplicationThread()) {
                    showAlertInternal(title, message);
                } else {
                    Platform.runLater(() -> showAlertInternal(title, message));
                }
            }
        } catch (Exception e) {
            System.err.println("Critical error in showSafeAlert: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void showAlertInternal(String title, String message) {
        try {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle(title != null ? title : "Error");
            alert.setHeaderText(null);
            alert.setContentText(message != null ? message : "An unknown error occurred");
            alert.showAndWait();
        } catch (Exception e) {
            System.err.println("Failed to show alert dialog: " + e.getMessage());
            e.printStackTrace();
        } finally {
            isDialogOpen = false;
        }
    }
    
    /**
     * Safe information dialog
     */
    public static void showSafeInfo(String title, String message) {
        try {
            if (!isDialogOpen) {
                isDialogOpen = true;
                
                if (Platform.isFxApplicationThread()) {
                    showInfoInternal(title, message);
                } else {
                    Platform.runLater(() -> showInfoInternal(title, message));
                }
            }
        } catch (Exception e) {
            System.err.println("Critical error in showSafeInfo: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void showInfoInternal(String title, String message) {
        try {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title != null ? title : "Information");
            alert.setHeaderText(null);
            alert.setContentText(message != null ? message : "No information available");
            alert.showAndWait();
        } catch (Exception e) {
            System.err.println("Failed to show info dialog: " + e.getMessage());
            e.printStackTrace();
        } finally {
            isDialogOpen = false;
        }
    }
    
    /**
     * Safe confirmation dialog
     */
    public static boolean showSafeConfirmation(String title, String message) {
        try {
            if (isDialogOpen) {
                return false; // Don't show multiple dialogs
            }
            
            isDialogOpen = true;
            
            Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
            alert.setTitle(title != null ? title : "Confirmation");
            alert.setHeaderText(null);
            alert.setContentText(message != null ? message : "Are you sure?");
            
            return alert.showAndWait().orElse(ButtonType.CANCEL) == ButtonType.OK;
            
        } catch (Exception e) {
            System.err.println("Failed to show confirmation dialog: " + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            isDialogOpen = false;
        }
    }
    
    /**
     * Check if an object is null and show error if it is
     */
    public static boolean checkNotNull(Object obj, String objectName) {
        if (obj == null) {
            System.err.println(objectName + " is null");
            showSafeAlert("UI Error", objectName + " is not properly initialized. Please restart the application.");
            return false;
        }
        return true;
    }
    
    /**
     * Safe execution wrapper that catches and handles exceptions
     */
    public static void safeExecute(String operationName, Runnable operation) {
        try {
            System.out.println("Executing: " + operationName);
            operation.run();
            System.out.println("Successfully completed: " + operationName);
        } catch (Exception e) {
            System.err.println("Error in " + operationName + ": " + e.getMessage());
            e.printStackTrace();
            showSafeAlert("Error", "Failed to " + operationName + ": " + e.getMessage());
        }
    }
    
    /**
     * Safe execution wrapper with return value
     */
    public static <T> T safeExecuteWithReturn(String operationName, java.util.function.Supplier<T> operation, T defaultValue) {
        try {
            System.out.println("Executing: " + operationName);
            T result = operation.get();
            System.out.println("Successfully completed: " + operationName);
            return result;
        } catch (Exception e) {
            System.err.println("Error in " + operationName + ": " + e.getMessage());
            e.printStackTrace();
            showSafeAlert("Error", "Failed to " + operationName + ": " + e.getMessage());
            return defaultValue;
        }
    }
    
    /**
     * Log error without showing dialog (for non-critical errors)
     */
    public static void logError(String context, Exception e) {
        System.err.println("Error in " + context + ": " + e.getMessage());
        e.printStackTrace();
    }
    
    /**
     * Check if we're on the JavaFX Application Thread
     */
    public static boolean isOnFXThread() {
        return Platform.isFxApplicationThread();
    }
    
    /**
     * Run on FX thread safely
     */
    public static void runOnFXThread(Runnable action) {
        if (Platform.isFxApplicationThread()) {
            action.run();
        } else {
            Platform.runLater(action);
        }
    }
}
