package com.restaurant.controller;


import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.scene.shape.Circle;

import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Controller for Category Item Manager interface
 * Manages menu items by categories with status indicators
 */
public class CategoryItemManagerController implements Initializable {

    // Header Filter Buttons
    @FXML private Button allBtn;
    @FXML private Button recentBtn;
    @FXML private Button homeWebsiteBtn;
    
    // Categories Sidebar
    @FXML private VBox categoriesContainer;
    
    // Items Content
    @FXML private Label contentTitleLabel;
    @FXML private Button addItemBtn;
    @FXML private Button confirmBtn;
    @FXML private VBox itemsContainer;
    
    // Data
    private List<String> categories = new ArrayList<>();
    private List<CategoryItem> categoryItems = new ArrayList<>();
    private String selectedCategory = "Started";
    private String currentFilter = "All";
    
    // Category buttons for selection tracking
    private List<Button> categoryButtons = new ArrayList<>();

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        setupSampleData();
        setupCategories();
        setupFilterButtons();
        showCategoryItems("Started");
        
        System.out.println("Category Item Manager Controller initialized");
    }
    
    private void setupSampleData() {
        // Create sample categories with items
        categoryItems.addAll(Arrays.asList(
            // Started category items
            new CategoryItem("Veg Manchurian", "Started", ItemStatus.AVAILABLE, true),
            new CategoryItem("French Fries", "Started", ItemStatus.UNAVAILABLE, false),
            new CategoryItem("Tomato Soup", "Started", ItemStatus.AVAILABLE, true),
            new CategoryItem("Chicken Wings", "Started", ItemStatus.AVAILABLE, true),
            
            // Main Course items
            new CategoryItem("Chicken Biryani", "Main Course", ItemStatus.AVAILABLE, true),
            new CategoryItem("Paneer Butter Masala", "Main Course", ItemStatus.AVAILABLE, true),
            new CategoryItem("Dal Tadka", "Main Course", ItemStatus.UNAVAILABLE, false),
            new CategoryItem("Veg Fried Rice", "Main Course", ItemStatus.AVAILABLE, true),
            new CategoryItem("Mutton Curry", "Main Course", ItemStatus.AVAILABLE, true),
            
            // Breads items
            new CategoryItem("Butter Naan", "Breads", ItemStatus.AVAILABLE, true),
            new CategoryItem("Garlic Bread", "Breads", ItemStatus.AVAILABLE, true),
            new CategoryItem("Roti", "Breads", ItemStatus.UNAVAILABLE, false),
            new CategoryItem("Cheese Naan", "Breads", ItemStatus.AVAILABLE, true),
            
            // Beverages items
            new CategoryItem("Mango Lassi", "Beverages", ItemStatus.AVAILABLE, true),
            new CategoryItem("Cold Coffee", "Beverages", ItemStatus.AVAILABLE, true),
            new CategoryItem("Fresh Lime Water", "Beverages", ItemStatus.UNAVAILABLE, false),
            new CategoryItem("Masala Chai", "Beverages", ItemStatus.AVAILABLE, true),
            new CategoryItem("Chocolate Shake", "Beverages", ItemStatus.AVAILABLE, true),

            // Some additional inactive items for demonstration
            new CategoryItem("Fish Curry", "Main Course", ItemStatus.UNAVAILABLE, false),
            new CategoryItem("Kulfi", "Beverages", ItemStatus.UNAVAILABLE, false),
            new CategoryItem("Samosa", "Started", ItemStatus.UNAVAILABLE, false)
        ));
    }
    
    private void setupCategories() {
        // Create category buttons
        String[] categoryNames = {"Started", "Main Course", "Breads", "Beverages", "Inactive"};
        String[] categoryColors = {"#28a745", "#28a745", "#ffc107", "#dc3545", "#6c757d"}; // Green, Green, Yellow, Red, Gray

        for (int i = 0; i < categoryNames.length; i++) {
            String categoryName = categoryNames[i];
            String color = categoryColors[i];

            Button categoryBtn = createCategoryButton(categoryName, color);
            categoryButtons.add(categoryBtn);
            categoriesContainer.getChildren().add(categoryBtn);
        }

        // Select first category by default
        if (!categoryButtons.isEmpty()) {
            selectCategoryButton(categoryButtons.get(0));
        }
    }
    
    private Button createCategoryButton(String categoryName, String color) {
        Button button = new Button();

        // Create the content with color indicator and text
        HBox content = new HBox(10);
        content.setAlignment(Pos.CENTER_LEFT);

        // Color indicator circle
        Circle colorIndicator = new Circle(6);
        colorIndicator.setFill(Color.web(color));

        // Category name label
        Label nameLabel = new Label(categoryName);
        nameLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #495057;");

        // Add special styling for Inactive category
        if ("Inactive".equals(categoryName)) {
            nameLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #6c757d; -fx-font-style: italic;");
            // Add a small icon to indicate inactive items
            Label iconLabel = new Label("💤");
            iconLabel.setStyle("-fx-font-size: 12px;");
            content.getChildren().addAll(colorIndicator, nameLabel, iconLabel);
        } else {
            content.getChildren().addAll(colorIndicator, nameLabel);
        }

        button.setGraphic(content);
        button.setText(""); // Clear text since we're using graphic

        // Styling
        button.setMaxWidth(Double.MAX_VALUE);
        button.setAlignment(Pos.CENTER_LEFT);
        button.getStyleClass().add("category-button");

        // Click handler
        button.setOnAction(e -> {
            selectCategoryButton(button);
            showCategoryItems(categoryName);
        });

        return button;
    }
    
    private void selectCategoryButton(Button selectedButton) {
        // Remove active class from all buttons
        categoryButtons.forEach(btn -> btn.getStyleClass().remove("category-button-active"));
        
        // Add active class to selected button
        selectedButton.getStyleClass().add("category-button-active");
    }
    
    private void setupFilterButtons() {
        // Set initial active state
        setActiveFilterButton(allBtn);
    }
    
    private void setActiveFilterButton(Button activeButton) {
        // Remove active class from all filter buttons
        allBtn.getStyleClass().remove("filter-btn-active");
        recentBtn.getStyleClass().remove("filter-btn-active");
        homeWebsiteBtn.getStyleClass().remove("filter-btn-active");
        
        // Add active class to selected button
        activeButton.getStyleClass().add("filter-btn-active");
    }
    
    private void showCategoryItems(String categoryName) {
        selectedCategory = categoryName;
        contentTitleLabel.setText(categoryName);

        List<CategoryItem> filteredItems;

        if ("Inactive".equals(categoryName)) {
            // Show all inactive/disabled items from all categories
            filteredItems = categoryItems.stream()
                .filter(item -> !item.isEnabled())
                .collect(Collectors.toList());
        } else {
            // Filter items by category (only active items)
            filteredItems = categoryItems.stream()
                .filter(item -> item.getCategory().equals(categoryName) && item.isEnabled())
                .collect(Collectors.toList());
        }

        // Clear and populate items container
        itemsContainer.getChildren().clear();

        for (CategoryItem item : filteredItems) {
            HBox itemRow = createItemRow(item);
            itemsContainer.getChildren().add(itemRow);
        }

        // Update content title with count
        int count = filteredItems.size();
        contentTitleLabel.setText(categoryName + " (" + count + " items)");
    }
    
    private HBox createItemRow(CategoryItem item) {
        HBox row = new HBox(20);
        row.setAlignment(Pos.CENTER_LEFT);
        row.getStyleClass().add("item-row");
        row.setPadding(new Insets(12, 15, 12, 15));
        
        // Status indicator (radio button style)
        RadioButton statusRadio = new RadioButton();
        statusRadio.setSelected(item.isSelected());
        statusRadio.getStyleClass().add("status-radio");
        
        // Status color indicator
        Circle statusIndicator = new Circle(6);
        statusIndicator.setFill(item.getStatus() == ItemStatus.AVAILABLE ? 
            Color.web("#28a745") : Color.web("#dc3545"));
        
        // Item name
        Label nameLabel = new Label(item.getName());
        nameLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #495057; -fx-font-weight: normal;");
        
        // Toggle switch
        ToggleSwitch toggleSwitch = new ToggleSwitch(item.isEnabled());
        toggleSwitch.setOnToggleChanged(enabled -> {
            item.setEnabled(enabled);
            item.setStatus(enabled ? ItemStatus.AVAILABLE : ItemStatus.UNAVAILABLE);
            statusIndicator.setFill(enabled ? Color.web("#28a745") : Color.web("#dc3545"));
            System.out.println(item.getName() + " " + (enabled ? "enabled" : "disabled"));

            // Refresh the current view to reflect changes
            showCategoryItems(selectedCategory);
        });
        
        // Layout
        HBox statusBox = new HBox(10);
        statusBox.setAlignment(Pos.CENTER_LEFT);
        statusBox.setPrefWidth(60);
        statusBox.getChildren().addAll(statusRadio, statusIndicator);
        
        HBox nameBox = new HBox();
        nameBox.setAlignment(Pos.CENTER_LEFT);
        HBox.setHgrow(nameBox, Priority.ALWAYS);
        nameBox.getChildren().add(nameLabel);
        
        HBox toggleBox = new HBox();
        toggleBox.setAlignment(Pos.CENTER_RIGHT);
        toggleBox.setPrefWidth(100);
        toggleBox.getChildren().add(toggleSwitch);
        
        row.getChildren().addAll(statusBox, nameBox, toggleBox);
        
        // Click handler for row selection
        row.setOnMouseClicked(e -> {
            item.setSelected(!item.isSelected());
            statusRadio.setSelected(item.isSelected());
        });
        
        return row;
    }
    
    @FXML
    private void showAll() {
        currentFilter = "All";
        setActiveFilterButton(allBtn);
        showCategoryItems(selectedCategory);
    }
    
    @FXML
    private void showRecent() {
        currentFilter = "Recent";
        setActiveFilterButton(recentBtn);
        showCategoryItems(selectedCategory);
    }
    
    @FXML
    private void showHomeWebsite() {
        currentFilter = "Home website";
        setActiveFilterButton(homeWebsiteBtn);
        showCategoryItems(selectedCategory);
    }
    
    @FXML
    private void addNewItem() {
        // Don't allow adding items to Inactive category
        if ("Inactive".equals(selectedCategory)) {
            showAlert("Invalid Action", "Cannot add new items to Inactive category. Please select a different category.");
            return;
        }

        // Create a simple dialog for adding new items
        TextInputDialog dialog = new TextInputDialog();
        dialog.setTitle("Add New Item");
        dialog.setHeaderText("Add new item to " + selectedCategory);
        dialog.setContentText("Item name:");

        Optional<String> result = dialog.showAndWait();
        result.ifPresent(name -> {
            if (!name.trim().isEmpty()) {
                CategoryItem newItem = new CategoryItem(name.trim(), selectedCategory, ItemStatus.AVAILABLE, true);
                categoryItems.add(newItem);
                showCategoryItems(selectedCategory);
                System.out.println("Added new item: " + name + " to " + selectedCategory);
            }
        });
    }

    @FXML
    private void confirmChanges() {
        // Count changes made
        int activeItems = (int) categoryItems.stream().filter(CategoryItem::isEnabled).count();
        int inactiveItems = (int) categoryItems.stream().filter(item -> !item.isEnabled()).count();

        // Show confirmation dialog
        Alert confirmDialog = new Alert(Alert.AlertType.INFORMATION);
        confirmDialog.setTitle("Changes Confirmed");
        confirmDialog.setHeaderText("All changes have been saved successfully!");
        confirmDialog.setContentText(
            "Summary:\n" +
            "• Active items: " + activeItems + "\n" +
            "• Inactive items: " + inactiveItems + "\n" +
            "• Total items: " + categoryItems.size() + "\n\n" +
            "All menu item statuses have been updated."
        );

        confirmDialog.showAndWait();

        // Here you would typically save to database
        // For now, just log the action
        System.out.println("Changes confirmed - Active: " + activeItems + ", Inactive: " + inactiveItems);
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    // Inner classes
    public static class CategoryItem {
        private String name;
        private String category;
        private ItemStatus status;
        private boolean enabled;
        private boolean selected;
        
        public CategoryItem(String name, String category, ItemStatus status, boolean enabled) {
            this.name = name;
            this.category = category;
            this.status = status;
            this.enabled = enabled;
            this.selected = false;
        }
        
        // Getters and setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        
        public ItemStatus getStatus() { return status; }
        public void setStatus(ItemStatus status) { this.status = status; }
        
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        
        public boolean isSelected() { return selected; }
        public void setSelected(boolean selected) { this.selected = selected; }
    }
    
    public enum ItemStatus {
        AVAILABLE, UNAVAILABLE
    }

    // Custom Toggle Switch Component
    public static class ToggleSwitch extends HBox {
        private boolean isOn;
        private Label onLabel;
        private Label offLabel;
        private Runnable onToggleChanged;

        public ToggleSwitch(boolean initialState) {
            this.isOn = initialState;
            setupToggleSwitch();
        }

        private void setupToggleSwitch() {
            setAlignment(Pos.CENTER);
            setSpacing(0);
            getStyleClass().add("toggle-switch");

            // Off label
            offLabel = new Label("Off");
            offLabel.getStyleClass().add("toggle-label");
            offLabel.getStyleClass().add("toggle-off");
            offLabel.setPadding(new Insets(4, 8, 4, 8));
            offLabel.setOnMouseClicked(e -> toggle());

            // On label
            onLabel = new Label("On");
            onLabel.getStyleClass().add("toggle-label");
            onLabel.getStyleClass().add("toggle-on");
            onLabel.setPadding(new Insets(4, 8, 4, 8));
            onLabel.setOnMouseClicked(e -> toggle());

            getChildren().addAll(offLabel, onLabel);
            updateAppearance();
        }

        private void toggle() {
            isOn = !isOn;
            updateAppearance();
            if (onToggleChanged != null) {
                onToggleChanged.run();
            }
        }

        private void updateAppearance() {
            if (isOn) {
                onLabel.getStyleClass().add("toggle-active");
                offLabel.getStyleClass().remove("toggle-active");
            } else {
                offLabel.getStyleClass().add("toggle-active");
                onLabel.getStyleClass().remove("toggle-active");
            }
        }

        public void setOnToggleChanged(java.util.function.Consumer<Boolean> callback) {
            this.onToggleChanged = () -> callback.accept(isOn);
        }

        public boolean isOn() {
            return isOn;
        }

        public void setOn(boolean on) {
            this.isOn = on;
            updateAppearance();
        }
    }
}
