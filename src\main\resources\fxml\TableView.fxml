<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.TableViewController" stylesheets="@../css/application.css">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="module-header">
         <children>
            <Button mnemonicParsing="false" onAction="#goBack" styleClass="back-button" text="←" />
            <VBox spacing="5.0">
               <children>
                  <Label styleClass="module-title" text="Table View">
                     <font>
                        <Font name="System Bold" size="24.0" />
                     </font>
                  </Label>
                  <Label styleClass="module-subtitle" text="Manage restaurant tables and orders" />
               </children>
            </VBox>
            <Region HBox.hgrow="ALWAYS" />
            
            <!-- Action Buttons -->
            <HBox spacing="10.0" alignment="CENTER_RIGHT">
               <children>
                  <Button fx:id="refreshBtn" mnemonicParsing="false" onAction="#refreshTables" styleClass="secondary-button" text="🔄" />
                  <ToggleButton fx:id="deliveryToggle" mnemonicParsing="false" onAction="#toggleDeliveryMode" styleClass="delivery-toggle" text="Delivery" />
                  <ToggleButton fx:id="pickUpToggle" mnemonicParsing="false" onAction="#togglePickUpMode" styleClass="pickup-toggle" text="Pick Up" />
                  <Button fx:id="addTableBtn" mnemonicParsing="false" onAction="#addNewTable" styleClass="primary-button" text="+ Add Table" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </HBox>

      <!-- Control Panel -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="control-panel">
         <children>
            <Button fx:id="tableReservationBtn" mnemonicParsing="false" onAction="#openTableReservation" styleClass="action-button" text="+ Table Reservation" />
            <Button fx:id="contestationsBtn" mnemonicParsing="false" onAction="#openContestations" styleClass="action-button" text="+ Contestations" />
            
            <Region HBox.hgrow="ALWAYS" />
            
            <!-- Move KOT/Items Controls -->
            <VBox spacing="5.0" alignment="CENTER">
               <children>
                  <Label text="Move KOT / Items" styleClass="control-label" />
                  <HBox spacing="10.0" alignment="CENTER">
                     <children>
                        <ComboBox fx:id="fromTableCombo" promptText="From Table" prefWidth="120.0" />
                        <Label text="→" />
                        <ComboBox fx:id="toTableCombo" promptText="To Table" prefWidth="120.0" />
                        <Button fx:id="moveKOTBtn" mnemonicParsing="false" onAction="#moveKOT" styleClass="secondary-button" text="Move" />
                     </children>
                  </HBox>
               </children>
            </VBox>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
         </padding>
      </HBox>

      <!-- Status Legend -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="status-legend">
         <children>
            <Label text="Status:" styleClass="legend-title" />
            <HBox spacing="15.0" alignment="CENTER_LEFT">
               <children>
                  <HBox spacing="5.0" alignment="CENTER_LEFT">
                     <children>
                        <Region styleClass="legend-indicator blank-table" prefWidth="15.0" prefHeight="15.0" />
                        <Label text="Blank Table" styleClass="legend-text" />
                     </children>
                  </HBox>
                  <HBox spacing="5.0" alignment="CENTER_LEFT">
                     <children>
                        <Region styleClass="legend-indicator running-table" prefWidth="15.0" prefHeight="15.0" />
                        <Label text="Running Table" styleClass="legend-text" />
                     </children>
                  </HBox>
                  <HBox spacing="5.0" alignment="CENTER_LEFT">
                     <children>
                        <Region styleClass="legend-indicator printed-table" prefWidth="15.0" prefHeight="15.0" />
                        <Label text="Printed Table" styleClass="legend-text" />
                     </children>
                  </HBox>
                  <HBox spacing="5.0" alignment="CENTER_LEFT">
                     <children>
                        <Region styleClass="legend-indicator paid-table" prefWidth="15.0" prefHeight="15.0" />
                        <Label text="Paid Table" styleClass="legend-text" />
                     </children>
                  </HBox>
                  <HBox spacing="5.0" alignment="CENTER_LEFT">
                     <children>
                        <Region styleClass="legend-indicator running-kot-table" prefWidth="15.0" prefHeight="15.0" />
                        <Label text="Running KOT Table" styleClass="legend-text" />
                     </children>
                  </HBox>
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="10.0" />
         </padding>
      </HBox>

      <!-- Floor Selection -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="floor-selection">
         <children>
            <Label text="Ground Floor" styleClass="floor-title">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <ComboBox fx:id="floorCombo" promptText="Select Floor" prefWidth="150.0" />
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="10.0" />
         </padding>
      </HBox>

      <!-- Tables Grid -->
      <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS" styleClass="tables-scroll-pane">
         <content>
            <GridPane fx:id="tablesGrid" hgap="15.0" vgap="15.0" alignment="CENTER" styleClass="tables-grid">
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>
            </GridPane>
         </content>
      </ScrollPane>
   </children>
</VBox>
