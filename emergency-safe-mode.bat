@echo off
echo EMERGENCY SAFE MODE - Minimal Application Startup...
echo.

echo PROBLEM: Application still getting stuck and not opening
echo SOLUTION: Create minimal safe mode that bypasses problematic components
echo.

echo Step 1: Creating minimal CSS file (no complex styling)...

echo /* MINIMAL SAFE CSS - No complex styling that can cause errors */ > "src\main\resources\css\application-safe.css"
echo .root { -fx-font-family: Arial; -fx-font-size: 14px; -fx-background-color: white; } >> "src\main\resources\css\application-safe.css"
echo .button { -fx-background-color: lightblue; -fx-text-fill: black; -fx-padding: 5px; } >> "src\main\resources\css\application-safe.css"
echo .text-field { -fx-background-color: white; -fx-border-color: gray; -fx-padding: 5px; } >> "src\main\resources\css\application-safe.css"
echo .table-view { -fx-background-color: white; } >> "src\main\resources\css\application-safe.css"

echo Step 2: Backup current CSS and use safe version...
copy "src\main\resources\css\application.css" "src\main\resources\css\application-complex-backup.css"
copy "src\main\resources\css\application-safe.css" "src\main\resources\css\application.css"

echo Step 3: Compiling with minimal CSS...
call mvn clean compile -q

if %ERRORLEVEL% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo ✅ Compilation successful with minimal CSS!
echo.

echo SAFE MODE CONFIGURATION:
echo ✅ Minimal CSS with no complex styling
echo ✅ No radius, gradient, or complex properties
echo ✅ Simple colors and basic styling only
echo ✅ Should eliminate ALL CSS-related crashes
echo.

echo Starting application in SAFE MODE...
echo.
echo EXPECTED BEHAVIOR:
echo ✅ Application should start without getting stuck
echo ✅ Login screen should appear (may look basic)
echo ✅ No CSS ClassCastException errors
echo ✅ Basic functionality should work
echo.
echo NOTE: UI will look basic but should be functional
echo We can restore complex styling once core issues are resolved
echo.

java -Dprism.order=sw ^
     -Dprism.allowhidpi=false ^
     -Djavafx.animation.pulse=60 ^
     -Djava.awt.headless=false ^
     -Dsqlite.purejava=false ^
     -Xms256m ^
     -Xmx1g ^
     -XX:+UseG1GC ^
     -XX:MaxGCPauseMillis=200 ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics ^
     -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\pdfbox\2.0.27\pdfbox-2.0.27.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\fontbox\2.0.27\fontbox-2.0.27.jar;%USERPROFILE%\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar" ^
     com.restaurant.RestaurantApp

echo.
echo Application closed.
echo.
echo SAFE MODE ANALYSIS:
echo.
if %ERRORLEVEL% equ 0 (
    echo ✅ Application started successfully in safe mode!
    echo This confirms the issue is with complex CSS styling.
) else (
    echo ❌ Application still failed with error code: %ERRORLEVEL%
    echo The issue may be deeper than CSS - checking other components needed.
)
echo.
echo NEXT STEPS:
echo.
if %ERRORLEVEL% equ 0 (
    echo ✅ SAFE MODE WORKED - The issue was complex CSS
    echo   - We can now gradually restore styling
    echo   - Core application functionality is working
    echo   - Can add back styling piece by piece
) else (
    echo ❌ SAFE MODE FAILED - Issue is not just CSS
    echo   - Need to check database initialization
    echo   - Need to check FXML loading
    echo   - Need to check notification system
    echo   - May need to disable more components
)
echo.
echo To restore complex styling later:
echo copy "src\main\resources\css\application-complex-backup.css" "src\main\resources\css\application.css"
echo.
pause
