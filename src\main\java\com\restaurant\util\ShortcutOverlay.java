package com.restaurant.util;

import javafx.animation.FadeTransition;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.Label;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.util.Duration;

/**
 * Overlay that shows keyboard shortcuts on screen
 */
public class ShortcutOverlay {
    
    private static VBox overlayPane;
    private static boolean isVisible = false;
    
    /**
     * Create the shortcut overlay pane
     */
    public static VBox createOverlay() {
        if (overlayPane != null) {
            return overlayPane;
        }
        
        overlayPane = new VBox(8);
        overlayPane.setAlignment(Pos.TOP_RIGHT);
        overlayPane.setPadding(new Insets(15));
        overlayPane.setStyle(
            "-fx-background-color: rgba(0, 0, 0, 0.85); " +
            "-fx-background-radius: 10; " +
            "-fx-border-color: #007bff; " +
            "-fx-border-width: 2; " +
            "-fx-border-radius: 10; " +
            "-fx-max-width: 300; " +
            "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 10, 0, 0, 3);"
        );
        
        // Title
        Label titleLabel = new Label("⚡ Quick Shortcuts");
        titleLabel.setFont(Font.font("System", FontWeight.BOLD, 14));
        titleLabel.setTextFill(Color.WHITE);
        titleLabel.setStyle("-fx-text-fill: #ffc107; -fx-padding: 0 0 8 0;");
        
        // Essential shortcuts
        VBox shortcutsContainer = new VBox(4);
        shortcutsContainer.getChildren().addAll(
            createShortcutLine("Ctrl+N", "New Order"),
            createShortcutLine("Ctrl+P", "Print KOT"),
            createShortcutLine("Ctrl+S", "Settle Bill"),
            createShortcutLine("F2", "Cash Payment"),
            createShortcutLine("F3", "Card Payment"),
            createShortcutLine("Ctrl+F", "Search"),
            createShortcutLine("1-9", "Quick Tables"),
            createShortcutLine("F1", "Help")
        );
        
        // Toggle hint
        Label toggleHint = new Label("Press F1 for full guide");
        toggleHint.setFont(Font.font("System", 10));
        toggleHint.setTextFill(Color.LIGHTGRAY);
        toggleHint.setStyle("-fx-padding: 8 0 0 0;");
        
        overlayPane.getChildren().addAll(titleLabel, shortcutsContainer, toggleHint);
        overlayPane.setVisible(false);
        overlayPane.setOpacity(0);
        
        return overlayPane;
    }
    
    /**
     * Create a shortcut line with key and description
     */
    private static HBox createShortcutLine(String key, String description) {
        HBox line = new HBox(8);
        line.setAlignment(Pos.CENTER_LEFT);
        
        Label keyLabel = new Label(key);
        keyLabel.setFont(Font.font("System", FontWeight.BOLD, 10));
        keyLabel.setTextFill(Color.WHITE);
        keyLabel.setStyle(
            "-fx-background-color: #495057; " +
            "-fx-padding: 2 6; " +
            "-fx-background-radius: 3; " +
            "-fx-min-width: 50; " +
            "-fx-alignment: center;"
        );
        
        Label descLabel = new Label(description);
        descLabel.setFont(Font.font("System", 10));
        descLabel.setTextFill(Color.LIGHTGRAY);
        
        line.getChildren().addAll(keyLabel, descLabel);
        return line;
    }
    
    /**
     * Show the overlay with fade-in animation
     */
    public static void show() {
        if (overlayPane == null) {
            createOverlay();
        }
        
        if (!isVisible) {
            overlayPane.setVisible(true);
            
            FadeTransition fadeIn = new FadeTransition(Duration.millis(300), overlayPane);
            fadeIn.setFromValue(0);
            fadeIn.setToValue(0.95);
            fadeIn.play();
            
            isVisible = true;
        }
    }
    
    /**
     * Hide the overlay with fade-out animation
     */
    public static void hide() {
        if (overlayPane != null && isVisible) {
            FadeTransition fadeOut = new FadeTransition(Duration.millis(300), overlayPane);
            fadeOut.setFromValue(0.95);
            fadeOut.setToValue(0);
            fadeOut.setOnFinished(e -> overlayPane.setVisible(false));
            fadeOut.play();
            
            isVisible = false;
        }
    }
    
    /**
     * Toggle overlay visibility
     */
    public static void toggle() {
        if (isVisible) {
            hide();
        } else {
            show();
        }
    }
    
    /**
     * Check if overlay is currently visible
     */
    public static boolean isVisible() {
        return isVisible;
    }
    
    /**
     * Update overlay content for specific context
     */
    public static void updateForContext(String context) {
        if (overlayPane == null) return;
        
        // Clear existing shortcuts
        VBox shortcutsContainer = (VBox) overlayPane.getChildren().get(1);
        shortcutsContainer.getChildren().clear();
        
        switch (context.toLowerCase()) {
            case "order":
                shortcutsContainer.getChildren().addAll(
                    createShortcutLine("Ctrl+N", "New Order"),
                    createShortcutLine("Ctrl+P", "Print KOT"),
                    createShortcutLine("Ctrl+D", "Apply Discount"),
                    createShortcutLine("Delete", "Delete Item"),
                    createShortcutLine("Ctrl+F", "Search Items")
                );
                break;
                
            case "payment":
                shortcutsContainer.getChildren().addAll(
                    createShortcutLine("F2", "Cash Payment"),
                    createShortcutLine("F3", "Card Payment"),
                    createShortcutLine("F4", "UPI Payment"),
                    createShortcutLine("Ctrl+S", "Settle Bill"),
                    createShortcutLine("Ctrl+D", "Apply Discount")
                );
                break;
                
            case "table":
                shortcutsContainer.getChildren().addAll(
                    createShortcutLine("1-9", "Select Table"),
                    createShortcutLine("Ctrl+T", "See Tables"),
                    createShortcutLine("Enter", "Open Menu"),
                    createShortcutLine("Escape", "Go Back")
                );
                break;
                
            case "menu":
                shortcutsContainer.getChildren().addAll(
                    createShortcutLine("Ctrl+M", "Menu Management"),
                    createShortcutLine("Ctrl+I", "Inventory"),
                    createShortcutLine("Ctrl+U", "User Management"),
                    createShortcutLine("Ctrl+A", "Admin Settings")
                );
                break;
                
            default:
                // Default shortcuts
                shortcutsContainer.getChildren().addAll(
                    createShortcutLine("Ctrl+N", "New Order"),
                    createShortcutLine("Ctrl+P", "Print KOT"),
                    createShortcutLine("F2", "Cash Payment"),
                    createShortcutLine("Ctrl+F", "Search"),
                    createShortcutLine("1-9", "Quick Tables"),
                    createShortcutLine("F1", "Help")
                );
        }
    }
    
    /**
     * Show temporary shortcut hint
     */
    public static void showHint(String shortcut, String action) {
        VBox hintPane = new VBox(5);
        hintPane.setAlignment(Pos.CENTER);
        hintPane.setPadding(new Insets(10));
        hintPane.setStyle(
            "-fx-background-color: rgba(40, 167, 69, 0.9); " +
            "-fx-background-radius: 8; " +
            "-fx-border-color: #28a745; " +
            "-fx-border-width: 1; " +
            "-fx-border-radius: 8;"
        );
        
        Label shortcutLabel = new Label(shortcut);
        shortcutLabel.setFont(Font.font("System", FontWeight.BOLD, 16));
        shortcutLabel.setTextFill(Color.WHITE);
        
        Label actionLabel = new Label(action);
        actionLabel.setFont(Font.font("System", 12));
        actionLabel.setTextFill(Color.WHITE);
        
        hintPane.getChildren().addAll(shortcutLabel, actionLabel);
        
        // Show hint with fade animation
        hintPane.setOpacity(0);
        FadeTransition fadeIn = new FadeTransition(Duration.millis(200), hintPane);
        fadeIn.setFromValue(0);
        fadeIn.setToValue(1);
        
        FadeTransition fadeOut = new FadeTransition(Duration.millis(200), hintPane);
        fadeOut.setFromValue(1);
        fadeOut.setToValue(0);
        fadeOut.setDelay(Duration.millis(1500));
        
        fadeIn.setOnFinished(e -> fadeOut.play());
        fadeIn.play();
    }
}
