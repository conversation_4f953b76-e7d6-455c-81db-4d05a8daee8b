package com.restaurant.util; 
 
public class SimpleBeepTester { 
    public static void main(String[] args) { 
        System.out.println("🎵 Testing Swiggy and Zomato beep patterns..."); 
        System.out.println(); 
 
        try { 
            testSwiggyPattern(); 
            Thread.sleep(2000); 
            testZomatoPattern(); 
        } catch (Exception e) { 
            e.printStackTrace(); 
        } 
    } 
 
    private static void testSwiggyPattern() throws InterruptedException { 
        System.out.println("🟠 SWIGGY ORDER SOUND - 4 Rapid + 2 Long Beeps"); 
        System.out.println("Pattern: BEEP-BEEP-BEEP-BEEP (pause) BEEP-BEEP (long)"); 
        System.out.println("Playing in 3 seconds..."); 
        Thread.sleep(3000); 
 
        // 4 rapid beeps 
        System.out.println("4 rapid beeps:"); 
        for (int i = 0; i < 4; i++) { 
            System.out.println("  Rapid BEEP " + (i + 1)); 
            java.awt.Toolkit.getDefaultToolkit().beep(); 
            Thread.sleep(100); 
        } 
 
        System.out.println("Pause..."); 
        Thread.sleep(400); 
 
        // 2 long beeps 
        System.out.println("2 long beeps:"); 
        for (int i = 0; i < 2; i++) { 
            System.out.println("  Long BEEP " + (i + 1)); 
            java.awt.Toolkit.getDefaultToolkit().beep(); 
            Thread.sleep(500); 
        } 
 
        System.out.println("✅ Swiggy pattern complete!"); 
        System.out.println(); 
    } 
 
    private static void testZomatoPattern() throws InterruptedException { 
        System.out.println("🔴 ZOMATO ORDER SOUND - 3 Sets of Double Beeps"); 
        System.out.println("Pattern: BEEP-BEEP (pause) BEEP-BEEP (pause) BEEP-BEEP"); 
        System.out.println("Playing in 3 seconds..."); 
        Thread.sleep(3000); 
 
        // 3 sets of double beeps 
        for (int set = 0; set < 3; set++) { 
            System.out.println("Set " + (set + 1) + ":"); 
 
            // Double beep 
            System.out.println("  BEEP 1"); 
            java.awt.Toolkit.getDefaultToolkit().beep(); 
            Thread.sleep(120); 
 
            System.out.println("  BEEP 2"); 
            java.awt.Toolkit.getDefaultToolkit().beep(); 
            Thread.sleep(300); 
        } 
 
        System.out.println("✅ Zomato pattern complete!"); 
        System.out.println(); 
        System.out.println("🎉 ALL SOUND PATTERNS TESTED!"); 
        System.out.println(); 
        System.out.println("SUMMARY:"); 
        System.out.println("🟠 Swiggy: 4 rapid beeps + 2 long beeps (urgent pattern)"); 
        System.out.println("🔴 Zomato: 3 sets of double beeps (distinctive pattern)"); 
        System.out.println(); 
        System.out.println("These patterns help staff instantly identify which platform has new orders!"); 
    } 
} 
