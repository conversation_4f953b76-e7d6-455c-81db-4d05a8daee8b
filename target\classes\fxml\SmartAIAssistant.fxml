<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.shape.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.SmartAIAssistantController">
   <!-- Smart AI Assistant Chat Interface -->
   <VBox fx:id="assistantContainer" styleClass="modern-ai-container" spacing="0">
      <children>
         <!-- Header Section -->
         <VBox styleClass="modern-header" spacing="8.0">
            <children>
               <HBox alignment="CENTER_LEFT" spacing="12.0">
                  <children>
                     <!-- AI Icon -->
                     <Label text="🤖" styleClass="modern-ai-icon" />

                     <!-- Title and Description -->
                     <VBox spacing="4.0" HBox.hgrow="ALWAYS">
                        <children>
                           <Text text="AI Assistant" styleClass="modern-title" />
                           <Text text="Chat with an AI to get insights about your restaurant." styleClass="modern-subtitle" />
                        </children>
                     </VBox>

                     <!-- Header Controls -->
                     <HBox spacing="8.0" alignment="CENTER_RIGHT">
                        <children>
                           <!-- Settings -->
                           <MenuButton fx:id="settingsMenu" styleClass="modern-control-button" text="⚙️">
                              <items>
                                 <CheckMenuItem fx:id="voiceEnabledItem" text="Voice Input Enabled" selected="true" />
                                 <CheckMenuItem fx:id="autoSuggestItem" text="Auto-suggest Commands" selected="true" />
                                 <CheckMenuItem fx:id="soundEffectsItem" text="Sound Effects" selected="true" />
                                 <SeparatorMenuItem />
                                 <MenuItem fx:id="exportChatItem" text="Export Chat History" onAction="#exportChatHistory" />
                                 <MenuItem fx:id="assistantHelpItem" text="Assistant Help" onAction="#showAssistantHelp" />
                              </items>
                           </MenuButton>

                           <!-- Clear Chat -->
                           <Button fx:id="clearChatButton" styleClass="modern-control-button" text="🗑️" onAction="#clearChat">
                              <tooltip>
                                 <Tooltip text="Clear Chat History" />
                              </tooltip>
                           </Button>
                        </children>
                     </HBox>
                  </children>
               </HBox>
            </children>
         </VBox>
         
         <!-- Chat Messages Area -->
         <ScrollPane fx:id="chatScrollPane" styleClass="modern-chat-scroll" fitToWidth="true" VBox.vgrow="ALWAYS">
            <content>
               <VBox fx:id="chatMessagesContainer" styleClass="modern-chat-container" spacing="16.0">
                  <children>
                     <!-- Welcome Message -->
                     <HBox alignment="CENTER_LEFT" spacing="12.0" styleClass="modern-message-container">
                        <children>
                           <!-- AI Avatar -->
                           <VBox alignment="CENTER" styleClass="modern-avatar-container">
                              <children>
                                 <Label text="🤖" styleClass="modern-message-avatar" />
                              </children>
                           </VBox>

                           <!-- Message Content -->
                           <VBox styleClass="modern-message-bubble" spacing="0" HBox.hgrow="ALWAYS">
                              <children>
                                 <Text styleClass="modern-message-text" text="Hello! I'm your AI Assistant. How can I help you with your restaurant data today?" />
                              </children>
                           </VBox>
                        </children>
                     </HBox>
                  </children>
               </VBox>
            </content>
         </ScrollPane>
         
         <!-- Input Area -->
         <VBox styleClass="modern-input-area" spacing="8.0">
            <children>
               <!-- Auto-suggestions Dropdown -->
               <VBox fx:id="autoSuggestionsContainer" styleClass="modern-suggestions-container" visible="false" spacing="4.0">
                  <children>
                     <ListView fx:id="autoSuggestionsList" styleClass="modern-suggestions-list" prefHeight="120.0" />
                  </children>
               </VBox>

               <!-- Main Input Row -->
               <HBox alignment="CENTER_LEFT" spacing="12.0" styleClass="modern-input-row">
                  <children>
                     <!-- Text Input Field -->
                     <TextField fx:id="messageInput" styleClass="modern-message-input"
                               promptText="e.g., What are our most popular items?"
                               onKeyPressed="#handleKeyPressed"
                               HBox.hgrow="ALWAYS" />

                     <!-- Voice Input Button -->
                     <StackPane styleClass="modern-voice-stack" visible="false">
                        <children>
                           <Circle fx:id="voicePulse" radius="20.0" styleClass="modern-voice-pulse" visible="false" />
                           <Button fx:id="voiceButton" styleClass="modern-voice-button" onAction="#handleVoiceInput">
                              <graphic>
                                 <Label fx:id="voiceIcon" text="🎤" styleClass="modern-voice-icon" />
                              </graphic>
                              <tooltip>
                                 <Tooltip fx:id="voiceTooltip" text="Click to speak" />
                              </tooltip>
                           </Button>
                        </children>
                     </StackPane>

                     <!-- Send Button -->
                     <Button fx:id="sendButton" styleClass="modern-send-button" onAction="#sendMessage" disable="true">
                        <graphic>
                           <Label text="➤" styleClass="modern-send-icon" />
                        </graphic>
                        <tooltip>
                           <Tooltip text="Send Message" />
                        </tooltip>
                     </Button>
                  </children>
               </HBox>

               <!-- Input Status Bar -->
               <HBox fx:id="inputStatusBar" alignment="CENTER_LEFT" spacing="8.0" styleClass="modern-status-bar" visible="false">
                  <children>
                     <Label fx:id="inputStatusIcon" text="🎙️" styleClass="modern-status-icon" />
                     <Text fx:id="inputStatusText" styleClass="modern-status-text" text="Listening..." />
                     <Region HBox.hgrow="ALWAYS" />
                     <Button fx:id="cancelInputButton" text="Cancel" styleClass="modern-cancel-button" onAction="#cancelVoiceInput" />
                  </children>
               </HBox>
            </children>
         </VBox>

      </children>
   </VBox>
</VBox>
