package com.restaurant.controller;

import com.restaurant.model.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.Node;
import javafx.scene.paint.Color;
import javafx.scene.shape.Circle;

import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Controller for Advanced Inventory Management with Purchase Orders and Internal Transfers
 */
public class AdvancedInventoryController implements Initializable {

    // FXML Components - Purchase Orders Section
    @FXML private VBox purchaseOrdersContainer;
    @FXML private DatePicker purchaseStartDate;
    @FXML private DatePicker purchaseEndDate;
    @FXML private Button addPurchaseOrderBtn;
    @FXML private Button refreshPurchaseBtn;
    
    // FXML Components - Internal Transfers Section
    @FXML private VBox internalTransfersContainer;
    @FXML private DatePicker transferStartDate;
    @FXML private DatePicker transferEndDate;
    @FXML private Button addInternalTransferBtn;
    @FXML private Button refreshTransferBtn;
    
    // FXML Components - Navigation
    @FXML private Button backButton;
    
    // Data
    private ObservableList<PurchaseOrder> purchaseOrders = FXCollections.observableArrayList();
    private ObservableList<InternalTransfer> internalTransfers = FXCollections.observableArrayList();
    
    // Sample locations
    private final String[] LOCATIONS = {"Vastrapur", "Makaraba", "Central Kitchen", "Warehouse"};
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        setupDatePickers();
        loadSampleData();
        displayPurchaseOrders();
        displayInternalTransfers();
        
        System.out.println("Advanced Inventory Controller initialized");
    }
    
    private void setupDatePickers() {
        // Set default date range (last 30 days)
        LocalDate today = LocalDate.now();
        LocalDate monthAgo = today.minusDays(30);
        
        purchaseStartDate.setValue(monthAgo);
        purchaseEndDate.setValue(today);
        transferStartDate.setValue(monthAgo);
        transferEndDate.setValue(today);
    }
    
    private void loadSampleData() {
        // Sample Purchase Orders
        purchaseOrders.addAll(Arrays.asList(
            new PurchaseOrder("P0324G", "Vastrapur", "Mango", "10 kg", "Saved"),
            new PurchaseOrder("P0456K", "Makaraba", "Carrot", "6 kg", "Processed"),
            new PurchaseOrder("P0567L", "Central Kitchen", "Onions", "15 kg", "Delivered"),
            new PurchaseOrder("P0678M", "Vastrapur", "Tomatoes", "8 kg", "Saved"),
            new PurchaseOrder("P0789N", "Warehouse", "Rice", "50 kg", "Processed")
        ));
        
        // Sample Internal Transfers
        internalTransfers.addAll(Arrays.asList(
            new InternalTransfer("P0324G", "Central Kitchen", "Vastrapur", "Mango", "10 kg", "Saved"),
            new InternalTransfer("P0456K", "Warehouse", "Makaraba", "Carrot", "6 kg", "Processed"),
            new InternalTransfer("P0567L", "Vastrapur", "Central Kitchen", "Spices", "2 kg", "Delivered"),
            new InternalTransfer("P0678M", "Central Kitchen", "Makaraba", "Oil", "5 L", "In Transit"),
            new InternalTransfer("P0789N", "Warehouse", "Vastrapur", "Flour", "20 kg", "Saved")
        ));
        
        // Set additional properties for sample data
        for (PurchaseOrder order : purchaseOrders) {
            order.setStartDate(LocalDate.now().minusDays((int)(Math.random() * 30)));
            order.setEndDate(LocalDate.now().plusDays((int)(Math.random() * 10)));
            order.setCreatedBy("System Admin");
        }
        
        for (InternalTransfer transfer : internalTransfers) {
            transfer.setStartDate(LocalDate.now().minusDays((int)(Math.random() * 30)));
            transfer.setEndDate(LocalDate.now().plusDays((int)(Math.random() * 10)));
            transfer.setCreatedBy("System Admin");
        }
    }
    
    private void displayPurchaseOrders() {
        purchaseOrdersContainer.getChildren().clear();
        
        // Add header
        HBox header = createPurchaseOrderHeader();
        purchaseOrdersContainer.getChildren().add(header);
        
        // Add purchase order rows
        for (PurchaseOrder order : purchaseOrders) {
            HBox orderRow = createPurchaseOrderRow(order);
            purchaseOrdersContainer.getChildren().add(orderRow);
        }
        
        // Add empty state if no orders
        if (purchaseOrders.isEmpty()) {
            Label emptyLabel = new Label("No purchase orders found");
            emptyLabel.getStyleClass().add("empty-state");
            purchaseOrdersContainer.getChildren().add(emptyLabel);
        }
    }
    
    private HBox createPurchaseOrderHeader() {
        HBox header = new HBox(15);
        header.setAlignment(Pos.CENTER_LEFT);
        header.getStyleClass().add("inventory-table-header");
        header.setPadding(new Insets(8, 15, 8, 15));

        Label toLabel = new Label("To");
        toLabel.setPrefWidth(100);
        toLabel.getStyleClass().add("table-header-label");

        Label reqLabel = new Label("Req. No.");
        reqLabel.setPrefWidth(80);
        reqLabel.getStyleClass().add("table-header-label");

        Label itemLabel = new Label("Item");
        itemLabel.setPrefWidth(100);
        itemLabel.getStyleClass().add("table-header-label");

        Label qtyLabel = new Label("Qty");
        qtyLabel.setPrefWidth(70);
        qtyLabel.getStyleClass().add("table-header-label");

        Label statusLabel = new Label("Status");
        statusLabel.setPrefWidth(80);
        statusLabel.getStyleClass().add("table-header-label");

        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);

        header.getChildren().addAll(toLabel, reqLabel, itemLabel, qtyLabel, statusLabel, spacer);
        return header;
    }
    
    private HBox createPurchaseOrderRow(PurchaseOrder order) {
        HBox row = new HBox(15);
        row.setAlignment(Pos.CENTER_LEFT);
        row.getStyleClass().add("inventory-table-row");
        row.setPadding(new Insets(8, 15, 8, 15));

        // To Location
        Label toLabel = new Label(order.getToLocation());
        toLabel.setPrefWidth(100);
        toLabel.getStyleClass().add("table-cell-label");

        // Request Number
        Label reqLabel = new Label(order.getRequestNumber());
        reqLabel.setPrefWidth(80);
        reqLabel.getStyleClass().add("table-cell-label");
        reqLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #007bff;");

        // Item
        Label itemLabel = new Label(order.getItem());
        itemLabel.setPrefWidth(100);
        itemLabel.getStyleClass().add("table-cell-label");

        // Quantity
        Label qtyLabel = new Label(order.getQuantity());
        qtyLabel.setPrefWidth(70);
        qtyLabel.getStyleClass().add("table-cell-label");

        // Status with colored background
        Label statusLabel = createStatusLabel(order.getStatus(), order.getStatusColor());
        statusLabel.setPrefWidth(80);

        // Action buttons
        HBox actionBox = new HBox(5);
        actionBox.setAlignment(Pos.CENTER_RIGHT);

        Button editBtn = new Button("Edit");
        editBtn.getStyleClass().add("edit-button");
        editBtn.setOnAction(e -> editPurchaseOrder(order));

        Button deleteBtn = new Button("Delete");
        deleteBtn.getStyleClass().add("delete-button");
        deleteBtn.setOnAction(e -> deletePurchaseOrder(order));

        actionBox.getChildren().addAll(editBtn, deleteBtn);

        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);

        row.getChildren().addAll(toLabel, reqLabel, itemLabel, qtyLabel, statusLabel, spacer, actionBox);

        // Add hover effect
        row.setOnMouseEntered(e -> row.getStyleClass().add("inventory-table-row-hover"));
        row.setOnMouseExited(e -> row.getStyleClass().remove("inventory-table-row-hover"));

        return row;
    }
    
    private void displayInternalTransfers() {
        internalTransfersContainer.getChildren().clear();
        
        // Add header
        HBox header = createInternalTransferHeader();
        internalTransfersContainer.getChildren().add(header);
        
        // Add transfer rows
        for (InternalTransfer transfer : internalTransfers) {
            HBox transferRow = createInternalTransferRow(transfer);
            internalTransfersContainer.getChildren().add(transferRow);
        }
        
        // Add empty state if no transfers
        if (internalTransfers.isEmpty()) {
            Label emptyLabel = new Label("No internal transfers found");
            emptyLabel.getStyleClass().add("empty-state");
            internalTransfersContainer.getChildren().add(emptyLabel);
        }
    }
    
    private HBox createInternalTransferHeader() {
        HBox header = new HBox(15);
        header.setAlignment(Pos.CENTER_LEFT);
        header.getStyleClass().add("inventory-table-header");
        header.setPadding(new Insets(8, 15, 8, 15));

        Label toLabel = new Label("To");
        toLabel.setPrefWidth(100);
        toLabel.getStyleClass().add("table-header-label");

        Label reqLabel = new Label("Req. No.");
        reqLabel.setPrefWidth(80);
        reqLabel.getStyleClass().add("table-header-label");

        Label itemLabel = new Label("Item");
        itemLabel.setPrefWidth(100);
        itemLabel.getStyleClass().add("table-header-label");

        Label qtyLabel = new Label("Qty");
        qtyLabel.setPrefWidth(70);
        qtyLabel.getStyleClass().add("table-header-label");

        Label statusLabel = new Label("Status");
        statusLabel.setPrefWidth(80);
        statusLabel.getStyleClass().add("table-header-label");

        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);

        header.getChildren().addAll(toLabel, reqLabel, itemLabel, qtyLabel, statusLabel, spacer);
        return header;
    }

    private HBox createInternalTransferRow(InternalTransfer transfer) {
        HBox row = new HBox(15);
        row.setAlignment(Pos.CENTER_LEFT);
        row.getStyleClass().add("inventory-table-row");
        row.setPadding(new Insets(8, 15, 8, 15));

        // To Location
        Label toLabel = new Label(transfer.getToLocation());
        toLabel.setPrefWidth(100);
        toLabel.getStyleClass().add("table-cell-label");

        // Request Number
        Label reqLabel = new Label(transfer.getRequestNumber());
        reqLabel.setPrefWidth(80);
        reqLabel.getStyleClass().add("table-cell-label");
        reqLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #007bff;");

        // Item
        Label itemLabel = new Label(transfer.getItem());
        itemLabel.setPrefWidth(100);
        itemLabel.getStyleClass().add("table-cell-label");

        // Quantity
        Label qtyLabel = new Label(transfer.getQuantity());
        qtyLabel.setPrefWidth(70);
        qtyLabel.getStyleClass().add("table-cell-label");

        // Status with colored background
        Label statusLabel = createStatusLabel(transfer.getStatus(), transfer.getStatusColor());
        statusLabel.setPrefWidth(80);

        // Action buttons
        HBox actionBox = new HBox(5);
        actionBox.setAlignment(Pos.CENTER_RIGHT);

        Button editBtn = new Button("Edit");
        editBtn.getStyleClass().add("edit-button");
        editBtn.setOnAction(e -> editInternalTransfer(transfer));

        Button deleteBtn = new Button("Delete");
        deleteBtn.getStyleClass().add("delete-button");
        deleteBtn.setOnAction(e -> deleteInternalTransfer(transfer));

        actionBox.getChildren().addAll(editBtn, deleteBtn);

        Region spacer = new Region();
        HBox.setHgrow(spacer, Priority.ALWAYS);

        row.getChildren().addAll(toLabel, reqLabel, itemLabel, qtyLabel, statusLabel, spacer, actionBox);

        // Add hover effect
        row.setOnMouseEntered(e -> row.getStyleClass().add("inventory-table-row-hover"));
        row.setOnMouseExited(e -> row.getStyleClass().remove("inventory-table-row-hover"));

        return row;
    }

    private Label createStatusLabel(String status, String color) {
        Label statusLabel = new Label(status);
        statusLabel.getStyleClass().add("status-label");
        statusLabel.setStyle(String.format(
            "-fx-background-color: %s; -fx-text-fill: white; " +
            "-fx-padding: 3px 6px; -fx-background-radius: 8px; " +
            "-fx-font-size: 10px; -fx-font-weight: bold;", color));
        statusLabel.setAlignment(Pos.CENTER);
        return statusLabel;
    }

    // Event Handlers
    @FXML
    private void addPurchaseOrder() {
        showAddPurchaseOrderDialog();
    }

    @FXML
    private void addInternalTransfer() {
        showAddInternalTransferDialog();
    }

    @FXML
    private void refreshPurchaseOrders() {
        // In a real application, this would reload from database
        displayPurchaseOrders();
        System.out.println("Purchase orders refreshed");
    }

    @FXML
    private void refreshInternalTransfers() {
        // In a real application, this would reload from database
        displayInternalTransfers();
        System.out.println("Internal transfers refreshed");
    }

    @FXML
    private void goBack() {
        try {
            // Simply close the modal window instead of navigating
            javafx.stage.Stage stage = (javafx.stage.Stage) backButton.getScene().getWindow();
            stage.close();

            System.out.println("Advanced Inventory Management window closed");

        } catch (Exception e) {
            System.err.println("Error closing window: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void editPurchaseOrder(PurchaseOrder order) {
        showEditPurchaseOrderDialog(order);
    }

    private void deletePurchaseOrder(PurchaseOrder order) {
        Alert confirmDialog = new Alert(Alert.AlertType.CONFIRMATION);
        confirmDialog.setTitle("Delete Purchase Order");
        confirmDialog.setHeaderText("Are you sure you want to delete this purchase order?");
        confirmDialog.setContentText("Request No: " + order.getRequestNumber() + "\nItem: " + order.getItem());

        Optional<ButtonType> result = confirmDialog.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            purchaseOrders.remove(order);
            displayPurchaseOrders();
            System.out.println("Purchase order deleted: " + order.getRequestNumber());
        }
    }

    private void editInternalTransfer(InternalTransfer transfer) {
        showEditInternalTransferDialog(transfer);
    }

    private void deleteInternalTransfer(InternalTransfer transfer) {
        Alert confirmDialog = new Alert(Alert.AlertType.CONFIRMATION);
        confirmDialog.setTitle("Delete Internal Transfer");
        confirmDialog.setHeaderText("Are you sure you want to delete this internal transfer?");
        confirmDialog.setContentText("Request No: " + transfer.getRequestNumber() + "\nItem: " + transfer.getItem());

        Optional<ButtonType> result = confirmDialog.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            internalTransfers.remove(transfer);
            displayInternalTransfers();
            System.out.println("Internal transfer deleted: " + transfer.getRequestNumber());
        }
    }

    private void showAddPurchaseOrderDialog() {
        Dialog<PurchaseOrder> dialog = new Dialog<>();
        dialog.setTitle("Add Purchase Order");
        dialog.setHeaderText("Create New Purchase Order");

        // Create form fields
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        ComboBox<String> locationCombo = new ComboBox<>();
        locationCombo.getItems().addAll(LOCATIONS);
        locationCombo.setValue(LOCATIONS[0]);

        TextField itemField = new TextField();
        itemField.setPromptText("Item name");

        TextField quantityField = new TextField();
        quantityField.setPromptText("Quantity (e.g., 10 kg)");

        ComboBox<String> statusCombo = new ComboBox<>();
        statusCombo.getItems().addAll("Saved", "Processed", "Delivered");
        statusCombo.setValue("Saved");

        grid.add(new Label("To Location:"), 0, 0);
        grid.add(locationCombo, 1, 0);
        grid.add(new Label("Item:"), 0, 1);
        grid.add(itemField, 1, 1);
        grid.add(new Label("Quantity:"), 0, 2);
        grid.add(quantityField, 1, 2);
        grid.add(new Label("Status:"), 0, 3);
        grid.add(statusCombo, 1, 3);

        dialog.getDialogPane().setContent(grid);

        ButtonType addButtonType = new ButtonType("Add", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(addButtonType, ButtonType.CANCEL);

        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == addButtonType) {
                String requestNumber = "P" + String.format("%05d", purchaseOrders.size() + 1) +
                                     (char)('A' + (int)(Math.random() * 26));
                return new PurchaseOrder(requestNumber, locationCombo.getValue(),
                                       itemField.getText(), quantityField.getText(),
                                       statusCombo.getValue());
            }
            return null;
        });

        Optional<PurchaseOrder> result = dialog.showAndWait();
        result.ifPresent(order -> {
            order.setStartDate(LocalDate.now());
            order.setEndDate(LocalDate.now().plusDays(7));
            order.setCreatedBy("Current User");
            purchaseOrders.add(order);
            displayPurchaseOrders();
            System.out.println("Added purchase order: " + order.getRequestNumber());
        });
    }

    private void showEditPurchaseOrderDialog(PurchaseOrder order) {
        Dialog<PurchaseOrder> dialog = new Dialog<>();
        dialog.setTitle("Edit Purchase Order");
        dialog.setHeaderText("Edit Purchase Order: " + order.getRequestNumber());

        // Create form fields
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        // Pre-populate fields with existing data
        ComboBox<String> locationCombo = new ComboBox<>();
        locationCombo.getItems().addAll(LOCATIONS);
        locationCombo.setValue(order.getToLocation());

        TextField itemField = new TextField(order.getItem());
        itemField.setPromptText("Item name");

        TextField quantityField = new TextField(order.getQuantity());
        quantityField.setPromptText("Quantity (e.g., 10 kg)");

        ComboBox<String> statusCombo = new ComboBox<>();
        statusCombo.getItems().addAll("Saved", "Processed", "Delivered", "Cancelled");
        statusCombo.setValue(order.getStatus());

        TextField notesField = new TextField(order.getNotes() != null ? order.getNotes() : "");
        notesField.setPromptText("Notes (optional)");

        TextField unitPriceField = new TextField(String.valueOf(order.getUnitPrice()));
        unitPriceField.setPromptText("Unit price");

        // Calculate total when unit price or quantity changes
        TextField totalAmountField = new TextField(String.valueOf(order.getTotalAmount()));
        totalAmountField.setPromptText("Total amount");
        totalAmountField.setEditable(false);

        // Auto-calculate total amount
        Runnable calculateTotal = () -> {
            try {
                double unitPrice = Double.parseDouble(unitPriceField.getText());
                String qtyText = quantityField.getText().replaceAll("[^0-9.]", ""); // Extract numbers
                if (!qtyText.isEmpty()) {
                    double quantity = Double.parseDouble(qtyText);
                    double total = unitPrice * quantity;
                    totalAmountField.setText(String.format("%.2f", total));
                }
            } catch (NumberFormatException e) {
                totalAmountField.setText("0.00");
            }
        };

        unitPriceField.textProperty().addListener((obs, oldVal, newVal) -> calculateTotal.run());
        quantityField.textProperty().addListener((obs, oldVal, newVal) -> calculateTotal.run());

        grid.add(new Label("To Location:"), 0, 0);
        grid.add(locationCombo, 1, 0);
        grid.add(new Label("Item:"), 0, 1);
        grid.add(itemField, 1, 1);
        grid.add(new Label("Quantity:"), 0, 2);
        grid.add(quantityField, 1, 2);
        grid.add(new Label("Status:"), 0, 3);
        grid.add(statusCombo, 1, 3);
        grid.add(new Label("Unit Price:"), 0, 4);
        grid.add(unitPriceField, 1, 4);
        grid.add(new Label("Total Amount:"), 0, 5);
        grid.add(totalAmountField, 1, 5);
        grid.add(new Label("Notes:"), 0, 6);
        grid.add(notesField, 1, 6);

        dialog.getDialogPane().setContent(grid);

        ButtonType saveButtonType = new ButtonType("Save Changes", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(saveButtonType, ButtonType.CANCEL);

        // Enable/disable save button based on required fields
        Node saveButton = dialog.getDialogPane().lookupButton(saveButtonType);
        saveButton.setDisable(itemField.getText().trim().isEmpty() || quantityField.getText().trim().isEmpty());

        itemField.textProperty().addListener((observable, oldValue, newValue) -> {
            saveButton.setDisable(newValue.trim().isEmpty() || quantityField.getText().trim().isEmpty());
        });

        quantityField.textProperty().addListener((observable, oldValue, newValue) -> {
            saveButton.setDisable(newValue.trim().isEmpty() || itemField.getText().trim().isEmpty());
        });

        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == saveButtonType) {
                // Update the existing order with new values
                order.setToLocation(locationCombo.getValue());
                order.setItem(itemField.getText().trim());
                order.setQuantity(quantityField.getText().trim());
                order.setStatus(statusCombo.getValue());
                order.setNotes(notesField.getText().trim());

                try {
                    order.setUnitPrice(Double.parseDouble(unitPriceField.getText()));
                    order.setTotalAmount(Double.parseDouble(totalAmountField.getText()));
                } catch (NumberFormatException e) {
                    order.setUnitPrice(0.0);
                    order.setTotalAmount(0.0);
                }

                order.setLastUpdated(java.time.LocalDateTime.now());
                return order;
            }
            return null;
        });

        Optional<PurchaseOrder> result = dialog.showAndWait();
        result.ifPresent(updatedOrder -> {
            // Update in database if using DAO
            // PurchaseOrderDAO.updatePurchaseOrder(updatedOrder);

            // Refresh the display
            displayPurchaseOrders();
            System.out.println("Updated purchase order: " + updatedOrder.getRequestNumber());

            // Show success message
            showAlert("Success", "Purchase order updated successfully!");
        });
    }

    private void showAddInternalTransferDialog() {
        Dialog<InternalTransfer> dialog = new Dialog<>();
        dialog.setTitle("Add Internal Transfer");
        dialog.setHeaderText("Create New Internal Transfer");

        // Create form fields
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        ComboBox<String> fromLocationCombo = new ComboBox<>();
        fromLocationCombo.getItems().addAll(LOCATIONS);
        fromLocationCombo.setValue(LOCATIONS[0]);

        ComboBox<String> toLocationCombo = new ComboBox<>();
        toLocationCombo.getItems().addAll(LOCATIONS);
        toLocationCombo.setValue(LOCATIONS[1]);

        TextField itemField = new TextField();
        itemField.setPromptText("Item name");

        TextField quantityField = new TextField();
        quantityField.setPromptText("Quantity (e.g., 10 kg)");

        ComboBox<String> statusCombo = new ComboBox<>();
        statusCombo.getItems().addAll("Saved", "Processed", "In Transit", "Delivered");
        statusCombo.setValue("Saved");

        grid.add(new Label("From Location:"), 0, 0);
        grid.add(fromLocationCombo, 1, 0);
        grid.add(new Label("To Location:"), 0, 1);
        grid.add(toLocationCombo, 1, 1);
        grid.add(new Label("Item:"), 0, 2);
        grid.add(itemField, 1, 2);
        grid.add(new Label("Quantity:"), 0, 3);
        grid.add(quantityField, 1, 3);
        grid.add(new Label("Status:"), 0, 4);
        grid.add(statusCombo, 1, 4);

        dialog.getDialogPane().setContent(grid);

        ButtonType addButtonType = new ButtonType("Add", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(addButtonType, ButtonType.CANCEL);

        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == addButtonType) {
                String requestNumber = "P" + String.format("%05d", internalTransfers.size() + 1) +
                                     (char)('A' + (int)(Math.random() * 26));
                return new InternalTransfer(requestNumber, fromLocationCombo.getValue(),
                                          toLocationCombo.getValue(), itemField.getText(),
                                          quantityField.getText(), statusCombo.getValue());
            }
            return null;
        });

        Optional<InternalTransfer> result = dialog.showAndWait();
        result.ifPresent(transfer -> {
            transfer.setStartDate(LocalDate.now());
            transfer.setEndDate(LocalDate.now().plusDays(3));
            transfer.setCreatedBy("Current User");
            internalTransfers.add(transfer);
            displayInternalTransfers();
            System.out.println("Added internal transfer: " + transfer.getRequestNumber());
        });
    }

    private void showEditInternalTransferDialog(InternalTransfer transfer) {
        Dialog<InternalTransfer> dialog = new Dialog<>();
        dialog.setTitle("Edit Internal Transfer");
        dialog.setHeaderText("Edit Internal Transfer: " + transfer.getRequestNumber());

        // Create form fields
        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        // Pre-populate fields with existing data
        ComboBox<String> fromLocationCombo = new ComboBox<>();
        fromLocationCombo.getItems().addAll(LOCATIONS);
        fromLocationCombo.setValue(transfer.getFromLocation());

        ComboBox<String> toLocationCombo = new ComboBox<>();
        toLocationCombo.getItems().addAll(LOCATIONS);
        toLocationCombo.setValue(transfer.getToLocation());

        TextField itemField = new TextField(transfer.getItem());
        itemField.setPromptText("Item name");

        TextField quantityField = new TextField(transfer.getQuantity());
        quantityField.setPromptText("Quantity (e.g., 10 kg)");

        ComboBox<String> statusCombo = new ComboBox<>();
        statusCombo.getItems().addAll("Saved", "Processed", "In Transit", "Delivered", "Cancelled");
        statusCombo.setValue(transfer.getStatus());

        TextField notesField = new TextField(transfer.getNotes() != null ? transfer.getNotes() : "");
        notesField.setPromptText("Notes (optional)");

        TextField reasonField = new TextField(transfer.getTransferReason() != null ? transfer.getTransferReason() : "");
        reasonField.setPromptText("Transfer reason (optional)");

        TextField approvedByField = new TextField(transfer.getApprovedBy() != null ? transfer.getApprovedBy() : "");
        approvedByField.setPromptText("Approved by (optional)");

        // Validation to prevent same from/to location
        Label validationLabel = new Label();
        validationLabel.setStyle("-fx-text-fill: #dc3545; -fx-font-size: 11px;");

        Runnable validateLocations = () -> {
            if (fromLocationCombo.getValue() != null && fromLocationCombo.getValue().equals(toLocationCombo.getValue())) {
                toLocationCombo.getStyleClass().add("error");
                fromLocationCombo.getStyleClass().add("error");
                validationLabel.setText("From and To locations cannot be the same");
            } else {
                toLocationCombo.getStyleClass().remove("error");
                fromLocationCombo.getStyleClass().remove("error");
                validationLabel.setText("");
            }
        };

        fromLocationCombo.valueProperty().addListener((obs, oldVal, newVal) -> validateLocations.run());
        toLocationCombo.valueProperty().addListener((obs, oldVal, newVal) -> validateLocations.run());

        grid.add(new Label("From Location:"), 0, 0);
        grid.add(fromLocationCombo, 1, 0);
        grid.add(new Label("To Location:"), 0, 1);
        grid.add(toLocationCombo, 1, 1);
        grid.add(new Label("Item:"), 0, 2);
        grid.add(itemField, 1, 2);
        grid.add(new Label("Quantity:"), 0, 3);
        grid.add(quantityField, 1, 3);
        grid.add(new Label("Status:"), 0, 4);
        grid.add(statusCombo, 1, 4);
        grid.add(new Label("Transfer Reason:"), 0, 5);
        grid.add(reasonField, 1, 5);
        grid.add(new Label("Approved By:"), 0, 6);
        grid.add(approvedByField, 1, 6);
        grid.add(new Label("Notes:"), 0, 7);
        grid.add(notesField, 1, 7);
        grid.add(validationLabel, 1, 8);

        dialog.getDialogPane().setContent(grid);

        ButtonType saveButtonType = new ButtonType("Save Changes", ButtonBar.ButtonData.OK_DONE);
        dialog.getDialogPane().getButtonTypes().addAll(saveButtonType, ButtonType.CANCEL);

        // Enable/disable save button based on required fields and validation
        Node saveButton = dialog.getDialogPane().lookupButton(saveButtonType);

        Runnable updateSaveButton = () -> {
            boolean isValid = !itemField.getText().trim().isEmpty() &&
                            !quantityField.getText().trim().isEmpty() &&
                            fromLocationCombo.getValue() != null &&
                            toLocationCombo.getValue() != null &&
                            !fromLocationCombo.getValue().equals(toLocationCombo.getValue());
            saveButton.setDisable(!isValid);
        };

        itemField.textProperty().addListener((observable, oldValue, newValue) -> updateSaveButton.run());
        quantityField.textProperty().addListener((observable, oldValue, newValue) -> updateSaveButton.run());
        fromLocationCombo.valueProperty().addListener((observable, oldValue, newValue) -> updateSaveButton.run());
        toLocationCombo.valueProperty().addListener((observable, oldValue, newValue) -> updateSaveButton.run());

        // Initial validation
        updateSaveButton.run();

        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == saveButtonType) {
                // Update the existing transfer with new values
                transfer.setFromLocation(fromLocationCombo.getValue());
                transfer.setToLocation(toLocationCombo.getValue());
                transfer.setItem(itemField.getText().trim());
                transfer.setQuantity(quantityField.getText().trim());
                transfer.setStatus(statusCombo.getValue());
                transfer.setNotes(notesField.getText().trim());
                transfer.setTransferReason(reasonField.getText().trim());
                transfer.setApprovedBy(approvedByField.getText().trim());
                transfer.setLastUpdated(java.time.LocalDateTime.now());

                return transfer;
            }
            return null;
        });

        Optional<InternalTransfer> result = dialog.showAndWait();
        result.ifPresent(updatedTransfer -> {
            // Update in database if using DAO
            // InternalTransferDAO.updateInternalTransfer(updatedTransfer);

            // Refresh the display
            displayInternalTransfers();
            System.out.println("Updated internal transfer: " + updatedTransfer.getRequestNumber());

            // Show success message
            showAlert("Success", "Internal transfer updated successfully!");
        });
    }

    private void showAlert(String title, String message) {
        Alert.AlertType alertType = title.toLowerCase().contains("success") ?
            Alert.AlertType.INFORMATION : Alert.AlertType.INFORMATION;

        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);

        // Add custom styling for success messages
        if (title.toLowerCase().contains("success")) {
            alert.getDialogPane().getStylesheets().add(getClass().getResource("/css/application.css").toExternalForm());
            alert.getDialogPane().getStyleClass().add("success-alert");
        }

        alert.showAndWait();
    }
}
