package com.restaurant.util;

import javafx.application.Platform;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Alert;
import javafx.stage.Stage;
import java.util.*;

/**
 * Universal Navigation Manager - Handles all back navigation scenarios
 * Features:
 * - Single ESC: Normal back navigation
 * - Double ESC: Quick return to dashboard
 * - All back buttons work reliably
 * - Production-ready error handling
 */
public class UniversalNavigationManager {
    
    private static UniversalNavigationManager instance;
    
    // Navigation history stack
    private Stack<NavigationState> navigationHistory;
    
    // ESC key handling
    private long lastEscapeTime = 0;
    private static final long DOUBLE_ESC_THRESHOLD = 800; // 800ms for double ESC (more user-friendly)

    // Global ESC key handler registration
    private boolean globalEscHandlerRegistered = false;

    // Special handling for Ctrl+S → ESC scenario
    private boolean ctrlSPressed = false;
    private long ctrlSPressTime = 0;
    private static final long CTRL_S_ESC_TIMEOUT = 5000; // 5 seconds timeout
    
    // Current controller tracking
    private String currentController;
    
    private UniversalNavigationManager() {
        navigationHistory = new Stack<>();
    }
    
    public static UniversalNavigationManager getInstance() {
        if (instance == null) {
            instance = new UniversalNavigationManager();
        }
        return instance;
    }
    
    /**
     * Navigation state to track where we came from
     */
    public static class NavigationState {
        private String fxmlPath;
        private String controllerName;
        private Map<String, Object> data;
        
        public NavigationState(String fxmlPath, String controllerName) {
            this.fxmlPath = fxmlPath;
            this.controllerName = controllerName;
            this.data = new HashMap<>();
        }
        
        public NavigationState(String fxmlPath, String controllerName, Map<String, Object> data) {
            this.fxmlPath = fxmlPath;
            this.controllerName = controllerName;
            this.data = data != null ? data : new HashMap<>();
        }
        
        // Getters
        public String getFxmlPath() { return fxmlPath; }
        public String getControllerName() { return controllerName; }
        public Map<String, Object> getData() { return data; }
    }
    
    /**
     * Navigate to a new view and add current state to history
     */
    public void navigateTo(String fxmlPath, String targetController, Map<String, Object> data) {
        try {
            // Add current state to history if we have one
            if (currentController != null) {
                String currentFxml = getCurrentFxmlPath();
                if (currentFxml != null) {
                    navigationHistory.push(new NavigationState(currentFxml, currentController, new HashMap<>()));
                }
            }
            
            // Load the new view
            performNavigation(fxmlPath, targetController, data);
            currentController = targetController;
            
            System.out.println("UniversalNavigationManager: Navigated to " + targetController + 
                             " (History size: " + navigationHistory.size() + ")");
            
        } catch (Exception e) {
            System.err.println("UniversalNavigationManager: Navigation failed: " + e.getMessage());
            e.printStackTrace();
            showAlert("Navigation Error", "Failed to navigate: " + e.getMessage());
        }
    }
    
    /**
     * Navigate to a new view without adding to history (for dashboard)
     */
    public void navigateToWithoutHistory(String fxmlPath, String targetController) {
        try {
            performNavigation(fxmlPath, targetController, null);
            currentController = targetController;
            
            // Clear history when going to dashboard
            if ("DashboardController".equals(targetController)) {
                navigationHistory.clear();
                System.out.println("UniversalNavigationManager: Navigated to Dashboard - History cleared");
            }
            
        } catch (Exception e) {
            System.err.println("UniversalNavigationManager: Navigation failed: " + e.getMessage());
            e.printStackTrace();
            showAlert("Navigation Error", "Failed to navigate: " + e.getMessage());
        }
    }
    
    /**
     * Handle back navigation (single ESC or back button)
     */
    public void goBack() {
        try {
            if (navigationHistory.isEmpty()) {
                // No history, go to dashboard
                goToDashboard();
                return;
            }
            
            NavigationState previousState = navigationHistory.pop();
            performNavigation(previousState.getFxmlPath(), previousState.getControllerName(), 
                            previousState.getData());
            currentController = previousState.getControllerName();
            
            System.out.println("UniversalNavigationManager: Went back to " + previousState.getControllerName() + 
                             " (History size: " + navigationHistory.size() + ")");
            
        } catch (Exception e) {
            System.err.println("UniversalNavigationManager: Back navigation failed: " + e.getMessage());
            e.printStackTrace();
            // Fallback to dashboard
            goToDashboard();
        }
    }
    
    /**
     * Handle ESC key press with double-ESC detection
     */
    public void handleEscapeKey() {
        long currentTime = System.currentTimeMillis();
        
        if (currentTime - lastEscapeTime < DOUBLE_ESC_THRESHOLD) {
            // Double ESC - go to dashboard
            System.out.println("UniversalNavigationManager: Double ESC detected - Going to Dashboard");
            goToDashboard();
        } else {
            // Single ESC - normal back
            System.out.println("UniversalNavigationManager: Single ESC detected - Going back");
            goBack();
        }
        
        lastEscapeTime = currentTime;
    }

    /**
     * Register global ESC key handler for any scene
     * This ensures ESC works everywhere in the application
     */
    public void registerGlobalEscHandler(Scene scene) {
        if (scene == null) {
            System.err.println("UniversalNavigationManager: Cannot register ESC handler - scene is null");
            return;
        }

        // Store existing key handler if any
        var existingHandler = scene.getOnKeyPressed();

        // Create new handler that combines existing functionality with ESC handling
        scene.setOnKeyPressed(event -> {
            // Handle Ctrl+S detection for special ESC behavior
            if (event.getCode() == javafx.scene.input.KeyCode.S && event.isControlDown()) {
                System.out.println("UniversalNavigationManager: Ctrl+S detected globally");
                ctrlSPressed = true;
                ctrlSPressTime = System.currentTimeMillis();
            }

            // Handle ESC key globally
            if (event.getCode() == javafx.scene.input.KeyCode.ESCAPE) {
                System.out.println("UniversalNavigationManager: Global ESC key detected");

                // Check if this ESC is after a recent Ctrl+S
                long currentTime = System.currentTimeMillis();
                if (ctrlSPressed && (currentTime - ctrlSPressTime) < CTRL_S_ESC_TIMEOUT) {
                    System.out.println("UniversalNavigationManager: ESC after Ctrl+S detected - going back immediately");
                    ctrlSPressed = false; // Reset flag
                    goBack(); // Direct back navigation
                    event.consume();
                    return;
                }

                // Normal ESC handling
                handleEscapeKey();
                event.consume();
                return;
            }

            // Reset Ctrl+S flag on any other key
            if (event.getCode() != javafx.scene.input.KeyCode.S && event.getCode() != javafx.scene.input.KeyCode.CONTROL) {
                ctrlSPressed = false;
            }

            // Call existing handler if it exists
            if (existingHandler != null) {
                existingHandler.handle(event);
            }
        });

        globalEscHandlerRegistered = true;
        System.out.println("UniversalNavigationManager: Global ESC handler registered for scene");
    }

    /**
     * Register global ESC handler for any stage
     */
    public void registerGlobalEscHandler(Stage stage) {
        if (stage == null || stage.getScene() == null) {
            System.err.println("UniversalNavigationManager: Cannot register ESC handler - stage or scene is null");
            return;
        }

        registerGlobalEscHandler(stage.getScene());
    }

    /**
     * Auto-register ESC handler when navigating
     */
    private void autoRegisterEscHandler() {
        try {
            Stage stage = findStage();
            if (stage != null && stage.getScene() != null) {
                registerGlobalEscHandler(stage.getScene());
            }
        } catch (Exception e) {
            System.err.println("UniversalNavigationManager: Failed to auto-register ESC handler: " + e.getMessage());
        }
    }

    /**
     * Initialize global ESC key handling for the entire application
     * Call this once from the main application startup
     */
    public void initializeGlobalEscHandling() {
        try {
            // Register ESC handler for current stage if available
            autoRegisterEscHandler();

            // Set up a global event filter for Ctrl+S → ESC scenario
            setupGlobalEventFilter();

            // Set up a listener for future stage changes
            Platform.runLater(() -> {
                try {
                    Stage stage = findStage();
                    if (stage != null) {
                        // Monitor scene changes
                        stage.sceneProperty().addListener((obs, oldScene, newScene) -> {
                            if (newScene != null) {
                                registerGlobalEscHandler(newScene);
                                setupGlobalEventFilter(); // Ensure filter is applied to new scenes
                            }
                        });

                        // Register for current scene
                        if (stage.getScene() != null) {
                            registerGlobalEscHandler(stage.getScene());
                        }
                    }
                } catch (Exception e) {
                    System.err.println("UniversalNavigationManager: Error setting up scene listener: " + e.getMessage());
                }
            });

            System.out.println("UniversalNavigationManager: Global ESC handling initialized");

        } catch (Exception e) {
            System.err.println("UniversalNavigationManager: Failed to initialize global ESC handling: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Set up global event filter to catch Ctrl+S → ESC at application level
     */
    private void setupGlobalEventFilter() {
        try {
            Stage stage = findStage();
            if (stage != null && stage.getScene() != null) {
                Scene scene = stage.getScene();

                // Add event filter to catch events before they reach handlers
                scene.addEventFilter(javafx.scene.input.KeyEvent.KEY_PRESSED, event -> {
                    // Track Ctrl+S globally
                    if (event.getCode() == javafx.scene.input.KeyCode.S && event.isControlDown()) {
                        System.out.println("UniversalNavigationManager: GLOBAL FILTER - Ctrl+S detected");
                        ctrlSPressed = true;
                        ctrlSPressTime = System.currentTimeMillis();
                    }

                    // Handle ESC after Ctrl+S globally
                    if (event.getCode() == javafx.scene.input.KeyCode.ESCAPE) {
                        long currentTime = System.currentTimeMillis();
                        if (ctrlSPressed && (currentTime - ctrlSPressTime) < CTRL_S_ESC_TIMEOUT) {
                            System.out.println("UniversalNavigationManager: GLOBAL FILTER - ESC after Ctrl+S, going back immediately");
                            ctrlSPressed = false; // Reset flag

                            Platform.runLater(() -> {
                                try {
                                    goBack();
                                } catch (Exception e) {
                                    System.err.println("Error in global back navigation: " + e.getMessage());
                                    goToDashboard(); // Fallback
                                }
                            });

                            event.consume(); // Prevent further processing
                        }
                    }
                });

                System.out.println("UniversalNavigationManager: Global event filter set up");
            }
        } catch (Exception e) {
            System.err.println("UniversalNavigationManager: Failed to set up global event filter: " + e.getMessage());
        }
    }
    
    /**
     * Go directly to dashboard
     */
    public void goToDashboard() {
        try {
            System.out.println("UniversalNavigationManager: Navigating to Dashboard");
            navigateToWithoutHistory("/fxml/Dashboard.fxml", "DashboardController");
        } catch (Exception e) {
            System.err.println("UniversalNavigationManager: Failed to navigate to dashboard: " + e.getMessage());
            e.printStackTrace();
            showAlert("Navigation Error", "Failed to return to dashboard: " + e.getMessage());
        }
    }
    
    /**
     * Perform the actual navigation using robust stage finding
     */
    private void performNavigation(String fxmlPath, String targetController, Map<String, Object> data) throws Exception {
        // Load FXML
        FXMLLoader loader = new FXMLLoader(getClass().getResource(fxmlPath));
        Parent view = loader.load();
        
        // Get stage using multiple methods
        Stage stage = findStage();
        
        if (stage != null) {
            // Clean up current controller's event handlers
            cleanupCurrentController();
            
            // Set new scene root
            stage.getScene().setRoot(view);
            
            // Initialize controller if needed
            Object controller = loader.getController();
            if (controller != null) {
                initializeController(controller, data);
            }

            // Auto-register global ESC handler for the new scene
            autoRegisterEscHandler();

        } else {
            throw new Exception("Could not find valid stage for navigation");
        }
    }
    
    /**
     * Find a valid stage using multiple robust methods
     */
    private Stage findStage() {
        // Method 1: Get from SceneEventHandlerManager
        Stage stage = SceneEventHandlerManager.getInstance().getPrimaryStage();
        if (stage != null) {
            System.out.println("UniversalNavigationManager: Found stage via SceneEventHandlerManager");
            return stage;
        }
        
        // Method 2: Get from JavaFX Window list
        for (javafx.stage.Window window : javafx.stage.Window.getWindows()) {
            if (window instanceof Stage && window.isShowing()) {
                System.out.println("UniversalNavigationManager: Found stage via Window list");
                return (Stage) window;
            }
        }
        
        System.err.println("UniversalNavigationManager: No valid stage found");
        return null;
    }
    
    /**
     * Clean up current controller's event handlers
     */
    private void cleanupCurrentController() {
        if (currentController != null) {
            SceneEventHandlerManager.getInstance().cleanupNonDashboardHandlers(findStage().getScene());
        }
    }
    
    /**
     * Initialize controller with data if supported
     */
    private void initializeController(Object controller, Map<String, Object> data) {
        // Add controller-specific initialization logic here if needed
        System.out.println("UniversalNavigationManager: Controller initialized: " + controller.getClass().getSimpleName());
    }
    
    /**
     * Get current FXML path based on controller
     */
    private String getCurrentFxmlPath() {
        if (currentController == null) return null;
        
        switch (currentController) {
            case "DashboardController": return "/fxml/Dashboard.fxml";
            case "BillingKOTController": return "/fxml/BillingKOT.fxml";
            case "MenuSelectionController": return "/fxml/MenuSelection.fxml";
            case "TableManagementController": return "/fxml/TableManagement.fxml";
            case "MenuManagementController": return "/fxml/MenuManagement.fxml";
            case "UserManagementController": return "/fxml/UserManagement.fxml";
            case "ReportsController": return "/fxml/Reports.fxml";
            case "SettingsController": return "/fxml/Settings.fxml";
            default: return null;
        }
    }
    
    /**
     * Show error alert
     */
    private void showAlert(String title, String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }
    
    /**
     * Set current controller (for tracking)
     */
    public void setCurrentController(String controllerName) {
        this.currentController = controllerName;
    }
    
    /**
     * Get current controller
     */
    public String getCurrentController() {
        return currentController;
    }
    
    /**
     * Clear navigation history
     */
    public void clearHistory() {
        navigationHistory.clear();
        System.out.println("UniversalNavigationManager: Navigation history cleared");
    }
    
    /**
     * Get navigation history size
     */
    public int getHistorySize() {
        return navigationHistory.size();
    }
}
