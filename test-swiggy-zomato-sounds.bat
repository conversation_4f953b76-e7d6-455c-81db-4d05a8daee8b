@echo off
echo 🎵 TESTING SWIGGY & ZOMATO NOTIFICATION SOUNDS 🎵
echo.

echo This will test the MP3 audio notifications for <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> orders
echo without needing to login to the application.
echo.

echo 🔊 AUDIO FILES CHECK:
echo.

if exist "sounds\swiggy-notification.mp3" (
    echo ✅ Swiggy audio file found: sounds\swiggy-notification.mp3
    set SWIGGY_FOUND=1
) else (
    echo ⚠️ Swiggy audio file not found: sounds\swiggy-notification.mp3
    set SWIGGY_FOUND=0
)

if exist "sounds\zomato-notification.mp3" (
    echo ✅ Zomato audio file found: sounds\zomato-notification.mp3
    set ZOMATO_FOUND=1
) else (
    echo ⚠️ Zomato audio file not found: sounds\zomato-notification.mp3
    set ZOMATO_FOUND=0
)

if exist "sounds\mixkit-urgent-simple-tone-loop-2976.mp3" (
    echo ✅ Default audio file found: sounds\mixkit-urgent-simple-tone-loop-2976.mp3
    set DEFAULT_FOUND=1
) else (
    echo ❌ Default audio file not found: sounds\mixkit-urgent-simple-tone-loop-2976.mp3
    set DEFAULT_FOUND=0
)

echo.
echo 🎯 TESTING PLAN:
echo.

if %SWIGGY_FOUND%==1 (
    echo 🟠 SWIGGY: Will play custom swiggy-notification.mp3
) else (
    if %DEFAULT_FOUND%==1 (
        echo 🟠 SWIGGY: Will play default mixkit-urgent-simple-tone-loop-2976.mp3
    ) else (
        echo 🟠 SWIGGY: Will use system beep fallback
    )
)

if %ZOMATO_FOUND%==1 (
    echo 🔴 ZOMATO: Will play custom zomato-notification.mp3
) else (
    if %DEFAULT_FOUND%==1 (
        echo 🔴 ZOMATO: Will play default mixkit-urgent-simple-tone-loop-2976.mp3
    ) else (
        echo 🔴 ZOMATO: Will use system beep fallback
    )
)

echo.
echo 🔧 CREATING SOUND TESTER...

echo package com.restaurant.util; > src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo import javafx.application.Application; >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo import javafx.scene.media.Media; >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo import javafx.scene.media.MediaPlayer; >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo import javafx.stage.Stage; >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo import java.io.File; >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo public class DirectSoundTester extends Application { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo     @Override >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo     public void start(Stage primaryStage) { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         System.out.println("🎵 Starting Swiggy & Zomato Sound Test..."); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         System.out.println(); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         testSwiggySound(); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo     } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo     private void testSwiggySound() { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         System.out.println("🟠 TESTING SWIGGY NOTIFICATION SOUND..."); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         try { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             String audioFile = findSwiggyAudio(); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             if (audioFile != null) { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                 playAudio(audioFile, "Swiggy"); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             } else { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                 playSystemBeepPattern("swiggy"); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             // Wait 5 seconds then test Zomato >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             new Thread(() -> { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                 try { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                     Thread.sleep(5000); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                     testZomatoSound(); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                 } catch (Exception e) { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                     e.printStackTrace(); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                 } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             }).start(); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             System.err.println("Error testing Swiggy sound: " + e.getMessage()); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             playSystemBeepPattern("swiggy"); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo     } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo     private void testZomatoSound() { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         System.out.println("🔴 TESTING ZOMATO NOTIFICATION SOUND..."); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         try { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             String audioFile = findZomatoAudio(); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             if (audioFile != null) { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                 playAudio(audioFile, "Zomato"); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             } else { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                 playSystemBeepPattern("zomato"); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             // Wait 5 seconds then exit >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             new Thread(() -> { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                 try { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                     Thread.sleep(5000); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                     System.out.println("🎉 Sound testing complete!"); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                     System.exit(0); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                 } catch (Exception e) { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                     e.printStackTrace(); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                 } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             }).start(); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             System.err.println("Error testing Zomato sound: " + e.getMessage()); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             playSystemBeepPattern("zomato"); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo     } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo     private String findSwiggyAudio() { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         File swiggy = new File("sounds/swiggy-notification.mp3"); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         if (swiggy.exists()) return "sounds/swiggy-notification.mp3"; >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         File defaultFile = new File("sounds/mixkit-urgent-simple-tone-loop-2976.mp3"); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         if (defaultFile.exists()) return "sounds/mixkit-urgent-simple-tone-loop-2976.mp3"; >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         return null; >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo     } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo     private String findZomatoAudio() { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         File zomato = new File("sounds/zomato-notification.mp3"); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         if (zomato.exists()) return "sounds/zomato-notification.mp3"; >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         File defaultFile = new File("sounds/mixkit-urgent-simple-tone-loop-2976.mp3"); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         if (defaultFile.exists()) return "sounds/mixkit-urgent-simple-tone-loop-2976.mp3"; >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         return null; >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo     } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo     private void playAudio(String fileName, String platform) { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         try { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             File audioFile = new File(fileName); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             Media media = new Media(audioFile.toURI().toString()); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             MediaPlayer player = new MediaPlayer(media); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             player.setVolume(1.0); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             player.setOnReady(() -> { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                 System.out.println("🎵 Playing " + platform + " audio: " + fileName); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                 player.play(); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             }); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             player.setOnError(() -> { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                 System.err.println("❌ Audio error for " + platform + ": " + player.getError()); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                 playSystemBeepPattern(platform.toLowerCase()); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             }); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             System.err.println("❌ Error playing " + platform + " audio: " + e.getMessage()); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             playSystemBeepPattern(platform.toLowerCase()); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo     } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo     private void playSystemBeepPattern(String platform) { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         new Thread(() -> { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             try { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                 if ("swiggy".equals(platform)) { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                     System.out.println("🟠 Playing Swiggy system beep pattern (4 rapid + 2 long)"); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                     for (int i = 0; i ^< 4; i++) { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                         java.awt.Toolkit.getDefaultToolkit().beep(); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                         Thread.sleep(80); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                     } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                     Thread.sleep(300); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                     for (int i = 0; i ^< 2; i++) { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                         java.awt.Toolkit.getDefaultToolkit().beep(); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                         Thread.sleep(400); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                     } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                 } else if ("zomato".equals(platform)) { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                     System.out.println("🔴 Playing Zomato system beep pattern (3 sets of double beeps)"); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                     for (int set = 0; set ^< 3; set++) { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                         java.awt.Toolkit.getDefaultToolkit().beep(); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                         Thread.sleep(100); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                         java.awt.Toolkit.getDefaultToolkit().beep(); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                         Thread.sleep(250); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                     } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                 } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             } catch (Exception e) { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo                 e.printStackTrace(); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo             } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         }).start(); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo     } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo. >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo     public static void main(String[] args) { >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo         launch(args); >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo     } >> src\main\java\com\restaurant\util\DirectSoundTester.java
echo } >> src\main\java\com\restaurant\util\DirectSoundTester.java

echo ✅ DirectSoundTester created
echo.

echo 🔧 COMPILING SOUND TESTER...
mvn compile -q

if %ERRORLEVEL% neq 0 (
    echo ❌ Compilation failed
    pause
    exit /b 1
)

echo ✅ Compilation successful
echo.

echo 🎵 STARTING SWIGGY & ZOMATO SOUND TEST...
echo.
echo You will hear:
echo 1. 🟠 Swiggy notification sound (5 seconds)
echo 2. 🔴 Zomato notification sound (5 seconds)
echo 3. Test complete
echo.

java -Dprism.order=sw ^
     -Dprism.allowhidpi=false ^
     -Djavafx.animation.pulse=60 ^
     -Djava.awt.headless=false ^
     -Dsqlite.purejava=false ^
     -Xms512m ^
     -Xmx1g ^
     -XX:+UseG1GC ^
     -XX:MaxGCPauseMillis=200 ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics,javafx.media ^
     -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\pdfbox\2.0.27\pdfbox-2.0.27.jar;%USERPROFILE%\.m2\repository\org\apache\pdfbox\fontbox\2.0.27\fontbox-2.0.27.jar;%USERPROFILE%\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2.jar" ^
     com.restaurant.util.DirectSoundTester

echo.
echo 🎉 SWIGGY & ZOMATO SOUND TEST COMPLETE!
echo.
echo WHAT YOU SHOULD HAVE HEARD:
echo 🟠 Swiggy: Either custom MP3 or 4 rapid + 2 long beeps
echo 🔴 Zomato: Either custom MP3 or 3 sets of double beeps
echo.
echo AUDIO FILE STATUS:
if %SWIGGY_FOUND%==1 (echo ✅ Swiggy custom audio available) else (echo ⚠️ Swiggy using fallback)
if %ZOMATO_FOUND%==1 (echo ✅ Zomato custom audio available) else (echo ⚠️ Zomato using fallback)
if %DEFAULT_FOUND%==1 (echo ✅ Default audio available) else (echo ❌ No default audio)
echo.

pause
