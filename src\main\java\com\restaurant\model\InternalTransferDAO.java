package com.restaurant.model;

import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Data Access Object for Internal Transfers
 */
public class InternalTransferDAO {

    /**
     * Get all internal transfers
     */
    public static List<InternalTransfer> getAllInternalTransfers() {
        List<InternalTransfer> transfers = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(
                     "SELECT * FROM internal_transfers ORDER BY created_at DESC")) {
            
            while (rs.next()) {
                InternalTransfer transfer = createInternalTransferFromResultSet(rs);
                transfers.add(transfer);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return transfers;
    }

    /**
     * Get internal transfer by ID
     */
    public static InternalTransfer getInternalTransferById(int id) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT * FROM internal_transfers WHERE id = ?")) {
            
            ps.setInt(1, id);
            ResultSet rs = ps.executeQuery();
            
            if (rs.next()) {
                return createInternalTransferFromResultSet(rs);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * Add new internal transfer
     */
    public static boolean addInternalTransfer(InternalTransfer transfer) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "INSERT INTO internal_transfers (request_number, from_location, to_location, item, " +
                     "quantity, status, start_date, end_date, created_by, approved_by, notes, " +
                     "transfer_reason, transfer_date, delivery_date, created_at, last_updated) " +
                     "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", 
                     Statement.RETURN_GENERATED_KEYS)) {
            
            ps.setString(1, transfer.getRequestNumber());
            ps.setString(2, transfer.getFromLocation());
            ps.setString(3, transfer.getToLocation());
            ps.setString(4, transfer.getItem());
            ps.setString(5, transfer.getQuantity());
            ps.setString(6, transfer.getStatus());
            ps.setDate(7, transfer.getStartDate() != null ? Date.valueOf(transfer.getStartDate()) : null);
            ps.setDate(8, transfer.getEndDate() != null ? Date.valueOf(transfer.getEndDate()) : null);
            ps.setString(9, transfer.getCreatedBy());
            ps.setString(10, transfer.getApprovedBy());
            ps.setString(11, transfer.getNotes());
            ps.setString(12, transfer.getTransferReason());
            ps.setTimestamp(13, transfer.getTransferDate() != null ? Timestamp.valueOf(transfer.getTransferDate()) : null);
            ps.setTimestamp(14, transfer.getDeliveryDate() != null ? Timestamp.valueOf(transfer.getDeliveryDate()) : null);
            ps.setTimestamp(15, Timestamp.valueOf(transfer.getCreatedAt()));
            ps.setTimestamp(16, Timestamp.valueOf(transfer.getLastUpdated()));
            
            int result = ps.executeUpdate();
            
            if (result > 0) {
                ResultSet rs = ps.getGeneratedKeys();
                if (rs.next()) {
                    transfer.setId(rs.getInt(1));
                }
                return true;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * Update internal transfer
     */
    public static boolean updateInternalTransfer(InternalTransfer transfer) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "UPDATE internal_transfers SET request_number = ?, from_location = ?, to_location = ?, " +
                     "item = ?, quantity = ?, status = ?, start_date = ?, end_date = ?, approved_by = ?, " +
                     "notes = ?, transfer_reason = ?, transfer_date = ?, delivery_date = ?, last_updated = ? " +
                     "WHERE id = ?")) {
            
            ps.setString(1, transfer.getRequestNumber());
            ps.setString(2, transfer.getFromLocation());
            ps.setString(3, transfer.getToLocation());
            ps.setString(4, transfer.getItem());
            ps.setString(5, transfer.getQuantity());
            ps.setString(6, transfer.getStatus());
            ps.setDate(7, transfer.getStartDate() != null ? Date.valueOf(transfer.getStartDate()) : null);
            ps.setDate(8, transfer.getEndDate() != null ? Date.valueOf(transfer.getEndDate()) : null);
            ps.setString(9, transfer.getApprovedBy());
            ps.setString(10, transfer.getNotes());
            ps.setString(11, transfer.getTransferReason());
            ps.setTimestamp(12, transfer.getTransferDate() != null ? Timestamp.valueOf(transfer.getTransferDate()) : null);
            ps.setTimestamp(13, transfer.getDeliveryDate() != null ? Timestamp.valueOf(transfer.getDeliveryDate()) : null);
            ps.setTimestamp(14, Timestamp.valueOf(LocalDateTime.now()));
            ps.setInt(15, transfer.getId());
            
            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * Delete internal transfer
     */
    public static boolean deleteInternalTransfer(int id) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement("DELETE FROM internal_transfers WHERE id = ?")) {
            
            ps.setInt(1, id);
            return ps.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * Get internal transfers by status
     */
    public static List<InternalTransfer> getInternalTransfersByStatus(String status) {
        List<InternalTransfer> transfers = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT * FROM internal_transfers WHERE status = ? ORDER BY created_at DESC")) {
            
            ps.setString(1, status);
            ResultSet rs = ps.executeQuery();
            
            while (rs.next()) {
                InternalTransfer transfer = createInternalTransferFromResultSet(rs);
                transfers.add(transfer);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return transfers;
    }

    /**
     * Get internal transfers by location
     */
    public static List<InternalTransfer> getInternalTransfersByLocation(String location, boolean isFromLocation) {
        List<InternalTransfer> transfers = new ArrayList<>();
        
        String query = isFromLocation ? 
            "SELECT * FROM internal_transfers WHERE from_location = ? ORDER BY created_at DESC" :
            "SELECT * FROM internal_transfers WHERE to_location = ? ORDER BY created_at DESC";
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(query)) {
            
            ps.setString(1, location);
            ResultSet rs = ps.executeQuery();
            
            while (rs.next()) {
                InternalTransfer transfer = createInternalTransferFromResultSet(rs);
                transfers.add(transfer);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return transfers;
    }

    /**
     * Get internal transfers by date range
     */
    public static List<InternalTransfer> getInternalTransfersByDateRange(LocalDate startDate, LocalDate endDate) {
        List<InternalTransfer> transfers = new ArrayList<>();
        
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                     "SELECT * FROM internal_transfers " +
                     "WHERE start_date >= ? AND end_date <= ? " +
                     "ORDER BY created_at DESC")) {
            
            ps.setDate(1, Date.valueOf(startDate));
            ps.setDate(2, Date.valueOf(endDate));
            ResultSet rs = ps.executeQuery();
            
            while (rs.next()) {
                InternalTransfer transfer = createInternalTransferFromResultSet(rs);
                transfers.add(transfer);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return transfers;
    }

    /**
     * Generate next request number
     */
    public static String generateNextRequestNumber() {
        try (Connection conn = DatabaseManager.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(
                     "SELECT MAX(CAST(SUBSTR(request_number, 2) AS INTEGER)) as max_num " +
                     "FROM internal_transfers WHERE request_number LIKE 'P%'")) {
            
            if (rs.next()) {
                int maxNum = rs.getInt("max_num");
                return String.format("P%05d", maxNum + 1);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return "P00001";
    }

    /**
     * Helper method to create InternalTransfer from ResultSet
     */
    private static InternalTransfer createInternalTransferFromResultSet(ResultSet rs) throws SQLException {
        InternalTransfer transfer = new InternalTransfer(
            rs.getInt("id"),
            rs.getString("request_number"),
            rs.getString("from_location"),
            rs.getString("to_location"),
            rs.getString("item"),
            rs.getString("quantity"),
            rs.getString("status")
        );
        
        Date startDate = rs.getDate("start_date");
        if (startDate != null) transfer.setStartDate(startDate.toLocalDate());
        
        Date endDate = rs.getDate("end_date");
        if (endDate != null) transfer.setEndDate(endDate.toLocalDate());
        
        transfer.setCreatedBy(rs.getString("created_by"));
        transfer.setApprovedBy(rs.getString("approved_by"));
        transfer.setNotes(rs.getString("notes"));
        transfer.setTransferReason(rs.getString("transfer_reason"));
        
        Timestamp transferDate = rs.getTimestamp("transfer_date");
        if (transferDate != null) transfer.setTransferDate(transferDate.toLocalDateTime());
        
        Timestamp deliveryDate = rs.getTimestamp("delivery_date");
        if (deliveryDate != null) transfer.setDeliveryDate(deliveryDate.toLocalDateTime());
        
        transfer.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
        transfer.setLastUpdated(rs.getTimestamp("last_updated").toLocalDateTime());
        
        return transfer;
    }
}
