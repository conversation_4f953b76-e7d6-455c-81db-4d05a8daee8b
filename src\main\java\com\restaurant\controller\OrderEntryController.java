package com.restaurant.controller;

import com.restaurant.model.*;
import com.restaurant.util.PrintService;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Parent;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.input.KeyCode;
import javafx.stage.Stage;

import java.net.URL;
import java.util.List;
import java.util.ResourceBundle;

public class OrderEntryController implements Initializable {
    
    @FXML private TextField tableNumberField;
    @FXML private CheckBox takeawayCheckbox;
    @FXML private Button newOrderBtn;
    @FXML private Button saveOrderBtn;
    
    // Menu section
    @FXML private ComboBox<MenuCategory> categoryComboBox;
    @FXML private TextField searchField;
    @FXML private TableView<com.restaurant.model.MenuItem> menuTable;
    @FXML private TableColumn<com.restaurant.model.MenuItem, String> nameColumn;
    @FXML private TableColumn<com.restaurant.model.MenuItem, Double> priceColumn;
    @FXML private TableColumn<com.restaurant.model.MenuItem, String> categoryColumn;
    @FXML private Spinner<Integer> quantitySpinner;
    
    // Order section
    @FXML private TableView<OrderItem> orderTable;
    @FXML private TableColumn<OrderItem, String> orderItemColumn;
    @FXML private TableColumn<OrderItem, Integer> orderQuantityColumn;
    @FXML private TableColumn<OrderItem, Double> orderPriceColumn;
    @FXML private TableColumn<OrderItem, Double> orderTotalColumn;
    
    // Summary labels
    @FXML private Label subtotalLabel;
    @FXML private Label gstLabel;
    @FXML private Label serviceChargeLabel;
    @FXML private Label grandTotalLabel;
    
    @FXML private Button generateKOTBtn;
    @FXML private Button generateBillBtn;
    
    private ObservableList<com.restaurant.model.MenuItem> menuItems;
    private ObservableList<MenuCategory> categories;
    private Order currentOrder;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        setupTableColumns();
        setupSpinner();
        loadData();
        newOrder();
        setupKeyHandlers();
    }

    /**
     * Set the table number for this order (called from TableManagement)
     * This will load any existing order for the table
     */
    public void setTableNumber(String tableNumber) {
        System.out.println("DEBUG: setTableNumber called with: " + tableNumber);

        if (tableNumberField != null && tableNumber != null) {
            System.out.println("DEBUG: Setting table number field to: " + tableNumber);
            tableNumberField.setText(tableNumber);
            // Disable takeaway checkbox since we have a table
            takeawayCheckbox.setSelected(false);

            System.out.println("DEBUG: About to load existing order for table: " + tableNumber);
            // Load existing order for this table if it exists
            loadExistingOrderForTable(Integer.parseInt(tableNumber));
        } else {
            System.out.println("DEBUG: tableNumberField is null or tableNumber is null");
            System.out.println("DEBUG: tableNumberField: " + tableNumberField);
            System.out.println("DEBUG: tableNumber: " + tableNumber);
        }
    }

    /**
     * Load existing order for a specific table
     */
    private void loadExistingOrderForTable(int tableNumber) {
        System.out.println("DEBUG: loadExistingOrderForTable called for table: " + tableNumber);

        try {
            // Get the existing order for this table (if any)
            System.out.println("DEBUG: Calling OrderDAO.getActiveOrderByTable(" + tableNumber + ")");
            Order existingOrder = OrderDAO.getActiveOrderByTable(tableNumber);

            if (existingOrder != null) {
                System.out.println("DEBUG: Found existing order with ID: " + existingOrder.getId() +
                                 " for table: " + tableNumber);

                // Load the existing order
                currentOrder = existingOrder;

                // Load order items into the table
                ObservableList<OrderItem> orderItems = FXCollections.observableArrayList();
                orderItems.addAll(currentOrder.getItems());
                orderTable.setItems(orderItems);

                System.out.println("DEBUG: Loaded " + orderItems.size() + " items into order table");

                // Update the order summary
                updateOrderSummary();

                System.out.println("SUCCESS: Loaded existing order for table " + tableNumber + " with " +
                                 orderItems.size() + " items");
            } else {
                System.out.println("DEBUG: No existing order found for table " + tableNumber);

                // No existing order, create new one
                currentOrder = new Order();
                currentOrder.setTableNumber(tableNumber);
                orderTable.setItems(FXCollections.observableArrayList());
                updateOrderSummary();

                System.out.println("SUCCESS: Created new order for table " + tableNumber);
            }
        } catch (Exception e) {
            System.out.println("ERROR: Exception in loadExistingOrderForTable: " + e.getMessage());
            e.printStackTrace();
            // Fallback to new order
            currentOrder = new Order();
            currentOrder.setTableNumber(tableNumber);
            orderTable.setItems(FXCollections.observableArrayList());
            updateOrderSummary();
        }
    }

    private void setupTableColumns() {
        // Menu table columns
        nameColumn.setCellValueFactory(new PropertyValueFactory<>("name"));
        priceColumn.setCellValueFactory(new PropertyValueFactory<>("price"));
        categoryColumn.setCellValueFactory(new PropertyValueFactory<>("category"));
        
        // Order table columns
        orderItemColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(cellData.getValue().getMenuItem().getName()));
        orderQuantityColumn.setCellValueFactory(new PropertyValueFactory<>("quantity"));
        orderPriceColumn.setCellValueFactory(new PropertyValueFactory<>("price"));
        orderTotalColumn.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleDoubleProperty(cellData.getValue().getTotalPrice()).asObject());
        
        // Format price columns
        priceColumn.setCellFactory(column -> new TableCell<com.restaurant.model.MenuItem, Double>() {
            @Override
            protected void updateItem(Double price, boolean empty) {
                super.updateItem(price, empty);
                if (empty || price == null) {
                    setText(null);
                } else {
                    setText(String.format("$%.2f", price));
                }
            }
        });
        
        orderPriceColumn.setCellFactory(column -> new TableCell<OrderItem, Double>() {
            @Override
            protected void updateItem(Double price, boolean empty) {
                super.updateItem(price, empty);
                if (empty || price == null) {
                    setText(null);
                } else {
                    setText(String.format("$%.2f", price));
                }
            }
        });
        
        orderTotalColumn.setCellFactory(column -> new TableCell<OrderItem, Double>() {
            @Override
            protected void updateItem(Double total, boolean empty) {
                super.updateItem(total, empty);
                if (empty || total == null) {
                    setText(null);
                } else {
                    setText(String.format("$%.2f", total));
                }
            }
        });
    }
    
    private void setupSpinner() {
        quantitySpinner.setValueFactory(new SpinnerValueFactory.IntegerSpinnerValueFactory(1, 99, 1));
    }
    
    private void loadData() {
        loadMenuItems();
        loadCategories();
    }
    
    private void loadMenuItems() {
        List<com.restaurant.model.MenuItem> items = MenuDAO.getAllMenuItems();
        menuItems = FXCollections.observableArrayList(items);
        menuTable.setItems(menuItems);
    }
    
    private void loadCategories() {
        List<MenuCategory> cats = MenuDAO.getAllCategories();
        categories = FXCollections.observableArrayList();
        categories.add(new MenuCategory(0, "All Categories"));
        categories.addAll(cats);
        categoryComboBox.setItems(categories);
        categoryComboBox.setValue(categories.get(0));
    }
    
    @FXML
    private void newOrder() {
        currentOrder = new Order();
        tableNumberField.clear();
        takeawayCheckbox.setSelected(false);
        orderTable.setItems(FXCollections.observableArrayList());
        updateOrderSummary();
        
        // Reset buttons
        generateKOTBtn.setDisable(true);
        generateBillBtn.setDisable(true);
        saveOrderBtn.setDisable(false);
    }
    
    @FXML
    private void saveOrder() {
        if (!validateOrder()) return;
        
        // Set order details
        if (!takeawayCheckbox.isSelected() && !tableNumberField.getText().trim().isEmpty()) {
            currentOrder.setTableNumber(Integer.parseInt(tableNumberField.getText().trim()));
        }
        currentOrder.setTakeaway(takeawayCheckbox.isSelected());
        
        int orderId = OrderDAO.saveOrder(currentOrder);
        if (orderId > 0) {
            currentOrder.setId(orderId);
            showAlert("Success", "Order saved successfully! Order ID: " + orderId);
            
            // Enable KOT and Bill generation
            generateKOTBtn.setDisable(false);
            generateBillBtn.setDisable(false);
            saveOrderBtn.setDisable(true);
        } else {
            showAlert("Error", "Failed to save order.");
        }
    }
    
    private boolean validateOrder() {
        if (currentOrder.getItems().isEmpty()) {
            showAlert("Validation Error", "Please add items to the order.");
            return false;
        }
        
        if (!takeawayCheckbox.isSelected() && tableNumberField.getText().trim().isEmpty()) {
            showAlert("Validation Error", "Please enter table number or select takeaway.");
            return false;
        }
        
        if (!takeawayCheckbox.isSelected()) {
            try {
                Integer.parseInt(tableNumberField.getText().trim());
            } catch (NumberFormatException e) {
                showAlert("Validation Error", "Please enter a valid table number.");
                return false;
            }
        }
        
        return true;
    }
    
    @FXML
    private void addToOrder() {
        com.restaurant.model.MenuItem selectedItem = menuTable.getSelectionModel().getSelectedItem();
        if (selectedItem == null) {
            showAlert("No Selection", "Please select an item from the menu.");
            return;
        }
        
        int quantity = quantitySpinner.getValue();
        OrderItem orderItem = new OrderItem(selectedItem, quantity);
        currentOrder.addItem(orderItem);
        
        // Refresh order table
        orderTable.setItems(FXCollections.observableArrayList(currentOrder.getItems()));
        updateOrderSummary();
        
        // Reset quantity spinner
        quantitySpinner.getValueFactory().setValue(1);
    }
    
    @FXML
    private void removeFromOrder() {
        OrderItem selectedItem = orderTable.getSelectionModel().getSelectedItem();
        if (selectedItem == null) {
            showAlert("No Selection", "Please select an item to remove.");
            return;
        }
        
        currentOrder.removeItem(selectedItem);
        orderTable.setItems(FXCollections.observableArrayList(currentOrder.getItems()));
        updateOrderSummary();
    }
    
    @FXML
    private void clearOrder() {
        Alert confirmation = new Alert(Alert.AlertType.CONFIRMATION);
        confirmation.setTitle("Clear Order");
        confirmation.setHeaderText("Clear Current Order");
        confirmation.setContentText("Are you sure you want to clear all items from the current order?");
        
        if (confirmation.showAndWait().orElse(ButtonType.CANCEL) == ButtonType.OK) {
            currentOrder.getItems().clear();
            orderTable.setItems(FXCollections.observableArrayList());
            updateOrderSummary();
        }
    }
    
    private void updateOrderSummary() {
        double subtotal = currentOrder.calculateSubtotal();
        double gst = currentOrder.calculateGST();
        double serviceCharge = currentOrder.calculateServiceCharge();
        double grandTotal = currentOrder.calculateGrandTotal();
        
        subtotalLabel.setText(String.format("$%.2f", subtotal));
        gstLabel.setText(String.format("$%.2f", gst));
        serviceChargeLabel.setText(String.format("$%.2f", serviceCharge));
        grandTotalLabel.setText(String.format("$%.2f", grandTotal));
    }
    
    @FXML
    private void filterByCategory() {
        MenuCategory selected = categoryComboBox.getValue();
        if (selected != null && selected.getId() == 0) {
            loadMenuItems();
        } else if (selected != null) {
            List<com.restaurant.model.MenuItem> filtered = MenuDAO.getMenuItemsByCategory(selected.getId());
            menuItems = FXCollections.observableArrayList(filtered);
            menuTable.setItems(menuItems);
        }
    }
    
    @FXML
    private void searchItems() {
        String searchTerm = searchField.getText().trim();
        if (searchTerm.isEmpty()) {
            loadMenuItems();
        } else {
            List<com.restaurant.model.MenuItem> filtered = MenuDAO.searchMenuItems(searchTerm);
            menuItems = FXCollections.observableArrayList(filtered);
            menuTable.setItems(menuItems);
        }
    }
    
    @FXML
    private void generateKOT() {
        if (currentOrder.getId() == 0) {
            showAlert("Error", "Please save the order first.");
            return;
        }
        
        try {
            PrintService.generateKOT(currentOrder);
            showAlert("Success", "KOT generated successfully!");
        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Failed to generate KOT: " + e.getMessage());
        }
    }
    
    @FXML
    private void generateBill() {
        if (currentOrder.getId() == 0) {
            showAlert("Error", "Please save the order first.");
            return;
        }
        
        try {
            PrintService.generateBill(currentOrder);
            showAlert("Success", "Bill generated successfully!");
            
            // Update order status to completed
            OrderDAO.updateOrderStatus(currentOrder.getId(), "COMPLETED");
        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Failed to generate bill: " + e.getMessage());
        }
    }

    private void setupKeyHandlers() {
        // This will be called after the scene is set
        Platform.runLater(() -> {
            if (tableNumberField != null && tableNumberField.getScene() != null) {
                tableNumberField.getScene().setOnKeyPressed(event -> {
                    if (event.getCode() == KeyCode.ESCAPE) {
                        goBack();
                    }
                });
            }
        });
    }

    @FXML
    private void goBack() {
        try {
            // Navigate back to dashboard
            Parent dashboardView = FXMLLoader.load(getClass().getResource("/fxml/Dashboard.fxml"));
            Stage stage = (Stage) tableNumberField.getScene().getWindow();
            stage.getScene().setRoot(dashboardView);
        } catch (Exception e) {
            e.printStackTrace();
            showAlert("Error", "Could not return to dashboard");
        }
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
