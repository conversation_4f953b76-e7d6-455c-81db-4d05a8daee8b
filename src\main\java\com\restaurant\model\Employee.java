package com.restaurant.model;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class Employee {
    private int id;
    private String name;
    private String employeeId;
    private String department; // "Kitchen", "Service", "Management", "Cleaning"
    private String position;
    private String phone;
    private String email;
    private String address;
    private LocalDate dateOfJoining;
    private String status; // "Active", "Inactive", "On Leave"
    private double salary;
    private String shiftTiming; // "Morning", "Evening", "Night", "Full Day"
    private LocalDateTime createdAt;
    private LocalDateTime lastUpdated;
    private String notes;
    
    // Constructors
    public Employee() {
        this.createdAt = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
        this.status = "Active";
        this.dateOfJoining = LocalDate.now();
    }
    
    public Employee(int id, String name, String employeeId, String department, String position) {
        this.id = id;
        this.name = name;
        this.employeeId = employeeId;
        this.department = department;
        this.position = position;
        this.createdAt = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
        this.status = "Active";
        this.dateOfJoining = LocalDate.now();
    }
    
    public Employee(String name, String employeeId, String department, String position, String phone, String email) {
        this.name = name;
        this.employeeId = employeeId;
        this.department = department;
        this.position = position;
        this.phone = phone;
        this.email = email;
        this.createdAt = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
        this.status = "Active";
        this.dateOfJoining = LocalDate.now();
    }
    
    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { 
        this.name = name;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public String getEmployeeId() { return employeeId; }
    public void setEmployeeId(String employeeId) { 
        this.employeeId = employeeId;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public String getDepartment() { return department; }
    public void setDepartment(String department) { 
        this.department = department;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public String getPosition() { return position; }
    public void setPosition(String position) { 
        this.position = position;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { 
        this.phone = phone;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { 
        this.email = email;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public String getAddress() { return address; }
    public void setAddress(String address) { 
        this.address = address;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public LocalDate getDateOfJoining() { return dateOfJoining; }
    public void setDateOfJoining(LocalDate dateOfJoining) { 
        this.dateOfJoining = dateOfJoining;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { 
        this.status = status;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public double getSalary() { return salary; }
    public void setSalary(double salary) { 
        this.salary = salary;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public String getShiftTiming() { return shiftTiming; }
    public void setShiftTiming(String shiftTiming) { 
        this.shiftTiming = shiftTiming;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getLastUpdated() { return lastUpdated; }
    public void setLastUpdated(LocalDateTime lastUpdated) { this.lastUpdated = lastUpdated; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { 
        this.notes = notes;
        this.lastUpdated = LocalDateTime.now();
    }
    
    // Helper methods
    public String getDisplayName() {
        return name + " (" + employeeId + ")";
    }
    
    public String getContactInfo() {
        StringBuilder contact = new StringBuilder();
        if (phone != null && !phone.isEmpty()) {
            contact.append("📞 ").append(phone);
        }
        if (email != null && !email.isEmpty()) {
            if (contact.length() > 0) contact.append(" | ");
            contact.append("📧 ").append(email);
        }
        return contact.toString();
    }
    
    public boolean isActive() {
        return "Active".equals(status);
    }
    
    public String getStatusIcon() {
        switch (status) {
            case "Active": return "🟢";
            case "On Leave": return "🟡";
            case "Inactive": return "🔴";
            default: return "⚪";
        }
    }
    
    public String getDepartmentIcon() {
        if (department == null) return "👤";
        
        switch (department.toLowerCase()) {
            case "kitchen": return "👨‍🍳";
            case "service": return "👨‍💼";
            case "management": return "👔";
            case "cleaning": return "🧹";
            case "security": return "🛡️";
            default: return "👤";
        }
    }
    
    public String getShiftIcon() {
        if (shiftTiming == null) return "🕐";
        
        switch (shiftTiming.toLowerCase()) {
            case "morning": return "🌅";
            case "evening": return "🌆";
            case "night": return "🌙";
            case "full day": return "☀️";
            default: return "🕐";
        }
    }
    
    public String getSalaryDisplay() {
        return "₹" + String.format("%.2f", salary);
    }
    
    public long getExperienceInDays() {
        return java.time.temporal.ChronoUnit.DAYS.between(dateOfJoining, LocalDate.now());
    }
    
    public String getExperienceDisplay() {
        long days = getExperienceInDays();
        if (days < 30) {
            return days + " days";
        } else if (days < 365) {
            long months = days / 30;
            return months + " month" + (months > 1 ? "s" : "");
        } else {
            long years = days / 365;
            long remainingMonths = (days % 365) / 30;
            String result = years + " year" + (years > 1 ? "s" : "");
            if (remainingMonths > 0) {
                result += ", " + remainingMonths + " month" + (remainingMonths > 1 ? "s" : "");
            }
            return result;
        }
    }
    
    @Override
    public String toString() {
        return getDisplayName();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Employee employee = (Employee) obj;
        return id == employee.id;
    }
    
    @Override
    public int hashCode() {
        return Integer.hashCode(id);
    }
}
