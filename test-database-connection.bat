@echo off
echo Testing Database Connection and Thread Safety...
echo.

echo Compiling project...
call mvn clean compile -q

if %ERRORLEVEL% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo.
echo Running database connection test...
echo This will test the new thread-safe database connection management.
echo.

java -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar" com.restaurant.model.DatabaseManager

echo.
echo Test completed. Check for any errors above.
echo If no errors, the database connection improvements are working.
pause
