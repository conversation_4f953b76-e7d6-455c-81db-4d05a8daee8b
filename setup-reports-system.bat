@echo off
echo 📊 SETTING UP ADVANCED REPORTS SYSTEM 📊
echo.

echo 🗄️ STEP 1: Creating Reports Database Schema...
echo.

echo 🔧 COMPILING REPORTS SCHEMA CREATOR...
javac -d target/classes -cp "target/classes;lib/*" src/main/java/com/restaurant/util/ReportsSchemaCreator.java

if %ERRORLEVEL% neq 0 (
    echo ❌ Compilation failed for ReportsSchemaCreator
    pause
    exit /b 1
)

echo ✅ Compilation successful
echo.

echo 🗄️ CREATING REPORTS DATABASE TABLES...
java -cp "target/classes;lib/*" com.restaurant.util.ReportsSchemaCreator

if %ERRORLEVEL% neq 0 (
    echo ❌ Database schema creation failed
    pause
    exit /b 1
)

echo.
echo 📊 STEP 2: Testing Report Generation...
echo.

echo 🔧 CREATING REPORT TESTER...

echo package com.restaurant.test; > src\main\java\com\restaurant\test\ReportTester.java
echo. >> src\main\java\com\restaurant\test\ReportTester.java
echo import com.restaurant.model.*; >> src\main\java\com\restaurant\test\ReportTester.java
echo import com.restaurant.service.ReportService; >> src\main\java\com\restaurant\test\ReportTester.java
echo import java.time.LocalDate; >> src\main\java\com\restaurant\test\ReportTester.java
echo import java.util.Map; >> src\main\java\com\restaurant\test\ReportTester.java
echo. >> src\main\java\com\restaurant\test\ReportTester.java
echo public class ReportTester { >> src\main\java\com\restaurant\test\ReportTester.java
echo     public static void main(String[] args) { >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("🧪 Testing Reports System..."); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println(); >> src\main\java\com\restaurant\test\ReportTester.java
echo. >> src\main\java\com\restaurant\test\ReportTester.java
echo         // Test Daily Report Generation >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("📅 Testing Daily Report Generation..."); >> src\main\java\com\restaurant\test\ReportTester.java
echo         LocalDate today = LocalDate.now(); >> src\main\java\com\restaurant\test\ReportTester.java
echo         DailyReport dailyReport = ReportService.generateDailyReport(today); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("Daily Report: " + dailyReport); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println(); >> src\main\java\com\restaurant\test\ReportTester.java
echo. >> src\main\java\com\restaurant\test\ReportTester.java
echo         // Test Weekly Report Generation >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("📊 Testing Weekly Report Generation..."); >> src\main\java\com\restaurant\test\ReportTester.java
echo         LocalDate weekStart = today.minusDays(today.getDayOfWeek().getValue() - 1); >> src\main\java\com\restaurant\test\ReportTester.java
echo         LocalDate weekEnd = weekStart.plusDays(6); >> src\main\java\com\restaurant\test\ReportTester.java
echo         WeeklyReport weeklyReport = ReportService.generateWeeklyReport(weekStart, weekEnd); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("Weekly Report: " + weeklyReport); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println(); >> src\main\java\com\restaurant\test\ReportTester.java
echo. >> src\main\java\com\restaurant\test\ReportTester.java
echo         // Test Monthly Report Generation >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("📆 Testing Monthly Report Generation..."); >> src\main\java\com\restaurant\test\ReportTester.java
echo         MonthlyReport monthlyReport = ReportService.generateMonthlyReport(today.getMonthValue(), today.getYear()); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("Monthly Report: " + monthlyReport); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println(); >> src\main\java\com\restaurant\test\ReportTester.java
echo. >> src\main\java\com\restaurant\test\ReportTester.java
echo         // Test Analytics Summary >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("📈 Testing Analytics Summary..."); >> src\main\java\com\restaurant\test\ReportTester.java
echo         Map^<String, Object^> analytics = ReportService.getAnalyticsSummary(); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("Analytics Summary:"); >> src\main\java\com\restaurant\test\ReportTester.java
echo         analytics.forEach((key, value) -^> System.out.println("  " + key + ": " + value)); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println(); >> src\main\java\com\restaurant\test\ReportTester.java
echo. >> src\main\java\com\restaurant\test\ReportTester.java
echo         // Test Saving Reports >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("💾 Testing Report Saving..."); >> src\main\java\com\restaurant\test\ReportTester.java
echo         boolean dailySaved = ReportService.generateAndSaveDailyReport(today); >> src\main\java\com\restaurant\test\ReportTester.java
echo         boolean weeklySaved = ReportService.generateAndSaveWeeklyReport(weekStart, weekEnd); >> src\main\java\com\restaurant\test\ReportTester.java
echo         boolean monthlySaved = ReportService.generateAndSaveMonthlyReport(today.getMonthValue(), today.getYear()); >> src\main\java\com\restaurant\test\ReportTester.java
echo. >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("Daily Report Saved: " + (dailySaved ? "✅" : "❌")); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("Weekly Report Saved: " + (weeklySaved ? "✅" : "❌")); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("Monthly Report Saved: " + (monthlySaved ? "✅" : "❌")); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println(); >> src\main\java\com\restaurant\test\ReportTester.java
echo. >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("🎉 Reports System Testing Complete!"); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println(); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("📊 REPORTS SYSTEM FEATURES:"); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("✅ Daily Reports - Track daily sales, orders, and performance"); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("✅ Weekly Reports - Analyze weekly trends and patterns"); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("✅ Monthly Reports - Monitor monthly growth and insights"); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("✅ Analytics Dashboard - Real-time business metrics"); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("✅ Platform Analytics - Swiggy vs Zomato performance"); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("✅ Growth Tracking - Month-over-month growth analysis"); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("✅ Peak Hour Analysis - Identify busiest times"); >> src\main\java\com\restaurant\test\ReportTester.java
echo         System.out.println("✅ Revenue Trends - Visual charts and graphs"); >> src\main\java\com\restaurant\test\ReportTester.java
echo     } >> src\main\java\com\restaurant\test\ReportTester.java
echo } >> src\main\java\com\restaurant\test\ReportTester.java

echo ✅ ReportTester created
echo.

echo 🔧 COMPILING REPORT TESTER...
javac -d target/classes -cp "target/classes;lib/*" src/main/java/com/restaurant/test/ReportTester.java

if %ERRORLEVEL% neq 0 (
    echo ❌ Compilation failed for ReportTester
    pause
    exit /b 1
)

echo ✅ Compilation successful
echo.

echo 🧪 RUNNING REPORTS SYSTEM TEST...
java -cp "target/classes;lib/*" com.restaurant.test.ReportTester

echo.
echo 🎉 REPORTS SYSTEM SETUP COMPLETE!
echo.

echo 📋 WHAT WAS CREATED:
echo ✅ Reports database schema (daily_reports, weekly_reports, monthly_reports)
echo ✅ Analytics metrics tables for detailed tracking
echo ✅ Report data models (DailyReport, WeeklyReport, MonthlyReport)
echo ✅ ReportDAO for database operations
echo ✅ ReportService for generating reports and analytics
echo ✅ AdvancedReportsController for UI management
echo ✅ Advanced Reports FXML interface with charts and tables
echo.

echo 🚀 HOW TO USE:
echo 1. Open the Advanced Reports interface in your restaurant app
echo 2. View real-time analytics on the Dashboard tab
echo 3. Generate daily reports for specific dates
echo 4. Create weekly reports for date ranges
echo 5. Generate monthly reports with growth analysis
echo 6. Export reports for business analysis
echo.

echo 📊 ANALYTICS FEATURES:
echo 📈 Real-time dashboard with today/week/month summaries
echo 🥧 Platform distribution pie charts (Swiggy vs Zomato)
echo 📉 Revenue trend line charts
echo 📅 Automated daily report generation
echo 📊 Weekly performance analysis
echo 📆 Monthly growth tracking with percentage calculations
echo ⏰ Peak hour identification
echo 🏆 Best performing days/weeks identification
echo.

echo Your advanced reports and analytics system is ready! 📊✨
echo.

pause
