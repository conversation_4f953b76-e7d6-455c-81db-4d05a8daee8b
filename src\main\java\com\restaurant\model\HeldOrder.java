package com.restaurant.model;

import java.time.LocalDateTime;

/**
 * Model class representing a held order (KOT on hold)
 * Used when orders need to be temporarily paused before sending to kitchen
 */
public class HeldOrder {
    
    private String orderId;
    private int tableNumber;
    private LocalDateTime heldTime;
    private String reason;
    private double totalAmount;
    private String orderData; // JSON representation of the order
    private String heldBy; // Staff member who held the order
    private LocalDateTime createdTime;
    private String status; // HELD, RELEASED, CANCELLED
    
    // Constructors
    public HeldOrder() {
        this.createdTime = LocalDateTime.now();
        this.status = "HELD";
    }
    
    public HeldOrder(String orderId, int tableNumber, String reason) {
        this();
        this.orderId = orderId;
        this.tableNumber = tableNumber;
        this.reason = reason;
        this.heldTime = LocalDateTime.now();
    }
    
    public HeldOrder(String orderId, int tableNumber, String reason, double totalAmount, String orderData) {
        this(orderId, tableNumber, reason);
        this.totalAmount = totalAmount;
        this.orderData = orderData;
    }
    
    // Getters and Setters
    
    public String getOrderId() {
        return orderId;
    }
    
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
    
    public int getTableNumber() {
        return tableNumber;
    }
    
    public void setTableNumber(int tableNumber) {
        this.tableNumber = tableNumber;
    }
    
    public LocalDateTime getHeldTime() {
        return heldTime;
    }
    
    public void setHeldTime(LocalDateTime heldTime) {
        this.heldTime = heldTime;
    }
    
    public String getReason() {
        return reason;
    }
    
    public void setReason(String reason) {
        this.reason = reason;
    }
    
    public double getTotalAmount() {
        return totalAmount;
    }
    
    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }
    
    public String getOrderData() {
        return orderData;
    }
    
    public void setOrderData(String orderData) {
        this.orderData = orderData;
    }
    
    public String getHeldBy() {
        return heldBy;
    }
    
    public void setHeldBy(String heldBy) {
        this.heldBy = heldBy;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    // Utility methods
    
    /**
     * Get formatted held time for display
     */
    public String getFormattedHeldTime() {
        if (heldTime != null) {
            return heldTime.format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"));
        }
        return "";
    }
    
    /**
     * Get duration since order was held
     */
    public String getHeldDuration() {
        if (heldTime != null) {
            LocalDateTime now = LocalDateTime.now();
            long minutes = java.time.Duration.between(heldTime, now).toMinutes();
            
            if (minutes < 60) {
                return minutes + " min";
            } else {
                long hours = minutes / 60;
                long remainingMinutes = minutes % 60;
                return hours + "h " + remainingMinutes + "m";
            }
        }
        return "0 min";
    }
    
    /**
     * Check if order has been held for too long (more than 30 minutes)
     */
    public boolean isOverdue() {
        if (heldTime != null) {
            LocalDateTime now = LocalDateTime.now();
            long minutes = java.time.Duration.between(heldTime, now).toMinutes();
            return minutes > 30; // Consider overdue after 30 minutes
        }
        return false;
    }
    
    /**
     * Get priority level based on how long order has been held
     */
    public String getPriorityLevel() {
        if (heldTime != null) {
            LocalDateTime now = LocalDateTime.now();
            long minutes = java.time.Duration.between(heldTime, now).toMinutes();
            
            if (minutes > 30) {
                return "HIGH";
            } else if (minutes > 15) {
                return "MEDIUM";
            } else {
                return "LOW";
            }
        }
        return "LOW";
    }
    
    /**
     * Get priority icon for display
     */
    public String getPriorityIcon() {
        switch (getPriorityLevel()) {
            case "HIGH":
                return "🔴";
            case "MEDIUM":
                return "🟡";
            case "LOW":
                return "🟢";
            default:
                return "⚪";
        }
    }
    
    /**
     * Get status icon for display
     */
    public String getStatusIcon() {
        switch (status) {
            case "HELD":
                return "⏸️";
            case "RELEASED":
                return "▶️";
            case "CANCELLED":
                return "❌";
            default:
                return "❓";
        }
    }
    
    /**
     * Create a summary string for the held order
     */
    public String getSummary() {
        return String.format("Order %s - Table %d - %s - ₹%.2f", 
                           orderId, tableNumber, getHeldDuration(), totalAmount);
    }
    
    @Override
    public String toString() {
        return "HeldOrder{" +
                "orderId='" + orderId + '\'' +
                ", tableNumber=" + tableNumber +
                ", heldTime=" + heldTime +
                ", reason='" + reason + '\'' +
                ", totalAmount=" + totalAmount +
                ", status='" + status + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        HeldOrder heldOrder = (HeldOrder) obj;
        return orderId != null ? orderId.equals(heldOrder.orderId) : heldOrder.orderId == null;
    }
    
    @Override
    public int hashCode() {
        return orderId != null ? orderId.hashCode() : 0;
    }
    
    // Static factory methods
    
    /**
     * Create a held order from an existing order
     */
    public static HeldOrder fromOrder(Order order, String reason) {
        HeldOrder heldOrder = new HeldOrder();
        heldOrder.setOrderId("HELD_" + System.currentTimeMillis());
        heldOrder.setTableNumber(order.getTableNumber());
        heldOrder.setReason(reason);
        heldOrder.setTotalAmount(order.calculateTotal());
        heldOrder.setOrderData(order.toJsonString());
        heldOrder.setHeldTime(LocalDateTime.now());
        return heldOrder;
    }
    
    /**
     * Create a quick hold order with default reason
     */
    public static HeldOrder createQuickHold(int tableNumber, double totalAmount) {
        HeldOrder heldOrder = new HeldOrder();
        heldOrder.setOrderId("QUICK_" + System.currentTimeMillis());
        heldOrder.setTableNumber(tableNumber);
        heldOrder.setReason("Quick hold - no reason specified");
        heldOrder.setTotalAmount(totalAmount);
        heldOrder.setHeldTime(LocalDateTime.now());
        return heldOrder;
    }
}
