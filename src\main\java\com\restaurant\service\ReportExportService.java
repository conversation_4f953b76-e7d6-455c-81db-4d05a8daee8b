package com.restaurant.service;

import com.restaurant.model.*;
import java.io.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

public class ReportExportService {
    
    // Export Daily Report to CSV
    public static boolean exportDailyReportToCSV(DailyReport report, String filePath) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(filePath))) {
            // CSV Header
            writer.println("Date,Total Orders,Total Revenue,Swiggy Orders,Zomato Orders,Swiggy Revenue,Zomato Revenue,Avg Order Value,Peak Hour");
            
            // CSV Data
            writer.printf("%s,%d,%.2f,%d,%d,%.2f,%.2f,%.2f,%s%n",
                report.getReportDate(),
                report.getTotalOrders(),
                report.getTotalRevenue(),
                report.getSwiggyOrders(),
                report.getZomatoOrders(),
                report.getSwiggyRevenue(),
                report.getZomatoRevenue(),
                report.getAvgOrderValue(),
                report.getPeakHour() != null ? report.getPeakHour() : "N/A"
            );
            
            return true;
        } catch (IOException e) {
            System.err.println("Error exporting daily report to CSV: " + e.getMessage());
            return false;
        }
    }
    
    // Export Weekly Report to CSV
    public static boolean exportWeeklyReportToCSV(WeeklyReport report, String filePath) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(filePath))) {
            // CSV Header
            writer.println("Week Start,Week End,Total Orders,Total Revenue,Swiggy Orders,Zomato Orders,Swiggy Revenue,Zomato Revenue,Avg Daily Orders,Avg Daily Revenue,Best Day");
            
            // CSV Data
            writer.printf("%s,%s,%d,%.2f,%d,%d,%.2f,%.2f,%.2f,%.2f,%s%n",
                report.getWeekStartDate(),
                report.getWeekEndDate(),
                report.getTotalOrders(),
                report.getTotalRevenue(),
                report.getSwiggyOrders(),
                report.getZomatoOrders(),
                report.getSwiggyRevenue(),
                report.getZomatoRevenue(),
                report.getAvgDailyOrders(),
                report.getAvgDailyRevenue(),
                report.getBestDay() != null ? report.getBestDay() : "N/A"
            );
            
            return true;
        } catch (IOException e) {
            System.err.println("Error exporting weekly report to CSV: " + e.getMessage());
            return false;
        }
    }
    
    // Export Monthly Report to CSV
    public static boolean exportMonthlyReportToCSV(MonthlyReport report, String filePath) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(filePath))) {
            // CSV Header
            writer.println("Month,Year,Total Orders,Total Revenue,Swiggy Orders,Zomato Orders,Swiggy Revenue,Zomato Revenue,Avg Daily Orders,Avg Daily Revenue,Growth Percentage,Best Week");
            
            // CSV Data
            writer.printf("%d,%d,%d,%.2f,%d,%d,%.2f,%.2f,%.2f,%.2f,%.2f,%s%n",
                report.getReportMonth(),
                report.getReportYear(),
                report.getTotalOrders(),
                report.getTotalRevenue(),
                report.getSwiggyOrders(),
                report.getZomatoOrders(),
                report.getSwiggyRevenue(),
                report.getZomatoRevenue(),
                report.getAvgDailyOrders(),
                report.getAvgDailyRevenue(),
                report.getGrowthPercentage(),
                report.getBestWeek() != null ? report.getBestWeek() : "N/A"
            );
            
            return true;
        } catch (IOException e) {
            System.err.println("Error exporting monthly report to CSV: " + e.getMessage());
            return false;
        }
    }
    
    // Export Multiple Daily Reports to CSV
    public static boolean exportDailyReportsToCSV(List<DailyReport> reports, String filePath) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(filePath))) {
            // CSV Header
            writer.println("Date,Total Orders,Total Revenue,Swiggy Orders,Zomato Orders,Swiggy Revenue,Zomato Revenue,Avg Order Value,Peak Hour");
            
            // CSV Data
            for (DailyReport report : reports) {
                writer.printf("%s,%d,%.2f,%d,%d,%.2f,%.2f,%.2f,%s%n",
                    report.getReportDate(),
                    report.getTotalOrders(),
                    report.getTotalRevenue(),
                    report.getSwiggyOrders(),
                    report.getZomatoOrders(),
                    report.getSwiggyRevenue(),
                    report.getZomatoRevenue(),
                    report.getAvgOrderValue(),
                    report.getPeakHour() != null ? report.getPeakHour() : "N/A"
                );
            }
            
            return true;
        } catch (IOException e) {
            System.err.println("Error exporting daily reports to CSV: " + e.getMessage());
            return false;
        }
    }
    
    // Generate Analytics Summary Report
    public static boolean exportAnalyticsSummaryToText(String filePath) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(filePath))) {
            writer.println("📊 RESTAURANT ANALYTICS SUMMARY REPORT");
            writer.println("Generated on: " + LocalDate.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
            writer.println("=" + "=".repeat(50));
            writer.println();
            
            // Get analytics data
            var analytics = ReportService.getAnalyticsSummary();
            
            writer.println("📈 TODAY'S PERFORMANCE:");
            writer.println("  Orders: " + analytics.get("todayOrders"));
            writer.println("  Revenue: ₹" + String.format("%.2f", (Double) analytics.get("todayRevenue")));
            writer.println();
            
            writer.println("📊 THIS WEEK'S PERFORMANCE:");
            writer.println("  Orders: " + analytics.get("weekOrders"));
            writer.println("  Revenue: ₹" + String.format("%.2f", (Double) analytics.get("weekRevenue")));
            writer.println();
            
            writer.println("📆 THIS MONTH'S PERFORMANCE:");
            writer.println("  Orders: " + analytics.get("monthOrders"));
            writer.println("  Revenue: ₹" + String.format("%.2f", (Double) analytics.get("monthRevenue")));
            writer.println("  Growth: " + String.format("%.1f%%", (Double) analytics.get("monthGrowth")));
            writer.println();
            
            writer.println("🥧 PLATFORM DISTRIBUTION:");
            writer.println("  Swiggy: " + String.format("%.1f%%", (Double) analytics.get("swiggyPercentage")));
            writer.println("  Zomato: " + String.format("%.1f%%", (Double) analytics.get("zomatoPercentage")));
            writer.println();
            
            writer.println("=" + "=".repeat(50));
            writer.println("Report generated by Restaurant Management System");
            
            return true;
        } catch (IOException e) {
            System.err.println("Error exporting analytics summary: " + e.getMessage());
            return false;
        }
    }
    
    // Generate file name with timestamp
    public static String generateFileName(String prefix, String extension) {
        String timestamp = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        return prefix + "_" + timestamp + "." + extension;
    }
    
    // Export all reports for a date range
    public static boolean exportAllReports(LocalDate startDate, LocalDate endDate, String directory) {
        try {
            // Create directory if it doesn't exist
            File dir = new File(directory);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            
            // Export daily reports
            List<DailyReport> dailyReports = ReportDAO.getDailyReports(startDate, endDate);
            String dailyFile = directory + "/" + generateFileName("daily_reports", "csv");
            exportDailyReportsToCSV(dailyReports, dailyFile);
            
            // Export weekly reports
            List<WeeklyReport> weeklyReports = ReportDAO.getWeeklyReports(10);
            String weeklyFile = directory + "/" + generateFileName("weekly_reports", "csv");
            if (!weeklyReports.isEmpty()) {
                try (PrintWriter writer = new PrintWriter(new FileWriter(weeklyFile))) {
                    writer.println("Week Start,Week End,Total Orders,Total Revenue,Swiggy Orders,Zomato Orders,Swiggy Revenue,Zomato Revenue,Avg Daily Orders,Avg Daily Revenue,Best Day");
                    for (WeeklyReport report : weeklyReports) {
                        writer.printf("%s,%s,%d,%.2f,%d,%d,%.2f,%.2f,%.2f,%.2f,%s%n",
                            report.getWeekStartDate(),
                            report.getWeekEndDate(),
                            report.getTotalOrders(),
                            report.getTotalRevenue(),
                            report.getSwiggyOrders(),
                            report.getZomatoOrders(),
                            report.getSwiggyRevenue(),
                            report.getZomatoRevenue(),
                            report.getAvgDailyOrders(),
                            report.getAvgDailyRevenue(),
                            report.getBestDay() != null ? report.getBestDay() : "N/A"
                        );
                    }
                }
            }
            
            // Export monthly reports
            List<MonthlyReport> monthlyReports = ReportDAO.getMonthlyReports(12);
            String monthlyFile = directory + "/" + generateFileName("monthly_reports", "csv");
            if (!monthlyReports.isEmpty()) {
                try (PrintWriter writer = new PrintWriter(new FileWriter(monthlyFile))) {
                    writer.println("Month,Year,Total Orders,Total Revenue,Swiggy Orders,Zomato Orders,Swiggy Revenue,Zomato Revenue,Avg Daily Orders,Avg Daily Revenue,Growth Percentage,Best Week");
                    for (MonthlyReport report : monthlyReports) {
                        writer.printf("%d,%d,%d,%.2f,%d,%d,%.2f,%.2f,%.2f,%.2f,%.2f,%s%n",
                            report.getReportMonth(),
                            report.getReportYear(),
                            report.getTotalOrders(),
                            report.getTotalRevenue(),
                            report.getSwiggyOrders(),
                            report.getZomatoOrders(),
                            report.getSwiggyRevenue(),
                            report.getZomatoRevenue(),
                            report.getAvgDailyOrders(),
                            report.getAvgDailyRevenue(),
                            report.getGrowthPercentage(),
                            report.getBestWeek() != null ? report.getBestWeek() : "N/A"
                        );
                    }
                }
            }
            
            // Export analytics summary
            String summaryFile = directory + "/" + generateFileName("analytics_summary", "txt");
            exportAnalyticsSummaryToText(summaryFile);
            
            System.out.println("✅ All reports exported to: " + directory);
            return true;
            
        } catch (Exception e) {
            System.err.println("Error exporting all reports: " + e.getMessage());
            return false;
        }
    }
}
