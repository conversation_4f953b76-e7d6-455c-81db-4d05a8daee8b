<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.UserManagementController" stylesheets="@../css/application.css">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="module-header">
         <children>
            <Button mnemonicParsing="false" onAction="#goBack" styleClass="back-button" text="← Back" />
            <Label styleClass="module-title" text="🧑‍💼 User Management">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Button mnemonicParsing="false" onAction="#manageStaffRecord" styleClass="staff-record-button" text="👥 Staff Record" />
            <Button mnemonicParsing="false" onAction="#addNewUser" styleClass="primary-button" text="➕ Add New User" />
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </HBox>
      
      <!-- Search and Filter Section -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="filter-section">
         <children>
            <Label text="Search:" />
            <TextField fx:id="searchField" promptText="Search by username or name..." styleClass="search-field" />
            <Label text="Role:" />
            <ComboBox fx:id="roleFilterCombo" promptText="All Roles" styleClass="filter-combo" />
            <Label text="Status:" />
            <ComboBox fx:id="statusFilterCombo" promptText="All Status" styleClass="filter-combo" />
            <Button mnemonicParsing="false" onAction="#refreshUserList" styleClass="refresh-button" text="🔄 Refresh" />
         </children>
         <padding>
            <Insets bottom="15.0" left="20.0" right="20.0" top="5.0" />
         </padding>
      </HBox>
      
      <!-- Users Table -->
      <VBox VBox.vgrow="ALWAYS" styleClass="table-section">
         <children>
            <TableView fx:id="usersTable" styleClass="users-table">
               <columns>
                  <TableColumn fx:id="userIdColumn" prefWidth="60.0" text="ID" />
                  <TableColumn fx:id="usernameColumn" prefWidth="140.0" text="Username" />
                  <TableColumn fx:id="fullNameColumn" prefWidth="180.0" text="Full Name" />
                  <TableColumn fx:id="roleColumn" prefWidth="100.0" text="Role" />
                  <TableColumn fx:id="statusColumn" prefWidth="80.0" text="Status" />
                  <TableColumn fx:id="lastLoginColumn" prefWidth="140.0" text="Last Login" />
                  <TableColumn fx:id="createdDateColumn" prefWidth="120.0" text="Created Date" />
                  <TableColumn fx:id="actionsColumn" prefWidth="120.0" text="Actions" />
               </columns>
            </TableView>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="10.0" />
         </padding>
      </VBox>
      
      <!-- User Form Dialog (Hidden by default) -->
      <StackPane fx:id="userFormDialog" managed="false" visible="false" styleClass="centered-modal-overlay">
         <children>
            <!-- Modal Backdrop -->
            <Region styleClass="modal-backdrop" onMouseClicked="#closeUserDialog" />

            <!-- Centered Modal Dialog -->
            <VBox styleClass="centered-modal-dialog" maxWidth="600" maxHeight="550">
               <children>
                  <!-- Modal Header -->
                  <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="modal-header">
                     <children>
                        <Label fx:id="dialogTitle" styleClass="modal-title" text="Add New User">
                           <font>
                              <Font name="System Bold" size="18.0" />
                           </font>
                        </Label>
                        <Region HBox.hgrow="ALWAYS" />
                        <Button mnemonicParsing="false" onAction="#closeUserDialog" styleClass="modal-close-btn" text="✕">
                           <tooltip>
                              <Tooltip text="Close Dialog (ESC)" />
                           </tooltip>
                        </Button>
                     </children>
                  </HBox>

                  <!-- Modal Body -->
                  <ScrollPane styleClass="modal-scroll-pane" fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED">
                     <content>
                        <VBox styleClass="modal-body" spacing="20.0">
                                 <children>
                  
                           <!-- Row 1: Username and Full Name -->
                           <HBox spacing="15.0">
                              <children>
                                 <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                    <children>
                                       <Label styleClass="form-label" text="Username *" />
                                       <TextField fx:id="usernameField" promptText="Enter username" styleClass="form-field" />
                                    </children>
                                 </VBox>
                                 <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                    <children>
                                       <Label styleClass="form-label" text="Full Name *" />
                                       <TextField fx:id="fullNameField" promptText="Enter full name" styleClass="form-field" />
                                    </children>
                                 </VBox>
                              </children>
                           </HBox>

                           <!-- Row 2: Role and Status -->
                           <HBox spacing="15.0">
                              <children>
                                 <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                    <children>
                                       <Label styleClass="form-label" text="Role *" />
                                       <ComboBox fx:id="roleCombo" promptText="Select role" styleClass="form-field" maxWidth="Infinity" />
                                    </children>
                                 </VBox>
                                 <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                    <children>
                                       <Label styleClass="form-label" text="Status" />
                                       <ComboBox fx:id="statusCombo" styleClass="form-field" maxWidth="Infinity" />
                                    </children>
                                 </VBox>
                              </children>
                           </HBox>

                           <!-- Row 3: Password and Confirm Password -->
                           <HBox spacing="15.0">
                              <children>
                                 <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                    <children>
                                       <Label styleClass="form-label" text="Password *" />
                                       <PasswordField fx:id="passwordField" promptText="Enter password" styleClass="form-field" />
                                    </children>
                                 </VBox>
                                 <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                    <children>
                                       <Label styleClass="form-label" text="Confirm Password *" />
                                       <PasswordField fx:id="confirmPasswordField" promptText="Confirm password" styleClass="form-field" />
                                    </children>
                                 </VBox>
                              </children>
                           </HBox>

                           <!-- Row 4: Email and Phone -->
                           <HBox spacing="15.0">
                              <children>
                                 <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                    <children>
                                       <Label styleClass="form-label" text="Email" />
                                       <TextField fx:id="emailField" promptText="Enter email address" styleClass="form-field" />
                                    </children>
                                 </VBox>
                                 <VBox spacing="5.0" HBox.hgrow="ALWAYS">
                                    <children>
                                       <Label styleClass="form-label" text="Phone" />
                                       <TextField fx:id="phoneField" promptText="Enter phone number" styleClass="form-field" />
                                    </children>
                                 </VBox>
                              </children>
                           </HBox>
                        </children>
                        </VBox>
                     </content>
                  </ScrollPane>

                  <!-- Modal Footer -->
                  <HBox alignment="CENTER_RIGHT" spacing="12.0" styleClass="modal-footer">
                     <children>
                        <Button mnemonicParsing="false" onAction="#closeUserDialog" styleClass="cancel-btn" text="Cancel">
                           <tooltip>
                              <Tooltip text="Cancel and close dialog" />
                           </tooltip>
                        </Button>
                        <Button mnemonicParsing="false" onAction="#saveUser" styleClass="save-btn" text="Save User">
                           <tooltip>
                              <Tooltip text="Save user information" />
                           </tooltip>
                        </Button>
                     </children>
                  </HBox>
               </children>
            </VBox>
         </children>
      </StackPane>
   </children>
</VBox>
