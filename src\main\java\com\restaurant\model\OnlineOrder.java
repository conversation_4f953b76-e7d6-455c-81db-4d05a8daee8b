package com.restaurant.model;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Model class for tracking online orders from <PERSON><PERSON>gy and Zomato
 */
public class OnlineOrder {
    
    public enum OrderStatus {
        NEW("New Order"),              // Red - Needs immediate attention & continuous ring
        PREPARING("Preparing"),        // Orange - Kitchen working
        READY("Ready"),               // Green - Ready for delivery
        PRICING("Pricing"),           // Blue - Final pricing
        COMPLETED("Completed");       // Gray - Finished
        
        private final String displayName;
        
        OrderStatus(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        @Override
        public String toString() {
            return displayName;
        }
    }
    
    public enum Platform {
        SWIGGY("Swiggy"),
        ZOMATO("Zomato"),
        WOK_KA_TADKA("Wok Ka Tadka");

        private final String displayName;

        Platform(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }

        @Override
        public String toString() {
            return displayName;
        }
    }
    
    private int id;
    private String orderId;
    private Platform platform;
    private String customerName;
    private String customerPhone;
    private String deliveryAddress;
    private OrderStatus status;
    private double totalAmount;
    private LocalDateTime orderTime;
    private LocalDateTime statusUpdatedTime;
    private List<OnlineOrderItem> items;
    private String specialInstructions;
    private int estimatedPrepTime; // in minutes
    
    // Constructors
    public OnlineOrder() {}
    
    public OnlineOrder(int id, String orderId, Platform platform, String customerName, 
                      String customerPhone, String deliveryAddress, OrderStatus status, 
                      double totalAmount, LocalDateTime orderTime) {
        this.id = id;
        this.orderId = orderId;
        this.platform = platform;
        this.customerName = customerName;
        this.customerPhone = customerPhone;
        this.deliveryAddress = deliveryAddress;
        this.status = status;
        this.totalAmount = totalAmount;
        this.orderTime = orderTime;
        this.statusUpdatedTime = LocalDateTime.now();
    }
    
    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public String getOrderId() { return orderId; }
    public void setOrderId(String orderId) { this.orderId = orderId; }
    
    public Platform getPlatform() { return platform; }
    public void setPlatform(Platform platform) { this.platform = platform; }
    
    public String getCustomerName() { return customerName; }
    public void setCustomerName(String customerName) { this.customerName = customerName; }
    
    public String getCustomerPhone() { return customerPhone; }
    public void setCustomerPhone(String customerPhone) { this.customerPhone = customerPhone; }
    
    public String getDeliveryAddress() { return deliveryAddress; }
    public void setDeliveryAddress(String deliveryAddress) { this.deliveryAddress = deliveryAddress; }
    
    public OrderStatus getStatus() { return status; }
    public void setStatus(OrderStatus status) { 
        this.status = status; 
        this.statusUpdatedTime = LocalDateTime.now();
    }
    
    public double getTotalAmount() { return totalAmount; }
    public void setTotalAmount(double totalAmount) { this.totalAmount = totalAmount; }
    
    public LocalDateTime getOrderTime() { return orderTime; }
    public void setOrderTime(LocalDateTime orderTime) { this.orderTime = orderTime; }
    
    public LocalDateTime getStatusUpdatedTime() { return statusUpdatedTime; }
    public void setStatusUpdatedTime(LocalDateTime statusUpdatedTime) { this.statusUpdatedTime = statusUpdatedTime; }
    
    public List<OnlineOrderItem> getItems() { return items; }
    public void setItems(List<OnlineOrderItem> items) { this.items = items; }
    
    public String getSpecialInstructions() { return specialInstructions; }
    public void setSpecialInstructions(String specialInstructions) { this.specialInstructions = specialInstructions; }
    
    public int getEstimatedPrepTime() { return estimatedPrepTime; }
    public void setEstimatedPrepTime(int estimatedPrepTime) { this.estimatedPrepTime = estimatedPrepTime; }
    
    // Utility methods
    public String getFormattedOrderTime() {
        if (orderTime != null) {
            return orderTime.format(java.time.format.DateTimeFormatter.ofPattern("HH:mm"));
        }
        return "";
    }
    
    public String getFormattedOrderDate() {
        if (orderTime != null) {
            return orderTime.format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy"));
        }
        return "";
    }
    
    public String getStatusColor() {
        switch (status) {
            case NEW: return "#f44336"; // Red - Urgent attention needed
            case PREPARING: return "#ff9800"; // Orange - Kitchen working
            case READY: return "#4caf50"; // Green - Ready for delivery
            case PRICING: return "#2196f3"; // Blue - Final pricing
            case COMPLETED: return "#9e9e9e"; // Gray - Finished
            default: return "#000000"; // Black
        }
    }
    
    public String getPlatformColor() {
        switch (platform) {
            case SWIGGY: return "#fc8019"; // Swiggy Orange
            case ZOMATO: return "#e23744"; // Zomato Red
            case WOK_KA_TADKA: return "#2e7d32"; // Wok Ka Tadka Green
            default: return "#000000"; // Black
        }
    }
    
    @Override
    public String toString() {
        return String.format("OnlineOrder{id=%d, orderId='%s', platform=%s, customer='%s', status=%s, amount=%.2f}", 
                           id, orderId, platform, customerName, status, totalAmount);
    }
}
