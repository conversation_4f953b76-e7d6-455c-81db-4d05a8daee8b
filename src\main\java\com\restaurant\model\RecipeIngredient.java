package com.restaurant.model;

/**
 * Model class for Recipe Ingredients
 */
public class RecipeIngredient {
    private int id;
    private int recipeId;
    private String materialName;
    private double quantity;
    private String unit;
    private String notes;
    private boolean isOptional;
    private int sortOrder;

    // Constructors
    public RecipeIngredient() {}

    public RecipeIngredient(String materialName, double quantity, String unit) {
        this.materialName = materialName;
        this.quantity = quantity;
        this.unit = unit;
        this.isOptional = false;
    }

    public RecipeIngredient(int id, int recipeId, String materialName, double quantity, String unit) {
        this(materialName, quantity, unit);
        this.id = id;
        this.recipeId = recipeId;
    }

    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public int getRecipeId() { return recipeId; }
    public void setRecipeId(int recipeId) { this.recipeId = recipeId; }

    public String getMaterialName() { return materialName; }
    public void setMaterialName(String materialName) { this.materialName = materialName; }

    public double getQuantity() { return quantity; }
    public void setQuantity(double quantity) { this.quantity = quantity; }

    public String getUnit() { return unit; }
    public void setUnit(String unit) { this.unit = unit; }

    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }

    public boolean isOptional() { return isOptional; }
    public void setOptional(boolean optional) { isOptional = optional; }

    public int getSortOrder() { return sortOrder; }
    public void setSortOrder(int sortOrder) { this.sortOrder = sortOrder; }

    // Utility methods
    public String getFormattedQuantity() {
        if (quantity == (int) quantity) {
            return String.valueOf((int) quantity);
        } else {
            return String.format("%.1f", quantity);
        }
    }

    public String getDisplayText() {
        return materialName + " - " + getFormattedQuantity() + " " + unit;
    }

    @Override
    public String toString() {
        return "RecipeIngredient{" +
                "materialName='" + materialName + '\'' +
                ", quantity=" + quantity +
                ", unit='" + unit + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        RecipeIngredient that = (RecipeIngredient) obj;
        return id == that.id;
    }

    @Override
    public int hashCode() {
        return Integer.hashCode(id);
    }
}
