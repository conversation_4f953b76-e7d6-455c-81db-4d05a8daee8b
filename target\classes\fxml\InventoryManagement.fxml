<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/17.0.2-ea" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.InventoryManagementController">
   <top>
      <!-- Header Section -->
      <VBox styleClass="inventory-header">
         <children>
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Button fx:id="backButton" mnemonicParsing="false" onAction="#goBack" styleClass="back-button" text="←" />
                  <VBox>
                     <children>
                        <Label styleClass="page-title" text="Inventory Management">
                           <font>
                              <Font name="System Bold" size="24.0" />
                           </font>
                        </Label>
                        <Label styleClass="page-subtitle" text="Track and manage kitchen inventory" />
                     </children>
                  </VBox>
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="advancedInventoryBtn" mnemonicParsing="false" onAction="#openAdvancedInventory" styleClass="secondary-button" text="📋 Purchase Orders &amp; Transfers" />
                  <Button fx:id="addItemButton" mnemonicParsing="false" onAction="#showAddItemDialog" styleClass="add-item-button" text="+ Add Item" />
               </children>
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>
            </HBox>
            
            <!-- Tab Navigation -->
            <HBox styleClass="inventory-tabs">
               <children>
                  <Button fx:id="ingredientsTab" mnemonicParsing="false" onAction="#showIngredients" styleClass="tab-button,tab-active" text="Ingredients" />
                  <Button fx:id="stockAlertsTab" mnemonicParsing="false" onAction="#showStockAlerts" styleClass="tab-button" text="Stock Alerts" />
                  <Button fx:id="wastageLogTab" mnemonicParsing="false" onAction="#showWastageLog" styleClass="tab-button" text="Wastage Log" />
                  <Button fx:id="suppliersTab" mnemonicParsing="false" onAction="#showSuppliers" styleClass="tab-button" text="Suppliers" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="20.0" right="20.0" top="10.0" />
               </padding>
            </HBox>
            
            <!-- Search Section -->
            <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="search-section">
               <children>
                  <TextField fx:id="searchField" onKeyReleased="#searchItems" promptText="Search ingredients..." styleClass="search-field" HBox.hgrow="ALWAYS" />
                  <Button mnemonicParsing="false" onAction="#searchItems" styleClass="search-button" text="Search" />
               </children>
               <padding>
                  <Insets bottom="15.0" left="20.0" right="20.0" top="15.0" />
               </padding>
            </HBox>
         </children>
      </VBox>
   </top>
   
   <center>
      <!-- Main Content Area -->
      <StackPane fx:id="contentArea">
         <children>
            <!-- Ingredients View -->
            <ScrollPane fx:id="ingredientsView" fitToWidth="true" styleClass="universal-scroll-pane" hbarPolicy="NEVER" vbarPolicy="ALWAYS">
               <content>
                  <VBox fx:id="ingredientsContainer" spacing="10.0" styleClass="inventory-container">
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                  </VBox>
               </content>
            </ScrollPane>
            
            <!-- Stock Alerts View -->
            <ScrollPane fx:id="stockAlertsView" fitToWidth="true" styleClass="universal-scroll-pane" hbarPolicy="NEVER" vbarPolicy="ALWAYS" visible="false">
               <content>
                  <VBox fx:id="stockAlertsContainer" spacing="10.0" styleClass="inventory-container">
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                  </VBox>
               </content>
            </ScrollPane>
            
            <!-- Wastage Log View -->
            <ScrollPane fx:id="wastageLogView" fitToWidth="true" styleClass="universal-scroll-pane" hbarPolicy="NEVER" vbarPolicy="ALWAYS" visible="false">
               <content>
                  <VBox fx:id="wastageLogContainer" spacing="10.0" styleClass="inventory-container">
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                     <children>
                        <Label styleClass="empty-state" text="No wastage records found" />
                     </children>
                  </VBox>
               </content>
            </ScrollPane>

            <!-- Suppliers View -->
            <ScrollPane fx:id="suppliersView" fitToWidth="true" styleClass="universal-scroll-pane" hbarPolicy="NEVER" vbarPolicy="ALWAYS" visible="false">
               <content>
                  <VBox fx:id="suppliersContainer" spacing="10.0" styleClass="inventory-container">
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                     <children>
                        <Label styleClass="empty-state" text="No suppliers configured" />
                     </children>
                  </VBox>
               </content>
            </ScrollPane>
         </children>
      </StackPane>
   </center>
</BorderPane>
