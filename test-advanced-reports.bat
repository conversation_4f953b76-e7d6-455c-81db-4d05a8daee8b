@echo off
echo 📊 TESTING ADVANCED REPORTS UI 📊
echo.

echo 🔧 COMPILING ADVANCED REPORTS CONTROLLER...
javac -d target/classes -cp "target/classes;lib/*" src/main/java/com/restaurant/controller/AdvancedReportsController.java

if %ERRORLEVEL% neq 0 (
    echo ❌ Compilation failed for AdvancedReportsController
    pause
    exit /b 1
)

echo ✅ Compilation successful
echo.

echo 🚀 CREATING REPORTS UI LAUNCHER...

echo package com.restaurant.test; > src\main\java\com\restaurant\test\ReportsUILauncher.java
echo. >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo import javafx.application.Application; >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo import javafx.fxml.FXMLLoader; >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo import javafx.scene.Scene; >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo import javafx.stage.Stage; >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo. >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo public class ReportsUILauncher extends Application { >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo. >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo     @Override >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo     public void start(Stage primaryStage) { >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo         try { >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo             System.out.println("🚀 Launching Advanced Reports UI..."); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo. >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo             FXMLLoader loader = new FXMLLoader(); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo             loader.setLocation(getClass().getResource("/com/restaurant/view/advanced-reports.fxml")); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo. >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo             Scene scene = new Scene(loader.load(), 1200, 800); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo. >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo             primaryStage.setTitle("📊 Advanced Reports ^& Analytics - Restaurant Management"); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo             primaryStage.setScene(scene); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo             primaryStage.setMaximized(true); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo             primaryStage.show(); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo. >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo             System.out.println("✅ Advanced Reports UI launched successfully!"); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo             System.out.println(); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo             System.out.println("📊 FEATURES AVAILABLE:"); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo             System.out.println("📈 Analytics Dashboard - Real-time business metrics"); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo             System.out.println("📅 Daily Reports - Generate and view daily performance"); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo             System.out.println("📊 Weekly Reports - Analyze weekly trends"); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo             System.out.println("📆 Monthly Reports - Track monthly growth"); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo             System.out.println("🥧 Platform Charts - Swiggy vs Zomato distribution"); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo             System.out.println("📉 Revenue Trends - Visual revenue tracking"); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo. >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo         } catch (Exception e) { >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo             System.err.println("❌ Error launching Reports UI: " + e.getMessage()); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo             e.printStackTrace(); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo         } >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo     } >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo. >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo     public static void main(String[] args) { >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo         System.out.println("📊 Starting Advanced Reports System..."); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo         launch(args); >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo     } >> src\main\java\com\restaurant\test\ReportsUILauncher.java
echo } >> src\main\java\com\restaurant\test\ReportsUILauncher.java

echo ✅ ReportsUILauncher created
echo.

echo 🔧 COMPILING UI LAUNCHER...
javac -d target/classes -cp "target/classes;lib/*" src/main/java/com/restaurant/test/ReportsUILauncher.java

if %ERRORLEVEL% neq 0 (
    echo ❌ Compilation failed for ReportsUILauncher
    pause
    exit /b 1
)

echo ✅ Compilation successful
echo.

echo 🚀 LAUNCHING ADVANCED REPORTS UI...
echo.
echo This will open the Advanced Reports interface with:
echo 📈 Analytics Dashboard with real-time metrics
echo 📅 Daily Reports generation and viewing
echo 📊 Weekly Reports with trend analysis
echo 📆 Monthly Reports with growth tracking
echo 🥧 Platform distribution charts
echo 📉 Revenue trend visualizations
echo.

java -Dprism.order=sw ^
     --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\17.0.2\javafx-base-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\17.0.2\javafx-controls-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\17.0.2\javafx-fxml-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\17.0.2\javafx-graphics-17.0.2-win.jar;%USERPROFILE%\.m2\repository\org\openjfx\javafx-media\17.0.2\javafx-media-17.0.2-win.jar" ^
     --add-modules=javafx.base,javafx.controls,javafx.fxml,javafx.graphics,javafx.media ^
     -cp "target/classes;lib/*" ^
     com.restaurant.test.ReportsUILauncher

echo.
echo 🎉 ADVANCED REPORTS UI TEST COMPLETE!
echo.

pause
