<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.collections.FXCollections?>
<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import java.lang.String?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.LoginController" stylesheets="@../css/application.css">
   <children>
      <VBox alignment="CENTER" prefHeight="800.0" prefWidth="1200.0" styleClass="wok-login-container">
         <children>
            <!-- Header Section -->
            <VBox alignment="CENTER" spacing="10.0" styleClass="wok-header">
               <children>
                  <Label styleClass="wok-title" text="WOK KA TADKA">
                     <font>
                        <Font name="System Bold" size="48.0" />
                     </font>
                  </Label>
                  <Label styleClass="wok-subtitle" text="Restaurant Management System">
                     <font>
                        <Font size="18.0" />
                     </font>
                  </Label>
                  <Region prefHeight="20.0" />
               </children>
               <VBox.margin>
                  <Insets bottom="40.0" />
               </VBox.margin>
            </VBox>

            <!-- Login Form Card -->
            <VBox alignment="CENTER" maxWidth="450.0" prefWidth="450.0" spacing="0.0" styleClass="wok-login-card">
               <children>
                  <!-- Card Header -->
                  <HBox alignment="CENTER" spacing="10.0" styleClass="wok-card-header">
                     <children>
                        <Label styleClass="wok-user-icon" text="👤" />
                        <VBox alignment="CENTER_LEFT">
                           <children>
                              <Label styleClass="wok-card-title" text="Staff Login">
                                 <font>
                                    <Font name="System Bold" size="20.0" />
                                 </font>
                              </Label>
                              <Label styleClass="wok-card-subtitle" text="Access your dashboard securely">
                                 <font>
                                    <Font size="12.0" />
                                 </font>
                              </Label>
                           </children>
                        </VBox>
                     </children>
                     <padding>
                        <Insets bottom="20.0" left="30.0" right="30.0" top="30.0" />
                     </padding>
                  </HBox>

                  <!-- Form Fields -->
                  <VBox spacing="20.0" styleClass="wok-form-section">
                     <children>
                        <VBox spacing="8.0">
                           <children>
                              <Label styleClass="wok-field-label" text="Username" />
                              <TextField fx:id="usernameField" promptText="Enter your username" styleClass="wok-input-field" />
                           </children>
                        </VBox>

                        <VBox spacing="8.0">
                           <children>
                              <Label styleClass="wok-field-label" text="Password" />
                              <PasswordField fx:id="passwordField" promptText="Enter your password" styleClass="wok-input-field" />
                           </children>
                        </VBox>

                        <VBox spacing="8.0">
                           <children>
                              <Label styleClass="wok-field-label" text="Role" />
                              <ComboBox fx:id="roleComboBox" prefWidth="200.0" promptText="Select your role" styleClass="wok-combo-field">
                                 <items>
                                    <FXCollections fx:factory="observableArrayList">
                                       <String fx:value="ADMIN" />
                                       <String fx:value="STAFF" />
                                    </FXCollections>
                                 </items>
                              </ComboBox>
                           </children>
                        </VBox>

                        <Button mnemonicParsing="false" onAction="#handleLogin" prefWidth="380.0" styleClass="wok-login-button" text="Login to Dashboard →">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Button>
                     </children>
                     <padding>
                        <Insets bottom="30.0" left="30.0" right="30.0" top="10.0" />
                     </padding>
                  </VBox>
               </children>
            </VBox>
         </children>
         <padding>
            <Insets bottom="50.0" left="50.0" right="50.0" top="50.0" />
         </padding>
      </VBox>
   </children>
</VBox>
