@echo off
echo Starting Restaurant Management System with Enhanced Stability...
echo This version includes SQLite thread safety fixes and software rendering.
echo.

REM Test database connection first
echo Testing database connection...
call mvn clean compile -q
java -cp "target/classes;%USERPROFILE%\.m2\repository\org\xerial\sqlite-jdbc\3.45.1.0\sqlite-jdbc-3.45.1.0.jar;%USERPROFILE%\.m2\repository\org\mindrot\jbcrypt\0.4\jbcrypt-0.4.jar" com.restaurant.model.DatabaseManager

if %ERRORLEVEL% neq 0 (
    echo Database test failed! Please check the error messages above.
    pause
    exit /b 1
)

echo Database test passed!
echo.

REM Set enhanced JVM options for stability
set MAVEN_OPTS=-Dprism.order=sw -Dprism.allowhidpi=false -Djavafx.animation.pulse=60 -Djava.awt.headless=false -Dsqlite.purejava=false -Xms512m -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200

echo Starting application with enhanced stability settings...
echo - Software rendering to avoid graphics crashes
echo - Updated SQLite JDBC driver (3.45.1.0)
echo - Thread-safe database connections
echo - Improved error handling
echo.

mvn javafx:run

echo.
echo Application closed.
echo If you experienced any crashes, please check the console output above.
pause
