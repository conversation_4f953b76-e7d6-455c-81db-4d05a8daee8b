package com.restaurant.model;

import java.sql.*;
import java.time.LocalDateTime;
import org.mindrot.jbcrypt.BCrypt;

public class UserDAO {

    public static User authenticate(String username, String password) {
        System.out.println("UserDAO.authenticate called with username: " + username);
        try (Connection conn = java.sql.DriverManager.getConnection("*************************");
             PreparedStatement ps = conn.prepareStatement("SELECT * FROM users WHERE username = ?")) {

            ps.setString(1, username);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                System.out.println("User found in database: " + username);
                String storedHash = rs.getString("password_hash");
                System.out.println("Stored hash: " + storedHash);
                System.out.println("Checking password: " + password);

                if (BCrypt.checkpw(password, storedHash)) {
                    System.out.println("Password check successful");
                    return new User(
                        rs.getInt("id"),
                        rs.getString("username"),
                        rs.getString("role")
                    );
                } else {
                    System.out.println("Password check failed");
                }
            } else {
                System.out.println("User not found in database: " + username);
            }
        } catch (SQLException e) {
            System.out.println("SQL Exception in authenticate: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    public boolean createUser(User user) {
        System.out.println("UserDAO.createUser called for username: " + user.getUsername());

        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement ps = conn.prepareStatement(
                 "INSERT INTO users (username, password_hash, role) VALUES (?, ?, ?)",
                 Statement.RETURN_GENERATED_KEYS)) {

            ps.setString(1, user.getUsername());
            ps.setString(2, user.getPasswordHash());
            ps.setString(3, user.getRole());

            int result = ps.executeUpdate();

            if (result > 0) {
                ResultSet rs = ps.getGeneratedKeys();
                if (rs.next()) {
                    user.setId(rs.getInt(1));
                }
                System.out.println("User created successfully with ID: " + user.getId());
                return true;
            }

        } catch (SQLException e) {
            System.err.println("Error creating user: " + e.getMessage());
            e.printStackTrace();
        }

        return false;
    }
}