<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.CategoryItemManagerController" stylesheets="@../css/application.css">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="module-header">
         <children>
            <Label styleClass="module-title" text="📋 Category Item Manager">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            
            <!-- Filter Buttons -->
            <HBox spacing="10.0" alignment="CENTER_RIGHT">
               <children>
                  <Button fx:id="allBtn" onAction="#showAll" styleClass="filter-btn filter-btn-active" text="🔴 All" />
                  <Button fx:id="recentBtn" onAction="#showRecent" styleClass="filter-btn" text="🕒 Recent" />
                  <Button fx:id="homeWebsiteBtn" onAction="#showHomeWebsite" styleClass="filter-btn" text="🌐 Home website" />
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="25.0" right="25.0" top="20.0" />
         </padding>
      </HBox>
      
      <!-- Main Content -->
      <HBox spacing="0.0" VBox.vgrow="ALWAYS">
         <children>
            <!-- Categories Sidebar -->
            <VBox styleClass="categories-sidebar" prefWidth="250.0" minWidth="250.0" maxWidth="250.0">
               <children>
                  <Label styleClass="sidebar-title" text="Categories">
                     <font>
                        <Font name="System Bold" size="18.0" />
                     </font>
                     <padding>
                        <Insets bottom="15.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                  </Label>
                  
                  <!-- Categories List -->
                  <VBox fx:id="categoriesContainer" spacing="2.0" styleClass="categories-list">
                     <children>
                        <!-- Categories will be dynamically added here -->
                     </children>
                     <padding>
                        <Insets bottom="10.0" left="10.0" right="10.0" top="0.0" />
                     </padding>
                  </VBox>
               </children>
            </VBox>
            
            <!-- Items Content Area -->
            <VBox styleClass="items-content" HBox.hgrow="ALWAYS">
               <children>
                  <!-- Content Header -->
                  <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="content-header">
                     <children>
                        <Label fx:id="contentTitleLabel" styleClass="content-title" text="Started">
                           <font>
                              <Font name="System Bold" size="20.0" />
                           </font>
                        </Label>
                        <Region HBox.hgrow="ALWAYS" />
                        
                        <!-- Action Buttons -->
                        <Button fx:id="addItemBtn" onAction="#addNewItem" styleClass="secondary-button" text="➕ Add Item" />
                        <Button fx:id="confirmBtn" onAction="#confirmChanges" styleClass="primary-button" text="✅ OK" />
                     </children>
                     <padding>
                        <Insets bottom="15.0" left="25.0" right="25.0" top="20.0" />
                     </padding>
                  </HBox>
                  
                  <!-- Items Table Header -->
                  <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="table-header">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0" prefWidth="60.0">
                           <children>
                              <Label text="Status" styleClass="table-header-label" />
                           </children>
                        </HBox>
                        <HBox alignment="CENTER_LEFT" spacing="10.0" HBox.hgrow="ALWAYS">
                           <children>
                              <Label text="Name" styleClass="table-header-label" />
                           </children>
                        </HBox>
                        <HBox alignment="CENTER_RIGHT" spacing="10.0" prefWidth="100.0">
                           <children>
                              <Label text="Mark as" styleClass="table-header-label" />
                           </children>
                        </HBox>
                     </children>
                     <padding>
                        <Insets bottom="10.0" left="25.0" right="25.0" top="10.0" />
                     </padding>
                  </HBox>
                  
                  <!-- Items List -->
                  <ScrollPane styleClass="universal-scroll-pane" fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED" VBox.vgrow="ALWAYS">
                     <content>
                        <VBox fx:id="itemsContainer" spacing="1.0" styleClass="items-list">
                           <children>
                              <!-- Items will be dynamically added here -->
                           </children>
                           <padding>
                              <Insets bottom="20.0" left="10.0" right="10.0" top="5.0" />
                           </padding>
                        </VBox>
                     </content>
                  </ScrollPane>
               </children>
            </VBox>
         </children>
      </HBox>
   </children>
</VBox>
