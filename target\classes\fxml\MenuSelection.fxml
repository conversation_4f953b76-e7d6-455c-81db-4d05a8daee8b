<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.restaurant.controller.MenuSelectionController" stylesheets="@../css/application.css">
   
   <!-- Top Header -->
   <top>
      <VBox styleClass="menu-header">
         <children>
            <!-- Navigation Bar -->
            <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="top-nav">
               <children>
                  <Button mnemonicParsing="false" onAction="#goBack" styleClass="back-button" text="← Back" />
                  <Label styleClass="restaurant-name" text="Restaurant Name">
                     <font>
                        <Font name="System Bold" size="18.0" />
                     </font>
                  </Label>
                  <Region HBox.hgrow="ALWAYS" />
                  <Label fx:id="tableLabel" styleClass="table-info" text="Table 1">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>
               </children>
               <padding>
                  <Insets bottom="10.0" left="20.0" right="20.0" top="10.0" />
               </padding>
            </HBox>
            
            <!-- Order Type Tabs -->
            <HBox alignment="CENTER" spacing="0.0" styleClass="order-tabs">
               <children>
                  <Button fx:id="dineInTab" mnemonicParsing="false" onAction="#selectDineIn" styleClass="tab-button, tab-active" text="Dine In" />
                  <Button fx:id="deliveryTab" mnemonicParsing="false" onAction="#selectDelivery" styleClass="tab-button" text="Delivery" />
                  <Button fx:id="pickupTab" mnemonicParsing="false" onAction="#selectPickup" styleClass="tab-button" text="Pick Up" />
               </children>
            </HBox>
            
            <!-- Search Bar -->
            <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="search-section">
               <children>
                  <TextField fx:id="searchField" promptText="Search items..." styleClass="search-field" HBox.hgrow="ALWAYS" />
                  <Button mnemonicParsing="false" onAction="#searchItems" styleClass="search-button" text="🔍" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="20.0" right="20.0" top="10.0" />
               </padding>
            </HBox>
         </children>
      </VBox>
   </top>
   
   <!-- Main Content -->
   <center>
      <HBox spacing="0.0">
         <children>
            <!-- Left Categories Panel -->
            <VBox styleClass="categories-panel" minWidth="200.0" maxWidth="200.0">
               <children>
                  <Label styleClass="categories-title" text="Categories">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  <ListView fx:id="categoriesList" styleClass="categories-list" VBox.vgrow="ALWAYS" />
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
            
            <!-- Center Menu Items -->
            <ScrollPane styleClass="menu-scroll" HBox.hgrow="ALWAYS">
               <content>
                  <GridPane fx:id="menuGrid" styleClass="menu-grid" hgap="15.0" vgap="15.0">
                     <columnConstraints>
                        <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" prefWidth="180.0" />
                        <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" prefWidth="180.0" />
                        <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" prefWidth="180.0" />
                        <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" prefWidth="180.0" />
                     </columnConstraints>
                     <children>
                        <!-- Sample Menu Items - These will be populated dynamically -->
                        <VBox styleClass="menu-item-card" GridPane.columnIndex="0" GridPane.rowIndex="0">
                           <children>
                              <Label styleClass="item-name" text="Aloo Tikki Burger">
                                 <font>
                                    <Font name="System Bold" size="12.0" />
                                 </font>
                              </Label>
                              <Label styleClass="item-price" text="₹85.00" />
                              <Button mnemonicParsing="false" styleClass="add-button" text="Add" userData="1" />
                           </children>
                        </VBox>
                        
                        <VBox styleClass="menu-item-card" GridPane.columnIndex="1" GridPane.rowIndex="0">
                           <children>
                              <Label styleClass="item-name" text="Cheese Garlic Bread">
                                 <font>
                                    <Font name="System Bold" size="12.0" />
                                 </font>
                              </Label>
                              <Label styleClass="item-price" text="₹120.00" />
                              <Button mnemonicParsing="false" styleClass="add-button" text="Add" userData="2" />
                           </children>
                        </VBox>
                        
                        <VBox styleClass="menu-item-card" GridPane.columnIndex="2" GridPane.rowIndex="0">
                           <children>
                              <Label styleClass="item-name" text="Chicken Angara">
                                 <font>
                                    <Font name="System Bold" size="12.0" />
                                 </font>
                              </Label>
                              <Label styleClass="item-price" text="₹180.00" />
                              <Button mnemonicParsing="false" styleClass="add-button" text="Add" userData="3" />
                           </children>
                        </VBox>
                        
                        <VBox styleClass="menu-item-card" GridPane.columnIndex="3" GridPane.rowIndex="0">
                           <children>
                              <Label styleClass="item-name" text="Chili Mushroom">
                                 <font>
                                    <Font name="System Bold" size="12.0" />
                                 </font>
                              </Label>
                              <Label styleClass="item-price" text="₹150.00" />
                              <Button mnemonicParsing="false" styleClass="add-button" text="Add" userData="4" />
                           </children>
                        </VBox>
                     </children>
                     <padding>
                        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                     </padding>
                  </GridPane>
               </content>
            </ScrollPane>
            
            <!-- Right Order Panel -->
            <VBox styleClass="order-panel" minWidth="300.0" maxWidth="300.0">
               <children>
                  <!-- Order Header -->
                  <HBox alignment="CENTER_LEFT" spacing="10.0" styleClass="order-header">
                     <children>
                        <Label styleClass="order-title" text="Order Details">
                           <font>
                              <Font name="System Bold" size="14.0" />
                           </font>
                        </Label>
                        <Region HBox.hgrow="ALWAYS" />
                        <Label fx:id="itemCountLabel" styleClass="item-count" text="0 Items" />
                     </children>
                  </HBox>
                  
                  <!-- Order Items List -->
                  <ScrollPane styleClass="order-scroll" VBox.vgrow="ALWAYS">
                     <content>
                        <VBox fx:id="orderItemsContainer" spacing="10.0" styleClass="order-items">
                           <children>
                              <Label styleClass="empty-order" text="No items added yet" />
                           </children>
                        </VBox>
                     </content>
                  </ScrollPane>
                  
                  <!-- Order Summary -->
                  <VBox styleClass="order-summary" spacing="10.0">
                     <children>
                        <HBox alignment="CENTER_LEFT">
                           <children>
                              <Label text="Subtotal:" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Label fx:id="subtotalLabel" text="₹0.00" />
                           </children>
                        </HBox>
                        <HBox alignment="CENTER_LEFT">
                           <children>
                              <Label text="GST (18%):" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Label fx:id="gstLabel" text="₹0.00" />
                           </children>
                        </HBox>
                        <HBox alignment="CENTER_LEFT">
                           <children>
                              <Label text="Service Charge:" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Label fx:id="serviceChargeLabel" text="₹0.00" />
                           </children>
                        </HBox>
                        <HBox alignment="CENTER_LEFT">
                           <children>
                              <Label text="Order Discount:" />
                              <Region HBox.hgrow="ALWAYS" />
                              <Label fx:id="orderDiscountLabel" text="₹0.00" styleClass="discount-amount" />
                           </children>
                        </HBox>
                        <HBox alignment="CENTER_LEFT" spacing="5.0">
                           <children>
                              <Button fx:id="applyOrderDiscountBtn" onAction="#showOrderDiscountDialog" text="💰 Apply Discount" styleClass="discount-button" />
                           </children>
                        </HBox>
                        <Separator />
                        <HBox alignment="CENTER_LEFT">
                           <children>
                              <Label styleClass="total-label" text="Total:">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                              <Region HBox.hgrow="ALWAYS" />
                              <Label fx:id="totalLabel" styleClass="total-amount" text="₹0.00">
                                 <font>
                                    <Font name="System Bold" size="14.0" />
                                 </font>
                              </Label>
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Action Buttons -->
                  <VBox spacing="10.0" styleClass="action-buttons">
                     <children>
                        <Button mnemonicParsing="false" onAction="#saveOrder" styleClass="save-button" text="Save Order" />
                        <Button mnemonicParsing="false" onAction="#printKOT" styleClass="kot-button" text="Print KOT" />
                        <Button mnemonicParsing="false" onAction="#generateBill" styleClass="bill-button" text="Generate Bill" />
                     </children>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
         </children>
      </HBox>
   </center>
</BorderPane>
