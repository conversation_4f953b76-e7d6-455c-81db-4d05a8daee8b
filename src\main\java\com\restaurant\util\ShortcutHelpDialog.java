package com.restaurant.util;

import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.stage.StageStyle;

/**
 * Utility class to display keyboard shortcuts help dialog
 */
public class ShortcutHelpDialog {
    
    public static void showShortcutsDialog() {
        Stage dialog = new Stage();
        dialog.initModality(Modality.APPLICATION_MODAL);
        dialog.initStyle(StageStyle.DECORATED);
        dialog.setTitle("🎛️ Keyboard Shortcuts - Quick Reference");
        dialog.setResizable(true);
        
        // Main container
        VBox mainContainer = new VBox(15);
        mainContainer.setPadding(new Insets(20));
        mainContainer.setStyle("-fx-background-color: #f8f9fa;");
        
        // Title
        Label titleLabel = new Label("🚀 Restaurant POS - Keyboard Shortcuts");
        titleLabel.setFont(Font.font("System", FontWeight.BOLD, 20));
        titleLabel.setStyle("-fx-text-fill: #2c3e50;");
        
        // Create tabs for different categories
        TabPane tabPane = new TabPane();
        tabPane.setTabClosingPolicy(TabPane.TabClosingPolicy.UNAVAILABLE);
        
        // Order Management Tab
        Tab orderTab = new Tab("🍽️ Orders");
        orderTab.setContent(createOrderShortcutsPane());
        
        // Payment Tab
        Tab paymentTab = new Tab("💳 Payments");
        paymentTab.setContent(createPaymentShortcutsPane());
        
        // Navigation Tab
        Tab navTab = new Tab("🧭 Navigation");
        navTab.setContent(createNavigationShortcutsPane());
        
        // Management Tab
        Tab mgmtTab = new Tab("🔧 Management");
        mgmtTab.setContent(createManagementShortcutsPane());
        
        // Tips Tab
        Tab tipsTab = new Tab("💡 Tips");
        tipsTab.setContent(createTipsPane());
        
        tabPane.getTabs().addAll(orderTab, paymentTab, navTab, mgmtTab, tipsTab);
        
        // Close button
        Button closeButton = new Button("✅ Got It!");
        closeButton.setStyle("-fx-background-color: #28a745; -fx-text-fill: white; " +
                           "-fx-font-weight: bold; -fx-padding: 10 20; -fx-background-radius: 5;");
        closeButton.setOnAction(e -> dialog.close());
        
        HBox buttonBox = new HBox();
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.getChildren().add(closeButton);
        
        mainContainer.getChildren().addAll(titleLabel, tabPane, buttonBox);
        
        Scene scene = new Scene(mainContainer, 800, 600);
        scene.getStylesheets().add(ShortcutHelpDialog.class.getResource("/css/application.css").toExternalForm());
        
        dialog.setScene(scene);
        dialog.showAndWait();
    }
    
    private static ScrollPane createOrderShortcutsPane() {
        VBox content = new VBox(10);
        content.setPadding(new Insets(15));
        
        content.getChildren().addAll(
            createSectionHeader("📋 Order Management"),
            createShortcutRow("Ctrl + N", "New Order", "Create a new order"),
            createShortcutRow("Ctrl + H", "Hold KOT", "Hold current Kitchen Order Ticket"),
            createShortcutRow("Ctrl + P", "Print KOT", "Print Kitchen Order Ticket"),
            createShortcutRow("Ctrl + S", "Settle Bill", "Process bill payment"),
            createShortcutRow("Ctrl + D", "Apply Discount", "Apply discount to order/item"),
            createShortcutRow("Delete", "Delete Item", "Remove selected item from order"),
            createShortcutRow("Backspace", "Delete Item", "Remove selected item from order"),
            
            createSectionHeader("🖱️ Mouse Actions"),
            createShortcutRow("Single Click", "Add Item", "Add menu item to order"),
            createShortcutRow("Double Click", "Discount Options", "Open discount dialog for item"),
            createShortcutRow("💰 Button", "Item Discount", "Apply discount to specific item"),
            createShortcutRow("🗑 Button", "Remove Item", "Delete item from order")
        );
        
        return new ScrollPane(content);
    }
    
    private static ScrollPane createPaymentShortcutsPane() {
        VBox content = new VBox(10);
        content.setPadding(new Insets(15));
        
        content.getChildren().addAll(
            createSectionHeader("💳 Quick Payments"),
            createShortcutRow("F2", "Cash Payment", "Process cash payment quickly"),
            createShortcutRow("F3", "Card Payment", "Process card payment"),
            createShortcutRow("F4", "UPI Payment", "Process UPI/digital payment"),
            
            createSectionHeader("💰 Billing Actions"),
            createShortcutRow("Ctrl + S", "Settle Bill", "Complete bill payment"),
            createShortcutRow("Ctrl + D", "Apply Discount", "Apply order-level discount"),
            createShortcutRow("Ctrl + P", "Print Bill", "Print customer bill"),
            
            createSectionHeader("🧾 Receipt Options"),
            createShortcutRow("Ctrl + R", "Reprint", "Reprint last receipt"),
            createShortcutRow("Ctrl + E", "Email Receipt", "Send receipt via email")
        );
        
        return new ScrollPane(content);
    }
    
    private static ScrollPane createNavigationShortcutsPane() {
        VBox content = new VBox(10);
        content.setPadding(new Insets(15));
        
        content.getChildren().addAll(
            createSectionHeader("🧭 Navigation"),
            createShortcutRow("Ctrl + F", "Search", "Focus on search field"),
            createShortcutRow("Ctrl + S", "Search", "Search menu items"),
            createShortcutRow("Ctrl + N", "Save Order", "Save current order"),
            createShortcutRow("Ctrl + P", "Print KOT", "Print Kitchen Order Ticket"),
            createShortcutRow("Ctrl + B", "Generate Bill", "Generate and print bill"),
            createShortcutRow("Ctrl + D", "Order Discount", "Apply discount to entire order"),
            createShortcutRow("Ctrl + K", "Billing & KOT", "Open billing and KOT management"),
            createShortcutRow("Enter", "Finish/Confirm", "Complete current task or save order"),
            createShortcutRow("Ctrl + Enter", "Force Confirm", "Force generate bill directly"),
            createShortcutRow("Escape", "Cancel/Back", "Cancel operation or go back"),
            createShortcutRow("Ctrl + T", "See Tables", "Open table management with keyboard navigation"),
            createShortcutRow("F1", "Help", "Show this shortcuts guide"),
            createShortcutRow("F5", "Refresh", "Refresh current view"),
            createShortcutRow("Escape", "Cancel/Back", "Cancel action or go back"),
            createShortcutRow("Enter", "Confirm", "Confirm current action"),
            
            createSectionHeader("🏪 Table Access"),
            createShortcutRow("1-9", "Quick Tables", "Direct access to tables 1-9"),
            createShortcutRow("0", "Table 10", "Access table 10"),
            createShortcutRow("Ctrl + T", "See Tables", "Open table management view"),
            createSectionHeader("📋 In Table Management View"),
            createShortcutRow("1-9", "Select Table", "Type table number to select"),
            createShortcutRow("Enter", "Open Table", "Open selected table menu"),
            createShortcutRow("Backspace", "Delete Digit", "Remove last typed digit"),
            createShortcutRow("Escape", "Clear Selection", "Clear current table selection"),

            createSectionHeader("🔍 In Menu Search Mode"),
            createShortcutRow("↓ Down", "Next Item", "Navigate to next search result"),
            createShortcutRow("↑ Up", "Previous Item", "Navigate to previous search result"),
            createShortcutRow("→ Right", "Next Item", "Navigate to next search result"),
            createShortcutRow("← Left", "Previous Item", "Navigate to previous search result"),
            createShortcutRow("Enter", "Add Item", "Add selected item to order (search stays open)"),
            createShortcutRow("Escape", "Clear Search", "Clear search and exit navigation")
        );
        
        return new ScrollPane(content);
    }
    
    private static ScrollPane createManagementShortcutsPane() {
        VBox content = new VBox(10);
        content.setPadding(new Insets(15));
        
        content.getChildren().addAll(
            createSectionHeader("🔧 System Management"),
            createShortcutRow("Ctrl + M", "Menu Management", "Open menu management"),
            createShortcutRow("Ctrl + U", "User Management", "Open user management"),
            createShortcutRow("Ctrl + I", "Inventory", "Open inventory management"),
            createShortcutRow("Ctrl + A", "Admin Settings", "Open admin settings"),
            createShortcutRow("Ctrl + L", "Log Out", "Log out of system"),
            
            createSectionHeader("📊 Reports & Analytics"),
            createShortcutRow("Ctrl + Shift + R", "Reports", "Open reports dashboard"),
            createShortcutRow("Ctrl + Shift + S", "Sales Report", "View sales analytics"),
            createShortcutRow("Ctrl + Shift + I", "Inventory Report", "View inventory status")
        );
        
        return new ScrollPane(content);
    }
    
    private static ScrollPane createTipsPane() {
        VBox content = new VBox(15);
        content.setPadding(new Insets(15));
        
        content.getChildren().addAll(
            createSectionHeader("💡 Power User Tips"),
            
            createTipCard("⚡ Speed Billing Workflow",
                "1. Ctrl+N → New Order\n" +
                "2. Ctrl+F → Search items\n" +
                "3. Ctrl+D → Apply discounts\n" +
                "4. Ctrl+P → Print KOT\n" +
                "5. F2/F3/F4 → Quick payment\n" +
                "6. Ctrl+S → Settle bill"),
            
            createTipCard("🎯 Table Management",
                "• Use number keys (1-9) for instant table access\n" +
                "• Double-click tables to open menu directly\n" +
                "• Use Ctrl+T for table switching dialog\n" +
                "• Eye icon (👁) shows tables with active orders"),
            
            createTipCard("🚀 Efficiency Boosters",
                "• Memorize F2, F3, F4 for quick payments\n" +
                "• Use Ctrl+F to search menu items instantly\n" +
                "• Double-click menu items for discount options\n" +
                "• Press F1 anytime for context help"),
            
            createTipCard("🎨 Customization",
                "• Shortcuts can be customized in Settings\n" +
                "• Create user-specific shortcut profiles\n" +
                "• Assign shortcuts to frequent menu items\n" +
                "• Use voice commands with 'Hey Restaurant'")
        );
        
        return new ScrollPane(content);
    }
    
    private static Label createSectionHeader(String text) {
        Label header = new Label(text);
        header.setFont(Font.font("System", FontWeight.BOLD, 16));
        header.setStyle("-fx-text-fill: #2c3e50; -fx-padding: 10 0 5 0;");
        return header;
    }
    
    private static HBox createShortcutRow(String shortcut, String action, String description) {
        HBox row = new HBox(15);
        row.setAlignment(Pos.CENTER_LEFT);
        row.setPadding(new Insets(5));
        row.setStyle("-fx-background-color: white; -fx-background-radius: 5; " +
                    "-fx-border-color: #e9ecef; -fx-border-radius: 5;");
        
        // Shortcut key
        Label shortcutLabel = new Label(shortcut);
        shortcutLabel.setMinWidth(100);
        shortcutLabel.setStyle("-fx-background-color: #495057; -fx-text-fill: white; " +
                             "-fx-padding: 4 8; -fx-background-radius: 3; -fx-font-family: monospace; " +
                             "-fx-font-weight: bold; -fx-font-size: 11px;");
        
        // Action
        Label actionLabel = new Label(action);
        actionLabel.setMinWidth(120);
        actionLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #495057;");
        
        // Description
        Label descLabel = new Label(description);
        descLabel.setStyle("-fx-text-fill: #6c757d;");
        
        row.getChildren().addAll(shortcutLabel, actionLabel, descLabel);
        return row;
    }
    
    private static VBox createTipCard(String title, String content) {
        VBox card = new VBox(8);
        card.setPadding(new Insets(15));
        card.setStyle("-fx-background-color: white; -fx-background-radius: 8; " +
                     "-fx-border-color: #dee2e6; -fx-border-radius: 8; " +
                     "-fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 3, 0, 0, 1);");
        
        Label titleLabel = new Label(title);
        titleLabel.setFont(Font.font("System", FontWeight.BOLD, 14));
        titleLabel.setStyle("-fx-text-fill: #495057;");
        
        Label contentLabel = new Label(content);
        contentLabel.setStyle("-fx-text-fill: #6c757d; -fx-font-size: 12px;");
        contentLabel.setWrapText(true);
        
        card.getChildren().addAll(titleLabel, contentLabel);
        return card;
    }
}
