# Advanced Inventory Management System

This document describes the comprehensive inventory management system with Purchase Orders and Internal Transfers, exactly matching your design requirements.

## Features Overview

This advanced inventory system provides:
1. **Purchase Orders Management** - Track orders from suppliers to locations
2. **Internal Transfers Management** - Manage transfers between restaurant locations
3. **Multi-Location Support** - Vastrapur, Makaraba, Central Kitchen, Warehouse
4. **Status Tracking** - Real-time status updates with color coding
5. **Date Range Filtering** - Filter orders and transfers by date ranges

## Interface Sections

### 🏪 Purchase Orders Section

#### Header Features:
- **🏪 Purchase order** title with icon
- **Date Range Filters**: Start date and End date pickers
- **➕ Add Order** button for creating new purchase orders
- **🔄 Refresh** button to reload data

#### Table Columns:
- **To**: Destination location (Vastrapur, Makaraba, Central Kitchen, etc.)
- **Req. No.**: Request number (P0324G, P0456K, etc.)
- **Item**: Product name (Mango, Carrot, etc.)
- **Qty**: Quantity with units (10 kg, 6 kg, etc.)
- **Status**: Color-coded status badges
  - 🟡 **Saved** (Yellow) - Order created but not processed
  - 🔵 **Processed** (Blue) - Order being fulfilled
  - 🟢 **Delivered** (Green) - Order completed

#### Sample Purchase Orders:
- **P0324G** → Vastrapur: Mango, 10 kg, **Saved**
- **P0456K** → Makaraba: Carrot, 6 kg, **Processed**
- **P0567L** → Central Kitchen: Onions, 15 kg, **Delivered**
- **P0678M** → Vastrapur: Tomatoes, 8 kg, **Saved**
- **P0789N** → Warehouse: Rice, 50 kg, **Processed**

### 🔄 Internal Transfer Section

#### Header Features:
- **🔄 Internal Transfer** title with icon
- **Date Range Filters**: Start date and End date pickers
- **➕ Add Transfer** button for creating new transfers
- **🔄 Refresh** button to reload data

#### Table Columns:
- **To**: Destination location
- **Req. No.**: Request number (same format as purchase orders)
- **Item**: Product being transferred
- **Qty**: Quantity with units
- **Status**: Color-coded status badges
  - 🟡 **Saved** (Yellow) - Transfer requested
  - 🔵 **Processed** (Blue) - Transfer approved
  - 🔵 **In Transit** (Teal) - Items being moved
  - 🟢 **Delivered** (Green) - Transfer completed

#### Sample Internal Transfers:
- **P0324G** → Central Kitchen to Vastrapur: Mango, 10 kg, **Saved**
- **P0456K** → Warehouse to Makaraba: Carrot, 6 kg, **Processed**
- **P0567L** → Vastrapur to Central Kitchen: Spices, 2 kg, **Delivered**
- **P0678M** → Central Kitchen to Makaraba: Oil, 5 L, **In Transit**
- **P0789N** → Warehouse to Vastrapur: Flour, 20 kg, **Saved**

### 🏭 Location Network Diagram

Visual representation of location connections:

```
Vastrapur ↔ Central Kitchen ↔ Makaraba
              ↕
           Warehouse
```

#### Location Icons:
- 🏪 **Vastrapur** (Restaurant location)
- 🏭 **Central Kitchen** (Main processing center)
- 🏪 **Makaraba** (Restaurant location)
- 📦 **Warehouse** (Storage facility)

## Database Schema

### Purchase Orders Table (`purchase_orders`)
```sql
CREATE TABLE purchase_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    request_number TEXT NOT NULL UNIQUE,
    to_location TEXT NOT NULL,
    item TEXT NOT NULL,
    quantity TEXT NOT NULL,
    status TEXT DEFAULT 'Saved',
    start_date DATE,
    end_date DATE,
    created_by TEXT,
    notes TEXT,
    unit_price REAL DEFAULT 0,
    total_amount REAL DEFAULT 0,
    supplier_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
);
```

### Internal Transfers Table (`internal_transfers`)
```sql
CREATE TABLE internal_transfers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    request_number TEXT NOT NULL UNIQUE,
    from_location TEXT NOT NULL,
    to_location TEXT NOT NULL,
    item TEXT NOT NULL,
    quantity TEXT NOT NULL,
    status TEXT DEFAULT 'Saved',
    start_date DATE,
    end_date DATE,
    created_by TEXT,
    approved_by TEXT,
    notes TEXT,
    transfer_reason TEXT,
    transfer_date TIMESTAMP,
    delivery_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## How to Access

### Method 1: From Inventory Management
1. Open the main restaurant application
2. Go to **Inventory Management**
3. Click the **📋 Purchase Orders & Transfers** button in the header
4. The advanced inventory interface opens as a modal dialog

### Method 2: Standalone Launch
You can also run the interface independently:

```bash
# Navigate to your project directory
cd e:\restaurant-desktop

# Compile and run the standalone launcher
javac -cp "path/to/javafx/lib/*" src/main/java/com/restaurant/util/AdvancedInventoryLauncher.java
java -cp "path/to/javafx/lib/*:src/main/java" com.restaurant.util.AdvancedInventoryLauncher
```

## Functionality

### Purchase Order Management
- **Create Orders**: Add new purchase orders with supplier details
- **Track Status**: Monitor order progress from Saved to Delivered
- **Edit Orders**: Modify order details while in Saved status
- **Delete Orders**: Remove orders with confirmation
- **Date Filtering**: Filter orders by date range
- **Auto-numbering**: Automatic request number generation (P00001, P00002, etc.)

### Internal Transfer Management
- **Create Transfers**: Set up transfers between any two locations
- **Multi-Status Tracking**: Saved → Processed → In Transit → Delivered
- **Location Validation**: Ensure valid from/to location combinations
- **Transfer History**: Complete audit trail of all transfers
- **Approval Workflow**: Track who created and approved transfers

### Enhanced Features
- **Real-time Updates**: Changes reflect immediately in the interface
- **Color-coded Status**: Visual status indicators for quick recognition
- **Interactive Dialogs**: User-friendly forms for adding/editing
- **Confirmation Dialogs**: Prevent accidental deletions
- **Date Range Controls**: Flexible filtering options
- **Location Network**: Visual representation of transfer routes

## Status Color Coding

### Purchase Orders:
- 🟡 **Saved** (#ffc107) - Yellow
- 🔵 **Processed** (#007bff) - Blue  
- 🟢 **Delivered** (#28a745) - Green
- 🔴 **Cancelled** (#dc3545) - Red

### Internal Transfers:
- 🟡 **Saved** (#ffc107) - Yellow
- 🔵 **Processed** (#007bff) - Blue
- 🔵 **In Transit** (#17a2b8) - Teal
- 🟢 **Delivered** (#28a745) - Green
- 🔴 **Cancelled** (#dc3545) - Red

## Files Created/Modified

### New Files:
- `src/main/java/com/restaurant/model/PurchaseOrder.java`
- `src/main/java/com/restaurant/model/InternalTransfer.java`
- `src/main/java/com/restaurant/model/PurchaseOrderDAO.java`
- `src/main/java/com/restaurant/model/InternalTransferDAO.java`
- `src/main/java/com/restaurant/controller/AdvancedInventoryController.java`
- `src/main/resources/fxml/AdvancedInventoryManagement.fxml`
- `src/main/java/com/restaurant/util/AdvancedInventoryLauncher.java`

### Modified Files:
- `src/main/java/com/restaurant/model/DatabaseManager.java` (added new tables)
- `src/main/resources/css/application.css` (added inventory styles)
- `src/main/java/com/restaurant/controller/InventoryManagementController.java` (added integration)
- `src/main/resources/fxml/InventoryManagement.fxml` (added button)

## Usage Example

1. **Access Interface**: Click "📋 Purchase Orders & Transfers" from Inventory Management
2. **Create Purchase Order**: 
   - Click "➕ Add Order"
   - Select "Vastrapur" as destination
   - Enter "Fresh Vegetables" as item
   - Enter "25 kg" as quantity
   - Set status to "Saved"
   - Click "Add"
3. **Create Internal Transfer**:
   - Click "➕ Add Transfer"
   - Select "Central Kitchen" as from location
   - Select "Makaraba" as to location
   - Enter "Prepared Sauce" as item
   - Enter "10 L" as quantity
   - Set status to "Saved"
   - Click "Add"
4. **Track Progress**: Monitor status changes through the color-coded badges
5. **Filter by Date**: Use date pickers to view orders/transfers in specific periods
6. **View Network**: See location relationships in the diagram at bottom

This comprehensive inventory management system provides complete control over purchase orders and internal transfers, exactly matching your design requirements with enhanced functionality for restaurant operations!
